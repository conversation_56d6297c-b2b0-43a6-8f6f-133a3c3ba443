# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* documents_project
# 
# Translators:
# Ивайло <PERSON>линов <<EMAIL>>, 2024
# <PERSON>, 2024
# <PERSON>, 2024
# <PERSON><PERSON><PERSON> <alben<PERSON>_v<PERSON><PERSON>@abv.bg>, 2024
# <PERSON><PERSON><PERSON><PERSON>, 2024
# KeyVillage, 2024
# <PERSON>, 2025
# <PERSON><PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-03-26 20:47+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2025\n"
"Language-Team: Bulgarian (https://app.transifex.com/odoo/teams/41243/bg/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: bg\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: documents_project
#: model:ir.actions.server,name:documents_project.ir_actions_server_tag_add_draft
msgid "Add Tag Draft"
msgstr ""

#. module: documents_project
#: model:ir.actions.server,name:documents_project.ir_actions_server_tag_add_to_validate
msgid "Add Tag To Validate"
msgstr ""

#. module: documents_project
#: model:ir.actions.server,name:documents_project.ir_actions_server_tag_add_validated
msgid "Add Tag Validated"
msgstr "Добавяне на таг Валидирано"

#. module: documents_project
#: model:ir.actions.server,name:documents_project.ir_actions_server_ask_for_validation_code
msgid "Ask For Validation - Check conditions"
msgstr ""

#. module: documents_project
#: model:ir.actions.server,name:documents_project.ir_actions_server_ask_for_validation
msgid "Ask for validation"
msgstr ""

#. module: documents_project
#: model:ir.model,name:documents_project.model_ir_attachment
msgid "Attachment"
msgstr "Прикачен файл"

#. module: documents_project
#: model_terms:ir.ui.view,arch_db:documents_project.edit_project_document_form_inherit
msgid "Categorize and share your documents with your customers"
msgstr "Категоризирай и споделяй своите документи с клиенти си"

#. module: documents_project
#: model:ir.actions.server,name:documents_project.ir_actions_server_create_a_task_code
#: model:ir.actions.server,name:documents_project.ir_actions_server_create_project_task
msgid "Create a Task"
msgstr "Създай задача"

#. module: documents_project
#: model:ir.model.fields,field_description:documents_project.field_project_project__documents_tag_ids
msgid "Default Tags"
msgstr "Етикет по подразбиране"

#. module: documents_project
#: model:ir.actions.server,name:documents_project.ir_actions_server_create_project_deprecate
#: model:ir.actions.server,name:documents_project.ir_actions_server_tag_add_deprecated
#: model:ir.actions.server,name:documents_project.ir_actions_server_tag_remove_deprecated
msgid "Deprecate"
msgstr "Отхвърляне"

#. module: documents_project
#: model:ir.actions.server,name:documents_project.ir_actions_server_deprecate_code
msgid "Deprecate - Check conditions"
msgstr ""

#. module: documents_project
#: model:ir.model,name:documents_project.model_documents_document
msgid "Document"
msgstr "Документ"

#. module: documents_project
#. odoo-python
#: code:addons/documents_project/controllers/portal.py:0
#: code:addons/documents_project/models/project_project.py:0
#: model:ir.actions.act_window,name:documents_project.action_view_documents_project_project
#: model:ir.actions.act_window,name:documents_project.action_view_documents_project_task
#: model:ir.embedded.actions,name:documents_project.project_embedded_action_documents
#: model:ir.embedded.actions,name:documents_project.project_embedded_action_documents_dashboard
#: model:ir.model.fields,field_description:documents_project.field_project_project__use_documents
#: model:ir.model.fields,field_description:documents_project.field_project_task__document_ids
#: model_terms:ir.ui.view,arch_db:documents_project.portal_my_task
#: model_terms:ir.ui.view,arch_db:documents_project.portal_tasks_list
#: model_terms:ir.ui.view,arch_db:documents_project.project_sharing_project_task_view_form_inherit
#: model_terms:ir.ui.view,arch_db:documents_project.project_view_kanban_inherit_documents
#: model_terms:ir.ui.view,arch_db:documents_project.view_task_form2_document_inherit
msgid "Documents"
msgstr "Документи"

#. module: documents_project
#: model_terms:ir.ui.view,arch_db:documents_project.edit_project_document_form_inherit
msgid "Documents &amp; Analytics"
msgstr ""

#. module: documents_project
#. odoo-python
#: code:addons/documents_project/models/documents_document.py:0
msgid "Documents already linked to a task."
msgstr ""

#. module: documents_project
#: model:ir.model.fields,field_description:documents_project.field_project_project__documents_folder_id
msgid "Folder"
msgstr "Папка"

#. module: documents_project
#: model:ir.model.fields,help:documents_project.field_project_project__documents_folder_id
#: model:ir.model.fields,help:documents_project.field_project_task__documents_folder_id
msgid ""
"Folder in which all of the documents of this project will be categorized. "
"All of the attachments of your tasks will be automatically added as "
"documents in this workspace as well."
msgstr ""

#. module: documents_project
#. odoo-python
#: code:addons/documents_project/models/documents_document.py:0
msgid "Impossible to create a task on a deprecated document"
msgstr ""

#. module: documents_project
#. odoo-python
#: code:addons/documents_project/models/documents_document.py:0
msgid ""
"Impossible to update write `res_id` without `access_internal` for records "
"with different `res_models` when some are linked to projects or tasks."
msgstr ""

#. module: documents_project
#. odoo-python
#: code:addons/documents_project/models/documents_document.py:0
msgid "Invalid project search"
msgstr "Невалидно търсене на проект"

#. module: documents_project
#. odoo-python
#: code:addons/documents_project/models/documents_document.py:0
msgid "Invalid task search"
msgstr "Невалидно търсене на задача"

#. module: documents_project
#: model:ir.actions.server,name:documents_project.ir_actions_server_mark_as_draft_code
msgid "Mark As Draft - Check conditions"
msgstr ""

#. module: documents_project
#: model:ir.actions.server,name:documents_project.ir_actions_server_mark_as_draft
msgid "Mark as draft"
msgstr ""

#. module: documents_project
#. odoo-python
#: code:addons/documents_project/models/documents_document.py:0
msgid "New task from Documents"
msgstr "Нова задача от Документи"

#. module: documents_project
#: model:ir.model,name:documents_project.model_project_project
#: model_terms:ir.ui.view,arch_db:documents_project.document_view_search_inherit
msgid "Project"
msgstr "Проект"

#. module: documents_project
#: model:ir.model.fields,field_description:documents_project.field_documents_document__project_ids
msgid "Projects"
msgstr "Проекти"

#. module: documents_project
#: model:ir.actions.server,name:documents_project.ir_actions_server_tag_remove_draft
msgid "Remove Tag Draft"
msgstr ""

#. module: documents_project
#: model:ir.actions.server,name:documents_project.ir_actions_server_tag_remove_to_validate
msgid "Remove Tag To Validate"
msgstr "Премахване на тага за валидиране"

#. module: documents_project
#: model:ir.actions.server,name:documents_project.ir_actions_server_tag_remove_validated
msgid "Remove Tag Validated"
msgstr ""

#. module: documents_project
#: model:ir.model,name:documents_project.model_project_task
#: model_terms:ir.ui.view,arch_db:documents_project.document_view_search_inherit
msgid "Task"
msgstr "Задача"

#. module: documents_project
#. odoo-python
#: code:addons/documents_project/models/documents_document.py:0
msgid "Task created from document %s"
msgstr ""

#. module: documents_project
#. odoo-python
#: code:addons/documents_project/models/documents_document.py:0
msgid "Task created from documents"
msgstr ""

#. module: documents_project
#. odoo-python
#: code:addons/documents_project/models/project_task.py:0
msgid "Task's Documents"
msgstr ""

#. module: documents_project
#. odoo-python
#: code:addons/documents_project/models/project_project.py:0
msgid ""
"The \"%(folder)s\" folder should either be in the \"%(company)s\" company "
"like this project or be open to all companies."
msgstr ""

#. module: documents_project
#. odoo-python
#: code:addons/documents_project/models/documents_document.py:0
msgid ""
"The \"%s\" workspace is required by the Project application and cannot be "
"archived."
msgstr ""

#. module: documents_project
#. odoo-python
#: code:addons/documents_project/models/documents_document.py:0
msgid ""
"The \"%s\" workspace is required by the Project application and cannot be "
"deleted."
msgstr ""

#. module: documents_project
#. odoo-python
#: code:addons/documents_project/models/documents_document.py:0
msgid ""
"This action can't be performed, as it would remove the workspaces used by the following projects:\n"
"%(projects)s\n"
"To continue, choose different workspaces or turn off the Documents feature for these projects."
msgstr ""

#. module: documents_project
#. odoo-python
#: code:addons/documents_project/models/documents_document.py:0
msgid ""
"This folder should remain in the same company as the \"%(project)s\" project"
" to which it is linked. Please update the company of the \"%(project)s\" "
"project, or leave the company of this workspace empty."
msgstr ""

#. module: documents_project
#. odoo-python
#: code:addons/documents_project/models/documents_document.py:0
msgid ""
"This folder should remain in the same company as the following projects to which it is linked:\n"
"%s\n"
"\n"
"Please update the company of those projects, or leave the company of this workspace empty."
msgstr ""

#. module: documents_project
#: model_terms:ir.actions.act_window,help:documents_project.action_view_documents_project_project
#: model_terms:ir.actions.act_window,help:documents_project.action_view_documents_project_task
msgid ""
"Upload <span class=\"fw-normal\">a file or </span>drag <span class=\"fw-"
"normal\">it here.</span>"
msgstr ""

#. module: documents_project
#: model:ir.model.fields,field_description:documents_project.field_project_task__folder_user_permission
msgid "User permission"
msgstr "Потребителско разрешение"

#. module: documents_project
#: model:ir.actions.server,name:documents_project.ir_actions_server_ask_validate
msgid "Validate"
msgstr "Валидирай"

#. module: documents_project
#: model:ir.actions.server,name:documents_project.ir_actions_server_validate_code
msgid "Validate - Check conditions"
msgstr ""

#. module: documents_project
#. odoo-python
#: code:addons/documents_project/models/project_project.py:0
msgid ""
"You cannot change the company of this project, because its workspace is linked to the other following projects that are still in the \"%(other_company)s\" company:\n"
"%(other_workspaces)s\n"
"\n"
"Please update the company of all projects so that they remain in the same company as their workspace, or leave the company of the \"%(workspace)s\" workspace blank."
msgstr ""

#. module: documents_project
#. odoo-python
#: code:addons/documents_project/models/documents_document.py:0
msgid "You cannot create a task from a folder."
msgstr ""

#. module: documents_project
#. odoo-python
#: code:addons/documents_project/models/documents_document.py:0
msgid "You cannot set a company on the %s folder."
msgstr ""

#. module: documents_project
#. odoo-python
#: code:addons/documents_project/models/documents_document.py:0
msgid "new %(model)s from %(new_record)s"
msgstr ""
