# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* documents_project
# 
# Translators:
# <PERSON><PERSON>, 2024
# <PERSON><PERSON><PERSON>, 2025
# <PERSON> <<EMAIL>>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-03-26 20:47+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2025\n"
"Language-Team: Spanish (Latin America) (https://app.transifex.com/odoo/teams/41243/es_419/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es_419\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: documents_project
#: model:ir.actions.server,name:documents_project.ir_actions_server_tag_add_draft
msgid "Add Tag Draft"
msgstr "Agregar etiqueta de borrador"

#. module: documents_project
#: model:ir.actions.server,name:documents_project.ir_actions_server_tag_add_to_validate
msgid "Add Tag To Validate"
msgstr "Agregar etiqueta por validar"

#. module: documents_project
#: model:ir.actions.server,name:documents_project.ir_actions_server_tag_add_validated
msgid "Add Tag Validated"
msgstr "Agregar etiqueta validada"

#. module: documents_project
#: model:ir.actions.server,name:documents_project.ir_actions_server_ask_for_validation_code
msgid "Ask For Validation - Check conditions"
msgstr "Pedir validación - revisar condiciones"

#. module: documents_project
#: model:ir.actions.server,name:documents_project.ir_actions_server_ask_for_validation
msgid "Ask for validation"
msgstr "Solicitar validación"

#. module: documents_project
#: model:ir.model,name:documents_project.model_ir_attachment
msgid "Attachment"
msgstr "Archivo adjunto"

#. module: documents_project
#: model_terms:ir.ui.view,arch_db:documents_project.edit_project_document_form_inherit
msgid "Categorize and share your documents with your customers"
msgstr "Clasifique y comparta sus documentos con sus clientes"

#. module: documents_project
#: model:ir.actions.server,name:documents_project.ir_actions_server_create_a_task_code
#: model:ir.actions.server,name:documents_project.ir_actions_server_create_project_task
msgid "Create a Task"
msgstr "Crear una tarea"

#. module: documents_project
#: model:ir.model.fields,field_description:documents_project.field_project_project__documents_tag_ids
msgid "Default Tags"
msgstr "Etiquetas predeterminadas"

#. module: documents_project
#: model:ir.actions.server,name:documents_project.ir_actions_server_create_project_deprecate
#: model:ir.actions.server,name:documents_project.ir_actions_server_tag_add_deprecated
#: model:ir.actions.server,name:documents_project.ir_actions_server_tag_remove_deprecated
msgid "Deprecate"
msgstr "Obsoletizar"

#. module: documents_project
#: model:ir.actions.server,name:documents_project.ir_actions_server_deprecate_code
msgid "Deprecate - Check conditions"
msgstr "Depreciar - revisar condiciones"

#. module: documents_project
#: model:ir.model,name:documents_project.model_documents_document
msgid "Document"
msgstr "Documento"

#. module: documents_project
#. odoo-python
#: code:addons/documents_project/controllers/portal.py:0
#: code:addons/documents_project/models/project_project.py:0
#: model:ir.actions.act_window,name:documents_project.action_view_documents_project_project
#: model:ir.actions.act_window,name:documents_project.action_view_documents_project_task
#: model:ir.embedded.actions,name:documents_project.project_embedded_action_documents
#: model:ir.embedded.actions,name:documents_project.project_embedded_action_documents_dashboard
#: model:ir.model.fields,field_description:documents_project.field_project_project__use_documents
#: model:ir.model.fields,field_description:documents_project.field_project_task__document_ids
#: model_terms:ir.ui.view,arch_db:documents_project.portal_my_task
#: model_terms:ir.ui.view,arch_db:documents_project.portal_tasks_list
#: model_terms:ir.ui.view,arch_db:documents_project.project_sharing_project_task_view_form_inherit
#: model_terms:ir.ui.view,arch_db:documents_project.project_view_kanban_inherit_documents
#: model_terms:ir.ui.view,arch_db:documents_project.view_task_form2_document_inherit
msgid "Documents"
msgstr "Documentos"

#. module: documents_project
#: model_terms:ir.ui.view,arch_db:documents_project.edit_project_document_form_inherit
msgid "Documents &amp; Analytics"
msgstr "Documentos y análisis"

#. module: documents_project
#. odoo-python
#: code:addons/documents_project/models/documents_document.py:0
msgid "Documents already linked to a task."
msgstr "Documentos que ya están vinculados a esta tarea."

#. module: documents_project
#: model:ir.model.fields,field_description:documents_project.field_project_project__documents_folder_id
msgid "Folder"
msgstr "Carpeta"

#. module: documents_project
#: model:ir.model.fields,help:documents_project.field_project_project__documents_folder_id
#: model:ir.model.fields,help:documents_project.field_project_task__documents_folder_id
msgid ""
"Folder in which all of the documents of this project will be categorized. "
"All of the attachments of your tasks will be automatically added as "
"documents in this workspace as well."
msgstr ""
"Carpeta en la que se clasificarán todos los documentos de este proyecto. "
"Todos los archivos adjuntos de sus tareas se agregarán de forma automática "
"como documentos a este espacio de trabajo."

#. module: documents_project
#. odoo-python
#: code:addons/documents_project/models/documents_document.py:0
msgid "Impossible to create a task on a deprecated document"
msgstr "No es posible crear una tarea en un documento obsoleto."

#. module: documents_project
#. odoo-python
#: code:addons/documents_project/models/documents_document.py:0
msgid ""
"Impossible to update write `res_id` without `access_internal` for records "
"with different `res_models` when some are linked to projects or tasks."
msgstr ""
"No es posible actualizar `res_id` sin `access_internal` para los registros "
"con distintos `res_models` cuando algunos están vinculados a proyectos o "
"tareas."

#. module: documents_project
#. odoo-python
#: code:addons/documents_project/models/documents_document.py:0
msgid "Invalid project search"
msgstr "Búsqueda de proyecto no válida"

#. module: documents_project
#. odoo-python
#: code:addons/documents_project/models/documents_document.py:0
msgid "Invalid task search"
msgstr "Búsqueda de tarea no válida"

#. module: documents_project
#: model:ir.actions.server,name:documents_project.ir_actions_server_mark_as_draft_code
msgid "Mark As Draft - Check conditions"
msgstr "Marcar como borrador - revisar condiciones"

#. module: documents_project
#: model:ir.actions.server,name:documents_project.ir_actions_server_mark_as_draft
msgid "Mark as draft"
msgstr "Marcar como borrador"

#. module: documents_project
#. odoo-python
#: code:addons/documents_project/models/documents_document.py:0
msgid "New task from Documents"
msgstr "Nueva tarea de Documentos"

#. module: documents_project
#: model:ir.model,name:documents_project.model_project_project
#: model_terms:ir.ui.view,arch_db:documents_project.document_view_search_inherit
msgid "Project"
msgstr "Proyecto"

#. module: documents_project
#: model:ir.model.fields,field_description:documents_project.field_documents_document__project_ids
msgid "Projects"
msgstr "Proyectos"

#. module: documents_project
#: model:ir.actions.server,name:documents_project.ir_actions_server_tag_remove_draft
msgid "Remove Tag Draft"
msgstr "Eliminar etiqueta de borrador"

#. module: documents_project
#: model:ir.actions.server,name:documents_project.ir_actions_server_tag_remove_to_validate
msgid "Remove Tag To Validate"
msgstr "Quitar etiqueta por validar"

#. module: documents_project
#: model:ir.actions.server,name:documents_project.ir_actions_server_tag_remove_validated
msgid "Remove Tag Validated"
msgstr "Quitar etiqueta validada"

#. module: documents_project
#: model:ir.model,name:documents_project.model_project_task
#: model_terms:ir.ui.view,arch_db:documents_project.document_view_search_inherit
msgid "Task"
msgstr "Tarea"

#. module: documents_project
#. odoo-python
#: code:addons/documents_project/models/documents_document.py:0
msgid "Task created from document %s"
msgstr "Tarea creada desde el documento %s"

#. module: documents_project
#. odoo-python
#: code:addons/documents_project/models/documents_document.py:0
msgid "Task created from documents"
msgstr "Tarea creada desde los documentos"

#. module: documents_project
#. odoo-python
#: code:addons/documents_project/models/project_task.py:0
msgid "Task's Documents"
msgstr "Documentos de la tarea"

#. module: documents_project
#. odoo-python
#: code:addons/documents_project/models/project_project.py:0
msgid ""
"The \"%(folder)s\" folder should either be in the \"%(company)s\" company "
"like this project or be open to all companies."
msgstr ""
"La carpeta \"%(folder)s\" debe estar en la empresa \"%(company)s\" como este"
" proyecto o debe abrirse para todas las empresas."

#. module: documents_project
#. odoo-python
#: code:addons/documents_project/models/documents_document.py:0
msgid ""
"The \"%s\" workspace is required by the Project application and cannot be "
"archived."
msgstr ""
"La aplicación Proyecto necesita del espacio de trabajo \"%s\" así que no lo "
"puede archivar."

#. module: documents_project
#. odoo-python
#: code:addons/documents_project/models/documents_document.py:0
msgid ""
"The \"%s\" workspace is required by the Project application and cannot be "
"deleted."
msgstr ""
"La aplicación Proyecto necesita del espacio de trabajo \"%s\" así que no lo "
"puede eliminar."

#. module: documents_project
#. odoo-python
#: code:addons/documents_project/models/documents_document.py:0
msgid ""
"This action can't be performed, as it would remove the workspaces used by the following projects:\n"
"%(projects)s\n"
"To continue, choose different workspaces or turn off the Documents feature for these projects."
msgstr ""
"No puede realizar esta acción ya que borraría los espacios de trabajo que usan los siguientes proyectos:\n"
"%(projects)s\n"
"Para continuar, seleccione espacios de trabajo diferentes o desactive la función Documentos para estos proyectos"

#. module: documents_project
#. odoo-python
#: code:addons/documents_project/models/documents_document.py:0
msgid ""
"This folder should remain in the same company as the \"%(project)s\" project"
" to which it is linked. Please update the company of the \"%(project)s\" "
"project, or leave the company of this workspace empty."
msgstr ""
"Esta carpeta debe permanecer en la misma empresa que el proyecto "
"\"%(project)s\" al cual está vinculado. Actualice la empresa del proyecto "
"\"%(project)s\" o deje la empresa de este espacio de trabajo en blanco."

#. module: documents_project
#. odoo-python
#: code:addons/documents_project/models/documents_document.py:0
msgid ""
"This folder should remain in the same company as the following projects to which it is linked:\n"
"%s\n"
"\n"
"Please update the company of those projects, or leave the company of this workspace empty."
msgstr ""
"Esta carpeta debe permanecer en la misma empresa que los siguientes proyectos a los que está vinculado:\n"
"%s\n"
"\n"
"Actualice la empresa de esos proyectos o deje la empresa del espacio de trabajo vacía."

#. module: documents_project
#: model_terms:ir.actions.act_window,help:documents_project.action_view_documents_project_project
#: model_terms:ir.actions.act_window,help:documents_project.action_view_documents_project_task
msgid ""
"Upload <span class=\"fw-normal\">a file or </span>drag <span class=\"fw-"
"normal\">it here.</span>"
msgstr ""
"Suba <span class=\"fw-normal\">un archivo o </span>arrástrelo <span "
"class=\"fw-normal\">aquí.</span>"

#. module: documents_project
#: model:ir.model.fields,field_description:documents_project.field_project_task__folder_user_permission
msgid "User permission"
msgstr "Permiso del usuario"

#. module: documents_project
#: model:ir.actions.server,name:documents_project.ir_actions_server_ask_validate
msgid "Validate"
msgstr "Validar "

#. module: documents_project
#: model:ir.actions.server,name:documents_project.ir_actions_server_validate_code
msgid "Validate - Check conditions"
msgstr "Validar - comprobar condiciones"

#. module: documents_project
#. odoo-python
#: code:addons/documents_project/models/project_project.py:0
msgid ""
"You cannot change the company of this project, because its workspace is linked to the other following projects that are still in the \"%(other_company)s\" company:\n"
"%(other_workspaces)s\n"
"\n"
"Please update the company of all projects so that they remain in the same company as their workspace, or leave the company of the \"%(workspace)s\" workspace blank."
msgstr ""
"No puede cambiar la empresa de este proyecto ya que su espacio de trabajo está vinculado a estos proyectos, los cuales siguen en la empresa  \"%(other_company)s\":\n"
"%(other_workspaces)s\n"
"\n"
"Actualice la empresa de todos los proyectos para que permanezcan en la misma empresa que su espacio de trabajo o deje la empresa del espacio de trabajo \"%(workspace)s\" vacía."

#. module: documents_project
#. odoo-python
#: code:addons/documents_project/models/documents_document.py:0
msgid "You cannot create a task from a folder."
msgstr "No puede crear una tarea desde una carpeta."

#. module: documents_project
#. odoo-python
#: code:addons/documents_project/models/documents_document.py:0
msgid "You cannot set a company on the %s folder."
msgstr "No puede configurar una empresa en la carpeta %s."

#. module: documents_project
#. odoo-python
#: code:addons/documents_project/models/documents_document.py:0
msgid "new %(model)s from %(new_record)s"
msgstr "nuevo %(model)s de %(new_record)s"
