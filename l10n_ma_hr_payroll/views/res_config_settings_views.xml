<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="res_config_settings_view_form" model="ir.ui.view">
        <field name="name">res.config.settings.view.form.inherit.l10n_ma_hr_payroll</field>
        <field name="model">res.config.settings</field>
        <field name="priority" eval="45"/>
        <field name="inherit_id" ref="hr_payroll.res_config_settings_view_form"/>
        <field name="arch" type="xml">
            <block id="hr_payroll_accountant" position="after">
                <field name="company_country_code" invisible="1"/>
                <block title="Moroccan Localization" invisible="company_country_code != 'MA'">
                    <setting string="Company Information" company_dependent="1">
                        <div class="text-muted">
                            Offical Company Information
                        </div>
                        <group class="mt16">
                            <field name="l10n_ma_employer_contribution"/>
                            <field name="l10n_ma_social_security_organization"/>
                            <field name="l10n_ma_collective_agreement"/>
                        </group>
                    </setting>
                </block>
            </block>
        </field>
    </record>
</odoo>
