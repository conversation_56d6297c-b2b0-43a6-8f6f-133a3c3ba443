# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_payroll_expense
# 
# Translators:
# Wil Odoo, 2024
# <AUTHOR> <EMAIL>, 2024
# <PERSON>, 2024
# <PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:26+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Chinese (China) (https://app.transifex.com/odoo/teams/41243/zh_CN/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_CN\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: hr_payroll_expense
#: model_terms:ir.ui.view,arch_db:hr_payroll_expense.view_account_payment_register_form_inherit_hr_payroll_expense
msgid ""
"<span class=\"fw-bold\">\n"
"                            The expenses will already be reimbursed in a payslip. Make sure this payment is intended.\n"
"                        </span>"
msgstr ""
"<span class=\"fw-bold\">\n"
"                            费用已在工资单中报销。请确认这笔付款是有意安排的。\n"
"                        </span>"

#. module: hr_payroll_expense
#: model_terms:ir.ui.view,arch_db:hr_payroll_expense.hr_expense_sheet_view_form_inherit_payroll
msgid "<span class=\"o_stat_text\">Payslip</span>"
msgstr "<span class=\"o_stat_text\">工资单</span>"

#. module: hr_payroll_expense
#: model:ir.model,name:hr_payroll_expense.model_hr_expense_sheet
msgid "Expense Report"
msgstr "费用报表"

#. module: hr_payroll_expense
#. odoo-python
#: code:addons/hr_payroll_expense/models/hr_expense_sheet.py:0
msgid ""
"Expense reimbursement rule needs to be configured to add expenses to payslips.\n"
"Please create one salary rule with the \"%(code)s\" code on the relevant salary structures."
msgstr ""
"需要配置费用报销规则，以便在工资单上添加费用。\n"
"请在相关工资结构上创建一个带有“%(code)s ”代码的工资规则。"

#. module: hr_payroll_expense
#. odoo-python
#: code:addons/hr_payroll_expense/models/hr_expense_sheet.py:0
msgid "Expense report (\"%(name)s\") was removed from the next payslip."
msgstr "从下一张工资单中删除了费用报告（\"%(name)s\"）。"

#. module: hr_payroll_expense
#. odoo-python
#: code:addons/hr_payroll_expense/models/hr_expense_sheet.py:0
msgid "Expense report (\"%(name)s\") will be added to the next payslip."
msgstr "费用报告（\"%(name)s\"）将添加到下一张工资单中。"

#. module: hr_payroll_expense
#: model:ir.model.fields,field_description:hr_payroll_expense.field_hr_payslip__expense_sheet_ids
#: model_terms:ir.ui.view,arch_db:hr_payroll_expense.hr_payslip_view_form_inherit_expense
msgid "Expenses"
msgstr "费用"

#. module: hr_payroll_expense
#: model:ir.model.fields,field_description:hr_payroll_expense.field_hr_payslip__expenses_count
msgid "Expenses Count"
msgstr "费用计数"

#. module: hr_payroll_expense
#: model:hr.salary.rule,name:hr_payroll_expense.hr_salary_rule_expense_refund
msgid "Expenses Reimbursement"
msgstr "费用报销"

#. module: hr_payroll_expense
#: model:ir.model.fields,help:hr_payroll_expense.field_hr_payslip__expense_sheet_ids
msgid "Expenses to reimburse to employee."
msgstr "报销给员工的费用。"

#. module: hr_payroll_expense
#. odoo-python
#: code:addons/hr_payroll_expense/models/hr_expense_sheet.py:0
msgid "Go to salary rules"
msgstr "转到薪资规则"

#. module: hr_payroll_expense
#: model:ir.model.fields,field_description:hr_payroll_expense.field_account_payment_register__is_already_paid_through_a_payslip
msgid "Is Already Paid Through A Payslip"
msgstr "已通过工资单支付"

#. module: hr_payroll_expense
#: model:ir.model,name:hr_payroll_expense.model_account_move
msgid "Journal Entry"
msgstr "日记账分录"

#. module: hr_payroll_expense
#. odoo-python
#: code:addons/hr_payroll_expense/models/hr_payroll_structure.py:0
msgid ""
"No debit account found in the '%(rule_name)s' payslip salary rule. Please "
"add a payable debit account to be able to create an accounting entry for the"
" expense reports linked to this payslip."
msgstr ""
"工资单薪酬规则 “%(rule_name)s” 中，没有找到扣账账户。请新增一个应付款项扣账账户，以便能够为已连接至此工资单的开支报告，创建会计记项。"

#. module: hr_payroll_expense
#. odoo-python
#: code:addons/hr_payroll_expense/models/hr_payslip.py:0
msgid ""
"No salary rule was found to handle expenses in structure "
"'%(structure_name)s'."
msgstr "找不到薪酬规则可处理以下薪酬结构中的报销开支：%(structure_name)s。"

#. module: hr_payroll_expense
#. odoo-python
#: code:addons/hr_payroll_expense/models/hr_expense_sheet.py:0
msgid ""
"Only approved expense reports that were paid by an employee can be "
"reimbursed in a payslip."
msgstr "只有经批准由员工支付的费用报告，才能在工资单中报销。"

#. module: hr_payroll_expense
#: model:ir.model,name:hr_payroll_expense.model_account_payment_register
msgid "Pay"
msgstr "支付"

#. module: hr_payroll_expense
#: model:ir.model,name:hr_payroll_expense.model_hr_payslip
msgid "Pay Slip"
msgstr "工资单"

#. module: hr_payroll_expense
#. odoo-python
#: code:addons/hr_payroll_expense/models/hr_expense_sheet.py:0
#: model:ir.model.fields,field_description:hr_payroll_expense.field_hr_expense_sheet__payslip_id
msgid "Payslip"
msgstr "工资单"

#. module: hr_payroll_expense
#: model:ir.model.fields,field_description:hr_payroll_expense.field_hr_expense_sheet__refund_in_payslip
msgid "Reimburse In Next Payslip"
msgstr "下一个工资单中报销"

#. module: hr_payroll_expense
#. odoo-python
#: code:addons/hr_payroll_expense/models/hr_payslip.py:0
msgid "Reimbursed Expenses"
msgstr "报销费用"

#. module: hr_payroll_expense
#: model_terms:ir.ui.view,arch_db:hr_payroll_expense.hr_expense_sheet_view_form_inherit_payroll
msgid "Remove from Payslip"
msgstr "从工资单上移除"

#. module: hr_payroll_expense
#: model:ir.actions.server,name:hr_payroll_expense.hr_expense_add_to_payslip_action_server
#: model_terms:ir.ui.view,arch_db:hr_payroll_expense.hr_expense_sheet_view_form_inherit_payroll
msgid "Report in Next Payslip"
msgstr "于下期工资单报表"

#. module: hr_payroll_expense
#: model:ir.model,name:hr_payroll_expense.model_hr_payroll_structure
msgid "Salary Structure"
msgstr "工资结构"

#. module: hr_payroll_expense
#. odoo-python
#: code:addons/hr_payroll_expense/models/hr_payroll_structure.py:0
msgid ""
"The '%(account_name)s' account for the salary rule '%(rule_name)s' must be "
"of type 'Payable'."
msgstr "账户“%(account_name)s”是用于薪酬规则“%(rule_name)s”，账户类型必须是“应付款项”。"

#. module: hr_payroll_expense
#. odoo-python
#: code:addons/hr_payroll_expense/models/hr_payslip.py:0
msgid ""
"The salary rules with the code 'EXPENSES' must have a debit account set to "
"be able to properly reimburse the linked expenses. This must be an account "
"of type 'Payable'."
msgstr "代码为 'EXPENSES' 8的薪金规则必须设置一个借方账户，以便能够正确报销相关费用。该账户必须是 \"应付 \"类型的账户。"

#. module: hr_payroll_expense
#. odoo-python
#: code:addons/hr_payroll_expense/models/hr_expense_sheet.py:0
msgid ""
"The state of the accounting entries linked to this expense report do not "
"allow it to be reimbursed through a payslip."
msgstr "连接至此费用报告的会计记项，其状态不允许通过工资单处理发还。"

#. module: hr_payroll_expense
#. odoo-python
#: code:addons/hr_payroll_expense/models/hr_expense_sheet.py:0
msgid "There are no valid expense sheets selected."
msgstr "所选项目中，没有有效的开支表。"

#. module: hr_payroll_expense
#. odoo-python
#: code:addons/hr_payroll_expense/models/hr_expense_sheet.py:0
msgid ""
"You cannot remove an expense from a payslip that has already been validated.\n"
"Expenses can only be removed from draft or canceled payslips."
msgstr ""
"您不能从已验证的工资单上删除费用。\n"
"只能从草稿或已取消的工资单中删除。"

#. module: hr_payroll_expense
#. odoo-python
#: code:addons/hr_payroll_expense/models/hr_payslip.py:0
msgid ""
"You don't have the access rights to link an expense report to a payslip. You"
" need to be a payroll officer to do that."
msgstr "您没有访问权限将费用报告与工资单联系起来。您需要成为薪资管理人员，才能执行此操作。"

#. module: hr_payroll_expense
#. odoo-python
#: code:addons/hr_payroll_expense/models/account_move.py:0
msgid "You don't have the access rights to post an invoice."
msgstr "没有过账发票的访问权限."
