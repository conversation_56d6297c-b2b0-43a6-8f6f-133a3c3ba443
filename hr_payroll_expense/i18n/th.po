# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_payroll_expense
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON><PERSON><PERSON> Lappiam, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:26+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Rasareeyar Lappiam, 2025\n"
"Language-Team: Thai (https://app.transifex.com/odoo/teams/41243/th/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: th\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: hr_payroll_expense
#: model_terms:ir.ui.view,arch_db:hr_payroll_expense.view_account_payment_register_form_inherit_hr_payroll_expense
msgid ""
"<span class=\"fw-bold\">\n"
"                            The expenses will already be reimbursed in a payslip. Make sure this payment is intended.\n"
"                        </span>"
msgstr ""
"<span class=\"fw-bold\">\n"
"                            ค่าใช้จ่ายจะได้รับการชำระคืนในใบรับเงินแล้ว ตรวจสอบให้แน่ใจว่ามีจุดประสงค์ในการชำระเงินนี้\n"
"                        </span>"

#. module: hr_payroll_expense
#: model_terms:ir.ui.view,arch_db:hr_payroll_expense.hr_expense_sheet_view_form_inherit_payroll
msgid "<span class=\"o_stat_text\">Payslip</span>"
msgstr "<span class=\"o_stat_text\">สลิปเงินเดือน</span>"

#. module: hr_payroll_expense
#: model:ir.model,name:hr_payroll_expense.model_hr_expense_sheet
msgid "Expense Report"
msgstr "รายงานรายจ่าย"

#. module: hr_payroll_expense
#. odoo-python
#: code:addons/hr_payroll_expense/models/hr_expense_sheet.py:0
msgid ""
"Expense reimbursement rule needs to be configured to add expenses to payslips.\n"
"Please create one salary rule with the \"%(code)s\" code on the relevant salary structures."
msgstr ""
"จำเป็นต้องกำหนดค่ากฎการขอคืนเงินค่าใช้จ่ายเพื่อเพิ่มค่าใช้จ่ายลงในสลิปเงินเดือน\n"
"โปรดสร้างกฎเงินเดือนหนึ่งรายการโดยใช้รหัส \"%(code)s\" ในโครงสร้างเงินเดือนที่เกี่ยวข้อง"

#. module: hr_payroll_expense
#. odoo-python
#: code:addons/hr_payroll_expense/models/hr_expense_sheet.py:0
msgid "Expense report (\"%(name)s\") was removed from the next payslip."
msgstr "รายงานค่าใช้จ่าย (\"%(name)s\") ถูกลบออกจากสลิปเงินเดือนถัดไป"

#. module: hr_payroll_expense
#. odoo-python
#: code:addons/hr_payroll_expense/models/hr_expense_sheet.py:0
msgid "Expense report (\"%(name)s\") will be added to the next payslip."
msgstr "รายงานค่าใช้จ่าย (\"%(name)s\") จะถูกเพิ่มลงในสลิปเงินเดือนถัดไป"

#. module: hr_payroll_expense
#: model:ir.model.fields,field_description:hr_payroll_expense.field_hr_payslip__expense_sheet_ids
#: model_terms:ir.ui.view,arch_db:hr_payroll_expense.hr_payslip_view_form_inherit_expense
msgid "Expenses"
msgstr "รายจ่าย"

#. module: hr_payroll_expense
#: model:ir.model.fields,field_description:hr_payroll_expense.field_hr_payslip__expenses_count
msgid "Expenses Count"
msgstr "จำนวนรายจ่าย"

#. module: hr_payroll_expense
#: model:hr.salary.rule,name:hr_payroll_expense.hr_salary_rule_expense_refund
msgid "Expenses Reimbursement"
msgstr "เบิกค่าใช้จ่าย"

#. module: hr_payroll_expense
#: model:ir.model.fields,help:hr_payroll_expense.field_hr_payslip__expense_sheet_ids
msgid "Expenses to reimburse to employee."
msgstr "ค่าใช้จ่ายในการชดใช้ให้กับพนักงาน"

#. module: hr_payroll_expense
#. odoo-python
#: code:addons/hr_payroll_expense/models/hr_expense_sheet.py:0
msgid "Go to salary rules"
msgstr ""

#. module: hr_payroll_expense
#: model:ir.model.fields,field_description:hr_payroll_expense.field_account_payment_register__is_already_paid_through_a_payslip
msgid "Is Already Paid Through A Payslip"
msgstr ""

#. module: hr_payroll_expense
#: model:ir.model,name:hr_payroll_expense.model_account_move
msgid "Journal Entry"
msgstr "รายการสมุดรายวัน"

#. module: hr_payroll_expense
#. odoo-python
#: code:addons/hr_payroll_expense/models/hr_payroll_structure.py:0
msgid ""
"No debit account found in the '%(rule_name)s' payslip salary rule. Please "
"add a payable debit account to be able to create an accounting entry for the"
" expense reports linked to this payslip."
msgstr ""
"ไม่พบบัญชีเดบิตในกฎเงินเดือนสลิปเงินเดือน '%(rule_name)s' "
"โปรดเพิ่มบัญชีเดบิตที่ต้องชำระเพื่อให้สามารถสร้างรายการบัญชีสำหรับรายงานค่าใช้จ่ายที่เชื่อมโยงกับสลิปเงินเดือนนี้ได้"

#. module: hr_payroll_expense
#. odoo-python
#: code:addons/hr_payroll_expense/models/hr_payslip.py:0
msgid ""
"No salary rule was found to handle expenses in structure "
"'%(structure_name)s'."
msgstr "ไม่พบกฎเงินเดือนในการจัดการค่าใช้จ่ายในโครงสร้าง '%(structure_name)s'"

#. module: hr_payroll_expense
#. odoo-python
#: code:addons/hr_payroll_expense/models/hr_expense_sheet.py:0
msgid ""
"Only approved expense reports that were paid by an employee can be "
"reimbursed in a payslip."
msgstr ""
"เฉพาะรายงานค่าใช้จ่ายที่ได้รับอนุมัติซึ่งจ่ายโดยพนักงานเท่านั้นที่สามารถขอคืนเป็นสลิปเงินเดือนได้"

#. module: hr_payroll_expense
#: model:ir.model,name:hr_payroll_expense.model_account_payment_register
msgid "Pay"
msgstr "จ่าย"

#. module: hr_payroll_expense
#: model:ir.model,name:hr_payroll_expense.model_hr_payslip
msgid "Pay Slip"
msgstr "สลิปเงินเดือน"

#. module: hr_payroll_expense
#. odoo-python
#: code:addons/hr_payroll_expense/models/hr_expense_sheet.py:0
#: model:ir.model.fields,field_description:hr_payroll_expense.field_hr_expense_sheet__payslip_id
msgid "Payslip"
msgstr "สลิปเงินเดือน"

#. module: hr_payroll_expense
#: model:ir.model.fields,field_description:hr_payroll_expense.field_hr_expense_sheet__refund_in_payslip
msgid "Reimburse In Next Payslip"
msgstr "คืนเงินในสลิปเงินเดือนถัดไป"

#. module: hr_payroll_expense
#. odoo-python
#: code:addons/hr_payroll_expense/models/hr_payslip.py:0
msgid "Reimbursed Expenses"
msgstr "ค่าใช้จ่ายที่ได้รับคืน"

#. module: hr_payroll_expense
#: model_terms:ir.ui.view,arch_db:hr_payroll_expense.hr_expense_sheet_view_form_inherit_payroll
msgid "Remove from Payslip"
msgstr "ลบออกจากสลิปเงินเดือน"

#. module: hr_payroll_expense
#: model:ir.actions.server,name:hr_payroll_expense.hr_expense_add_to_payslip_action_server
#: model_terms:ir.ui.view,arch_db:hr_payroll_expense.hr_expense_sheet_view_form_inherit_payroll
msgid "Report in Next Payslip"
msgstr "รายงานในสลิปเงินเดือนครั้งต่อไป"

#. module: hr_payroll_expense
#: model:ir.model,name:hr_payroll_expense.model_hr_payroll_structure
msgid "Salary Structure"
msgstr "โครงสร้างเงินเดือน"

#. module: hr_payroll_expense
#. odoo-python
#: code:addons/hr_payroll_expense/models/hr_payroll_structure.py:0
msgid ""
"The '%(account_name)s' account for the salary rule '%(rule_name)s' must be "
"of type 'Payable'."
msgstr ""
"บัญชี '%(account_name)s' สำหรับกฎเงินเดือน '%(rule_name)s' ต้องเป็นประเภท "
"'ที่ต้องชำระ'"

#. module: hr_payroll_expense
#. odoo-python
#: code:addons/hr_payroll_expense/models/hr_payslip.py:0
msgid ""
"The salary rules with the code 'EXPENSES' must have a debit account set to "
"be able to properly reimburse the linked expenses. This must be an account "
"of type 'Payable'."
msgstr ""
"กฎเงินเดือนที่มีรหัส 'รายจ่าย' "
"จะต้องมีบัญชีเดบิตที่ตั้งค่าไว้เพื่อให้สามารถชำระค่าใช้จ่ายที่เชื่อมโยงได้อย่างถูกต้อง"
" ต้องเป็นบัญชีประเภท 'เจ้าหนี้'"

#. module: hr_payroll_expense
#. odoo-python
#: code:addons/hr_payroll_expense/models/hr_expense_sheet.py:0
msgid ""
"The state of the accounting entries linked to this expense report do not "
"allow it to be reimbursed through a payslip."
msgstr ""
"สถานะของรายการทางบัญชีที่เชื่อมโยงกับรายงานค่าใช้จ่ายนี้ไม่อนุญาตให้ชำระคืนผ่านสลิปเงินเดือน"

#. module: hr_payroll_expense
#. odoo-python
#: code:addons/hr_payroll_expense/models/hr_expense_sheet.py:0
msgid "There are no valid expense sheets selected."
msgstr "ไม่มีการเลือกแผ่นค่าใช้จ่ายที่ถูกต้อง"

#. module: hr_payroll_expense
#. odoo-python
#: code:addons/hr_payroll_expense/models/hr_expense_sheet.py:0
msgid ""
"You cannot remove an expense from a payslip that has already been validated.\n"
"Expenses can only be removed from draft or canceled payslips."
msgstr ""
"คุณไม่สามารถลบค่าใช้จ่ายออกจากสลิปเงินเดือนที่ผ่านการตรวจสอบแล้วได้\n"
"ค่าใช้จ่ายสามารถลบออกจากร่างหรือสลิปเงินเดือนที่ถูกยกเลิกแล้วเท่านั้น"

#. module: hr_payroll_expense
#. odoo-python
#: code:addons/hr_payroll_expense/models/hr_payslip.py:0
msgid ""
"You don't have the access rights to link an expense report to a payslip. You"
" need to be a payroll officer to do that."
msgstr ""
"คุณไม่มีสิทธิ์ในการเชื่อมโยงรายงานค่าใช้จ่ายกับสลิปเงินเดือน "
"คุณต้องเป็นเจ้าหน้าที่บัญชีเงินเดือนจึงจะดำเนินการเช่นนั้นได้"

#. module: hr_payroll_expense
#. odoo-python
#: code:addons/hr_payroll_expense/models/account_move.py:0
msgid "You don't have the access rights to post an invoice."
msgstr "คุณไม่มีสิทธิ์เข้าถึงในการผ่านรายการใบแจ้งหนี้"
