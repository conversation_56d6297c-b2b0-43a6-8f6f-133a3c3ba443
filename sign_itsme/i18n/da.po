# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sign_itsme
# 
# Translators:
# <AUTHOR> <EMAIL>, 2024
# <PERSON>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:28+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: Danish (https://app.transifex.com/odoo/teams/41243/da/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: da\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: sign_itsme
#: model_terms:ir.ui.view,arch_db:sign_itsme.sign_request_logs_user
msgid ""
"<small>Name: The signatory has provided this identity through itsme®</small>"
msgstr ""

#. module: sign_itsme
#: model:iap.service,description:sign_itsme.iap_service_itsme_proxy
msgid ""
"Ask document signatories in Odoo Sign to provide their identity using the "
"itsme® identity platform. By combining Odoo Sign with itsme®️, you can add "
"an identification step in your signature flows and ask signatories to "
"provide their identity through the itsme®️ platform, using their mobile "
"device. Available in Belgium and in the Netherlands."
msgstr ""

#. module: sign_itsme
#: model:iap.service,unit_name:sign_itsme.iap_service_itsme_proxy
msgid "Credits"
msgstr ""

#. module: sign_itsme
#: model:sign.item.role,name:sign_itsme.sign_item_role_itsme_customer
msgid "Customer (identified with itsme®)"
msgstr ""

#. module: sign_itsme
#. odoo-javascript
#: code:addons/sign_itsme/static/src/components/document_signable.js:0
msgid "Error"
msgstr "Fejl"

#. module: sign_itsme
#: model:ir.model.fields,field_description:sign_itsme.field_sign_item_role__auth_method
msgid "Extra Authentication Step"
msgstr ""

#. module: sign_itsme
#: model:ir.model.fields,help:sign_itsme.field_sign_item_role__auth_method
msgid "Force the signatory to identify using a second authentication method"
msgstr ""

#. module: sign_itsme
#. odoo-javascript
#: code:addons/sign_itsme/static/src/dialogs/itsme_dialog.xml:0
msgid "Go Back"
msgstr "Gå tilbage"

#. module: sign_itsme
#. odoo-javascript
#: code:addons/sign_itsme/static/src/components/document_signable.js:0
msgid "Identification refused"
msgstr ""

#. module: sign_itsme
#. odoo-javascript
#: code:addons/sign_itsme/static/src/dialogs/itsme_dialog.xml:0
msgid "Identify with itsme"
msgstr ""

#. module: sign_itsme
#: model_terms:ir.ui.view,arch_db:sign_itsme.sign_request_logs_user
msgid "Name"
msgstr "Navn"

#. module: sign_itsme
#. odoo-javascript
#: code:addons/sign_itsme/static/src/dialogs/itsme_dialog.xml:0
msgid "Please confirm your identity to finalize your signature."
msgstr ""

#. module: sign_itsme
#. odoo-python
#: code:addons/sign_itsme/models/sign_request_item.py:0
msgid "Sign request item is not validated yet."
msgstr ""

#. module: sign_itsme
#: model:ir.model,name:sign_itsme.model_sign_item_role
msgid "Signature Item Party"
msgstr "Underskriftspart"

#. module: sign_itsme
#: model:ir.model,name:sign_itsme.model_sign_request
msgid "Signature Request"
msgstr "Anmodning om underskrift"

#. module: sign_itsme
#: model:ir.model,name:sign_itsme.model_sign_request_item
msgid "Signature Request Item"
msgstr "Underskrifts anmodning artikel"

#. module: sign_itsme
#. odoo-javascript
#: code:addons/sign_itsme/static/src/components/document_signable.js:0
msgid ""
"The itsme® identification data could not be forwarded to Odoo, the signature"
" could not be saved."
msgstr ""

#. module: sign_itsme
#: model:ir.model.fields.selection,name:sign_itsme.selection__sign_item_role__auth_method__itsme
msgid "Via itsme®"
msgstr ""

#. module: sign_itsme
#. odoo-javascript
#: code:addons/sign_itsme/static/src/components/document_signable.js:0
msgid ""
"You have rejected the identification request or took too long to process it."
" You can try again to finalize your signature."
msgstr ""

#. module: sign_itsme
#. odoo-python
#: code:addons/sign_itsme/controllers/main.py:0
msgid "itsme® IAP service could not be found."
msgstr ""

#. module: sign_itsme
#: model:ir.model.fields,field_description:sign_itsme.field_sign_request_item__itsme_signer_birthdate
msgid "itsme® Signer's Birthdate"
msgstr ""

#. module: sign_itsme
#: model:ir.model.fields,field_description:sign_itsme.field_sign_request_item__itsme_signer_name
msgid "itsme® Signer's Name"
msgstr ""

#. module: sign_itsme
#: model:ir.model.fields,field_description:sign_itsme.field_sign_request_item__itsme_validation_hash
msgid "itsme® Validation Token"
msgstr ""
