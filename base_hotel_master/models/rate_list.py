from odoo import fields, models, api, _
from odoo.exceptions import ValidationError
from datetime import datetime, timedelta


class RateList(models.Model):
    _name = 'rate.list'
    _description = 'rate list'
    _rec_name = 'ref'
    _order = 'id desc'
    _inherit = ['mail.thread', 'mail.activity.mixin']

    ref = fields.Char(string='Reference', readonly=False, store=True, copy=False, default="New")
    from_date = fields.Date('From Date', tracking=True, required=True)
    to_date = fields.Date('To Date', tracking=True, required=True)
    date = fields.Datetime('Date', default=fields.Datetime.now())
    price = fields.Float('Price', required=True, tracking=True)
    rate_type_id = fields.Many2one('rate.type.master', string='Rate Type', required=True)

    state = fields.Selection([
        ('draft', 'Draft'),
        ('confirm', 'Confirm'),
        ('cancel', 'Cancel')
    ],
        string='State',
        default='draft'
    )

    company_id = fields.Many2one(
        'res.company',
        string='Company',
        default=lambda self: self.env.company,
        tracking=True,
        required=True
    )

    @api.model_create_multi
    def create(self, vals_list):
        """ Generate sequence for rate list ."""
        for vals in vals_list:
            if vals.get("ref", _("New")) == _("New"):
                vals["ref"] = self.env["ir.sequence"].next_by_code("rate.list") or _("New")
        return super(RateList, self).create(vals_list)

    @api.constrains('from_date', 'to_date')
    def check_from_date(self):
        today = fields.Date.today()
        for rec in self:
            if rec.from_date < today:
                raise ValidationError(_(
                    "The 'From Date' (%s) cannot be earlier than today's date (%s)."
                    % (rec.from_date, today)
                ))
            if rec.from_date > rec.to_date:
                raise ValidationError(_(
                    "The 'From Date' (%s) cannot be later than the 'To Date' (%s)."
                    % (rec.from_date, rec.to_date)
                ))

    @api.constrains('price')
    def check_rate_price(self):
        for rec in self:
            if rec.price < 1:
                raise ValidationError(_(f"Price can not be negative or zero: {rec.price}"))

    def update_rack_rate(self):
        """ update rack rate """
        overlapping_rates = self.search([
            ('rate_type', '=', 'rack_rate'),
            ('id', '!=', self.id),
            ('from_date', '<=', self.from_date),
            ('to_date', '>=', self.from_date),
            ('state', '=', 'confirm')
        ])
        if overlapping_rates:
            if overlapping_rates:
                raise ValidationError(_(
                    "A rack rate already exists in the range from %s to %s.\n"
                    "Please check reference: %s."
                ) % (overlapping_rates.from_date, overlapping_rates.to_date, overlapping_rates.ref))

    def update_bar_rate(self):
        # Fetch the most recent confirmed bar rate of the same rate type, excluding the current record
        last_bar_rate = self.search([
            ('rate_type', '=', self.rate_type),
            ('id', '!=', self.id),
            ('state', '=', 'confirm')
        ], limit=1, order='to_date desc')

        if last_bar_rate:
            last_date = self.from_date - timedelta(days=1)

            if self.to_date > last_bar_rate.from_date:
                # Check for overlapping or invalid date ranges
                if self.from_date <= last_bar_rate.to_date:
                    if last_date >= last_bar_rate.from_date:
                        # Update the `to_date` of the previous rate
                        last_bar_rate.to_date = last_date
                    else:
                        raise ValidationError(_(
                            "The 'From Date' of the new rate overlaps with the existing rate range (%s to %s). "
                            "Please cancel or adjust the existing rate before proceeding.\n"
                            "Please check reference (%s)."
                        ) % (last_bar_rate.from_date, last_bar_rate.to_date, last_bar_rate.ref))
            else:
                pass

    def action_confirm_rate(self):
        """ confirm rate list """
        if self.rate_type_id:
            if self.rate_type_id.name == 'rack_rate':
                self.update_rack_rate()
            elif self.rate_type_id.name == 'bar_rate':
                self.update_bar_rate()
            self.state = 'confirm'

    def action_cancel_rate(self):
        self.write({'state': 'cancel'})
