# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* documents_account_peppol
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-01-27 13:55+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON><PERSON>, 2025\n"
"Language-Team: Italian (https://app.transifex.com/odoo/teams/41243/it/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: it\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: documents_account_peppol
#: model:ir.model,name:documents_account_peppol.model_account_edi_proxy_client_user
msgid "Account EDI proxy user"
msgstr "Account EDI proxy user"

#. module: documents_account_peppol
#: model:ir.model,name:documents_account_peppol.model_res_company
msgid "Companies"
msgstr "Aziende"

#. module: documents_account_peppol
#: model:ir.model,name:documents_account_peppol.model_res_config_settings
msgid "Config Settings"
msgstr "Impostazioni di configurazione"

#. module: documents_account_peppol
#: model:ir.model.fields,field_description:documents_account_peppol.field_res_company__documents_account_peppol_tag_ids
#: model:ir.model.fields,field_description:documents_account_peppol.field_res_config_settings__documents_account_peppol_tag_ids
#: model_terms:ir.ui.view,arch_db:documents_account_peppol.res_config_settings_view_form_inherit_documents_account_peppol
msgid "Document Tags"
msgstr "Tag documento"

#. module: documents_account_peppol
#: model:ir.model.fields,field_description:documents_account_peppol.field_res_company__documents_account_peppol_folder_id
#: model:ir.model.fields,field_description:documents_account_peppol.field_res_config_settings__documents_account_peppol_folder_id
#: model_terms:ir.ui.view,arch_db:documents_account_peppol.res_config_settings_view_form_inherit_documents_account_peppol
msgid "Document Workspace"
msgstr "Spazio di lavoro documento"

#. module: documents_account_peppol
#. odoo-python
#: code:addons/documents_account_peppol/models/account_edi_proxy_user.py:0
msgid "Peppol document (UUID: %(uuid)s) has been received successfully."
msgstr "Il documento Peppol (UUID: %(uuid)s) è stato ricevuto correttamente."
