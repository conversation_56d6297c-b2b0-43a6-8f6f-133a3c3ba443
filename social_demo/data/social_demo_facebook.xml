<?xml version='1.0' encoding='utf-8'?>
<odoo>
<data noupdate="1">
    <record id="social_stream_facebook_page" model="social.stream">
        <field name="name">Facebook Posts: My Company</field>
        <field name="create_uid" ref="base.user_admin"/>
        <field name="stream_type_id" ref="social_facebook.stream_type_page_posts" />
        <field name="media_id" ref="social_facebook.social_media_facebook" />
        <field name="account_id" ref="social_account_facebook" />
    </record>

    <record id="social_post_facebook" model="social.post">
        <field name="message">Our company wishes a happy new year to everyone on Facebook!</field>
        <field name="create_uid" ref="base.user_admin"/>
        <field name="published_date" eval="time.strftime('%Y-01-01 00:01:00')"/>
        <field name="state">posted</field>
        <field name="account_ids" eval="[(6, 0, [ref('social_demo.social_account_facebook')])]" />
        <field name="post_method">scheduled</field>
        <field name="scheduled_date" eval="time.strftime('%Y-01-01 00:01:00')"/>
    </record>

    <record id="social_live_post_facebook_1" model="social.live.post">
        <field name="state">posted</field>
        <field name="facebook_post_id">pfbid02nCXzwGHSYqPFv4PPwc91nwCYnCLBjaXEXRPrmE51vABKEr21Fk3UkWysr21v5gq5l</field>
        <field name="post_id" ref="social_post_global" />
        <field name="account_id" ref="social_account_facebook" />
        <field name="engagement">7754</field>
    </record>

    <record id="social_live_post_facebook_2" model="social.live.post">
        <field name="state">posted</field>
        <field name="facebook_post_id">pfbid02nCXzwGHSYqPFv4PPwc91nwCYnCLBjaXEXRPrmE51vABKEr21Fk3UkWysr21v5gq5l</field>
        <field name="post_id" ref="social_post_facebook" />
        <field name="account_id" ref="social_account_facebook" />
        <field name="engagement">9954</field>
    </record>

    <record id="social_live_post_facebook_3" model="social.live.post">
        <field name="state">posted</field>
        <field name="facebook_post_id">pfbid02nCXzwGHSYqPFv4PPwc91nwCYnCLBjaXEXRPrmE51vABKEr21Fk3UkWysr21v5gq5l</field>
        <field name="post_id" ref="social_post_global_2" />
        <field name="account_id" ref="social_account_facebook" />
        <field name="engagement">9431</field>
    </record>

    <record id="social_live_post_facebook_4" model="social.live.post">
        <field name="state">posted</field>
        <field name="facebook_post_id">pfbid02nCXzwGHSYqPFv4PPwc91nwCYnCLBjaXEXRPrmE51vABKEr21Fk3UkWysr21v5gq5l</field>
        <field name="post_id" ref="social_post_global_3" />
        <field name="account_id" ref="social_account_facebook" />
        <field name="engagement">7489</field>
    </record>

    <record id="social_stream_post_facebook_1" model="social.stream.post">
        <field name="stream_id" ref="social_stream_facebook_page" />
        <field name="facebook_post_id">1</field>
        <field name="facebook_reach">34618</field>
        <field name="facebook_comments_count">94</field>
        <field name="facebook_shares_count">98</field>
        <field name="facebook_likes_count">7524</field>
        <field name="author_name">Odoo</field>
        <field name="facebook_reactions_count">{"LIKE": 6337, "LOVE": 501, "WOW": 686}</field>
        <field name="facebook_user_likes" eval="True"/>
        <field name="twitter_profile_image_url" eval="'/web/image/res.partner/%s/image_128' % ref('social_demo.res_partner_1')" />
        <field name="message">Our new product has been released 🎉 Check it out!</field>
        <field name="published_date" eval="time.strftime('%Y-%m-%d 08:00:00')"/>
    </record>

    <record id="social_stream_post_facebook_2" model="social.stream.post">
        <field name="stream_id" ref="social_stream_facebook_page" />
        <field name="facebook_post_id">2</field>
        <field name="facebook_reach">47618</field>
        <field name="facebook_comments_count">354</field>
        <field name="facebook_shares_count">5</field>
        <field name="facebook_likes_count">9622</field>
        <field name="author_name">Odoo</field>
        <field name="facebook_reactions_count">{"LIKE": 6337, "LOVE": 501, "WOW": 686, "CARE": 2098}</field>
        <field name="twitter_profile_image_url" eval="'/web/image/res.partner/%s/image_128' % ref('social_demo.res_partner_1')" />
        <field name="message">Our company wishes a happy new year to everyone on Facebook!</field>
        <field name="published_date" eval="time.strftime('%Y-01-01 00:01:00')"/>
    </record>

    <record id="social_stream_post_facebook_3" model="social.stream.post">
        <field name="stream_id" ref="social_stream_facebook_page" />
        <field name="facebook_post_id">3</field>
        <field name="facebook_reach">87618</field>
        <field name="facebook_comments_count">75</field>
        <field name="facebook_shares_count">88</field>
        <field name="facebook_likes_count">9911</field>
        <field name="author_name">Odoo</field>
        <field name="facebook_reactions_count">{"LIKE": 6340, "LOVE": 501, "WOW": 686, "CARE": 2098}</field>
        <field name="twitter_profile_image_url" eval="'/web/image/res.partner/%s/image_128' % ref('social_demo.res_partner_1')" />
        <field name="message">Get 20% out of your purchases on https://mycompany.com/shop
Better run to our website while it lasts 🏃</field>
        <field name="published_date" eval="time.strftime('%Y-%m-%d 09:00:00')"/>
    </record>

    <record id="social_stream_post_facebook_4" model="social.stream.post">
        <field name="stream_id" ref="social_stream_facebook_page" />
        <field name="facebook_post_id">4</field>
        <field name="facebook_reach">85888</field>
        <field name="facebook_comments_count">75</field>
        <field name="facebook_shares_count">48</field>
        <field name="facebook_likes_count">9711</field>
        <field name="author_name">Odoo</field>
        <field name="facebook_reactions_count">{"LIKE": 6340, "CARE": 3371}</field>
        <field name="facebook_user_likes" eval="True"/>
        <field name="twitter_profile_image_url" eval="'/web/image/res.users/%s/image_128' % ref('base.user_admin')" />
        <field name="message">To celebrate the new year, we have a gift for you: Learn how to build your own chair!

https://www.youtube.com/watch?v=kmt-oVAB6hU</field>
        <field name="published_date" eval="time.strftime('%Y-01-10 09:00:00')"/>
    </record>

    <record id="social_stream_post_facebook_image_1" model="social.stream.post.image">
        <field name="image_url" eval="'/web/image/product.product/%s/image_512' % ref('social_demo.product_product_4')"></field>
        <field name="stream_post_id" ref="social_stream_post_facebook_3" />
    </record>

    <record id="social_stream_post_facebook_image_2" model="social.stream.post.image">
        <field name="image_url" eval="'/web/image/product.product/%s/image_512' % ref('social_demo.product_product_4b')"></field>
        <field name="stream_post_id" ref="social_stream_post_facebook_3" />
    </record>

    <record id="social_stream_post_facebook_image_3" model="social.stream.post.image">
        <field name="image_url" eval="'/web/image/product.product/%s/image_512' % ref('social_demo.product_product_4c')"></field>
        <field name="stream_post_id" ref="social_stream_post_facebook_3" />
    </record>

    <record id="social_stream_post_facebook_image_4" model="social.stream.post.image">
        <field name="image_url" eval="'/web/image/product.product/%s/image_512' % ref('social_demo.product_product_4d')"></field>
        <field name="stream_post_id" ref="social_stream_post_facebook_3" />
    </record>
</data>
</odoo>
