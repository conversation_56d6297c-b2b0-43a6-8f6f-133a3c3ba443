# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_knowledge
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-25 09:16+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON><PERSON>, 2025\n"
"Language-Team: Spanish (https://app.transifex.com/odoo/teams/41243/es/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: website_knowledge
#: model_terms:ir.ui.view,arch_db:website_knowledge.article_view_public
msgid "<i class=\"fa fa-lg fa-bars\" title=\"Toggle aside menu\"/>"
msgstr "<i class=\"fa fa-lg fa-bars\" title=\"Alternar menú lateral\"/>"

#. module: website_knowledge
#. odoo-javascript
#: code:addons/website_knowledge/static/src/backend/components/permission_panel/permission_panel.xml:0
msgid "Article not Published"
msgstr "Artículo no publicado"

#. module: website_knowledge
#. odoo-javascript
#: code:addons/website_knowledge/static/src/frontend/knowledge_public_view/knowledge_public_view.xml:0
#: model_terms:ir.ui.view,arch_db:website_knowledge.article_view_public
msgid "Article not found"
msgstr "No se encontró el artículo"

#. module: website_knowledge
#. odoo-javascript
#: code:addons/website_knowledge/static/src/backend/components/permission_panel/permission_panel.xml:0
msgid "Article shared to web"
msgstr "Artículo compartido a la web"

#. module: website_knowledge
#: model:ir.model.fields,field_description:website_knowledge.field_knowledge_article__can_publish
msgid "Can Publish"
msgstr "Puede publicar"

#. module: website_knowledge
#: model:ir.model.fields,field_description:website_knowledge.field_knowledge_article__is_published
#: model_terms:ir.ui.view,arch_db:website_knowledge.knowledge_article_view_search
#: model_terms:ir.ui.view,arch_db:website_knowledge.knowledge_article_view_tree
msgid "Is Published"
msgstr "Está publicado"

#. module: website_knowledge
#. odoo-javascript
#: code:addons/website_knowledge/static/src/backend/components/permission_panel/permission_panel.xml:0
msgid "It and its published children can be read by anyone"
msgstr "Todo el mundo puede leer el artículo y sus hijos publicados"

#. module: website_knowledge
#: model:ir.model,name:website_knowledge.model_knowledge_article
msgid "Knowledge Article"
msgstr "Artículo de Información"

#. module: website_knowledge
#: model_terms:ir.ui.view,arch_db:website_knowledge.articles_template
msgid "Load more"
msgstr "Cargar mas"

#. module: website_knowledge
#. odoo-javascript
#: code:addons/website_knowledge/static/src/frontend/editor/embedded_components/view/view_placeholder.xml:0
msgid "Log in"
msgstr "Iniciar sesión"

#. module: website_knowledge
#: model_terms:ir.ui.view,arch_db:website_knowledge.public_sidebar
msgid "No article found"
msgstr "No se encontró ningún artículo"

#. module: website_knowledge
#. odoo-javascript
#: code:addons/website_knowledge/static/src/backend/components/permission_panel/permission_panel.xml:0
msgid "Only specific people can access"
msgstr "Solo personas específicas tienen acceso"

#. module: website_knowledge
#: model:ir.actions.server,name:website_knowledge.knowledge_action_publish_articles
msgid "Publish Articles"
msgstr "Publicar artículos"

#. module: website_knowledge
#. odoo-javascript
#: code:addons/website_knowledge/static/src/backend/components/permission_panel/permission_panel.xml:0
msgid "Publish this Article and its children on the web"
msgstr "Publicar este artículo y sus hijos en el sitio web"

#. module: website_knowledge
#. odoo-javascript
#: code:addons/website_knowledge/static/src/frontend/knowledge_public_view/knowledge_public_view.xml:0
msgid "Search an article..."
msgstr "Buscar un artículo..."

#. module: website_knowledge
#. odoo-javascript
#: code:addons/website_knowledge/static/src/backend/components/permission_panel/permission_panel.xml:0
msgid "Share to web"
msgstr "Compartir en la web"

#. module: website_knowledge
#. odoo-javascript
#: code:addons/website_knowledge/static/src/frontend/knowledge_public_view/knowledge_public_view.xml:0
#: model_terms:ir.ui.view,arch_db:website_knowledge.article_view_public
msgid "Sign in"
msgstr "Iniciar sesión"

#. module: website_knowledge
#: model:ir.model.fields,field_description:website_knowledge.field_knowledge_article__summary
msgid "Summary"
msgstr "Resumen"

#. module: website_knowledge
#. odoo-javascript
#: code:addons/website_knowledge/static/src/frontend/knowledge_public_view/knowledge_public_view.xml:0
#: model_terms:ir.ui.view,arch_db:website_knowledge.article_view_public
msgid ""
"The article you are trying the read has either been removed or you do not "
"have access to it."
msgstr ""
"El artículo que está intentando leer ha sido eliminado o no tiene acceso."

#. module: website_knowledge
#: model:ir.model.fields,help:website_knowledge.field_knowledge_article__website_url
msgid "The full URL to access the document through the website."
msgstr "La URL completa para acceder al documento a través del sitio web."

#. module: website_knowledge
#. odoo-python
#: code:addons/website_knowledge/controllers/main.py:0
msgid ""
"This Article cannot be unfolded. Either you lost access to it or it has been"
" deleted."
msgstr ""
"Este artículo no se puede mostrar, ha perdido sus permisos de acceso o se ha"
" eliminado."

#. module: website_knowledge
#. odoo-javascript
#: code:addons/website_knowledge/static/src/frontend/editor/embedded_components/view/view_placeholder.xml:0
msgid "This view is only available for internal users"
msgstr "Esta vista solo está disponible para usuarios internos"

#. module: website_knowledge
#. odoo-javascript
#: code:addons/website_knowledge/static/src/frontend/knowledge_public_view/knowledge_public_view.xml:0
msgid "Toggle aside menu"
msgstr "Alternar menú lateral"

#. module: website_knowledge
#: model:ir.actions.server,name:website_knowledge.knowledge_action_unpublish_articles
msgid "Unpublish Articles"
msgstr "Anular publicación de artículos"

#. module: website_knowledge
#. odoo-javascript
#: code:addons/website_knowledge/static/src/frontend/knowledge_public_view/knowledge_public_view.xml:0
#: model_terms:ir.ui.view,arch_db:website_knowledge.article_view_public
msgid "Untitled"
msgstr "Sin título"

#. module: website_knowledge
#: model:ir.model.fields,field_description:website_knowledge.field_knowledge_article__website_published
msgid "Visible on current website"
msgstr "Visible en el sitio web actual"

#. module: website_knowledge
#: model:ir.model,name:website_knowledge.model_website
msgid "Website"
msgstr "Sitio web"

#. module: website_knowledge
#: model:ir.model.fields,field_description:website_knowledge.field_knowledge_article__website_url
msgid "Website URL"
msgstr "URL del sitio web"

#. module: website_knowledge
#. odoo-javascript
#: code:addons/website_knowledge/static/src/frontend/knowledge_public_view/knowledge_public_view.xml:0
msgid "loader"
msgstr "loader"
