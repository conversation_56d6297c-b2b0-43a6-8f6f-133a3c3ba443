from odoo import api, fields, models


class StockQuant(models.Model):
    _inherit = 'stock.quant'

    branch_id = fields.Many2one(
        comodel_name='multi.branch',
        string='BU',
        domain=lambda self: [('company_id', '=', self.env.company.id)]
    )
    inventory_value = fields.Float(string='Inventory Value')
    standard_price = fields.Float(string='Cost', digits='Product Price', compute='_compute_standard_price', store=True)

    @api.depends('product_id')
    def _compute_standard_price(self):
        for rec in self:
            if rec.product_id and rec.product_id.standard_price:
                rec.standard_price = rec.product_id.standard_price
            else:
                rec.standard_price = 0

    @api.model
    def _is_inventory_mode(self):
        res = super()._is_inventory_mode()
        return False

    @api.model
    def _get_forbidden_fields_write(self):
        """ add fields to forbidden fields for create inventory quant """
        res = super()._get_forbidden_fields_write()
        if res:
            res.extend(['branch_id', 'inventory_value', 'standard_price'])
        return res

    @api.model
    def _get_inventory_fields_create(self):
        """ add fields to create inventory quant """
        res = super()._get_inventory_fields_create()
        if res:
            res.extend(['branch_id', 'inventory_value', 'standard_price'])
        return res
