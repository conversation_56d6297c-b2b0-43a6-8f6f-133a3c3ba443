# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_renting
# 
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2025
# <PERSON>il <PERSON>, 2025
# <PERSON><PERSON>, 2025
# <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, 2025
# <PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-23 18:44+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON>, 2025\n"
"Language-Team: Czech (https://app.transifex.com/odoo/teams/41243/cs/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: cs\n"
"Plural-Forms: nplurals=4; plural=(n == 1 && n % 1 == 0) ? 0 : (n >= 2 && n <= 4 && n % 1 == 0) ? 1: (n % 1 != 0 ) ? 2 : 3;\n"

#. module: sale_renting
#. odoo-python
#: code:addons/sale_renting/models/sale_order_line.py:0
msgid ""
"\n"
"%(from_date)s to %(to_date)s"
msgstr ""
"\n"
"%(from_date)s - %(to_date)s"

#. module: sale_renting
#. odoo-python
#: code:addons/sale_renting/models/product_template.py:0
msgid "%(amount)s (fixed)"
msgstr "%(amount)s (opraveno)"

#. module: sale_renting
#. odoo-python
#: code:addons/sale_renting/models/product_pricing.py:0
msgid "%(amount)s / %(duration)s"
msgstr "%(amount)s / %(duration)s"

#. module: sale_renting
#. odoo-python
#: code:addons/sale_renting/models/product_template.py:0
#: code:addons/sale_renting/models/sale_order_recurrence.py:0
msgid "%(duration)s %(unit)s"
msgstr "%(duration)s %(unit)s"

#. module: sale_renting
#. odoo-python
#: code:addons/sale_renting/models/product_product.py:0
#: code:addons/sale_renting/models/product_template.py:0
msgid "%s (Rental)"
msgstr "%s (Pronájem)"

#. module: sale_renting
#: model:ir.actions.report,print_report_name:sale_renting.action_report_rental_saleorder
msgid ""
"(object.rental_status not in ('draft', 'sent') and 'Delivery or Return "
"Receipt - %s' %(object.name)) or 'Order - %s' % (object.name)"
msgstr ""
"(object.rental_status not in ('návrh', 'odesláno') and 'Vyzvednutí a vrácení"
" příjmu - %s' %(object.name)) or 'Objednávka - %s' % (object.name)"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.report_rental_order_document
msgid "123 Main St"
msgstr ""

#. module: sale_renting
#: model:sale.temporal.recurrence,name:sale_renting.recurrence_2_weeks
msgid "2 Weeks"
msgstr ""

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.report_rental_order_document
msgid "2023-08-01"
msgstr "2023-08-01"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.report_rental_order_document
msgid "2023-08-10"
msgstr "10. 8. 2023"

#. module: sale_renting
#: model:sale.temporal.recurrence,name:sale_renting.recurrence_3_hours
msgid "3 Hours"
msgstr ""

#. module: sale_renting
#: model:sale.temporal.recurrence,name:sale_renting.recurrence_3_year
msgid "3 years"
msgstr ""

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.report_rental_order_document
msgid "456 Other St"
msgstr ""

#. module: sale_renting
#: model:sale.temporal.recurrence,name:sale_renting.recurrence5_year
msgid "5 Years"
msgstr ""

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_view_kanban
msgid "<i class=\"fa fa-warning\"/> Late"
msgstr ""

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.product_product_form_view_rental_gantt
#: model_terms:ir.ui.view,arch_db:sale_renting.product_template_form_view_rental_gantt
msgid "<span class=\"o_stat_text\">in Rental</span>"
msgstr "<span class=\"o_stat_text\">v pronájmu</span>"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_form_view
msgid ""
"<span invisible=\"duration_days != 1\"> day </span>\n"
"                    <span invisible=\"duration_days in [0,1]\"> days </span>\n"
"                    <span invisible=\"duration_days == 0 or remaining_hours == 0\">and </span>"
msgstr ""

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_form_view
msgid ""
"<span invisible=\"remaining_hours != 1\"> hour </span>\n"
"                    <span invisible=\"remaining_hours in [0,1]\"> hours </span>"
msgstr ""

#. module: sale_renting
#: model_terms:web_tour.tour,rainbow_man_message:sale_renting.rental_tour
msgid "<span><b>Good job!</b> You went through all steps of this tour.</span>"
msgstr ""
"<span><b>Dobrá práce!</b> Prošli jste všechny kroky této prohlídky.</span>"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.report_rental_order_document
msgid "<strong class=\"d-block mt-3\">Shipping Address:</strong>"
msgstr ""

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_gantt
msgid "<strong>Order # — </strong>"
msgstr "<strong>Objednávka # — </strong>"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_gantt
msgid "<strong>Pickup  — </strong>"
msgstr "<strong>Vyzvednutí  — </strong>"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_gantt
msgid "<strong>Return — </strong>"
msgstr "<strong>Vrácení — </strong>"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.report_rental_order_document
msgid "<strong>Salesperson:</strong>"
msgstr "<strong>Obchodník:</strong>"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_gantt
msgid "<strong>Status — </strong>"
msgstr "<strong>Stav — </strong>"

#. module: sale_renting
#. odoo-python
#: code:addons/sale_renting/models/product_template.py:0
msgid "A rental combo product can only contain rental products."
msgstr ""

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_temporal_recurrence__active
msgid "Active"
msgstr "Aktivní"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.product_template_form_view_rental
msgid "Add a price"
msgstr "Přidat cenu"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.res_config_settings_view_form
msgid "Additional costs for late returns"
msgstr "Dodatečné náklady za pozdní vrácení"

#. module: sale_renting
#: model:ir.model.fields,help:sale_renting.field_product_product__rent_ok
#: model:ir.model.fields,help:sale_renting.field_product_template__rent_ok
#: model:ir.model.fields,help:sale_renting.field_sale_order_line__is_product_rentable
msgid "Allow renting of this product."
msgstr "Povolit pronájem tohoto produktu."

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.res_config_settings_view_form
msgid "Apply after"
msgstr "Použít po"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.res_config_settings_view_form
msgid "Ask customer to sign documents on the spot."
msgstr "Požádejte zákazníka o podpis dokumentů na místě."

#. module: sale_renting
#. odoo-javascript
#: code:addons/sale_renting/static/src/js/tours/rental.js:0
msgid "At first, let's create some products to rent."
msgstr "Nejprve vytvoříme několik produktů k pronájmu."

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_form_view
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_form
msgid "Booked"
msgstr "Rezervováno"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_wizard_view_form
msgid "Cancel"
msgstr "Zrušit"

#. module: sale_renting
#: model:ir.model.fields.selection,name:sale_renting.selection__sale_order__rental_status__cancel
#: model:ir.model.fields.selection,name:sale_renting.selection__sale_rental_report__state__cancel
#: model:ir.model.fields.selection,name:sale_renting.selection__sale_rental_schedule__rental_status__cancel
#: model:ir.model.fields.selection,name:sale_renting.selection__sale_rental_schedule__state__cancel
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_gantt
msgid "Cancelled"
msgstr "Zrušeno"

#. module: sale_renting
#. odoo-javascript
#: code:addons/sale_renting/static/src/js/tours/rental.js:0
msgid "Click here to create a new quotation."
msgstr "Kliknutím sem vytvoříte novou nabídku."

#. module: sale_renting
#. odoo-javascript
#: code:addons/sale_renting/static/src/js/tours/rental.js:0
msgid "Click here to register the pickup."
msgstr "Pro registraci vyzvednutí klikněte zde."

#. module: sale_renting
#. odoo-javascript
#: code:addons/sale_renting/static/src/js/tours/rental.js:0
msgid "Click here to set up your first rental product."
msgstr "Kliknutím sem si můžete nastavit svůj první produkt k pronájmu."

#. module: sale_renting
#. odoo-javascript
#: code:addons/sale_renting/static/src/js/tours/rental.js:0
msgid "Click here to start filling the quotation."
msgstr "Klepnutím sem začnete vyplňovat nabídku."

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__color
msgid "Color"
msgstr "Barva"

#. module: sale_renting
#: model:ir.model,name:sale_renting.model_res_company
msgid "Companies"
msgstr "Společnosti"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_product_pricing__company_id
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_report__company_id
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__company_id
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_report_search_view
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_search
msgid "Company"
msgstr "Společnost"

#. module: sale_renting
#: model:ir.model,name:sale_renting.model_res_config_settings
msgid "Config Settings"
msgstr "Konfigurační nastavení"

#. module: sale_renting
#: model:ir.ui.menu,name:sale_renting.menu_rental_config
msgid "Configuration"
msgstr "Konfigurace"

#. module: sale_renting
#. odoo-javascript
#: code:addons/sale_renting/static/src/js/tours/rental.js:0
msgid "Confirm the order when the customer agrees with the terms."
msgstr "Potvrďte objednávku, když zákazník souhlasí s podmínkami."

#. module: sale_renting
#. odoo-javascript
#: code:addons/sale_renting/static/src/js/tours/rental.js:0
msgid "Confirm the returned quantities and hit Validate."
msgstr "Potvrďte vrácené množství a stiskněte tlačítko ověřit."

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_report_search_view
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_search
msgid "Confirmed Orders"
msgstr "Potvrzené objednávky"

#. module: sale_renting
#: model:ir.actions.act_window,name:sale_renting.action_create_rental_order
msgid "Create Rental Orders"
msgstr "Vytvořte objednávky pronájmu"

#. module: sale_renting
#: model_terms:ir.actions.act_window,help:sale_renting.sale_temporal_recurrence_action
msgid "Create a new period"
msgstr ""

#. module: sale_renting
#: model_terms:ir.actions.act_window,help:sale_renting.rental_order_action
msgid "Create a new quotation, the first step of a new rental!"
msgstr "Vytvořte novou nabídku, první krok nového pronájmu!"

#. module: sale_renting
#: model_terms:ir.actions.act_window,help:sale_renting.product_pricing_action
msgid "Create a new recurrence"
msgstr "Vytvořit nové opakování"

#. module: sale_renting
#: model_terms:ir.actions.act_window,help:sale_renting.action_rental_report
msgid "Create a new rental order"
msgstr "Vytvořit novou objednávku pronájmu"

#. module: sale_renting
#: model_terms:ir.actions.act_window,help:sale_renting.rental_product_template_action
msgid "Create a new rental product!"
msgstr "Vytvořte nový produkt k pronájmu!"

#. module: sale_renting
#. odoo-javascript
#: code:addons/sale_renting/static/src/js/tours/rental.js:0
msgid "Create or select a customer here."
msgstr "Zde vytvořte nebo vyberte zákazníka."

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_order__is_rental_order
msgid "Created In App Rental"
msgstr "Vytvořeno v aplikaci pronájem"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_product_pricing__create_uid
#: model:ir.model.fields,field_description:sale_renting.field_rental_order_wizard__create_uid
#: model:ir.model.fields,field_description:sale_renting.field_rental_order_wizard_line__create_uid
#: model:ir.model.fields,field_description:sale_renting.field_sale_temporal_recurrence__create_uid
msgid "Created by"
msgstr "Vytvořeno uživatelem"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_product_pricing__create_date
#: model:ir.model.fields,field_description:sale_renting.field_rental_order_wizard__create_date
#: model:ir.model.fields,field_description:sale_renting.field_rental_order_wizard_line__create_date
#: model:ir.model.fields,field_description:sale_renting.field_sale_temporal_recurrence__create_date
msgid "Created on"
msgstr "Vytvořeno dne"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_product_pricing__currency_id
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_report__currency_id
msgid "Currency"
msgstr "Měna"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_product_product__product_pricing_ids
#: model:ir.model.fields,field_description:sale_renting.field_product_template__product_pricing_ids
msgid "Custom Pricings"
msgstr "Vlastní ceny"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_report__partner_id
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__partner_id
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_view_search
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_report_search_view
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_search
msgid "Customer"
msgstr "Zákazník"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__country_id
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_search
msgid "Customer Country"
msgstr "Země zákazníka"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__commercial_partner_id
msgid "Customer Entity"
msgstr "Zákaznická entita"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__card_name
msgid "Customer Name"
msgstr "Jméno zákazníka"

#. module: sale_renting
#: model:ir.ui.menu,name:sale_renting.menu_orders_customers
msgid "Customers"
msgstr "Zákazníci"

#. module: sale_renting
#: model:sale.temporal.recurrence,name:sale_renting.recurrence_daily
msgid "Daily"
msgstr "Denně"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_report__price
msgid "Daily Amount"
msgstr "Denní množství"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_report__quantity
msgid "Daily Ordered Qty"
msgstr "Denně objednané množství"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_report__qty_delivered
msgid "Daily Picked-Up Qty"
msgstr "Denně vyzvednuté množství"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_report__qty_returned
msgid "Daily Returned Qty"
msgstr "Denní vrácené množství"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_report__date
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_report_search_view
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_search
msgid "Date"
msgstr "Datum"

#. module: sale_renting
#. odoo-python
#: code:addons/sale_renting/models/sale_order_recurrence.py:0
msgid "Day"
msgstr "Den"

#. module: sale_renting
#: model:ir.model.fields.selection,name:sale_renting.selection__sale_temporal_recurrence__unit__day
msgid "Days"
msgstr "Dny"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.res_config_settings_view_form
msgid "Default Delay Costs"
msgstr ""

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_res_config_settings__extra_product
msgid "Delay Product"
msgstr "Zpoždění produktu"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_product_pricing__description
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__description
#: model_terms:ir.ui.view,arch_db:sale_renting.report_rental_order_document
msgid "Description"
msgstr "Popis"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_res_config_settings__module_sale_renting_sign
msgid "Digital Documents"
msgstr "Digitální dokumenty"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_product_pricing__display_name
#: model:ir.model.fields,field_description:sale_renting.field_rental_order_wizard__display_name
#: model:ir.model.fields,field_description:sale_renting.field_rental_order_wizard_line__display_name
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_report__display_name
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__display_name
#: model:ir.model.fields,field_description:sale_renting.field_sale_temporal_recurrence__display_name
msgid "Display Name"
msgstr "Zobrazovací název"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_temporal_recurrence__duration
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_form_view
msgid "Duration"
msgstr "Trvání"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_product_pricing__name
#: model:ir.model.fields,field_description:sale_renting.field_sale_temporal_recurrence__duration_display
msgid "Duration Display"
msgstr "Trvání zobrazení"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_order__duration_days
msgid "Duration in days"
msgstr "Trvání ve dnech"

#. module: sale_renting
#. odoo-javascript
#: code:addons/sale_renting/static/src/js/tours/rental.js:0
msgid "Enter the product name."
msgstr "Zadejte název produktu."

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.report_rental_order_document
msgid "Expected Return"
msgstr "Očekávané vrácení"

#. module: sale_renting
#. odoo-python
#: code:addons/sale_renting/models/sale_order_line.py:0
msgid "Expected: %(date)s"
msgstr ""

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_product_product__extra_daily
#: model:ir.model.fields,field_description:sale_renting.field_product_template__extra_daily
msgid "Extra Day"
msgstr "Den navíc"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_product_product__extra_hourly
#: model:ir.model.fields,field_description:sale_renting.field_product_template__extra_hourly
msgid "Extra Hour"
msgstr "Hodina navíc"

#. module: sale_renting
#: model:ir.model.fields,help:sale_renting.field_product_product__extra_daily
#: model:ir.model.fields,help:sale_renting.field_product_template__extra_daily
msgid "Fine by day overdue"
msgstr "Pokuta za den zpoždění"

#. module: sale_renting
#: model:ir.model.fields,help:sale_renting.field_product_product__extra_hourly
#: model:ir.model.fields,help:sale_renting.field_product_template__extra_hourly
msgid "Fine by hour overdue"
msgstr "Pokuta za hodinu zpoždění"

#. module: sale_renting
#: model:ir.model.fields,help:sale_renting.field_product_product__display_price
#: model:ir.model.fields,help:sale_renting.field_product_template__display_price
msgid "First rental pricing of the product"
msgstr "Cena za první pronájem produktu"

#. module: sale_renting
#. odoo-javascript
#: code:addons/sale_renting/static/src/js/tours/rental.js:0
msgid "Go to the orders menu."
msgstr "Přejděte do nabídky objednávek."

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_view_search
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_report_search_view
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_search
msgid "Group By"
msgstr "Seskupit podle"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_order__show_update_duration
msgid "Has Duration Changed"
msgstr ""

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_order__has_pickable_lines
msgid "Has Pickable Lines"
msgstr "Má řady k vyzvednutí"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_order__has_rented_products
msgid "Has Rented Products"
msgstr ""

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_order__has_returnable_lines
msgid "Has Returnable Lines"
msgstr "Má řady k vrácení"

#. module: sale_renting
#. odoo-python
#: code:addons/sale_renting/models/sale_order_recurrence.py:0
msgid "Hour"
msgstr "Hodina"

#. module: sale_renting
#: model:sale.temporal.recurrence,name:sale_renting.recurrence_hourly
msgid "Hourly"
msgstr ""

#. module: sale_renting
#: model:ir.model.fields.selection,name:sale_renting.selection__sale_temporal_recurrence__unit__hour
msgid "Hours"
msgstr "Hodiny"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_product_pricing__id
#: model:ir.model.fields,field_description:sale_renting.field_rental_order_wizard__id
#: model:ir.model.fields,field_description:sale_renting.field_rental_order_wizard_line__id
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_report__id
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__id
#: model:ir.model.fields,field_description:sale_renting.field_sale_temporal_recurrence__id
msgid "ID"
msgstr "ID"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.report_rental_order_document
msgid "Invoicing Address:"
msgstr "Fakturační adresa:"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.report_rental_order_document
msgid "Invoicing and Shipping Address:"
msgstr "Fakturační a dodací adresa:"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__late
msgid "Is Late"
msgstr "Má zpoždění"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_order_line__is_rental
msgid "Is Rental"
msgstr "Je pronájem"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_rental_order_wizard__is_late
#: model:ir.model.fields,field_description:sale_renting.field_sale_order__is_late
msgid "Is overdue"
msgstr "Je splatné"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.report_rental_order_document
msgid "Jane Doe"
msgstr ""

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.report_rental_order_document
msgid "John Doe"
msgstr "Jan Testovací"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_product_pricing__write_uid
#: model:ir.model.fields,field_description:sale_renting.field_rental_order_wizard__write_uid
#: model:ir.model.fields,field_description:sale_renting.field_rental_order_wizard_line__write_uid
#: model:ir.model.fields,field_description:sale_renting.field_sale_temporal_recurrence__write_uid
msgid "Last Updated by"
msgstr "Naposledy upraveno uživatelem"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_product_pricing__write_date
#: model:ir.model.fields,field_description:sale_renting.field_rental_order_wizard__write_date
#: model:ir.model.fields,field_description:sale_renting.field_rental_order_wizard_line__write_date
#: model:ir.model.fields,field_description:sale_renting.field_sale_temporal_recurrence__write_date
msgid "Last Updated on"
msgstr "Naposledy upraveno dne"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_view_search
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_gantt
msgid "Late"
msgstr "Zpožděné"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_form_view
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_form
msgid "Late Pickup"
msgstr "Pozdní vyzvednutí"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_form_view
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_form
msgid "Late Return"
msgstr "Pozdní vrácení"

#. module: sale_renting
#. odoo-javascript
#: code:addons/sale_renting/static/src/js/tours/rental.js:0
msgid "Let's now create an order."
msgstr "Nyní vytvoříme objednávku."

#. module: sale_renting
#: model:product.template,name:sale_renting.rental_product_2_product_template
msgid "Meeting Room"
msgstr "Zasedací místnost"

#. module: sale_renting
#: model:ir.model.constraint,message:sale_renting.constraint_res_company_min_extra_hour
msgid "Minimal delay time before applying fines has to be positive."
msgstr "Minimální doba zpoždění před použitím pokut musí být kladná."

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_res_company__min_extra_hour
#: model:ir.model.fields,field_description:sale_renting.field_res_config_settings__min_extra_hour
msgid "Minimum delay time before applying fines."
msgstr "Minimální doba prodlevy před uplatněním pokut."

#. module: sale_renting
#: model:ir.model.fields,help:sale_renting.field_sale_temporal_recurrence__duration
msgid ""
"Minimum duration before this rule is applied. If set to 0, it represents a "
"fixedrental price."
msgstr ""

#. module: sale_renting
#. odoo-python
#: code:addons/sale_renting/models/sale_order_recurrence.py:0
msgid "Month"
msgstr "Měsíc"

#. module: sale_renting
#: model:sale.temporal.recurrence,name:sale_renting.recurrence_monthly
msgid "Monthly"
msgstr "Měsíčně"

#. module: sale_renting
#: model:ir.model.fields.selection,name:sale_renting.selection__sale_temporal_recurrence__unit__month
msgid "Months"
msgstr "měsíců"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_view_search
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_search
msgid "My Orders"
msgstr "Moje objednávky"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_temporal_recurrence__name
msgid "Name"
msgstr "Název"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_order__next_action_date
msgid "Next Action"
msgstr "Další akce"

#. module: sale_renting
#: model_terms:ir.actions.act_window,help:sale_renting.action_rental_report
msgid "No data yet!"
msgstr "Zatím žádná data!"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_form
msgid "Ok"
msgstr "Ok"

#. module: sale_renting
#: model_terms:ir.actions.act_window,help:sale_renting.rental_order_action
msgid ""
"Once the quotation is confirmed, it becomes a rental order.<br> You will be "
"able to create an invoice and collect the payment."
msgstr ""
"Jakmile je nabídka potvrzena, stává se objednávkou pronájmu. <br>Budete moci"
" vytvořit fakturu a inkasovat platbu."

#. module: sale_renting
#. odoo-javascript
#: code:addons/sale_renting/static/src/js/tours/rental.js:0
msgid "Once the rental is done, you can register the return."
msgstr "Jakmile je pronájem dokončen, můžete zaregistrovat vrácení."

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_rental_order_wizard__order_id
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_view_search
msgid "Order"
msgstr "Objednávka"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_report__order_id
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__order_id
msgid "Order #"
msgstr "Číslo objednávky"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__order_date
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_search
msgid "Order Date"
msgstr "Datum objednání"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_rental_order_wizard_line__order_line_id
msgid "Order Line"
msgstr "Řádek objednávky"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__name
msgid "Order Reference"
msgstr "Odkaz objednávky"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__order_line_id
msgid "Order line #"
msgstr "Objednací řádek č."

#. module: sale_renting
#: model:ir.ui.menu,name:sale_renting.rental_order_menu
#: model:ir.ui.menu,name:sale_renting.rental_orders_all
msgid "Orders"
msgstr "Objednávky"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_res_company__extra_day
#: model:ir.model.fields,field_description:sale_renting.field_res_config_settings__extra_day
msgid "Per Day"
msgstr "Na den"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_res_company__extra_hour
#: model:ir.model.fields,field_description:sale_renting.field_res_config_settings__extra_hour
msgid "Per Hour"
msgstr "Na hodinu"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.product_pricelist_view
#: model_terms:ir.ui.view,arch_db:sale_renting.product_template_form_view_rental
msgid "Period"
msgstr "Období"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.sale_temporal_recurrence_view_form
#: model_terms:ir.ui.view,arch_db:sale_renting.sale_temporal_recurrence_view_tree
msgid "Periodicity"
msgstr "Periodicita"

#. module: sale_renting
#: model:ir.actions.act_window,name:sale_renting.sale_temporal_recurrence_action
msgid "Periods"
msgstr "Období"

#. module: sale_renting
#: model:ir.model,name:sale_renting.model_rental_order_wizard
msgid "Pick-up/Return products"
msgstr "Vyzvednutí/vrácení produktů"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_gantt
msgid "Picked-Up"
msgstr "Vyzvednuté"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_rental_order_wizard_line__qty_delivered
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_form_view
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_form
msgid "Picked-up"
msgstr "Vyzvednuté"

#. module: sale_renting
#: model:ir.model.fields.selection,name:sale_renting.selection__sale_order__rental_status__return
#: model:ir.model.fields.selection,name:sale_renting.selection__sale_rental_schedule__rental_status__return
#: model:ir.model.fields.selection,name:sale_renting.selection__sale_rental_schedule__report_line_status__pickedup
#: model_terms:ir.ui.view,arch_db:sale_renting.report_rental_order_document
msgid "Pickedup"
msgstr "Vyzvednuté"

#. module: sale_renting
#: model:ir.model.fields.selection,name:sale_renting.selection__rental_order_wizard__status__pickup
#: model:ir.ui.menu,name:sale_renting.rental_orders_pickup
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_form_view
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_gantt
msgid "Pickup"
msgstr "Vyzdvihnutí"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__pickup_date
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_view_search
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_search
#: model_terms:ir.ui.view,arch_db:sale_renting.report_rental_order_document
msgid "Pickup Date"
msgstr "Datum vyzvednutí"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.report_rental_order_document
msgid "Pickup Receipt #"
msgstr "Potvrzení o vyzvednutí č."

#. module: sale_renting
#: model:ir.actions.report,name:sale_renting.action_report_rental_saleorder
msgid "Pickup and Return Receipt"
msgstr "Potvrzení o vyzvednutí a vrácení"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_order_line__reservation_begin
msgid "Pickup date - padding time"
msgstr "Datum vyzvednutí - čas tolerance"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_view_kanban
msgid "Pickup:"
msgstr "Vyzvednutí:"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_product_pricing__price
msgid "Price"
msgstr "Cena"

#. module: sale_renting
#: model:ir.model,name:sale_renting.model_product_pricelist
#: model:ir.model.fields,field_description:sale_renting.field_product_pricing__pricelist_id
msgid "Pricelist"
msgstr "Ceník"

#. module: sale_renting
#: model:ir.actions.act_window,name:sale_renting.product_pricing_action
#: model_terms:ir.ui.view,arch_db:sale_renting.product_pricing_tree
msgid "Prices"
msgstr "Ceny"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.product_template_form_view_rental
msgid "Pricing"
msgstr "Ceny"

#. module: sale_renting
#: model:ir.model,name:sale_renting.model_product_pricing
msgid "Pricing rule of rental products"
msgstr ""

#. module: sale_renting
#: model:product.template,name:sale_renting.rental_product_3_product_template
msgid "Printer"
msgstr "Tiskárna"

#. module: sale_renting
#: model:ir.model,name:sale_renting.model_product_template
#: model:ir.model.fields,field_description:sale_renting.field_rental_order_wizard_line__product_id
#: model:ir.model.fields,field_description:sale_renting.field_res_company__extra_product
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_report__product_id
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__product_id
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_view_search
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_report_search_view
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_search
#: model_terms:ir.ui.view,arch_db:sale_renting.res_config_settings_view_form
msgid "Product"
msgstr "Produkt"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.report_rental_order_document
msgid "Product A"
msgstr ""

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_report__categ_id
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__categ_id
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_report_search_view
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_search
msgid "Product Category"
msgstr "Produktová kategorie"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__product_name
msgid "Product Reference"
msgstr "Odkaz na produkt"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_product_pricing__product_template_id
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_report__product_tmpl_id
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__product_tmpl_id
msgid "Product Template"
msgstr "Šablona produktu"

#. module: sale_renting
#: model:ir.model,name:sale_renting.model_product_product
#: model:ir.model.fields,field_description:sale_renting.field_product_pricing__product_variant_ids
msgid "Product Variant"
msgstr "Produktová varianta"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.res_config_settings_view_form
msgid "Product to charge extra time"
msgstr "Produkt pro účtování času navíc"

#. module: sale_renting
#: model:ir.actions.act_window,name:sale_renting.rental_product_template_action
#: model:ir.ui.menu,name:sale_renting.menu_rental_products
#: model_terms:ir.ui.view,arch_db:sale_renting.product_pricelist_view
msgid "Products"
msgstr "Produkty"

#. module: sale_renting
#: model:product.template,name:sale_renting.rental_product_1_product_template
msgid "Projector"
msgstr "Projektor"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__product_uom_qty
msgid "Qty Ordered"
msgstr "Množ. objednané"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__qty_delivered
msgid "Qty Picked-Up"
msgstr "Vyzvednuté množství"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__qty_returned
msgid "Qty Returned"
msgstr "Vrácené množství"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_product_product__qty_in_rent
#: model:ir.model.fields,field_description:sale_renting.field_product_template__qty_in_rent
msgid "Quantity currently in rent"
msgstr "Množství aktuálně v pronájmu"

#. module: sale_renting
#: model:sale.temporal.recurrence,name:sale_renting.recurrence_quarterly
msgid "Quarterly"
msgstr "Kvartálně"

#. module: sale_renting
#: model:ir.model.fields.selection,name:sale_renting.selection__sale_order__rental_status__draft
#: model:ir.model.fields.selection,name:sale_renting.selection__sale_rental_report__state__draft
#: model:ir.model.fields.selection,name:sale_renting.selection__sale_rental_schedule__rental_status__draft
#: model:ir.model.fields.selection,name:sale_renting.selection__sale_rental_schedule__state__draft
msgid "Quotation"
msgstr "Nabídka"

#. module: sale_renting
#: model:ir.model.fields.selection,name:sale_renting.selection__sale_order__rental_status__sent
#: model:ir.model.fields.selection,name:sale_renting.selection__sale_rental_report__state__sent
#: model:ir.model.fields.selection,name:sale_renting.selection__sale_rental_schedule__rental_status__sent
#: model:ir.model.fields.selection,name:sale_renting.selection__sale_rental_schedule__state__sent
msgid "Quotation Sent"
msgstr "Nabídka odeslána"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_report_search_view
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_search
msgid "Quotations"
msgstr "Nabídky"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_form_view
msgid "Recompute all prices based on this duration"
msgstr ""

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_order__remaining_hours
msgid "Remaining duration in hours"
msgstr ""

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_product_product__rent_ok
#: model:ir.model.fields,field_description:sale_renting.field_product_template__rent_ok
#: model:ir.model.fields,field_description:sale_renting.field_sale_order_line__is_product_rentable
#: model:ir.ui.menu,name:sale_renting.rental_menu_root
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_product_template_search_view
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_gantt
#: model_terms:ir.ui.view,arch_db:sale_renting.res_config_settings_view_form
msgid "Rental"
msgstr "Pronájem"

#. module: sale_renting
#: model:ir.actions.act_window,name:sale_renting.action_rental_report
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_report_graph_view
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_report_pivot_view
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_report_search_view
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_search
#: model_terms:ir.ui.view,arch_db:sale_renting.sale_rental_report_view_tree
msgid "Rental Analysis"
msgstr "Analýza pronájmu"

#. module: sale_renting
#: model:ir.model,name:sale_renting.model_sale_rental_report
msgid "Rental Analysis Report"
msgstr "Analýza pronájmů"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_form
msgid "Rental Order"
msgstr "Objednávka pronájmu"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_rental_order_wizard_line__rental_order_wizard_id
msgid "Rental Order Wizard"
msgstr "Průvodce objednávkou pronájmu"

#. module: sale_renting
#: model:ir.actions.act_window,name:sale_renting.rental_order_action
#: model:ir.actions.act_window,name:sale_renting.rental_order_today_pickup_action
#: model:ir.actions.act_window,name:sale_renting.rental_order_today_return_action
msgid "Rental Orders"
msgstr "Objednávky pronájmu"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.product_pricelist_view
msgid "Rental Pricelist Rules"
msgstr ""

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_order__rental_return_date
#: model:ir.model.fields,field_description:sale_renting.field_sale_order_line__return_date
msgid "Rental Return Date"
msgstr ""

#. module: sale_renting
#: model:ir.model,name:sale_renting.model_sale_rental_schedule
msgid "Rental Schedule"
msgstr "Kalendář pronájmů"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_order__rental_start_date
#: model:ir.model.fields,field_description:sale_renting.field_sale_order_line__start_date
msgid "Rental Start Date"
msgstr ""

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_order__rental_status
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__rental_status
msgid "Rental Status"
msgstr "Stav pronájmu"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__report_line_status
msgid "Rental Status (advanced)"
msgstr "Stav výpůjčky (pokročilé)"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_rental_order_wizard__rental_wizard_line_ids
msgid "Rental Wizard Line"
msgstr "Průvodce půjčovnou"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_form_view
msgid "Rental period"
msgstr ""

#. module: sale_renting
#: model:ir.ui.menu,name:sale_renting.menu_sale_renting_periods
msgid "Rental periods"
msgstr "Období pronájmu"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_product_product__display_price
#: model:ir.model.fields,field_description:sale_renting.field_product_template__display_price
msgid "Rental price"
msgstr ""

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.product_template_form_view_rental
msgid "Rental prices"
msgstr ""

#. module: sale_renting
#. odoo-python
#: code:addons/sale_renting/models/sale_order.py:0
msgid "Rental prices have been recomputed with the new period."
msgstr ""

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.product_pricelist_view
msgid "Rental rules"
msgstr ""

#. module: sale_renting
#: model:ir.model,name:sale_renting.model_rental_order_wizard_line
msgid "RentalOrderLine transient representation"
msgstr "Přechodná reprezentace položky objednávky pronájmů"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_view_search
msgid "Rentals"
msgstr "Pronájmy"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_product_pricing__recurrence_id
msgid "Renting Period"
msgstr ""

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_product_pricelist__product_pricing_ids
msgid "Renting Price Rules"
msgstr ""

#. module: sale_renting
#: model:ir.ui.menu,name:sale_renting.menu_rental_reporting
msgid "Reporting"
msgstr "Výkazy"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.product_template_form_view_rental
msgid "Reservations"
msgstr "Rezervace"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_rental_order_wizard_line__qty_reserved
#: model:ir.model.fields.selection,name:sale_renting.selection__sale_order__rental_status__pickup
#: model:ir.model.fields.selection,name:sale_renting.selection__sale_rental_schedule__rental_status__pickup
#: model:ir.model.fields.selection,name:sale_renting.selection__sale_rental_schedule__report_line_status__reserved
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_gantt
msgid "Reserved"
msgstr "Rezervováno"

#. module: sale_renting
#: model:ir.model.fields.selection,name:sale_renting.selection__rental_order_wizard__status__return
#: model:ir.ui.menu,name:sale_renting.rental_orders_return
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_form_view
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_gantt
msgid "Return"
msgstr "Navrácení"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__return_date
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_view_search
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_search
msgid "Return Date"
msgstr "Datum vrácení"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_view_kanban
msgid "Return:"
msgstr "Vrácení:"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_rental_order_wizard_line__qty_returned
#: model:ir.model.fields,field_description:sale_renting.field_sale_order_line__qty_returned
#: model:ir.model.fields.selection,name:sale_renting.selection__sale_order__rental_status__returned
#: model:ir.model.fields.selection,name:sale_renting.selection__sale_rental_schedule__rental_status__returned
#: model:ir.model.fields.selection,name:sale_renting.selection__sale_rental_schedule__report_line_status__returned
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_form_view
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_form
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_gantt
#: model_terms:ir.ui.view,arch_db:sale_renting.report_rental_order_document
msgid "Returned"
msgstr "Vráceno"

#. module: sale_renting
#. odoo-python
#: code:addons/sale_renting/models/sale_order_line.py:0
msgid "Returned: %(date)s"
msgstr ""

#. module: sale_renting
#: model:ir.model,name:sale_renting.model_sale_temporal_recurrence
msgid "Sale temporal Recurrence"
msgstr "Časové opakování prodeje"

#. module: sale_renting
#: model:ir.model,name:sale_renting.model_sale_order
#: model:ir.model.fields.selection,name:sale_renting.selection__sale_rental_report__state__sale
#: model:ir.model.fields.selection,name:sale_renting.selection__sale_rental_schedule__state__sale
msgid "Sales Order"
msgstr "Prodejní objednávka"

#. module: sale_renting
#: model:ir.model,name:sale_renting.model_sale_order_line
msgid "Sales Order Line"
msgstr "Položka prodejní objednávky"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__team_id
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_view_search
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_search
msgid "Sales Team"
msgstr "Obchodní tým"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_report__user_id
msgid "Salesman"
msgstr "Pokladník"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__user_id
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_view_search
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_report_search_view
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_search
msgid "Salesperson"
msgstr "Obchodník"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.report_rental_order_document
msgid "Sample Document"
msgstr ""

#. module: sale_renting
#. odoo-javascript
#: code:addons/sale_renting/static/src/js/tours/rental.js:0
msgid "Save the product."
msgstr "Uložte produkt."

#. module: sale_renting
#: model:ir.ui.menu,name:sale_renting.menu_rental_schedule
msgid "Schedule"
msgstr "Kalendář pronájmů"

#. module: sale_renting
#. odoo-python
#: code:addons/sale_renting/models/product_product.py:0
#: code:addons/sale_renting/models/product_template.py:0
#: model:ir.actions.act_window,name:sale_renting.action_rental_order_schedule
msgid "Scheduled Rentals"
msgstr "Plánované pronájmy"

#. module: sale_renting
#: model:ir.model.fields,help:sale_renting.field_product_pricing__product_variant_ids
msgid ""
"Select Variants of the Product for which this rule applies.Leave empty if "
"this rule applies for any variant of this template."
msgstr ""

#. module: sale_renting
#: model:ir.model.fields,help:sale_renting.field_product_pricing__product_template_id
msgid "Select products on which this pricing will be applied."
msgstr "Vyberte produkty, na které se bude tato cena vztahovat."

#. module: sale_renting
#. odoo-javascript
#: code:addons/sale_renting/static/src/js/tours/rental.js:0
msgid "Select the rental dates and check the price."
msgstr ""

#. module: sale_renting
#. odoo-javascript
#: code:addons/sale_renting/static/src/js/tours/rental.js:0
msgid "Select your rental product."
msgstr "Vyberte produkt k pronájmu."

#. module: sale_renting
#: model:ir.actions.act_window,name:sale_renting.action_rental_config_settings
#: model:ir.ui.menu,name:sale_renting.menu_rental_settings
msgid "Settings"
msgstr "Nastavení"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_wizard_view_form
msgid "Some delay cost will be added to the sales order."
msgstr "Některé náklady za zpoždění budou přidány k objednávce."

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_report_search_view
msgid "State"
msgstr "Stav"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_rental_order_wizard__status
#: model:ir.model.fields,field_description:sale_renting.field_rental_order_wizard_line__status
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_report__state
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__state
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_view_search
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_search
msgid "Status"
msgstr "Stav"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.sale_rental_report_view_tree
msgid "Sum of Daily Amount"
msgstr "Součet denního množství"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.sale_rental_report_view_tree
msgid "Sum of Daily Ordered Qty"
msgstr "Součet denního objednaného množství"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.sale_rental_report_view_tree
msgid "Sum of Daily Picked-Up Qty"
msgstr "Součet denního objednaného množství"

#. module: sale_renting
#: model:ir.model.fields,help:sale_renting.field_sale_order__duration_days
msgid "The duration in days of the rental period."
msgstr ""

#. module: sale_renting
#: model:ir.model.fields,help:sale_renting.field_sale_order__remaining_hours
msgid "The leftover hours of the rental period."
msgstr ""

#. module: sale_renting
#: model:ir.model.constraint,message:sale_renting.constraint_sale_temporal_recurrence_temporal_recurrence_duration
msgid "The pricing duration has to be greater or equal to 0."
msgstr "Trvání ceny musí být větší nebo rovno 0."

#. module: sale_renting
#: model:ir.model.fields,help:sale_renting.field_res_company__extra_product
msgid "The product is used to add the cost to the sales order"
msgstr "Produkt se používá k přidání nákladů do prodejní objednávky"

#. module: sale_renting
#: model:ir.model.fields,help:sale_renting.field_rental_order_wizard__is_late
#: model:ir.model.fields,help:sale_renting.field_sale_order__is_late
msgid "The products haven't been picked-up or returned in time"
msgstr ""

#. module: sale_renting
#. odoo-javascript
#: code:addons/sale_renting/static/src/js/tours/rental.js:0
msgid "The rental configuration is available here."
msgstr "Nastavení pronájmu je dostupné zde."

#. module: sale_renting
#: model:ir.model.constraint,message:sale_renting.constraint_sale_order_rental_period_coherence
msgid "The rental start date must be before the rental return date if any."
msgstr ""

#. module: sale_renting
#: model_terms:ir.actions.act_window,help:sale_renting.rental_order_today_pickup_action
#: model_terms:ir.actions.act_window,help:sale_renting.rental_order_today_return_action
msgid "There isn't any scheduled pickup or return."
msgstr "Není zde žádný plánované vyzvednutí ani vrácení."

#. module: sale_renting
#: model:ir.model.fields,help:sale_renting.field_res_config_settings__extra_day
msgid ""
"This is the default extra cost per day set on newly created products.You can"
" change this value for existing products directly on the product itself."
msgstr ""

#. module: sale_renting
#: model:ir.model.fields,help:sale_renting.field_res_config_settings__extra_hour
msgid ""
"This is the default extra cost per hour set on newly created products.You "
"can change this value for existing products directly on the product itself."
msgstr ""

#. module: sale_renting
#: model:ir.model.fields,help:sale_renting.field_res_config_settings__extra_product
msgid "This product will be used to add fines in the Rental Order."
msgstr "Tento produkt bude použit k přidání pokut do objednávky pronájmu."

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_form_view
msgid ""
"This will update the unit price of all rental products based on the new "
"period."
msgstr ""

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.res_config_settings_view_form
msgid ""
"Those values are applied to any new rental product and can be changed on "
"product forms."
msgstr ""
"Tyto hodnoty se použijí na jakýkoli nový pronájem produktu a lze je změnit "
"na formulářích produktu."

#. module: sale_renting
#: model:ir.ui.menu,name:sale_renting.rental_orders_today
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_view_search
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_schedule_view_search
msgid "To Do Today"
msgstr "Dnešní úkony"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_view_search_without_searchpanel
msgid "To Pickup"
msgstr "K vyzvednutí"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_view_search_without_searchpanel
msgid "To Return"
msgstr "K vrácení"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_view_tree
msgid "Total Tax Included"
msgstr "Celkem s daní"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_temporal_recurrence__unit
msgid "Unit"
msgstr "Jednotka"

#. module: sale_renting
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_report__product_uom
#: model:ir.model.fields,field_description:sale_renting.field_sale_rental_schedule__product_uom
msgid "Unit of Measure"
msgstr "Měrná jednotka"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_form_view
msgid "Update Rental Prices"
msgstr ""

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.rental_order_wizard_view_form
msgid "Validate"
msgstr "Potvrdit"

#. module: sale_renting
#. odoo-python
#: code:addons/sale_renting/models/sale_order.py:0
msgid "Validate a pickup"
msgstr "Potvrdit vyzvednutí"

#. module: sale_renting
#. odoo-python
#: code:addons/sale_renting/models/sale_order.py:0
msgid "Validate a return"
msgstr "Potvrdit vrácení"

#. module: sale_renting
#. odoo-javascript
#: code:addons/sale_renting/static/src/js/tours/rental.js:0
msgid "Validate the operation after checking the picked-up quantities."
msgstr "Po kontrole odebraných množství potvrďte operaci."

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.product_pricelist_view
msgid "Variants"
msgstr "Varianty"

#. module: sale_renting
#. odoo-javascript
#: code:addons/sale_renting/static/src/js/tours/rental.js:0
msgid ""
"Want to <b>rent products</b>? \n"
" Let's discover Odoo Rental App."
msgstr ""
"Chcete <b>pronajmout produkty</b>? \n"
" Pojďme objevit aplikaci Odoo pronájem."

#. module: sale_renting
#. odoo-python
#: code:addons/sale_renting/models/sale_order_recurrence.py:0
msgid "Week"
msgstr "Týden"

#. module: sale_renting
#: model:sale.temporal.recurrence,name:sale_renting.recurrence_weekly
msgid "Weekly"
msgstr "Týdně"

#. module: sale_renting
#: model:ir.model.fields.selection,name:sale_renting.selection__sale_temporal_recurrence__unit__week
msgid "Weeks"
msgstr "Týdny"

#. module: sale_renting
#. odoo-python
#: code:addons/sale_renting/models/sale_order_recurrence.py:0
msgid "Year"
msgstr "Rok"

#. module: sale_renting
#: model:sale.temporal.recurrence,name:sale_renting.recurrence_yearly
msgid "Yearly"
msgstr "Ročně"

#. module: sale_renting
#: model:ir.model.fields.selection,name:sale_renting.selection__sale_temporal_recurrence__unit__year
msgid "Years"
msgstr "Roků"

#. module: sale_renting
#: model_terms:ir.actions.act_window,help:sale_renting.rental_product_template_action
msgid ""
"You can also create additional products or services to sell by checking *Can"
" be Sold* in the product form (e.g. insurance)."
msgstr ""
"Můžete také vytvořit další produkty nebo služby k prodeji zaškrtnutím "
"políčka *Lze prodat* ve formuláři produktu (např. pojištění)."

#. module: sale_renting
#. odoo-python
#: code:addons/sale_renting/models/product_pricelist.py:0
msgid "You can not have a time-based rule for products that are not rentable."
msgstr ""

#. module: sale_renting
#: model_terms:ir.actions.act_window,help:sale_renting.rental_order_today_pickup_action
#: model_terms:ir.actions.act_window,help:sale_renting.rental_order_today_return_action
msgid ""
"You can search on a larger period using the filters here above\n"
"                <br>\n"
"                or create a new rental order."
msgstr ""
"Pomocí filtrů zde výše můžete vyhledávat ve větším období\n"
"                <br>\n"
"               nebo vytvořit novou objednávku pronájmu."

#. module: sale_renting
#. odoo-python
#: code:addons/sale_renting/wizard/rental_processing.py:0
msgid "You can't return more than what's been picked-up."
msgstr "Nemůžete vrátit více, než co bylo vyzvednuto."

#. module: sale_renting
#. odoo-python
#: code:addons/sale_renting/models/product_pricing.py:0
msgid ""
"You cannot have multiple pricing for the same variant, recurrence and "
"pricelist"
msgstr "Nemůžete mít více cen pro stejnou variantu, opakování a ceník"

#. module: sale_renting
#: model:ir.model.constraint,message:sale_renting.constraint_sale_order_line_rental_stock_coherence
msgid "You cannot return more than what has been picked up."
msgstr "Nemůžete vrátit více, než co bylo vyzvednuto."

#. module: sale_renting
#. odoo-javascript
#: code:addons/sale_renting/static/src/js/tours/rental.js:0
msgid "You're done with your fist rental. Congratulations!"
msgstr ""

#. module: sale_renting
#. odoo-python
#: code:addons/sale_renting/models/product_pricing.py:0
msgid "all variants"
msgstr "všechny varianty"

#. module: sale_renting
#: model_terms:ir.ui.view,arch_db:sale_renting.res_config_settings_view_form
msgid "hours"
msgstr "hodin"
