# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_contract_salary_payroll
# 
# Translators:
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:27+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Hindi (https://app.transifex.com/odoo/teams/41243/hi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: hi\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: hr_contract_salary_payroll
#: model_terms:ir.ui.view,arch_db:hr_contract_salary_payroll.salary_package_sidebar_payroll
msgid ""
"<option value=\"100\" selected=\"1\">Full Time</option>\n"
"                <option value=\"90\">9/10</option>\n"
"                <option value=\"80\">4/5</option>\n"
"                <option value=\"60\">3/5</option>\n"
"                <option value=\"50\">Half Time</option>\n"
"                <option value=\"40\">2/5</option>\n"
"                <option value=\"20\">1/5</option>"
msgstr ""

#. module: hr_contract_salary_payroll
#: model:ir.model.fields,field_description:hr_contract_salary_payroll.field_hr_contract_salary_resume__code
msgid "Code"
msgstr "कोड"

#. module: hr_contract_salary_payroll
#: model:ir.ui.menu,name:hr_contract_salary_payroll.menu_hr_payroll_configuration_contract
msgid "Contracts"
msgstr ""

#. module: hr_contract_salary_payroll
#: model:ir.model.fields,field_description:hr_contract_salary_payroll.field_hr_payroll_headcount_line__employer_cost
msgid "Employer Cost"
msgstr ""

#. module: hr_contract_salary_payroll
#: model:ir.ui.menu,name:hr_contract_salary_payroll.hr_payroll_menu_contract_type
msgid "Employment Types"
msgstr ""

#. module: hr_contract_salary_payroll
#: model:ir.model,name:hr_contract_salary_payroll.model_hr_payroll_headcount_line
msgid "Headcount Line"
msgstr ""

#. module: hr_contract_salary_payroll
#: model_terms:ir.ui.view,arch_db:hr_contract_salary_payroll.hr_contract_salary_resume_view_search_inherit
msgid "Impacts Monthly Total"
msgstr ""

#. module: hr_contract_salary_payroll
#: model:ir.model.fields.selection,name:hr_contract_salary_payroll.selection__hr_contract_salary_resume__value_type__payslip
msgid "Payslip Value"
msgstr ""

#. module: hr_contract_salary_payroll
#: model:ir.model,name:hr_contract_salary_payroll.model_hr_payslip_worked_days
msgid "Payslip Worked Days"
msgstr ""

#. module: hr_contract_salary_payroll
#: model:ir.model.fields,help:hr_contract_salary_payroll.field_hr_contract_salary_resume__value_type
msgid ""
"Pick how the value of the information is computed:\n"
"Fixed value: Set a determined value static for all links\n"
"Contract value: Get the value from a field on the contract record\n"
"Payslip value: Get the value from a field on the payslip record\n"
"Sum of Benefits value: You can pick in all benefits and compute a sum of them\n"
"Monthly Total: The information will be a total of all the informations in the category Monthly Benefits"
msgstr ""

#. module: hr_contract_salary_payroll
#. odoo-javascript
#: code:addons/hr_contract_salary_payroll/static/src/xml/brut2net_modal.xml:0
msgid ""
"Please note that this information may be inaccurate and should be used for "
"reference only."
msgstr ""

#. module: hr_contract_salary_payroll
#: model:ir.model,name:hr_contract_salary_payroll.model_hr_contract_salary_resume
msgid "Salary Package Resume"
msgstr ""

#. module: hr_contract_salary_payroll
#. odoo-javascript
#: code:addons/hr_contract_salary_payroll/static/src/js/tours/hr_payroll.js:0
msgid "Select an <strong>HR Responsible</strong> for the contract."
msgstr ""

#. module: hr_contract_salary_payroll
#: model:ir.ui.menu,name:hr_contract_salary_payroll.hr_payroll_menu_contract_templates
msgid "Templates"
msgstr ""

#. module: hr_contract_salary_payroll
#. odoo-javascript
#: code:addons/hr_contract_salary_payroll/static/src/xml/brut2net_modal.xml:0
msgid "The amounts are calculated  based on a full time permanent contract."
msgstr ""

#. module: hr_contract_salary_payroll
#. odoo-javascript
#: code:addons/hr_contract_salary_payroll/static/src/xml/brut2net_modal.xml:0
msgid ""
"There is no defined payroll structure for your contract. Please contact a "
"responsible for more information."
msgstr ""

#. module: hr_contract_salary_payroll
#. odoo-python
#: code:addons/hr_contract_salary_payroll/controllers/main.py:0
msgid ""
"This is the gross calculated for the current month with a total of %s hours."
msgstr ""

#. module: hr_contract_salary_payroll
#: model:ir.model.fields,help:hr_contract_salary_payroll.field_hr_payroll_headcount_line__employer_cost
msgid "Total real monthly cost of the employee for the employer."
msgstr ""

#. module: hr_contract_salary_payroll
#: model:ir.model.fields,field_description:hr_contract_salary_payroll.field_hr_contract_salary_resume__value_type
msgid "Value Type"
msgstr ""

#. module: hr_contract_salary_payroll
#: model_terms:ir.ui.view,arch_db:hr_contract_salary_payroll.salary_package_sidebar_payroll
msgid "Working Schedule"
msgstr ""
