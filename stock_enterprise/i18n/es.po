# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* stock_enterprise
# 
# Translators:
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:29+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Spanish (https://app.transifex.com/odoo/teams/41243/es/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: stock_enterprise
#: model:ir.model.fields.selection,name:stock_enterprise.selection__stock_report__state__assigned
msgid "Available"
msgstr "Disponible"

#. module: stock_enterprise
#: model_terms:ir.ui.view,arch_db:stock_enterprise.stock_report_pivot_view
msgid "Average Cycle Time (Days)"
msgstr "Tiempo de ciclo medio (días)"

#. module: stock_enterprise
#: model_terms:ir.ui.view,arch_db:stock_enterprise.stock_report_pivot_view
msgid "Average Delay (Days)"
msgstr "Retraso medio (días)"

#. module: stock_enterprise
#: model:ir.model.fields.selection,name:stock_enterprise.selection__stock_report__state__cancel
msgid "Cancelled"
msgstr "Cancelado"

#. module: stock_enterprise
#: model:ir.model.fields,field_description:stock_enterprise.field_stock_report__company_id
msgid "Company"
msgstr "Compañía"

#. module: stock_enterprise
#: model:ir.model.fields,field_description:stock_enterprise.field_stock_report__creation_date
msgid "Creation Date"
msgstr "Fecha de creación"

#. module: stock_enterprise
#: model:ir.model.fields.selection,name:stock_enterprise.selection__stock_report__picking_type_code__outgoing
msgid "Customers"
msgstr "Clientes"

#. module: stock_enterprise
#: model_terms:ir.ui.view,arch_db:stock_enterprise.stock_report_form_view
msgid "Cycle Time"
msgstr "Tiempo del ciclo"

#. module: stock_enterprise
#: model:ir.model.fields,field_description:stock_enterprise.field_stock_report__cycle_time
msgid "Cycle Time (Days)"
msgstr "Tiempo del ciclo (días) "

#. module: stock_enterprise
#: model_terms:ir.ui.view,arch_db:stock_enterprise.stock_report_form_view
msgid "Days"
msgstr "Días"

#. module: stock_enterprise
#: model_terms:ir.ui.view,arch_db:stock_enterprise.stock_report_form_view
msgid "Delay"
msgstr "Retraso"

#. module: stock_enterprise
#: model:ir.model.fields,field_description:stock_enterprise.field_stock_report__delay
msgid "Delay (Days)"
msgstr "Retraso (Días)"

#. module: stock_enterprise
#: model:ir.model.fields,field_description:stock_enterprise.field_stock_report__display_name
msgid "Display Name"
msgstr "Nombre mostrado"

#. module: stock_enterprise
#: model:ir.model.fields.selection,name:stock_enterprise.selection__stock_report__state__done
msgid "Done"
msgstr "Hecho"

#. module: stock_enterprise
#: model_terms:ir.ui.view,arch_db:stock_enterprise.stock_report_search_view
msgid "Done Deliveries"
msgstr "Entregas hechas"

#. module: stock_enterprise
#: model_terms:ir.ui.view,arch_db:stock_enterprise.stock_report_search_view
msgid "Done Receipts"
msgstr "Recepciones hechas"

#. module: stock_enterprise
#: model_terms:ir.ui.view,arch_db:stock_enterprise.stock_report_search_view
msgid "Done Transfers"
msgstr "Traslados hechos"

#. module: stock_enterprise
#: model:ir.model.fields,field_description:stock_enterprise.field_stock_report__scheduled_date
msgid "Expected Date"
msgstr "Fecha prevista"

#. module: stock_enterprise
#: model:ir.model.fields,field_description:stock_enterprise.field_stock_report__id
msgid "ID"
msgstr "ID"

#. module: stock_enterprise
#: model:ir.model.fields.selection,name:stock_enterprise.selection__stock_report__picking_type_code__internal
msgid "Internal"
msgstr "Interno"

#. module: stock_enterprise
#: model:ir.model.fields,field_description:stock_enterprise.field_stock_report__is_late
msgid "Is Late"
msgstr "Es tarde"

#. module: stock_enterprise
#: model:ir.model.fields,field_description:stock_enterprise.field_stock_report__is_backorder
msgid "Is a Backorder"
msgstr "Es una entrega parcial"

#. module: stock_enterprise
#: model:ir.model.fields.selection,name:stock_enterprise.selection__stock_report__state__draft
msgid "New"
msgstr "Nuevo"

#. module: stock_enterprise
#: model_terms:ir.actions.act_window,help:stock_enterprise.stock_report_action_performance
msgid "No data yet!"
msgstr "¡Todavía no hay información!"

#. module: stock_enterprise
#: model:ir.model.fields,field_description:stock_enterprise.field_stock_report__operation_type_id
msgid "Operation Type"
msgstr "Tipo de operación"

#. module: stock_enterprise
#: model:ir.model.fields.selection,name:stock_enterprise.selection__stock_report__state__partially_available
msgid "Partially Available"
msgstr "Parcialmente disponible"

#. module: stock_enterprise
#: model:ir.model.fields,field_description:stock_enterprise.field_stock_report__partner_id
msgid "Partner"
msgstr "Contacto"

#. module: stock_enterprise
#: model:ir.ui.menu,name:stock_enterprise.stock_dashboard_menuitem
msgid "Performance"
msgstr "Rendimiento"

#. module: stock_enterprise
#: model:ir.model.fields,field_description:stock_enterprise.field_stock_report__picking_name
msgid "Picking Name"
msgstr "Nombre de albarán"

#. module: stock_enterprise
#: model:ir.model.fields,field_description:stock_enterprise.field_stock_report__product_id
msgid "Product"
msgstr "Producto"

#. module: stock_enterprise
#: model:ir.model.fields,field_description:stock_enterprise.field_stock_report__categ_id
msgid "Product Category"
msgstr "Categoría de producto"

#. module: stock_enterprise
#: model:ir.model.fields,field_description:stock_enterprise.field_stock_report__product_qty
msgid "Product Quantity"
msgstr "Cantidad de producto"

#. module: stock_enterprise
#: model_terms:ir.ui.view,arch_db:stock_enterprise.stock_enterprise_move_tree_view
msgid "Quantity"
msgstr "Cantidad"

#. module: stock_enterprise
#: model:ir.model.fields,field_description:stock_enterprise.field_stock_report__reference
#: model_terms:ir.ui.view,arch_db:stock_enterprise.stock_map_view
msgid "Reference"
msgstr "Referencia"

#. module: stock_enterprise
#: model_terms:ir.ui.view,arch_db:stock_enterprise.stock_report_cohort_view
msgid "Report"
msgstr "Informe"

#. module: stock_enterprise
#: model_terms:ir.ui.view,arch_db:stock_enterprise.stock_map_view
msgid "Scheduled Date"
msgstr "Fecha programada"

#. module: stock_enterprise
#: model:ir.model.fields,field_description:stock_enterprise.field_stock_report__state
msgid "Status"
msgstr "Estado"

#. module: stock_enterprise
#: model_terms:ir.ui.view,arch_db:stock_enterprise.stock_report_pivot_view
msgid "Stock Overview"
msgstr "Resumen del inventario"

#. module: stock_enterprise
#: model:ir.model,name:stock_enterprise.model_stock_report
msgid "Stock Report"
msgstr "Informe de stock"

#. module: stock_enterprise
#: model:ir.model.fields,field_description:stock_enterprise.field_stock_report__date_done
msgid "Transfer Date"
msgstr "Fecha de traslado"

#. module: stock_enterprise
#: model:ir.model.fields,field_description:stock_enterprise.field_stock_report__picking_id
msgid "Transfer Reference"
msgstr "Referencia de traslado"

#. module: stock_enterprise
#: model:ir.model.fields,field_description:stock_enterprise.field_stock_report__picking_type_code
msgid "Type"
msgstr "Tipo"

#. module: stock_enterprise
#: model:ir.model.fields.selection,name:stock_enterprise.selection__stock_report__picking_type_code__incoming
msgid "Vendors"
msgstr "Proveedores"

#. module: stock_enterprise
#: model:ir.model.fields.selection,name:stock_enterprise.selection__stock_report__state__waiting
msgid "Waiting Another Move"
msgstr "En espera de otro movimiento"

#. module: stock_enterprise
#: model:ir.model.fields.selection,name:stock_enterprise.selection__stock_report__state__confirmed
msgid "Waiting Availability"
msgstr "Esperando disponibilidad"

#. module: stock_enterprise
#: model:ir.actions.act_window,name:stock_enterprise.stock_report_action_performance
msgid "Warehouse Analysis"
msgstr "Análisis de almacén"
