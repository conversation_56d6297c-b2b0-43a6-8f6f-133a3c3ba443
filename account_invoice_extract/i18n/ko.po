# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_invoice_extract
# 
# Translators:
# Wil Odoo, 2024
# Sarah Park, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:26+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Sarah Park, 2024\n"
"Language-Team: Korean (https://app.transifex.com/odoo/teams/41243/ko/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ko\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_move__message_needaction
msgid "Action Needed"
msgstr "조치 필요"

#. module: account_invoice_extract
#: model:ir.model,name:account_invoice_extract.model_ir_attachment
msgid "Attachment"
msgstr "첨부 파일"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_move__message_attachment_count
msgid "Attachment Count"
msgstr "첨부 파일 수"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_bank_statement_line__extract_can_show_banners
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_move__extract_can_show_banners
msgid "Can show the ocr banners"
msgstr "OCR 배너 표시 가능"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_move__extract_can_show_send_button
msgid "Can show the ocr send button"
msgstr "OCR 전송 버튼 표시 가능"

#. module: account_invoice_extract
#: model:ir.model,name:account_invoice_extract.model_res_company
msgid "Companies"
msgstr "회사"

#. module: account_invoice_extract
#: model:ir.model,name:account_invoice_extract.model_res_config_settings
msgid "Config Settings"
msgstr "환경 설정"

#. module: account_invoice_extract
#. odoo-python
#: code:addons/account_invoice_extract/models/account_invoice.py:0
msgid "Couldn't reload AI data."
msgstr "AI 정보를 불러올 수 없습니다."

#. module: account_invoice_extract
#. odoo-javascript
#: code:addons/account_invoice_extract/static/src/js/invoice_extract_form_renderer.js:0
msgid "Create"
msgstr "작성"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_invoice_extract_words__create_uid
msgid "Created by"
msgstr "작성자"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_invoice_extract_words__create_date
msgid "Created on"
msgstr "작성일자"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_res_config_settings__extract_out_invoice_digitalization_mode
msgid "Customer Invoices"
msgstr "고객 청구서"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_res_company__extract_out_invoice_digitalization_mode
msgid "Digitization mode on customer invoices"
msgstr "고객 청구서 대상 디지털 변환 모드"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_res_company__extract_in_invoice_digitalization_mode
msgid "Digitization mode on vendor bills"
msgstr "공급업체 청구서 대상 디지털 변환 모드"

#. module: account_invoice_extract
#: model:ir.model.fields.selection,name:account_invoice_extract.selection__res_company__extract_in_invoice_digitalization_mode__auto_send
#: model:ir.model.fields.selection,name:account_invoice_extract.selection__res_company__extract_out_invoice_digitalization_mode__auto_send
msgid "Digitize automatically"
msgstr "자동으로 디지털 변환"

#. module: account_invoice_extract
#: model_terms:ir.ui.view,arch_db:account_invoice_extract.view_move_form_inherit_ocr
msgid "Digitize document"
msgstr "문서 디지털 변환"

#. module: account_invoice_extract
#: model:ir.model.fields.selection,name:account_invoice_extract.selection__res_company__extract_in_invoice_digitalization_mode__manual_send
#: model:ir.model.fields.selection,name:account_invoice_extract.selection__res_company__extract_out_invoice_digitalization_mode__manual_send
msgid "Digitize on demand only"
msgstr "지정할 경우에만 디지털 변환"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_invoice_extract_words__display_name
msgid "Display Name"
msgstr "표시명"

#. module: account_invoice_extract
#: model:ir.model.fields.selection,name:account_invoice_extract.selection__res_company__extract_in_invoice_digitalization_mode__no_send
#: model:ir.model.fields.selection,name:account_invoice_extract.selection__res_company__extract_out_invoice_digitalization_mode__no_send
msgid "Do not digitize"
msgstr "디지털 변환을 하지 않음"

#. module: account_invoice_extract
#: model_terms:ir.ui.view,arch_db:account_invoice_extract.res_config_settings_view_form
msgid "Enable to get only one invoice line per tax"
msgstr "청구서에 세금별로 하나의 라인만 생성합니다."

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_move__extract_error_message
msgid "Error message"
msgstr "오류 메시지"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_bank_statement_line__extract_attachment_id
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_move__extract_attachment_id
msgid "Extract Attachment"
msgstr "첨부 파일 추출"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_bank_statement_line__extract_detected_layout
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_move__extract_detected_layout
msgid "Extract Detected Layout Id"
msgstr "감지된 레이아웃 ID 추출"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_bank_statement_line__extract_partner_name
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_move__extract_partner_name
msgid "Extract Detected Partner Name"
msgstr "감지된 협력사 이름 추출"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_bank_statement_line__extract_prefill_data
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_move__extract_prefill_data
msgid "Extract Prefill Data"
msgstr "사전 입력 데이터 추출"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_move__extract_state_processed
msgid "Extract State Processed"
msgstr "진행 상태 추출"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_bank_statement_line__extract_word_ids
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_move__extract_word_ids
msgid "Extract Word"
msgstr "단어 추출"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_move__extract_state
msgid "Extract state"
msgstr "추출 상태"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_move__extract_status
msgid "Extract status"
msgstr "추출 상태"

#. module: account_invoice_extract
#: model:ir.model,name:account_invoice_extract.model_account_invoice_extract_words
msgid "Extracted words from invoice scan"
msgstr "청구서를 스캔하여 추출된 단어"

#. module: account_invoice_extract
#: model_terms:ir.ui.view,arch_db:account_invoice_extract.view_move_form_inherit_ocr
msgid "Extraction Information"
msgstr "추출 정보"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_invoice_extract_words__field
msgid "Field"
msgstr "필드"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_move__message_follower_ids
msgid "Followers"
msgstr "팔로워"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_move__message_partner_ids
msgid "Followers (Partners)"
msgstr "팔로워 (협력사)"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_move__has_message
msgid "Has Message"
msgstr "메시지가 있습니다"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_invoice_extract_words__id
msgid "ID"
msgstr "ID"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_move__extract_document_uuid
msgid "ID of the request to IAP-OCR"
msgstr "IAP-OCR 요청 ID"

#. module: account_invoice_extract
#: model:ir.model.fields,help:account_invoice_extract.field_account_move__message_needaction
msgid "If checked, new messages require your attention."
msgstr "만약 선택하였으면, 신규 메시지에 주의를 기울여야 합니다."

#. module: account_invoice_extract
#: model:ir.model.fields,help:account_invoice_extract.field_account_move__message_has_error
#: model:ir.model.fields,help:account_invoice_extract.field_account_move__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "이 옵션을 선택하면 일부 정보가 전달 오류를 생성합니다."

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_invoice_extract_words__invoice_id
msgid "Invoice"
msgstr "청구서"

#. module: account_invoice_extract
#: model:ir.actions.server,name:account_invoice_extract.ir_cron_update_ocr_status_ir_actions_server
msgid "Invoice OCR: Update All Status"
msgstr "청구서 OCR : 전체 상태 업데이트"

#. module: account_invoice_extract
#: model:ir.actions.server,name:account_invoice_extract.ir_cron_ocr_validate_ir_actions_server
msgid "Invoice OCR: Validate Invoices"
msgstr "청구서 OCR: 청구서 확인"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_move__message_is_follower
msgid "Is Follower"
msgstr "팔로워임"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_move__is_in_extractable_state
msgid "Is In Extractable State"
msgstr "추출 가능"

#. module: account_invoice_extract
#: model:ir.model,name:account_invoice_extract.model_account_move
msgid "Journal Entry"
msgstr "전표 입력"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_invoice_extract_words__write_uid
msgid "Last Updated by"
msgstr "최근 갱신한 사람"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_invoice_extract_words__write_date
msgid "Last Updated on"
msgstr "최근 갱신 일자"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_move__message_main_attachment_id
msgid "Main Attachment"
msgstr "주요 첨부 파일"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_move__message_has_error
msgid "Message Delivery error"
msgstr "메시지 전송 오류"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_move__message_ids
msgid "Messages"
msgstr "메시지"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_move__message_needaction_counter
msgid "Number of Actions"
msgstr "작업 수"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_move__message_has_error_counter
msgid "Number of errors"
msgstr "오류 횟수"

#. module: account_invoice_extract
#: model:ir.model.fields,help:account_invoice_extract.field_account_move__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "조치가 필요한 메시지 수"

#. module: account_invoice_extract
#: model:ir.model.fields,help:account_invoice_extract.field_account_move__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "전송 오류가 발생한 메시지 수입니다."

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_invoice_extract_words__ocr_selected
msgid "Ocr Selected"
msgstr "OCR 선택"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_move__rating_ids
msgid "Ratings"
msgstr "평가"

#. module: account_invoice_extract
#: model_terms:ir.ui.view,arch_db:account_invoice_extract.view_move_form_inherit_ocr
msgid "Reload AI Data"
msgstr "AI 데이터 새로고침"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_move__message_has_sms_error
msgid "SMS Delivery error"
msgstr "SMS 전송 에러"

#. module: account_invoice_extract
#: model:ir.actions.server,name:account_invoice_extract.model_account_send_for_digitalization
msgid "Send Bills for digitization"
msgstr "업체 청구서 디지털 변환"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_res_company__extract_single_line_per_tax
#: model:ir.model.fields,field_description:account_invoice_extract.field_res_config_settings__extract_single_line_per_tax
msgid "Single Invoice Line Per Tax"
msgstr "청구서에 세금별 단일 라인 생성"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_invoice_extract_words__user_selected
msgid "User Selected"
msgstr "선택한 사용자"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_res_config_settings__extract_in_invoice_digitalization_mode
msgid "Vendor Bills"
msgstr "공급업체 청구서"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_move__website_message_ids
msgid "Website Messages"
msgstr "웹사이트 메시지"

#. module: account_invoice_extract
#: model:ir.model.fields,help:account_invoice_extract.field_account_move__website_message_ids
msgid "Website communication history"
msgstr "웹사이트 대화 이력"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_invoice_extract_words__word_box_angle
msgid "Word Box Angle"
msgstr "문자 상자 각도"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_invoice_extract_words__word_box_height
msgid "Word Box Height"
msgstr "문자 상자 높이"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_invoice_extract_words__word_box_midX
msgid "Word Box Midx"
msgstr "문자 상자 가로 중앙"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_invoice_extract_words__word_box_midY
msgid "Word Box Midy"
msgstr "문자 상자 세로 중앙"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_invoice_extract_words__word_box_width
msgid "Word Box Width"
msgstr "문자 상자 너비"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_invoice_extract_words__word_page
msgid "Word Page"
msgstr "페이지 내 단어"

#. module: account_invoice_extract
#: model:ir.model.fields,field_description:account_invoice_extract.field_account_invoice_extract_words__word_text
msgid "Word Text"
msgstr "단어"

#. module: account_invoice_extract
#. odoo-python
#: code:addons/account_invoice_extract/models/account_invoice.py:0
msgid "You cannot send a expense that is not in draft state!"
msgstr "경비가 미결 상태인 경우에는 전송할 수 없습니다!"
