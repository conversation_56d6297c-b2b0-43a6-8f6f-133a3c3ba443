# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * stock_intrastat
#
# Translators:
# <PERSON>, 2018
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~11.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-09-20 09:49+0000\n"
"PO-Revision-Date: 2018-09-18 10:05+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2018\n"
"Language-Team: Bosnian (https://www.transifex.com/odoo/teams/41243/bs/)\n"
"Language: bs\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#. module: stock_intrastat
#: model:ir.model.fields,field_description:stock_intrastat.field_stock_warehouse__company_country_id
msgid "Fiscal Country"
msgstr ""

#. module: stock_intrastat
#: model:ir.model,name:stock_intrastat.model_stock_intrastat_report_handler
msgid "Intrastat Report Custom Handler (Stock)"
msgstr ""

#. module: stock_intrastat
#: model:ir.model.fields,field_description:stock_intrastat.field_stock_warehouse__intrastat_region_id
msgid "Intrastat region"
msgstr ""

#. module: stock_intrastat
#: model:ir.model.fields,help:stock_intrastat.field_stock_warehouse__company_country_id
msgid "The country to use the tax reports from for this company"
msgstr ""

#. module: stock_intrastat
#: model:ir.model,name:stock_intrastat.model_stock_warehouse
msgid "Warehouse"
msgstr "Skladište"
