# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_planning
# 
# Translators:
# <PERSON><PERSON>, 2024
# <PERSON>, 2024
# KeyVillage, 2024
# <PERSON> <igor.shelud<PERSON>@gmail.com>, 2024
# <PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON><PERSON>, 2024
# <PERSON>, 2024
# <PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:28+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON>, 2025\n"
"Language-Team: Bulgarian (https://app.transifex.com/odoo/teams/41243/bg/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: bg\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: sale_planning
#. odoo-python
#: code:addons/sale_planning/controllers/main.py:0
msgid "(%(remaining_hours)s remaining)"
msgstr ""

#. module: sale_planning
#: model_terms:ir.ui.view,arch_db:sale_planning.planning_shift_ics_description_inherit
msgid "<b>Sales Order Item:</b>"
msgstr ""

#. module: sale_planning
#: model_terms:ir.ui.view,arch_db:sale_planning.planning_slot_view_form_inherit_sale_planning
msgid ""
"<span class=\"fa fa-exclamation-triangle text-warning\" title=\"The sales "
"order associated with this shift has been cancelled. We recommend either "
"updating the sales order item or unscheduling this shift in line with the "
"cancellation of the sales order.\" invisible=\"sale_order_state != "
"'cancel'\"/>"
msgstr ""

#. module: sale_planning
#: model_terms:ir.ui.view,arch_db:sale_planning.view_order_form_inherit_sale_planning
msgid "<span class=\"o_stat_text\">Planned</span>"
msgstr ""

#. module: sale_planning
#: model_terms:ir.ui.view,arch_db:sale_planning.planning_slot_view_form_inherit_sale_planning
msgid "<span class=\"o_stat_text\">Sales Order</span>"
msgstr "<span class=\"o_stat_text\">Поръчка за продажба</span>"

#. module: sale_planning
#: model_terms:ir.ui.view,arch_db:sale_planning.view_order_form_inherit_sale_planning
msgid "<span class=\"o_stat_text\">To Plan</span>"
msgstr ""

#. module: sale_planning
#: model_terms:ir.ui.view,arch_db:sale_planning.product_template_form_view_invoice_policy_inherit_sale_planning
msgid ""
"<span invisible=\"not planning_enabled\" class=\"oe_inline me-2\">\n"
"                        as\n"
"                    </span>"
msgstr ""

#. module: sale_planning
#: model_terms:ir.ui.view,arch_db:sale_planning.planning_slot_view_gantt_inherit_sale_planning
msgid "<strong>Sales Order Item  — </strong>"
msgstr ""

#. module: sale_planning
#. odoo-javascript
#: code:addons/sale_planning/static/src/views/planning_hooks.js:0
msgid ""
"All open shifts and sales orders have already been assigned, or there are no"
" resources available to take them at this time."
msgstr ""

#. module: sale_planning
#: model:ir.model.fields,field_description:sale_planning.field_planning_slot__allocated_hours
msgid "Allocated Time"
msgstr ""

#. module: sale_planning
#: model_terms:ir.ui.view,arch_db:sale_planning.planning_slot_view_search_inherit_sale_planning
msgid "Billable"
msgstr ""

#. module: sale_planning
#: model:ir.ui.menu,name:sale_planning.sale_planning_menu_schedule_by_sale_order
msgid "By Sales Order"
msgstr ""

#. module: sale_planning
#: model:ir.model.fields,field_description:sale_planning.field_planning_slot__partner_id
msgid "Customer"
msgstr "Клиент"

#. module: sale_planning
#: model:product.template,name:sale_planning.developer_product_product_template
msgid "Developer (Plan services)"
msgstr ""

#. module: sale_planning
#: model:ir.model.fields,field_description:sale_planning.field_planning_slot__end_datetime
msgid "End Date"
msgstr "Крайна дата"

#. module: sale_planning
#: model:planning.role,name:sale_planning.planning_role_tester
msgid "Functional Tester"
msgstr ""

#. module: sale_planning
#: model_terms:ir.ui.view,arch_db:sale_planning.view_order_form_inherit_sale_planning
msgid "Hours"
msgstr "Часове"

#. module: sale_planning
#: model:product.template,name:sale_planning.technical_maintainance_product_product_template
msgid "IT Technical Maintenance (Plan services)"
msgstr ""

#. module: sale_planning
#: model:planning.role,name:sale_planning.planning_role_technician
msgid "IT Technician"
msgstr ""

#. module: sale_planning
#: model:ir.model.fields,help:sale_planning.field_planning_slot__sale_line_plannable
#: model:ir.model.fields,help:sale_planning.field_product_product__planning_enabled
#: model:ir.model.fields,help:sale_planning.field_product_template__planning_enabled
msgid ""
"If enabled, a shift will automatically be generated for the selected role "
"when confirming the Sales Order.                 With the 'auto plan' "
"feature, only employees with this role will be automatically assigned shifts"
" for Sales Orders containing this service.                 The system will "
"consider employee availability and the remaining time to be planned."
"                 You can also manually schedule open shifts for your Sales "
"Order or assign them to any employee you prefer."
msgstr ""

#. module: sale_planning
#: model_terms:ir.ui.view,arch_db:sale_planning.planning_slot_view_search_inherit_sale_planning
msgid "My Sales Orders"
msgstr ""

#. module: sale_planning
#: model_terms:ir.actions.act_window,help:sale_planning.sale_planning_action_schedule_by_sale_order
msgid "No shifts found. Let's create one!"
msgstr "Не са намерени смени. Нека създадем една!"

#. module: sale_planning
#: model_terms:ir.ui.view,arch_db:sale_planning.planning_slot_view_search_inherit_sale_planning
msgid "Non-Billable"
msgstr ""

#. module: sale_planning
#: model:ir.model.constraint,message:sale_planning.constraint_planning_slot_check_datetimes_set_or_plannable_slot
msgid ""
"Only slots linked to a Sales Order with a plannable service can be "
"unscheduled."
msgstr ""

#. module: sale_planning
#. odoo-javascript
#: code:addons/sale_planning/static/src/views/planning_hooks.js:0
msgid "Open shifts and sales orders assigned"
msgstr ""

#. module: sale_planning
#. odoo-javascript
#: code:addons/sale_planning/static/src/views/planning_hooks.js:0
msgid "Open shifts and sales orders unscheduled"
msgstr ""

#. module: sale_planning
#: model_terms:product.template,description:sale_planning.developer_product_product_template
msgid "Our developer will help you to add new features to your Software."
msgstr ""

#. module: sale_planning
#: model:ir.model.fields,field_description:sale_planning.field_product_product__planning_enabled
#: model:ir.model.fields,field_description:sale_planning.field_product_template__planning_enabled
msgid "Plan Services"
msgstr ""

#. module: sale_planning
#: model_terms:ir.ui.view,arch_db:sale_planning.product_template_form_view_invoice_policy_inherit_sale_planning
msgid "Plan shifts for your orders for employees who have this role."
msgstr ""

#. module: sale_planning
#. odoo-python
#: code:addons/sale_planning/models/product.py:0
msgid ""
"Plannable services should be a service product, product\n"
"%s."
msgstr ""

#. module: sale_planning
#. odoo-python
#: code:addons/sale_planning/models/product.py:0
msgid "Plannable services should use an UoM within the %s category."
msgstr ""

#. module: sale_planning
#: model:ir.model,name:sale_planning.model_planning_analysis_report
msgid "Planning Analysis Report"
msgstr "Отчет за анализ на планирането"

#. module: sale_planning
#: model:ir.model,name:sale_planning.model_planning_role
#: model:ir.model.fields,field_description:sale_planning.field_product_product__planning_role_id
#: model:ir.model.fields,field_description:sale_planning.field_product_template__planning_role_id
msgid "Planning Role"
msgstr "Роля в планирането"

#. module: sale_planning
#: model:ir.model,name:sale_planning.model_planning_slot
msgid "Planning Shift"
msgstr "Планова смяна"

#. module: sale_planning
#: model:ir.model,name:sale_planning.model_product_template
msgid "Product"
msgstr "Продукт"

#. module: sale_planning
#: model:ir.model.fields,field_description:sale_planning.field_planning_analysis_report__role_product_ids
msgid "Role Product"
msgstr ""

#. module: sale_planning
#: model:ir.model,name:sale_planning.model_sale_order
#: model:ir.model.fields,field_description:sale_planning.field_planning_analysis_report__sale_order_id
#: model:ir.model.fields,field_description:sale_planning.field_planning_slot__sale_order_id
#: model_terms:ir.ui.view,arch_db:sale_planning.planning_slot_view_search_inherit_sale_planning
msgid "Sales Order"
msgstr "Поръчка"

#. module: sale_planning
#: model:ir.model.fields,field_description:sale_planning.field_planning_analysis_report__sale_line_id
#: model:ir.model.fields,field_description:sale_planning.field_planning_slot__sale_line_id
#: model_terms:ir.ui.view,arch_db:sale_planning.open_slots_list_template
#: model_terms:ir.ui.view,arch_db:sale_planning.period_report_template
#: model_terms:ir.ui.view,arch_db:sale_planning.planning_slot_view_search_inherit_sale_planning
#: model_terms:ir.ui.view,arch_db:sale_planning.resource_sale_planning
msgid "Sales Order Item"
msgstr ""

#. module: sale_planning
#: model_terms:ir.ui.view,arch_db:sale_planning.planning_shift_ics_description_inherit
msgid "Sales Order Item:"
msgstr ""

#. module: sale_planning
#: model:ir.model,name:sale_planning.model_sale_order_line
msgid "Sales Order Line"
msgstr "Ред на поръчка за продажби"

#. module: sale_planning
#: model:ir.model.fields,field_description:sale_planning.field_planning_slot__sale_order_state
msgid "Sales Order State"
msgstr ""

#. module: sale_planning
#: model:ir.model.fields,help:sale_planning.field_planning_slot__sale_line_id
msgid ""
"Sales order item for which this shift will be performed. When sales orders "
"are automatically planned, the remaining hours of the sales order item, as "
"well as the role defined on the service, are taken into account."
msgstr ""

#. module: sale_planning
#: model:ir.actions.act_window,name:sale_planning.sale_planning_action_schedule_by_sale_order
msgid "Schedule by Sales Order"
msgstr ""

#. module: sale_planning
#: model_terms:ir.actions.act_window,help:sale_planning.sale_planning_action_schedule_by_sale_order
msgid ""
"Schedule your human and material resources across roles, projects and sales "
"orders."
msgstr ""
"Планирайте вашите човешки и материални ресурси по длъжности, проекти и "
"поръчки за продажби."

#. module: sale_planning
#: model:ir.model.fields,field_description:sale_planning.field_planning_role__product_ids
msgid "Services"
msgstr "Услуги"

#. module: sale_planning
#: model:ir.model.fields,field_description:sale_planning.field_planning_slot__start_datetime
msgid "Start Date"
msgstr "Начална Дата"

#. module: sale_planning
#: model_terms:product.template,description:sale_planning.technical_maintainance_product_product_template
msgid "Take a rest. We'll do our best."
msgstr ""

#. module: sale_planning
#. odoo-javascript
#: code:addons/sale_planning/static/src/views/sale_planning_gantt/sale_planning_gantt_renderer.js:0
msgid ""
"There are no hours left to plan, or there are no resources available at the "
"time."
msgstr ""

#. module: sale_planning
#. odoo-javascript
#: code:addons/sale_planning/static/src/views/sale_planning_gantt/sale_planning_gantt_renderer.js:0
msgid "There are no sales order items to plan."
msgstr ""

#. module: sale_planning
#. odoo-python
#: code:addons/sale_planning/models/planning_slot.py:0
msgid "This Sale Order Item doesn't have a target value of planned hours."
msgstr ""

#. module: sale_planning
#. odoo-javascript
#: code:addons/sale_planning/static/src/views/sale_planning_gantt/sale_planning_gantt_renderer.js:0
msgid ""
"This resource is not available for this shift during the selected period."
msgstr ""

#. module: sale_planning
#. odoo-javascript
#: code:addons/sale_planning/static/src/views/sale_planning_gantt/sale_planning_gantt_renderer.js:0
#: model_terms:ir.ui.view,arch_db:sale_planning.planning_slot_view_gantt_inherit_sale_planning
#: model_terms:ir.ui.view,arch_db:sale_planning.planning_view_form_in_gantt_inherit_sale_planning
msgid "Unschedule"
msgstr ""

#. module: sale_planning
#. odoo-python
#: code:addons/sale_planning/models/sale_order.py:0
msgid "View Planning"
msgstr ""

#. module: sale_planning
#: model:planning.role,name:sale_planning.planning_role_planner
msgid "Work Planner"
msgstr ""

#. module: sale_planning
#. odoo-python
#: code:addons/sale_planning/models/planning_slot.py:0
msgid "You are attempting to create a slot for a cancelled sales order."
msgstr ""

#. module: sale_planning
#. odoo-python
#: code:addons/sale_planning/models/sale_order.py:0
msgid ""
"You cannot update the company for sales order %(order_name)s as it's linked to shifts in another company.\n"
"Please transfer shifts %(slots_names)s to the destination company first."
msgstr ""

#. module: sale_planning
#: model_terms:ir.ui.view,arch_db:sale_planning.planning_role_view_tree_inherit_sale_planning
msgid "e.g. Cleaning Services"
msgstr ""

#. module: sale_planning
#: model_terms:ir.ui.view,arch_db:sale_planning.product_template_form_view_invoice_policy_inherit_sale_planning
msgid "e.g. Consultant"
msgstr ""

#. module: sale_planning
#. odoo-python
#: code:addons/sale_planning/models/sale_order_line.py:0
msgid "remaining"
msgstr ""
