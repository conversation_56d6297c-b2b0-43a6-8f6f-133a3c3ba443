# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* helpdesk_stock
# 
# Translators:
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:27+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Korean (https://app.transifex.com/odoo/teams/41243/ko/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ko\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: helpdesk_stock
#: model:ir.model.fields,field_description:helpdesk_stock.field_stock_return_picking__partner_id
msgid "Customer"
msgstr "고객"

#. module: helpdesk_stock
#: model_terms:ir.ui.view,arch_db:helpdesk_stock.view_stock_return_picking_form_inherit_helpdesk_stock
msgid "Delivery to Return"
msgstr "반품 배송"

#. module: helpdesk_stock
#: model:ir.model.fields,help:helpdesk_stock.field_helpdesk_ticket__tracking
msgid "Ensure the traceability of a storable product in your warehouse."
msgstr "창고에 저장 가능한 품목의 추적성을 확인하십시오."

#. module: helpdesk_stock
#: model:ir.model.fields,field_description:helpdesk_stock.field_helpdesk_ticket__has_partner_picking
msgid "Has Partner Picking"
msgstr "협력사 픽업 있음"

#. module: helpdesk_stock
#: model:ir.model,name:helpdesk_stock.model_helpdesk_ticket
msgid "Helpdesk Ticket"
msgstr "고객센터 티켓"

#. module: helpdesk_stock
#: model:ir.model.fields,field_description:helpdesk_stock.field_helpdesk_ticket__lot_id
msgid "Lot/Serial Number"
msgstr "LOT/일련번호"

#. module: helpdesk_stock
#: model:ir.model.fields,field_description:helpdesk_stock.field_stock_return_picking__picking_id
msgid "Picking"
msgstr "선별"

#. module: helpdesk_stock
#: model:ir.model.fields,field_description:helpdesk_stock.field_helpdesk_sla_report_analysis__product_id
#: model:ir.model.fields,field_description:helpdesk_stock.field_helpdesk_ticket__product_id
#: model:ir.model.fields,field_description:helpdesk_stock.field_helpdesk_ticket_report_analysis__product_id
msgid "Product"
msgstr "품목"

#. module: helpdesk_stock
#: model:ir.model.fields,help:helpdesk_stock.field_helpdesk_ticket__product_id
msgid ""
"Product this ticket is about. If selected, only the sales orders, deliveries"
" and invoices including this product will be visible."
msgstr "이 티켓에 대한 품목입니다. 선택하면 이 품목과 함께 판매주문서, 배송 및 청구서만 표시됩니다."

#. module: helpdesk_stock
#: model_terms:ir.ui.view,arch_db:helpdesk_stock.helpdesk_ticket_view_form_inherit_stock_user
msgid "Return"
msgstr "반납"

#. module: helpdesk_stock
#. odoo-python
#: code:addons/helpdesk_stock/models/stock_picking.py:0
msgid "Return %(status)s"
msgstr "반품 %(status)s"

#. module: helpdesk_stock
#. odoo-python
#: code:addons/helpdesk_stock/models/helpdesk_ticket.py:0
#: model:ir.model.fields,field_description:helpdesk_stock.field_helpdesk_ticket__picking_ids
msgid "Return Orders"
msgstr "반품 주문서"

#. module: helpdesk_stock
#: model:ir.model.fields,field_description:helpdesk_stock.field_helpdesk_ticket__pickings_count
msgid "Return Orders Count"
msgstr "반품 주문서 수"

#. module: helpdesk_stock
#: model:ir.model,name:helpdesk_stock.model_stock_return_picking
msgid "Return Picking"
msgstr "반품 선별"

#. module: helpdesk_stock
#: model_terms:ir.ui.view,arch_db:helpdesk_stock.helpdesk_ticket_view_form_inherit_helpdesk_stock
msgid "Returns"
msgstr "반품"

#. module: helpdesk_stock
#: model:ir.model,name:helpdesk_stock.model_helpdesk_sla_report_analysis
msgid "SLA Status Analysis"
msgstr "SLA 상태 분석"

#. module: helpdesk_stock
#: model:ir.model.fields,field_description:helpdesk_stock.field_stock_return_picking__sale_order_id
msgid "Sales Order"
msgstr "판매 주문"

#. module: helpdesk_stock
#: model:ir.model.fields,field_description:helpdesk_stock.field_stock_return_picking__suitable_picking_ids
msgid "Suitable Picking"
msgstr "적합한 픽업"

#. module: helpdesk_stock
#: model:ir.model.fields,field_description:helpdesk_stock.field_stock_return_picking__suitable_sale_order_ids
msgid "Suitable Sale Order"
msgstr "적합한 판매주문서"

#. module: helpdesk_stock
#: model:ir.model.fields,field_description:helpdesk_stock.field_stock_return_picking__ticket_id
msgid "Ticket"
msgstr "티켓"

#. module: helpdesk_stock
#: model:ir.model,name:helpdesk_stock.model_helpdesk_ticket_report_analysis
msgid "Ticket Analysis"
msgstr "티켓 분석"

#. module: helpdesk_stock
#: model:ir.model.fields,field_description:helpdesk_stock.field_helpdesk_ticket__tracking
msgid "Tracking"
msgstr "추적"

#. module: helpdesk_stock
#: model:ir.model,name:helpdesk_stock.model_stock_picking
msgid "Transfer"
msgstr "전송"
