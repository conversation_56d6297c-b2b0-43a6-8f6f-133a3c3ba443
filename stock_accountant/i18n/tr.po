# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* stock_accountant
# 
# Translators:
# <PERSON><PERSON>, 2024
# <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:28+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON>, 2024\n"
"Language-Team: Turkish (https://app.transifex.com/odoo/teams/41243/tr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: tr\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: stock_accountant
#: model_terms:ir.ui.view,arch_db:stock_accountant.res_config_settings_view_form
msgid "Automatic Accounting"
msgstr ""

#. module: stock_accountant
#: model:ir.model,name:stock_accountant.model_account_bank_statement_line
msgid "Bank Statement Line"
msgstr "Banka Hesap Ekstresi Kalemi"

#. module: stock_accountant
#: model:ir.model,name:stock_accountant.model_res_config_settings
msgid "Config Settings"
msgstr "Yapılandırma Ayarları"

#. module: stock_accountant
#: model_terms:ir.ui.view,arch_db:stock_accountant.res_config_settings_view_form
msgid "Enable automatic accounting entries for stock movements"
msgstr ""

#. module: stock_accountant
#: model:ir.model.fields,field_description:stock_accountant.field_res_config_settings__property_account_expense_categ_id
msgid "Expense Account"
msgstr "Gider Hesabı"

#. module: stock_accountant
#: model_terms:ir.ui.view,arch_db:stock_accountant.res_config_settings_view_form
msgid "General Account Properties"
msgstr ""

#. module: stock_accountant
#: model:ir.model.fields,field_description:stock_accountant.field_res_config_settings__property_account_income_categ_id
msgid "Income Account"
msgstr "Gelir Hesabı"

#. module: stock_accountant
#: model:ir.model.fields,field_description:stock_accountant.field_res_config_settings__property_stock_account_input_categ_id
msgid "Stock Input Account"
msgstr "Stok Giriş Hesabı"

#. module: stock_accountant
#: model:ir.model.fields,field_description:stock_accountant.field_res_config_settings__property_stock_journal
msgid "Stock Journal"
msgstr "Stok Yevmiyesi"

#. module: stock_accountant
#: model:ir.model.fields,field_description:stock_accountant.field_res_config_settings__property_stock_account_output_categ_id
msgid "Stock Output Account"
msgstr "Stok Çıkış Hesabı"

#. module: stock_accountant
#: model_terms:ir.ui.view,arch_db:stock_accountant.res_config_settings_view_form
msgid "Stock Valuation"
msgstr "Stok Değerleme"

#. module: stock_accountant
#: model:ir.model.fields,field_description:stock_accountant.field_res_config_settings__property_stock_valuation_account_id
msgid "Stock Valuation Account"
msgstr "Stok Değerleme Hesabı"

#. module: stock_accountant
#: model_terms:ir.ui.view,arch_db:stock_accountant.res_config_settings_view_form
msgid ""
"The below accounts will be used by default for automatic inventory "
"valuation."
msgstr ""
