# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* spreadsheet_dashboard_hr_payroll
# 
# Translators:
# <PERSON> <<EMAIL>>, 2024
# <PERSON>, 2024
# Rune Restad, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-25 09:15+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Rune Restad, 2025\n"
"Language-Team: <PERSON>l (https://app.transifex.com/odoo/teams/41243/nb/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: nb\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: spreadsheet_dashboard_hr_payroll
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_sample_dashboard.json:0
msgid "#Payslips"
msgstr ""

#. module: spreadsheet_dashboard_hr_payroll
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_sample_dashboard.json:0
msgid "Absenteeism Rate"
msgstr ""

#. module: spreadsheet_dashboard_hr_payroll
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_sample_dashboard.json:0
msgid "Attendance Rate"
msgstr ""

#. module: spreadsheet_dashboard_hr_payroll
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_sample_dashboard.json:0
msgid "Average Basic Wage"
msgstr "Gjennomsnitt normallønn"

#. module: spreadsheet_dashboard_hr_payroll
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_sample_dashboard.json:0
msgid "Average Gross Wage"
msgstr ""

#. module: spreadsheet_dashboard_hr_payroll
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_sample_dashboard.json:0
msgid "Average Hours per Days of Work"
msgstr ""

#. module: spreadsheet_dashboard_hr_payroll
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_sample_dashboard.json:0
msgid "Average Net Wage"
msgstr "Gjennomsnittlig netto lønn"

#. module: spreadsheet_dashboard_hr_payroll
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_sample_dashboard.json:0
msgid "Avg Hours/Days"
msgstr "Gj.snitt timer/dager"

#. module: spreadsheet_dashboard_hr_payroll
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_dashboard.json:0
msgid "Companies"
msgstr "Firmaer"

#. module: spreadsheet_dashboard_hr_payroll
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_sample_dashboard.json:0
msgid "Current"
msgstr "Nåværende"

#. module: spreadsheet_dashboard_hr_payroll
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_sample_dashboard.json:0
msgid "Days"
msgstr "Dager"

#. module: spreadsheet_dashboard_hr_payroll
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_sample_dashboard.json:0
msgid "Employees"
msgstr "Ansatte"

#. module: spreadsheet_dashboard_hr_payroll
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_sample_dashboard.json:0
msgid "Employees (%)"
msgstr ""

#. module: spreadsheet_dashboard_hr_payroll
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_sample_dashboard.json:0
msgid "FTE"
msgstr ""

#. module: spreadsheet_dashboard_hr_payroll
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_sample_dashboard.json:0
msgid "FTE (%)"
msgstr ""

#. module: spreadsheet_dashboard_hr_payroll
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_dashboard.json:0
msgid "Job position"
msgstr ""

#. module: spreadsheet_dashboard_hr_payroll
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_sample_dashboard.json:0
msgid "Paid Time Off"
msgstr "Lønnet fravær"

#. module: spreadsheet_dashboard_hr_payroll
#: model:spreadsheet.dashboard,name:spreadsheet_dashboard_hr_payroll.spreadsheet_dashboard_payroll
msgid "Payroll"
msgstr "Lønnsavregning"

#. module: spreadsheet_dashboard_hr_payroll
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_sample_dashboard.json:0
msgid "Payroll Analysis"
msgstr "Lønnsanalyse"

#. module: spreadsheet_dashboard_hr_payroll
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_dashboard.json:0
msgid "Period"
msgstr "Periode"

#. module: spreadsheet_dashboard_hr_payroll
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_sample_dashboard.json:0
msgid "Previous"
msgstr "Tilbake"

#. module: spreadsheet_dashboard_hr_payroll
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_sample_dashboard.json:0
msgid "Total Basic Wage"
msgstr "Total grunnlønn"

#. module: spreadsheet_dashboard_hr_payroll
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_sample_dashboard.json:0
msgid "Total Gross Wage"
msgstr ""

#. module: spreadsheet_dashboard_hr_payroll
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_sample_dashboard.json:0
msgid "Total Net Wage"
msgstr "Total netto lønn"

#. module: spreadsheet_dashboard_hr_payroll
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_sample_dashboard.json:0
msgid "Unpaid TIme Off"
msgstr ""

#. module: spreadsheet_dashboard_hr_payroll
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_sample_dashboard.json:0
msgid "Work Days"
msgstr "Arbeidsdager"

#. module: spreadsheet_dashboard_hr_payroll
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_sample_dashboard.json:0
msgid "Work Entries Analysis"
msgstr "Analyse av arbeidstype"

#. module: spreadsheet_dashboard_hr_payroll
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_sample_dashboard.json:0
msgid "Work Hours"
msgstr "Arbeidstimer"

#. module: spreadsheet_dashboard_hr_payroll
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_sample_dashboard.json:0
msgid "Work type"
msgstr "Arbeidstype"
