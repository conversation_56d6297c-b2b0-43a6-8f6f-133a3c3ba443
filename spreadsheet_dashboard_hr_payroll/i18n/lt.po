# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* spreadsheet_dashboard_hr_payroll
# 
# Translators:
# <AUTHOR> <EMAIL>, 2024
# <PERSON><PERSON> <gail<PERSON>@vialaurea.lt>, 2024
# <PERSON> <<EMAIL>>, 2024
# <PERSON>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# Donata<PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-25 09:15+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Donatas <<EMAIL>>, 2024\n"
"Language-Team: Lithuanian (https://app.transifex.com/odoo/teams/41243/lt/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: lt\n"
"Plural-Forms: nplurals=4; plural=(n % 10 == 1 && (n % 100 > 19 || n % 100 < 11) ? 0 : (n % 10 >= 2 && n % 10 <=9) && (n % 100 > 19 || n % 100 < 11) ? 1 : n % 1 != 0 ? 2: 3);\n"

#. module: spreadsheet_dashboard_hr_payroll
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_sample_dashboard.json:0
msgid "#Payslips"
msgstr ""

#. module: spreadsheet_dashboard_hr_payroll
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_sample_dashboard.json:0
msgid "Absenteeism Rate"
msgstr ""

#. module: spreadsheet_dashboard_hr_payroll
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_sample_dashboard.json:0
msgid "Attendance Rate"
msgstr ""

#. module: spreadsheet_dashboard_hr_payroll
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_sample_dashboard.json:0
msgid "Average Basic Wage"
msgstr "Vidutinis bazinis atlyginimas"

#. module: spreadsheet_dashboard_hr_payroll
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_sample_dashboard.json:0
msgid "Average Gross Wage"
msgstr ""

#. module: spreadsheet_dashboard_hr_payroll
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_sample_dashboard.json:0
msgid "Average Hours per Days of Work"
msgstr ""

#. module: spreadsheet_dashboard_hr_payroll
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_sample_dashboard.json:0
msgid "Average Net Wage"
msgstr "Vidutinis neto atlyginimas"

#. module: spreadsheet_dashboard_hr_payroll
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_sample_dashboard.json:0
msgid "Avg Hours/Days"
msgstr "Vid. val./d."

#. module: spreadsheet_dashboard_hr_payroll
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_dashboard.json:0
msgid "Companies"
msgstr "Įmonės"

#. module: spreadsheet_dashboard_hr_payroll
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_sample_dashboard.json:0
msgid "Current"
msgstr "Esamas"

#. module: spreadsheet_dashboard_hr_payroll
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_sample_dashboard.json:0
msgid "Days"
msgstr "Dienos"

#. module: spreadsheet_dashboard_hr_payroll
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_sample_dashboard.json:0
msgid "Employees"
msgstr "Darbuotojai"

#. module: spreadsheet_dashboard_hr_payroll
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_sample_dashboard.json:0
msgid "Employees (%)"
msgstr ""

#. module: spreadsheet_dashboard_hr_payroll
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_sample_dashboard.json:0
msgid "FTE"
msgstr ""

#. module: spreadsheet_dashboard_hr_payroll
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_sample_dashboard.json:0
msgid "FTE (%)"
msgstr ""

#. module: spreadsheet_dashboard_hr_payroll
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_dashboard.json:0
msgid "Job position"
msgstr ""

#. module: spreadsheet_dashboard_hr_payroll
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_sample_dashboard.json:0
msgid "Paid Time Off"
msgstr "Apmokamos Atostogos"

#. module: spreadsheet_dashboard_hr_payroll
#: model:spreadsheet.dashboard,name:spreadsheet_dashboard_hr_payroll.spreadsheet_dashboard_payroll
msgid "Payroll"
msgstr "Atlyginimai"

#. module: spreadsheet_dashboard_hr_payroll
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_sample_dashboard.json:0
msgid "Payroll Analysis"
msgstr "Darbo užmokesčio analizė"

#. module: spreadsheet_dashboard_hr_payroll
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_dashboard.json:0
msgid "Period"
msgstr "Laikotarpis"

#. module: spreadsheet_dashboard_hr_payroll
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_sample_dashboard.json:0
msgid "Previous"
msgstr "Ankstesnis"

#. module: spreadsheet_dashboard_hr_payroll
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_sample_dashboard.json:0
msgid "Total Basic Wage"
msgstr "Bendras pagrindinis atlyginimas"

#. module: spreadsheet_dashboard_hr_payroll
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_sample_dashboard.json:0
msgid "Total Gross Wage"
msgstr ""

#. module: spreadsheet_dashboard_hr_payroll
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_sample_dashboard.json:0
msgid "Total Net Wage"
msgstr "Bendras neto užmokestis"

#. module: spreadsheet_dashboard_hr_payroll
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_sample_dashboard.json:0
msgid "Unpaid TIme Off"
msgstr ""

#. module: spreadsheet_dashboard_hr_payroll
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_sample_dashboard.json:0
msgid "Work Days"
msgstr "Darbo dienos"

#. module: spreadsheet_dashboard_hr_payroll
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_sample_dashboard.json:0
msgid "Work Entries Analysis"
msgstr "Darbo įrašų analizė"

#. module: spreadsheet_dashboard_hr_payroll
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_sample_dashboard.json:0
msgid "Work Hours"
msgstr "Darbo valandos"

#. module: spreadsheet_dashboard_hr_payroll
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_sample_dashboard.json:0
msgid "Work type"
msgstr "Darbo tipas"
