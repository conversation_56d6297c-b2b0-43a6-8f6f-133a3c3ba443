# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* quality_repair
# 
# Translators:
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:25+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Arabic (https://app.transifex.com/odoo/teams/41243/ar/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ar\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#. module: quality_repair
#: model_terms:ir.ui.view,arch_db:quality_repair.view_repair_order_form_inherit_quality
msgid "--&gt;"
msgstr "--&gt;"

#. module: quality_repair
#: model_terms:ir.ui.view,arch_db:quality_repair.view_repair_order_form_inherit_quality
msgid "<span class=\"o_stat_text text-danger\">Quality Checks</span>"
msgstr "<span class=\"o_stat_text text-danger\">فحوصات الجودة</span> "

#. module: quality_repair
#: model_terms:ir.ui.view,arch_db:quality_repair.view_repair_order_form_inherit_quality
msgid "<span class=\"o_stat_text text-success\">Quality Checks</span>"
msgstr "<span class=\"o_stat_text text-success\">فحوصات الجودة</span> "

#. module: quality_repair
#: model_terms:ir.ui.view,arch_db:quality_repair.view_repair_order_form_inherit_quality
msgid "<span class=\"o_stat_text\">Quality Alerts</span>"
msgstr "<span class=\"o_stat_text\">تنبيهات الجودة</span> "

#. module: quality_repair
#: model_terms:ir.ui.view,arch_db:quality_repair.view_repair_order_form_inherit_quality
msgid "<span class=\"o_stat_text\">Quality Checks</span>"
msgstr "<span class=\"o_stat_text\">فحوصات الجودة</span> "

#. module: quality_repair
#: model:ir.model.fields,field_description:quality_repair.field_repair_order__quality_alert_ids
msgid "Alerts"
msgstr "التنبيهات "

#. module: quality_repair
#: model:ir.model.fields,field_description:quality_repair.field_repair_order__quality_check_ids
msgid "Checks"
msgstr "الفحوصات "

#. module: quality_repair
#: model:ir.model,name:quality_repair.model_quality_alert
#: model_terms:ir.ui.view,arch_db:quality_repair.view_repair_order_form_inherit_quality
msgid "Quality Alert"
msgstr "تنبيه الجودة"

#. module: quality_repair
#: model:ir.model.fields,field_description:quality_repair.field_repair_order__quality_alert_count
msgid "Quality Alert Count"
msgstr "عدد تنبيهات الجودة"

#. module: quality_repair
#: model:ir.model,name:quality_repair.model_quality_check
msgid "Quality Check"
msgstr "فحص الجودة"

#. module: quality_repair
#: model:ir.model.fields,field_description:quality_repair.field_repair_order__quality_check_fail
msgid "Quality Check Fail"
msgstr "فشل فحص الجودة"

#. module: quality_repair
#: model:ir.model.fields,field_description:quality_repair.field_repair_order__quality_check_todo
msgid "Quality Check Todo"
msgstr "فحص الجودة المُراد إجراؤه"

#. module: quality_repair
#: model_terms:ir.ui.view,arch_db:quality_repair.view_repair_order_form_inherit_quality
msgid "Quality Checks"
msgstr "فحوصات الجودة"

#. module: quality_repair
#: model:ir.model,name:quality_repair.model_quality_point
msgid "Quality Control Point"
msgstr "نقطة مراقبة الجودة"

#. module: quality_repair
#: model:ir.model,name:quality_repair.model_repair_order
#: model:ir.model.fields,field_description:quality_repair.field_quality_alert__repair_id
#: model:ir.model.fields,field_description:quality_repair.field_quality_check__repair_id
msgid "Repair Order"
msgstr "أمر الإصلاح "

#. module: quality_repair
#. odoo-python
#: code:addons/quality_repair/models/quality.py:0
msgid ""
"The Quantity quality check type is not possible with repair operation types."
msgstr "نوع فحص جودة الكمية غير ممكن مع أنواع عمليات الإصلاح. "

#. module: quality_repair
#. odoo-python
#: code:addons/quality_repair/models/repair.py:0
msgid "You still need to do the quality checks!"
msgstr "لا تزال بحاجة إلى القيام بفحوصات الجودة! "
