<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="view_invoice_form" model="ir.ui.view">
        <field name="model">account.move</field>
        <field name="name">account.move.edi.form</field>
        <field name="inherit_id" ref="account.view_move_form" />
        <field name="arch" type="xml">
            <xpath expr="//header/field[@name='state']" position="before">
                <field name="l10n_cl_dte_acceptation_status" invisible="1"/>
                <field name="l10n_cl_dte_status" invisible="1"/>
                <button name="l10n_cl_accept_document"
                        string="Accept Document"
                        type="object"
                        invisible="move_type in ['out_invoice', 'out_refund'] or state != 'posted' or l10n_cl_dte_acceptation_status in ['claimed', 'accepted', 'accepted_goods'] or not l10n_latam_use_documents or country_code != 'CL'"/>
                <button name="l10n_cl_receipt_service_or_merchandise"
                        string="Receipt RG 19983"
                        type="object"
                        invisible="move_type in ['out_invoice', 'out_refund'] or state != 'posted' or l10n_cl_dte_acceptation_status in ['claimed', 'goods', 'accepted_goods'] or not l10n_latam_use_documents or country_code != 'CL'"/>
                <button name="l10n_cl_claim_document"
                        string="Claim Document"
                        type="object"
                        invisible="move_type in ['out_invoice', 'out_refund'] or l10n_cl_dte_acceptation_status in ['claimed', 'accepted', 'goods', 'accepted_goods'] or not l10n_latam_use_documents or country_code != 'CL'"/>
            </xpath>
            
            <notebook>
                <page string="Cross Reference Documents" name="cross_reference_docs" invisible="move_type not in ['in_invoice', 'in_refund', 'out_invoice', 'out_refund'] or country_code != 'CL'">
                    <field name="l10n_cl_reference_ids" mode="list" readonly="state != 'draft'">
                        <form>
                            <group>
                                <field name="origin_doc_number" required="True"/>
                                <field name="l10n_cl_reference_doc_type_id" required="parent.move_type in ['out_invoice', 'out_refund']"/>
                                <field name="l10n_cl_reference_doc_internal_type" invisible="True"/>
                                <field name="reference_doc_code"/>
                                <field name="reason"/>
                                <field name="date"/>
                            </group>
                        </form>
                        <list>
                            <field name="origin_doc_number" required="True"/>
                            <field name="l10n_cl_reference_doc_type_id" required="parent.move_type in ['out_invoice', 'out_refund']"/>
                            <field name="reference_doc_code"/>
                            <field name="reason"/>
                            <field name="date"/>
                        </list>
                    </field>
                </page>
                <page string="Electronic Invoice" name="electronic_invoice" invisible="not l10n_cl_dte_status or country_code != 'CL'">
                    <group>
                        <field name="l10n_cl_dte_partner_status"/>
                        <field name="l10n_cl_sii_send_ident" readonly="state != 'draft'"/>
                    </group>
                </page>
            </notebook>
        </field>
    </record>

    <record id="invoice_status_form_cl" model="ir.ui.view">
        <field name="model">account.move</field>
        <field name="name">account.move.invoice.status.form</field>
        <field name="inherit_id" ref="l10n_latam_invoice_document.view_move_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='l10n_latam_document_number']" position="after">
                <label for="l10n_cl_dte_status" invisible="l10n_latam_manual_document_number or not l10n_latam_use_documents or country_code != 'CL'"/>
                <div invisible="not l10n_latam_use_documents or l10n_latam_manual_document_number or country_code != 'CL'">
                    <field name="l10n_cl_dte_status" invisible="l10n_latam_manual_document_number" readonly="True" class="oe_inline" />
                    <button name="l10n_cl_send_dte_to_sii" type="object" invisible="l10n_cl_dte_status != 'not_sent'" string="⇒ Send Now to SII" class="oe_inline oe_link" groups="base.group_user"/>
                    <button name="l10n_cl_verify_dte_status" type="object" invisible="l10n_cl_dte_status != 'ask_for_status'" context="{'send_immediately': True}" string="⇒ Verify on SII" class="oe_inline oe_link" groups="base.group_user"/>
                </div>
                <label for="l10n_cl_dte_acceptation_status" invisible="country_code != 'CL' or not l10n_cl_dte_acceptation_status"/>
                <div invisible="country_code != 'CL' or not l10n_cl_dte_acceptation_status">
                    <field name="l10n_cl_dte_acceptation_status" readonly="True" class="oe_online"/>
                    <button name="l10n_cl_reprocess_acknowledge" type="object" invisible="l10n_cl_dte_acceptation_status != 'received' or move_type not in ['in_invoice', 'in_refund']" string="⇒ Reprocess Acknowledge" class="oe_inline oe_link" groups="base.group_user"/>
                    <button name="l10n_cl_verify_claim_status" type="object" invisible="l10n_cl_dte_acceptation_status not in ['goods', 'accepted', 'claimed'] or move_type not in ['out_invoice', 'out_refund']" string="⇒ Verify on SII" class="oe_inline oe_link" groups="base.group_user"/>
                </div>
            </xpath>
        </field>
    </record>

</odoo>
