# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_cl_edi
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.3alpha1+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-05-01 01:02+0000\n"
"PO-Revision-Date: 2024-05-01 01:02+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_cl_edi
#: model:mail.template,body_html:l10n_cl_edi.email_template_receipt_ack
msgid ""
"\n"
"            \n"
"                <header>\n"
"                <strong>Electronic Invoice - Receipt Acknowledge of DTE Sending\n"
"                <t t-out=\"object.name or ''\"></t></strong>\n"
"                </header>\n"
"                <p>In the attached File you will find the result of the revision an validation process of a sent Tax Document made by you.</p>\n"
"                <br />\n"
"                <p>This is an automatic application, thus you should not answer this email or make comments to the origin email address..</p>\n"
"                <br /><br /><br /><br />\n"
"                <p>Sent Using Odoo</p>\n"
"            \n"
"            "
msgstr ""

#. module: l10n_cl_edi
#: model:mail.template,body_html:l10n_cl_edi.email_template_receipt_commercial_accept
msgid ""
"\n"
"            \n"
"                <header>\n"
"                <strong>Electronic Invoicing - Commercial Acceptance Response - <t t-out=\"object.display_name or ''\"></t></strong>\n"
"                </header>\n"
"                <p>In the attached file you will find the commercial acceptance of your Electronic Tax Document(s).</p>\n"
"                <br /><br /><br /><br />\n"
"                <p>Sent Using Odoo</p>\n"
"            \n"
"            "
msgstr ""

#. module: l10n_cl_edi
#: model:mail.template,body_html:l10n_cl_edi.email_template_claimed_ack
msgid ""
"\n"
"            \n"
"                <header>\n"
"                <strong>Electronic Invoicing - Commercial Rejection response <t t-out=\"object.name or ''\"></t></strong>\n"
"                </header>\n"
"                <p>In the attached file you will find the response for a commercial rejection of Electronic Tax Documents sent by you.</p>\n"
"                <br />\n"
"                <p>This is an automatic application, thus you should not answer this email or make comments to the origin email address..</p>\n"
"                <br /><br /><br /><br />\n"
"                <p>Sent Using Odoo</p>\n"
"            \n"
"            "
msgstr ""

#. module: l10n_cl_edi
#: model:mail.template,body_html:l10n_cl_edi.email_template_receipt_goods
msgid ""
"\n"
"            \n"
"                <header>\n"
"                <strong>Electronic Invoicing - Reception of Services or Goods RG 19.983 - <t t-out=\"object.display_name or ''\"></t></strong>\n"
"                </header>\n"
"                <p>In the attached file you will find the reception of goods RG 19.983 for Electronic Tax Document(s).</p>\n"
"                <p>El acuse de recibo que se declara en este acto, de acuerdo a lo dispuesto en la letra b)\n"
"                del Art. 4, y la letra c) del Art. 5 de la Ley 19.983, acredita que la entrega de\n"
"                mercaderias o servicio(s) prestado(s) ha(n) sido recibido(s).</p>\n"
"                <br /><br /><br /><br />\n"
"                <p>Sent Using Odoo</p>\n"
"            \n"
"            "
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields,help:l10n_cl_edi.field_account_bank_statement_line__l10n_cl_dte_partner_status
#: model:ir.model.fields,help:l10n_cl_edi.field_account_move__l10n_cl_dte_partner_status
#: model:ir.model.fields,help:l10n_cl_edi.field_account_payment__l10n_cl_dte_partner_status
msgid ""
"\n"
"    Status of sending the DTE to the partner:\n"
"    - Not sent: the DTE has not been sent to the partner but it has sent to SII.\n"
"    - Sent: The DTE has been sent to the partner."
msgstr ""

#. module: l10n_cl_edi
#. odoo-python
#: code:addons/l10n_cl_edi/models/account_move.py:0
msgid " -- Response simulated in Demo Mode"
msgstr ""

#. module: l10n_cl_edi
#. odoo-python
#: code:addons/l10n_cl_edi/models/account_move.py:0
msgid " in DEMO mode."
msgstr ""

#. module: l10n_cl_edi
#. odoo-python
#: code:addons/l10n_cl_edi/models/l10n_cl_edi_util.py:0
msgid "- It was not possible to get a response after %s retries."
msgstr ""

#. module: l10n_cl_edi
#: model_terms:ir.ui.view,arch_db:l10n_cl_edi.dte_subtemplate
msgid "01"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields,help:l10n_cl_edi.field_res_company__l10n_cl_sii_taxpayer_type
#: model:ir.model.fields,help:l10n_cl_edi.field_res_config_settings__l10n_cl_sii_taxpayer_type
msgid ""
"1 - VAT Affected (1st Category) (Most of the cases)\n"
"2 - Fees Receipt Issuer (Applies to suppliers who issue fees receipt)\n"
"3 - End consumer (only receipts)\n"
"4 - Foreigner"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields.selection,name:l10n_cl_edi.selection__account_debit_note__l10n_cl_edi_reference_doc_code__1
#: model:ir.model.fields.selection,name:l10n_cl_edi.selection__account_move_reversal__l10n_cl_edi_reference_doc_code__1
#: model:ir.model.fields.selection,name:l10n_cl_edi.selection__l10n_cl_account_invoice_reference__reference_doc_code__1
msgid "1. Cancels Referenced Document"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.actions.server,name:l10n_cl_edi.ir_cron_send_send_ir_actions_server
msgid "1. Cron Job - Send document to SII"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields.selection,name:l10n_cl_edi.selection__account_payment_term__l10n_cl_sii_code__1
msgid "1: Cash payment"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields.selection,name:l10n_cl_edi.selection__account_debit_note__l10n_cl_edi_reference_doc_code__2
#: model:ir.model.fields.selection,name:l10n_cl_edi.selection__account_move_reversal__l10n_cl_edi_reference_doc_code__2
#: model:ir.model.fields.selection,name:l10n_cl_edi.selection__l10n_cl_account_invoice_reference__reference_doc_code__2
msgid "2. Corrects Referenced Document Text"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.actions.server,name:l10n_cl_edi.ir_cron_sii_request_ir_actions_server
msgid "2. Cron Job - General Jobs with SII and electronic invoicing for Chile"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields.selection,name:l10n_cl_edi.selection__account_payment_term__l10n_cl_sii_code__2
msgid "2: Credit"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields.selection,name:l10n_cl_edi.selection__account_debit_note__l10n_cl_edi_reference_doc_code__3
#: model:ir.model.fields.selection,name:l10n_cl_edi.selection__account_move_reversal__l10n_cl_edi_reference_doc_code__3
#: model:ir.model.fields.selection,name:l10n_cl_edi.selection__l10n_cl_account_invoice_reference__reference_doc_code__3
msgid "3. Corrects Referenced Document Amount"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields.selection,name:l10n_cl_edi.selection__account_payment_term__l10n_cl_sii_code__3
msgid "3: Other"
msgstr ""

#. module: l10n_cl_edi
#. odoo-python
#: code:addons/l10n_cl_edi/models/account_move.py:0
msgid ""
"<br/><br/>If you are trying to test %s of documents, you should send this %s"
" as a vendor to %s before doing the test."
msgstr ""

#. module: l10n_cl_edi
#: model:mail.template,body_html:l10n_cl_edi.l10n_cl_edi_email_template_invoice
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        Dear\n"
"        <t t-if=\"object.commercial_partner_id\">\n"
"            <t t-out=\"object.partner_id.name or ''\">Deco Addict</t> (<t t-out=\"object.commercial_partner_id.name or ''\">Deco Addict</t>),\n"
"        </t>\n"
"         <t t-else=\"\">\n"
"            <t t-out=\"object.partner_id.name or ''\">Deco Addict</t>,\n"
"        </t>\n"
"        <br><br>\n"
"        Here is your\n"
"        <t t-if=\"object.name\">\n"
"            invoice <strong t-out=\"object.name or ''\">INV/2021/05/0004</strong>\n"
"        </t>\n"
"        <t t-else=\"\">\n"
"            invoice\n"
"        </t>\n"
"        <t t-if=\"object.invoice_origin\">\n"
"            (with reference: <t t-out=\"object.invoice_origin or ''\">S00056</t>)\n"
"        </t>\n"
"        with a total amount of <strong t-out=\"format_amount(object.amount_total, object.currency_id) or ''\">$ 377,825</strong>\n"
"        from <t t-out=\"object.company_id.name or ''\">YourCompany</t>.\n"
"        <t t-if=\"object.payment_state in ('paid', 'in_payment')\">\n"
"            This invoice is already paid.\n"
"        </t>\n"
"        <t t-else=\"\">\n"
"            Please remit payment at your earliest convenience.\n"
"        </t>\n"
"        <br><br>\n"
"        Do not hesitate to contact us if you have any questions.\n"
"    </p>\n"
"</div>\n"
"            "
msgstr ""

#. module: l10n_cl_edi
#. odoo-python
#: code:addons/l10n_cl_edi/models/fetchmail_server.py:0
msgid ""
"<li><b>Name</b>: %(name)s</li><li><b>RUT</b>: "
"%(vat)s</li><li><b>Address</b>: %(address)s</li>"
msgstr ""

#. module: l10n_cl_edi
#: model_terms:ir.ui.view,arch_db:l10n_cl_edi.barcode_stamp_footer
msgid "<span name=\"verification_url\">Verifique documento en www.sii.cl</span>"
msgstr ""

#. module: l10n_cl_edi
#: model_terms:ir.ui.view,arch_db:l10n_cl_edi.informations
msgid "<span>Date</span>"
msgstr ""

#. module: l10n_cl_edi
#: model_terms:ir.ui.view,arch_db:l10n_cl_edi.informations
msgid "<span>Doc Code</span>"
msgstr ""

#. module: l10n_cl_edi
#: model_terms:ir.ui.view,arch_db:l10n_cl_edi.informations
msgid "<span>Origin Ref</span>"
msgstr ""

#. module: l10n_cl_edi
#: model_terms:ir.ui.view,arch_db:l10n_cl_edi.informations
msgid "<span>Reason</span>"
msgstr ""

#. module: l10n_cl_edi
#: model_terms:ir.ui.view,arch_db:l10n_cl_edi.informations
msgid "<span>Reference Doc Type</span>"
msgstr ""

#. module: l10n_cl_edi
#. odoo-python
#: code:addons/l10n_cl_edi/models/account_move.py:0
msgid "<strong>WARNING: Simulating %s in Demo Mode</strong>"
msgstr ""

#. module: l10n_cl_edi
#. odoo-python
#: code:addons/l10n_cl_edi/models/fetchmail_server.py:0
msgid ""
"<strong>Warning:</strong> The total amount of the DTE's XML is %s and the "
"total amount calculated by Odoo is %s. Typically this is caused by "
"additional lines in the detail or by unidentified taxes, please check if a "
"manual correction is needed."
msgstr ""

#. module: l10n_cl_edi
#: model_terms:ir.ui.view,arch_db:l10n_cl_edi.res_config_settings_view_cl_form
msgid ""
"<strong>Warning:</strong> there is no shared digital signature for this company. You need to define at least one certificate without a user.\n"
"                                    Otherwise, you will need to send electronic invoices to the SII manually, and Odoo won't be able to send automatic receipt acknowledgements for vendor bills."
msgstr ""

#. module: l10n_cl_edi
#: model_terms:ir.ui.view,arch_db:l10n_cl_edi.certificate_form_view
msgid ""
"<strong>Warning:</strong> there is no shared digital signature for this company. You need to define at least one certificate without a user.\n"
"                            Otherwise, you will need to send electronic invoices to the SII manually, and Odoo won't be able to send automatic receipt acknowledgements for vendor bills."
msgstr ""

#. module: l10n_cl_edi
#: model_terms:ir.ui.view,arch_db:l10n_cl_edi.view_invoice_form
msgid "Accept Document"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields.selection,name:l10n_cl_edi.selection__account_move__l10n_cl_claim__acd
msgid "Accept the Content of the Document"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields.selection,name:l10n_cl_edi.selection__account_move__l10n_cl_dte_acceptation_status__accepted
#: model:ir.model.fields.selection,name:l10n_cl_edi.selection__account_move__l10n_cl_dte_status__accepted
msgid "Accepted"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields.selection,name:l10n_cl_edi.selection__account_move__l10n_cl_dte_status__objected
msgid "Accepted With Objections"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields.selection,name:l10n_cl_edi.selection__account_move__l10n_cl_dte_acceptation_status__accepted_goods
msgid "Accepted and RG 19983"
msgstr ""

#. module: l10n_cl_edi
#. odoo-python
#: code:addons/l10n_cl_edi/models/account_move.py:0
msgid "Accepted with objections"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model,name:l10n_cl_edi.model_account_move_reversal
msgid "Account Move Reversal"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields.selection,name:l10n_cl_edi.selection__account_move__l10n_cl_dte_acceptation_status__ack_sent
msgid "Acknowledge Sent"
msgstr ""

#. module: l10n_cl_edi
#: model:mail.template,subject:l10n_cl_edi.email_template_receipt_ack
msgid "Acknowledgment of Receipt - {{ object.name }}"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields,field_description:l10n_cl_edi.field_l10n_cl_company_activities__active
msgid "Active"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields,field_description:l10n_cl_edi.field_res_company__l10n_cl_company_activity_ids
#: model:ir.model.fields,field_description:l10n_cl_edi.field_res_config_settings__l10n_cl_company_activity_ids
msgid "Activities Names"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields,field_description:l10n_cl_edi.field_l10n_cl_company_activities__code
msgid "Activity Code"
msgstr ""

#. module: l10n_cl_edi
#: model_terms:ir.ui.view,arch_db:l10n_cl_edi.res_config_settings_view_cl_form
msgid "Activity Codes"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model,name:l10n_cl_edi.model_account_debit_note
msgid "Add Debit Note wizard"
msgstr ""

#. module: l10n_cl_edi
#. odoo-python
#: code:addons/l10n_cl_edi/models/account_move.py:0
#, python-format
msgid "Address"
msgstr ""

#. module: l10n_cl_edi
#. odoo-python
#: code:addons/l10n_cl_edi/models/account_move.py:0
#, python-format
msgid ""
"All the items you are billing in invoice %s - %s, have no taxes.\n"
" If you need to bill exempt items you must either use exempt invoice document type (34), or at least one of the items should have vat tax."
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields,help:l10n_cl_edi.field_l10n_cl_company_activities__active
msgid "Allows you to hide the activity without removing it."
msgstr ""

#. module: l10n_cl_edi
#. odoo-python
#: code:addons/l10n_cl_edi/models/account_move.py:0
msgid "An error occurred while processing this document."
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields,help:l10n_cl_edi.field_l10n_cl_account_invoice_reference__l10n_cl_reference_doc_internal_type
msgid ""
"Analog to odoo account.move.move_type but with more options allowing to "
"identify the kind of document we are working with. (not only related to "
"account.move, could be for documents of other models like stock.picking)"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields.selection,name:l10n_cl_edi.selection__res_company__l10n_cl_sii_regional_office__ur_anc
msgid "Ancud"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields.selection,name:l10n_cl_edi.selection__res_company__l10n_cl_sii_regional_office__ur_ang
msgid "Angol"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields.selection,name:l10n_cl_edi.selection__res_company__l10n_cl_sii_regional_office__ur_ant
msgid "Antofagasta"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields.selection,name:l10n_cl_edi.selection__res_company__l10n_cl_sii_regional_office__ur_ari
msgid "Arica y Parinacota"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields.selection,name:l10n_cl_edi.selection__account_move__l10n_cl_dte_status__ask_for_status
msgid "Ask For Status"
msgstr ""

#. module: l10n_cl_edi
#. odoo-python
#: code:addons/l10n_cl_edi/models/l10n_cl_edi_util.py:0
msgid "Ask for DTE status to SII failed due to:"
msgstr ""

#. module: l10n_cl_edi
#. odoo-python
#: code:addons/l10n_cl_edi/models/account_move.py:0
#: code:addons/l10n_cl_edi/models/account_move.py:0
msgid "Asking for DTE status with response:"
msgstr ""

#. module: l10n_cl_edi
#. odoo-python
#: code:addons/l10n_cl_edi/models/l10n_cl_edi_util.py:0
msgid "Asking for claim status failed due to:"
msgstr ""

#. module: l10n_cl_edi
#. odoo-python
#: code:addons/l10n_cl_edi/models/account_move.py:0
#: code:addons/l10n_cl_edi/models/account_move.py:0
msgid "Asking for claim status with response:"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model,name:l10n_cl_edi.model_ir_attachment
msgid "Attachment"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields.selection,name:l10n_cl_edi.selection__res_company__l10n_cl_sii_regional_office__ur_ays
msgid "Aysén"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields.selection,name:l10n_cl_edi.selection__res_company__l10n_cl_sii_regional_office__ur_buin
msgid "Buin"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields,help:l10n_cl_edi.field_fetchmail_server__l10n_cl_is_dte
msgid ""
"By checking this option, this email account will be used to receive the electronic\n"
"invoices from the suppliers, and communications from the SII regarding the electronic\n"
"invoices issued. In this case, this email should match both emails declared on the SII\n"
"site in the section: \"ACTUALIZACION DE DATOS DEL CONTRIBUYENTE\", \"Mail Contacto SII\"\n"
"and \"Mail Contacto Empresas\"."
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields,help:l10n_cl_edi.field_l10n_cl_dte_caf__final_nb
msgid "CAF Ends to this number"
msgstr ""

#. module: l10n_cl_edi
#: model_terms:ir.ui.view,arch_db:l10n_cl_edi.view_dte_caf_tree
msgid "CAF Files"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model,name:l10n_cl_edi.model_l10n_cl_dte_caf
msgid "CAF Files for chilean electronic invoicing"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields,help:l10n_cl_edi.field_l10n_cl_dte_caf__start_nb
msgid "CAF Starts from this number"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields,field_description:l10n_cl_edi.field_l10n_cl_dte_caf__caf_file
msgid "CAF XML File"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.actions.act_window,name:l10n_cl_edi.action_l10n_cl_dte_caf
#: model:ir.ui.menu,name:l10n_cl_edi.menu_l10n_cl_dte_caf
msgid "CAFs"
msgstr ""

#. module: l10n_cl_edi
#. odoo-python
#: code:addons/l10n_cl_edi/models/l10n_cl_dte_caf.py:0
msgid "Caf vat %s should be the same that assigned company's vat: %s!"
msgstr ""

#. module: l10n_cl_edi
#: model_terms:ir.ui.view,arch_db:l10n_cl_edi.l10n_cl_latam_document_type_view
msgid "Cafs"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields.selection,name:l10n_cl_edi.selection__res_company__l10n_cl_sii_regional_office__ur_cal
msgid "Calama"
msgstr ""

#. module: l10n_cl_edi
#: model_terms:ir.ui.view,arch_db:l10n_cl_edi.view_dte_caf_form
msgid "Cancel"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields.selection,name:l10n_cl_edi.selection__account_move__l10n_cl_dte_status__cancelled
msgid "Cancelled"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields.selection,name:l10n_cl_edi.selection__res_company__l10n_cl_sii_regional_office__ur_cas
msgid "Castro"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields.selection,name:l10n_cl_edi.selection__res_company__l10n_cl_sii_regional_office__ur_cau
msgid "Cauquenes"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields,field_description:l10n_cl_edi.field_l10n_cl_certificate__certificate
msgid "Certificate"
msgstr ""

#. module: l10n_cl_edi
#: model_terms:ir.ui.view,arch_db:l10n_cl_edi.certificate_form_view
msgid "Certificate Form"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields,field_description:l10n_cl_edi.field_l10n_cl_certificate__signature_key_file
msgid "Certificate Key"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields,help:l10n_cl_edi.field_l10n_cl_certificate__signature_key_file
msgid "Certificate Key in PFX format"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields,field_description:l10n_cl_edi.field_l10n_cl_certificate__user_id
msgid "Certificate Owner"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields,field_description:l10n_cl_edi.field_l10n_cl_certificate__signature_pass_phrase
msgid "Certificate Passkey"
msgstr ""

#. module: l10n_cl_edi
#: model_terms:ir.ui.view,arch_db:l10n_cl_edi.certificate_tree_view
msgid "Certificate Tree"
msgstr ""

#. module: l10n_cl_edi
#. odoo-python
#: code:addons/l10n_cl_edi/models/l10n_cl_edi_util.py:0
msgid "Certificate does not exist"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.actions.act_window,name:l10n_cl_edi.certificate_list_action
msgid "Certificates"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields,field_description:l10n_cl_edi.field_res_company__l10n_cl_certificate_ids
msgid "Certificates (CL)"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields.selection,name:l10n_cl_edi.selection__res_company__l10n_cl_sii_regional_office__ur_cha
msgid "Chaitén"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields.selection,name:l10n_cl_edi.selection__res_company__l10n_cl_sii_regional_office__ur_chn
msgid "Chañaral"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields.selection,name:l10n_cl_edi.selection__res_company__l10n_cl_sii_regional_office__ur_chc
msgid "Chile Chico"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields,help:l10n_cl_edi.field_res_config_settings__l10n_cl_activity_description
msgid "Chile: Economic activity."
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields,help:l10n_cl_edi.field_res_company__l10n_cl_dte_email
#: model:ir.model.fields,help:l10n_cl_edi.field_res_config_settings__l10n_cl_dte_email
#: model:ir.model.fields,help:l10n_cl_edi.field_res_partner__l10n_cl_dte_email
#: model:ir.model.fields,help:l10n_cl_edi.field_res_users__l10n_cl_dte_email
msgid "Chile: Email used to send and receive electronic documents."
msgstr ""

#. module: l10n_cl_edi
#: model_terms:ir.ui.view,arch_db:l10n_cl_edi.view_account_journal_form
msgid "Chilean Point of Sale Configuration"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.ui.menu,name:l10n_cl_edi.menu_sii_chile
msgid "Chilean SII"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields.selection,name:l10n_cl_edi.selection__res_company__l10n_cl_sii_regional_office__ur_chi
msgid "Chillán"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields,field_description:l10n_cl_edi.field_account_bank_statement_line__l10n_cl_claim
#: model:ir.model.fields,field_description:l10n_cl_edi.field_account_move__l10n_cl_claim
#: model:ir.model.fields,field_description:l10n_cl_edi.field_account_payment__l10n_cl_claim
msgid "Claim"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields,field_description:l10n_cl_edi.field_account_bank_statement_line__l10n_cl_claim_description
#: model:ir.model.fields,field_description:l10n_cl_edi.field_account_move__l10n_cl_claim_description
#: model:ir.model.fields,field_description:l10n_cl_edi.field_account_payment__l10n_cl_claim_description
msgid "Claim Detail"
msgstr ""

#. module: l10n_cl_edi
#: model_terms:ir.ui.view,arch_db:l10n_cl_edi.view_invoice_form
msgid "Claim Document"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields.selection,name:l10n_cl_edi.selection__account_move__l10n_cl_claim__rfp
msgid "Claim for Partial Lack of Merchandise"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields.selection,name:l10n_cl_edi.selection__account_move__l10n_cl_claim__rft
msgid "Claim for Total Lack of Merchandise"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields.selection,name:l10n_cl_edi.selection__account_move__l10n_cl_claim__nca
msgid "Reception of Cancellation that References Document"
msgstr ""

#. module: l10n_cl_edi
#. odoo-python
#: code:addons/l10n_cl_edi/models/account_move.py:0
#: code:addons/l10n_cl_edi/models/account_move.py:0
msgid "Claim status"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields.selection,name:l10n_cl_edi.selection__account_move__l10n_cl_claim__rcd
msgid "Claim the Content of the Document"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields.selection,name:l10n_cl_edi.selection__account_move__l10n_cl_dte_acceptation_status__claimed
msgid "Claimed"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields.selection,name:l10n_cl_edi.selection__res_company__l10n_cl_sii_regional_office__ur_coc
msgid "Cochrane"
msgstr ""

#. module: l10n_cl_edi
#: model:mail.template,subject:l10n_cl_edi.email_template_receipt_commercial_accept
msgid "Commercial Acceptance Response - {{ object.name }}"
msgstr ""

#. module: l10n_cl_edi
#: model:mail.template,subject:l10n_cl_edi.email_template_claimed_ack
msgid "Commercial Rejection response - {{ object.name }}"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model,name:l10n_cl_edi.model_res_company
msgid "Companies"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields,field_description:l10n_cl_edi.field_l10n_cl_certificate__company_id
#: model:ir.model.fields,field_description:l10n_cl_edi.field_l10n_cl_dte_caf__company_id
msgid "Company"
msgstr ""

#. module: l10n_cl_edi
#: model_terms:ir.ui.view,arch_db:l10n_cl_edi.view_company_l10n_cl_edi_form
msgid "Company Activity Codes"
msgstr ""

#. module: l10n_cl_edi
#. odoo-python
#: code:addons/l10n_cl_edi/models/account_move.py:0
msgid "Company Not Authorized to Send Files"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields,field_description:l10n_cl_edi.field_l10n_cl_company_activities__name
msgid "Complete Name"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields.selection,name:l10n_cl_edi.selection__res_company__l10n_cl_sii_regional_office__ur_cop
msgid "Concepción "
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model,name:l10n_cl_edi.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: l10n_cl_edi
#: model_terms:ir.ui.view,arch_db:l10n_cl_edi.res_config_settings_view_cl_form
msgid "Configure DTE Incoming Email"
msgstr ""

#. module: l10n_cl_edi
#: model_terms:ir.ui.view,arch_db:l10n_cl_edi.res_config_settings_view_cl_form
msgid "Configure Signature Certificates"
msgstr ""

#. module: l10n_cl_edi
#: model_terms:ir.ui.view,arch_db:l10n_cl_edi.res_config_settings_view_cl_form
msgid "Configure your signature certificates to sign SII DTEs"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields.selection,name:l10n_cl_edi.selection__res_company__l10n_cl_sii_regional_office__ur_cos
msgid "Constitución"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model,name:l10n_cl_edi.model_res_partner
msgid "Contact"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields.selection,name:l10n_cl_edi.selection__res_company__l10n_cl_sii_regional_office__ur_coo
msgid "Copiapo"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields.selection,name:l10n_cl_edi.selection__res_company__l10n_cl_sii_regional_office__ur_coq
msgid "Coquimbo"
msgstr ""

#. module: l10n_cl_edi
#. odoo-python
#: code:addons/l10n_cl_edi/models/fetchmail_server.py:0
#: code:addons/l10n_cl_edi/models/fetchmail_server.py:0
#: code:addons/l10n_cl_edi/models/fetchmail_server.py:0
msgid ""
"Couldn't get your emails. Check out the error message below for more info:\n"
"%s"

#. module: l10n_cl_edi
#. odoo-python
#: code:addons/l10n_cl_edi/models/account_move.py:0
msgid "Cover OK"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields.selection,name:l10n_cl_edi.selection__res_company__l10n_cl_sii_regional_office__ur_coy
msgid "Coyhaique"
msgstr ""

#. module: l10n_cl_edi
#: model_terms:ir.ui.view,arch_db:l10n_cl_edi.l10n_cl_latam_document_type_view
msgid "Create Demo CAF File"
msgstr ""

#. module: l10n_cl_edi
#: model_terms:ir.actions.act_window,help:l10n_cl_edi.certificate_list_action
msgid "Create the first certificate"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields,field_description:l10n_cl_edi.field_l10n_cl_account_invoice_reference__create_uid
#: model:ir.model.fields,field_description:l10n_cl_edi.field_l10n_cl_certificate__create_uid
#: model:ir.model.fields,field_description:l10n_cl_edi.field_l10n_cl_company_activities__create_uid
#: model:ir.model.fields,field_description:l10n_cl_edi.field_l10n_cl_dte_caf__create_uid
msgid "Created by"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields,field_description:l10n_cl_edi.field_l10n_cl_account_invoice_reference__create_date
#: model:ir.model.fields,field_description:l10n_cl_edi.field_l10n_cl_certificate__create_date
#: model:ir.model.fields,field_description:l10n_cl_edi.field_l10n_cl_company_activities__create_date
#: model:ir.model.fields,field_description:l10n_cl_edi.field_l10n_cl_dte_caf__create_date
msgid "Created on"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model,name:l10n_cl_edi.model_l10n_cl_account_invoice_reference
msgid "Cross Reference Docs for Chilean Electronic Invoicing"
msgstr ""

#. module: l10n_cl_edi
#: model_terms:ir.ui.view,arch_db:l10n_cl_edi.view_invoice_form
msgid "Cross Reference Documents"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields.selection,name:l10n_cl_edi.selection__res_company__l10n_cl_sii_regional_office__ur_cur
msgid "Curicó"
msgstr ""

#. module: l10n_cl_edi
#. odoo-python
#: code:addons/l10n_cl_edi/models/fetchmail_server.py:0
#, python-format
msgid "Draft move with id: %s has been filled from DTE %s"
msgstr ""

#. module: l10n_cl_edi
#: model:mail.template,name:l10n_cl_edi.l10n_cl_edi_email_template_invoice
msgid "DTE - Send by Email"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields,field_description:l10n_cl_edi.field_account_bank_statement_line__l10n_cl_dte_acceptation_status
#: model:ir.model.fields,field_description:l10n_cl_edi.field_account_move__l10n_cl_dte_acceptation_status
#: model:ir.model.fields,field_description:l10n_cl_edi.field_account_payment__l10n_cl_dte_acceptation_status
msgid "DTE Accept status"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields,field_description:l10n_cl_edi.field_l10n_latam_document_type__l10n_cl_dte_caf_ids
msgid "DTE Caf"
msgstr ""

#. module: l10n_cl_edi
#: model:mail.template,name:l10n_cl_edi.email_template_claimed_ack
msgid "DTE Commercial Reject"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields,field_description:l10n_cl_edi.field_res_company__l10n_cl_dte_email
#: model:ir.model.fields,field_description:l10n_cl_edi.field_res_config_settings__l10n_cl_dte_email
#: model:ir.model.fields,field_description:l10n_cl_edi.field_res_partner__l10n_cl_dte_email
#: model:ir.model.fields,field_description:l10n_cl_edi.field_res_users__l10n_cl_dte_email
msgid "DTE Email"
msgstr ""

#. module: l10n_cl_edi
#: model_terms:ir.ui.view,arch_db:l10n_cl_edi.ack_template
msgid "DTE Has been Successfully Received"
msgstr ""

#. module: l10n_cl_edi
#: model:mail.template,name:l10n_cl_edi.email_template_receipt_ack
msgid "DTE Receipt Acknowledgment"
msgstr ""

#. module: l10n_cl_edi
#: model:mail.template,name:l10n_cl_edi.email_template_receipt_commercial_accept
msgid "DTE Receipt Commercial Accepted"
msgstr ""

#. module: l10n_cl_edi
#: model:mail.template,name:l10n_cl_edi.email_template_receipt_goods
msgid "DTE Reception of Services Or Goods"
msgstr ""

#. module: l10n_cl_edi
#: model:mail.template,subject:l10n_cl_edi.email_template_receipt_goods
msgid "DTE Reception of Services Or Goods - {{ object.name }}"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields,field_description:l10n_cl_edi.field_account_payment_term__l10n_cl_sii_code
msgid "DTE SII Code"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields,field_description:l10n_cl_edi.field_res_company__l10n_cl_dte_service_provider
#: model:ir.model.fields,field_description:l10n_cl_edi.field_res_config_settings__l10n_cl_dte_service_provider
msgid "DTE Service Provider"
msgstr ""

#. module: l10n_cl_edi
#. odoo-python
#: code:addons/l10n_cl_edi/models/account_move.py:0
msgid "DTE attachment not found => %s"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields,field_description:l10n_cl_edi.field_account_bank_statement_line__l10n_cl_dte_file
#: model:ir.model.fields,field_description:l10n_cl_edi.field_account_move__l10n_cl_dte_file
#: model:ir.model.fields,field_description:l10n_cl_edi.field_account_payment__l10n_cl_dte_file
msgid "DTE file"
msgstr ""

#. module: l10n_cl_edi
#. odoo-python
#: code:addons/l10n_cl_edi/models/account_move.py:0
msgid "DTE has been created%s"
msgstr ""

#. module: l10n_cl_edi
#. odoo-python
#: code:addons/l10n_cl_edi/models/account_move.py:0
msgid "DTE has been sent to SII with response: %s"
msgstr ""

#. module: l10n_cl_edi
#. odoo-python
#: code:addons/l10n_cl_edi/models/account_move.py:0
msgid "DTE has been sent to SII with response: %s."
msgstr ""

#. module: l10n_cl_edi
#. odoo-python
#: code:addons/l10n_cl_edi/models/account_move.py:0
msgid "DTE has been sent to the partner"
msgstr ""

#. module: l10n_cl_edi
#. odoo-python
#: code:addons/l10n_cl_edi/models/fetchmail_server.py:0
msgid "DTE reception status established as <b>%s</b> by incoming email"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields,field_description:l10n_cl_edi.field_fetchmail_server__l10n_cl_is_dte
msgid "DTE server"
msgstr ""

#. module: l10n_cl_edi
#: model_terms:ir.ui.view,arch_db:l10n_cl_edi.res_config_settings_view_cl_form
msgid ""
"Define the tax payer type and SII regional office for your company. This is "
"mandatory for electronic invoicing."
msgstr ""

#. module: l10n_cl_edi
#: model_terms:ir.ui.view,arch_db:l10n_cl_edi.ack_template
msgid "Delivery Received According"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields,field_description:l10n_cl_edi.field_l10n_cl_account_invoice_reference__display_name
#: model:ir.model.fields,field_description:l10n_cl_edi.field_l10n_cl_certificate__display_name
#: model:ir.model.fields,field_description:l10n_cl_edi.field_l10n_cl_company_activities__display_name
#: model:ir.model.fields,field_description:l10n_cl_edi.field_l10n_cl_dte_caf__display_name
msgid "Display Name"
msgstr ""

#. module: l10n_cl_edi
#. odoo-python
#: code:addons/l10n_cl_edi/models/account_move.py:0
msgid "Document %s failed with the following response:"
msgstr ""

#. module: l10n_cl_edi
#. odoo-python
#: code:addons/l10n_cl_edi/models/account_move.py:0
msgid "Document %s was accepted with the following response:"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields,field_description:l10n_cl_edi.field_l10n_cl_account_invoice_reference__date
msgid "Document Date"
msgstr ""

#. module: l10n_cl_edi
#. odoo-python
#: code:addons/l10n_cl_edi/models/account_move.py:0
msgid "Document Signature"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields,field_description:l10n_cl_edi.field_l10n_cl_dte_caf__l10n_latam_document_type_id
msgid "Document Type"
msgstr ""

#. module: l10n_cl_edi
#. odoo-python
#: code:addons/l10n_cl_edi/models/l10n_cl_edi_util.py:0
msgid "Document acceptance or claim failed due to:"
msgstr ""

#. module: l10n_cl_edi
#: model_terms:ir.ui.view,arch_db:l10n_cl_edi.res_config_settings_view_cl_form
msgid "Economical Activities Information"
msgstr ""

#. module: l10n_cl_edi
#: model_terms:ir.ui.view,arch_db:l10n_cl_edi.view_invoice_form
msgid "Electronic Invoice"
msgstr ""

#. module: l10n_cl_edi
#: model_terms:ir.ui.view,arch_db:l10n_cl_edi.view_company_l10n_cl_edi_form
#: model_terms:ir.ui.view,arch_db:l10n_cl_edi.view_partner_l10n_cl_edi_form
msgid "Electronic Invoicing"
msgstr ""

#. module: l10n_cl_edi
#: model_terms:ir.ui.view,arch_db:l10n_cl_edi.res_config_settings_view_cl_form
msgid "Email Alias"
msgstr ""

#. module: l10n_cl_edi
#: model_terms:ir.ui.view,arch_db:l10n_cl_edi.res_config_settings_view_cl_form
msgid "Email Alias Electronic Invoicing"
msgstr ""

#. module: l10n_cl_edi
#: model_terms:ir.ui.view,arch_db:l10n_cl_edi.res_config_settings_view_cl_form
msgid "Email Box Electronic Invoicing"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields,field_description:l10n_cl_edi.field_l10n_cl_dte_caf__final_nb
msgid "End Number"
msgstr ""

#. module: l10n_cl_edi
#. odoo-python
#: code:addons/l10n_cl_edi/models/l10n_cl_edi_util.py:0
msgid "Error Get Seed: (Message Exception)"
msgstr ""

#. module: l10n_cl_edi
#. odoo-python
#: code:addons/l10n_cl_edi/models/l10n_cl_edi_util.py:0
msgid "Error Get Seed: Retorno"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.constraint,message:l10n_cl_edi.constraint_l10n_cl_dte_caf_filename_unique
msgid "Error! Filename Already Exist!"
msgstr ""

#. module: l10n_cl_edi
#. odoo-python
#: code:addons/l10n_cl_edi/models/account_move.py:0
msgid "Exception error parsing the response: %s"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields,field_description:l10n_cl_edi.field_l10n_cl_certificate__cert_expiration
msgid "Expiration date"
msgstr ""

#. module: l10n_cl_edi
#. odoo-python
#: code:addons/l10n_cl_edi/models/fetchmail_server.py:0
#, python-format
msgid "Failed to determine origin type of the attached document, attempting to process as a vendor bill"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields,field_description:l10n_cl_edi.field_l10n_cl_dte_caf__filename
msgid "File Name"
msgstr ""

#. module: l10n_cl_edi
#. odoo-python
#: code:addons/l10n_cl_edi/models/account_move.py:0
msgid "File Size Error (Too Big or Too Small)"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields,field_description:l10n_cl_edi.field_res_config_settings__l10n_cl_activity_description
msgid "Glosa Giro"
msgstr ""

#. module: l10n_cl_edi
#: model_terms:ir.ui.view,arch_db:l10n_cl_edi.res_config_settings_view_cl_form
msgid ""
"Hint: \"Factura Electrónica/Sistema de Certificación de Mercado/Actualizar "
"datos de la empresa autorizada\"."
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields,field_description:l10n_cl_edi.field_l10n_cl_account_invoice_reference__id
#: model:ir.model.fields,field_description:l10n_cl_edi.field_l10n_cl_certificate__id
#: model:ir.model.fields,field_description:l10n_cl_edi.field_l10n_cl_company_activities__id
#: model:ir.model.fields,field_description:l10n_cl_edi.field_l10n_cl_dte_caf__id
msgid "ID"
msgstr ""

#. module: l10n_cl_edi
#: model_terms:ir.ui.view,arch_db:l10n_cl_edi.dte_subtemplate
msgid "INT1"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields,help:l10n_cl_edi.field_l10n_cl_certificate__user_id
msgid ""
"If this certificate has an owner, he will be the only user authorized to use"
" it, otherwise, the certificate will be shared with other users of the "
"current company"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields,help:l10n_cl_edi.field_l10n_cl_company_activities__tax_category
msgid ""
"If your company is 2nd category tax payer type, you should use activity "
"codes of type 2, otherwise they should be type 1. If your activity is "
"affected by vat tax depending on other situations, SII uses type ND. In "
"every cases the tax category is defined by the CIIU4.CL nomenclature adopted"
" by SII, and you should only add new activities in case they are added in "
"the future."
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields.selection,name:l10n_cl_edi.selection__res_company__l10n_cl_sii_regional_office__ur_ill
msgid "Illapel"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields.selection,name:l10n_cl_edi.selection__l10n_cl_dte_caf__status__in_use
msgid "In Use"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields,help:l10n_cl_edi.field_l10n_cl_dte_caf__status
msgid ""
"In Use: means that the CAF file is being used. Spent: means that the number "
"interval has been exhausted."
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model,name:l10n_cl_edi.model_fetchmail_server
msgid "Incoming Mail Server"
msgstr ""

#. module: l10n_cl_edi
#. odoo-python
#: code:addons/l10n_cl_edi/models/fetchmail_server.py:0
msgid ""
"Incoming SII DTE result:<br/> <li><b>ESTADO</b>: "
"%s</li><li><b>REVISIONDTE/ESTADO</b>: %s</li><li><b>REVISIONDTE/DETALLE</b>:"
" %s</li>"
msgstr ""

#. module: l10n_cl_edi
#. odoo-python
#: code:addons/l10n_cl_edi/models/fetchmail_server.py:0
msgid "Incoming SII DTE result:<br/><li><b>ESTADO</b>: %s</li>"
msgstr ""

#. module: l10n_cl_edi
#. odoo-python
#: code:addons/l10n_cl_edi/models/account_move.py:0
msgid "Incomplete File (Size <> Parameter size)"
msgstr ""

#. module: l10n_cl_edi
#. odoo-python
#: code:addons/l10n_cl_edi/models/account_move.py:0
msgid "Internal Error"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields,field_description:l10n_cl_edi.field_l10n_cl_account_invoice_reference__l10n_cl_reference_doc_internal_type
msgid "Internal Type"
msgstr ""

#. module: l10n_cl_edi
#. odoo-python
#: code:addons/l10n_cl_edi/models/account_move.py:0
msgid "Invalid Schema"
msgstr ""

#. module: l10n_cl_edi
#. odoo-python
#: code:addons/l10n_cl_edi/models/account_move.py:0
msgid ""
"Invoice %s has the currency %s inactive. Please activate the currency and "
"try again."
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields.selection,name:l10n_cl_edi.selection__res_company__l10n_cl_sii_regional_office__ur_iqu
msgid "Iquique"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields,field_description:l10n_cl_edi.field_l10n_cl_certificate__l10n_cl_is_there_shared_certificate
#: model:ir.model.fields,field_description:l10n_cl_edi.field_res_company__l10n_cl_is_there_shared_certificate
#: model:ir.model.fields,field_description:l10n_cl_edi.field_res_config_settings__l10n_cl_is_there_shared_certificate
msgid "Is There Shared Certificate?"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields,field_description:l10n_cl_edi.field_l10n_cl_dte_caf__issued_date
msgid "Issued Date"
msgstr ""

#. module: l10n_cl_edi
#. odoo-python
#: code:addons/l10n_cl_edi/models/account_move.py:0
msgid "It is not allowed to create receipts in a different currency than CLP"
msgstr ""

#. module: l10n_cl_edi
#. odoo-python
#: code:addons/l10n_cl_edi/models/account_move.py:0
msgid ""
"It seems that you are using items with taxes in exempt documents in invoice %s - %s. You must either:\n"
"   - Change the document type to a not exempt type.\n"
"   - Set an exempt fiscal position to remove taxes automatically.\n"
"   - Use products without taxes.\n"
"   - Remove taxes from product lines."
msgstr ""

#. module: l10n_cl_edi
#. odoo-python
#: code:addons/l10n_cl_edi/models/l10n_cl_dte_caf.py:0
msgid "It's not a valid XML caf file"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model,name:l10n_cl_edi.model_account_journal
msgid "Journal"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model,name:l10n_cl_edi.model_account_move
msgid "Journal Entry"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model,name:l10n_cl_edi.model_account_move_line
msgid "Journal Item"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields,field_description:l10n_cl_edi.field_l10n_latam_document_type__l10n_cl_show_caf_button
msgid "L10N Cl Show Caf Button"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields.selection,name:l10n_cl_edi.selection__res_company__l10n_cl_sii_regional_office__ur_laf
msgid "La Florida"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields.selection,name:l10n_cl_edi.selection__res_company__l10n_cl_sii_regional_office__ur_lal
msgid "La Ligua"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields.selection,name:l10n_cl_edi.selection__res_company__l10n_cl_sii_regional_office__ur_las
msgid "La Serena"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields.selection,name:l10n_cl_edi.selection__res_company__l10n_cl_sii_regional_office__ur_lau
msgid "La Unión"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields.selection,name:l10n_cl_edi.selection__res_company__l10n_cl_sii_regional_office__ur_lan
msgid "Lanco"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields,field_description:l10n_cl_edi.field_l10n_cl_certificate__last_rest_token
msgid "Last REST Token"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields,field_description:l10n_cl_edi.field_l10n_cl_certificate__last_token
msgid "Last Token"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields,field_description:l10n_cl_edi.field_l10n_cl_account_invoice_reference__write_uid
#: model:ir.model.fields,field_description:l10n_cl_edi.field_l10n_cl_certificate__write_uid
#: model:ir.model.fields,field_description:l10n_cl_edi.field_l10n_cl_company_activities__write_uid
#: model:ir.model.fields,field_description:l10n_cl_edi.field_l10n_cl_dte_caf__write_uid
msgid "Last Updated by"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields,field_description:l10n_cl_edi.field_l10n_cl_account_invoice_reference__write_date
#: model:ir.model.fields,field_description:l10n_cl_edi.field_l10n_cl_certificate__write_date
#: model:ir.model.fields,field_description:l10n_cl_edi.field_l10n_cl_company_activities__write_date
#: model:ir.model.fields,field_description:l10n_cl_edi.field_l10n_cl_dte_caf__write_date
msgid "Last Updated on"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields,field_description:l10n_cl_edi.field_fetchmail_server__l10n_cl_last_uid
msgid "Last read message ID (CL)"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model,name:l10n_cl_edi.model_l10n_latam_document_type
msgid "Latam Document Type"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields.selection,name:l10n_cl_edi.selection__res_company__l10n_cl_sii_regional_office__ur_leb
msgid "Lebu"
msgstr ""

#. module: l10n_cl_edi
#: model_terms:ir.ui.view,arch_db:l10n_cl_edi.res_config_settings_view_cl_form
msgid "Legal Electronic Invoicing Data"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields.selection,name:l10n_cl_edi.selection__res_company__l10n_cl_sii_regional_office__ur_lin
msgid "Linares"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields.selection,name:l10n_cl_edi.selection__res_company__l10n_cl_sii_regional_office__ur_lod
msgid "Los Andes"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields.selection,name:l10n_cl_edi.selection__res_company__l10n_cl_sii_regional_office__ur_losrios
msgid "Los Ríos"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields.selection,name:l10n_cl_edi.selection__res_company__l10n_cl_sii_regional_office__ur_log
msgid "Los Ángeles"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields.selection,name:l10n_cl_edi.selection__res_company__l10n_cl_sii_regional_office__ur_maipu
msgid "Maipu"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields.selection,name:l10n_cl_edi.selection__account_journal__l10n_cl_point_of_sale_type__manual
#: model:ir.model.fields.selection,name:l10n_cl_edi.selection__account_move__l10n_cl_dte_status__manual
msgid "Manual"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields.selection,name:l10n_cl_edi.selection__res_company__l10n_cl_sii_regional_office__ur_melipilla
msgid "Melipilla"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields.selection,name:l10n_cl_edi.selection__l10n_cl_company_activities__tax_category__nd
msgid "ND"
msgstr ""

#. module: l10n_cl_edi
#. odoo-python
#: code:addons/l10n_cl_edi/models/account_move.py:0
#, python-format
msgid "Name"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields,field_description:l10n_cl_edi.field_account_debit_note__l10n_cl_corrected_text
#: model:ir.model.fields,field_description:l10n_cl_edi.field_account_move_reversal__l10n_cl_corrected_text
msgid "New Corrected Text"
msgstr ""

#. module: l10n_cl_edi
#. odoo-python
#: code:addons/l10n_cl_edi/models/l10n_cl_edi_util.py:0
msgid "No possible to get a seed"
msgstr ""

#. module: l10n_cl_edi
#. odoo-python
#: code:addons/l10n_cl_edi/models/l10n_cl_edi_util.py:0
#: code:addons/l10n_cl_edi/models/l10n_cl_edi_util.py:0
msgid "No response trying to get a token"
msgstr ""

#. module: l10n_cl_edi
#. odoo-python
#: code:addons/l10n_cl_edi/models/account_move.py:0
msgid "Not Authenticated"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields.selection,name:l10n_cl_edi.selection__account_move__l10n_cl_dte_partner_status__not_sent
msgid "Not Sent"
msgstr ""

#. module: l10n_cl_edi
#. odoo-python
#: code:addons/l10n_cl_edi/models/l10n_cl_edi_util.py:0
msgid "Not possible to get a seed"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields.selection,name:l10n_cl_edi.selection__account_journal__l10n_cl_point_of_sale_type__online
msgid "Online"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields,field_description:l10n_cl_edi.field_account_move_reversal__l10n_cl_is_text_correction
msgid "Only Text Correction"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields,field_description:l10n_cl_edi.field_l10n_cl_account_invoice_reference__origin_doc_number
msgid "Origin Document Number"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields,help:l10n_cl_edi.field_l10n_cl_account_invoice_reference__origin_doc_number
msgid "Origin document number, the document you are referring to"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields,field_description:l10n_cl_edi.field_account_debit_note__l10n_cl_original_text
#: model:ir.model.fields,field_description:l10n_cl_edi.field_account_move_reversal__l10n_cl_original_text
msgid "Original Text"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields,field_description:l10n_cl_edi.field_l10n_cl_account_invoice_reference__move_id
msgid "Originating Document"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields.selection,name:l10n_cl_edi.selection__res_company__l10n_cl_sii_regional_office__ur_oso
msgid "Osorno"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields.selection,name:l10n_cl_edi.selection__res_company__l10n_cl_sii_regional_office__ur_ova
msgid "Ovalle"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields.selection,name:l10n_cl_edi.selection__res_company__l10n_cl_sii_regional_office__ur_pan
msgid "Panguipulli"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields.selection,name:l10n_cl_edi.selection__res_company__l10n_cl_sii_regional_office__ur_par
msgid "Parral"
msgstr ""

#. module: l10n_cl_edi
#. odoo-python
#: code:addons/l10n_cl_edi/models/account_move.py:0
msgid "Partner DTE has been generated"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields,field_description:l10n_cl_edi.field_account_bank_statement_line__l10n_cl_dte_partner_status
#: model:ir.model.fields,field_description:l10n_cl_edi.field_account_move__l10n_cl_dte_partner_status
#: model:ir.model.fields,field_description:l10n_cl_edi.field_account_payment__l10n_cl_dte_partner_status
msgid "Partner DTE status"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields,help:l10n_cl_edi.field_l10n_cl_certificate__signature_pass_phrase
msgid "Passphrase for the certificate key"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model,name:l10n_cl_edi.model_account_payment_term
msgid "Payment Terms"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields.selection,name:l10n_cl_edi.selection__account_move__l10n_cl_dte_status__not_sent
msgid "Pending To Be Sent"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields.selection,name:l10n_cl_edi.selection__res_company__l10n_cl_sii_regional_office__ur_pic
msgid "Pichilemu"
msgstr ""

#. module: l10n_cl_edi
#. odoo-python
#: code:addons/l10n_cl_edi/models/account_move.py:0
msgid "Please assign a partner before sending the acknowledgement"
msgstr ""

#. module: l10n_cl_edi
#. odoo-python
#: code:addons/l10n_cl_edi/models/account_move.py:0
msgid "Please check the document number"
msgstr ""

#. module: l10n_cl_edi
#: model_terms:ir.ui.view,arch_db:l10n_cl_edi.res_config_settings_view_cl_form
msgid ""
"Please choose your DTE service provider. If possible, avoid selecting SII "
"Demo mode in a production database."
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields,help:l10n_cl_edi.field_res_company__l10n_cl_company_activity_ids
#: model:ir.model.fields,help:l10n_cl_edi.field_res_config_settings__l10n_cl_company_activity_ids
msgid ""
"Please select the SII registered economic activities codes for the company"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields,help:l10n_cl_edi.field_res_company__l10n_cl_dte_service_provider
#: model:ir.model.fields,help:l10n_cl_edi.field_res_config_settings__l10n_cl_dte_service_provider
msgid "Please select your company service provider for DTE service."
msgstr ""

#. module: l10n_cl_edi
#. odoo-python
#: code:addons/l10n_cl_edi/models/account_move.py:0
msgid "Please set the subject serial number in the certificate: %s"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields,field_description:l10n_cl_edi.field_account_journal__l10n_cl_point_of_sale_name
msgid "Point Of Sale Name"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields,field_description:l10n_cl_edi.field_account_journal__l10n_cl_point_of_sale_number
msgid "Point Of Sale Number"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields,field_description:l10n_cl_edi.field_account_bank_statement_line__l10n_cl_journal_point_of_sale_type
#: model:ir.model.fields,field_description:l10n_cl_edi.field_account_journal__l10n_cl_point_of_sale_type
#: model:ir.model.fields,field_description:l10n_cl_edi.field_account_move__l10n_cl_journal_point_of_sale_type
#: model:ir.model.fields,field_description:l10n_cl_edi.field_account_payment__l10n_cl_journal_point_of_sale_type
msgid "Point Of Sale Type"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields.selection,name:l10n_cl_edi.selection__res_company__l10n_cl_sii_regional_office__ur_por
msgid "Porvenir"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields,field_description:l10n_cl_edi.field_l10n_cl_certificate__private_key
msgid "Private Key"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields.selection,name:l10n_cl_edi.selection__account_move__l10n_cl_claim__erm
msgid "Provide Receipt of Merchandise or Services"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields.selection,name:l10n_cl_edi.selection__res_company__l10n_cl_sii_regional_office__ur_pum
msgid "Puerto Montt"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields.selection,name:l10n_cl_edi.selection__res_company__l10n_cl_sii_regional_office__ur_pun
msgid "Puerto Natales"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields.selection,name:l10n_cl_edi.selection__res_company__l10n_cl_sii_regional_office__ur_puv
msgid "Puerto Varas"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields.selection,name:l10n_cl_edi.selection__res_company__l10n_cl_sii_regional_office__ur_pua
msgid "Punta Arenas"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields.selection,name:l10n_cl_edi.selection__res_company__l10n_cl_sii_regional_office__ur_qui
msgid "Quillota"
msgstr ""

#. module: l10n_cl_edi
#. odoo-python
#: code:addons/l10n_cl_edi/models/account_move.py:0
#, python-format
msgid "RUT"
msgstr ""

#. module: l10n_cl_edi
#. odoo-python
#: code:addons/l10n_cl_edi/models/l10n_cl_edi_util.py:0
#: code:addons/l10n_cl_edi/models/l10n_cl_edi_util.py:0
msgid "RUT validation error"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields.selection,name:l10n_cl_edi.selection__res_company__l10n_cl_sii_regional_office__ur_ran
msgid "Rancagua"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields,field_description:l10n_cl_edi.field_l10n_cl_account_invoice_reference__reason
msgid "Reason"
msgstr ""

#. module: l10n_cl_edi
#: model_terms:ir.ui.view,arch_db:l10n_cl_edi.view_invoice_form
msgid "Receipt RG 19983"
msgstr ""

#. module: l10n_cl_edi
#. odoo-python
#: code:addons/l10n_cl_edi/models/account_move.py:0
msgid "Receipts with withholding taxes are not allowed"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields.selection,name:l10n_cl_edi.selection__account_move__l10n_cl_dte_acceptation_status__received
msgid "Received"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields.selection,name:l10n_cl_edi.selection__account_move__l10n_cl_dte_acceptation_status__goods
msgid "Reception 19983"
msgstr ""

#. module: l10n_cl_edi
#. odoo-python
#: code:addons/l10n_cl_edi/models/account_move.py:0
msgid "Reception law 19983"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields,field_description:l10n_cl_edi.field_account_bank_statement_line__l10n_cl_reference_ids
#: model:ir.model.fields,field_description:l10n_cl_edi.field_account_move__l10n_cl_reference_ids
#: model:ir.model.fields,field_description:l10n_cl_edi.field_account_payment__l10n_cl_reference_ids
msgid "Reference Records"
msgstr ""

#. module: l10n_cl_edi
#: model_terms:ir.ui.view,arch_db:l10n_cl_edi.res_config_settings_view_cl_form
msgid ""
"Register up to four economical activity codes and description for your "
"company. This is mandatory for electronic invoicing."
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields.selection,name:l10n_cl_edi.selection__account_move__l10n_cl_dte_status__rejected
msgid "Rejected"
msgstr ""

#. module: l10n_cl_edi
#. odoo-python
#: code:addons/l10n_cl_edi/models/account_move.py:0
msgid "Rejected by error in covert"
msgstr ""

#. module: l10n_cl_edi
#. odoo-python
#: code:addons/l10n_cl_edi/models/account_move.py:0
msgid "Rejected by schema"
msgstr ""

#. module: l10n_cl_edi
#. odoo-python
#: code:addons/l10n_cl_edi/models/account_move.py:0
msgid "Rejected due to error in signature"
msgstr ""

#. module: l10n_cl_edi
#. odoo-python
#: code:addons/l10n_cl_edi/models/account_move.py:0
msgid "Rejected due to information errors"
msgstr ""

#. module: l10n_cl_edi
#. odoo-python
#: code:addons/l10n_cl_edi/models/account_move.py:0
msgid "Rejected for consistency"
msgstr ""

#. module: l10n_cl_edi
#. odoo-python
#: code:addons/l10n_cl_edi/models/account_move.py:0
msgid "Repeat submission rejected"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields.selection,name:l10n_cl_edi.selection__res_company__l10n_cl_dte_service_provider__siidemo
msgid "SII - Demo Mode"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields.selection,name:l10n_cl_edi.selection__res_company__l10n_cl_dte_service_provider__sii
msgid "SII - Production"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields.selection,name:l10n_cl_edi.selection__res_company__l10n_cl_dte_service_provider__siitest
msgid "SII - Test"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields,field_description:l10n_cl_edi.field_account_bank_statement_line__l10n_cl_sii_barcode
#: model:ir.model.fields,field_description:l10n_cl_edi.field_account_move__l10n_cl_sii_barcode
#: model:ir.model.fields,field_description:l10n_cl_edi.field_account_payment__l10n_cl_sii_barcode
msgid "SII Barcode"
msgstr ""

#. module: l10n_cl_edi
#: model_terms:ir.ui.view,arch_db:l10n_cl_edi.view_dte_caf_form
msgid "SII CAF Files for DTE"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model,name:l10n_cl_edi.model_l10n_cl_company_activities
msgid "SII Company Economical Activities"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields,field_description:l10n_cl_edi.field_account_bank_statement_line__l10n_cl_dte_status
#: model:ir.model.fields,field_description:l10n_cl_edi.field_account_move__l10n_cl_dte_status
#: model:ir.model.fields,field_description:l10n_cl_edi.field_account_payment__l10n_cl_dte_status
msgid "SII DTE status"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model,name:l10n_cl_edi.model_l10n_cl_certificate
msgid "SII Digital Signature"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields,field_description:l10n_cl_edi.field_l10n_cl_account_invoice_reference__l10n_cl_reference_doc_type_id
msgid "SII Doc Type Selector"
msgstr ""

#. module: l10n_cl_edi
#: model_terms:ir.ui.view,arch_db:l10n_cl_edi.view_partner_activities_form
#: model_terms:ir.ui.view,arch_db:l10n_cl_edi.view_partner_activities_tree
msgid "SII Economic Activities"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields,field_description:l10n_cl_edi.field_res_company__l10n_cl_dte_resolution_date
#: model:ir.model.fields,field_description:l10n_cl_edi.field_res_config_settings__l10n_cl_dte_resolution_date
msgid "SII Exempt Resolution Date"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields,field_description:l10n_cl_edi.field_res_company__l10n_cl_dte_resolution_number
#: model:ir.model.fields,field_description:l10n_cl_edi.field_res_config_settings__l10n_cl_dte_resolution_number
msgid "SII Exempt Resolution Number"
msgstr ""

#. module: l10n_cl_edi
#: model_terms:ir.ui.view,arch_db:l10n_cl_edi.res_config_settings_view_cl_form
msgid "SII Office"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.actions.act_window,name:l10n_cl_edi.act_partner_activities
#: model:ir.ui.menu,name:l10n_cl_edi.menu_action_act_partner_activities
msgid "SII Partner Activities"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields,field_description:l10n_cl_edi.field_account_debit_note__l10n_cl_edi_reference_doc_code
#: model:ir.model.fields,field_description:l10n_cl_edi.field_account_move_reversal__l10n_cl_edi_reference_doc_code
#: model:ir.model.fields,field_description:l10n_cl_edi.field_l10n_cl_account_invoice_reference__reference_doc_code
msgid "SII Reference Code"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields,field_description:l10n_cl_edi.field_res_company__l10n_cl_sii_regional_office
#: model:ir.model.fields,field_description:l10n_cl_edi.field_res_config_settings__l10n_cl_sii_regional_office
msgid "SII Regional Office"
msgstr ""

#. module: l10n_cl_edi
#: model_terms:ir.ui.view,arch_db:l10n_cl_edi.res_config_settings_view_cl_form
msgid "SII Resolution Date"
msgstr ""

#. module: l10n_cl_edi
#: model_terms:ir.ui.view,arch_db:l10n_cl_edi.res_config_settings_view_cl_form
msgid "SII Resolution Nº"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields,field_description:l10n_cl_edi.field_account_bank_statement_line__l10n_cl_sii_send_ident
#: model:ir.model.fields,field_description:l10n_cl_edi.field_account_move__l10n_cl_sii_send_ident
#: model:ir.model.fields,field_description:l10n_cl_edi.field_account_payment__l10n_cl_sii_send_ident
msgid "SII Send Identification(Track ID)"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields,field_description:l10n_cl_edi.field_account_bank_statement_line__l10n_cl_sii_send_file
#: model:ir.model.fields,field_description:l10n_cl_edi.field_account_move__l10n_cl_sii_send_file
#: model:ir.model.fields,field_description:l10n_cl_edi.field_account_payment__l10n_cl_sii_send_file
msgid "SII Send file"
msgstr ""

#. module: l10n_cl_edi
#: model_terms:ir.ui.view,arch_db:l10n_cl_edi.res_config_settings_view_cl_form
msgid "SII Web Services"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields.selection,name:l10n_cl_edi.selection__res_company__l10n_cl_sii_regional_office__ur_saa
msgid "San Antonio"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields.selection,name:l10n_cl_edi.selection__res_company__l10n_cl_sii_regional_office__ur_sanbernardo
msgid "San Bernardo"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields.selection,name:l10n_cl_edi.selection__res_company__l10n_cl_sii_regional_office__ur_sar
msgid "San Carlos"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields.selection,name:l10n_cl_edi.selection__res_company__l10n_cl_sii_regional_office__ur_saf
msgid "San Felipe"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields.selection,name:l10n_cl_edi.selection__res_company__l10n_cl_sii_regional_office__ur_sad
msgid "San Fernando"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields.selection,name:l10n_cl_edi.selection__res_company__l10n_cl_sii_regional_office__ur_sav
msgid "San Vicente de Tagua Tagua"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields.selection,name:l10n_cl_edi.selection__res_company__l10n_cl_sii_regional_office__ur_saz
msgid "Santa Cruz"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields.selection,name:l10n_cl_edi.selection__res_company__l10n_cl_sii_regional_office__ur_sac
msgid "Santiago Centro"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields.selection,name:l10n_cl_edi.selection__res_company__l10n_cl_sii_regional_office__ur_san
msgid "Santiago Norte"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields.selection,name:l10n_cl_edi.selection__res_company__l10n_cl_sii_regional_office__ur_sao
msgid "Santiago Oriente"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields.selection,name:l10n_cl_edi.selection__res_company__l10n_cl_sii_regional_office__ur_sap
msgid "Santiago Poniente"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields.selection,name:l10n_cl_edi.selection__res_company__l10n_cl_sii_regional_office__ur_sas
msgid "Santiago Sur"
msgstr ""

#. module: l10n_cl_edi
#: model_terms:ir.ui.view,arch_db:l10n_cl_edi.view_partner_activities_search
msgid "Search By"
msgstr ""

#. module: l10n_cl_edi
#. odoo-python
#: code:addons/l10n_cl_edi/models/account_move.py:0
msgid "Sender Does Not Have Permission To Send"
msgstr ""

#. module: l10n_cl_edi
#. odoo-python
#: code:addons/l10n_cl_edi/models/l10n_cl_edi_util.py:0
#: code:addons/l10n_cl_edi/models/l10n_cl_edi_util.py:0
msgid "Sending DTE to SII failed due to:"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields.selection,name:l10n_cl_edi.selection__account_move__l10n_cl_dte_partner_status__sent
msgid "Sent"
msgstr ""

#. module: l10n_cl_edi
#: model_terms:ir.ui.view,arch_db:l10n_cl_edi.res_config_settings_view_cl_form
msgid "Signature Certificates"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields,field_description:l10n_cl_edi.field_l10n_cl_certificate__signature_filename
msgid "Signature File Name"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields.selection,name:l10n_cl_edi.selection__l10n_cl_dte_caf__status__spent
msgid "Spent"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields,field_description:l10n_cl_edi.field_l10n_cl_dte_caf__start_nb
msgid "Start Number"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields,field_description:l10n_cl_edi.field_l10n_cl_dte_caf__status
msgid "Status"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields,help:l10n_cl_edi.field_account_bank_statement_line__l10n_cl_dte_status
#: model:ir.model.fields,help:l10n_cl_edi.field_account_move__l10n_cl_dte_status
#: model:ir.model.fields,help:l10n_cl_edi.field_account_payment__l10n_cl_dte_status
msgid ""
"Status of sending the DTE to the SII:\n"
"    - Not sent: the DTE has not been sent to SII but it has created.\n"
"    - Ask For Status: The DTE is asking for its status to the SII.\n"
"    - Accepted: The DTE has been accepted by SII.\n"
"    - Accepted With Objections: The DTE has been accepted with objections by SII.\n"
"    - Rejected: The DTE has been rejected by SII.\n"
"    - Cancelled: The DTE has been deleted by the user.\n"
"    - Manual: The DTE is sent manually, i.e.: the DTE will not be sending manually."
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields,field_description:l10n_cl_edi.field_l10n_cl_certificate__subject_serial_number
msgid "Subject Serial Number"
msgstr ""

#. module: l10n_cl_edi
#. odoo-python
#: code:addons/l10n_cl_edi/models/account_move.py:0
msgid "Submission in process"
msgstr ""

#. module: l10n_cl_edi
#. odoo-python
#: code:addons/l10n_cl_edi/models/account_move.py:0
msgid "Submission processed"
msgstr ""

#. module: l10n_cl_edi
#. odoo-python
#: code:addons/l10n_cl_edi/models/account_move.py:0
msgid "Submission received"
msgstr ""

#. module: l10n_cl_edi
#. odoo-python
#: code:addons/l10n_cl_edi/models/account_move.py:0
msgid "Submission signature validated"
msgstr ""

#. module: l10n_cl_edi
#. odoo-python
#: code:addons/l10n_cl_edi/models/account_move.py:0
msgid "System Locked"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields,field_description:l10n_cl_edi.field_l10n_cl_company_activities__tax_category
msgid "TAX Category"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields.selection,name:l10n_cl_edi.selection__res_company__l10n_cl_sii_regional_office__ur_tat
msgid "Tal-Tal"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields.selection,name:l10n_cl_edi.selection__res_company__l10n_cl_sii_regional_office__ur_tac
msgid "Talca"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields.selection,name:l10n_cl_edi.selection__res_company__l10n_cl_sii_regional_office__ur_tah
msgid "Talcahuano"
msgstr ""

#. module: l10n_cl_edi
#: model_terms:ir.ui.view,arch_db:l10n_cl_edi.res_config_settings_view_cl_form
msgid "Tax payer information"
msgstr ""

#. module: l10n_cl_edi
#: model_terms:ir.ui.view,arch_db:l10n_cl_edi.res_config_settings_view_cl_form
msgid "Taxpayer"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields,field_description:l10n_cl_edi.field_res_company__l10n_cl_sii_taxpayer_type
#: model:ir.model.fields,field_description:l10n_cl_edi.field_res_config_settings__l10n_cl_sii_taxpayer_type
msgid "Taxpayer Type"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields.selection,name:l10n_cl_edi.selection__res_company__l10n_cl_sii_regional_office__ur_tem
msgid "Temuco"
msgstr ""

#. module: l10n_cl_edi
#. odoo-python
#: code:addons/l10n_cl_edi/models/account_move.py:0
msgid ""
"The %s %s has not a DTE email defined. This is mandatory for electronic "
"invoicing."
msgstr ""

#. module: l10n_cl_edi
#. odoo-python
#: code:addons/l10n_cl_edi/models/account_move.py:0
msgid "The .xml file was not found"
msgstr ""

#. module: l10n_cl_edi
#. odoo-python
#: code:addons/l10n_cl_edi/models/l10n_cl_dte_caf.py:0
msgid ""
"The VAT of your company has not been configured. Please go to your company "
"data and add it."
msgstr ""

#. module: l10n_cl_edi
#: model_terms:ir.ui.view,arch_db:l10n_cl_edi.barcode_stamp_footer
msgid "The VAT tax of this boleta is:"
msgstr ""

#. module: l10n_cl_edi
#. odoo-python
#: code:addons/l10n_cl_edi/models/l10n_cl_certificate.py:0
msgid "The certificate is expired since %s"
msgstr ""

#. module: l10n_cl_edi
#. odoo-python
#: code:addons/l10n_cl_edi/models/l10n_cl_certificate.py:0
#: code:addons/l10n_cl_edi/models/l10n_cl_certificate.py:0
msgid "The certificate signature_key_file is invalid: %s."
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields,help:l10n_cl_edi.field_l10n_cl_certificate__cert_expiration
msgid "The date on which the certificate expires"
msgstr ""

#. module: l10n_cl_edi
#. odoo-python
#: code:addons/l10n_cl_edi/models/account_move.py:0
msgid "The document type with code %s cannot be %s"
msgstr ""

#. module: l10n_cl_edi
#: model_terms:ir.ui.view,arch_db:l10n_cl_edi.res_config_settings_view_cl_form
msgid "The parameters to configure the signature certificate."
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields,help:l10n_cl_edi.field_account_bank_statement_line__l10n_cl_claim
#: model:ir.model.fields,help:l10n_cl_edi.field_account_move__l10n_cl_claim
#: model:ir.model.fields,help:l10n_cl_edi.field_account_payment__l10n_cl_claim
msgid "The reason why the DTE was accepted or claimed by the customer"
msgstr ""

#. module: l10n_cl_edi
#. odoo-python
#: code:addons/l10n_cl_edi/models/fetchmail_server.py:0
msgid "The server must be of type IMAP."
msgstr ""

#. module: l10n_cl_edi
#. odoo-python
#: code:addons/l10n_cl_edi/models/account_move.py:0
msgid ""
"The stamp date and time cannot be prior to the invoice issue date and time. "
"TIP: check in your user preferences if the timezone is \"America/Santiago\""
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields,help:l10n_cl_edi.field_account_bank_statement_line__l10n_cl_dte_acceptation_status
#: model:ir.model.fields,help:l10n_cl_edi.field_account_move__l10n_cl_dte_acceptation_status
#: model:ir.model.fields,help:l10n_cl_edi.field_account_payment__l10n_cl_dte_acceptation_status
msgid ""
"The status of the DTE Acceptation\n"
"    Received: the DTE was received by us for vendor bills, by our customers for customer invoices.\n"
"    Acknowledge Sent: the Acknowledge has been sent to the vendor.\n"
"    Claimed: the DTE was claimed by us for vendor bills, by our customers for customer invoices.\n"
"    Accepted: the DTE was accepted by us for vendor bills, by our customers for customer invoices.\n"
"    Reception 19983: means that the merchandise or services reception has been created and sent.\n"
"    Accepted and RG 19983: means that both the content of the document has been accepted and the merchandise or \n"
"services reception has been received as well.\n"
"    "
msgstr ""

#. module: l10n_cl_edi
#. odoo-python
#: code:addons/l10n_cl_edi/models/account_move.py:0
#, python-format
msgid ""
"The total amount of the DTE's XML is %(xml_amount)s and the total amount calculated by "
"Odoo is %(move_amount)s. Typically this is caused by additional lines in the detail or by"
" unidentified taxes, please check if a manual correction is needed."
msgstr ""

#. module: l10n_cl_edi
#. odoo-python
#: code:addons/l10n_cl_edi/models/l10n_latam_document_type.py:0
msgid ""
"There are no CAFs available for folio %s in the sequence of %s. Please "
"upload a CAF file or ask for a new one at www.sii.cl website"
msgstr ""

#. module: l10n_cl_edi
#. odoo-python
#: code:addons/l10n_cl_edi/models/l10n_latam_document_type.py:0
msgid ""
"There are no CAFs available. Please upload a CAF file or ask for a new one "
"at www.sii.cl website"
msgstr ""

#. module: l10n_cl_edi
#. odoo-python
#: code:addons/l10n_cl_edi/models/account_move.py:0
msgid ""
"There are no activity codes configured in your company. This is mandatory "
"for electronic invoicing. Please go to your company and set the correct "
"activity codes (www.sii.cl - Mi SII)"
msgstr ""

#. module: l10n_cl_edi
#. odoo-python
#: code:addons/l10n_cl_edi/models/l10n_cl_edi_util.py:0
#: code:addons/l10n_cl_edi/models/l10n_cl_edi_util.py:0
msgid "There is an unexpected response from SII"
msgstr ""

#. module: l10n_cl_edi
#. odoo-python
#: code:addons/l10n_cl_edi/models/account_move.py:0
msgid ""
"There is no SII Regional Office configured in your company. This is "
"mandatory for electronic invoicing. Please go to your company and set the "
"regional office, according to your company address (www.sii.cl - Mi SII)"
msgstr ""

#. module: l10n_cl_edi
#. odoo-python
#: code:addons/l10n_cl_edi/models/account_move.py:0
msgid ""
"There is no address configured in your customer %s record. This is mandatory"
" for electronic invoicing for this type of document. Please go to the "
"partner record and set the address"
msgstr ""

#. module: l10n_cl_edi
#. odoo-python
#: code:addons/l10n_cl_edi/models/account_move.py:0
msgid ""
"There is no signature available to send acknowledge or acceptation of this "
"DTE. Please setup your digital signature"
msgstr ""

#. module: l10n_cl_edi
#. odoo-python
#: code:addons/l10n_cl_edi/models/res_company.py:0
msgid "There is not a valid certificate for the company: %s"
msgstr ""

#. module: l10n_cl_edi
#. odoo-python
#: code:addons/l10n_cl_edi/models/account_move.py:0
msgid ""
"There is not an activity description configured in the customer %s record. "
"This is mandatory for electronic invoicing for this type of document. Please"
" go to the partner record and set the activity description"
msgstr ""

#. module: l10n_cl_edi
#. odoo-python
#: code:addons/l10n_cl_edi/models/account_move.py:0
msgid ""
"This %s is in SII status %s. It cannot be reset to draft state. Instead you "
"should revert it."
msgstr ""

#. module: l10n_cl_edi
#. odoo-python
#: code:addons/l10n_cl_edi/models/account_move.py:0
msgid ""
"This %s is in SII status: %s. It cannot be cancelled. Instead you should "
"revert it."
msgstr ""

#. module: l10n_cl_edi
#. odoo-python
#: code:addons/l10n_cl_edi/models/account_move.py:0
msgid ""
"This %s is in the intermediate state: 'Ask for Status in the SII'. You will "
"be able to cancel it only when the document has reached the state of "
"rejection. Otherwise, if it were accepted or objected you should revert it "
"with a suitable document instead of cancelling it."
msgstr ""

#. module: l10n_cl_edi
#. odoo-python
#: code:addons/l10n_cl_edi/models/account_move.py:0
msgid ""
"This %s is in the intermediate state: 'Ask for Status in the SII'. You will "
"be able to reset it to draft only when the document has reached the state of"
" rejection. Otherwise, if it were accepted or objected you should revert it "
"with a suitable document instead of cancelling it."
msgstr ""

#. module: l10n_cl_edi
#. odoo-python
#: code:addons/l10n_cl_edi/models/account_move.py:0
msgid ""
"This %s is rejected by SII. Instead of creating a reverse, you should set it"
" to draft state, correct it and post it again."
msgstr ""

#. module: l10n_cl_edi
#. odoo-python
#: code:addons/l10n_cl_edi/models/account_move.py:0
#: code:addons/l10n_cl_edi/models/account_move.py:0
msgid ""
"This DTE has been generated in DEMO Mode. It is considered as accepted and "
"it won't be sent to SII."
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields,help:l10n_cl_edi.field_account_bank_statement_line__l10n_cl_sii_barcode
#: model:ir.model.fields,help:l10n_cl_edi.field_account_move__l10n_cl_sii_barcode
#: model:ir.model.fields,help:l10n_cl_edi.field_account_payment__l10n_cl_sii_barcode
msgid ""
"This XML contains the portion of the DTE XML that should be coded in PDF417 "
"and printed in the invoice barcode should be present in the printed invoice "
"report to be valid"
msgstr ""

#. module: l10n_cl_edi
#: model_terms:ir.ui.view,arch_db:l10n_cl_edi.res_config_settings_view_cl_form
msgid ""
"This alias will be used as sender in the outgoing emails when you send "
"invoices attached to your customers and with invoice acknowledge / "
"acceptation or claim sent to your vendors."
msgstr ""

#. module: l10n_cl_edi
#. odoo-python
#: code:addons/l10n_cl_edi/models/account_move.py:0
msgid "This electronic document is being processed already."
msgstr ""

#. module: l10n_cl_edi
#: model_terms:ir.ui.view,arch_db:l10n_cl_edi.res_config_settings_view_cl_form
msgid ""
"This email account should match both emails declared on the SII site in the "
"section: \"ACTUALIZACION DE DATOS DEL CONTRIBUYENTE\", \"Mail Contacto SII\""
" and \"Mail Contacto Empresas\"."
msgstr ""

#. module: l10n_cl_edi
#. odoo-python
#: code:addons/l10n_cl_edi/models/account_move.py:0
msgid "This feature is not available in certification/test mode"
msgstr ""

#. module: l10n_cl_edi
#. odoo-python
#: code:addons/l10n_cl_edi/models/account_move.py:0
msgid "This invoice is being processed already."
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields,help:l10n_cl_edi.field_l10n_cl_certificate__subject_serial_number
msgid ""
"This is the document of the owner of this certificate.Some certificates does"
" not provide this number and you must fill it by hand"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields,help:l10n_cl_edi.field_account_journal__l10n_cl_point_of_sale_name
msgid ""
"This is the name that you want to assign to your point of sale. It is not "
"mandatory."
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields,help:l10n_cl_edi.field_account_debit_note__l10n_cl_original_text
#: model:ir.model.fields,help:l10n_cl_edi.field_account_move_reversal__l10n_cl_original_text
msgid "This is the text that is intended to be changed"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields,help:l10n_cl_edi.field_account_debit_note__l10n_cl_corrected_text
#: model:ir.model.fields,help:l10n_cl_edi.field_account_move_reversal__l10n_cl_corrected_text
msgid "This is the text that should say"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields,help:l10n_cl_edi.field_account_journal__l10n_cl_point_of_sale_number
msgid "This number is needed only if provided by SII."
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields,help:l10n_cl_edi.field_fetchmail_server__l10n_cl_last_uid
msgid ""
"This value is pointing to the number of the last message read by odoo in the"
" inbox. This value will be updated by the system during its normaloperation."
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields,help:l10n_cl_edi.field_res_company__l10n_cl_dte_resolution_number
#: model:ir.model.fields,help:l10n_cl_edi.field_res_config_settings__l10n_cl_dte_resolution_number
msgid ""
"This value must be provided and must appear in your pdf or printed tribute "
"document, under the electronic stamp to be legally valid."
msgstr ""

#. module: l10n_cl_edi
#: model_terms:ir.ui.view,arch_db:l10n_cl_edi.barcode_stamp_footer
msgid ""
"Timbre Electrónico SII<br/>\n"
"                        Resolución Nº:"
msgstr ""

#. module: l10n_cl_edi
#. odoo-python
#: code:addons/l10n_cl_edi/models/res_company.py:0
msgid "To create demo CAF files, you must define the company VAT first."
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields.selection,name:l10n_cl_edi.selection__res_company__l10n_cl_sii_regional_office__ur_toc
msgid "Tocopilla"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields,field_description:l10n_cl_edi.field_l10n_cl_certificate__token_time
msgid "Token Time"
msgstr ""

#. module: l10n_cl_edi
#. odoo-python
#: code:addons/l10n_cl_edi/models/l10n_cl_edi_util.py:0
#: code:addons/l10n_cl_edi/models/l10n_cl_edi_util.py:0
#: code:addons/l10n_cl_edi/models/l10n_cl_edi_util.py:0
#: code:addons/l10n_cl_edi/models/l10n_cl_edi_util.py:0
#: code:addons/l10n_cl_edi/models/l10n_cl_edi_util.py:0
msgid "Token cannot be generated. Please try again"
msgstr ""

#. module: l10n_cl_edi
#. odoo-python
#: code:addons/l10n_cl_edi/models/account_move.py:0
msgid "Upload OK"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields,help:l10n_cl_edi.field_l10n_cl_dte_caf__caf_file
msgid "Upload the CAF XML File in this holder"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields,help:l10n_cl_edi.field_l10n_cl_account_invoice_reference__reference_doc_code
msgid ""
"Use one of these codes for credit or debit notes that intend to change "
"taxable data in the origin referred document"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model,name:l10n_cl_edi.model_l10n_cl_edi_util
msgid "Utility Methods for Chilean Electronic Invoicing"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields.selection,name:l10n_cl_edi.selection__res_company__l10n_cl_sii_regional_office__ur_vld
msgid "Valdivia"
msgstr ""

#. module: l10n_cl_edi
#. odoo-python
#: code:addons/l10n_cl_edi/models/account_move.py:0
msgid "Validated schema"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields.selection,name:l10n_cl_edi.selection__res_company__l10n_cl_sii_regional_office__ur_val
msgid "Vallenar"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields.selection,name:l10n_cl_edi.selection__res_company__l10n_cl_sii_regional_office__ur_vlp
msgid "Valparaíso"
msgstr ""

#. module: l10n_cl_edi
#. odoo-python
#: code:addons/l10n_cl_edi/models/fetchmail_server.py:0
msgid "Vendor Bill DTE has been generated for the following vendor:"
msgstr ""

#. module: l10n_cl_edi
#. odoo-python
#: code:addons/l10n_cl_edi/models/fetchmail_server.py:0
msgid ""
"Vendor not found: You can generate this vendor manually with the following "
"information:"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields.selection,name:l10n_cl_edi.selection__res_company__l10n_cl_sii_regional_office__ur_vic
msgid "Victoria"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields.selection,name:l10n_cl_edi.selection__res_company__l10n_cl_sii_regional_office__ur_via
msgid "Villa Alemana"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields.selection,name:l10n_cl_edi.selection__res_company__l10n_cl_sii_regional_office__ur_vir
msgid "Villarrica"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields.selection,name:l10n_cl_edi.selection__res_company__l10n_cl_sii_regional_office__ur_vim
msgid "Viña del Mar"
msgstr ""

#. module: l10n_cl_edi
#. odoo-python
#: code:addons/l10n_cl_edi/models/account_move.py:0
#: code:addons/l10n_cl_edi/wizard/account_move_debit.py:0
msgid "Where it says: %(original_text)s should say: %(corrected_text)s"
msgstr ""

#. module: l10n_cl_edi
#. odoo-python
#: code:addons/l10n_cl_edi/wizard/account_move_debit.py:0
msgid ""
"You can add a debit note only if the %s is accepted or objected by SII. "
msgstr ""

#. module: l10n_cl_edi
#. odoo-python
#: code:addons/l10n_cl_edi/models/account_move.py:0
msgid ""
"You have not selected an invoicing service provider for your company. Please"
" go to your company and select one"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields,help:l10n_cl_edi.field_account_bank_statement_line__l10n_cl_journal_point_of_sale_type
#: model:ir.model.fields,help:l10n_cl_edi.field_account_journal__l10n_cl_point_of_sale_type
#: model:ir.model.fields,help:l10n_cl_edi.field_account_move__l10n_cl_journal_point_of_sale_type
#: model:ir.model.fields,help:l10n_cl_edi.field_account_payment__l10n_cl_journal_point_of_sale_type
msgid ""
"You must select \"Online\" for journals with documents that need to be\n"
"sent to SII automatically. In this case you must upload a CAF file for each\n"
"type of document you will use in this journal.\n"
"You must select \"Manual\" if you are either a user of \"Facturación MiPyme\"\n"
"(free SII's website invoicing system) or if you have already generated\n"
"those documents using a different system in the past, and you want to\n"
"register them in Odoo now."
msgstr ""

#. module: l10n_cl_edi
#. odoo-python
#: code:addons/l10n_cl_edi/wizard/account_move_reversal.py:0
msgid "You need to provide a reason for the refund. "
msgstr ""

#. module: l10n_cl_edi
#. odoo-python
#: code:addons/l10n_cl_edi/models/account_move.py:0
msgid ""
"Your company has not an activity description configured. This is mandatory "
"for electronic invoicing. Please go to your company and set the correct one "
"(www.sii.cl - Mi SII)"
msgstr ""

#. module: l10n_cl_edi
#. odoo-python
#: code:addons/l10n_cl_edi/models/account_move.py:0
msgid "acceptance"
msgstr ""

#. module: l10n_cl_edi
#. odoo-python
#: code:addons/l10n_cl_edi/models/account_move.py:0
msgid "claim"
msgstr ""

#. module: l10n_cl_edi
#. odoo-python
#: code:addons/l10n_cl_edi/models/account_move.py:0
msgid "company"
msgstr ""

#. module: l10n_cl_edi
#: model_terms:ir.ui.view,arch_db:l10n_cl_edi.barcode_stamp_footer
msgid "de Fecha:"
msgstr ""

#. module: l10n_cl_edi
#. odoo-python
#: code:addons/l10n_cl_edi/models/account_move.py:0
msgid "failed due to:"
msgstr ""

#. module: l10n_cl_edi
#. odoo-python
#: code:addons/l10n_cl_edi/models/account_move.py:0
msgid "partner"
msgstr ""

#. module: l10n_cl_edi
#. odoo-python
#: code:addons/l10n_cl_edi/models/account_move.py:0
msgid "reception of goods or services RG 19.983"
msgstr ""

#. module: l10n_cl_edi
#: model:mail.template,subject:l10n_cl_edi.l10n_cl_edi_email_template_invoice
msgid "{{ object.company_id.name }} DTE (Ref {{ (object.name or 'n/a') }})"
msgstr ""

#. module: l10n_cl_edi
#: model:ir.model.fields.selection,name:l10n_cl_edi.selection__res_company__l10n_cl_sii_regional_office__ur_nunoa
msgid "Ñuñoa"
msgstr ""

#. module: l10n_cl_edi
#: model_terms:ir.ui.view,arch_db:l10n_cl_edi.invoice_status_form_cl
msgid "⇒ Reprocess Acknowledge"
msgstr ""

#. module: l10n_cl_edi
#: model_terms:ir.ui.view,arch_db:l10n_cl_edi.invoice_status_form_cl
msgid "⇒ Send Now to SII"
msgstr ""

#. module: l10n_cl_edi
#: model_terms:ir.ui.view,arch_db:l10n_cl_edi.invoice_status_form_cl
msgid "⇒ Verify on SII"
msgstr ""
