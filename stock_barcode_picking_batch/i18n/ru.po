# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* stock_barcode_picking_batch
# 
# Translators:
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:28+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Russian (https://app.transifex.com/odoo/teams/41243/ru/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ru\n"
"Plural-Forms: nplurals=4; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<12 || n%100>14) ? 1 : n%10==0 || (n%10>=5 && n%10<=9) || (n%100>=11 && n%100<=14)? 2 : 3);\n"

#. module: stock_barcode_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_barcode_picking_batch.stock_move_line_product_selector_inherit
msgid "<i class=\"fa fa-lg fa-truck\" title=\"Transfer\"/>"
msgstr ""

#. module: stock_barcode_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_barcode_picking_batch.stock_picking_view_kanban
msgid "<span class=\"fa fa-external-link-square ms-1\" title=\"Open Batch Picking\"/>"
msgstr ""
"<span class=\"fa fa-external-link-square ms-1\" title=\"Open Batch "
"Picking\"/>"

#. module: stock_barcode_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_barcode_picking_batch.stock_barcode_batch_picking_view_info
msgid "Allocation"
msgstr "Распределение"

#. module: stock_barcode_picking_batch
#: model:ir.actions.client,name:stock_barcode_picking_batch.stock_barcode_picking_batch_client_action
msgid "Barcode Batch Picking Client Action"
msgstr "Действие клиента по подбору партии штрихкодов"

#. module: stock_barcode_picking_batch
#: model:ir.model,name:stock_barcode_picking_batch.model_stock_picking_batch
#: model:ir.model.fields,field_description:stock_barcode_picking_batch.field_stock_barcode_cancel_operation__batch_id
msgid "Batch Transfer"
msgstr "Пакетная передача"

#. module: stock_barcode_picking_batch
#: model:ir.model.fields,field_description:stock_barcode_picking_batch.field_stock_barcode_cancel_operation__batch_name
msgid "Batch Transfer Name"
msgstr "Имя пакетной передачи"

#. module: stock_barcode_picking_batch
#: model:ir.actions.act_window,name:stock_barcode_picking_batch.stock_barcode_batch_picking_action_kanban
msgid "Batch Transfers"
msgstr "Пакетные передачи"

#. module: stock_barcode_picking_batch
#. odoo-javascript
#: code:addons/stock_barcode_picking_batch/static/src/kanban/stock_barcode_kanban_renderer.xml:0
msgid "Batches"
msgstr "Партии"

#. module: stock_barcode_picking_batch
#. odoo-javascript
#: code:addons/stock_barcode_picking_batch/static/src/models/barcode_picking_batch_model.js:0
msgid "Cancel Batch Transfer"
msgstr "Отмена пакетной передачи"

#. module: stock_barcode_picking_batch
#: model:ir.model,name:stock_barcode_picking_batch.model_stock_barcode_cancel_operation
msgid "Cancel Operation"
msgstr "Отменить операции"

#. module: stock_barcode_picking_batch
#. odoo-python
#: code:addons/stock_barcode_picking_batch/models/stock_picking_batch.py:0
msgid "Cancel this batch transfer?"
msgstr "Отменить пакетную передачу?"

#. module: stock_barcode_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_barcode_picking_batch.stock_barcode_batch_picking_view_info
msgid "Confirm"
msgstr "Подтвердить"

#. module: stock_barcode_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_barcode_picking_batch.stock_barcode_batch_picking_view_info
msgid "Description"
msgstr "Описание"

#. module: stock_barcode_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_barcode_picking_batch.stock_barcode_batch_picking_view_info
msgid "Discard"
msgstr "Отменить"

#. module: stock_barcode_picking_batch
#: model:ir.model.fields,field_description:stock_barcode_picking_batch.field_stock_picking__display_batch_button
msgid "Display Batch Button"
msgstr "Дисплей Кнопка пакетной обработки"

#. module: stock_barcode_picking_batch
#: model:ir.model.fields,field_description:stock_barcode_picking_batch.field_stock_picking_type__group_lines_by_product
msgid "Group batch lines"
msgstr ""

#. module: stock_barcode_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_barcode_picking_batch.stock_barcode_batch_picking_view_kanban
msgid "Lines"
msgstr "Линии"

#. module: stock_barcode_picking_batch
#: model:ir.model.fields,help:stock_barcode_picking_batch.field_stock_picking_type__group_lines_by_product
msgid ""
"Lines of same product at same location appear grouped. Not to use for "
"cluster picking"
msgstr ""

#. module: stock_barcode_picking_batch
#: model_terms:ir.actions.act_window,help:stock_barcode_picking_batch.stock_barcode_batch_picking_action_kanban
msgid "No batch transfer found"
msgstr "Пакетная передача не найдена"

#. module: stock_barcode_picking_batch
#. odoo-javascript
#: code:addons/stock_barcode_picking_batch/static/src/models/barcode_picking_batch_model.js:0
msgid "No ready transfers found"
msgstr "Готовые переводы не найдены"

#. module: stock_barcode_picking_batch
#. odoo-python
#: code:addons/stock_barcode_picking_batch/models/stock_picking_batch.py:0
msgid "Open picking batch form"
msgstr "Открыть форму партии комплектования"

#. module: stock_barcode_picking_batch
#: model:ir.model,name:stock_barcode_picking_batch.model_stock_picking_type
msgid "Picking Type"
msgstr "Тип комплектования"

#. module: stock_barcode_picking_batch
#. odoo-javascript
#: code:addons/stock_barcode_picking_batch/static/src/models/barcode_picking_batch_model.js:0
msgid "Print Batch Transfer"
msgstr "Печать пакетной передачи"

#. module: stock_barcode_picking_batch
#. odoo-javascript
#: code:addons/stock_barcode_picking_batch/static/src/models/barcode_picking_batch_model.js:0
msgid "Print Product Labels"
msgstr ""

#. module: stock_barcode_picking_batch
#: model:ir.model,name:stock_barcode_picking_batch.model_stock_move_line
msgid "Product Moves (Stock Move Line)"
msgstr "Перемещение продукта (Позиции движения запасов)"

#. module: stock_barcode_picking_batch
#. odoo-javascript
#: code:addons/stock_barcode_picking_batch/static/src/models/barcode_picking_batch_model.js:0
msgid "Scan the package %s"
msgstr "Сканирование пакета %s"

#. module: stock_barcode_picking_batch
#. odoo-javascript
#: code:addons/stock_barcode_picking_batch/static/src/models/barcode_picking_batch_model.js:0
msgid "Select an operation type for batch transfer"
msgstr "Выберите тип операции для пакетной передачи"

#. module: stock_barcode_picking_batch
#. odoo-javascript
#: code:addons/stock_barcode_picking_batch/static/src/models/barcode_picking_batch_model.js:0
msgid "Select transfers for batch transfer"
msgstr "Выберите передачи для пакетной передачи"

#. module: stock_barcode_picking_batch
#. odoo-javascript
#: code:addons/stock_barcode_picking_batch/static/src/models/barcode_picking_batch_model.js:0
msgid "The Batch Transfer has been validated"
msgstr "Пакетная передача была подтверждена"

#. module: stock_barcode_picking_batch
#. odoo-javascript
#: code:addons/stock_barcode_picking_batch/static/src/models/barcode_picking_batch_model.js:0
msgid "The batch picking has been cancelled"
msgstr "Выборка партии была отменена"

#. module: stock_barcode_picking_batch
#: model_terms:ir.actions.act_window,help:stock_barcode_picking_batch.stock_barcode_batch_picking_action_kanban
msgid ""
"The goal of the batch transfer is to group operations that may\n"
"            (needs to) be done together in order to increase their efficiency.\n"
"            It may also be useful to assign jobs (one person = one batch) or\n"
"            help the timing management of operations (tasks to be done at 1pm)."
msgstr ""
"Целью пакетной передачи является группировка операций, которые могут\n"
"            (должны) делаться вместе, чтобы повысить их эффективность.\n"
"            Также может быть полезно назначить работу (один человек = одна партия) или\n"
"            помощь в управлении сроками выполнения операций (задачи, которые должны быть выполнены в час дня)."

#. module: stock_barcode_picking_batch
#: model:ir.model.fields,help:stock_barcode_picking_batch.field_stock_move_line__picking_id
msgid "The stock operation where the packing has been made"
msgstr "Складская операция, на которой была произведена упаковка"

#. module: stock_barcode_picking_batch
#. odoo-javascript
#: code:addons/stock_barcode_picking_batch/static/src/models/barcode_picking_batch_model.js:0
msgid "This batch transfer is already done"
msgstr "Этот пакетный перевод уже выполнен"

#. module: stock_barcode_picking_batch
#. odoo-javascript
#: code:addons/stock_barcode_picking_batch/static/src/models/barcode_picking_batch_model.js:0
msgid "This batch transfer is cancelled"
msgstr "Эта пакетная передача отменяется"

#. module: stock_barcode_picking_batch
#. odoo-javascript
#: code:addons/stock_barcode_picking_batch/static/src/models/barcode_picking_batch_model.js:0
msgid ""
"This batch transfer is still draft, it must be confirmed before being "
"processed"
msgstr ""
"Этот пакетный перевод все еще является черновым, его необходимо подтвердить "
"перед обработкой"

#. module: stock_barcode_picking_batch
#: model:ir.model,name:stock_barcode_picking_batch.model_stock_picking
#: model:ir.model.fields,field_description:stock_barcode_picking_batch.field_stock_move_line__picking_id
#: model_terms:ir.ui.view,arch_db:stock_barcode_picking_batch.stock_move_line_product_selector_inherit
msgid "Transfer"
msgstr "Перевод"

#. module: stock_barcode_picking_batch
#. odoo-javascript
#: code:addons/stock_barcode_picking_batch/static/src/kanban/stock_barcode_kanban_renderer.xml:0
msgid "Transfers"
msgstr "Трансферы"

#. module: stock_barcode_picking_batch
#: model:ir.model.fields,field_description:stock_barcode_picking_batch.field_stock_picking_batch__picking_type_code
msgid "Type of Operation"
msgstr "Тип операции"

#. module: stock_barcode_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_barcode_picking_batch.stock_barcode_batch_picking_view_info
msgid "Unbatch Transfer"
msgstr "Беспакетная передача"

#. module: stock_barcode_picking_batch
#. odoo-python
#: code:addons/stock_barcode_picking_batch/models/stock_picking.py:0
msgid ""
"You cannot validate a transfer if no quantities are reserved nor done. You "
"can use the info button on the top right corner of your screen to remove the"
" transfer in question from the batch."
msgstr ""
"Вы не можете подтвердить трансфер, если не зарезервировано и не выполнено ни"
" одного количества. Вы можете воспользоваться кнопкой \"Информация\" в "
"правом верхнем углу экрана, чтобы удалить данный трансфер из партии."
