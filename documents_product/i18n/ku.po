# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* documents_product
# 
# Translators:
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 09:15+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON><PERSON>, 2025\n"
"Language-Team: Kurdish (https://app.transifex.com/odoo/teams/41243/ku/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ku\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: documents_product
#: model_terms:ir.ui.view,arch_db:documents_product.res_config_settings_view_form
msgid "Centralize files attached to products"
msgstr ""

#. module: documents_product
#: model:ir.model,name:documents_product.model_res_company
msgid "Companies"
msgstr "کۆمپانیاکان"

#. module: documents_product
#: model:ir.model,name:documents_product.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: documents_product
#: model:ir.actions.server,name:documents_product.ir_actions_server_create_product_template
msgid "Create Product Template"
msgstr ""

#. module: documents_product
#: model_terms:ir.ui.view,arch_db:documents_product.res_config_settings_view_form
msgid "Default Tags"
msgstr ""

#. module: documents_product
#: model:ir.model,name:documents_product.model_documents_document
msgid "Document"
msgstr ""

#. module: documents_product
#: model:ir.model.fields,field_description:documents_product.field_res_company__documents_product_settings
msgid "Documents Product Settings"
msgstr ""

#. module: documents_product
#. odoo-python
#: code:addons/documents_product/models/document.py:0
msgid "Invalid %s search"
msgstr ""

#. module: documents_product
#: model:ir.model,name:documents_product.model_product_template
#: model:ir.model.fields,field_description:documents_product.field_documents_document__product_template_id
#: model:ir.model.fields,field_description:documents_product.field_res_config_settings__documents_product_settings
msgid "Product"
msgstr "بەرهەم"

#. module: documents_product
#: model:documents.tag,name:documents_product.documents_product_sheet_tag
msgid "Product > DataSheets"
msgstr ""

#. module: documents_product
#: model:documents.tag,name:documents_product.documents_product_msds_tag
msgid "Product > MSDS"
msgstr ""

#. module: documents_product
#: model:documents.tag,name:documents_product.documents_product_new_tag
msgid "Product > New"
msgstr ""

#. module: documents_product
#: model:documents.tag,name:documents_product.documents_product_plans_tag
msgid "Product > Plans"
msgstr ""

#. module: documents_product
#: model:documents.tag,name:documents_product.documents_product_specs_tag
msgid "Product > Specs"
msgstr ""

#. module: documents_product
#: model:ir.model.fields,field_description:documents_product.field_res_company__product_tag_ids
msgid "Product Tag"
msgstr ""

#. module: documents_product
#: model:ir.model.fields,field_description:documents_product.field_res_config_settings__product_tag_ids
msgid "Product Tags"
msgstr ""

#. module: documents_product
#: model:ir.model,name:documents_product.model_product_product
#: model:ir.model.fields,field_description:documents_product.field_documents_document__product_id
msgid "Product Variant"
msgstr "جۆری بەرهەم"

#. module: documents_product
#: model:ir.model.fields,field_description:documents_product.field_res_company__product_folder_id
msgid "Product Workspace"
msgstr ""

#. module: documents_product
#. odoo-python
#: code:addons/documents_product/models/document.py:0
msgid "Product created from Documents"
msgstr ""

#. module: documents_product
#: model_terms:ir.ui.view,arch_db:documents_product.res_config_settings_view_form
msgid "Workspace"
msgstr ""

#. module: documents_product
#: model:ir.model.fields,field_description:documents_product.field_res_config_settings__product_folder_id
msgid "product default workspace"
msgstr ""
