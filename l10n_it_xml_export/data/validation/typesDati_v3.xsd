<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns="urn:www.agenziaentrate.gov.it:specificheTecniche:common" targetNamespace="urn:www.agenziaentrate.gov.it:specificheTecniche:common" elementFormDefault="qualified" attributeFormDefault="unqualified" version="3.0">
	<xs:annotation>
		<xs:documentation xml:lang="it"><![CDATA[
		Versione 3.0 - 10/12/13
		- modificato targetNamespace
		Versione 2.0.1 - 14/06/12
		- modificati i tipi semplici DatoAN_Type, DatoEM_Type
		Versione 2.0 - 15/02/12
		- modificato targetNamespace
		- introdotti i tipi semplici DatoGA_Type, DatoTL_Type, DatoCP_Type
		]]></xs:documentation>
	</xs:annotation>
	<xs:include schemaLocation="typesProvincie_v3.xsd"/>
	<xs:simpleType name="DatoAN_Type">
		<xs:annotation>
			<xs:documentation>Tipo semplice costituito da caratteri alfanumerici maiuscoli e dai caratteri: punto, virgola, apice, trattino, spazio, barra semplice, °, ^, ampersand, parentesi aperta e chiusa, doppie virgolette, barra rovesciata, la barra dritta, il più, le maiuscole accentate e la Ü. Tali caratteri non sono ammesi come primo carattere tranne: i numeri da 0 a 9, i caratteri maiuscoli da A a Z, il meno e le dopppie virgolette.</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:pattern value="([0-9A-Z\-]|&quot;){1}([ 0-9A-Z&amp;]|'|\-|\.|,|/|°|\^|\(|\)|À|È|É|Ì|Ò|Ù|Ü|&quot;|\\|\||\+)*"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="DatoNU_Type">
		<xs:annotation>
			<xs:documentation>Tipo semplice che identifica numeri naturali positivi e negativi con al massimo 16 cifre.</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="16"/>
			<xs:pattern value="(\-[1-9]|[1-9])[0-9]*"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="DatoPC_Type">
		<xs:annotation>
			<xs:documentation>Tipo semplice che esprime una percentuale e dunque consente valori positivi non superiori a 100, con al massimo 2 cifre decimali. Il separatore decimale previsto è la virgola.</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="16"/>
			<xs:pattern value="[0-9]?[0-9](,\d{1,3})?|100(,0{1,3})?"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="DatoQU_Type">
		<xs:annotation>
			<xs:documentation>Tipo semplice che identifica numeri positivi con al massimo 5 cifre decimali. La lunghezza massima prevista è di 16 caratteri, il separatore decimale previsto è la virgola.</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="16"/>
			<xs:pattern value="[0-9]+(,[0-9]{1,5})?"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="DatoVP_Type">
		<xs:annotation>
			<xs:documentation>Tipo semplice che identifica numeri positivi con 2 cifre decimali. La lunghezza massima prevista è di 16 caratteri, il separatore decimale previsto è la virgola.</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="16"/>
			<xs:pattern value="[0-9]+,[0-9]{2}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="DatoN1_Type">
		<xs:annotation>
			<xs:documentation>Tipo semplice che identifica i numeri naturali da 1 a 9.</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="1"/>
			<xs:pattern value="[1-9]"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="DatoNP_Type">
		<xs:annotation>
			<xs:documentation>Tipo semplice che identifica numeri naturali positivi con al massimo 16 cifre.</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:pattern value="[1-9]{1}[0-9]*"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="DatoPI_Type">
		<xs:annotation>
			<xs:documentation>Tipo semplice che identifica la partita IVA rispettandone i vincoli di struttura. </xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:length value="11"/>
			<xs:pattern value="[0-7][0-9]{10}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="DatoCN_Type">
		<xs:annotation>
			<xs:documentation>Tipo semplice che identifica un codice fiscale numerico rispettandone i vincoli di struttura.</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:length value="11"/>
			<xs:pattern value="[0-9]{11}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="DatoCF_Type">
		<xs:annotation>
			<xs:documentation>Tipo semplice che identifica un codice fiscale provvisorio o alfanumerico rispettandone i vincoli di struttura.</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:pattern value="[0-9]{11}|[A-Z]{6}[0-9LMNPQRSTUV]{2}[A-Z]{1}[0-9LMNPQRSTUV]{2}[A-Z]{1}[0-9LMNPQRSTUV]{3}[A-Z]{1}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="DatoCB_Type">
		<xs:annotation>
			<xs:documentation>Tipo semplice che consente esclusivamente i valori 0 e 1.</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:byte">
			<xs:pattern value="[01]"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="DatoCB12_Type">
		<xs:annotation>
			<xs:documentation>Tipo semplice che consente esclusivamente 12 caratteri con i valori 0 e 1.</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:byte">
			<xs:pattern value="[10]{12}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="DatoDT_Type">
		<xs:annotation>
			<xs:documentation>Tipo semplice che identifica una data nel formato ggmmaaaa. La data indicata non deve essere successiva alla data corrente.</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:length value="8"/>
			<xs:pattern value="(((0[1-9]|[12][0-9]|3[01])(0[13578]|10|12)(\d{4}))|(([0][1-9]|[12][0-9]|30)(0[469]|11)(\d{4}))|((0[1-9]|1[0-9]|2[0-8])(02)(\d{4}))|((29)(02)([02468][048]00))|((29)(02)([13579][26]00))|((29)(02)([0-9][0-9][0][48]))|((29)(02)([0-9][0-9][2468][048]))|((29)(02)([0-9][0-9][13579][26])))"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="DatoDA_Type">
		<xs:annotation>
			<xs:documentation>Tipo semplice che identifica un anno nel formato aaaa. Sono ammessi anni dal 1800 al 2099.</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:length value="4"/>
			<xs:pattern value="(18|19|20)[0-9]{2}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="DatoDN_Type">
		<xs:annotation>
			<xs:documentation>Tipo semplice che identifica una data nel formato ggmmaaaa.</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:length value="8"/>
			<xs:pattern value="(((0[1-9]|[12][0-9]|3[01])(0[13578]|10|12)(\d{4}))|(([0][1-9]|[12][0-9]|30)(0[469]|11)(\d{4}))|((0[1-9]|1[0-9]|2[0-8])(02)(\d{4}))|((29)(02)([02468][048]00))|((29)(02)([13579][26]00))|((29)(02)([0-9][0-9][0][48]))|((29)(02)([0-9][0-9][2468][048]))|((29)(02)([0-9][0-9][13579][26])))"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="DatoD6_Type">
		<xs:annotation>
			<xs:documentation>Tipo semplice che identifica una data nel formato mmaaaa.</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:length value="6"/>
			<xs:pattern value="((0[0-9])|(1[0-2]))((19|20)[0-9][0-9])"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="DatoEM_Type">
		<xs:annotation>
			<xs:documentation>Tipo semplice che identifica un elemento di tipo email</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:pattern value="[a-zA-Z0-9._%\-'&quot;?^~=]+@[a-zA-Z0-9.\-]+\.[a-zA-Z]{2,4}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="DatoGA_Type">
		<xs:annotation>
			<xs:documentation>Tipo semplice che identifica il numero di giorni in un anno e va da 1 a 365</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="3"/>
			<xs:pattern value="[1-9]|([1-9][0-9])|([12][0-9][0-9])|(3[0-5][0-9])|(36[0-5])"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="DatoTL_Type">
		<xs:annotation>
			<xs:documentation>Tipo semplice che identifica un elemento di tipo telefono</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:pattern value="[0-9]*"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="DatoCP_Type">
		<xs:annotation>
			<xs:documentation>Tipo semplice che identifica un elemento di tipo cap</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:pattern value="[0-9]{5}"/>
		</xs:restriction>
	</xs:simpleType>
</xs:schema>
