# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_at_saft
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.5alpha1+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-24 13:01+0000\n"
"PO-Revision-Date: 2023-10-24 13:01+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_at_saft
#: model:ir.model.fields,field_description:l10n_at_saft.field_l10n_at_saft_account__account_class
msgid "Account Class"
msgstr "Kontenklasse"

#. module: l10n_at_saft
#: model:ir.model.fields,field_description:l10n_at_saft.field_l10n_at_saft_account__account_type
msgid "Account Type"
msgstr "Kontenart"

#. module: l10n_at_saft
#. odoo-python
#: code:addons/l10n_at_saft/models/account_general_ledger.py:0
msgid "Accounts which cannot be mapped into the SAF-T chart of accounts"
msgstr "Kontos, die nicht in den SAF-T Kontenrahmen eingeordnet werden können"

#. module: l10n_at_saft
#: model:ir.model,name:l10n_at_saft.model_ir_attachment
msgid "Attachment"
msgstr "Anhang"

#. module: l10n_at_saft
#: model_terms:ir.ui.view,arch_db:l10n_at_saft.res_config_settings_form_inherit_l10n_at_saft
msgid "Austrian localization"
msgstr "Lokalisierung für Österreich"

#. module: l10n_at_saft
#: model:ir.model.fields.selection,name:l10n_at_saft.selection__res_company__l10n_at_profit_assessment_method__par_4_abs_1
msgid "Betriebsvermögensvergleich nach § 4 Abs. 1 EStG"
msgstr "Betriebsvermögensvergleich nach § 4 Abs. 1 EStG"

#. module: l10n_at_saft
#: model:ir.model.fields.selection,name:l10n_at_saft.selection__res_company__l10n_at_profit_assessment_method__par_5
msgid "Betriebsvermögensvergleich nach § 5 EStG"
msgstr "Betriebsvermögensvergleich nach § 5 EStG"

#. module: l10n_at_saft
#: model:ir.model.fields,help:l10n_at_saft.field_l10n_at_saft_account__name
msgid "Called \"Bezeichnung\" in the Austrian SAF-T documentation"
msgstr "\"Bezeichnung\" genannt in der österreichischen Vorgabe zum SAF-T"

#. module: l10n_at_saft
#: model:ir.model.fields,help:l10n_at_saft.field_l10n_at_saft_account__account_type
msgid "Called \"Kontenart (Ka)\" in the Austrian SAF-T documentation"
msgstr "\"Kontenart (Ka)\" genannt in der österreichischen Vorgabe zum SAF-T"

#. module: l10n_at_saft
#: model:ir.model.fields,help:l10n_at_saft.field_l10n_at_saft_account__account_class
msgid "Called \"Kontenklasse (Kl)\" in the Austrian SAF-T documentation"
msgstr "\"Kontenklasse (Kl)\" genannt in der österreichischen Vorgabe zum SAF-T"

#. module: l10n_at_saft
#: model:ir.model.fields,help:l10n_at_saft.field_l10n_at_saft_account__code
msgid "Called \"Konto Nr\" in the Austrian SAF-T documentation"
msgstr "\"Konto Nr\" genannt in der österreichischen Vorgabe zum SAF-T"
#. module: l10n_at_saft
#. odoo-python
#: code:addons/l10n_at_saft/models/account_general_ledger.py:0
msgid "Check Accounts"
msgstr "Prüfe Accounts"

#. module: l10n_at_saft
#. odoo-python
#: code:addons/l10n_at_saft/models/account_general_ledger.py:0
msgid "Check Company"
msgstr "Prüfe Unternehmen"

#. module: l10n_at_saft
#. odoo-python
#: code:addons/l10n_at_saft/models/account_general_ledger.py:0
msgid "Check Partners"
msgstr "Prüfe Partner"

#. module: l10n_at_saft
#. odoo-python
#: code:addons/l10n_at_saft/models/account_general_ledger.py:0
msgid "Check Taxes"
msgstr "Prüfe Steuern"

#. module: l10n_at_saft
#: model:ir.model.fields,field_description:l10n_at_saft.field_l10n_at_saft_account__code
msgid "Code"
msgstr "Kontonummer"

#. module: l10n_at_saft
#: model:ir.model,name:l10n_at_saft.model_res_company
msgid "Companies"
msgstr "Unternehmen"

#. module: l10n_at_saft
#: model:ir.model,name:l10n_at_saft.model_res_config_settings
msgid "Config Settings"
msgstr "Konfigurationseinstellungen"

#. module: l10n_at_saft
#: model:ir.model.fields,field_description:l10n_at_saft.field_l10n_at_saft_account__create_uid
msgid "Created by"
msgstr "Erstellt von"

#. module: l10n_at_saft
#: model:ir.model.fields,field_description:l10n_at_saft.field_l10n_at_saft_account__create_date
msgid "Created on"
msgstr "Erstellt am"

#. module: l10n_at_saft
#: model:ir.model.fields,field_description:l10n_at_saft.field_l10n_at_saft_account__display_name
msgid "Display Name"
msgstr "Anzeigename"

#. module: l10n_at_saft
#: model:ir.model,name:l10n_at_saft.model_account_general_ledger_report_handler
msgid "General Ledger Custom Handler"
msgstr "Benutzerdefinierter Hauptbuch-Handler"

#. module: l10n_at_saft
#. odoo-python
#: code:addons/l10n_at_saft/models/account_general_ledger.py:0
msgid "Go to Settings"
msgstr "Siehe Settings"

#. module: l10n_at_saft
#: model:ir.model.fields,field_description:l10n_at_saft.field_l10n_at_saft_account__id
msgid "ID"
msgstr "ID"

#. module: l10n_at_saft
#: model:ir.model,name:l10n_at_saft.model_l10n_at_saft_account
msgid ""
"Information for the SAF-T export about a virtual account from the chart of "
"accounts given in the Austrian SAF-T specification; each (accounting) "
"account has to be mapped to such a virtual account for the SAF-T export"
msgstr "Informationen für den SAF-T-Export über virtuelle Konten des Kontenrahmens aus der österreichischen Vorgabe zum SAF-T; jedes (Buchhaltungs) Konto muss auf so ein virtuelles Konto abgebildet werden"

#. module: l10n_at_saft
#: model:ir.model.fields,field_description:l10n_at_saft.field_l10n_at_saft_account__write_uid
msgid "Last Updated by"
msgstr "Zuletzt aktualisiert durch"

#. module: l10n_at_saft
#: model:ir.model.fields,field_description:l10n_at_saft.field_l10n_at_saft_account__write_date
msgid "Last Updated on"
msgstr "Zuletzt aktualisiert am"

#. module: l10n_at_saft
#: model:ir.model,name:l10n_at_saft.model_account_report_file_download_error_wizard
msgid "Manage the file generation errors from report exports."
msgstr "Verwaltung von Fehlern bei der Datenerzeugung beim Exportieren von Berichten"

#. module: l10n_at_saft
#: model:ir.model.fields,field_description:l10n_at_saft.field_l10n_at_saft_account__name
msgid "Name"
msgstr "Name"

#. module: l10n_at_saft
#: model_terms:ir.ui.view,arch_db:l10n_at_saft.saft_template_inherit_l10n_at_saft
msgid "Odoo"
msgstr "Odoo"

#. module: l10n_at_saft
#. odoo-python
#: code:addons/l10n_at_saft/models/account_general_ledger.py:0
msgid "Please define a phone or mobile phone number for your company contact:"
msgstr "Bitte tragen Sie eine Telefon- oder Handynummer in den Kontakt Ihres Unternehmens ein:"

#. module: l10n_at_saft
#. odoo-python
#: code:addons/l10n_at_saft/models/account_general_ledger.py:0
msgid "Please define the %s in the accounting settings:"
msgstr "Bitte konfigurieren Sie %s in den Einstellungen zu Finanzen"

#. module: l10n_at_saft
#: model:ir.model.fields,field_description:l10n_at_saft.field_res_company__l10n_at_profit_assessment_method
#: model:ir.model.fields,field_description:l10n_at_saft.field_res_config_settings__l10n_at_profit_assessment_method
msgid "Profit Assessment Method"
msgstr "Gewinnermittlungsmethode"

#. module: l10n_at_saft
#. odoo-python
#: code:addons/l10n_at_saft/models/account_general_ledger.py:0
msgid "SAF-T"
msgstr "SAF-T"

#. module: l10n_at_saft
#: model_terms:ir.ui.view,arch_db:l10n_at_saft.res_config_settings_form_inherit_l10n_at_saft
msgid "SAF-T Export"
msgstr "SAF-T Export"

#. module: l10n_at_saft
#. odoo-python
#: code:addons/l10n_at_saft/models/account_general_ledger.py:0
msgid ""
"Some accounts can not be mapped to an account from the chart of accounts "
"given in the SAF-T specification (see the documentation for more "
"information):"
msgstr "Einige Konten können nicht in den Kontenrahmen aus der Vorgabe zum österreichischen SAF-T eingeordnet werden (s. Dokumentation):"

#. module: l10n_at_saft
#. odoo-python
#: code:addons/l10n_at_saft/models/account_general_ledger.py:0
msgid "Taxes that are not percentages are not supported."
msgstr "Nicht-prozentuale Steuern werden momentan nicht unterstützt."

#. module: l10n_at_saft
#. odoo-python
#: code:addons/l10n_at_saft/models/account_general_ledger.py:0
msgid ""
"The addresses (street, city, postal code, country) of some partners are "
"incomplete:"
msgstr "Die Adressen (Straße, Stadt, Postleitzahl, Land) der folgenden Partner sind unvollständig:"

#. module: l10n_at_saft
#. odoo-python
#: code:addons/l10n_at_saft/models/account_general_ledger.py:0
msgid "Unsupported taxes"
msgstr "Nicht-unterstützte Steuern"

#. module: l10n_at_saft
#. odoo-python
#: code:addons/l10n_at_saft/models/account_general_ledger.py:0
msgid "XML"
msgstr "XML"

#. module: l10n_at_saft
#. odoo-python
#: code:addons/l10n_at_saft/models/account_general_ledger.py:0
msgid "profit assessment method"
msgstr "Gewinnermittlungsmethode"

#. module: l10n_at_saft
#: model:ir.model.fields,field_description:l10n_at_saft.field_res_company__l10n_at_oenace_code
#: model:ir.model.fields,field_description:l10n_at_saft.field_res_config_settings__l10n_at_oenace_code
msgid "ÖNACE-Code"
msgstr "ÖNACE-Code"

#. module: l10n_at_saft
#: model:ir.model.fields,help:l10n_at_saft.field_res_company__l10n_at_oenace_code
#: model:ir.model.fields,help:l10n_at_saft.field_res_config_settings__l10n_at_oenace_code
msgid "ÖNACE-Code (Austrian Version of NACE-classification)"
msgstr "ÖNACE-Code (österreichische Version der NACE-Klassifikation)"

#. module: l10n_at_saft
#. odoo-python
#: code:addons/l10n_at_saft/models/account_general_ledger.py:0
msgid "ÖNACE-code"
msgstr "ÖNACE-Code"
