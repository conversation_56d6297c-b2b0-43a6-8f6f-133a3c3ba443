# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* pos_iot
# 
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# Wil Odoo, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-03-26 20:45+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Wil Odoo, 2025\n"
"Language-Team: Slovenian (https://app.transifex.com/odoo/teams/41243/sl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sl\n"
"Plural-Forms: nplurals=4; plural=(n%100==1 ? 0 : n%100==2 ? 1 : n%100==3 || n%100==4 ? 2 : 3);\n"

#. module: pos_iot
#: model_terms:ir.ui.view,arch_db:pos_iot.res_config_view_form_inherit_pos_iot
msgid "Accept payments with a Worldline payment terminal"
msgstr ""

#. module: pos_iot
#: model_terms:ir.ui.view,arch_db:pos_iot.res_config_view_form_inherit_pos_iot
msgid "Accept payments with an Ingenico payment terminal"
msgstr ""

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/app/iot_box_disconnected_dialog.xml:0
msgid "Access your"
msgstr ""

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/app/scale_certification_status/scale_certification_dialog.js:0
msgid "An error occurred while attempting to fix certification issues - %s"
msgstr ""

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/app/scale_certification_status/scale_certification_dialog.xml:0
msgid "Apply changes"
msgstr ""

#. module: pos_iot
#: model_terms:ir.ui.view,arch_db:pos_iot.res_config_view_form_inherit_pos_iot
msgid "Barcode Scanners/Card Readers"
msgstr ""

#. module: pos_iot
#: model:ir.model.fields,help:pos_iot.field_pos_config__iface_print_via_proxy
msgid "Bypass browser printing and prints via the hardware proxy."
msgstr ""

#. module: pos_iot
#: model_terms:ir.ui.view,arch_db:pos_iot.pos_iot_config_view_form
#: model_terms:ir.ui.view,arch_db:pos_iot.res_config_view_form_inherit_pos_iot
msgid "Cashdrawer"
msgstr "Blagajniški predal"

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/app/iot_box_disconnected_dialog.xml:0
msgid "Click on Advanced/Show Details/Details/More information"
msgstr ""

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/app/iot_box_disconnected_dialog.xml:0
msgid ""
"Click on Proceed to .../Add Exception/Visit this website/Go on to the "
"webpage"
msgstr ""

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/app/scale_certification_status/scale_certification_dialog.xml:0
msgid "Close"
msgstr "Zaključi"

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/app/iot_box_disconnected_dialog.xml:0
msgid "Close this window and try again"
msgstr ""

#. module: pos_iot
#: model:ir.model,name:pos_iot.model_res_config_settings
msgid "Config Settings"
msgstr "Uredi nastavitve"

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/app/payment.js:0
msgid "Configuration of payment terminal failed"
msgstr ""

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/app/payment.js:0
msgid "Connection to IoT Box failed"
msgstr ""

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/app/payment.js:0
msgid "Connection to terminal failed"
msgstr ""

#. module: pos_iot
#: model_terms:ir.ui.view,arch_db:pos_iot.pos_iot_config_view_form
#: model_terms:ir.ui.view,arch_db:pos_iot.res_config_view_form_inherit_pos_iot
msgid "Customer Display"
msgstr "Prikaz strank"

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/app/scale_certification_status/scale_certification_status.js:0
msgid "Decimal accuracy is less than 3 decimal places"
msgstr ""

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/app/scale_certification_status/scale_certification_dialog.xml:0
msgid "Discard"
msgstr "Opusti"

#. module: pos_iot
#: model:ir.model.fields,field_description:pos_iot.field_pos_config__iface_electronic_scale
#: model_terms:ir.ui.view,arch_db:pos_iot.pos_iot_config_view_form
#: model_terms:ir.ui.view,arch_db:pos_iot.res_config_view_form_inherit_pos_iot
msgid "Electronic Scale"
msgstr "Elektronska tehtnica"

#. module: pos_iot
#: model:ir.model.fields,help:pos_iot.field_pos_config__iface_scan_via_proxy
#: model:ir.model.fields,help:pos_iot.field_pos_config__iface_scanner_ids
#: model:ir.model.fields,help:pos_iot.field_res_config_settings__pos_iface_scanner_ids
msgid ""
"Enable barcode scanning with a remotely connected barcode scanner and card "
"swiping with a Vantiv card reader."
msgstr ""

#. module: pos_iot
#: model:ir.model.fields,help:pos_iot.field_pos_config__iface_electronic_scale
msgid "Enables Electronic Scale integration."
msgstr ""

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/app/iot_box_disconnected_dialog.xml:0
msgid "Firefox only: Click on Confirm Security Exception"
msgstr ""

#. module: pos_iot
#: model:ir.model,name:pos_iot.model_iot_device
msgid "IOT Device"
msgstr ""

#. module: pos_iot
#: model:ir.model.fields,field_description:pos_iot.field_pos_printer__device_identifier
msgid "Identifier"
msgstr "Identifikator"

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/app/iot_box_disconnected_dialog.xml:0
msgid ""
"If you are on a secure server (HTTPS) check if you accepted the certificate:"
msgstr ""

#. module: pos_iot
#: model:ir.model.fields,field_description:pos_iot.field_pos_config__iface_display_id
#: model:ir.model.fields,field_description:pos_iot.field_res_config_settings__pos_iface_display_id
msgid "Iface Display"
msgstr ""

#. module: pos_iot
#: model:ir.model.fields,field_description:pos_iot.field_pos_config__iface_printer_id
#: model:ir.model.fields,field_description:pos_iot.field_res_config_settings__pos_iface_printer_id
msgid "Iface Printer"
msgstr ""

#. module: pos_iot
#: model:ir.model.fields,field_description:pos_iot.field_pos_config__iface_scale_id
#: model:ir.model.fields,field_description:pos_iot.field_res_config_settings__pos_iface_scale_id
msgid "Iface Scale"
msgstr ""

#. module: pos_iot
#: model:ir.model.fields,field_description:pos_iot.field_pos_config__iface_scanner_ids
#: model:ir.model.fields,field_description:pos_iot.field_res_config_settings__pos_iface_scanner_ids
msgid "Iface Scanner"
msgstr ""

#. module: pos_iot
#: model_terms:ir.ui.view,arch_db:pos_iot.res_config_view_form_inherit_pos_iot
msgid "Ingenico (BENELUX)"
msgstr ""

#. module: pos_iot
#: model:ir.model.fields,field_description:pos_iot.field_res_config_settings__ingenico_payment_terminal
msgid "Ingenico Payment Terminal"
msgstr ""

#. module: pos_iot
#: model:ir.model,name:pos_iot.model_iot_box
msgid "IoT Box"
msgstr "IoT Box"

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/app/iot_box_disconnected_dialog.xml:0
msgid "IoT Box Homepage"
msgstr "Domača stran IoT Škatle"

#. module: pos_iot
#: model:ir.model.fields,field_description:pos_iot.field_pos_printer__device_id
msgid "IoT Device"
msgstr "IoT naprava"

#. module: pos_iot
#: model_terms:ir.ui.view,arch_db:pos_iot.pos_iot_config_view_form
#: model_terms:ir.ui.view,arch_db:pos_iot.res_config_view_form_inherit_pos_iot
msgid "IoT Devices"
msgstr ""

#. module: pos_iot
#: model:ir.model.fields,field_description:pos_iot.field_pos_config__iot_device_ids
msgid "Iot Device"
msgstr ""

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/app/iot_box_disconnected_dialog.xml:0
msgid "Network Error"
msgstr ""

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/app/scale_certification_status/scale_certification_dialog.xml:0
msgid ""
"Odoo can automatically apply the right modification for you.\n"
"                Be aware that the modification will change global rounding settings, and can affect more than just this Point of Sale."
msgstr ""

#. module: pos_iot
#: model:ir.model.fields,field_description:pos_iot.field_pos_payment_method__payment_terminal_ids
msgid "Payment Terminal"
msgstr "Plačilni terminal"

#. module: pos_iot
#: model:ir.model.fields,field_description:pos_iot.field_pos_config__payment_terminal_device_ids
#: model:ir.model.fields,field_description:pos_iot.field_pos_payment_method__iot_device_id
msgid "Payment Terminal Device"
msgstr ""

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/app/payment.js:0
msgid "Payment terminal error"
msgstr ""

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/app/iot_box_disconnected_dialog.xml:0
#: code:addons/pos_iot/static/src/app/payment.js:0
msgid "Please check if the IoT Box is still connected."
msgstr ""

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/app/payment.js:0
msgid "Please check if the terminal is still connected."
msgstr ""

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/app/payment.js:0
msgid ""
"Please check the network connection and then check the status of the last "
"transaction manually."
msgstr ""

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/overrides/models/pos_store.js:0
msgid "Please process or cancel the current transaction."
msgstr ""

#. module: pos_iot
#: model:ir.model,name:pos_iot.model_pos_config
msgid "Point of Sale Configuration"
msgstr "Nastavitve POS-blagajne"

#. module: pos_iot
#: model:ir.model,name:pos_iot.model_pos_payment_method
msgid "Point of Sale Payment Methods"
msgstr ""

#. module: pos_iot
#: model:ir.model,name:pos_iot.model_pos_printer
msgid "Point of Sale Printer"
msgstr ""

#. module: pos_iot
#: model:ir.model,name:pos_iot.model_pos_session
msgid "Point of Sale Session"
msgstr "Seja POS"

#. module: pos_iot
#: model:ir.model.fields,field_description:pos_iot.field_pos_config__iface_print_via_proxy
msgid "Print via Proxy"
msgstr "Natisni preko proxy"

#. module: pos_iot
#: model:ir.model.fields,field_description:pos_iot.field_pos_printer__proxy_ip
msgid "Proxy IP Address"
msgstr "IP naslov Proxy strežnika"

#. module: pos_iot
#. odoo-python
#: code:addons/pos_iot/models/pos_printer.py:0
msgid "Proxy IP cannot be empty."
msgstr ""

#. module: pos_iot
#: model_terms:ir.ui.view,arch_db:pos_iot.pos_iot_config_view_form
#: model_terms:ir.ui.view,arch_db:pos_iot.res_config_view_form_inherit_pos_iot
msgid "Receipt Printer"
msgstr ""

#. module: pos_iot
#: model:ir.model.fields,field_description:pos_iot.field_pos_config__iface_scan_via_proxy
msgid "Scan via Proxy"
msgstr "Skeniraj preko proxy"

#. module: pos_iot
#: model_terms:ir.ui.view,arch_db:pos_iot.pos_iot_config_view_form
msgid "Scanner"
msgstr "Optični čitalnik"

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/app/payment.js:0
msgid "Terminal Disconnected"
msgstr ""

#. module: pos_iot
#: model:ir.model.fields,help:pos_iot.field_pos_printer__proxy_ip
msgid "The IP Address or hostname of the Printer's hardware proxy"
msgstr "Gostitelj ali IP naslov proxy strežnika za strojno opremo."

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/app/scale_certification_status/scale_certification_status.js:0
msgid "The following units of measure have insufficient rounding accuracy: %s"
msgstr ""

#. module: pos_iot
#: model_terms:ir.ui.view,arch_db:pos_iot.res_config_view_form_inherit_pos_iot
msgid ""
"The transactions are processed by Ingenico. Set your Ingenico device on the "
"related payment method."
msgstr ""

#. module: pos_iot
#: model:ir.model.fields,help:pos_iot.field_res_config_settings__worldline_payment_terminal
#: model_terms:ir.ui.view,arch_db:pos_iot.res_config_view_form_inherit_pos_iot
msgid ""
"The transactions are processed by Worldline. Set your Worldline device on "
"the related payment method."
msgstr ""

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/app/scale_certification_status/scale_certification_status.xml:0
msgid "This Point of Sale is EU Certified for weighing scales."
msgstr ""

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/app/scale_certification_status/scale_certification_status.xml:0
msgid "This Point of Sale is uncertified for weighing scales."
msgstr ""

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/app/payment.js:0
msgid "Transaction could not be cancelled"
msgstr ""

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/overrides/models/pos_store.js:0
msgid "Transaction in progress"
msgstr ""

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/app/scale_certification_status/scale_certification_dialog.xml:0
msgid "Warning"
msgstr "Opozorilo"

#. module: pos_iot
#: model_terms:ir.ui.view,arch_db:pos_iot.res_config_view_form_inherit_pos_iot
msgid "Worldline (BENELUX)"
msgstr ""

#. module: pos_iot
#: model:ir.model.fields,field_description:pos_iot.field_res_config_settings__worldline_payment_terminal
msgid "Worldline Payment Terminal"
msgstr ""

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/app/payment.js:0
msgid "You must select a payment terminal in your POS config."
msgstr ""

#. module: pos_iot
#. odoo-python
#: code:addons/pos_iot/models/pos_config.py:0
msgid ""
"You must set a display device for an IOT-connected screen. You'll find the "
"field under the 'IoT Box' option."
msgstr ""

#. module: pos_iot
#. odoo-javascript
#: code:addons/pos_iot/static/src/app/scale_certification_status/scale_certification_dialog.xml:0
msgid ""
"Your configurations are not compliant with the European regulation. In order"
" to use a certified POS with your scale, you should address the following:"
msgstr ""
