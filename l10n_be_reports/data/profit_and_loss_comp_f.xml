<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data auto_sequence="1">
        <record id="account_financial_report_pl_comp_f" model="account.report">
            <field name="name">Profit and Loss (Full)</field>
            <field name="root_report_id" ref="account_reports.profit_and_loss"/>
            <field name="availability_condition">coa</field>
            <field name="chart_template">be_comp</field>
            <field name="filter_unfold_all" eval="True"/>
            <field name="filter_analytic_groupby" eval="True"/>
            <field name="filter_journals" eval="True"/>
            <field name="country_id" ref="base.be"/>
            <field name="filter_multi_company">selector</field>
            <field name="column_ids">
                <record id="account_financial_report_pl_comp_f_column" model="account.report.column">
                    <field name="name">Balance</field>
                    <field name="expression_label">balance</field>
                </record>
            </field>
            <field name="line_ids">
                <record id="account_financial_report_pl_comp_f_pl" model="account.report.line">
                    <field name="name">PROFIT AND LOSS ACCOUNT</field>
                    <field name="hierarchy_level">0</field>
                    <field name="children_ids">
                        <record id="account_financial_report_pl_comp_f_pl_oi" model="account.report.line">
                            <field name="name">70/76A - Operating Income</field>
                            <field name="code">BE_PL_COMP_F_70_76A</field>
                            <field name="hierarchy_level">1</field>
                            <field name="aggregation_formula">BE_PL_70.balance + BE_PL_F_71.balance + BE_PL_F_72.balance + BE_PL_F_74.balance + BE_PL_76A.balance</field>
                            <field name="children_ids">
                                <record id="account_financial_report_pl_comp_f_pl_oi_t" model="account.report.line">
                                    <field name="name">70 - Turnover</field>
                                    <field name="code">BE_PL_70</field>
                                    <field name="groupby">account_id</field>
                                    <field name="foldable" eval="True"/>
                                    <field name="account_codes_formula">-70</field>
                                </record>
                                <record id="account_financial_report_pl_comp_f_pl_oi_sfgwcp" model="account.report.line">
                                    <field name="name">71 - Stocks of Finished Goods and Work and Contracts in Progress: Increase (Decrease) (+)/(-)</field>
                                    <field name="code">BE_PL_F_71</field>
                                    <field name="groupby">account_id</field>
                                    <field name="foldable" eval="True"/>
                                    <field name="account_codes_formula">-71</field>
                                </record>
                                <record id="account_financial_report_pl_comp_f_pl_oi_pfa" model="account.report.line">
                                    <field name="name">72 - Produced Fixed Assets</field>
                                    <field name="code">BE_PL_F_72</field>
                                    <field name="groupby">account_id</field>
                                    <field name="foldable" eval="True"/>
                                    <field name="account_codes_formula">-72</field>
                                </record>
                                <record id="account_financial_report_pl_comp_f_pl_oi_ooi" model="account.report.line">
                                    <field name="name">74 - Other Operating Income</field>
                                    <field name="code">BE_PL_F_74</field>
                                    <field name="groupby">account_id</field>
                                    <field name="foldable" eval="True"/>
                                    <field name="account_codes_formula">-74</field>
                                </record>
                                <record id="account_financial_report_pl_comp_f_pl_oi_nroi" model="account.report.line">
                                    <field name="name">76A - Non-Recurring Operating Income</field>
                                    <field name="code">BE_PL_76A</field>
                                    <field name="groupby">account_id</field>
                                    <field name="foldable" eval="True"/>
                                    <field name="account_codes_formula">-760 - 7620 - 7630 - 764 - 765 - 766 - 767 - 768</field>
                                </record>
                            </field>
                        </record>
                        <record id="account_financial_report_pl_comp_f_pl_oc" model="account.report.line">
                            <field name="name">60/66A - Operating Charges</field>
                            <field name="code">BE_PL_COMP_F_60_66A</field>
                            <field name="hierarchy_level">1</field>
                            <field name="expression_ids">
                                <record id="account_financial_report_pl_comp_f_pl_oc_balance" model="account.report.expression">
                                    <field name="label">balance</field>
                                    <field name="engine">aggregation</field>
                                    <field name="formula">BE_PL_F_60.balance + BE_PL_F_61.balance + BE_PL_62.balance + BE_PL_630.balance + BE_PL_631_4.balance + BE_PL_COMP_635_8.balance + BE_PL_640_8.balance + BE_PL_649.balance + BE_PL_66A.balance</field>
                                    <field name="green_on_positive" eval="False"/>
                                </record>
                            </field>
                            <field name="children_ids">
                                <record id="account_financial_report_pl_comp_f_pl_oc_grrmc" model="account.report.line">
                                    <field name="name">60 - Goods for Resale, Raw Materials and Consumables</field>
                                    <field name="code">BE_PL_F_60</field>
                                    <field name="foldable" eval="True"/>
                                    <field name="expression_ids">
                                        <record id="account_financial_report_pl_comp_f_pl_oc_grrmc_balance" model="account.report.expression">
                                            <field name="label">balance</field>
                                            <field name="engine">aggregation</field>
                                            <field name="formula">BE_PL_F_600_8.balance + BE_PL_F_609.balance</field>
                                            <field name="green_on_positive" eval="False"/>
                                        </record>
                                    </field>
                                    <field name="children_ids">
                                        <record id="account_financial_report_pl_comp_f_pl_oc_grrmc_p" model="account.report.line">
                                            <field name="name">600/8 - Purchases</field>
                                            <field name="code">BE_PL_F_600_8</field>
                                            <field name="groupby">account_id</field>
                                            <field name="foldable" eval="True"/>
                                            <field name="expression_ids">
                                                <record id="account_financial_report_pl_comp_f_pl_oc_grrmc_p_balance" model="account.report.expression">
                                                    <field name="label">balance</field>
                                                    <field name="engine">account_codes</field>
                                                    <field name="formula">600 + 601 + 602 + 603 + 604 + 605 + 606 + 607 + 608</field>
                                                    <field name="green_on_positive" eval="False"/>
                                                </record>
                                            </field>
                                        </record>
                                        <record id="account_financial_report_pl_comp_f_pl_oc_grrmc_s" model="account.report.line">
                                            <field name="name">609 - Stocks: Decrease (Increase) (+)/(-)</field>
                                            <field name="code">BE_PL_F_609</field>
                                            <field name="groupby">account_id</field>
                                            <field name="foldable" eval="True"/>
                                            <field name="expression_ids">
                                                <record id="account_financial_report_pl_comp_f_pl_oc_grrmc_s_balance" model="account.report.expression">
                                                    <field name="label">balance</field>
                                                    <field name="engine">account_codes</field>
                                                    <field name="formula">609</field>
                                                    <field name="green_on_positive" eval="False"/>
                                                </record>
                                            </field>
                                        </record>
                                    </field>
                                </record>
                                <record id="account_financial_report_pl_comp_f_pl_oc_sog" model="account.report.line">
                                    <field name="name">61 - Services and Other Goods</field>
                                    <field name="code">BE_PL_F_61</field>
                                    <field name="groupby">account_id</field>
                                    <field name="foldable" eval="True"/>
                                    <field name="expression_ids">
                                        <record id="account_financial_report_pl_comp_f_pl_oc_sog_balance" model="account.report.expression">
                                            <field name="label">balance</field>
                                            <field name="engine">account_codes</field>
                                            <field name="formula">61</field>
                                            <field name="green_on_positive" eval="False"/>
                                        </record>
                                    </field>
                                </record>
                                <record id="account_financial_report_pl_comp_f_pl_oc_rssp" model="account.report.line">
                                    <field name="name">62 - Remuneration, Social Security and Pensions (+)/(-)</field>
                                    <field name="code">BE_PL_62</field>
                                    <field name="groupby">account_id</field>
                                    <field name="foldable" eval="True"/>
                                    <field name="expression_ids">
                                        <record id="account_financial_report_pl_comp_f_pl_oc_rssp_balance" model="account.report.expression">
                                            <field name="label">balance</field>
                                            <field name="engine">account_codes</field>
                                            <field name="formula">62</field>
                                            <field name="green_on_positive" eval="False"/>
                                        </record>
                                    </field>
                                </record>
                                <record id="account_financial_report_pl_comp_f_pl_oc_aoawdfeitfa" model="account.report.line">
                                    <field name="name">630 - Amortisations of and Other Amounts Written Down on Formation Expenses, Intangible and Tangible Fixed Assets</field>
                                    <field name="code">BE_PL_630</field>
                                    <field name="groupby">account_id</field>
                                    <field name="foldable" eval="True"/>
                                    <field name="expression_ids">
                                        <record id="account_financial_report_pl_comp_f_pl_oc_aoawdfeitfa_balance" model="account.report.expression">
                                            <field name="label">balance</field>
                                            <field name="engine">account_codes</field>
                                            <field name="formula">630</field>
                                            <field name="green_on_positive" eval="False"/>
                                        </record>
                                    </field>
                                </record>
                                <record id="account_financial_report_pl_comp_f_pl_oc_awdscptd" model="account.report.line">
                                    <field name="name">631/4 - Amounts Written Down on Stocks, Contracts in Progress and Trade Debtors: Additions (Write-Backs) (+)/(-)</field>
                                    <field name="code">BE_PL_631_4</field>
                                    <field name="groupby">account_id</field>
                                    <field name="foldable" eval="True"/>
                                    <field name="expression_ids">
                                        <record id="account_financial_report_pl_comp_f_pl_oc_awdscptd_balance" model="account.report.expression">
                                            <field name="label">balance</field>
                                            <field name="engine">account_codes</field>
                                            <field name="formula">631 + 632 + 633 + 634</field>
                                            <field name="green_on_positive" eval="False"/>
                                        </record>
                                    </field>
                                </record>
                                <record id="account_financial_report_pl_comp_f_pl_oc_plc" model="account.report.line">
                                    <field name="name">635/8 - Provisions for Liabilities and Charges: Appropriations (Uses and Write-Backs) (+)/(-)</field>
                                    <field name="code">BE_PL_COMP_635_8</field>
                                    <field name="groupby">account_id</field>
                                    <field name="foldable" eval="True"/>
                                    <field name="expression_ids">
                                        <record id="account_financial_report_pl_comp_f_pl_oc_plc_balance" model="account.report.expression">
                                            <field name="label">balance</field>
                                            <field name="engine">account_codes</field>
                                            <field name="formula">635 + 636 + 637 + 638</field>
                                            <field name="green_on_positive" eval="False"/>
                                        </record>
                                    </field>
                                </record>
                                <record id="account_financial_report_pl_comp_f_pl_oc_ooc" model="account.report.line">
                                    <field name="name">640/8 - Other Operating Charges</field>
                                    <field name="code">BE_PL_640_8</field>
                                    <field name="groupby">account_id</field>
                                    <field name="foldable" eval="True"/>
                                    <field name="expression_ids">
                                        <record id="account_financial_report_pl_comp_f_pl_oc_ooc_balance" model="account.report.expression">
                                            <field name="label">balance</field>
                                            <field name="engine">account_codes</field>
                                            <field name="formula">640 + 641 + 642 + 643 + 644 + 645 + 646 + 647 + 648</field>
                                            <field name="green_on_positive" eval="False"/>
                                        </record>
                                    </field>
                                </record>
                                <record id="account_financial_report_pl_comp_f_pl_oc_ocrarc" model="account.report.line">
                                    <field name="name">649 - Operating Charges Reported as Assets Under Restructuring Costs (-)</field>
                                    <field name="code">BE_PL_649</field>
                                    <field name="groupby">account_id</field>
                                    <field name="foldable" eval="True"/>
                                    <field name="expression_ids">
                                        <record id="account_financial_report_pl_comp_f_pl_oc_ocrarc_balance" model="account.report.expression">
                                            <field name="label">balance</field>
                                            <field name="engine">account_codes</field>
                                            <field name="formula">649</field>
                                            <field name="green_on_positive" eval="False"/>
                                        </record>
                                    </field>
                                </record>
                                <record id="account_financial_report_pl_comp_f_pl_oc_nroc" model="account.report.line">
                                    <field name="name">66A - Non-Recurring Operating Charges</field>
                                    <field name="code">BE_PL_66A</field>
                                    <field name="groupby">account_id</field>
                                    <field name="foldable" eval="True"/>
                                    <field name="expression_ids">
                                        <record id="account_financial_report_pl_comp_f_pl_oc_nroc_balance" model="account.report.expression">
                                            <field name="label">balance</field>
                                            <field name="engine">account_codes</field>
                                            <field name="formula">660 + 6620 + 6630 + 664 + 665 + 666 + 667 + 6690</field>
                                            <field name="green_on_positive" eval="False"/>
                                        </record>
                                    </field>
                                </record>
                            </field>
                        </record>
                        <record id="account_financial_report_pl_comp_f_pl_opl" model="account.report.line">
                            <field name="name">9901 - Operating Profit (Loss) (+)/(-)</field>
                            <field name="hierarchy_level">1</field>
                            <field name="code">BE_PL_COMP_F_9901</field>
                            <field name="aggregation_formula">BE_PL_COMP_F_70_76A.balance - BE_PL_COMP_F_60_66A.balance</field>
                        </record>
                        <record id="account_financial_report_pl_comp_f_pl_comp_fi" model="account.report.line">
                            <field name="name">75/76B - Financial Income</field>
                            <field name="hierarchy_level">1</field>
                            <field name="print_on_new_page" eval="True"/>
                            <field name="code">BE_PL_F_75_76B</field>
                            <field name="aggregation_formula">BE_PL_F_75.balance + BE_PL_76B.balance</field>
                            <field name="children_ids">
                                <record id="account_financial_report_pl_comp_f_pl_comp_fi_rfi" model="account.report.line">
                                    <field name="name">75 - Recurring Financial Income</field>
                                    <field name="code">BE_PL_F_75</field>
                                    <field name="foldable" eval="True"/>
                                    <field name="aggregation_formula">BE_PL_F_750.balance + BE_PL_F_751.balance + BE_PL_F_752_9.balance</field>
                                    <field name="children_ids">
                                        <record id="account_financial_report_pl_comp_f_pl_comp_fi_rfi_iffa" model="account.report.line">
                                            <field name="name">750 - Income From Financial Fixed Assets</field>
                                            <field name="code">BE_PL_F_750</field>
                                            <field name="groupby">account_id</field>
                                            <field name="foldable" eval="True"/>
                                            <field name="account_codes_formula">-750</field>
                                        </record>
                                        <record id="account_financial_report_pl_comp_f_pl_comp_fi_rfi_ica" model="account.report.line">
                                            <field name="name">751 - Income From Current Assets</field>
                                            <field name="code">BE_PL_F_751</field>
                                            <field name="groupby">account_id</field>
                                            <field name="foldable" eval="True"/>
                                            <field name="account_codes_formula">-751</field>
                                        </record>
                                        <record id="account_financial_report_pl_comp_f_pl_comp_fi_rfi_ofi" model="account.report.line">
                                            <field name="name">752/9 - Other Financial Income</field>
                                            <field name="code">BE_PL_F_752_9</field>
                                            <field name="groupby">account_id</field>
                                            <field name="foldable" eval="True"/>
                                            <field name="account_codes_formula">-752 - 753 - 754 - 755 - 756 - 757 - 758 - 759</field>
                                        </record>
                                    </field>
                                </record>
                                <record id="account_financial_report_pl_comp_f_pl_comp_fi_nrfi" model="account.report.line">
                                    <field name="name">76B - Non-Recurring Financial Income</field>
                                    <field name="code">BE_PL_76B</field>
                                    <field name="groupby">account_id</field>
                                    <field name="foldable" eval="True"/>
                                    <field name="account_codes_formula">-761 - 7621 - 763\(7630) - 769</field>
                                </record>
                            </field>
                        </record>
                        <record id="account_financial_report_pl_comp_f_pl_comp_fc" model="account.report.line">
                            <field name="name">65/66B - Financial Charges</field>
                            <field name="hierarchy_level">1</field>
                            <field name="code">BE_PL_F_65_66B</field>
                            <field name="expression_ids">
                                <record id="account_financial_report_pl_comp_f_pl_comp_fc_balance" model="account.report.expression">
                                    <field name="label">balance</field>
                                    <field name="engine">aggregation</field>
                                    <field name="formula">BE_PL_F_65.balance + BE_PL_66B.balance</field>
                                    <field name="green_on_positive" eval="False"/>
                                </record>
                            </field>
                            <field name="children_ids">
                                <record id="account_financial_report_pl_comp_f_pl_comp_fc_rfc" model="account.report.line">
                                    <field name="name">65 - Recurring Financial Charges</field>
                                    <field name="code">BE_PL_F_65</field>
                                    <field name="foldable" eval="True"/>
                                    <field name="expression_ids">
                                        <record id="account_financial_report_pl_comp_f_pl_comp_fc_rfc_balance" model="account.report.expression">
                                            <field name="label">balance</field>
                                            <field name="engine">aggregation</field>
                                            <field name="formula">BE_PL_F_650.balance + BE_PL_F_651.balance + BE_PL_F_652_9.balance</field>
                                            <field name="green_on_positive" eval="False"/>
                                        </record>
                                    </field>
                                    <field name="children_ids">
                                        <record id="account_financial_report_pl_comp_f_pl_comp_fc_rfc_dc" model="account.report.line">
                                            <field name="name">650 - Debt Charges</field>
                                            <field name="code">BE_PL_F_650</field>
                                            <field name="groupby">account_id</field>
                                            <field name="foldable" eval="True"/>
                                            <field name="expression_ids">
                                                <record id="account_financial_report_pl_comp_f_pl_comp_fc_rfc_dc_balance" model="account.report.expression">
                                                    <field name="label">balance</field>
                                                    <field name="engine">account_codes</field>
                                                    <field name="formula">650</field>
                                                    <field name="green_on_positive" eval="False"/>
                                                </record>
                                            </field>
                                        </record>
                                        <record id="account_financial_report_pl_comp_f_pl_comp_fc_rfc_awdcaoscptd" model="account.report.line">
                                            <field name="name">651 - Amounts Written Down on Current Assets Other Than Stocks, Contracts in Progress and Trade Debtors: Additions (Write-Backs) (+)/(-)</field>
                                            <field name="code">BE_PL_F_651</field>
                                            <field name="groupby">account_id</field>
                                            <field name="foldable" eval="True"/>
                                            <field name="expression_ids">
                                                <record id="account_financial_report_pl_comp_f_pl_comp_fc_rfc_awdcaoscptd_balance" model="account.report.expression">
                                                    <field name="label">balance</field>
                                                    <field name="engine">account_codes</field>
                                                    <field name="formula">651</field>
                                                    <field name="green_on_positive" eval="False"/>
                                                </record>
                                            </field>
                                        </record>
                                        <record id="account_financial_report_pl_comp_f_pl_comp_fc_rfc_ofc" model="account.report.line">
                                            <field name="name">652/9 - Other Financial Charges</field>
                                            <field name="code">BE_PL_F_652_9</field>
                                            <field name="groupby">account_id</field>
                                            <field name="foldable" eval="True"/>
                                            <field name="expression_ids">
                                                <record id="account_financial_report_pl_comp_f_pl_comp_fc_rfc_ofc_balance" model="account.report.expression">
                                                    <field name="label">balance</field>
                                                    <field name="engine">account_codes</field>
                                                    <field name="formula">652 + 653 + 654 + 655 + 656 + 657 + 658 + 659</field>
                                                    <field name="green_on_positive" eval="False"/>
                                                </record>
                                            </field>
                                        </record>
                                    </field>
                                </record>
                                <record id="account_financial_report_pl_comp_f_pl_comp_fc_nrfc" model="account.report.line">
                                    <field name="name">66B - Non-Recurring Financial Charges</field>
                                    <field name="code">BE_PL_66B</field>
                                    <field name="groupby">account_id</field>
                                    <field name="foldable" eval="True"/>
                                    <field name="expression_ids">
                                        <record id="account_financial_report_pl_comp_f_pl_comp_fc_nrfc_balance" model="account.report.expression">
                                            <field name="label">balance</field>
                                            <field name="engine">account_codes</field>
                                            <field name="formula">661 + 6621 + 6631 + 668 + 6691</field>
                                            <field name="green_on_positive" eval="False"/>
                                        </record>
                                    </field>
                                </record>
                            </field>
                        </record>
                        <record id="account_financial_report_pl_comp_f_pl_plpbt" model="account.report.line">
                            <field name="name">9903 - Profit (Loss) for the Period Before Taxes (+)/(-)</field>
                            <field name="hierarchy_level">1</field>
                            <field name="code">BE_PL_COMP_F_9903</field>
                            <field name="aggregation_formula">BE_PL_COMP_F_9901.balance + BE_PL_F_75_76B.balance - BE_PL_F_65_66B.balance</field>
                        </record>
                        <record id="account_financial_report_pl_comp_f_pl_tfdt" model="account.report.line">
                            <field name="name">780 - Transfer From Deferred Taxes</field>
                            <field name="hierarchy_level">1</field>
                            <field name="code">BE_PL_780</field>
                            <field name="groupby">account_id</field>
                            <field name="foldable" eval="True"/>
                            <field name="account_codes_formula">-780</field>
                        </record>
                        <record id="account_financial_report_pl_comp_f_pl_ttdt" model="account.report.line">
                            <field name="name">680 - Transfer to Deferred Taxes</field>
                            <field name="hierarchy_level">1</field>
                            <field name="code">BE_PL_680</field>
                            <field name="groupby">account_id</field>
                            <field name="foldable" eval="True"/>
                            <field name="expression_ids">
                                <record id="account_financial_report_pl_comp_f_pl_ttdt_balance" model="account.report.expression">
                                    <field name="label">balance</field>
                                    <field name="engine">account_codes</field>
                                    <field name="formula">680</field>
                                    <field name="green_on_positive" eval="False"/>
                                </record>
                            </field>
                        </record>
                        <record id="account_financial_report_pl_comp_f_pl_itr" model="account.report.line">
                            <field name="name">67/77 - Income Taxes on the Result (+)/(-)</field>
                            <field name="hierarchy_level">1</field>
                            <field name="code">BE_PL_F_67_77</field>
                            <field name="expression_ids">
                                <record id="account_financial_report_pl_comp_f_pl_itr_balance" model="account.report.expression">
                                    <field name="label">balance</field>
                                    <field name="engine">aggregation</field>
                                    <field name="formula">BE_PL_F_670_3.balance - BE_PL_F_77.balance</field>
                                    <field name="green_on_positive" eval="False"/>
                                </record>
                            </field>
                            <field name="children_ids">
                                <record id="account_financial_report_pl_comp_f_pl_itr_t" model="account.report.line">
                                    <field name="name">670/3 - Taxes</field>
                                    <field name="code">BE_PL_F_670_3</field>
                                    <field name="groupby">account_id</field>
                                    <field name="foldable" eval="True"/>
                                    <field name="expression_ids">
                                        <record id="account_financial_report_pl_comp_f_pl_itr_t_balance" model="account.report.expression">
                                            <field name="label">balance</field>
                                            <field name="engine">account_codes</field>
                                            <field name="formula">670 + 671 + 672 + 673</field>
                                            <field name="green_on_positive" eval="False"/>
                                        </record>
                                    </field>
                                </record>
                                <record id="account_financial_report_pl_comp_f_pl_itr_aitwbtp" model="account.report.line">
                                    <field name="name">77 - Adjustment of Income Taxes and Write-Back of Tax Provisions</field>
                                    <field name="code">BE_PL_F_77</field>
                                    <field name="groupby">account_id</field>
                                    <field name="foldable" eval="True"/>
                                    <field name="account_codes_formula">-77</field>
                                </record>
                            </field>
                        </record>
                        <record id="account_financial_report_pl_comp_f_pl_plp" model="account.report.line">
                            <field name="name">9904 - Profit (Loss) of the Period (+)/(-)</field>
                            <field name="hierarchy_level">1</field>
                            <field name="code">BE_PL_COMP_F_9904</field>
                            <field name="aggregation_formula">BE_PL_COMP_F_9903.balance + BE_PL_780.balance - BE_PL_680.balance - BE_PL_F_67_77.balance</field>
                        </record>
                        <record id="account_financial_report_pl_comp_f_pl_tfur" model="account.report.line">
                            <field name="name">789 - Transfer From Untaxed Reserves</field>
                            <field name="hierarchy_level">1</field>
                            <field name="code">BE_PL_789</field>
                            <field name="groupby">account_id</field>
                            <field name="foldable" eval="True"/>
                            <field name="account_codes_formula">-789</field>
                        </record>
                        <record id="account_financial_report_pl_comp_f_pl_ttur" model="account.report.line">
                            <field name="name">689 - Transfer to Untaxed Reserves</field>
                            <field name="hierarchy_level">1</field>
                            <field name="code">BE_PL_689</field>
                            <field name="groupby">account_id</field>
                            <field name="foldable" eval="True"/>
                            <field name="expression_ids">
                                <record id="account_financial_report_pl_comp_f_pl_ttur_balance" model="account.report.expression">
                                    <field name="label">balance</field>
                                    <field name="engine">account_codes</field>
                                    <field name="formula">689</field>
                                    <field name="green_on_positive" eval="False"/>
                                </record>
                            </field>
                        </record>
                        <record id="account_financial_report_pl_comp_f_pl_plpa" model="account.report.line">
                            <field name="name">9905 - Profit (Loss) of the Period Available for Appropriation (+)/(-)</field>
                            <field name="hierarchy_level">1</field>
                            <field name="code">BE_PL_COMP_F_9905</field>
                            <field name="aggregation_formula">BE_PL_COMP_F_9904.balance + BE_PL_789.balance - BE_PL_689.balance</field>
                        </record>
                    </field>
                </record>
                <record id="account_financial_report_pl_comp_f_a" model="account.report.line">
                    <field name="name">APPROPRIATION ACCOUNT</field>
                    <field name="hierarchy_level">0</field>
                    <field name="print_on_new_page" eval="True"/>
                    <field name="children_ids">
                        <record id="account_financial_report_pl_comp_f_a_pltba" model="account.report.line">
                            <field name="name">9906 - Profit (Loss) to Be Appropriated (+)/(-)</field>
                            <field name="hierarchy_level">1</field>
                            <field name="code">BE_PL_COMP_F_9906</field>
                            <field name="aggregation_formula">BE_PL_COMP_F_9905.balance + BE_PL_14P.balance</field>
                            <field name="children_ids">
                                <record id="account_financial_report_pl_comp_f_a_pltba_plpafa" model="account.report.line">
                                    <field name="code">BE_PL_COMP_F_9905_C</field>
                                    <field name="name">(9905) - Profit (Loss) of the Period Available for Appropriation (+)/(-)</field>
                                    <field name="aggregation_formula">BE_PL_COMP_F_9905.balance</field>
                                </record>
                                <record id="account_financial_report_pl_comp_f_a_pltba_plppbf" model="account.report.line">
                                    <field name="name">14P - Profit (Loss) of the Preceding Period Brought Forward (+)/(-)</field>
                                    <field name="code">BE_PL_14P</field>
                                    <field name="groupby" eval="False"/>
                                    <field name="foldable" eval="False"/>
                                    <field name="expression_ids">
                                        <record id="account_financial_report_pl_comp_f_a_pltba_plppbf_balance" model="account.report.expression">
                                            <field name="label">balance</field>
                                            <field name="engine">account_codes</field>
                                            <field name="formula">-6 - 7 - 14</field>
                                            <field name="date_scope">to_beginning_of_period</field>
                                        </record>
                                    </field>
                                </record>
                            </field>
                        </record>
                        <record id="account_financial_report_pl_comp_f_a_tfe" model="account.report.line">
                            <field name="name">791/2 - Transfers From Equity</field>
                            <field name="hierarchy_level">1</field>
                            <field name="code">BE_PL_COMP_F_791_2</field>
                            <field name="aggregation_formula">BE_PL_COMP_F_791.balance + BE_PL_COMP_F_792.balance</field>
                            <field name="children_ids">
                                <record id="account_financial_report_pl_comp_f_a_tfe_tc" model="account.report.line">
                                    <field name="name">791 - From Contributions</field>
                                    <field name="code">BE_PL_COMP_F_791</field>
                                    <field name="groupby">account_id</field>
                                    <field name="foldable" eval="True"/>
                                    <field name="account_codes_formula">-791</field>
                                </record>
                                <record id="account_financial_report_pl_comp_f_a_tfe_fr" model="account.report.line">
                                    <field name="name">792 - From Reserves</field>
                                    <field name="code">BE_PL_COMP_F_792</field>
                                    <field name="groupby">account_id</field>
                                    <field name="foldable" eval="True"/>
                                    <field name="account_codes_formula">-792</field>
                                </record>
                            </field>
                        </record>
                        <record id="account_financial_report_pl_comp_f_a_a" model="account.report.line">
                            <field name="name">691/2 - Appropriations to Equity</field>
                            <field name="hierarchy_level">1</field>
                            <field name="code">BE_PL_COMP_691_2</field>
                            <field name="expression_ids">
                                <record id="account_financial_report_pl_comp_f_a_a_balance" model="account.report.expression">
                                    <field name="label">balance</field>
                                    <field name="engine">aggregation</field>
                                    <field name="formula">BE_PL_COMP_691.balance + BE_PL_COMP_6920.balance + BE_PL_COMP_6921.balance</field>
                                    <field name="green_on_positive" eval="False"/>
                                </record>
                            </field>
                            <field name="children_ids">
                                <record id="account_financial_report_pl_comp_f_a_a_tc" model="account.report.line">
                                    <field name="name">691 - To Contributions</field>
                                    <field name="code">BE_PL_COMP_691</field>
                                    <field name="groupby">account_id</field>
                                    <field name="foldable" eval="True"/>
                                    <field name="expression_ids">
                                        <record id="account_financial_report_pl_comp_f_a_a_tc_balance" model="account.report.expression">
                                            <field name="label">balance</field>
                                            <field name="engine">account_codes</field>
                                            <field name="formula">691</field>
                                            <field name="green_on_positive" eval="False"/>
                                        </record>
                                    </field>
                                </record>
                                <record id="account_financial_report_pl_comp_f_a_a_tlr" model="account.report.line">
                                    <field name="name">6920 - To Legal Reserve</field>
                                    <field name="code">BE_PL_COMP_6920</field>
                                    <field name="groupby">account_id</field>
                                    <field name="foldable" eval="True"/>
                                    <field name="expression_ids">
                                        <record id="account_financial_report_pl_comp_f_a_a_tlr_balance" model="account.report.expression">
                                            <field name="label">balance</field>
                                            <field name="engine">account_codes</field>
                                            <field name="formula">6920</field>
                                            <field name="green_on_positive" eval="False"/>
                                        </record>
                                    </field>
                                </record>
                                <record id="account_financial_report_pl_comp_f_a_a_tor" model="account.report.line">
                                    <field name="name">6921 - To Other Reserves</field>
                                    <field name="code">BE_PL_COMP_6921</field>
                                    <field name="groupby">account_id</field>
                                    <field name="foldable" eval="True"/>
                                    <field name="expression_ids">
                                        <record id="account_financial_report_pl_comp_f_a_a_tor_balance" model="account.report.expression">
                                            <field name="label">balance</field>
                                            <field name="engine">account_codes</field>
                                            <field name="formula">6921</field>
                                            <field name="green_on_positive" eval="False"/>
                                        </record>
                                    </field>
                                </record>
                            </field>
                        </record>
                        <record id="account_financial_report_pl_comp_f_a_plcf" model="account.report.line">
                            <field name="name">(14) - Profit (Loss) to Be Carried Forward (+)/(-)</field>
                            <field name="hierarchy_level">1</field>
                            <field name="code">BE_PL_COMP_F_14</field>
                            <field name="aggregation_formula">BE_PL_COMP_F_9906.balance + BE_PL_COMP_F_791_2.balance - BE_PL_COMP_691_2.balance + BE_PL_COMP_794.balance - BE_PL_COMP_694_7.balance</field>
                        </record>
                        <record id="account_financial_report_pl_comp_f_a_scirol" model="account.report.line">
                            <field name="name">794 - Shareholders' Contribution in Respect of Losses</field>
                            <field name="hierarchy_level">1</field>
                            <field name="code">BE_PL_COMP_794</field>
                            <field name="groupby">account_id</field>
                            <field name="foldable" eval="True"/>
                            <field name="account_codes_formula">-794</field>
                        </record>
                        <record id="account_financial_report_pl_comp_f_a_ptbd" model="account.report.line">
                            <field name="name">694/7 - Profit to Be Distributed</field>
                            <field name="hierarchy_level">1</field>
                            <field name="code">BE_PL_COMP_694_7</field>
                            <field name="expression_ids">
                                <record id="account_financial_report_pl_comp_f_a_ptbd_balance" model="account.report.expression">
                                    <field name="label">balance</field>
                                    <field name="engine">aggregation</field>
                                    <field name="formula">BE_PL_COMP_694.balance + BE_PL_COMP_695.balance + BE_PL_COMP_696.balance + BE_PL_COMP_697.balance</field>
                                    <field name="green_on_positive" eval="False"/>
                                </record>
                            </field>
                            <field name="children_ids">
                                <record id="account_financial_report_pl_comp_f_a_ptbd_cfc" model="account.report.line">
                                    <field name="name">694 - Compensation for Contributions</field>
                                    <field name="code">BE_PL_COMP_694</field>
                                    <field name="groupby">account_id</field>
                                    <field name="foldable" eval="True"/>
                                    <field name="expression_ids">
                                        <record id="account_financial_report_pl_comp_f_a_ptbd_cfc_balance" model="account.report.expression">
                                            <field name="label">balance</field>
                                            <field name="engine">account_codes</field>
                                            <field name="formula">694</field>
                                            <field name="green_on_positive" eval="False"/>
                                        </record>
                                    </field>
                                </record>
                                <record id="account_financial_report_pl_comp_f_a_ptbd_dom" model="account.report.line">
                                    <field name="name">695 - Directors or Managers</field>
                                    <field name="code">BE_PL_COMP_695</field>
                                    <field name="groupby">account_id</field>
                                    <field name="foldable" eval="True"/>
                                    <field name="expression_ids">
                                        <record id="account_financial_report_pl_comp_f_a_ptbd_dom_balance" model="account.report.expression">
                                            <field name="label">balance</field>
                                            <field name="engine">account_codes</field>
                                            <field name="formula">695</field>
                                            <field name="green_on_positive" eval="False"/>
                                        </record>
                                    </field>
                                </record>
                                <record id="account_financial_report_pl_comp_f_a_ptbd_e" model="account.report.line">
                                    <field name="name">696 - Employees</field>
                                    <field name="code">BE_PL_COMP_696</field>
                                    <field name="groupby">account_id</field>
                                    <field name="foldable" eval="True"/>
                                    <field name="expression_ids">
                                        <record id="account_financial_report_pl_comp_f_a_ptbd_e_balance" model="account.report.expression">
                                            <field name="label">balance</field>
                                            <field name="engine">account_codes</field>
                                            <field name="formula">696</field>
                                            <field name="green_on_positive" eval="False"/>
                                        </record>
                                    </field>
                                </record>
                                <record id="account_financial_report_pl_comp_f_a_ptbd_ob" model="account.report.line">
                                    <field name="name">697 - Other Beneficiaries</field>
                                    <field name="code">BE_PL_COMP_697</field>
                                    <field name="groupby">account_id</field>
                                    <field name="foldable" eval="True"/>
                                    <field name="expression_ids">
                                        <record id="account_financial_report_pl_comp_f_a_ptbd_ob_balance" model="account.report.expression">
                                            <field name="label">balance</field>
                                            <field name="engine">account_codes</field>
                                            <field name="formula">697</field>
                                            <field name="green_on_positive" eval="False"/>
                                        </record>
                                    </field>
                                </record>
                            </field>
                        </record>
                    </field>
                </record>
            </field>
        </record>
    </data>

    <data noupdate="1">
        <record id="account_financial_report_pl_comp_f" model="account.report">
            <field name="active" eval="False"/>
        </record>
    </data>
</odoo>
