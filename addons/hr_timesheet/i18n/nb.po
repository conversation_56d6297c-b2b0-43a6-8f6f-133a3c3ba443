# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_timesheet
# 
# Translators:
# Cé<PERSON>le Collart <<EMAIL>>, 2024
# <PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON>, 2024
# <PERSON> <<EMAIL>>, 2024
# <PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON>, 2024
# <PERSON>, 2024
# <PERSON><PERSON>, 2025
# <PERSON> <<EMAIL>>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-16 13:39+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2025\n"
"Language-Team: <PERSON> (https://app.transifex.com/odoo/teams/41243/nb/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: nb\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_timesheet_table
msgid "#{timesheet.employee_id.name}"
msgstr ""

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/models/project_project.py:0
msgid "%(effective)s %(uom_name)s"
msgstr "%(effective)s %(uom_name)s"

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/models/project_project.py:0
msgid "%(effective)s / %(allocated)s %(uom_name)s"
msgstr ""

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/models/project_project.py:0
msgid "%(effective)s / %(allocated)s %(uom_name)s (%(success_rate)s%%)"
msgstr ""

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/models/project_project.py:0
msgid "%(exceeding_hours)s %(uom_name)s (+%(exceeding_rate)s%%)"
msgstr ""

#. module: hr_timesheet
#. odoo-javascript
#: code:addons/hr_timesheet/static/src/components/time_hour_field/time_hour_field.js:0
msgid "%(hours)sh"
msgstr ""

#. module: hr_timesheet
#. odoo-javascript
#: code:addons/hr_timesheet/static/src/components/time_hour_field/time_hour_field.js:0
msgid "%(hours)sh%(minutes)s"
msgstr ""

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/models/project_project.py:0
msgid "%(name)s's Timesheets"
msgstr "%(name)s's timelister"

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/models/hr_timesheet.py:0
msgid ""
"'%(missing_plan_names)s' analytic plan(s) required on the project "
"'%(project_name)s' linked to the timesheet."
msgstr ""

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/models/project_task.py:0
msgid "(%(sign)s%(hours)s:%(minutes)s remaining)"
msgstr "(%(sign)s %(hours)s: %(minutes)sgjenstår)"

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/models/project_task.py:0
msgid "(%s days remaining)"
msgstr "(%s dager gjenstår)"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_form2_inherited
msgid "(incl."
msgstr "(inkl."

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheet_table
msgid "1 day"
msgstr "1 dag"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheet_table
msgid "2 hours"
msgstr "2 timer"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheet_table
msgid "2021-09-01"
msgstr ""

#. module: hr_timesheet
#: model_terms:digest.tip,tip_description:hr_timesheet.digest_tip_hr_timesheet_0
msgid "<b class=\"tip_title\">Tip: Record your Timesheets faster</b>"
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_kanban_account_analytic_line
msgid "<i class=\"fa fa-calendar me-1\" role=\"img\" aria-label=\"Date\" title=\"Date\"/>"
msgstr "<i class=\"fa fa-calendar me-1\" role=\"img\" aria-label=\"Date\" title=\"Date\"/>"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_task
msgid "<i class=\"fa fa-print\"/> View Details"
msgstr "<i class=\"fa fa-print\"/> Vis detaljer"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_employee_view_form_inherit_timesheet
msgid "<span class=\"o_stat_text\">Timesheets</span>"
msgstr "<span class=\"o_stat_text\">Timelister</span>"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_form2_inherited
msgid "<span class=\"text-nowrap\">Time Spent on Sub-tasks:</span>"
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_employee_delete_wizard_form
msgid ""
"<span invisible=\"not has_timesheet\">\n"
"                        You cannot delete employees who have timesheets.\n"
"                        <span invisible=\"not has_active_employee\">\n"
"                            You can either archive these employees or first delete all of their timesheets.\n"
"                        </span>\n"
"                        <span invisible=\"has_active_employee\" groups=\"hr_timesheet.group_hr_timesheet_approver\">\n"
"                            Please first delete all of their timesheets.\n"
"                        </span>\n"
"                    </span>\n"
"                    <span invisible=\"has_timesheet\">\n"
"                        Are you sure you want to delete these employees?\n"
"                    </span>"
msgstr ""
"<span invisible=\"not has_timesheet\">\n"
"                     Du kan ikke slette ansatte som har timelister.\n"
"                         <span invisible=\"not has_active_employee\">\n"
"                          Du kan enten arkivere disse ansatte eller først slette alle timelistene\n"
"                          </span>\n"
"                          <span invisible=\"has_active_employee\" groups=\"hr_timesheet.group_hr_timesheet_approver\">\n"
"                           Vennligst slett alle timelistene.\n"
"                            </span>\n"
"                          </span>\n"
"                           <span invisible=\"has_timesheet\">\n"
"                            Er du sikker på at du vil slette disse ansatte? \n"
"                        </span>"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheet_table
msgid "<span style=\"margin-right: 15px;\">Total (Days)</span>"
msgstr "<span style=\"margin-right: 15px;\"> Totalt (Dager)</span>"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheet_table
msgid "<span style=\"margin-right: 15px;\">Total (Hours)</span>"
msgstr "<span style=\"margin-right: 15px;\"> Totalt (Timer)</span>"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheet_table
msgid "<span>Date</span>"
msgstr "<span>Dato</span>"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheet_table
msgid "<span>Description</span>"
msgstr "<span>Beskrivelse</span>"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheet_table
msgid "<span>Employee</span>"
msgstr "<span>Ansatt</span>"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheet_table
msgid "<span>Time Spent</span>"
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_task
msgid "<strong>Progress:</strong>"
msgstr "<strong>Progresjon:</strong>"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_timesheet_table
msgid "<strong>Time Remaining: </strong>"
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_timesheet_table
msgid "<strong>Time recorded on sub-tasks: </strong>"
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_timesheet_table
msgid "<strong>Total Days: </strong>"
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_timesheet_table
msgid "<strong>Total Hours: </strong>"
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_timesheet_table
msgid "<strong>Total Time Spent: </strong>"
msgstr ""

#. module: hr_timesheet
#: model:res.groups,name:hr_timesheet.group_timesheet_manager
msgid "Administrator"
msgstr "Administrator"

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/controllers/portal.py:0
msgid "All"
msgstr "Alle"

#. module: hr_timesheet
#: model:ir.actions.act_window,name:hr_timesheet.timesheet_action_all
#: model:ir.ui.menu,name:hr_timesheet.timesheet_menu_activity_all
msgid "All Timesheets"
msgstr "Alle timelister"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_project__allocated_hours
#: model:ir.model.fields,field_description:hr_timesheet.field_project_update__allocated_time
#: model:ir.model.fields,field_description:hr_timesheet.field_report_project_task_user__allocated_hours
msgid "Allocated Time"
msgstr "Allokert tid"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_timesheets_analysis_report__amount
msgid "Amount"
msgstr "Beløp"

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/__init__.py:0
msgid "Analysis"
msgstr "Analyse"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_timesheet_line_form
msgid "Analytic Entry"
msgstr "Analytiske registrering"

#. module: hr_timesheet
#: model:ir.model,name:hr_timesheet.model_account_analytic_line
msgid "Analytic Line"
msgstr "Analytisk linje"

#. module: hr_timesheet
#: model:ir.model,name:hr_timesheet.model_account_analytic_applicability
msgid "Analytic Plan's Applicabilities"
msgstr "Analyseringsplanens anvendeligheter."

#. module: hr_timesheet
#: model_terms:ir.actions.act_window,help:hr_timesheet.act_hr_timesheet_report
#: model_terms:ir.actions.act_window,help:hr_timesheet.timesheet_action_report_by_project
#: model_terms:ir.actions.act_window,help:hr_timesheet.timesheet_action_report_by_task
msgid ""
"Analyze the projects and tasks on which your employees spend their time.<br>\n"
"                Evaluate which part is billable and what costs it represents."
msgstr ""

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_res_config_settings__reminder_allow
msgid "Approver Reminder"
msgstr "Påminnelse godkjenner"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_employee_delete_wizard_form
msgid "Archive Employees"
msgstr "Arkiver ansatt"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheet_table
msgid "Audrey Peterson"
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_tree2_inherited
msgid "Average of Progress"
msgstr "Gjennomsnitt av progresjonen"

#. module: hr_timesheet
#: model:ir.ui.menu,name:hr_timesheet.menu_hr_activity_analysis
msgid "By Employee"
msgstr "Etter ansatt"

#. module: hr_timesheet
#: model:ir.ui.menu,name:hr_timesheet.timesheet_menu_report_timesheet_by_project
msgid "By Project"
msgstr "Etter prosjekt"

#. module: hr_timesheet
#: model:ir.ui.menu,name:hr_timesheet.timesheet_menu_report_timesheet_by_task
msgid "By Task"
msgstr "Etter oppgave"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheet_table
msgid "Call client and discuss project"
msgstr ""

#. module: hr_timesheet
#: model:ir.model,name:hr_timesheet.model_project_collaborator
msgid "Collaborators in project shared"
msgstr ""

#. module: hr_timesheet
#: model:ir.model,name:hr_timesheet.model_res_company
msgid "Companies"
msgstr "Firmaer"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_timesheets_analysis_report__company_id
msgid "Company"
msgstr "Firma"

#. module: hr_timesheet
#: model:ir.model,name:hr_timesheet.model_res_config_settings
msgid "Config Settings"
msgstr "Innstillinger"

#. module: hr_timesheet
#: model:ir.ui.menu,name:hr_timesheet.hr_timesheet_menu_configuration
msgid "Configuration"
msgstr "Konfigurasjon"

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/models/hr_employee.py:0
msgid "Confirmation"
msgstr "Bekreftelse"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_hr_employee_delete_wizard__create_uid
msgid "Created by"
msgstr "Opprettet av"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_hr_employee_delete_wizard__create_date
msgid "Created on"
msgstr "Opprettet den"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_timesheets_analysis_report__currency_id
msgid "Currency"
msgstr "Valuta"

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/controllers/portal.py:0
#: model:ir.model.fields,field_description:hr_timesheet.field_timesheets_analysis_report__date
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_timesheets
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_timesheet_table
msgid "Date"
msgstr "Dato"

#. module: hr_timesheet
#: model:ir.model.fields.selection,name:hr_timesheet.selection__res_config_settings__timesheet_encode_method__days
msgid "Days / Half-Days"
msgstr "Dager / Halvdager"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_sharing_inherit_project_task_view_form
msgid "Days Remaining"
msgstr ""

#. module: hr_timesheet
#: model:ir.model.fields,help:hr_timesheet.field_project_project__analytic_account_active
#: model:ir.model.fields,help:hr_timesheet.field_project_task__analytic_account_active
msgid "Deactivate the account."
msgstr "Deaktiver konto"

#. module: hr_timesheet
#: model:ir.model.fields,help:hr_timesheet.field_res_company__internal_project_id
msgid "Default project value for timesheet generated from time off type."
msgstr ""

#. module: hr_timesheet
#: model:ir.model.fields,help:hr_timesheet.field_account_analytic_line__employee_id
msgid ""
"Define an 'hourly cost' on the employee to track the cost of their time."
msgstr "Definer timekostnad for ansatt for å spore tidskostnaden."

#. module: hr_timesheet
#: model:ir.actions.server,name:hr_timesheet.unlink_employee_action
msgid "Delete"
msgstr "Slett"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_employee_delete_wizard_form
msgid "Delete Employee"
msgstr "Slett ansatt"

#. module: hr_timesheet
#: model:ir.model.fields,help:hr_timesheet.field_account_analytic_line__milestone_id
#: model:ir.model.fields,help:hr_timesheet.field_timesheets_analysis_report__milestone_id
msgid ""
"Deliver your services automatically when a milestone is reached by linking "
"it to a sales order item."
msgstr ""

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_account_analytic_line__department_id
#: model:ir.model.fields,field_description:hr_timesheet.field_timesheets_analysis_report__department_id
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_timesheet_line_search
msgid "Department"
msgstr "Avdeling"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_timesheet_line_form
msgid "Describe your activity"
msgstr "Beskriv din aktivitet"

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/controllers/portal.py:0
#: model:ir.model.fields,field_description:hr_timesheet.field_timesheets_analysis_report__name
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_timesheets
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_timesheet_table
msgid "Description"
msgstr "Beskrivelse"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_employee_delete_wizard_form
msgid "Discard"
msgstr "Avbryt"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_hr_employee_delete_wizard__display_name
#: model:ir.model.fields,field_description:hr_timesheet.field_project_task__display_name
#: model:ir.model.fields,field_description:hr_timesheet.field_timesheets_analysis_report__display_name
msgid "Display Name"
msgstr "Visningsnavn"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_account_analytic_applicability__business_domain
msgid "Domain"
msgstr "Domene"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_form2_inherited
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_tree2_inherited
msgid "Effective Hours"
msgstr "Effektive timer"

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/controllers/portal.py:0
#: model:ir.model,name:hr_timesheet.model_hr_employee
#: model:ir.model.fields,field_description:hr_timesheet.field_account_analytic_line__employee_id
#: model:ir.model.fields,field_description:hr_timesheet.field_timesheets_analysis_report__employee_id
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_timesheet_line_search
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_timesheets
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_timesheet_table
msgid "Employee"
msgstr "Ansatt"

#. module: hr_timesheet
#: model:ir.model,name:hr_timesheet.model_hr_employee_delete_wizard
msgid "Employee Delete Wizard"
msgstr ""

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_res_config_settings__reminder_user_allow
msgid "Employee Reminder"
msgstr "Ansatt Påminnelse"

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/wizard/hr_employee_delete_wizard.py:0
msgid "Employee Termination"
msgstr "Ansatt terminering"

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/wizard/hr_employee_delete_wizard.py:0
msgid "Employees' Timesheets"
msgstr "Ansattes timelister"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_res_config_settings__timesheet_encode_method
msgid "Encoding Method"
msgstr ""

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/models/project_project.py:0
msgid "Extra Time"
msgstr "Ekstra tid"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.res_config_settings_view_form
msgid ""
"Generate timesheets for validated time off requests and public holidays"
msgstr ""

#. module: hr_timesheet
#: model:ir.model,name:hr_timesheet.model_ir_http
msgid "HTTP Routing"
msgstr "HTTP-ruting"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_timesheets_analysis_report__has_department_manager_access
msgid "Has Department Manager Access"
msgstr ""

#. module: hr_timesheet
#: model:ir.model.fields.selection,name:hr_timesheet.selection__res_config_settings__timesheet_encode_method__hours
msgid "Hours / Minutes"
msgstr "Timer / minutter"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_report_project_task_user__total_hours_spent
msgid "Hours By Task (Including Subtasks)"
msgstr ""

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_hr_employee_delete_wizard__id
#: model:ir.model.fields,field_description:hr_timesheet.field_timesheets_analysis_report__id
msgid "ID"
msgstr "ID"

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/models/res_company.py:0
#: model:project.task.type,name:hr_timesheet.internal_project_default_stage
msgid "Internal"
msgstr "Intern"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_res_company__internal_project_id
msgid "Internal Project"
msgstr "Internt prosjekt"

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/models/project_project.py:0
msgid "Invalid operator: %s"
msgstr ""

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/models/project_project.py:0
msgid "Invalid value: %s"
msgstr ""

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/controllers/portal.py:0
msgid "Last Month"
msgstr "Forrige måned"

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/controllers/portal.py:0
msgid "Last Quarter"
msgstr "Forrige kvartal"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_hr_employee_delete_wizard__write_uid
msgid "Last Updated by"
msgstr "Sist oppdatert av"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_hr_employee_delete_wizard__write_date
msgid "Last Updated on"
msgstr "Sist oppdatert"

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/controllers/portal.py:0
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_timesheet_line_search
msgid "Last Week"
msgstr "Forrige uke"

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/controllers/portal.py:0
msgid "Last Year"
msgstr "Forrige år"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_invoice_form
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_project_view_form_simplified_inherit_timesheet
msgid "Log time on tasks"
msgstr "Logg tid på oppgaver"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_account_analytic_line__manager_id
#: model:ir.model.fields,field_description:hr_timesheet.field_timesheets_analysis_report__manager_id
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_timesheet_line_search
msgid "Manager"
msgstr "Leder"

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/models/res_company.py:0
msgid "Meeting"
msgstr "Møte"

#. module: hr_timesheet
#: model:ir.model,name:hr_timesheet.model_ir_ui_menu
msgid "Menu"
msgstr "Meny"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_account_analytic_line__message_partner_ids
#: model:ir.model.fields,field_description:hr_timesheet.field_timesheets_analysis_report__message_partner_ids
msgid "Message Partner"
msgstr ""

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_account_analytic_line__milestone_id
#: model:ir.model.fields,field_description:hr_timesheet.field_timesheets_analysis_report__milestone_id
msgid "Milestone"
msgstr "Milestein"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_update_view_search_inherit
msgid "My Department's Updates"
msgstr "Oppdateringer avdeling"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_update_view_search_inherit
msgid "My Team's Updates"
msgstr "Oppdateringer team"

#. module: hr_timesheet
#: model:ir.actions.act_window,name:hr_timesheet.act_hr_timesheet_line
#: model:ir.ui.menu,name:hr_timesheet.timesheet_menu_activity_mine
#: model:ir.ui.menu,name:hr_timesheet.timesheet_menu_activity_user
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_timesheet_line_search
msgid "My Timesheets"
msgstr "Timelistene mine"

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/controllers/portal.py:0
msgid "Newest"
msgstr "Nyeste"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_timesheets
msgid "No Parent Task"
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_timesheets
msgid "No Task"
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.actions.act_window,help:hr_timesheet.act_hr_timesheet_line
#: model_terms:ir.actions.act_window,help:hr_timesheet.timesheet_action_all
msgid "No activities found. Let's start a new one!"
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.actions.act_window,help:hr_timesheet.act_hr_timesheet_report
#: model_terms:ir.actions.act_window,help:hr_timesheet.timesheet_action_report_by_project
#: model_terms:ir.actions.act_window,help:hr_timesheet.timesheet_action_report_by_task
msgid "No data yet!"
msgstr "Ingen data ennå"

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/controllers/portal.py:0
msgid "None"
msgstr "Ingen"

#. module: hr_timesheet
#: model:ir.model.fields,help:hr_timesheet.field_project_task__remaining_hours
msgid "Number of allocated hours minus the number of hours spent."
msgstr "Antall allokerte timer minus brukte timer"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_employee_delete_wizard_form
msgid "Ok"
msgstr "Ok"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_task__overtime
#: model:ir.model.fields,field_description:hr_timesheet.field_report_project_task_user__overtime
msgid "Overtime"
msgstr "Overtid"

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/controllers/portal.py:0
#: model:ir.model.fields,field_description:hr_timesheet.field_account_analytic_line__parent_task_id
#: model:ir.model.fields,field_description:hr_timesheet.field_timesheets_analysis_report__parent_task_id
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_timesheet_line_search
msgid "Parent Task"
msgstr "Hovedoppgave"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_account_analytic_line__partner_id
#: model:ir.model.fields,field_description:hr_timesheet.field_timesheets_analysis_report__partner_id
msgid "Partner"
msgstr "Partner"

#. module: hr_timesheet
#: model:ir.model,name:hr_timesheet.model_uom_uom
msgid "Product Unit of Measure"
msgstr "Produktenhet"

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/controllers/portal.py:0
#: model:ir.model.fields,field_description:hr_timesheet.field_project_task__progress
#: model:ir.model.fields,field_description:hr_timesheet.field_report_project_task_user__progress
msgid "Progress"
msgstr "Fremdrift"

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/controllers/portal.py:0
#: model:ir.model,name:hr_timesheet.model_project_project
#: model:ir.model.fields,field_description:hr_timesheet.field_account_analytic_line__project_id
#: model:ir.model.fields,field_description:hr_timesheet.field_project_task__project_id
#: model:ir.model.fields,field_description:hr_timesheet.field_timesheets_analysis_report__project_id
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_timesheet_line_search
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_timesheets
#: model_terms:ir.ui.view,arch_db:hr_timesheet.report_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheet_table
msgid "Project"
msgstr "Prosjekt"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_project__account_id
msgid "Project Account"
msgstr "Prosjektkonto"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_res_company__project_time_mode_id
#: model:ir.model.fields,field_description:hr_timesheet.field_res_config_settings__project_time_mode_id
msgid "Project Time Unit"
msgstr "Prosjekt-tidsenhet"

#. module: hr_timesheet
#: model:ir.model,name:hr_timesheet.model_project_update
msgid "Project Update"
msgstr "Oppdater prosjektet"

#. module: hr_timesheet
#: model:ir.actions.act_window,name:hr_timesheet.timesheet_action_project
msgid "Project's Timesheets"
msgstr "Prosjektets timeliste"

#. module: hr_timesheet
#: model_terms:ir.actions.act_window,help:hr_timesheet.act_hr_timesheet_line_by_project
#: model_terms:ir.actions.act_window,help:hr_timesheet.timesheet_action_from_employee
msgid "Record a new activity"
msgstr "Lag en ny aktivitet"

#. module: hr_timesheet
#: model_terms:digest.tip,tip_description:hr_timesheet.digest_tip_hr_timesheet_0
msgid ""
"Record your timesheets in an instant by pressing Shift + the corresponding "
"hotkey to add 15min to your projects."
msgstr ""

#. module: hr_timesheet
#: model:ir.ui.menu,name:hr_timesheet.menu_timesheets_reports
msgid "Reporting"
msgstr "Rapportering"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.report_timesheet
msgid "Research and Development"
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheet_table
msgid "Research and Development/New Portal System"
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_home_timesheet
msgid "Review all timesheets related to your projects"
msgstr "Gjennomgå alle timelister relatert til dine prosjekter"

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/controllers/portal.py:0
msgid "Search in Description"
msgstr "Søk i beskrivelse"

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/controllers/portal.py:0
msgid "Search in Employee"
msgstr "Søk i ansatt"

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/controllers/portal.py:0
msgid "Search in Parent Task"
msgstr ""

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/controllers/portal.py:0
msgid "Search in Project"
msgstr "Søk i prosjekt"

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/controllers/portal.py:0
msgid "Search in Task"
msgstr "Søk i oppgave"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_employee_delete_wizard_form
msgid "See Timesheets"
msgstr "Se timelister"

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/models/project_project.py:0
#: code:addons/hr_timesheet/models/project_task.py:0
msgid "See timesheet entries"
msgstr "Se oppføringer i timelister"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.res_config_settings_view_form
msgid ""
"Send a periodical email reminder to timesheets approvers that still have "
"timesheets to validate"
msgstr ""
"Send en periodisk epost påminnelse til godkjennere om å validere timelister"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.res_config_settings_view_form
msgid ""
"Send a periodical email reminder to timesheets users that still have "
"timesheets to encode"
msgstr ""
"Send en periodisk e-postpåminnelse til timelistebrukere som fortsatt har "
"timelister å registrere."

#. module: hr_timesheet
#: model:ir.actions.act_window,name:hr_timesheet.hr_timesheet_config_settings_action
msgid "Settings"
msgstr "Innstillinger"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheet_report_subtask
msgid "Sub-Task of '"
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_form2_inherited
msgid "Sub-tasks Time Spent"
msgstr ""

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/controllers/portal.py:0
#: model:ir.model,name:hr_timesheet.model_project_task
#: model:ir.model.fields,field_description:hr_timesheet.field_account_analytic_line__task_id
#: model:ir.model.fields,field_description:hr_timesheet.field_timesheets_analysis_report__task_id
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_timesheet_line_search
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_timesheets
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheet_table
msgid "Task"
msgstr "Oppgave"

#. module: hr_timesheet
#: model:ir.actions.act_window,name:hr_timesheet.timesheet_action_task
msgid "Task's Timesheets"
msgstr "Oppgavens timeliste"

#. module: hr_timesheet
#: model:ir.model,name:hr_timesheet.model_report_project_task_user
msgid "Tasks Analysis"
msgstr "Oppgaveanalyse"

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/models/res_company.py:0
msgid "The Internal Project of a company should be in that company."
msgstr ""

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/models/hr_timesheet.py:0
msgid ""
"The project, the task and the analytic accounts of the timesheet must belong"
" to the same company."
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_timesheets
msgid "There are no timesheets."
msgstr "Ingen timelister"

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/models/project_project.py:0
msgid ""
"These projects have some timesheet entries referencing them. Before removing"
" these projects, you have to remove these timesheet entries."
msgstr ""

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/models/project_task.py:0
msgid ""
"These tasks have some timesheet entries referencing them. Before removing "
"these tasks, you have to remove these timesheet entries."
msgstr ""

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/controllers/portal.py:0
msgid "This Month"
msgstr "Denne måneden"

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/controllers/portal.py:0
msgid "This Quarter"
msgstr "Dette kvartalet"

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/controllers/portal.py:0
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_timesheet_line_search
msgid "This Week"
msgstr "Denne uka"

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/controllers/portal.py:0
msgid "This Year"
msgstr "Dette året"

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/models/project_task.py:0
msgid "This operator %s is not supported in this search method."
msgstr ""

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/models/project_project.py:0
msgid ""
"This project has some timesheet entries referencing it. Before removing this"
" project, you have to remove these timesheet entries."
msgstr ""

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/models/project_task.py:0
msgid ""
"This task cannot be private because there are some timesheets linked to it."
msgstr ""

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/models/project_task.py:0
msgid ""
"This task has some timesheet entries referencing it. Before removing this "
"task, you have to remove these timesheet entries."
msgstr ""

#. module: hr_timesheet
#: model:ir.model.fields,help:hr_timesheet.field_res_company__project_time_mode_id
#: model:ir.model.fields,help:hr_timesheet.field_res_config_settings__project_time_mode_id
msgid ""
"This will set the unit of measure used in projects and tasks.\n"
"If you use the timesheet linked to projects, don't forget to setup the right unit of measure in your employees."
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.res_config_settings_view_form
msgid "Time Encoding"
msgstr ""

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_res_config_settings__module_project_timesheet_holidays
#: model_terms:ir.ui.view,arch_db:hr_timesheet.res_config_settings_view_form
msgid "Time Off"
msgstr "Fravær"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_project__remaining_hours
#: model:ir.model.fields,field_description:hr_timesheet.field_project_task__remaining_hours
#: model:ir.model.fields,field_description:hr_timesheet.field_report_project_task_user__remaining_hours
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_form2_inherited
msgid "Time Remaining"
msgstr ""

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_report_project_task_user__remaining_hours_percentage
msgid "Time Remaining Percentage"
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_tree2_inherited
msgid "Time Remaining on SO"
msgstr ""

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_project__effective_hours
#: model:ir.model.fields,field_description:hr_timesheet.field_project_task__effective_hours
#: model:ir.model.fields,field_description:hr_timesheet.field_report_project_task_user__effective_hours
#: model:ir.model.fields,field_description:hr_timesheet.field_timesheets_analysis_report__unit_amount
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_timesheet_line_form
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_timesheet_line_tree
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_timesheets
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_tasks_list_inherit
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_timesheet_table
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheets_analysis_report_form
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_hr_timesheet_line_graph
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_hr_timesheet_line_graph_all
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_hr_timesheet_line_graph_my
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_hr_timesheet_line_pivot
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_my_timesheet_line_pivot
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_form2_inherited
msgid "Time Spent"
msgstr "Tid brukt"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_report_project_task_user__subtask_effective_hours
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_tree2_inherited
msgid "Time Spent on Sub-Tasks"
msgstr ""

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_task__subtask_effective_hours
msgid "Time Spent on Sub-tasks"
msgstr ""

#. module: hr_timesheet
#: model:ir.model.fields,help:hr_timesheet.field_project_task__subtask_effective_hours
#: model:ir.model.fields,help:hr_timesheet.field_report_project_task_user__subtask_effective_hours
msgid "Time spent on the sub-tasks (and their own sub-tasks) of this task."
msgstr ""

#. module: hr_timesheet
#: model:ir.model.fields,help:hr_timesheet.field_project_task__total_hours_spent
msgid "Time spent on this task and its sub-tasks (and their own sub-tasks)."
msgstr ""

#. module: hr_timesheet
#: model:ir.model.fields,help:hr_timesheet.field_report_project_task_user__total_hours_spent
msgid "Time spent on this task, including its sub-tasks."
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.res_config_settings_view_form
msgid "Time unit used to record your timesheets"
msgstr "Tidsenhet brukt i din timeliste"

#. module: hr_timesheet
#: model:ir.model.fields.selection,name:hr_timesheet.selection__account_analytic_applicability__business_domain__timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_hr_timesheet_line_graph_all
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_hr_timesheet_line_graph_my
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_my_timesheet_line_pivot
msgid "Timesheet"
msgstr "Timeliste"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_timesheet_line_tree
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_form2_inherited
msgid "Timesheet Activities"
msgstr "Timeliste-aktiviteter"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheets_analysis_report_graph_employee
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheets_analysis_report_graph_project
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheets_analysis_report_graph_task
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheets_analysis_report_list
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheets_analysis_report_pivot_employee
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheets_analysis_report_pivot_project
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheets_analysis_report_pivot_task
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_hr_timesheet_line_graph
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_hr_timesheet_line_graph_all
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_hr_timesheet_line_graph_my
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_hr_timesheet_line_pivot
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_my_timesheet_line_pivot
msgid "Timesheet Costs"
msgstr "Timelistekostnad"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_res_company__timesheet_encode_uom_id
msgid "Timesheet Encoding Unit"
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_timesheet_report_search
msgid "Timesheet Report"
msgstr "Timeliste rapport"

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_update__timesheet_time
msgid "Timesheet Time"
msgstr "Timeliste tid"

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/models/hr_timesheet.py:0
#: code:addons/hr_timesheet/models/project_project.py:0
#: code:addons/hr_timesheet/models/project_task.py:0
#: model:ir.actions.act_window,name:hr_timesheet.act_hr_timesheet_line_by_project
#: model:ir.actions.act_window,name:hr_timesheet.timesheet_action_from_employee
#: model:ir.actions.report,name:hr_timesheet.timesheet_report
#: model:ir.actions.report,name:hr_timesheet.timesheet_report_project
#: model:ir.actions.report,name:hr_timesheet.timesheet_report_task
#: model:ir.actions.report,name:hr_timesheet.timesheet_report_task_timesheets
#: model:ir.embedded.actions,name:hr_timesheet.project_embedded_action_timesheets
#: model:ir.embedded.actions,name:hr_timesheet.project_embedded_action_timesheets_dashboard
#: model:ir.model.fields,field_description:hr_timesheet.field_project_project__allow_timesheets
#: model:ir.ui.menu,name:hr_timesheet.menu_hr_time_tracking
#: model:ir.ui.menu,name:hr_timesheet.menu_timesheets_reports_timesheet
#: model:ir.ui.menu,name:hr_timesheet.timesheet_menu_root
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_department_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_layout
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_home_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_task
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_timesheets
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_invoice_form
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_project_view_form_simplified_inherit_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:hr_timesheet.report_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.report_timesheet_task
#: model_terms:ir.ui.view,arch_db:hr_timesheet.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheets_analysis_report_graph_employee
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheets_analysis_report_graph_project
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheets_analysis_report_graph_task
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_hr_timesheet_line_graph
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_hr_timesheet_line_pivot
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_project_kanban_inherited
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_form2_inherited
msgid "Timesheets"
msgstr "Timelister"

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/models/hr_timesheet.py:0
msgid "Timesheets - %s"
msgstr "Timeliste - %s"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_task_view_search
msgid "Timesheets 80%"
msgstr "Timeliste 80%"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_task_view_search
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_project_project_filter_inherit_timesheet
msgid "Timesheets >100%"
msgstr "Timeliste > 100%"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheets_analysis_report_form
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheets_analysis_report_pivot_employee
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheets_analysis_report_pivot_project
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheets_analysis_report_pivot_task
msgid "Timesheets Analysis"
msgstr "Timeliste analyse"

#. module: hr_timesheet
#: model:ir.model,name:hr_timesheet.model_timesheets_analysis_report
msgid "Timesheets Analysis Report"
msgstr "Timeliste analyse rapport"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.res_config_settings_view_form
msgid "Timesheets Control"
msgstr "Timeliste kontroll"

#. module: hr_timesheet
#: model:ir.actions.act_window,name:hr_timesheet.act_hr_timesheet_report
msgid "Timesheets by Employee"
msgstr "Timeliste pr ansatt"

#. module: hr_timesheet
#: model:ir.actions.act_window,name:hr_timesheet.timesheet_action_report_by_project
msgid "Timesheets by Project"
msgstr "Timeliste pr prosjekt"

#. module: hr_timesheet
#: model:ir.actions.act_window,name:hr_timesheet.timesheet_action_report_by_task
msgid "Timesheets by Task"
msgstr "Timeliste pr oppgave"

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/models/hr_timesheet.py:0
msgid "Timesheets cannot be created on a private task."
msgstr "Timelister kan ikke lages på private oppgaver."

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/models/hr_timesheet.py:0
msgid ""
"Timesheets must be created with an active employee in the selected "
"companies."
msgstr ""

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/models/hr_timesheet.py:0
msgid ""
"Timesheets must be created with at least an active analytic account defined "
"in the plan '%(plan_name)s'."
msgstr ""

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/wizard/hr_employee_delete_wizard.py:0
msgid "Timesheets of %(name)s"
msgstr "Timelister av %(name)s"

#. module: hr_timesheet
#: model:digest.tip,name:hr_timesheet.digest_tip_hr_timesheet_0
msgid "Tip: Record your Timesheets faster"
msgstr ""

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/models/project_project.py:0
msgid ""
"To use the timesheets feature, you need an analytic account for your "
"project. Please set one up in the plan '%(plan_name)s' or turn off the "
"timesheets feature."
msgstr ""

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/controllers/portal.py:0
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_timesheet_line_search
msgid "Today"
msgstr "I dag"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.hr_timesheet_line_tree
#: model_terms:ir.ui.view,arch_db:hr_timesheet.timesheets_analysis_report_list
msgid "Total"
msgstr "Totalt"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_form2_inherited
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_tree2_inherited
msgid "Total Allocated Time"
msgstr "Total allokert tid"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_sharing_inherit_project_task_view_form
msgid "Total Days Spent"
msgstr ""

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_project_task__total_hours_spent
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_sharing_inherit_project_task_view_form
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_form2_inherited
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_tree2_inherited
msgid "Total Time Spent"
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_timesheets
msgid "Total:"
msgstr "Total:"

#. module: hr_timesheet
#: model_terms:ir.actions.act_window,help:hr_timesheet.act_hr_timesheet_line
#: model_terms:ir.actions.act_window,help:hr_timesheet.act_hr_timesheet_line_by_project
#: model_terms:ir.actions.act_window,help:hr_timesheet.timesheet_action_all
msgid ""
"Track your working hours by projects every day and invoice this time to your"
" customers."
msgstr ""

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/models/res_company.py:0
msgid "Training"
msgstr "Opplæring"

#. module: hr_timesheet
#: model:ir.model.fields,help:hr_timesheet.field_project_task__display_name
msgid ""
"Use these keywords in the title to set new tasks:\n"
"\n"
"        30h Allocate 30 hours to the task\n"
"        #tags Set tags on the task\n"
"        @user Assign the task to a user\n"
"        ! Set the task a high priority\n"
"\n"
"        Make sure to use the right format and order e.g. Improve the configuration screen 5h #feature #v16 @Mitchell !"
msgstr ""

#. module: hr_timesheet
#: model:ir.model.fields,field_description:hr_timesheet.field_account_analytic_line__user_id
#: model:ir.model.fields,field_description:hr_timesheet.field_timesheets_analysis_report__user_id
msgid "User"
msgstr "Bruker"

#. module: hr_timesheet
#: model:res.groups,name:hr_timesheet.group_hr_timesheet_approver
msgid "User: all timesheets"
msgstr "Bruker: alle timelister"

#. module: hr_timesheet
#: model:res.groups,name:hr_timesheet.group_hr_timesheet_user
msgid "User: own timesheets only"
msgstr "Bruker: Kun egne timelister"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.portal_my_task
msgid "View Details"
msgstr "Vis detaljer"

#. module: hr_timesheet
#: model_terms:ir.actions.act_window,help:hr_timesheet.timesheet_action_from_employee
msgid ""
"You can register and track your workings hours by project every\n"
"                    day. Every time spent on a project will become a cost and can be re-invoiced to\n"
"                    customers if required."
msgstr ""

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/models/hr_timesheet.py:0
msgid "You cannot access timesheets that are not yours."
msgstr "Du kan ikke se timelister som ikke er dine."

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/models/hr_employee.py:0
msgid "You cannot delete employees who have timesheets."
msgstr "Du kan ikke slette ansatte med timelister."

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_invoice_form
msgid ""
"You cannot log timesheets on this project since it is linked to an inactive analytic account.<br/>\n"
"                            Please switch to another account, or reactivate the current one to timesheet on the project."
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_form2_inherited
msgid ""
"You cannot log timesheets on this project since it is linked to an inactive "
"analytic account.<br/> Please change this account, or reactivate the current"
" one to timesheet on the project."
msgstr ""

#. module: hr_timesheet
#. odoo-python
#: code:addons/hr_timesheet/models/hr_timesheet.py:0
msgid "You cannot set an archived employee on existing timesheets."
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.report_timesheet_task
msgid "for"
msgstr "for "

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.report_timesheet
msgid "for the"
msgstr "for"

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.view_task_form2_inherited
msgid ""
"on\n"
"                            <span class=\"fw-bold text-dark\"> Sub-tasks</span>)"
msgstr ""

#. module: hr_timesheet
#: model_terms:ir.ui.view,arch_db:hr_timesheet.project_sharing_inherit_project_task_view_form
msgid ""
"on\n"
"                        <span class=\"fw-bold text-dark\"> Sub-tasks</span>)"
msgstr ""
