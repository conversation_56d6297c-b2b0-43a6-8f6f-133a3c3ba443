# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* payment_nuvei
# 
# Translators:
# Wil Odoo, 2025
# Sarah Park, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-03-19 20:38+0000\n"
"PO-Revision-Date: 2025-03-19 09:42+0000\n"
"Last-Translator: Sarah Park, 2025\n"
"Language-Team: Korean (https://app.transifex.com/odoo/teams/41243/ko/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ko\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: payment_nuvei
#. odoo-python
#: code:addons/payment_nuvei/models/payment_transaction.py:0
msgid "%(payment_method)s requires both a first and last name."
msgstr "%(payment_method)s 항목에는 성과 이름을 모두 입력해야 합니다."

#. module: payment_nuvei
#. odoo-python
#: code:addons/payment_nuvei/models/payment_transaction.py:0
msgid ""
"An error occurred during the processing of your payment (%(reason)s). Please"
" try again."
msgstr "결제를 처리하는 동안 오류가 발생했습니다 (%(reason)s). 다시 시도해 주세요."

#. module: payment_nuvei
#: model:ir.model.fields,field_description:payment_nuvei.field_payment_provider__code
msgid "Code"
msgstr "코드"

#. module: payment_nuvei
#: model_terms:ir.ui.view,arch_db:payment_nuvei.payment_provider_form
msgid "Merchant Identifier"
msgstr "판매자 식별 기호"

#. module: payment_nuvei
#. odoo-python
#: code:addons/payment_nuvei/models/payment_transaction.py:0
msgid "No transaction found matching reference %(ref)s."
msgstr "%(ref)s 참조와 일치하는 거래 항목이 없습니다."

#. module: payment_nuvei
#: model:ir.model.fields.selection,name:payment_nuvei.selection__payment_provider__code__nuvei
msgid "Nuvei"
msgstr "Nuvei"

#. module: payment_nuvei
#: model:ir.model.fields,field_description:payment_nuvei.field_payment_provider__nuvei_merchant_identifier
msgid "Nuvei Merchant Identifier"
msgstr "Nuvei 판매자 식별 기호"

#. module: payment_nuvei
#: model:ir.model.fields,field_description:payment_nuvei.field_payment_provider__nuvei_secret_key
msgid "Nuvei Secret Key"
msgstr "Nuvei 보안 키"

#. module: payment_nuvei
#: model:ir.model.fields,field_description:payment_nuvei.field_payment_provider__nuvei_site_identifier
msgid "Nuvei Site Identifier"
msgstr "Nuvei 사이트 식별 기호"

#. module: payment_nuvei
#: model:ir.model,name:payment_nuvei.model_payment_provider
msgid "Payment Provider"
msgstr "결제대행업체"

#. module: payment_nuvei
#: model:ir.model,name:payment_nuvei.model_payment_transaction
msgid "Payment Transaction"
msgstr "지불 거래"

#. module: payment_nuvei
#. odoo-python
#: code:addons/payment_nuvei/models/payment_transaction.py:0
msgid "Received data with missing payment state."
msgstr "결제 상태가 누락된 데이터가 수신되었습니다. "

#. module: payment_nuvei
#. odoo-python
#: code:addons/payment_nuvei/models/payment_transaction.py:0
msgid "Received data with missing reference."
msgstr "참조가 누락된 데이터가 수신되었습니다."

#. module: payment_nuvei
#. odoo-python
#: code:addons/payment_nuvei/models/payment_transaction.py:0
msgid ""
"Received invalid transaction status %(status)s and reason '%(reason)s'."
msgstr "잘못된 거래 상태 %(status)s와 사유 '%(reason)s'가 수신되었습니다."

#. module: payment_nuvei
#: model_terms:ir.ui.view,arch_db:payment_nuvei.payment_provider_form
msgid "Secret Key"
msgstr "비밀 키"

#. module: payment_nuvei
#: model_terms:ir.ui.view,arch_db:payment_nuvei.payment_provider_form
msgid "Site Identifier"
msgstr "사이트 식별자"

#. module: payment_nuvei
#: model:ir.model.fields,help:payment_nuvei.field_payment_provider__nuvei_merchant_identifier
msgid "The code of the merchant account to use with this provider."
msgstr "이 공급업체에서 사용할 판매자 계정 코드입니다."

#. module: payment_nuvei
#. odoo-python
#: code:addons/payment_nuvei/models/payment_transaction.py:0
msgid "The customer left the payment page."
msgstr "고객이 결제 페이지를 나갔습니다."

#. module: payment_nuvei
#: model:ir.model.fields,help:payment_nuvei.field_payment_provider__nuvei_site_identifier
msgid "The site identifier code associated with the merchant account."
msgstr "판매자 계정과 연결된 사이트 식별 코드입니다."

#. module: payment_nuvei
#: model:ir.model.fields,help:payment_nuvei.field_payment_provider__code
msgid "The technical code of this payment provider."
msgstr "이 결제대행업체의 기술 코드입니다."
