<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <data noupdate="1">
        <record id="sale_loyalty_program_company_rule" model="ir.rule">
            <field name="name">Loyalty program multi company rule</field>
            <field name="model_id" ref="model_loyalty_program"/>
            <field name="domain_force">['|', ('company_id', 'in', company_ids + [False]), ('company_id', 'parent_of', company_ids)]</field>
        </record>

        <record id="sale_loyalty_card_company_rule" model="ir.rule">
            <field name="name">Loyalty card multi company rule</field>
            <field name="model_id" ref="model_loyalty_card"/>
            <field name="domain_force">['|', ('company_id', 'in', company_ids + [False]), ('company_id', 'parent_of', company_ids)]</field>
        </record>

        <record id="loyalty_history_company_rule" model="ir.rule">
            <field name="name">Loyalty history multi company rule</field>
            <field name="model_id" ref="model_loyalty_history"/>
            <field name="domain_force">[('company_id', 'in', company_ids + [False])]</field>
        </record>

        <record id="sale_loyalty_rule_company_rule" model="ir.rule">
            <field name="name">Loyalty rule multi company rule</field>
            <field name="model_id" ref="model_loyalty_rule"/>
            <field name="domain_force">['|', ('company_id', 'in', company_ids + [False]), ('company_id', 'parent_of', company_ids)]</field>
        </record>

        <record id="sale_loyalty_reward_company_rule" model="ir.rule">
            <field name="name">Loyalty reward multi company rule</field>
            <field name="model_id" ref="model_loyalty_reward"/>
            <field name="domain_force">['|', ('company_id', 'in', company_ids + [False]), ('company_id', 'parent_of', company_ids)]</field>
        </record>
    </data>
</odoo>
