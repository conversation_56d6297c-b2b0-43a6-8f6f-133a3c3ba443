# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_id
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.1alpha1+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-01-12 10:05+0000\n"
"PO-Revision-Date: 2024-01-12 10:05+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_id
#: model_terms:res.company,report_footer:l10n_id.demo_company_id
msgid "+62 *********** <EMAIL> http://www.idexample.com"
msgstr ""

#. module: l10n_id
#: model:ir.model,name:l10n_id.model_account_chart_template
msgid "Account Chart Template"
msgstr ""

#. module: l10n_id
#: model:ir.model,name:l10n_id.model_res_partner_bank
msgid "Bank Accounts"
msgstr ""

#. module: l10n_id
#. odoo-python
#: code:addons/l10n_id/models/res_bank.py:0
#: code:addons/l10n_id/models/res_bank.py:0
msgid ""
"Communication with QRIS failed. QRIS returned with the following error: %s"
msgstr ""

#. module: l10n_id
#. odoo-python
#: code:addons/l10n_id/models/res_bank.py:0
msgid "Could not establish a connection to the QRIS API."
msgstr ""

#. module: l10n_id
#: model_terms:res.company,company_details:l10n_id.demo_company_id
msgid ""
"ID Company<br>\n"
"AE<br>\n"
"Moncongloe YO 90241<br>\n"
"Indonesia"
msgstr ""

#. module: l10n_id
#: model:account.account.tag,name:l10n_id.ppn_tag
msgid "PPN - 08"
msgstr ""

#. module: l10n_id
#. odoo-python
#: code:addons/l10n_id/models/res_bank.py:0
msgid "QRIS"
msgstr ""

#. module: l10n_id
#: model:ir.model.fields,field_description:l10n_id.field_account_setup_bank_manual_config__l10n_id_qris_api_key
#: model:ir.model.fields,field_description:l10n_id.field_res_partner_bank__l10n_id_qris_api_key
msgid "QRIS API Key"
msgstr ""

#. module: l10n_id
#: model:ir.model.fields,field_description:l10n_id.field_account_setup_bank_manual_config__l10n_id_qris_mid
#: model:ir.model.fields,field_description:l10n_id.field_res_partner_bank__l10n_id_qris_mid
msgid "QRIS Merchant ID"
msgstr ""

#. module: l10n_id
#. odoo-python
#: code:addons/l10n_id/models/res_bank.py:0
msgid ""
"To use QRIS QR code, Please setup the QRIS API Key and Merchant ID on the "
"bank's configuration"
msgstr ""

#. module: l10n_id
#. odoo-python
#: code:addons/l10n_id/models/res_bank.py:0
msgid ""
"You cannot generate a QRIS QR code with a bank account that is not in "
"Indonesia."
msgstr ""

#. module: l10n_id
#. odoo-python
#: code:addons/l10n_id/models/res_bank.py:0
msgid "You cannot generate a QRIS QR code with a currency other than IDR"
msgstr ""
