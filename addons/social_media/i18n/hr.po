# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* social_media
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON>, 2024
# <PERSON><PERSON><PERSON> <karol<PERSON>.<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:56+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Croatian (https://app.transifex.com/odoo/teams/41243/hr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: hr\n"
"Plural-Forms: nplurals=3; plural=n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2;\n"

#. module: social_media
#: model:ir.model,name:social_media.model_res_company
msgid "Companies"
msgstr "Tvrtke"

#. module: social_media
#: model:ir.model.fields,field_description:social_media.field_res_company__social_facebook
msgid "Facebook Account"
msgstr "Facebook račun"

#. module: social_media
#: model:ir.model.fields,field_description:social_media.field_res_company__social_github
msgid "GitHub Account"
msgstr "GitHub račun"

#. module: social_media
#: model:ir.model.fields,field_description:social_media.field_res_company__social_instagram
msgid "Instagram Account"
msgstr "Instagram račun"

#. module: social_media
#: model:ir.model.fields,field_description:social_media.field_res_company__social_linkedin
msgid "LinkedIn Account"
msgstr "LinkedIn račun"

#. module: social_media
#: model_terms:ir.ui.view,arch_db:social_media.view_company_form_inherit_social_media
msgid "Social Media"
msgstr "Društveni mediji"

#. module: social_media
#: model:ir.model.fields,field_description:social_media.field_res_company__social_tiktok
msgid "TikTok Account"
msgstr ""

#. module: social_media
#: model:ir.model.fields,field_description:social_media.field_res_company__social_twitter
msgid "X Account"
msgstr ""

#. module: social_media
#: model:ir.model.fields,field_description:social_media.field_res_company__social_youtube
msgid "Youtube Account"
msgstr "Youtube račun"
