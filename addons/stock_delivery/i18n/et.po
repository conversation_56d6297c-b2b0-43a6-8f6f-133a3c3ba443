# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* stock_delivery
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON>, 2024
# <PERSON><PERSON>, 2024
# <PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON>, 2024
# <PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <AUTHOR> <EMAIL>, 2024
# <PERSON> <<EMAIL>>, 2024
# <PERSON>, 2024
# <PERSON><PERSON>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:56+0000\n"
"PO-Revision-Date: 2024-09-25 09:42+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2025\n"
"Language-Team: Estonian (https://app.transifex.com/odoo/teams/41243/et/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: et\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: stock_delivery
#. odoo-python
#: code:addons/stock_delivery/models/sale_order.py:0
msgid "%(name)s (Estimated Cost: %(cost)s)"
msgstr ""

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.view_quant_package_weight_form
msgid "(computed:"
msgstr "(arvutatud: "

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.stock_report_delivery_package_section_line_inherit_delivery
msgid "<span> - Weight (estimated): </span>"
msgstr "<span> - Kaal (hinnanguline): </span>"

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.delivery_stock_report_delivery_no_package_section_line
#: model_terms:ir.ui.view,arch_db:stock_delivery.stock_report_delivery_package_section_line_inherit_delivery
msgid "<span> - Weight: </span>"
msgstr "<span> - Kaal: </span>"

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.report_package_barcode_small_delivery
msgid "<span>Shipping Weight: </span>"
msgstr "<span>Saadetise kaal: </span>"

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.report_delivery_document2
#: model_terms:ir.ui.view,arch_db:stock_delivery.report_shipping2
msgid "<strong>Carrier</strong>"
msgstr ""

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.report_shipping2
msgid "<strong>Shipping Method:</strong>"
msgstr ""

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.report_package_barcode_delivery
msgid "<strong>Shipping Weight: </strong>"
msgstr "<strong>Saadetise kaal: </strong>"

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.report_package_barcode_delivery
msgid ""
"<strong>Shipping Weight:</strong>\n"
"                    <br/>"
msgstr ""
"<strong>Saadetise kaal:</strong>\n"
"                    <br/>"

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.report_delivery_document2
msgid "<strong>Total Weight</strong>"
msgstr ""

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.report_delivery_document2
msgid "<strong>Tracking Number</strong>"
msgstr ""

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.report_package_barcode_delivery
msgid "<strong>Weight: </strong>"
msgstr "<strong>Kaal: </strong>"

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.report_shipping2
msgid "<strong>Weight</strong>"
msgstr ""

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_stock_route__shipping_selectable
msgid "Applicable on Shipping Methods"
msgstr ""

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.view_picking_withcarrier_out_form
msgid "Cancel"
msgstr "Tühista"

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.view_picking_withcarrier_out_form
msgid ""
"Cancelling a delivery may not be undoable. Are you sure you want to "
"continue?"
msgstr ""

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_stock_move_line__carrier_id
#: model:ir.model.fields,field_description:stock_delivery.field_stock_package_type__package_carrier_type
#: model:ir.model.fields,field_description:stock_delivery.field_stock_picking__carrier_id
#: model_terms:ir.ui.view,arch_db:stock_delivery.stock_move_line_view_search_delivery
msgid "Carrier"
msgstr "Vedaja"

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_stock_package_type__shipper_package_code
msgid "Carrier Code"
msgstr "Transpordifirma kood"

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.stock_move_line_view_search_delivery
msgid "Carrier name"
msgstr "Transpordifirma nimi"

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_choose_delivery_package__company_id
msgid "Company"
msgstr "Ettevõte"

#. module: stock_delivery
#. odoo-python
#: code:addons/stock_delivery/models/stock_picking.py:0
msgid "Cost: %(price).2f %(currency)s"
msgstr ""

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_choose_delivery_package__create_uid
msgid "Created by"
msgstr "Loonud"

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_choose_delivery_package__create_date
msgid "Created on"
msgstr "Loodud"

#. module: stock_delivery
#: model:ir.model,name:stock_delivery.model_choose_delivery_carrier
msgid "Delivery Carrier Selection Wizard"
msgstr "Tarne transpordiviisi valiku loomine"

#. module: stock_delivery
#: model:ir.ui.menu,name:stock_delivery.menu_action_delivery_carrier_form
msgid "Delivery Methods"
msgstr "Tarneviisid"

#. module: stock_delivery
#: model:ir.model,name:stock_delivery.model_choose_delivery_package
msgid "Delivery Package Selection Wizard"
msgstr "Tarneviiside valiku loomine"

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_choose_delivery_package__delivery_package_type_id
msgid "Delivery Package Type"
msgstr "Tarnepaki tüübid"

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.vpicktree_view_tree
msgid "Destination"
msgstr "Sihtkoht"

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_stock_move_line__destination_country_code
#: model:ir.model.fields,field_description:stock_delivery.field_stock_picking__destination_country_code
msgid "Destination Country"
msgstr "Sihtriik"

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.choose_delivery_package_view_form
msgid "Discard"
msgstr "Loobu"

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_choose_delivery_package__display_name
msgid "Display Name"
msgstr "Kuvatav nimi"

#. module: stock_delivery
#: model:ir.actions.act_window,name:stock_delivery.act_delivery_trackers_url
msgid "Display tracking links"
msgstr "Kuva jälgimislingid"

#. module: stock_delivery
#: model:ir.model.fields,help:stock_delivery.field_delivery_carrier__invoice_policy
msgid ""
"Estimated Cost: the customer will be invoiced the estimated cost of the shipping.\n"
"Real Cost: the customer will be invoiced the real cost of the shipping, the cost of theshipping will be updated on the SO after the delivery."
msgstr ""
"Hinnanguline kulu: kliendile esitatakse arve hinnangulise saatmiskulu põhjal.\n"
"Tegeliku kulu: kliendile esitatakse arve tegeliku saatmiskulu põhjal; saatmiskulu uuendatakse tellimuses pärast kohaletoimetamist."

#. module: stock_delivery
#. odoo-python
#: code:addons/stock_delivery/models/stock_picking.py:0
msgid "Exception occurred with respect to carrier on the transfer"
msgstr ""

#. module: stock_delivery
#. odoo-python
#: code:addons/stock_delivery/models/stock_picking.py:0
msgid "Exception:"
msgstr "Erand:"

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.report_shipping2
msgid "FedEx"
msgstr "FedEx"

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_product_product__hs_code
#: model:ir.model.fields,field_description:stock_delivery.field_product_template__hs_code
#: model_terms:ir.ui.view,arch_db:stock_delivery.product_template_hs_code
#: model_terms:ir.ui.view,arch_db:stock_delivery.report_delivery_document2
msgid "HS Code"
msgstr "HS kood"

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_choose_delivery_package__id
msgid "ID"
msgstr "ID"

#. module: stock_delivery
#: model:ir.model,name:stock_delivery.model_stock_route
msgid "Inventory Routes"
msgstr "Lao marsruudid"

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_delivery_carrier__invoice_policy
msgid "Invoicing Policy"
msgstr "Arvelduse meetod"

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_stock_picking__is_return_picking
msgid "Is Return Picking"
msgstr "on tagastussiire."

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_choose_delivery_package__write_uid
msgid "Last Updated by"
msgstr "Viimati uuendatud"

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_choose_delivery_package__write_date
msgid "Last Updated on"
msgstr "Viimati uuendatud"

#. module: stock_delivery
#. odoo-python
#: code:addons/stock_delivery/models/stock_picking.py:0
msgid "Manual actions might be needed."
msgstr ""

#. module: stock_delivery
#: model:ir.model.fields.selection,name:stock_delivery.selection__stock_package_type__package_carrier_type__none
msgid "No carrier integration"
msgstr "Puudu integratsioon transpordifirmaga"

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.delivery_tracking_url_warning_form
msgid "OK"
msgstr "OK"

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_product_product__country_of_origin
#: model:ir.model.fields,field_description:stock_delivery.field_product_template__country_of_origin
msgid "Origin of Goods"
msgstr "Kaupade päritolu"

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.choose_delivery_package_view_form
msgid "Package"
msgstr "Pakend"

#. module: stock_delivery
#. odoo-python
#: code:addons/stock_delivery/models/stock_picking.py:0
msgid "Package Details"
msgstr "Pakendi detailid"

#. module: stock_delivery
#. odoo-python
#: code:addons/stock_delivery/wizard/choose_delivery_package.py:0
msgid "Package too heavy!"
msgstr "Pakend on liiga raske!"

#. module: stock_delivery
#: model:ir.model,name:stock_delivery.model_stock_quant_package
msgid "Packages"
msgstr "Pakendid"

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_choose_delivery_package__picking_id
msgid "Picking"
msgstr "Laoleht"

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.sale_order_portal_content_inherit_sale_stock_inherit_website_sale
#: model_terms:ir.ui.view,arch_db:stock_delivery.view_picking_withcarrier_out_form
msgid "Print Return Label"
msgstr "Prindi tagastussilt"

#. module: stock_delivery
#: model:ir.model,name:stock_delivery.model_product_template
msgid "Product"
msgstr "Toode"

#. module: stock_delivery
#: model:ir.model,name:stock_delivery.model_stock_move_line
msgid "Product Moves (Stock Move Line)"
msgstr "Toote liikumised"

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_stock_picking__delivery_type
msgid "Provider"
msgstr "Teenusepakkuja"

#. module: stock_delivery
#: model:ir.model.fields.selection,name:stock_delivery.selection__delivery_carrier__invoice_policy__real
msgid "Real cost"
msgstr "Reaalne kulu"

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_stock_picking__return_label_ids
msgid "Return Label"
msgstr "Tagastussilt"

#. module: stock_delivery
#: model:ir.model,name:stock_delivery.model_stock_return_picking
msgid "Return Picking"
msgstr "Tagastuskorje"

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_delivery_carrier__route_ids
#: model_terms:ir.ui.view,arch_db:stock_delivery.view_delivery_carrier_form_inherit_stock_delivery
msgid "Routes"
msgstr "Marsruudid"

#. module: stock_delivery
#: model:ir.model.fields,help:stock_delivery.field_product_product__country_of_origin
#: model:ir.model.fields,help:stock_delivery.field_product_template__country_of_origin
msgid ""
"Rules of origin determine where goods originate, i.e. not where they have been shipped from, but where they have been produced or manufactured.\n"
"As such, the ‘origin’ is the 'economic nationality' of goods traded in commerce."
msgstr ""

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_stock_move_line__sale_price
msgid "Sale Price"
msgstr "Müügihind"

#. module: stock_delivery
#: model:ir.model,name:stock_delivery.model_sale_order
msgid "Sales Order"
msgstr "Müügitellimus"

#. module: stock_delivery
#: model:ir.model,name:stock_delivery.model_sale_order_line
msgid "Sales Order Line"
msgstr "Müügitellimuse rida"

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.choose_delivery_package_view_form
msgid "Save"
msgstr "Salvesta"

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.view_picking_withcarrier_out_form
msgid "Send to Shipper"
msgstr "Send to Shipper"

#. module: stock_delivery
#. odoo-python
#: code:addons/stock_delivery/models/stock_picking.py:0
msgid ""
"Shipment sent to carrier %(carrier_name)s for shipping with tracking number "
"%(ref)s"
msgstr ""

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_stock_picking__carrier_price
msgid "Shipping Cost"
msgstr "Transpordi hinnad"

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.view_picking_withcarrier_out_form
msgid "Shipping Information"
msgstr "Tarneinfo"

#. module: stock_delivery
#: model:ir.model,name:stock_delivery.model_delivery_carrier
#: model_terms:ir.ui.view,arch_db:stock_delivery.stock_location_route_view_form_inherit_stock_delivery
msgid "Shipping Methods"
msgstr "Tarneviisid"

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_choose_delivery_package__shipping_weight
msgid "Shipping Weight"
msgstr "Saadetise kaal"

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.report_package_barcode_small_delivery
msgid "Shipping Weight:"
msgstr "Shipping Weight:"

#. module: stock_delivery
#: model:ir.model.fields,help:stock_delivery.field_product_product__hs_code
#: model:ir.model.fields,help:stock_delivery.field_product_template__hs_code
msgid "Standardized code for international shipping and goods declaration."
msgstr ""

#. module: stock_delivery
#: model:ir.model,name:stock_delivery.model_stock_move
msgid "Stock Move"
msgstr "Laoliikumine"

#. module: stock_delivery
#: model:ir.model,name:stock_delivery.model_stock_package_type
msgid "Stock package type"
msgstr "Lao pakendi tüüp"

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_stock_quant_package__weight_uom_rounding
msgid "Technical field indicating weight's number of decimal places"
msgstr "Tehniline väli, mis näitab kaalu kümnendkohtade arvu."

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_stock_quant_package__weight_is_kg
msgid "Technical field indicating whether weight uom is kg or not (i.e. lb)"
msgstr "Tehniline väli, kas kaalu mõõtühik on kg või ei ole"

#. module: stock_delivery
#: model:ir.model.fields,help:stock_delivery.field_stock_move_line__destination_country_code
#: model:ir.model.fields,help:stock_delivery.field_stock_picking__destination_country_code
msgid ""
"The ISO country code in two chars. \n"
"You can use this field for quick search."
msgstr ""
"ISO riigi kood kahe tähega. \n"
"Saad kasutada seda välja kiirotsinguks."

#. module: stock_delivery
#. odoo-python
#: code:addons/stock_delivery/models/delivery_carrier.py:0
msgid ""
"The package cannot be created because the total weight of the products in "
"the picking is 0.0 %s"
msgstr ""

#. module: stock_delivery
#. odoo-python
#: code:addons/stock_delivery/wizard/choose_delivery_carrier.py:0
msgid "The shipping price will be set once the delivery is done."
msgstr "Tarne maksumus määratakse pärast kohaletoimetamist."

#. module: stock_delivery
#. odoo-python
#: code:addons/stock_delivery/wizard/choose_delivery_package.py:0
msgid ""
"The weight of your package is higher than the maximum weight authorized for "
"this package type. Please choose another package type."
msgstr ""

#. module: stock_delivery
#. odoo-python
#: code:addons/stock_delivery/models/delivery_carrier.py:0
msgid "There is no matching delivery rule."
msgstr ""

#. module: stock_delivery
#: model:ir.model.fields,help:stock_delivery.field_stock_quant_package__weight
msgid "Total weight of all the products contained in the package."
msgstr ""

#. module: stock_delivery
#: model:ir.model.fields,help:stock_delivery.field_stock_picking__weight
msgid "Total weight of the products in the picking."
msgstr ""

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.delivery_tracking_url_warning_form
msgid "Trackers URL"
msgstr "Jälgijate URL"

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.view_picking_withcarrier_out_form
msgid "Tracking"
msgstr "Jälgimine"

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_stock_picking__carrier_tracking_ref
msgid "Tracking Reference"
msgstr "Jälgimiskood"

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_stock_picking__carrier_tracking_url
msgid "Tracking URL"
msgstr "Jälgimise URL"

#. module: stock_delivery
#. odoo-python
#: code:addons/stock_delivery/models/stock_picking.py:0
msgid "Tracking links for shipment:"
msgstr ""

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.sale_order_portal_content_inherit_sale_stock_inherit_website_sale
msgid "Tracking:"
msgstr "Jälgimine:"

#. module: stock_delivery
#: model:ir.model,name:stock_delivery.model_stock_picking
msgid "Transfer"
msgstr "Siirded"

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_stock_move__weight
#: model:ir.model.fields,field_description:stock_delivery.field_stock_picking__weight
#: model:ir.model.fields,field_description:stock_delivery.field_stock_quant_package__weight
#: model_terms:ir.ui.view,arch_db:stock_delivery.view_picking_withcarrier_out_form
msgid "Weight"
msgstr "Kaal"

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.view_picking_withcarrier_out_form
msgid "Weight for shipping"
msgstr "Saadetise kaal"

#. module: stock_delivery
#: model:ir.model.fields,field_description:stock_delivery.field_choose_delivery_package__weight_uom_name
#: model:ir.model.fields,field_description:stock_delivery.field_stock_picking__weight_uom_name
#: model:ir.model.fields,field_description:stock_delivery.field_stock_quant_package__weight_uom_name
msgid "Weight unit of measure label"
msgstr "Kaalu mõõtühiku silt"

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.report_package_barcode_small_delivery
msgid "Weight:"
msgstr "Kaal:"

#. module: stock_delivery
#. odoo-python
#: code:addons/stock_delivery/models/stock_picking.py:0
msgid ""
"You cannot pack products into the same package when they have different "
"carriers (i.e. check that all of their transfers have a carrier assigned and"
" are using the same carrier)."
msgstr ""

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.delivery_tracking_url_warning_form
msgid "You have multiple tracker links, they are available in the chatter."
msgstr ""

#. module: stock_delivery
#. odoo-python
#: code:addons/stock_delivery/models/stock_picking.py:0
msgid ""
"Your delivery method has no redirect on courier provider's website to track "
"this order."
msgstr ""
"Your delivery method has no redirect on courier provider's website to track "
"this order."

#. module: stock_delivery
#: model:ir.ui.menu,name:stock_delivery.menu_delivery_zip_prefix
msgid "Zip Prefix"
msgstr "Sihtnumbri prefiks"

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.label_package_template_view_delivery
msgid "^A0N,44,33^FDShipping Weight:"
msgstr "^A0N,44,33^FDSaadetise kaal:"

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.label_package_template_view_delivery
msgid "^A0N,44,33^FDWeight:"
msgstr "^A0N,44,33^FDKaal:"

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.label_package_template_view_delivery
msgid "^FO310,200"
msgstr "^FO310,200"

#. module: stock_delivery
#: model_terms:ir.ui.view,arch_db:stock_delivery.label_package_template_view_delivery
msgid "^FS"
msgstr "^FS"
