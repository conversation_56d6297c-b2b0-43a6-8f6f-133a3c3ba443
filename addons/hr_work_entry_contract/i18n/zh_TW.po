# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_work_entry_contract
# 
# Translators:
# Wil <PERSON>, 2024
# <PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 08:39+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON>, 2024\n"
"Language-Team: Chinese (Taiwan) (https://app.transifex.com/odoo/teams/41243/zh_TW/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_TW\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: hr_work_entry_contract
#: model:ir.model.fields,help:hr_work_entry_contract.field_hr_contract__work_entry_source
#: model:ir.model.fields,help:hr_work_entry_contract.field_hr_work_entry__work_entry_source
msgid ""
"\n"
"        Defines the source for work entries generation\n"
"\n"
"        Working Schedule: Work entries will be generated from the working hours below.\n"
"        Attendances: Work entries will be generated from the employee's attendances. (requires Attendance app)\n"
"        Planning: Work entries will be generated from the employee's planning. (requires Planning app)\n"
"    "
msgstr ""
"\n"
"        定義了工作記錄生成的來源\n"
"\n"
"        工作安排：工作記錄將從以下的工作時間生成。\n"
"        出勤：工作記錄將從員工的出勤生成。(需要出勤應用程序)\n"
"        計劃：工作記錄將從員工的計劃生成。(需要計劃應用程序)\n"
"    "

#. module: hr_work_entry_contract
#. odoo-python
#: code:addons/hr_work_entry_contract/models/hr_work_entry.py:0
msgid ""
"%(employee)s does not have a contract from %(date_start)s to %(date_end)s."
msgstr "%(employee)s 在以下期間沒有合約：由 %(date_start)s 至 %(date_end)s。"

#. module: hr_work_entry_contract
#. odoo-python
#: code:addons/hr_work_entry_contract/models/hr_work_entry.py:0
msgid ""
"%(employee)s has multiple contracts from %(date_start)s to %(date_end)s. A "
"work entry cannot overlap multiple contracts."
msgstr ""
"員工 %(employee)s 在 %(date_start)s 至 %(date_end)s 期間，有多份不同合約。工作記項不可與多份合約重疊。"

#. module: hr_work_entry_contract
#: model_terms:ir.ui.view,arch_db:hr_work_entry_contract.hr_work_entry_regeneration_wizard
msgid ""
"<i class=\"fa fa-exclamation-triangle me-1\" title=\"Warning\"/>You are not "
"allowed to regenerate validated work entries"
msgstr "<i class=\"fa fa-exclamation-triangle me-1\" title=\"警告\"/> 已驗證的工作記錄不可重新生成"

#. module: hr_work_entry_contract
#: model_terms:ir.ui.view,arch_db:hr_work_entry_contract.hr_work_entry_regeneration_wizard
msgid "<i class=\"fa fa-info-circle me-1\" title=\"Hint\"/>"
msgstr "<i class=\"fa fa-info-circle me-1\" title=\"提示\"/>"

#. module: hr_work_entry_contract
#: model_terms:ir.ui.view,arch_db:hr_work_entry_contract.hr_work_entry_regeneration_wizard
msgid ""
"<span class=\"text-muted\">Warning: The work entry regeneration will delete "
"all manual changes on the selected period.</span>"
msgstr "<span class=\"text-muted\">警告：重新生成工作記錄將刪除選定時間段的所有手動更改。</span>"

#. module: hr_work_entry_contract
#: model:ir.model.fields,help:hr_work_entry_contract.field_hr_work_entry_type__is_leave
msgid "Allow the work entry type to be linked with time off types."
msgstr "允許工時紀錄類型與休假類型相關聯"

#. module: hr_work_entry_contract
#: model_terms:ir.ui.view,arch_db:hr_work_entry_contract.hr_work_entry_regeneration_wizard
msgid "Cancel"
msgstr "取消"

#. module: hr_work_entry_contract
#: model:hr.work.entry.type,name:hr_work_entry_contract.work_entry_type_compensatory
msgid "Compensatory Time Off"
msgstr "補假"

#. module: hr_work_entry_contract
#: model:ir.model.fields,field_description:hr_work_entry_contract.field_hr_work_entry__contract_id
msgid "Contract"
msgstr "合約"

#. module: hr_work_entry_contract
#: model:ir.model.fields,field_description:hr_work_entry_contract.field_hr_work_entry_regeneration_wizard__create_uid
msgid "Created by"
msgstr "建立人員"

#. module: hr_work_entry_contract
#: model:ir.model.fields,field_description:hr_work_entry_contract.field_hr_work_entry_regeneration_wizard__create_date
msgid "Created on"
msgstr "建立於"

#. module: hr_work_entry_contract
#: model:ir.model.fields,field_description:hr_work_entry_contract.field_hr_work_entry_regeneration_wizard__display_name
msgid "Display Name"
msgstr "顯示名稱"

#. module: hr_work_entry_contract
#: model:ir.model.fields,field_description:hr_work_entry_contract.field_hr_work_entry_regeneration_wizard__earliest_available_date_message
msgid "Earliest Available Date Message"
msgstr "最早可用日期消息"

#. module: hr_work_entry_contract
#: model:ir.model.fields,field_description:hr_work_entry_contract.field_hr_work_entry_regeneration_wizard__earliest_available_date
msgid "Earliest date"
msgstr "最早日期"

#. module: hr_work_entry_contract
#: model:ir.model,name:hr_work_entry_contract.model_hr_employee
#: model:ir.model.fields,field_description:hr_work_entry_contract.field_hr_work_entry__employee_id
msgid "Employee"
msgstr "員工"

#. module: hr_work_entry_contract
#: model:ir.model,name:hr_work_entry_contract.model_hr_contract
msgid "Employee Contract"
msgstr "員工契約"

#. module: hr_work_entry_contract
#: model:ir.model.fields,field_description:hr_work_entry_contract.field_hr_work_entry_regeneration_wizard__employee_ids
msgid "Employees"
msgstr "員工"

#. module: hr_work_entry_contract
#. odoo-javascript
#: code:addons/hr_work_entry_contract/static/src/views/work_entry_calendar/work_entry_calendar_model.js:0
msgid "Everybody's work entries"
msgstr "大家的工時紀錄"

#. module: hr_work_entry_contract
#: model:hr.work.entry.type,name:hr_work_entry_contract.work_entry_type_extra_hours
msgid "Extra Hours"
msgstr "加班時數"

#. module: hr_work_entry_contract
#: model:ir.model.fields,field_description:hr_work_entry_contract.field_hr_work_entry_regeneration_wizard__date_from
msgid "From"
msgstr "由"

#. module: hr_work_entry_contract
#: model_terms:ir.ui.view,arch_db:hr_work_entry_contract.hr_contract_view_form_inherit_work_entry
msgid "Fully Flexible"
msgstr "完全靈活"

#. module: hr_work_entry_contract
#: model:ir.actions.server,name:hr_work_entry_contract.ir_cron_generate_missing_work_entries_ir_actions_server
msgid "Generate Missing Work Entries"
msgstr "生成缺失的工作條目"

#. module: hr_work_entry_contract
#: model:ir.model.fields,field_description:hr_work_entry_contract.field_hr_contract__date_generated_from
msgid "Generated From"
msgstr "生成自"

#. module: hr_work_entry_contract
#: model:ir.model.fields,field_description:hr_work_entry_contract.field_hr_contract__date_generated_to
msgid "Generated To"
msgstr "生成為"

#. module: hr_work_entry_contract
#: model:hr.work.entry.type,name:hr_work_entry_contract.work_entry_type_leave
msgid "Generic Time Off"
msgstr "一般休假"

#. module: hr_work_entry_contract
#: model:ir.model,name:hr_work_entry_contract.model_hr_work_entry
msgid "HR Work Entry"
msgstr "HR工時紀錄"

#. module: hr_work_entry_contract
#: model:ir.model,name:hr_work_entry_contract.model_hr_work_entry_type
msgid "HR Work Entry Type"
msgstr "HR工時紀錄類型"

#. module: hr_work_entry_contract
#: model:hr.work.entry.type,name:hr_work_entry_contract.work_entry_type_home_working
msgid "Home Working"
msgstr "在家工作"

#. module: hr_work_entry_contract
#: model:ir.model.fields,field_description:hr_work_entry_contract.field_hr_work_entry_regeneration_wizard__id
msgid "ID"
msgstr "識別號"

#. module: hr_work_entry_contract
#. odoo-python
#: code:addons/hr_work_entry_contract/wizard/hr_work_entry_regeneration_wizard.py:0
msgid ""
"In order to regenerate the work entries, you need to provide the wizard with"
" an employee_id, a date_from and a date_to. In addition to that, the time "
"interval defined by date_from and date_to must not contain any validated "
"work entries."
msgstr ""
"為了重新生成工時紀錄，您需要為嚮導提供一個employee_id、一個date_from 和一個date_to。除此之外，由 date_from 和 "
"date_to 定義的時間間隔不得包含任何經過驗證的工時紀錄。"

#. module: hr_work_entry_contract
#. odoo-javascript
#: code:addons/hr_work_entry_contract/static/src/components/work_entry_source_field/work_entry_source_field.js:0
msgid ""
"Invalid option: For fully flexible calendars, the work entry source cannot "
"be 'Working Hours'."
msgstr "選項無效：對於完全靈活的日曆，工作記項來源不可以是「工作時間」。"

#. module: hr_work_entry_contract
#: model:ir.model.fields,field_description:hr_work_entry_contract.field_hr_contract__last_generation_date
msgid "Last Generation Date"
msgstr "最後生成的日期"

#. module: hr_work_entry_contract
#: model:ir.model.fields,field_description:hr_work_entry_contract.field_hr_work_entry_regeneration_wizard__write_uid
msgid "Last Updated by"
msgstr "最後更新者"

#. module: hr_work_entry_contract
#: model:ir.model.fields,field_description:hr_work_entry_contract.field_hr_work_entry_regeneration_wizard__write_date
msgid "Last Updated on"
msgstr "最後更新於"

#. module: hr_work_entry_contract
#: model:ir.model.fields,field_description:hr_work_entry_contract.field_hr_work_entry_regeneration_wizard__latest_available_date_message
msgid "Latest Available Date Message"
msgstr "最新可用日期消息"

#. module: hr_work_entry_contract
#: model:ir.model.fields,field_description:hr_work_entry_contract.field_hr_work_entry_regeneration_wizard__latest_available_date
msgid "Latest date"
msgstr "最晚日期"

#. module: hr_work_entry_contract
#: model:hr.work.entry.type,name:hr_work_entry_contract.work_entry_type_long_leave
msgid "Long Term Time Off"
msgstr "長期休假"

#. module: hr_work_entry_contract
#: model:hr.work.entry.type,name:hr_work_entry_contract.work_entry_type_legal_leave
msgid "Paid Time Off"
msgstr "年假"

#. module: hr_work_entry_contract
#: model:ir.model,name:hr_work_entry_contract.model_hr_work_entry_regeneration_wizard
#: model_terms:ir.ui.view,arch_db:hr_work_entry_contract.hr_work_entry_regeneration_wizard
msgid "Regenerate Employee Work Entries"
msgstr "重新生成員工工時紀錄"

#. module: hr_work_entry_contract
#. odoo-javascript
#: code:addons/hr_work_entry_contract/static/src/views/work_entry_calendar/work_entry_calendar.xml:0
#: model_terms:ir.ui.view,arch_db:hr_work_entry_contract.hr_work_entry_regeneration_wizard
msgid "Regenerate Work Entries"
msgstr "重新生成工時紀錄"

#. module: hr_work_entry_contract
#: model:ir.model,name:hr_work_entry_contract.model_resource_calendar
msgid "Resource Working Time"
msgstr "資源工作時間"

#. module: hr_work_entry_contract
#: model:ir.model.fields,field_description:hr_work_entry_contract.field_hr_work_entry_regeneration_wizard__search_criteria_completed
msgid "Search Criteria Completed"
msgstr "已完成搜索條件"

#. module: hr_work_entry_contract
#: model:hr.work.entry.type,name:hr_work_entry_contract.work_entry_type_sick_leave
msgid "Sick Time Off"
msgstr "病假"

#. module: hr_work_entry_contract
#. odoo-python
#: code:addons/hr_work_entry_contract/models/hr_contract.py:0
msgid ""
"Sorry, generating work entries from cancelled contracts is not allowed."
msgstr "抱歉，不允許從取消的合約中生成工作條目。"

#. module: hr_work_entry_contract
#. odoo-python
#: code:addons/hr_work_entry_contract/wizard/hr_work_entry_regeneration_wizard.py:0
msgid ""
"The from date must be >= '%(earliest_available_date)s' and the to date must "
"be <= '%(latest_available_date)s', which correspond to the generated work "
"entries time interval."
msgstr ""
"起始日期必須 >= '%(earliest_available_date)s'，結束日期必須為 <= "
"'%(latest_available_date)s'，它們對應於生成的工時紀錄時間間隔。"

#. module: hr_work_entry_contract
#: model_terms:ir.ui.view,arch_db:hr_work_entry_contract.hr_work_entry_contract_view_form_inherit
msgid "This work entry cannot be validated. The work entry type is undefined."
msgstr "無法驗證此工時紀錄。工時紀錄類型未定義。"

#. module: hr_work_entry_contract
#: model:ir.model.fields,field_description:hr_work_entry_contract.field_hr_work_entry_type__is_leave
msgid "Time Off"
msgstr "休假"

#. module: hr_work_entry_contract
#: model:ir.model.fields,field_description:hr_work_entry_contract.field_hr_work_entry_regeneration_wizard__date_to
msgid "To"
msgstr "至"

#. module: hr_work_entry_contract
#: model:hr.work.entry.type,name:hr_work_entry_contract.work_entry_type_unpaid_leave
msgid "Unpaid"
msgstr "無薪假"

#. module: hr_work_entry_contract
#: model:ir.model.fields,field_description:hr_work_entry_contract.field_hr_work_entry_regeneration_wizard__valid
msgid "Valid"
msgstr "有效"

#. module: hr_work_entry_contract
#: model_terms:ir.ui.view,arch_db:hr_work_entry_contract.hr_work_entry_regeneration_wizard
msgid "Work Entries"
msgstr "工作分錄"

#. module: hr_work_entry_contract
#: model:ir.model.fields,field_description:hr_work_entry_contract.field_hr_work_entry_regeneration_wizard__validated_work_entry_ids
msgid "Work Entries Within Interval"
msgstr "間隔內的工時紀錄"

#. module: hr_work_entry_contract
#: model:ir.actions.act_window,name:hr_work_entry_contract.hr_work_entry_regeneration_wizard_action
msgid "Work Entry Regeneration"
msgstr "工時紀錄重建"

#. module: hr_work_entry_contract
#: model:ir.model.fields,field_description:hr_work_entry_contract.field_hr_contract__work_entry_source
#: model:ir.model.fields,field_description:hr_work_entry_contract.field_hr_work_entry__work_entry_source
msgid "Work Entry Source"
msgstr "工作記錄來源"

#. module: hr_work_entry_contract
#: model:ir.model.fields,field_description:hr_work_entry_contract.field_hr_contract__work_entry_source_calendar_invalid
msgid "Work Entry Source Calendar Invalid"
msgstr "工作記項來源日曆無效"

#. module: hr_work_entry_contract
#: model:ir.model.fields.selection,name:hr_work_entry_contract.selection__hr_contract__work_entry_source__calendar
msgid "Working Schedule"
msgstr "工作安排"
