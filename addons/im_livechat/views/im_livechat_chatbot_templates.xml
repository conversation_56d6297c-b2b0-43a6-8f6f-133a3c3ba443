<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <template id="chatbot_test_script_page">
            <t t-call="web.frontend_layout">
                <t t-set="no_livechat" t-value="True"/>
                <t t-set="head">
                    <script>
                        <t t-call="im_livechat.loader">
                            <t t-set="info" t-value="{ 'available': True, 'options': { 'init': { 'available_for_me': True, 'rule': { 'chatbot': chatbot_data }, 'storeData': storeData }, 'isTestChatbot': True, 'force_thread': channel_data }, 'server_url': server_url }"/>
                        </t>
                    </script>
                </t>
                <t t-set="title" t-value="chatbot_data['name']"/>

                <div id="wrap">
                    <div groups="im_livechat.im_livechat_group_user" t-ignore="true"
                            class="alert alert-info alert-dismissible rounded-0 fade show d-print-none css_editable_mode_hidden mb-0">
                        <div t-ignore="true" class="text-center">
                            <a t-attf-href="/odoo/action-im_livechat.chatbot_script_action/#{chatbot_data['id']}">
                                <span>You are currently testing</span>
                                <span t-out="chatbot_data['name']"/>
                                <i class="oi oi-fw oi-arrow-right"/>Back to edit mode
                            </a>
                        </div>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                </div>
            </t>
        </template>
    </data>
</odoo>
