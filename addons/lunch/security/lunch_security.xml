<?xml version="1.0" ?>
<odoo>
        <record model="ir.module.category" id="module_lunch_category">
            <field name="name">Lunch</field>
            <field name="description">Helps you handle your lunch needs, if you are a manager you will be able to create new products, cashmoves and to confirm or cancel orders.</field>
            <field name="sequence">16</field>
        </record>
        <record id="group_lunch_user" model="res.groups">
            <field name="name">User : Order your meal</field>
            <field name="category_id" ref="base.module_category_human_resources_lunch"/>
        </record>
        <record id="group_lunch_manager" model="res.groups">
            <field name="name">Administrator</field>
            <field name="implied_ids" eval="[(4, ref('group_lunch_user'))]"/>
            <field name="category_id" ref="base.module_category_human_resources_lunch"/>
            <field name="users" eval="[(4, ref('base.user_root')), (4, ref('base.user_admin'))]"/>
        </record>

    <data noupdate="1">

        <record id="lunch_mind_your_own_food_money" model="ir.rule">
            <field name="name">lunch.cashmove: do not see other people's cashmove</field>
            <field name="model_id" ref="model_lunch_cashmove"/>
            <field name="groups" eval="[(4, ref('group_lunch_user'))]"/>
            <field name="domain_force">[('user_id', '=', user.id)]</field>
        </record>
        <record id="lunch_mind_other_food_money" model="ir.rule">
            <field name="name">lunch.cashmove: do see other people's cashmove</field>
            <field name="model_id" ref="model_lunch_cashmove"/>
            <field name="groups" eval="[(4, ref('group_lunch_manager'))]"/>
            <field name="domain_force">[(1, '=', 1)]</field>
        </record>
        <record id="lunch_order_rule_delete" model="ir.rule">
            <field name="name">lunch.order: Only new and cancelled order lines deleted.</field>
            <field name="model_id" ref="lunch.model_lunch_order"/>
            <field name="domain_force">[('state', 'in', ('new', 'cancelled'))]</field>
            <field name="perm_read" eval="0"/>
            <field name="perm_write" eval="0"/>
            <field name="perm_create" eval="0"/>
            <field name="perm_unlink" eval="1" />
            <field name="groups" eval="[(4,ref('lunch.group_lunch_user'))]"/>
        </record>

        <record id="lunch_order_rule_write" model="ir.rule">
            <field name="name">lunch.order: Don't change confirmed order</field>
            <field name="model_id" ref="lunch.model_lunch_order"/>
            <field name="domain_force">[('state', '!=', 'confirmed'), ('user_id', '=', user.id)]</field>
            <field name="perm_read" eval="0"/>
            <field name="perm_create" eval="0"/>
            <field name="perm_unlink" eval="0"/>
            <field name="groups" eval="[(4, ref('base.group_user'))]"/>
        </record>

        <record id="lunch_order_rule_write_manager" model="ir.rule">
            <field name="name">manager can do whatever</field>
            <field name="model_id" ref="lunch.model_lunch_order"/>
            <field name="domain_force">[(1, '=', 1)]</field>
            <field name="perm_read" eval="0"/>
            <field name="perm_create" eval="0"/>
            <field name="perm_unlink" eval="0"/>
            <field name="groups" eval="[(4, ref('lunch.group_lunch_manager'))]"/>
        </record>

        <record id="base.default_user" model="res.users">
            <field name="groups_id" eval="[(4,ref('lunch.group_lunch_manager'))]"/>
        </record>

        <record id="ir_rule_lunch_supplier_multi_company" model="ir.rule">
            <field name="name">Lunch supplier: Multi Company</field>
            <field name="model_id" ref="model_lunch_supplier"/>
            <field name="domain_force">[('company_id', 'in', company_ids + [False])]</field>
        </record>

        <record id="ir_rule_lunch_order_multi_company" model="ir.rule">
            <field name="name">Lunch order: Multi Company</field>
            <field name="model_id" ref="model_lunch_order"/>
            <field name="domain_force">[('company_id', 'in', company_ids + [False])]</field>
        </record>

        <record id="ir_rule_lunch_product_multi_company" model="ir.rule">
            <field name="name">Lunch product: Multi Company</field>
            <field name="model_id" ref="model_lunch_product"/>
            <field name="domain_force">[('company_id', 'in', company_ids + [False])]</field>
        </record>

        <record id="ir_rule_lunch_product_category_multi_company" model="ir.rule">
            <field name="name">Lunch product category: Multi Company</field>
            <field name="model_id" ref="model_lunch_product_category"/>
            <field name="domain_force">[('company_id', 'in', company_ids + [False])]</field>
        </record>

        <record id="ir_rule_lunch_location_multi_company" model="ir.rule">
            <field name="name">Lunch location: Multi Company</field>
            <field name="model_id" ref="model_lunch_location"/>
            <field name="domain_force">[('company_id', 'in', company_ids + [False])]</field>
        </record>
    </data>
</odoo>
