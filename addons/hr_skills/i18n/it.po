# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_skills
# 
# Translators:
# <PERSON>, 2024
# Wil <PERSON>, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 08:39+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: Italian (https://app.transifex.com/odoo/teams/41243/it/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: it\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: hr_skills
#. odoo-python
#: code:addons/hr_skills/models/hr_skill_type.py:0
msgid "%s (copy)"
msgstr "%s (copia)"

#. module: hr_skills
#: model:ir.actions.report,print_report_name:hr_skills.action_report_employee_cv
msgid "'CV - %s' % (object.name)"
msgstr "'CV - %s' % (object.name)"

#. module: hr_skills
#: model_terms:ir.ui.view,arch_db:hr_skills.report_employee_cv_sidepanel
msgid "+1234567890"
msgstr "+1234567890"

#. module: hr_skills
#: model_terms:ir.ui.view,arch_db:hr_skills.report_employee_cv_main_panel
msgid "2022"
msgstr "2022"

#. module: hr_skills
#: model_terms:ir.ui.view,arch_db:hr_skills.report_employee_cv_main_panel
msgid "2023"
msgstr "2023"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_line_admin_7
msgid "A 2D/3D map generator for incremental games."
msgstr "Un generatore di mappe 2D/3D per giochi incrementali."

#. module: hr_skills
#. odoo-javascript
#: code:addons/hr_skills/static/src/fields/skills_one2many/skills_one2many.xml:0
#: code:addons/hr_skills/static/src/xml/resume_templates.xml:0
msgid "ADD"
msgstr "AGGIUNGI"

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill_report__active
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill_type__active
msgid "Active"
msgstr "Attivo"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_adaptability
msgid "Adaptability"
msgstr "Adattabilità"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_agile_scrum
msgid "Agile and Scrum methodologies"
msgstr "Metodologie Agile e Scum"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_jog_allenkeller
msgid "Allen-Keller"
msgstr "Allen-Keller"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_line_admin_5
msgid ""
"Allows to encrypt/decrypt plain text or files. Available as a web app or as "
"an API."
msgstr ""
"Permette di crittografare/decrittografare testi o file. Disponibile come app"
" web o API."

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_jth_goodman_inc
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_ngh_jackson_schwartz_and_aguirre
msgid "Analytical chemist"
msgstr "Chimico analitico"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_analytics
msgid "Analytics"
msgstr "Analitiche"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_android
msgid "Android"
msgstr "Android"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_arabic
msgid "Arabic"
msgstr "Arabo"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_stw_green_ltd
msgid "Arboriculturist"
msgstr "Arboricoltore"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_jth_wilkinson_plc
msgid "Architectural technologist"
msgstr "Tecnologo dell'architettura"

#. module: hr_skills
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_employee_skill_report_view_search
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_employee_skill_type_view_form
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_skill_type_view_search
msgid "Archived"
msgstr "In archivio"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_ngh_armidale_city_public_school
msgid "Armidale City Public School"
msgstr "Armidale City Public School"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_jve_arnoldcohen
msgid "Arnold-Cohen"
msgstr "Arnold-Cohen"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_niv_arroyo_ltd
msgid "Arroyo Ltd"
msgstr "Arroyo Ltd"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_chs_avoca_primary_school
msgid "Avoca Primary School"
msgstr "Avoca Primary School"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_al_bathurst_west_public_school
msgid "Bathurst West Public School"
msgstr "Bathurst West Public School"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_bengali
msgid "Bengali"
msgstr "Bengalese"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_big_data_technologies
msgid "Big data technologies (Hadoop,Spark)"
msgstr "Tecnologie big data (Hadoop, Spark)"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_fme_russellwebster
msgid "Biochemist, clinical"
msgstr "Biochimico clinico"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_admin_blue_mountains_grammar_school
msgid "Blue Mountains Grammar School"
msgstr "Blue Mountains Grammar School"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_chs_boyd_wilson_and_moore
msgid "Boyd, Wilson and Moore"
msgstr "Boyd, Wilson and Moore"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_mit_burns_lester_and_cuevas
msgid "Burns, Lester and Cuevas"
msgstr "Burns, Lester and Cuevas"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_line_admin_3
msgid "Burtho Inc."
msgstr "Burtho Inc."

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_c
msgid "C/C++"
msgstr "C/C++"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_cms
msgid "CMS"
msgstr "CMS"

#. module: hr_skills
#. odoo-javascript
#: code:addons/hr_skills/static/src/xml/resume_templates.xml:0
msgid "CREATE A NEW ENTRY"
msgstr "CREA NUOVA VOCE"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_css
msgid "CSS"
msgstr "CSS"

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_cv_wizard__can_show_others
msgid "Can Show Others"
msgstr "Può mostrare altri"

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_cv_wizard__can_show_skills
msgid "Can Show Skills"
msgstr "Può mostrare competenze"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_jep_garcia_and_sons
msgid "Careers information officer"
msgstr "Addetta alle informazioni sulla carriera"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_fpi_chavez_group
msgid "Chavez Group"
msgstr "Chavez Group"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_vad_christian_outreach_college
msgid "Christian Outreach College"
msgstr "Christian Outreach College"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_admin_lewisbailey
msgid "Civil Service fast streamer"
msgstr "Servizio civile"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_jog_claremont_college
msgid "Claremont College"
msgstr "Claremont College"

#. module: hr_skills
#: model:ir.model.fields.selection,name:hr_skills.selection__hr_resume_line__display_type__classic
msgid "Classic"
msgstr "Classico"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_cloud_computing
msgid "Cloud computing"
msgstr "Cloud computing"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_jep_cole_ltd
msgid "Cole Ltd"
msgstr "Cole Ltd"

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill__color
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill__color
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill_type__color
msgid "Color"
msgstr "Colore"

#. module: hr_skills
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_employee_cv_wizard_view_form
msgid "Colors"
msgstr "Colori"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_com
#: model:hr.skill,name:hr_skills.hr_skill_communication
msgid "Communication"
msgstr "Comunicazioni"

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill_report__company_id
msgid "Company"
msgstr "Azienda"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_fpi_hubbarddean
msgid "Conference centre manager"
msgstr "Responsabile centro congressi"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_conflict_management
msgid "Conflict Management"
msgstr "Gestione dei conflitti"

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_cv_wizard__show_contact
msgid "Contact Information"
msgstr "Informazioni contatto"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_stw_finley_rowe_and_adams
msgid "Copywriter, advertising"
msgstr "Copywriter, pubblicità"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_ngh_craigmore_south_junior_primary_school
msgid "Craigmore South Junior Primary School"
msgstr "Craigmore South Junior Primary School"

#. module: hr_skills
#. odoo-javascript
#: code:addons/hr_skills/static/src/fields/resume_one2many/resume_one2many.xml:0
msgid "Create a new entry"
msgstr "Crea nuova voce"

#. module: hr_skills
#. odoo-javascript
#: code:addons/hr_skills/static/src/fields/skills_one2many/skills_one2many.xml:0
msgid "Create new Skills"
msgstr "Crea nuove competenze"

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_cv_wizard__create_uid
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill__create_uid
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill_log__create_uid
#: model:ir.model.fields,field_description:hr_skills.field_hr_resume_line__create_uid
#: model:ir.model.fields,field_description:hr_skills.field_hr_resume_line_type__create_uid
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill__create_uid
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill_level__create_uid
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill_type__create_uid
msgid "Created by"
msgstr "Creato da"

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_cv_wizard__create_date
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill__create_date
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill_log__create_date
#: model:ir.model.fields,field_description:hr_skills.field_hr_resume_line__create_date
#: model:ir.model.fields,field_description:hr_skills.field_hr_resume_line_type__create_date
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill__create_date
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill_level__create_date
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill_type__create_date
msgid "Created on"
msgstr "Data creazione"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_creativity
msgid "Creativity"
msgstr "Creatività"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_critical_thinking
msgid "Critical Thinking"
msgstr "Pensiero critico"

#. module: hr_skills
#. odoo-javascript
#: code:addons/hr_skills/static/src/fields/resume_one2many/resume_one2many.xml:0
msgid "Current"
msgstr "Attuale"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_jep_davis_sanchez_and_miller
msgid "Customer service manager"
msgstr "Responsabile servizio clienti"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_cybersecurity
msgid "Cybersecurity"
msgstr "Cybersicurezza"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_hne_dandenong_north_primary_school
msgid "Dandenong North Primary School"
msgstr "Dandenong North Primary School"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_mit_darlington_primary_school
msgid "Darlington Primary School"
msgstr "Darlington Primary School"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_data_analysis
msgid "Data analysis/visualization"
msgstr "Analisi/visualizzazione dati"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_database_management
msgid "Database Management"
msgstr "Gestione database"

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill_log__date
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_employee_skill_log_view_search
msgid "Date"
msgstr "Data"

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_resume_line__date_end
msgid "Date End"
msgstr "Data fine"

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_resume_line__date_start
msgid "Date Start"
msgstr "Data inizio"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_chs_davis_plc
#: model:hr.resume.line,name:hr_skills.employee_resume_han_davis_plc
msgid "Davis PLC"
msgstr "Davis PLC"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_jve_davis_and_sons
msgid "Davis and Sons"
msgstr "Davis and Sons"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_jep_davis_sanchez_and_miller
msgid "Davis, Sanchez and Miller"
msgstr "Davis, Sanchez and Miller"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_decision_making
msgid "Decision-Making"
msgstr "Prendere decisioni"

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill_level__default_level
msgid "Default Level"
msgstr "Livello predefinito"

#. module: hr_skills
#: model_terms:ir.ui.view,arch_db:hr_skills.report_employee_cv_company
msgid "Demo Address"
msgstr "Indirizzo demo"

#. module: hr_skills
#: model_terms:ir.ui.view,arch_db:hr_skills.report_employee_cv_company
msgid "Demo Company Name"
msgstr "Nome azienda demo"

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill_log__department_id
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill_report__department_id
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_employee_skill_report_view_search
msgid "Department"
msgstr "Ufficio"

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_resume_line__description
#: model_terms:ir.ui.view,arch_db:hr_skills.resume_line_view_form
msgid "Description"
msgstr "Descrizione"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_admin_white_inc
msgid "Designer, television/film set"
msgstr "Designer set televisivi/cinematografici"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_devops
msgid "DevOps"
msgstr "DevOps"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_digital_ad
msgid "Digital advertising"
msgstr "Pubblicità digitale"

#. module: hr_skills
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_employee_cv_wizard_view_form
msgid "Discard"
msgstr "Abbandona"

#. module: hr_skills
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_employee_skill_type_view_form
msgid "Display"
msgstr "Visualizzazione"

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_cv_wizard__display_name
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill__display_name
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill_log__display_name
#: model:ir.model.fields,field_description:hr_skills.field_hr_resume_line__display_name
#: model:ir.model.fields,field_description:hr_skills.field_hr_resume_line_type__display_name
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill__display_name
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill_level__display_name
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill_type__display_name
msgid "Display Name"
msgstr "Nome visualizzato"

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_resume_line__display_type
msgid "Display Type"
msgstr "Tipo visualizzato"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_django
msgid "Django"
msgstr "Django"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_jog_douglas_thompson_and_conner
msgid "Douglas, Thompson and Conner"
msgstr "Douglas, Thompson and Conner"

#. module: hr_skills
#: model_terms:ir.ui.view,arch_db:hr_skills.resume_line_view_form
msgid "Duration"
msgstr "Durata"

#. module: hr_skills
#: model:hr.resume.line.type,name:hr_skills.resume_type_education
msgid "Education"
msgstr "Istruzione"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_jve_ellinbank_primary_school
msgid "Ellinbank Primary School"
msgstr "Ellinbank Primary School"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_han_elphinstone_primary_school
msgid "Elphinstone Primary School"
msgstr "Elphinstone Primary School"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_email
msgid "Email Marketing"
msgstr "Marketing via e-mail"

#. module: hr_skills
#: model:ir.model,name:hr_skills.model_hr_employee
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_cv_wizard__employee_ids
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill__employee_id
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill_log__employee_id
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill_report__employee_id
#: model:ir.model.fields,field_description:hr_skills.field_hr_resume_line__employee_id
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_employee_skill_log_view_search
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_employee_skill_report_view_search
msgid "Employee"
msgstr "Dipendente"

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill_report__display_name
msgid "Employee Name"
msgstr "Nome Dipendente"

#. module: hr_skills
#: model:ir.actions.report,name:hr_skills.action_report_employee_cv
#: model:ir.model,name:hr_skills.model_report_hr_skills_report_employee_cv
msgid "Employee Resume"
msgstr "Curriculum dipendente"

#. module: hr_skills
#: model:ir.actions.act_window,name:hr_skills.hr_employee_skill_report_action
msgid "Employee Skills"
msgstr "Competenze dipendente"

#. module: hr_skills
#: model:ir.model,name:hr_skills.model_hr_employee_skill_report
msgid "Employee Skills Report"
msgstr "Resoconto competenze dipendent"

#. module: hr_skills
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_employee_skill_report_view_search
msgid "Employees with Skills"
msgstr "Dipendenti con competenze"

#. module: hr_skills
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_employee_skill_report_view_search
msgid "Employees without Skills"
msgstr "Dipendenti senza competenze"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_line_admin_5
msgid "Encryption/decryption"
msgstr "Encryption/decryption"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_al_jones_ltd
msgid "Energy manager"
msgstr "Responsabile energia"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_qdp_hughes_parker_and_barber
msgid "Engineer, drilling"
msgstr "Ingegnere esperto in trivellazione"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_admin_schultz_inc
msgid "Engineer, electrical"
msgstr "Ingegnere elettrico"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_fme_johnson_shaw_and_carroll
msgid "Engineer, mining"
msgstr "Ingegnere minerario"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_han_davis_plc
msgid "Engineer, production"
msgstr "Ignegnere di produzione"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_english
#: model_terms:ir.ui.view,arch_db:hr_skills.report_employee_cv_main_panel
msgid "English"
msgstr "Inglese"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_line_admin_6
msgid ""
"Enter your finance data and the app tries to forecast what will be your "
"future incomes/expenses. The application uses machine learning to train "
"itself."
msgstr ""
"Inserisci i tuoi dati finanziari e l'applicazione proverà a prevedere quali "
"saranno i tuoi guadagni e le tue spese future. L'applicazione utilizza "
"l'apprendimento automatizzato per allenarsi."

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_qdp_evans_cooper_and_white
msgid "Evans, Cooper and White"
msgstr "Evans, Cooper and White"

#. module: hr_skills
#: model:hr.resume.line.type,name:hr_skills.resume_type_experience
#: model_terms:ir.ui.view,arch_db:hr_skills.report_employee_cv_main_panel
msgid "Experience"
msgstr "Esperienza"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_jep_cole_ltd
msgid "Fast food restaurant manager"
msgstr "Responsabile di un ristorante fast food"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_filipino
msgid "Filipino"
msgstr "Filippino"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_line_admin_6
msgid "Finance forecaster"
msgstr "Finance forecaster"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_stw_finley_rowe_and_adams
msgid "Finley, Rowe and Adams"
msgstr "Finley, Rowe and Adams"

#. module: hr_skills
#: model_terms:ir.ui.view,arch_db:hr_skills.report_employee_cv_main_panel
msgid "Fluent"
msgstr "Fluente"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_jgo_fox_and_sons
msgid "Fox and Sons"
msgstr "Fox and Sons"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_chs_freeman_williams_and_berger
msgid "Freeman, Williams and Berger"
msgstr "Freeman, Williams and Berger"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_french
msgid "French"
msgstr "Francia"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_jgo_galilee_catholic_school
msgid "Galilee Catholic School"
msgstr "Galilee Catholic School"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_vad_gallegos_little_and_walters
msgid "Gallegos, Little and Walters"
msgstr "Gallegos, Little and Walters"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_jep_garcia_and_sons
msgid "Garcia and Sons"
msgstr "Garcia and Sons"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_al_garcia_smith_and_king
msgid "Garcia, Smith and King"
msgstr "Garcia, Smith and King"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_chs_hanson_roach_and_jordan
msgid "Geographical information systems officer"
msgstr "Addetta ai sistemi informativi geografici"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_han_perezmorgan
msgid "Geoscientist"
msgstr "Geologo"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_german
msgid "German"
msgstr "Tedesco"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_lur_ramirez_inc
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_mit_hill_group
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_ngh_stanleymendez
msgid "Glass blower/designer"
msgstr "Vetraia/designer"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_go
msgid "Go"
msgstr "Go"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_jth_goodman_inc
msgid "Goodman Inc"
msgstr "Goodman Inc"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_stw_green_ltd
msgid "Green Ltd"
msgstr "Green Ltd"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_admin_greeneorr
msgid "Greene-Orr"
msgstr "Greene-Orr"

#. module: hr_skills
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_employee_skill_log_view_search
msgid "Group By"
msgstr "Raggruppa per"

#. module: hr_skills
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_skill_view_search
msgid "Group By..."
msgstr "Raggruppa per..."

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_html
msgid "HTML"
msgstr "HTML"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_hadoop
msgid "Hadoop"
msgstr "Hadoop"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_chs_hanson_roach_and_jordan
msgid "Hanson, Roach and Jordan"
msgstr "Hanson, Roach and Jordan"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_admin_harrington_park_public_school
msgid "Harrington Park Public School"
msgstr "Harrington Park Public School"

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill_report__has_department_manager_access
msgid "Has Department Manager Access"
msgstr "Ha accesso responsabile dipartimento"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_jve_davis_and_sons
msgid "Health physicist"
msgstr "Fisico medico"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_mit_hill_group
msgid "Hill Group"
msgstr "Hill Group"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_hindi
msgid "Hindi"
msgstr "Hindi"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_lur_holy_family_primary_school
msgid "Holy Family Primary School"
msgstr "Holy Family Primary School"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_hne_nortonsilva
msgid "Horticulturist, commercial"
msgstr "Orticoltore commerciale"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_fpi_hubbarddean
msgid "Hubbard-Dean"
msgstr "Hubbard-Dean"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_qdp_hughes_parker_and_barber
msgid "Hughes, Parker and Barber"
msgstr "Hughes, Parker and Barber"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_chs_freeman_williams_and_berger
msgid "Human resources officer"
msgstr "Responsabile risorse umane"

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_cv_wizard__id
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill__id
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill_log__id
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill_report__id
#: model:ir.model.fields,field_description:hr_skills.field_hr_resume_line__id
#: model:ir.model.fields,field_description:hr_skills.field_hr_resume_line_type__id
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill__id
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill_level__id
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill_type__id
msgid "ID"
msgstr "ID"

#. module: hr_skills
#: model:hr.skill.type,name:hr_skills.hr_skill_type_it
msgid "IT"
msgstr "IT"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_it_governance_compliance
msgid "IT governance and compliance (GDPR,HIPAA,...)"
msgstr "Governance e conformità IT (GDPR,HIPAA,...)"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_it_infrastructure_architecture
msgid "IT infrastructure and architecture"
msgstr "Infrastruttura e architettura IT"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_jth_simmonswilcox
msgid "IT sales professional"
msgstr "Professionista delle vendite nel settore IT"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_it_service_management
msgid "IT service management (ITSM)"
msgstr "Gestione servizi IT (ITSM)"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_it_support
msgid "IT support"
msgstr "Supporto IT"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_jgo_martin_stanley_and_duncan
msgid "IT technical support officer"
msgstr "Responsabile supporto tecnico informatico"

#. module: hr_skills
#: model:ir.model.fields,help:hr_skills.field_hr_skill_level__default_level
msgid ""
"If checked, this level will be the default one selected when choosing this "
"skill."
msgstr ""
"Se selezionato, sarà il livello predefinito quando si sceglie questa "
"competenza."

#. module: hr_skills
#. odoo-javascript
#: code:addons/hr_skills/static/src/fields/skills_one2many/skills_one2many.xml:0
msgid "If skills are missing, they can be created by an HR officer."
msgstr ""
"Se mancano delle competenze, possono essre create da un responsabile delle "
"risorse umane."

#. module: hr_skills
#: model:ir.model.fields,help:hr_skills.field_hr_employee_skill_report__active
msgid ""
"If the active field is set to False, it will allow you to hide the resource "
"record without removing it."
msgstr ""
"Se il campo Attivo è impostato su falso, consente di nascondere il record "
"della risorsa senza rimuoverlo."

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_indonesian
msgid "Indonesian"
msgstr "Indonesiano"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_niv_arroyo_ltd
msgid "Insurance risk surveyor"
msgstr "Perito assicurativo"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_iot_embedded_systems
msgid "IoT and embedded systems"
msgstr "IoT e sistemi integrati"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_ngh_jackson_schwartz_and_aguirre
msgid "Jackson, Schwartz and Aguirre"
msgstr "Jackson, Schwartz and Aguirre"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_japanese
msgid "Japanese"
msgstr "Giapponese"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_java
msgid "Java"
msgstr "Java"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_javanese
msgid "Javanese"
msgstr "Javanese"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_js
msgid "Javascript"
msgstr "Javascript"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_jve_saundersadkins
msgid "Jewellery designer"
msgstr "Designer di gioielli"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_line_admin_4
msgid ""
"Job position: Development team leader\n"
"- Supported technical operations with investigating and correcting varied production support issues (Java, Perl, Shell scripts, SQL).\n"
"- Led quality assurance planning for multiple concurrent projects relative to overall system architecture or trading system changes/new developments.\n"
"- Configured and released business critical alpha and risk models using MATLAB and SQL with inputs from Portfolio Managers."
msgstr ""
"Posizione lavorativa: Responsabile del team di sviluppo\n"
"- Supportare le operazioni tecniche con l'investigazione e la correzione di vari problemi di supporto alla produzione (Java, Perl, script Shell, SQL).\n"
"- Condurre la pianificazione della garanzia di qualità per più progetti concomitanti relativi all'architettura generale del sistema o a modifiche/nuovi sviluppi del sistema di trading.\n"
"- Configurare e rilasciare modelli di alfa e di rischio critici per l'azienda utilizzando MATLAB e SQL con gli input dei manager del portfolio."

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_line_admin_3
msgid ""
"Job position: Product manager\n"
"- Coordinated and managed software deployment across five system environments from development to production.\n"
"- Developed stored procedures to assist Java level programming efforts.\n"
"- Developed multiple renewable energy plant architectures, both commercial installations and defense-related."
msgstr ""
"Posizione lavorativa: Product manager\n"
"- Coordinamento e gestione della distribuzione del software in cinque ambienti di sistema, dallo sviluppo alla produzione.\n"
"- Sviluppo di stored procedure per assistere gli sforzi di programmazione a livello Java.\n"
"- Sviluppo di diverse architetture di impianti di energia rinnovabile, sia per installazioni commerciali che per la difesa."

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_fme_johnson_shaw_and_carroll
msgid "Johnson, Shaw and Carroll"
msgstr "Johnson, Shaw and Carroll"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_al_jones_ltd
msgid "Jones Ltd"
msgstr "Jones Ltd"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_niv_kialla_west_primary_school
msgid "Kialla West Primary School"
msgstr "Kialla West Primary School"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_han_king_island_district_high_school
msgid "King Island District High School"
msgstr "King Island District High School"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_korean
msgid "Korean"
msgstr "Coreano"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_kotlin
msgid "Kotlin"
msgstr "Kotlin"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_qdp_rivera_shaw_and_hughes
msgid "Landscape architect"
msgstr "Architetto paesaggista"

#. module: hr_skills
#: model:hr.skill.type,name:hr_skills.hr_skill_type_lang
#: model_terms:ir.ui.view,arch_db:hr_skills.report_employee_cv_main_panel
msgid "Languages"
msgstr "Lingue"

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_cv_wizard__write_uid
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill__write_uid
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill_log__write_uid
#: model:ir.model.fields,field_description:hr_skills.field_hr_resume_line__write_uid
#: model:ir.model.fields,field_description:hr_skills.field_hr_resume_line_type__write_uid
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill__write_uid
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill_level__write_uid
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill_type__write_uid
msgid "Last Updated by"
msgstr "Ultimo aggiornamento di"

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_cv_wizard__write_date
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill__write_date
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill_log__write_date
#: model:ir.model.fields,field_description:hr_skills.field_hr_resume_line__write_date
#: model:ir.model.fields,field_description:hr_skills.field_hr_resume_line_type__write_date
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill__write_date
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill_level__write_date
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill_type__write_date
msgid "Last Updated on"
msgstr "Ultimo aggiornamento il"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_jep_lawson_public_school
msgid "Lawson Public School"
msgstr "Lawson Public School"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_leadership
msgid "Leadership"
msgstr "Leadership"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_vad_gallegos_little_and_walters
msgid "Lecturer, higher education"
msgstr "Docente, istruzione superiore"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_fme_leinster_school
msgid "Leinster School"
msgstr "Leinster School"

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill_report__level_progress
msgid "Level Progress"
msgstr "Avanzamento livello"

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill_type__skill_level_ids
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_employee_skill_type_view_form
msgid "Levels"
msgstr "Livelli"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_fme_lewis_group
msgid "Lewis Group"
msgstr "Lewis Group"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_admin_lewisbailey
msgid "Lewis-Bailey"
msgstr "Lewis-Bailey"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_jog_allenkeller
msgid "Lexicographer"
msgstr "Lessicografo"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_lur_lindenow_primary_school
msgid "Lindenow Primary School"
msgstr "Lindenow Primary School"

#. module: hr_skills
#: model:ir.ui.menu,name:hr_skills.hr_resume_line_type_menu
msgid "Line Types"
msgstr "Tipi righe"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_vad_loganmartin
msgid "Logan-Martin"
msgstr "Logan-Martin"

#. module: hr_skills
#: model_terms:ir.ui.view,arch_db:hr_skills.report_employee_cv_company
msgid "Logo"
msgstr "Logo"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_stw_lynchhodges
msgid "Lynch-Hodges"
msgstr "Lynch-Hodges"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_machine_learning
msgid "Machine Learning (AI)"
msgstr "Machine Learning (AI)"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_admin_greeneorr
msgid "Magazine journalist"
msgstr "Giornalista "

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_mandarin_chinese
msgid "Mandarin Chinese"
msgstr "Cinese Mandarino"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_jog_mandurah_catholic_college
msgid "Mandurah Catholic College"
msgstr "Mandurah Catholic College"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_line_admin_7
msgid "Map Generator"
msgstr "Map Generator"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_marathi
msgid "Marathi"
msgstr "Marathi"

#. module: hr_skills
#: model_terms:ir.ui.view,arch_db:hr_skills.report_employee_cv_sidepanel
msgid "Marc Demo"
msgstr "Marc Demo"

#. module: hr_skills
#: model:hr.skill.type,name:hr_skills.hr_skill_type_marketing
msgid "Marketing"
msgstr "Marketing"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_jgo_martin_stanley_and_duncan
msgid "Martin, Stanley and Duncan"
msgstr "Martin, Stanley and Duncan"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_line_admin_1
msgid ""
"Master in Electrical engineering\n"
"            Master thesis: Better grid management and control through machine learning"
msgstr ""
"Laurea magistrale in ingegneria elettrica\n"
"            Tesi: Una migliore gestione e controllo della rete grazie all'apprendimento automatizzato"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_matlab
msgid "Matlab"
msgstr "Matlab"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_jep_mcneil_rodriguez_and_warren
msgid "Mcneil, Rodriguez and Warren"
msgstr "Mcneil, Rodriguez and Warren"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_al_garcia_smith_and_king
msgid "Medical illustrator"
msgstr "Illustratore medico"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_chs_boyd_wilson_and_moore
msgid "Medical physicist"
msgstr "Fisica medica"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_fpi_chavez_group
msgid "Mental health nurse"
msgstr "Infermiera specialista in salute mentale"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_jgo_fox_and_sons
msgid "Merchant navy officer"
msgstr "Ufficiale marina mercantile"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_mobile_app_development
msgid "Mobile app development"
msgstr "Sviluppo app mobili"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_jog_douglas_thompson_and_conner
msgid "Music therapist"
msgstr "Musicoterapeuta"

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_resume_line__name
#: model:ir.model.fields,field_description:hr_skills.field_hr_resume_line_type__name
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill__name
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill_level__name
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill_type__name
msgid "Name"
msgstr "Nome"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_jth_narellan_public_school
msgid "Narellan Public School"
msgstr "Narellan Public School"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_lur_narrogin_primary_school
msgid "Narrogin Primary School"
msgstr "Narrogin Primary School"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_network_administration
msgid "Network administration"
msgstr "Amministrazione rete"

#. module: hr_skills
#. odoo-javascript
#: code:addons/hr_skills/static/src/fields/resume_one2many/resume_one2many.js:0
msgid "New Resume line"
msgstr "Nuova riga curriculum"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_nosql
msgid "NoSQL"
msgstr "NoSQL"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_stw_northern_bay_p12_college
msgid "Northern Bay P-12 College"
msgstr "Northern Bay P-12 College"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_hne_nortonsilva
msgid "Norton-Silva"
msgstr "Norton-Silva"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_line_admin_4
msgid "Odoo SA"
msgstr "Odoo SA"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_openness_to_criticism
msgid "Openness to criticism"
msgstr "Apertura alle critiche"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_organizational
msgid "Organizational"
msgstr "Organizzazione"

#. module: hr_skills
#. odoo-javascript
#. odoo-python
#: code:addons/hr_skills/report/hr_employee_cv_report.py:0
#: code:addons/hr_skills/static/src/views/skills_list_renderer.js:0
msgid "Other"
msgstr "Altro"

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_cv_wizard__show_others
msgid "Others"
msgstr "Altri"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_fpi_our_lady_star_of_the_sea_school
msgid "Our Lady Star of the Sea School"
msgstr "Our Lady Star of the Sea School"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_php
msgid "PHP"
msgstr "PHP"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_admin_park_lake_state_school
msgid "Park Lake State School"
msgstr "Park Lake State School"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_qdp_parke_state_school
msgid "Parke State School"
msgstr "Parke State School"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_mit_parker_roberson_and_acosta
msgid "Parker, Roberson and Acosta"
msgstr "Parker, Roberson and Acosta"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_han_perezmorgan
msgid "Perez-Morgan"
msgstr "Perez-Morgan"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_perl
msgid "Perl"
msgstr "Perl"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_jve_arnoldcohen
msgid "Personnel officer"
msgstr "Addetto al personale"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_persuasion
msgid "Persuasion"
msgstr "Persuasione"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_vad_loganmartin
msgid "Petroleum engineer"
msgstr "Ingegnere petrolifero"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_qdp_phillips_jones_and_brown
msgid "Phillips, Jones and Brown"
msgstr "Phillips, Jones and Brown"

#. module: hr_skills
#. odoo-javascript
#: code:addons/hr_skills/static/src/fields/skills_one2many/skills_one2many.xml:0
msgid "Pick a skill from the list"
msgstr "Scegli una competenza dall'elenco"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_mit_burns_lester_and_cuevas
msgid "Police officer"
msgstr "Agente di polizia"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_jog_port_curtis_road_state_school
msgid "Port Curtis Road State School"
msgstr "Port Curtis Road State School"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_portuguese
msgid "Portuguese"
msgstr "Portoghese"

#. module: hr_skills
#: model_terms:ir.ui.view,arch_db:hr_skills.report_employee_cv_main_panel
msgid "Present"
msgstr "Corrente"

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_cv_wizard__color_primary
msgid "Primary Color"
msgstr "Colore primario"

#. module: hr_skills
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_employee_cv_wizard_view_form
msgid "Print"
msgstr "Stampa"

#. module: hr_skills
#. odoo-python
#: code:addons/hr_skills/wizard/hr_employee_cv_wizard.py:0
#: model:ir.actions.act_window,name:hr_skills.action_hr_employee_cv_wizard
#: model:ir.actions.server,name:hr_skills.action_print_employees_cv
#: model:ir.model,name:hr_skills.model_hr_employee_cv_wizard
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_employee_cv_wizard_view_form
msgid "Print Resume"
msgstr "Stampa curriculum"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_problem_solving
msgid "Problem-Solving"
msgstr "Problem solving"

#. module: hr_skills
#: model:hr.skill.type,name:hr_skills.hr_skill_type_dev
msgid "Programming Languages"
msgstr "Lingue di programmazione"

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill__level_progress
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill_log__level_progress
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill_level__level_progress
msgid "Progress"
msgstr "Avanzamento"

#. module: hr_skills
#: model_terms:ir.ui.view,arch_db:hr_skills.employee_skill_level_view_form
msgid "Progress (%)"
msgstr "Avanzamento (%)"

#. module: hr_skills
#: model_terms:ir.ui.view,arch_db:hr_skills.report_employee_cv_main_panel
msgid "Progress bar"
msgstr "Barra di avanzamento"

#. module: hr_skills
#: model:ir.model.fields,help:hr_skills.field_hr_employee_skill__level_progress
#: model:ir.model.fields,help:hr_skills.field_hr_employee_skill_log__level_progress
#: model:ir.model.fields,help:hr_skills.field_hr_skill_level__level_progress
msgid "Progress from zero knowledge (0%) to fully mastered (100%)."
msgstr "Avanzamento da conoscenza zero (0%) a padronanza completa (100%)."

#. module: hr_skills
#: model:ir.model.constraint,message:hr_skills.constraint_hr_skill_level_check_level_progress
msgid "Progress should be a number between 0 and 100."
msgstr ""
"Il valore corrispondente all'avanzamento deve essere un numero tra 0 e 100."

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_project_management
msgid "Project Management"
msgstr "Gestione progetto"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_mit_robinson_crawford_and_norman
msgid "Psychiatric nurse"
msgstr "Infermiera psichiatrica"

#. module: hr_skills
#: model:ir.model,name:hr_skills.model_hr_employee_public
msgid "Public Employee"
msgstr "Dipendente pubblico"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_public
msgid "Public Speaking"
msgstr "Public speaking"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_stw_lynchhodges
msgid "Publishing rights manager"
msgstr "Responsabile dei diritti di pubblicazione"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_punjabi
msgid "Punjabi"
msgstr "Punjabi"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_python
#: model_terms:ir.ui.view,arch_db:hr_skills.report_employee_cv_main_panel
msgid "Python"
msgstr "Python"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_sql
msgid "RDMS"
msgstr "RDMS"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_lur_ramirez_inc
msgid "Ramirez Inc"
msgstr "Ramirez Inc"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_react
msgid "React"
msgstr "React"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_resourcefulness
msgid "Resourcefulness"
msgstr "Intraprendenza"

#. module: hr_skills
#: model:ir.model,name:hr_skills.model_resource_resource
msgid "Resources"
msgstr "Risorse"

#. module: hr_skills
#: model:ir.ui.menu,name:hr_skills.menu_human_resources_configuration_resume
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_employee_public_view_form_inherit
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_employee_public_view_search
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_employee_view_form
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_employee_view_search
#: model_terms:ir.ui.view,arch_db:hr_skills.res_users_view_form
#: model_terms:ir.ui.view,arch_db:hr_skills.resume_line_view_form
msgid "Resume"
msgstr "Curriculum"

#. module: hr_skills
#. odoo-python
#: code:addons/hr_skills/controllers/main.py:0
msgid "Resume %s"
msgstr "Curriculum %s"

#. module: hr_skills
#: model:ir.actions.act_window,name:hr_skills.hr_resume_type_action
msgid "Resume Line Types"
msgstr "Tipi righe CV"

#. module: hr_skills
#: model:ir.model,name:hr_skills.model_hr_resume_line
msgid "Resume line of an employee"
msgstr "Riga CV di un dipendente"

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee__resume_line_ids
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_public__resume_line_ids
#: model:ir.model.fields,field_description:hr_skills.field_res_users__resume_line_ids
msgid "Resume lines"
msgstr "Righe CV"

#. module: hr_skills
#. odoo-python
#: code:addons/hr_skills/controllers/main.py:0
msgid "Resumes"
msgstr "Curriculum"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_qdp_rivera_shaw_and_hughes
msgid "Rivera, Shaw and Hughes"
msgstr "Rivera, Shaw and Hughes"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_mit_robinson_crawford_and_norman
msgid "Robinson, Crawford and Norman"
msgstr "Robinson, Crawford and Norman"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_ruby
msgid "Ruby"
msgstr "Ruby"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_fme_russellwebster
msgid "Russell-Webster"
msgstr "Russell-Webster"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_russian
msgid "Russian"
msgstr "Russo"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_rust
msgid "Rust"
msgstr "Rust"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_line_admin_2
msgid "Saint-Joseph School"
msgstr "Saint-Joseph School"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_jve_saundersadkins
msgid "Saunders-Adkins"
msgstr "Saunders-Adkins"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_scala
msgid "Scala"
msgstr "Scala"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_admin_schultz_inc
msgid "Schultz Inc"
msgstr "Schultz Inc"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_line_admin_2
msgid "Science &amp; math"
msgstr "Scienze e matematica"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_mit_parker_roberson_and_acosta
msgid "Science writer"
msgstr "Scrittrice in ambito scientifico"

#. module: hr_skills
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_employee_skill_log_view_search
msgid "Search Logs"
msgstr "Cerca registri"

#. module: hr_skills
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_skill_view_search
msgid "Search Skill"
msgstr "Cerca competenza"

#. module: hr_skills
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_skill_type_view_search
msgid "Search Skill Type"
msgstr "Cerca tipo di competenza"

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_cv_wizard__color_secondary
msgid "Secondary Color"
msgstr "Colore secondario"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_chs_davis_plc
msgid "Secretary, company"
msgstr "Segretaria aziendale"

#. module: hr_skills
#. odoo-javascript
#: code:addons/hr_skills/static/src/fields/form_view_one2many/form_view_one2many.xml:0
msgid "Select & Close"
msgstr "Seleziona e chiudi"

#. module: hr_skills
#. odoo-javascript
#: code:addons/hr_skills/static/src/fields/form_view_one2many/form_view_one2many.xml:0
msgid "Select & New"
msgstr "Seleziona e nuova"

#. module: hr_skills
#. odoo-javascript
#: code:addons/hr_skills/static/src/fields/skills_one2many/skills_one2many.js:0
msgid "Select Skills"
msgstr "Seleziona competenze"

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_resume_line_type__sequence
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill__sequence
msgid "Sequence"
msgstr "Sequenza"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_mit_seymour_p12_college
msgid "Seymour P-12 College"
msgstr "Seymour P-12 College"

#. module: hr_skills
#: model:hr.resume.line.type,name:hr_skills.resume_type_side_projects
msgid "Side Projects"
msgstr "Progetto secondario"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_jth_simmonswilcox
msgid "Simmons-Wilcox"
msgstr "Simmons-Wilcox"

#. module: hr_skills
#: model:ir.model,name:hr_skills.model_hr_skill
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee__skill_ids
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill__skill_id
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill_log__skill_id
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill_report__skill_id
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_employee_skill_log_view_search
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_employee_skill_report_view_search
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_skill_view_search
msgid "Skill"
msgstr "Competenza"

#. module: hr_skills
#: model:ir.actions.act_window,name:hr_skills.action_hr_employee_skill_log_department
#: model:ir.actions.act_window,name:hr_skills.action_hr_employee_skill_log_employee
#: model:ir.actions.server,name:hr_skills.action_open_skills_log_department
msgid "Skill History Report"
msgstr "Resoconto cronologia competenze"

#. module: hr_skills
#: model:ir.model,name:hr_skills.model_hr_skill_level
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill__skill_level_id
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill_log__skill_level_id
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill_report__skill_level
#: model_terms:ir.ui.view,arch_db:hr_skills.employee_skill_level_view_form
msgid "Skill Level"
msgstr "Livello competenza"

#. module: hr_skills
#: model_terms:ir.ui.view,arch_db:hr_skills.employee_skill_level_view_tree
#: model_terms:ir.ui.view,arch_db:hr_skills.employee_skill_view_tree
msgid "Skill Levels"
msgstr "Livelli competenza"

#. module: hr_skills
#: model:ir.model,name:hr_skills.model_hr_skill_type
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill__skill_type_id
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill_log__skill_type_id
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill_report__skill_type_id
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill__skill_type_id
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill_level__skill_type_id
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_employee_skill_log_view_search
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_employee_skill_report_view_search
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_employee_skill_type_view_form
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_skill_view_search
msgid "Skill Type"
msgstr "Tipo di competenza"

#. module: hr_skills
#: model:ir.actions.act_window,name:hr_skills.hr_skill_type_action
#: model:ir.ui.menu,name:hr_skills.hr_skill_type_menu
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_skill_type_view_search
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_skill_type_view_tree
msgid "Skill Types"
msgstr "Tipi di competenza"

#. module: hr_skills
#: model:ir.model,name:hr_skills.model_hr_employee_skill
msgid "Skill level for an employee"
msgstr "Livello di competenza di un dipendente"

#. module: hr_skills
#. odoo-javascript
#: code:addons/hr_skills/static/src/components/avatar_card_resource/avatar_card_resource_popover.xml:0
#: code:addons/hr_skills/static/src/fields/skills_one2many/skills_one2many.xml:0
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee__employee_skill_ids
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_cv_wizard__show_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_public__employee_skill_ids
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill_type__skill_ids
#: model:ir.model.fields,field_description:hr_skills.field_res_users__employee_skill_ids
#: model:ir.model.fields,field_description:hr_skills.field_resource_resource__employee_skill_ids
#: model:ir.ui.menu,name:hr_skills.hr_employee_skill_report_menu
#: model_terms:ir.ui.view,arch_db:hr_skills.employee_skill_view_form
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_employee_skill_type_view_form
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_skill_view_form
#: model_terms:ir.ui.view,arch_db:hr_skills.report_employee_cv_main_panel
msgid "Skills"
msgstr "Competenze"

#. module: hr_skills
#: model:ir.model,name:hr_skills.model_hr_employee_skill_log
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_department_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_employee_skill_log_view_graph_department
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_employee_skill_log_view_graph_employee
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_employee_skill_log_view_tree
msgid "Skills History"
msgstr "Cronologia competenze"

#. module: hr_skills
#. odoo-javascript
#: code:addons/hr_skills/static/src/fields/skills_one2many/skills_one2many.js:0
msgid "Skills Report"
msgstr "Resoconto competenze"

#. module: hr_skills
#: model:hr.resume.line.type,name:hr_skills.resume_type_social_media
msgid "Social Media"
msgstr "Social network"

#. module: hr_skills
#: model:hr.skill.type,name:hr_skills.hr_skill_type_softskill
msgid "Soft Skills"
msgstr "Competenze trasversali"

#. module: hr_skills
#: model_terms:ir.ui.view,arch_db:hr_skills.report_employee_cv_main_panel
#: model_terms:ir.ui.view,arch_db:hr_skills.report_employee_cv_sidepanel
msgid "Software Developer"
msgstr "Sviluppatore di software"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_spanish
msgid "Spanish"
msgstr "Spagnolo"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_spark
msgid "Spark"
msgstr "Spark"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_lur_whitebell
msgid "Sports coach"
msgstr "Allenatore sportivo"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_fme_lewis_group
msgid "Sports development officer"
msgstr "Responsabile dello sviluppo sportivo"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_fme_st_michaels_primary_school
msgid "St Michael's Primary School"
msgstr "St Michael's Primary School"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_hne_st_peters_parish_primary_school
msgid "St Peter's Parish Primary School"
msgstr "St Peter's Parish Primary School"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_fpi_st_raphaels_primary_school
msgid "St Raphael's Primary School"
msgstr "St Raphael's Primary School"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_ngh_stanleymendez
msgid "Stanley-Mendez"
msgstr "Stanley-Mendez"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_stress_management
msgid "Stress management"
msgstr "Gestione dello stress"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_jep_mcneil_rodriguez_and_warren
msgid "Sub"
msgstr "Sub"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_mit_sutherland_dianella_primary_school
msgid "Sutherland Dianella Primary School"
msgstr "Sutherland Dianella Primary School"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_swift
msgid "Swift"
msgstr "Swift"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_system_administration
msgid "System Administration (Linux, Windows)"
msgstr "Amministrazione di sistemi (Linux, Windows)"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_jve_talbot_primary_school
msgid "Talbot Primary School"
msgstr "Talbot Primary School"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_qdp_phillips_jones_and_brown
msgid "Teacher, special educational needs"
msgstr "Insegnante, bisogni educativi speciali"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_teamwork
msgid "Teamwork"
msgstr "Lavoro di squadra"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_telugu
msgid "Telugu"
msgstr "Telugu"

#. module: hr_skills
#. odoo-python
#: code:addons/hr_skills/models/hr_employee_skill.py:0
msgid "The skill %(name)s and skill type %(type)s doesn't match"
msgstr "La competenza %(name)s non corrisponde alla tipologia %(type)s"

#. module: hr_skills
#. odoo-python
#: code:addons/hr_skills/models/hr_employee_skill.py:0
msgid "The skill level %(level)s is not valid for skill type: %(type)s"
msgstr "Livello di competenza %(level)s non valido per la tipologia: %(type)s"

#. module: hr_skills
#: model:ir.model.constraint,message:hr_skills.constraint_hr_resume_line_date_check
msgid "The start date must be anterior to the end date."
msgstr "La data di inizio deve essere antecedente la data di fine."

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_qdp_evans_cooper_and_white
msgid "Therapist, speech and language"
msgstr "Terapista del linguaggio e della parola"

#. module: hr_skills
#. odoo-javascript
#: code:addons/hr_skills/static/src/fields/resume_one2many/resume_one2many.xml:0
msgid ""
"There are no resume lines on this employee.\n"
"                        Why not add a new one?"
msgstr ""
"Non ci sono righe curriculum per questo dipendente.\n"
"                        Perché non aggiungerne una?"

#. module: hr_skills
#. odoo-javascript
#: code:addons/hr_skills/static/src/fields/skills_one2many/skills_one2many.xml:0
msgid "There are no skills defined in the library."
msgstr "Non sono state indicate competenze nella biblioteca."

#. module: hr_skills
#: model_terms:ir.actions.act_window,help:hr_skills.hr_employee_skill_report_action
msgid ""
"This report will give you an overview of the skills per Employee.\n"
"                Create them in configuration and add them on the Employee."
msgstr ""
"Il resoconto ti fornirà una panoramica delle competenze per dipendente.\n"
"                Creale dalle impostazioni e aggiungile al profilo del dipendente."

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_vad_thomas_chirnside_primary_school
msgid "Thomas Chirnside Primary School"
msgstr "Thomas Chirnside Primary School"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_time_management
msgid "Time Management"
msgstr "Gestione tempi"

#. module: hr_skills
#. odoo-javascript
#: code:addons/hr_skills/static/src/fields/skills_one2many/skills_one2many.xml:0
msgid "Timeline"
msgstr "Sequenza temporale"

#. module: hr_skills
#: model_terms:ir.ui.view,arch_db:hr_skills.resume_line_view_form
msgid "Title"
msgstr "Titolo"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_jgo_tottenham_central_school
msgid "Tottenham Central School"
msgstr "Tottenham Central School"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_jod_wilson_ltd
msgid "Trade union research officer"
msgstr "Funzionaria di ricerca sindacale"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_jep_trinity_college
msgid "Trinity College"
msgstr "Trinity College"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_turkish
msgid "Turkish"
msgstr "Turco"

#. module: hr_skills
#: model:ir.model.constraint,message:hr_skills.constraint_hr_employee_skill__unique_skill
msgid "Two levels for the same skill is not allowed"
msgstr "Non sono consentiti due livelli per la stessa competenza"

#. module: hr_skills
#: model:ir.model.constraint,message:hr_skills.constraint_hr_employee_skill_log__unique_skill_log
msgid "Two levels for the same skill on the same day is not allowed"
msgstr ""
"Non è permesso inserire due livelli per la stessa competenza, nello stesso "
"giorno"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_stw_tyndale_christian_school
msgid "Tyndale Christian School"
msgstr "Tyndale Christian School"

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_resume_line__line_type_id
msgid "Type"
msgstr "Tipologia"

#. module: hr_skills
#: model:ir.model,name:hr_skills.model_hr_resume_line_type
msgid "Type of a resume line"
msgstr "Tipo di una riga di CV"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_typescript
msgid "TypeScript"
msgstr "TypeScript"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_jod_umbakumba_school
msgid "Umbakumba School"
msgstr "Umbakumba School"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_line_admin_1
msgid "Université Libre de Bruxelles - Polytechnique"
msgstr "Université Libre de Bruxelles - Polytechnique"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_urdu
msgid "Urdu"
msgstr "Urdu"

#. module: hr_skills
#: model:ir.model,name:hr_skills.model_res_users
msgid "User"
msgstr "Utente"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_virtualization_containerization
msgid "Virtualization and Containerization"
msgstr "Virtualizzazione e containerizzazione"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_web_development
msgid "Web Development"
msgstr "Sviluppo web"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_admin_white_inc
msgid "White Inc"
msgstr "White Inc"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_lur_whitebell
msgid "White-Bell"
msgstr "White-Bell"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_stw_whitsunday_anglican_school
msgid "Whitsunday Anglican School"
msgstr "Whitsunday Anglican School"

#. module: hr_skills
#. odoo-javascript
#: code:addons/hr_skills/static/src/fields/skills_one2many/skills_one2many.xml:0
msgid "Why not try adding some ?"
msgstr "Perché non aggiungerne alcune?"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_jth_wilkinson_plc
msgid "Wilkinson PLC"
msgstr "Wilkinson PLC"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_han_william_light_r12_school
msgid "William Light R-12 School"
msgstr "William Light R-12 School"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_jod_wilson_ltd
msgid "Wilson Ltd"
msgstr "Wilson Ltd"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_fme_wodonga_primary_school
msgid "Wodonga Primary School"
msgstr "Wodonga Primary School"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_jep_woodend_primary_school
msgid "Woodend Primary School"
msgstr "Woodend Primary School"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_fpi_woodridge_state_school
msgid "Woodridge State School"
msgstr "Woodridge State School"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_wu_chinese
msgid "Wu Chinese"
msgstr "Cinese wu"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_vad_wycheproof_p12_college
msgid "Wycheproof P-12 College"
msgstr "Wycheproof P-12 College"

#. module: hr_skills
#. odoo-javascript
#: code:addons/hr_skills/static/src/fields/skills_one2many/skills_one2many.xml:0
msgid "You can add skills from our library to the employee profile."
msgstr ""
"Puoi aggiungere le competenze dalla biblioteca e inserirle nel profilo del "
"dipendente."

#. module: hr_skills
#: model_terms:ir.ui.view,arch_db:hr_skills.report_employee_cv_sidepanel
msgid "<EMAIL>"
msgstr "<EMAIL>"

#. module: hr_skills
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_employee_skill_type_view_form
msgid "e.g. Languages"
msgstr "es. Lingue"

#. module: hr_skills
#: model_terms:ir.ui.view,arch_db:hr_skills.resume_line_view_form
msgid "e.g. Odoo Inc."
msgstr "es. Odoo Inc."

#. module: hr_skills
#: model_terms:ir.ui.view,arch_db:hr_skills.report_employee_cv_sidepanel
msgid "www.demo.com"
msgstr "www.demo.com"
