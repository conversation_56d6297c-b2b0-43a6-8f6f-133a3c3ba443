# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_skills
# 
# Translators:
# Sarah <PERSON>, 2024
# <AUTHOR> <EMAIL>, 2024
# Wil Odoo, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 08:39+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: Korean (https://app.transifex.com/odoo/teams/41243/ko/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ko\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: hr_skills
#. odoo-python
#: code:addons/hr_skills/models/hr_skill_type.py:0
msgid "%s (copy)"
msgstr "%s (사본)"

#. module: hr_skills
#: model:ir.actions.report,print_report_name:hr_skills.action_report_employee_cv
msgid "'CV - %s' % (object.name)"
msgstr "'이력서 - %s' % (object.name)"

#. module: hr_skills
#: model_terms:ir.ui.view,arch_db:hr_skills.report_employee_cv_sidepanel
msgid "+1234567890"
msgstr "+1234567890"

#. module: hr_skills
#: model_terms:ir.ui.view,arch_db:hr_skills.report_employee_cv_main_panel
msgid "2022"
msgstr "2022"

#. module: hr_skills
#: model_terms:ir.ui.view,arch_db:hr_skills.report_employee_cv_main_panel
msgid "2023"
msgstr "2023"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_line_admin_7
msgid "A 2D/3D map generator for incremental games."
msgstr "인크리멘탈 게임을 위한 2D/3D 맵 생성기입니다."

#. module: hr_skills
#. odoo-javascript
#: code:addons/hr_skills/static/src/fields/skills_one2many/skills_one2many.xml:0
#: code:addons/hr_skills/static/src/xml/resume_templates.xml:0
msgid "ADD"
msgstr "추가하기"

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill_report__active
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill_type__active
msgid "Active"
msgstr "활성화"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_adaptability
msgid "Adaptability"
msgstr "적응력"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_agile_scrum
msgid "Agile and Scrum methodologies"
msgstr "애자일과 스크럼 방법론"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_jog_allenkeller
msgid "Allen-Keller"
msgstr "앨런 켈러"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_line_admin_5
msgid ""
"Allows to encrypt/decrypt plain text or files. Available as a web app or as "
"an API."
msgstr "일반 텍스트 또는 파일을 암호화/해독화할 수 있습니다. 웹 앱 또는 API로 사용할 수 있습니다."

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_jth_goodman_inc
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_ngh_jackson_schwartz_and_aguirre
msgid "Analytical chemist"
msgstr "분석 화학자"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_analytics
msgid "Analytics"
msgstr "분석"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_android
msgid "Android"
msgstr "안드로이드"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_arabic
msgid "Arabic"
msgstr "아랍어"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_stw_green_ltd
msgid "Arboriculturist"
msgstr "수목 관리사"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_jth_wilkinson_plc
msgid "Architectural technologist"
msgstr "건축 기술자"

#. module: hr_skills
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_employee_skill_report_view_search
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_employee_skill_type_view_form
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_skill_type_view_search
msgid "Archived"
msgstr "보관됨"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_ngh_armidale_city_public_school
msgid "Armidale City Public School"
msgstr "아미데일시 공립학교"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_jve_arnoldcohen
msgid "Arnold-Cohen"
msgstr "아놀드 코헨"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_niv_arroyo_ltd
msgid "Arroyo Ltd"
msgstr "아로요 주식회사"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_chs_avoca_primary_school
msgid "Avoca Primary School"
msgstr "아보카 초등학교"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_al_bathurst_west_public_school
msgid "Bathurst West Public School"
msgstr "배서스트 웨스트 공립학교"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_bengali
msgid "Bengali"
msgstr "벵골어"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_big_data_technologies
msgid "Big data technologies (Hadoop,Spark)"
msgstr "빅데이터 기술 (Hadoop, Spark)"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_fme_russellwebster
msgid "Biochemist, clinical"
msgstr "임상 생화학자"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_admin_blue_mountains_grammar_school
msgid "Blue Mountains Grammar School"
msgstr "블루 마운틴 그래머 스쿨"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_chs_boyd_wilson_and_moore
msgid "Boyd, Wilson and Moore"
msgstr "보이드, 윌슨, 무어"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_mit_burns_lester_and_cuevas
msgid "Burns, Lester and Cuevas"
msgstr "번스, 레스터, 쿠에바스"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_line_admin_3
msgid "Burtho Inc."
msgstr "버토 주식회사"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_c
msgid "C/C++"
msgstr "C/C++"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_cms
msgid "CMS"
msgstr "CMS"

#. module: hr_skills
#. odoo-javascript
#: code:addons/hr_skills/static/src/xml/resume_templates.xml:0
msgid "CREATE A NEW ENTRY"
msgstr "새 항목 만들기"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_css
msgid "CSS"
msgstr "CSS"

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_cv_wizard__can_show_others
msgid "Can Show Others"
msgstr "다른 사람에게 보이기 가능"

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_cv_wizard__can_show_skills
msgid "Can Show Skills"
msgstr "기술을 보일 수 있음"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_jep_garcia_and_sons
msgid "Careers information officer"
msgstr "채용 정보 담당자"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_fpi_chavez_group
msgid "Chavez Group"
msgstr "차베스 그룹"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_vad_christian_outreach_college
msgid "Christian Outreach College"
msgstr "크리스천 아웃리치 컬리지"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_admin_lewisbailey
msgid "Civil Service fast streamer"
msgstr "민원 서비스 패스트 스트리머"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_jog_claremont_college
msgid "Claremont College"
msgstr "클레어몬트 컬리지"

#. module: hr_skills
#: model:ir.model.fields.selection,name:hr_skills.selection__hr_resume_line__display_type__classic
msgid "Classic"
msgstr "기본"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_cloud_computing
msgid "Cloud computing"
msgstr "클라우드 컴퓨팅"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_jep_cole_ltd
msgid "Cole Ltd"
msgstr "콜 주식회사"

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill__color
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill__color
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill_type__color
msgid "Color"
msgstr "색상"

#. module: hr_skills
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_employee_cv_wizard_view_form
msgid "Colors"
msgstr "색상"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_com
#: model:hr.skill,name:hr_skills.hr_skill_communication
msgid "Communication"
msgstr "연락"

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill_report__company_id
msgid "Company"
msgstr "회사"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_fpi_hubbarddean
msgid "Conference centre manager"
msgstr "컨퍼런스 센터 관리자"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_conflict_management
msgid "Conflict Management"
msgstr "충돌 관리"

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_cv_wizard__show_contact
msgid "Contact Information"
msgstr "연락처 정보"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_stw_finley_rowe_and_adams
msgid "Copywriter, advertising"
msgstr "광고 카피라이터"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_ngh_craigmore_south_junior_primary_school
msgid "Craigmore South Junior Primary School"
msgstr "크레이그모어 사우스 주니어 초등학교"

#. module: hr_skills
#. odoo-javascript
#: code:addons/hr_skills/static/src/fields/resume_one2many/resume_one2many.xml:0
msgid "Create a new entry"
msgstr "새 항목 만들기"

#. module: hr_skills
#. odoo-javascript
#: code:addons/hr_skills/static/src/fields/skills_one2many/skills_one2many.xml:0
msgid "Create new Skills"
msgstr "새로운 기술 생성"

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_cv_wizard__create_uid
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill__create_uid
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill_log__create_uid
#: model:ir.model.fields,field_description:hr_skills.field_hr_resume_line__create_uid
#: model:ir.model.fields,field_description:hr_skills.field_hr_resume_line_type__create_uid
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill__create_uid
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill_level__create_uid
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill_type__create_uid
msgid "Created by"
msgstr "작성자"

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_cv_wizard__create_date
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill__create_date
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill_log__create_date
#: model:ir.model.fields,field_description:hr_skills.field_hr_resume_line__create_date
#: model:ir.model.fields,field_description:hr_skills.field_hr_resume_line_type__create_date
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill__create_date
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill_level__create_date
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill_type__create_date
msgid "Created on"
msgstr "작성일자"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_creativity
msgid "Creativity"
msgstr "창의력"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_critical_thinking
msgid "Critical Thinking"
msgstr "비판적 사고력"

#. module: hr_skills
#. odoo-javascript
#: code:addons/hr_skills/static/src/fields/resume_one2many/resume_one2many.xml:0
msgid "Current"
msgstr "현재"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_jep_davis_sanchez_and_miller
msgid "Customer service manager"
msgstr "고객 서비스 관리자"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_cybersecurity
msgid "Cybersecurity"
msgstr "사이버 보안"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_hne_dandenong_north_primary_school
msgid "Dandenong North Primary School"
msgstr "단데농 노스 초등학교"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_mit_darlington_primary_school
msgid "Darlington Primary School"
msgstr "달링턴 초등학교"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_data_analysis
msgid "Data analysis/visualization"
msgstr "데이터 분석/시각화"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_database_management
msgid "Database Management"
msgstr "데이터베이스 관리"

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill_log__date
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_employee_skill_log_view_search
msgid "Date"
msgstr "날짜"

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_resume_line__date_end
msgid "Date End"
msgstr "종료일"

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_resume_line__date_start
msgid "Date Start"
msgstr "시작일"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_chs_davis_plc
#: model:hr.resume.line,name:hr_skills.employee_resume_han_davis_plc
msgid "Davis PLC"
msgstr "데이비스 PLC"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_jve_davis_and_sons
msgid "Davis and Sons"
msgstr "데이비스와 아들들"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_jep_davis_sanchez_and_miller
msgid "Davis, Sanchez and Miller"
msgstr "데이비스, 산체스, 밀러"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_decision_making
msgid "Decision-Making"
msgstr "의사 결정"

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill_level__default_level
msgid "Default Level"
msgstr "기본 수준"

#. module: hr_skills
#: model_terms:ir.ui.view,arch_db:hr_skills.report_employee_cv_company
msgid "Demo Address"
msgstr "데모 주소"

#. module: hr_skills
#: model_terms:ir.ui.view,arch_db:hr_skills.report_employee_cv_company
msgid "Demo Company Name"
msgstr "데모 회사 명"

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill_log__department_id
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill_report__department_id
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_employee_skill_report_view_search
msgid "Department"
msgstr "부서"

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_resume_line__description
#: model_terms:ir.ui.view,arch_db:hr_skills.resume_line_view_form
msgid "Description"
msgstr "설명"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_admin_white_inc
msgid "Designer, television/film set"
msgstr "텔레비전/영화 세트장 디자이너"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_devops
msgid "DevOps"
msgstr "DevOps"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_digital_ad
msgid "Digital advertising"
msgstr "디지털 광고"

#. module: hr_skills
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_employee_cv_wizard_view_form
msgid "Discard"
msgstr "취소"

#. module: hr_skills
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_employee_skill_type_view_form
msgid "Display"
msgstr "표시"

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_cv_wizard__display_name
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill__display_name
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill_log__display_name
#: model:ir.model.fields,field_description:hr_skills.field_hr_resume_line__display_name
#: model:ir.model.fields,field_description:hr_skills.field_hr_resume_line_type__display_name
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill__display_name
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill_level__display_name
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill_type__display_name
msgid "Display Name"
msgstr "표시명"

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_resume_line__display_type
msgid "Display Type"
msgstr "표시 유형"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_django
msgid "Django"
msgstr "Django"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_jog_douglas_thompson_and_conner
msgid "Douglas, Thompson and Conner"
msgstr "더글라스, 톰슨, 코너"

#. module: hr_skills
#: model_terms:ir.ui.view,arch_db:hr_skills.resume_line_view_form
msgid "Duration"
msgstr "소요시간"

#. module: hr_skills
#: model:hr.resume.line.type,name:hr_skills.resume_type_education
msgid "Education"
msgstr "학력 사항"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_jve_ellinbank_primary_school
msgid "Ellinbank Primary School"
msgstr "엘린뱅크 초등학교"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_han_elphinstone_primary_school
msgid "Elphinstone Primary School"
msgstr "엘핀스톤 초등학교"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_email
msgid "Email Marketing"
msgstr "이메일 마케팅"

#. module: hr_skills
#: model:ir.model,name:hr_skills.model_hr_employee
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_cv_wizard__employee_ids
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill__employee_id
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill_log__employee_id
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill_report__employee_id
#: model:ir.model.fields,field_description:hr_skills.field_hr_resume_line__employee_id
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_employee_skill_log_view_search
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_employee_skill_report_view_search
msgid "Employee"
msgstr "임직원"

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill_report__display_name
msgid "Employee Name"
msgstr "직원 이름"

#. module: hr_skills
#: model:ir.actions.report,name:hr_skills.action_report_employee_cv
#: model:ir.model,name:hr_skills.model_report_hr_skills_report_employee_cv
msgid "Employee Resume"
msgstr "직원 이력서"

#. module: hr_skills
#: model:ir.actions.act_window,name:hr_skills.hr_employee_skill_report_action
msgid "Employee Skills"
msgstr "직원 역량"

#. module: hr_skills
#: model:ir.model,name:hr_skills.model_hr_employee_skill_report
msgid "Employee Skills Report"
msgstr "직원 역량 보고서"

#. module: hr_skills
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_employee_skill_report_view_search
msgid "Employees with Skills"
msgstr "기술을 보유한 직원"

#. module: hr_skills
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_employee_skill_report_view_search
msgid "Employees without Skills"
msgstr "기술이 부족한 직원"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_line_admin_5
msgid "Encryption/decryption"
msgstr "암호화/해독화"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_al_jones_ltd
msgid "Energy manager"
msgstr "에너지 관리자"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_qdp_hughes_parker_and_barber
msgid "Engineer, drilling"
msgstr "드릴링 엔지니어"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_admin_schultz_inc
msgid "Engineer, electrical"
msgstr "전기 엔지니어"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_fme_johnson_shaw_and_carroll
msgid "Engineer, mining"
msgstr "광산 엔지니어"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_han_davis_plc
msgid "Engineer, production"
msgstr "생산 엔지니어"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_english
#: model_terms:ir.ui.view,arch_db:hr_skills.report_employee_cv_main_panel
msgid "English"
msgstr "영어"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_line_admin_6
msgid ""
"Enter your finance data and the app tries to forecast what will be your "
"future incomes/expenses. The application uses machine learning to train "
"itself."
msgstr ""
"앱에 재무 관련 데이터를 입력하면 미래의 예상 수입/지출이 계산됩니다. 이 앱은 머신 러닝을 사용하여 스스로 학습이 가능합니다."

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_qdp_evans_cooper_and_white
msgid "Evans, Cooper and White"
msgstr "에반스, 쿠퍼, 화이트"

#. module: hr_skills
#: model:hr.resume.line.type,name:hr_skills.resume_type_experience
#: model_terms:ir.ui.view,arch_db:hr_skills.report_employee_cv_main_panel
msgid "Experience"
msgstr "경력"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_jep_cole_ltd
msgid "Fast food restaurant manager"
msgstr "패스트푸드점 관리자"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_filipino
msgid "Filipino"
msgstr "필리핀어"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_line_admin_6
msgid "Finance forecaster"
msgstr "재무 예측가"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_stw_finley_rowe_and_adams
msgid "Finley, Rowe and Adams"
msgstr "핀리, 로우, 아담스"

#. module: hr_skills
#: model_terms:ir.ui.view,arch_db:hr_skills.report_employee_cv_main_panel
msgid "Fluent"
msgstr "유창"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_jgo_fox_and_sons
msgid "Fox and Sons"
msgstr "폭스와 아들들"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_chs_freeman_williams_and_berger
msgid "Freeman, Williams and Berger"
msgstr "프리먼, 윌리엄스, 버거"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_french
msgid "French"
msgstr "불어"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_jgo_galilee_catholic_school
msgid "Galilee Catholic School"
msgstr "갈릴리 가톨릭 학교"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_vad_gallegos_little_and_walters
msgid "Gallegos, Little and Walters"
msgstr "갈레고스, 리틀, 월터스"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_jep_garcia_and_sons
msgid "Garcia and Sons"
msgstr "가르시아와 아들들"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_al_garcia_smith_and_king
msgid "Garcia, Smith and King"
msgstr "가르시아, 스미스, 킹"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_chs_hanson_roach_and_jordan
msgid "Geographical information systems officer"
msgstr "지리 정보 시스템 책임자"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_han_perezmorgan
msgid "Geoscientist"
msgstr "지구과학자"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_german
msgid "German"
msgstr "독일"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_lur_ramirez_inc
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_mit_hill_group
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_ngh_stanleymendez
msgid "Glass blower/designer"
msgstr "유리 공예 디자이너"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_go
msgid "Go"
msgstr "이동"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_jth_goodman_inc
msgid "Goodman Inc"
msgstr "굿맨 주식회사"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_stw_green_ltd
msgid "Green Ltd"
msgstr "그린 주식회사"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_admin_greeneorr
msgid "Greene-Orr"
msgstr "그린-오어"

#. module: hr_skills
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_employee_skill_log_view_search
msgid "Group By"
msgstr "그룹별"

#. module: hr_skills
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_skill_view_search
msgid "Group By..."
msgstr "그룹별..."

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_html
msgid "HTML"
msgstr "HTML"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_hadoop
msgid "Hadoop"
msgstr "Hadoop"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_chs_hanson_roach_and_jordan
msgid "Hanson, Roach and Jordan"
msgstr "핸슨, 로치, 조던"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_admin_harrington_park_public_school
msgid "Harrington Park Public School"
msgstr "해링턴 파크 공립학교"

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill_report__has_department_manager_access
msgid "Has Department Manager Access"
msgstr "부서장 권한"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_jve_davis_and_sons
msgid "Health physicist"
msgstr "의료 물리학자"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_mit_hill_group
msgid "Hill Group"
msgstr "힐 그룹"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_hindi
msgid "Hindi"
msgstr "힌디어"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_lur_holy_family_primary_school
msgid "Holy Family Primary School"
msgstr "홀리 패밀리 초등학교"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_hne_nortonsilva
msgid "Horticulturist, commercial"
msgstr "상업 원예사"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_fpi_hubbarddean
msgid "Hubbard-Dean"
msgstr "허바드-딘"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_qdp_hughes_parker_and_barber
msgid "Hughes, Parker and Barber"
msgstr "휴즈, 파커, 바버"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_chs_freeman_williams_and_berger
msgid "Human resources officer"
msgstr "인사 담당자"

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_cv_wizard__id
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill__id
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill_log__id
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill_report__id
#: model:ir.model.fields,field_description:hr_skills.field_hr_resume_line__id
#: model:ir.model.fields,field_description:hr_skills.field_hr_resume_line_type__id
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill__id
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill_level__id
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill_type__id
msgid "ID"
msgstr "ID"

#. module: hr_skills
#: model:hr.skill.type,name:hr_skills.hr_skill_type_it
msgid "IT"
msgstr "IT"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_it_governance_compliance
msgid "IT governance and compliance (GDPR,HIPAA,...)"
msgstr "IT 거버넌스 및 규정 준수 (GDPR, HIPAA 등)"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_it_infrastructure_architecture
msgid "IT infrastructure and architecture"
msgstr "IT 인프라 및 아키텍처"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_jth_simmonswilcox
msgid "IT sales professional"
msgstr "IT 영업 전문가"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_it_service_management
msgid "IT service management (ITSM)"
msgstr "IT 서비스 관리 (ITSM)"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_it_support
msgid "IT support"
msgstr "IT 지원"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_jgo_martin_stanley_and_duncan
msgid "IT technical support officer"
msgstr "IT 기술 지원 책임자"

#. module: hr_skills
#: model:ir.model.fields,help:hr_skills.field_hr_skill_level__default_level
msgid ""
"If checked, this level will be the default one selected when choosing this "
"skill."
msgstr "옵션 선택 시, 해당 기술을 선택할 때 이 레벨이 기본 값으로 사용됩니다."

#. module: hr_skills
#. odoo-javascript
#: code:addons/hr_skills/static/src/fields/skills_one2many/skills_one2many.xml:0
msgid "If skills are missing, they can be created by an HR officer."
msgstr "기술이 누락된 경우 인사 담당자가 생성할 수 있습니다."

#. module: hr_skills
#: model:ir.model.fields,help:hr_skills.field_hr_employee_skill_report__active
msgid ""
"If the active field is set to False, it will allow you to hide the resource "
"record without removing it."
msgstr "사용중인 필드를 아니오로 설정하면 제거하지 않고 자원 기록을 숨길 수 있습니다."

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_indonesian
msgid "Indonesian"
msgstr "인도네시아어"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_niv_arroyo_ltd
msgid "Insurance risk surveyor"
msgstr "보험 위험 조사관"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_iot_embedded_systems
msgid "IoT and embedded systems"
msgstr "IoT 및 임베디드 시스템"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_ngh_jackson_schwartz_and_aguirre
msgid "Jackson, Schwartz and Aguirre"
msgstr "잭슨, 슈워츠, 아기레"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_japanese
msgid "Japanese"
msgstr "일본어"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_java
msgid "Java"
msgstr "Java"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_javanese
msgid "Javanese"
msgstr "자바"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_js
msgid "Javascript"
msgstr "자바스크립트"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_jve_saundersadkins
msgid "Jewellery designer"
msgstr "쥬얼리 디자이너"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_line_admin_4
msgid ""
"Job position: Development team leader\n"
"- Supported technical operations with investigating and correcting varied production support issues (Java, Perl, Shell scripts, SQL).\n"
"- Led quality assurance planning for multiple concurrent projects relative to overall system architecture or trading system changes/new developments.\n"
"- Configured and released business critical alpha and risk models using MATLAB and SQL with inputs from Portfolio Managers."
msgstr ""
"직책: 개발팀 팀장\n"
"- 다양한 프로덕션 지원 관련 문제 (Java, Perl, Shell scripts, SQL)를 조사하고 수정하여 기술 운영을 지원.\n"
"- 전체 시스템 구조 및 트레이딩 시스템 변경/신규 개발과 관련된 동시 프로젝트에 대한 품질 보증 계획을 주도.\n"
"- 포트폴리오 매니저의 의견을 반영하여, MATLAB와 SQL을 활용한 비즈니스 크리티컬 알파 및 리스크 모델을 출시."

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_line_admin_3
msgid ""
"Job position: Product manager\n"
"- Coordinated and managed software deployment across five system environments from development to production.\n"
"- Developed stored procedures to assist Java level programming efforts.\n"
"- Developed multiple renewable energy plant architectures, both commercial installations and defense-related."
msgstr ""
"직책: 제품 관리자\n"
"- 개발부터 생산에 이르기까지 5가지 시스템 환경에 걸쳐 소프트웨어를 배포, 조정 및 관리\n"
"- Java 레벨의 프로그래밍 작업을 지원하기 위해 축적 절차 개발.\n"
"- 상업용 설치 및 방위 관련 재생 에너지 플랜트 아키텍처 개발."

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_fme_johnson_shaw_and_carroll
msgid "Johnson, Shaw and Carroll"
msgstr "존슨, 쇼, 캐롤"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_al_jones_ltd
msgid "Jones Ltd"
msgstr "존스 주식회사"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_niv_kialla_west_primary_school
msgid "Kialla West Primary School"
msgstr "키알라 웨스트 초등학교"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_han_king_island_district_high_school
msgid "King Island District High School"
msgstr "킹 아일랜드 디스트릭트 고등학교"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_korean
msgid "Korean"
msgstr "한국어"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_kotlin
msgid "Kotlin"
msgstr "Kotlin"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_qdp_rivera_shaw_and_hughes
msgid "Landscape architect"
msgstr "조경 설계사"

#. module: hr_skills
#: model:hr.skill.type,name:hr_skills.hr_skill_type_lang
#: model_terms:ir.ui.view,arch_db:hr_skills.report_employee_cv_main_panel
msgid "Languages"
msgstr "언어"

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_cv_wizard__write_uid
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill__write_uid
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill_log__write_uid
#: model:ir.model.fields,field_description:hr_skills.field_hr_resume_line__write_uid
#: model:ir.model.fields,field_description:hr_skills.field_hr_resume_line_type__write_uid
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill__write_uid
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill_level__write_uid
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill_type__write_uid
msgid "Last Updated by"
msgstr "최근 갱신한 사람"

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_cv_wizard__write_date
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill__write_date
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill_log__write_date
#: model:ir.model.fields,field_description:hr_skills.field_hr_resume_line__write_date
#: model:ir.model.fields,field_description:hr_skills.field_hr_resume_line_type__write_date
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill__write_date
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill_level__write_date
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill_type__write_date
msgid "Last Updated on"
msgstr "최근 갱신 일자"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_jep_lawson_public_school
msgid "Lawson Public School"
msgstr "로슨 공립학교"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_leadership
msgid "Leadership"
msgstr "리더십"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_vad_gallegos_little_and_walters
msgid "Lecturer, higher education"
msgstr "고등 교육 분야 강사"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_fme_leinster_school
msgid "Leinster School"
msgstr "린스터 학교"

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill_report__level_progress
msgid "Level Progress"
msgstr "단계 진행률"

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill_type__skill_level_ids
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_employee_skill_type_view_form
msgid "Levels"
msgstr "등급"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_fme_lewis_group
msgid "Lewis Group"
msgstr "루이스 그룹"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_admin_lewisbailey
msgid "Lewis-Bailey"
msgstr "루이스-베일리"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_jog_allenkeller
msgid "Lexicographer"
msgstr "사전 편찬자"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_lur_lindenow_primary_school
msgid "Lindenow Primary School"
msgstr "린데노우 초등학교"

#. module: hr_skills
#: model:ir.ui.menu,name:hr_skills.hr_resume_line_type_menu
msgid "Line Types"
msgstr "내역 유형"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_vad_loganmartin
msgid "Logan-Martin"
msgstr "로건-마틴"

#. module: hr_skills
#: model_terms:ir.ui.view,arch_db:hr_skills.report_employee_cv_company
msgid "Logo"
msgstr "로고"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_stw_lynchhodges
msgid "Lynch-Hodges"
msgstr "린치-호지스"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_machine_learning
msgid "Machine Learning (AI)"
msgstr "머신 러닝 (AI)"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_admin_greeneorr
msgid "Magazine journalist"
msgstr "잡지 기자"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_mandarin_chinese
msgid "Mandarin Chinese"
msgstr "중국어 (북경어)"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_jog_mandurah_catholic_college
msgid "Mandurah Catholic College"
msgstr "만두라 가톨릭 대학"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_line_admin_7
msgid "Map Generator"
msgstr "맵 생성기"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_marathi
msgid "Marathi"
msgstr "마라티어"

#. module: hr_skills
#: model_terms:ir.ui.view,arch_db:hr_skills.report_employee_cv_sidepanel
msgid "Marc Demo"
msgstr "Marc Demo"

#. module: hr_skills
#: model:hr.skill.type,name:hr_skills.hr_skill_type_marketing
msgid "Marketing"
msgstr "마케팅"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_jgo_martin_stanley_and_duncan
msgid "Martin, Stanley and Duncan"
msgstr "마틴, 스탠리, 던컨"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_line_admin_1
msgid ""
"Master in Electrical engineering\n"
"            Master thesis: Better grid management and control through machine learning"
msgstr ""
"전기 공학 석사\n"
"            석사 논문: 머신러닝을 통한 그리드 관리 및 제어 개선"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_matlab
msgid "Matlab"
msgstr "Matlab"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_jep_mcneil_rodriguez_and_warren
msgid "Mcneil, Rodriguez and Warren"
msgstr "맥닐, 로드리게스, 워렌"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_al_garcia_smith_and_king
msgid "Medical illustrator"
msgstr "의학 일러스트레이터"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_chs_boyd_wilson_and_moore
msgid "Medical physicist"
msgstr "의료 물리학자"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_fpi_chavez_group
msgid "Mental health nurse"
msgstr "정신 건강 간호사"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_jgo_fox_and_sons
msgid "Merchant navy officer"
msgstr "해군 장교"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_mobile_app_development
msgid "Mobile app development"
msgstr "모바일 앱 개발"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_jog_douglas_thompson_and_conner
msgid "Music therapist"
msgstr "음악 치료사"

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_resume_line__name
#: model:ir.model.fields,field_description:hr_skills.field_hr_resume_line_type__name
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill__name
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill_level__name
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill_type__name
msgid "Name"
msgstr "이름"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_jth_narellan_public_school
msgid "Narellan Public School"
msgstr "나렐란 공립학교"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_lur_narrogin_primary_school
msgid "Narrogin Primary School"
msgstr "나로긴 초등학교"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_network_administration
msgid "Network administration"
msgstr "네트워크 관리"

#. module: hr_skills
#. odoo-javascript
#: code:addons/hr_skills/static/src/fields/resume_one2many/resume_one2many.js:0
msgid "New Resume line"
msgstr "새로운 이력서 항목"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_nosql
msgid "NoSQL"
msgstr "NoSQL"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_stw_northern_bay_p12_college
msgid "Northern Bay P-12 College"
msgstr "노던 베이 P-12 컬리지"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_hne_nortonsilva
msgid "Norton-Silva"
msgstr "노턴-실바"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_line_admin_4
msgid "Odoo SA"
msgstr "Odoo SA"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_openness_to_criticism
msgid "Openness to criticism"
msgstr "비판에 대한 열린 자세"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_organizational
msgid "Organizational"
msgstr "조직"

#. module: hr_skills
#. odoo-javascript
#. odoo-python
#: code:addons/hr_skills/report/hr_employee_cv_report.py:0
#: code:addons/hr_skills/static/src/views/skills_list_renderer.js:0
msgid "Other"
msgstr "기타"

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_cv_wizard__show_others
msgid "Others"
msgstr "기타"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_fpi_our_lady_star_of_the_sea_school
msgid "Our Lady Star of the Sea School"
msgstr "성모 마리아 바다의 별 학교"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_php
msgid "PHP"
msgstr "PHP"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_admin_park_lake_state_school
msgid "Park Lake State School"
msgstr "파크 레이크 주립학교"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_qdp_parke_state_school
msgid "Parke State School"
msgstr "파크 주립학교"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_mit_parker_roberson_and_acosta
msgid "Parker, Roberson and Acosta"
msgstr "파커, 로버슨, 아코스타"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_han_perezmorgan
msgid "Perez-Morgan"
msgstr "페레즈-모건"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_perl
msgid "Perl"
msgstr "Perl"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_jve_arnoldcohen
msgid "Personnel officer"
msgstr "인사 담당자"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_persuasion
msgid "Persuasion"
msgstr "설득력"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_vad_loganmartin
msgid "Petroleum engineer"
msgstr "석유 엔지니어"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_qdp_phillips_jones_and_brown
msgid "Phillips, Jones and Brown"
msgstr "필립스, 존스, 브라운"

#. module: hr_skills
#. odoo-javascript
#: code:addons/hr_skills/static/src/fields/skills_one2many/skills_one2many.xml:0
msgid "Pick a skill from the list"
msgstr "목록에서 기술을 선택하세요."

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_mit_burns_lester_and_cuevas
msgid "Police officer"
msgstr "경찰관"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_jog_port_curtis_road_state_school
msgid "Port Curtis Road State School"
msgstr "포트 커티스 로드 주립학교"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_portuguese
msgid "Portuguese"
msgstr "포르투갈어"

#. module: hr_skills
#: model_terms:ir.ui.view,arch_db:hr_skills.report_employee_cv_main_panel
msgid "Present"
msgstr "출근"

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_cv_wizard__color_primary
msgid "Primary Color"
msgstr "기본 색상"

#. module: hr_skills
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_employee_cv_wizard_view_form
msgid "Print"
msgstr "인쇄"

#. module: hr_skills
#. odoo-python
#: code:addons/hr_skills/wizard/hr_employee_cv_wizard.py:0
#: model:ir.actions.act_window,name:hr_skills.action_hr_employee_cv_wizard
#: model:ir.actions.server,name:hr_skills.action_print_employees_cv
#: model:ir.model,name:hr_skills.model_hr_employee_cv_wizard
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_employee_cv_wizard_view_form
msgid "Print Resume"
msgstr "이력서 인쇄"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_problem_solving
msgid "Problem-Solving"
msgstr "문제 해결 능력"

#. module: hr_skills
#: model:hr.skill.type,name:hr_skills.hr_skill_type_dev
msgid "Programming Languages"
msgstr "프로그래밍 언어"

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill__level_progress
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill_log__level_progress
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill_level__level_progress
msgid "Progress"
msgstr "진행"

#. module: hr_skills
#: model_terms:ir.ui.view,arch_db:hr_skills.employee_skill_level_view_form
msgid "Progress (%)"
msgstr "진행율 (%)"

#. module: hr_skills
#: model_terms:ir.ui.view,arch_db:hr_skills.report_employee_cv_main_panel
msgid "Progress bar"
msgstr "진행 표시줄"

#. module: hr_skills
#: model:ir.model.fields,help:hr_skills.field_hr_employee_skill__level_progress
#: model:ir.model.fields,help:hr_skills.field_hr_employee_skill_log__level_progress
#: model:ir.model.fields,help:hr_skills.field_hr_skill_level__level_progress
msgid "Progress from zero knowledge (0%) to fully mastered (100%)."
msgstr "관련 지식 없음(0%)에서 완전히 숙달된 상태(100%)까지의 진행 상황."

#. module: hr_skills
#: model:ir.model.constraint,message:hr_skills.constraint_hr_skill_level_check_level_progress
msgid "Progress should be a number between 0 and 100."
msgstr "진행률은 0에서 100 사이의 숫자여야 합니다."

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_project_management
msgid "Project Management"
msgstr "프로젝트 관리"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_mit_robinson_crawford_and_norman
msgid "Psychiatric nurse"
msgstr "정신과 간호사"

#. module: hr_skills
#: model:ir.model,name:hr_skills.model_hr_employee_public
msgid "Public Employee"
msgstr "일반 직원"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_public
msgid "Public Speaking"
msgstr "공개 연설"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_stw_lynchhodges
msgid "Publishing rights manager"
msgstr "출판 저작권 관리자"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_punjabi
msgid "Punjabi"
msgstr "펀자브어"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_python
#: model_terms:ir.ui.view,arch_db:hr_skills.report_employee_cv_main_panel
msgid "Python"
msgstr "파이썬"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_sql
msgid "RDMS"
msgstr "RDMS"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_lur_ramirez_inc
msgid "Ramirez Inc"
msgstr "라미레즈 주식회사"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_react
msgid "React"
msgstr "반응"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_resourcefulness
msgid "Resourcefulness"
msgstr "수완성"

#. module: hr_skills
#: model:ir.model,name:hr_skills.model_resource_resource
msgid "Resources"
msgstr "자원"

#. module: hr_skills
#: model:ir.ui.menu,name:hr_skills.menu_human_resources_configuration_resume
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_employee_public_view_form_inherit
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_employee_public_view_search
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_employee_view_form
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_employee_view_search
#: model_terms:ir.ui.view,arch_db:hr_skills.res_users_view_form
#: model_terms:ir.ui.view,arch_db:hr_skills.resume_line_view_form
msgid "Resume"
msgstr "경력 사항"

#. module: hr_skills
#. odoo-python
#: code:addons/hr_skills/controllers/main.py:0
msgid "Resume %s"
msgstr "%s 이력서"

#. module: hr_skills
#: model:ir.actions.act_window,name:hr_skills.hr_resume_type_action
msgid "Resume Line Types"
msgstr "경력 사항 명세 유형"

#. module: hr_skills
#: model:ir.model,name:hr_skills.model_hr_resume_line
msgid "Resume line of an employee"
msgstr "직원의 경력 사항 명세"

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee__resume_line_ids
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_public__resume_line_ids
#: model:ir.model.fields,field_description:hr_skills.field_res_users__resume_line_ids
msgid "Resume lines"
msgstr "경력 사항 명세"

#. module: hr_skills
#. odoo-python
#: code:addons/hr_skills/controllers/main.py:0
msgid "Resumes"
msgstr "이력서"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_qdp_rivera_shaw_and_hughes
msgid "Rivera, Shaw and Hughes"
msgstr "리베라, 쇼, 휴즈"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_mit_robinson_crawford_and_norman
msgid "Robinson, Crawford and Norman"
msgstr "로빈슨, 크로포드, 노먼"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_ruby
msgid "Ruby"
msgstr "Ruby"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_fme_russellwebster
msgid "Russell-Webster"
msgstr "러셀-웹스터"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_russian
msgid "Russian"
msgstr "러시아어"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_rust
msgid "Rust"
msgstr "Rust"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_line_admin_2
msgid "Saint-Joseph School"
msgstr "세인트 조셉 학교"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_jve_saundersadkins
msgid "Saunders-Adkins"
msgstr "손더스-애드킨스"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_scala
msgid "Scala"
msgstr "Scala"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_admin_schultz_inc
msgid "Schultz Inc"
msgstr "슐츠 주식회사"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_line_admin_2
msgid "Science &amp; math"
msgstr "과학 &amp; 수학"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_mit_parker_roberson_and_acosta
msgid "Science writer"
msgstr "과학 전문 작가"

#. module: hr_skills
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_employee_skill_log_view_search
msgid "Search Logs"
msgstr "검색 기록"

#. module: hr_skills
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_skill_view_search
msgid "Search Skill"
msgstr "기록 검색"

#. module: hr_skills
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_skill_type_view_search
msgid "Search Skill Type"
msgstr "기술 유형 검색"

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_cv_wizard__color_secondary
msgid "Secondary Color"
msgstr "2차 색상"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_chs_davis_plc
msgid "Secretary, company"
msgstr "회사 비서"

#. module: hr_skills
#. odoo-javascript
#: code:addons/hr_skills/static/src/fields/form_view_one2many/form_view_one2many.xml:0
msgid "Select & Close"
msgstr "선택 및 닫기"

#. module: hr_skills
#. odoo-javascript
#: code:addons/hr_skills/static/src/fields/form_view_one2many/form_view_one2many.xml:0
msgid "Select & New"
msgstr "선택 및 새로 만들기"

#. module: hr_skills
#. odoo-javascript
#: code:addons/hr_skills/static/src/fields/skills_one2many/skills_one2many.js:0
msgid "Select Skills"
msgstr "기술 선택"

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_resume_line_type__sequence
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill__sequence
msgid "Sequence"
msgstr "순서"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_mit_seymour_p12_college
msgid "Seymour P-12 College"
msgstr "시모어 P-12 컬리지"

#. module: hr_skills
#: model:hr.resume.line.type,name:hr_skills.resume_type_side_projects
msgid "Side Projects"
msgstr "사이드 프로젝트"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_jth_simmonswilcox
msgid "Simmons-Wilcox"
msgstr "시몬스-윌콕스"

#. module: hr_skills
#: model:ir.model,name:hr_skills.model_hr_skill
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee__skill_ids
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill__skill_id
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill_log__skill_id
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill_report__skill_id
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_employee_skill_log_view_search
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_employee_skill_report_view_search
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_skill_view_search
msgid "Skill"
msgstr "업무 능력"

#. module: hr_skills
#: model:ir.actions.act_window,name:hr_skills.action_hr_employee_skill_log_department
#: model:ir.actions.act_window,name:hr_skills.action_hr_employee_skill_log_employee
#: model:ir.actions.server,name:hr_skills.action_open_skills_log_department
msgid "Skill History Report"
msgstr "기술 이력 보고서"

#. module: hr_skills
#: model:ir.model,name:hr_skills.model_hr_skill_level
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill__skill_level_id
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill_log__skill_level_id
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill_report__skill_level
#: model_terms:ir.ui.view,arch_db:hr_skills.employee_skill_level_view_form
msgid "Skill Level"
msgstr "능력 수준"

#. module: hr_skills
#: model_terms:ir.ui.view,arch_db:hr_skills.employee_skill_level_view_tree
#: model_terms:ir.ui.view,arch_db:hr_skills.employee_skill_view_tree
msgid "Skill Levels"
msgstr "능력 수준"

#. module: hr_skills
#: model:ir.model,name:hr_skills.model_hr_skill_type
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill__skill_type_id
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill_log__skill_type_id
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill_report__skill_type_id
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill__skill_type_id
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill_level__skill_type_id
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_employee_skill_log_view_search
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_employee_skill_report_view_search
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_employee_skill_type_view_form
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_skill_view_search
msgid "Skill Type"
msgstr "스킬 유형"

#. module: hr_skills
#: model:ir.actions.act_window,name:hr_skills.hr_skill_type_action
#: model:ir.ui.menu,name:hr_skills.hr_skill_type_menu
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_skill_type_view_search
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_skill_type_view_tree
msgid "Skill Types"
msgstr "스킬 유형"

#. module: hr_skills
#: model:ir.model,name:hr_skills.model_hr_employee_skill
msgid "Skill level for an employee"
msgstr "직원의 능력 수준"

#. module: hr_skills
#. odoo-javascript
#: code:addons/hr_skills/static/src/components/avatar_card_resource/avatar_card_resource_popover.xml:0
#: code:addons/hr_skills/static/src/fields/skills_one2many/skills_one2many.xml:0
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee__employee_skill_ids
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_cv_wizard__show_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_public__employee_skill_ids
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill_type__skill_ids
#: model:ir.model.fields,field_description:hr_skills.field_res_users__employee_skill_ids
#: model:ir.model.fields,field_description:hr_skills.field_resource_resource__employee_skill_ids
#: model:ir.ui.menu,name:hr_skills.hr_employee_skill_report_menu
#: model_terms:ir.ui.view,arch_db:hr_skills.employee_skill_view_form
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_employee_skill_type_view_form
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_skill_view_form
#: model_terms:ir.ui.view,arch_db:hr_skills.report_employee_cv_main_panel
msgid "Skills"
msgstr "업무 능력"

#. module: hr_skills
#: model:ir.model,name:hr_skills.model_hr_employee_skill_log
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_department_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_employee_skill_log_view_graph_department
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_employee_skill_log_view_graph_employee
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_employee_skill_log_view_tree
msgid "Skills History"
msgstr "기술 이력"

#. module: hr_skills
#. odoo-javascript
#: code:addons/hr_skills/static/src/fields/skills_one2many/skills_one2many.js:0
msgid "Skills Report"
msgstr "기술 보고서"

#. module: hr_skills
#: model:hr.resume.line.type,name:hr_skills.resume_type_social_media
msgid "Social Media"
msgstr "소셜미디어"

#. module: hr_skills
#: model:hr.skill.type,name:hr_skills.hr_skill_type_softskill
msgid "Soft Skills"
msgstr "소프트 스킬"

#. module: hr_skills
#: model_terms:ir.ui.view,arch_db:hr_skills.report_employee_cv_main_panel
#: model_terms:ir.ui.view,arch_db:hr_skills.report_employee_cv_sidepanel
msgid "Software Developer"
msgstr "소프트웨어 개발자"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_spanish
msgid "Spanish"
msgstr "스페인어"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_spark
msgid "Spark"
msgstr "스파크"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_lur_whitebell
msgid "Sports coach"
msgstr "스포츠 감독"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_fme_lewis_group
msgid "Sports development officer"
msgstr "스포츠 개발 책임자"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_fme_st_michaels_primary_school
msgid "St Michael's Primary School"
msgstr "세인트 마이클 초등학교"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_hne_st_peters_parish_primary_school
msgid "St Peter's Parish Primary School"
msgstr "세인트 피터 교구 초등학교"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_fpi_st_raphaels_primary_school
msgid "St Raphael's Primary School"
msgstr "세인트 라파엘 초등학교"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_ngh_stanleymendez
msgid "Stanley-Mendez"
msgstr "스탠리 멘데즈"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_stress_management
msgid "Stress management"
msgstr "스트레스 관리"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_jep_mcneil_rodriguez_and_warren
msgid "Sub"
msgstr "하위"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_mit_sutherland_dianella_primary_school
msgid "Sutherland Dianella Primary School"
msgstr "서덜랜드 디아넬라 초등학교"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_swift
msgid "Swift"
msgstr "Swift"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_system_administration
msgid "System Administration (Linux, Windows)"
msgstr "시스템 관리 (Linux, Windows)"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_jve_talbot_primary_school
msgid "Talbot Primary School"
msgstr "탤벗 초등학교"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_qdp_phillips_jones_and_brown
msgid "Teacher, special educational needs"
msgstr "특수 교육 교사"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_teamwork
msgid "Teamwork"
msgstr "팀워크"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_telugu
msgid "Telugu"
msgstr "텔루구어"

#. module: hr_skills
#. odoo-python
#: code:addons/hr_skills/models/hr_employee_skill.py:0
msgid "The skill %(name)s and skill type %(type)s doesn't match"
msgstr "%(name)s 능력과 스킬 유형%(type)s이 일치하지 않습니다"

#. module: hr_skills
#. odoo-python
#: code:addons/hr_skills/models/hr_employee_skill.py:0
msgid "The skill level %(level)s is not valid for skill type: %(type)s"
msgstr "%(level)s능력 수준이 스킬 유형%(type)s에 맞지 않습니다."

#. module: hr_skills
#: model:ir.model.constraint,message:hr_skills.constraint_hr_resume_line_date_check
msgid "The start date must be anterior to the end date."
msgstr "시작일은 종료일보다 앞서야 합니다."

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_qdp_evans_cooper_and_white
msgid "Therapist, speech and language"
msgstr "언어 치료사"

#. module: hr_skills
#. odoo-javascript
#: code:addons/hr_skills/static/src/fields/resume_one2many/resume_one2many.xml:0
msgid ""
"There are no resume lines on this employee.\n"
"                        Why not add a new one?"
msgstr ""
"이 직원에 대한 이력서 내용이 없습니다.\n"
"                        새 이력서를 추가하시겠습니까?"

#. module: hr_skills
#. odoo-javascript
#: code:addons/hr_skills/static/src/fields/skills_one2many/skills_one2many.xml:0
msgid "There are no skills defined in the library."
msgstr "라이브러리에 정의된 기술이 없습니다."

#. module: hr_skills
#: model_terms:ir.actions.act_window,help:hr_skills.hr_employee_skill_report_action
msgid ""
"This report will give you an overview of the skills per Employee.\n"
"                Create them in configuration and add them on the Employee."
msgstr ""
"이 보고서는 직원 개인별 업무 능력에 대한 개요를 제공합니다.\n"
"                설정에서 업무 능력을 생성하고 직원에게 추가하세요."

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_vad_thomas_chirnside_primary_school
msgid "Thomas Chirnside Primary School"
msgstr "토마스 천사이드 초등학교"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_time_management
msgid "Time Management"
msgstr "일정 관리"

#. module: hr_skills
#. odoo-javascript
#: code:addons/hr_skills/static/src/fields/skills_one2many/skills_one2many.xml:0
msgid "Timeline"
msgstr "활동 기록"

#. module: hr_skills
#: model_terms:ir.ui.view,arch_db:hr_skills.resume_line_view_form
msgid "Title"
msgstr "제목"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_jgo_tottenham_central_school
msgid "Tottenham Central School"
msgstr "토트넘 센트럴 스쿨"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_jod_wilson_ltd
msgid "Trade union research officer"
msgstr "노동 조합 연구 책임자"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_jep_trinity_college
msgid "Trinity College"
msgstr "트리니티 컬리지"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_turkish
msgid "Turkish"
msgstr "터키어"

#. module: hr_skills
#: model:ir.model.constraint,message:hr_skills.constraint_hr_employee_skill__unique_skill
msgid "Two levels for the same skill is not allowed"
msgstr "동일 능력에 대해 두 가지 수준이 함께 허용되지 않습니다."

#. module: hr_skills
#: model:ir.model.constraint,message:hr_skills.constraint_hr_employee_skill_log__unique_skill_log
msgid "Two levels for the same skill on the same day is not allowed"
msgstr "같은 날짜에 동일한 기술에 대해 두 개의 레벨을 사용할 수 없습니다"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_stw_tyndale_christian_school
msgid "Tyndale Christian School"
msgstr "틴데일 크리스천 스쿨"

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_resume_line__line_type_id
msgid "Type"
msgstr "유형"

#. module: hr_skills
#: model:ir.model,name:hr_skills.model_hr_resume_line_type
msgid "Type of a resume line"
msgstr "경력 사항 명세 유형"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_typescript
msgid "TypeScript"
msgstr "타입스크립트"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_jod_umbakumba_school
msgid "Umbakumba School"
msgstr "움바쿰바 학교"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_line_admin_1
msgid "Université Libre de Bruxelles - Polytechnique"
msgstr "리브레 데 브뤼셀 대학교 - 폴리테크닉"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_urdu
msgid "Urdu"
msgstr "우르두어"

#. module: hr_skills
#: model:ir.model,name:hr_skills.model_res_users
msgid "User"
msgstr "사용자"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_virtualization_containerization
msgid "Virtualization and Containerization"
msgstr "가상화 및 컨테이너화"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_web_development
msgid "Web Development"
msgstr "웹 개발"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_admin_white_inc
msgid "White Inc"
msgstr "화이트 주식회사"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_lur_whitebell
msgid "White-Bell"
msgstr "화이트-벨"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_stw_whitsunday_anglican_school
msgid "Whitsunday Anglican School"
msgstr "휘트선데이 성공회 학교"

#. module: hr_skills
#. odoo-javascript
#: code:addons/hr_skills/static/src/fields/skills_one2many/skills_one2many.xml:0
msgid "Why not try adding some ?"
msgstr "추가해 보시겠습니까?"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_jth_wilkinson_plc
msgid "Wilkinson PLC"
msgstr "윌킨슨 PLC"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_han_william_light_r12_school
msgid "William Light R-12 School"
msgstr "윌리엄 라이트 R-12 학교"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_jod_wilson_ltd
msgid "Wilson Ltd"
msgstr "윌슨 주식회사"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_fme_wodonga_primary_school
msgid "Wodonga Primary School"
msgstr "워동가 초등학교"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_jep_woodend_primary_school
msgid "Woodend Primary School"
msgstr "우드엔드 초등학교"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_fpi_woodridge_state_school
msgid "Woodridge State School"
msgstr "우드리지 주립학교"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_wu_chinese
msgid "Wu Chinese"
msgstr "우 중국어"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_vad_wycheproof_p12_college
msgid "Wycheproof P-12 College"
msgstr "와이치프루프 P-12 컬리지"

#. module: hr_skills
#. odoo-javascript
#: code:addons/hr_skills/static/src/fields/skills_one2many/skills_one2many.xml:0
msgid "You can add skills from our library to the employee profile."
msgstr "라이브러리의 기술을 직원 프로필에 추가할 수 있습니다."

#. module: hr_skills
#: model_terms:ir.ui.view,arch_db:hr_skills.report_employee_cv_sidepanel
msgid "<EMAIL>"
msgstr "<EMAIL>"

#. module: hr_skills
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_employee_skill_type_view_form
msgid "e.g. Languages"
msgstr "예. 언어"

#. module: hr_skills
#: model_terms:ir.ui.view,arch_db:hr_skills.resume_line_view_form
msgid "e.g. Odoo Inc."
msgstr "예. Odoo Inc."

#. module: hr_skills
#: model_terms:ir.ui.view,arch_db:hr_skills.report_employee_cv_sidepanel
msgid "www.demo.com"
msgstr "www.demo.com"
