# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* microsoft_calendar
# 
# Translators:
# Wil O<PERSON>, 2024
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-04-07 20:37+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON>, 2025\n"
"Language-Team: Japanese (https://app.transifex.com/odoo/teams/41243/ja/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ja\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: microsoft_calendar
#. odoo-python
#: code:addons/microsoft_calendar/models/calendar.py:0
msgid "%(reminder_type)s - %(duration)s Days"
msgstr "%(reminder_type)s - %(duration)s 日"

#. module: microsoft_calendar
#. odoo-python
#: code:addons/microsoft_calendar/models/calendar.py:0
msgid "%(reminder_type)s - %(duration)s Hours"
msgstr "%(reminder_type)s - %(duration)s 時間"

#. module: microsoft_calendar
#. odoo-python
#: code:addons/microsoft_calendar/models/calendar.py:0
msgid "%(reminder_type)s - %(duration)s Minutes"
msgstr "%(reminder_type)s - %(duration)s 分"

#. module: microsoft_calendar
#. odoo-python
#: code:addons/microsoft_calendar/models/calendar.py:0
msgid "%s - At time of event"
msgstr "%s - イベント時"

#. module: microsoft_calendar
#. odoo-python
#: code:addons/microsoft_calendar/models/calendar.py:0
msgid "(No title)"
msgstr "(タイトルなし)"

#. module: microsoft_calendar
#: model:ir.model.fields,field_description:microsoft_calendar.field_calendar_event__active
#: model:ir.model.fields,field_description:microsoft_calendar.field_calendar_recurrence__active
#: model:ir.model.fields,field_description:microsoft_calendar.field_microsoft_calendar_sync__active
msgid "Active"
msgstr "アクティブ"

#. module: microsoft_calendar
#. odoo-javascript
#: code:addons/microsoft_calendar/static/src/views/microsoft_calendar/microsoft_calendar_controller.js:0
msgid ""
"An administrator needs to configure Outlook Synchronization before you can "
"use it!"
msgstr "Outlook同期を使用する前に、管理者がOutlook同期を設定する必要があります！"

#. module: microsoft_calendar
#. odoo-python
#: code:addons/microsoft_calendar/models/res_users.py:0
msgid ""
"An error occurred while generating the token. Your authorization code may be"
" invalid or has already expired [%s]. You should check your Client ID and "
"secret on the Microsoft Azure portal or try to stop and restart your "
"calendar synchronisation."
msgstr ""
"トークンの生成中にエラーが発生しました。認証コードが無効であるか、有効期限が切れています [%s] Microsoft "
"AzureポータルでクライアントIDとシークレットを確認するか、カレンダの同期を停止して再開して下さい。"

#. module: microsoft_calendar
#: model:ir.model,name:microsoft_calendar.model_calendar_attendee
msgid "Calendar Attendee Information"
msgstr "カレンダ参加者情報"

#. module: microsoft_calendar
#: model:ir.model,name:microsoft_calendar.model_calendar_event
msgid "Calendar Event"
msgstr "カレンダーイベント"

#. module: microsoft_calendar
#: model_terms:ir.ui.view,arch_db:microsoft_calendar.microsoft_calendar_reset_account_view_form
msgid "Cancel"
msgstr "キャンセル"

#. module: microsoft_calendar
#: model_terms:ir.ui.view,arch_db:microsoft_calendar.res_config_settings_view_form
msgid "Client ID"
msgstr "クライアントID"

#. module: microsoft_calendar
#: model_terms:ir.ui.view,arch_db:microsoft_calendar.res_config_settings_view_form
msgid "Client Secret"
msgstr "クライアントシークレット"

#. module: microsoft_calendar
#: model:ir.model,name:microsoft_calendar.model_res_config_settings
msgid "Config Settings"
msgstr "コンフィグ設定"

#. module: microsoft_calendar
#. odoo-javascript
#: code:addons/microsoft_calendar/static/src/views/microsoft_calendar/microsoft_calendar_controller.js:0
msgid "Configuration"
msgstr "設定"

#. module: microsoft_calendar
#. odoo-javascript
#: code:addons/microsoft_calendar/static/src/views/microsoft_calendar/microsoft_calendar_controller.js:0
msgid "Configure"
msgstr "設定"

#. module: microsoft_calendar
#: model_terms:ir.ui.view,arch_db:microsoft_calendar.microsoft_calendar_reset_account_view_form
msgid "Confirm"
msgstr "確定"

#. module: microsoft_calendar
#: model:ir.model.fields,field_description:microsoft_calendar.field_microsoft_calendar_account_reset__create_uid
msgid "Created by"
msgstr "作成者"

#. module: microsoft_calendar
#: model:ir.model.fields,field_description:microsoft_calendar.field_microsoft_calendar_account_reset__create_date
msgid "Created on"
msgstr "作成日"

#. module: microsoft_calendar
#: model:ir.model.fields.selection,name:microsoft_calendar.selection__microsoft_calendar_account_reset__delete_policy__delete_odoo
msgid "Delete from Odoo"
msgstr "Odooから削除"

#. module: microsoft_calendar
#: model:ir.model.fields.selection,name:microsoft_calendar.selection__microsoft_calendar_account_reset__delete_policy__delete_both
msgid "Delete from both"
msgstr "両方から削除"

#. module: microsoft_calendar
#: model:ir.model.fields.selection,name:microsoft_calendar.selection__microsoft_calendar_account_reset__delete_policy__delete_microsoft
msgid "Delete from the current Microsoft Calendar account"
msgstr "現在のMicrosoft カレンダーアカウントから削除します"

#. module: microsoft_calendar
#. odoo-javascript
#: code:addons/microsoft_calendar/static/src/views/microsoft_calendar/microsoft_calendar_controller.js:0
msgid "Discard"
msgstr "破棄"

#. module: microsoft_calendar
#: model:ir.model.fields,field_description:microsoft_calendar.field_microsoft_calendar_account_reset__display_name
msgid "Display Name"
msgstr "表示名"

#. module: microsoft_calendar
#. odoo-python
#: code:addons/microsoft_calendar/models/calendar.py:0
msgid ""
"Due to an Outlook Calendar limitation, recurrence updates must be done "
"directly in Outlook Calendar."
msgstr "Outlookカレンダで制限されているため、再帰更新はOutlookカレンダで直接行う必要があります。"

#. module: microsoft_calendar
#. odoo-python
#: code:addons/microsoft_calendar/models/calendar.py:0
msgid ""
"Due to an Outlook Calendar limitation, recurrence updates must be done directly in Outlook Calendar.\n"
"If this recurrence is not shown in Outlook Calendar, you must delete it in Odoo Calendar and recreate it in Outlook Calendar."
msgstr ""
"Outlookカレンダで制限されているため、再帰更新はOutlookカレンダで直接行う必要があります。\n"
"この定期的な予定がOutlookカレンダに表示されていない場合、Odooカレンダで削除し、Outlookカレンダで再作成する必要があります。"

#. module: microsoft_calendar
#. odoo-python
#: code:addons/microsoft_calendar/models/calendar.py:0
msgid ""
"Due to an Outlook Calendar limitation, recurrent events must be created "
"directly in Outlook Calendar."
msgstr "Outlookカレンダで制限されているため、定期イベントはOutlookカレンダで直接作成する必要があります。"

#. module: microsoft_calendar
#: model:ir.model,name:microsoft_calendar.model_calendar_recurrence
msgid "Event Recurrence Rule"
msgstr "イベント繰返規則"

#. module: microsoft_calendar
#. odoo-python
#: code:addons/microsoft_calendar/models/calendar.py:0
msgid ""
"For a correct synchronization between Odoo and Outlook Calendar, all attendees must have an email address. However, some events do not respect this condition. As long as the events are incorrect, the calendars will not be synchronized.\n"
"Either update the events/attendees or archive these events %(details)s:\n"
"%(invalid_events)s"
msgstr ""
"OdooとOutlookカレンダを正しく同期させるためには、参加者全員がメールアドレスを持っている必要があります。しかし、一部のイベントはこの条件を満たしていません。イベントが正しくない限り、カレンダは同期されません。\n"
"イベント/出席者を更新するか、これらのイベントをアーカイブして下さい%(details)s: \n"
"%(invalid_events)s"

#. module: microsoft_calendar
#. odoo-python
#: code:addons/microsoft_calendar/models/calendar.py:0
msgid ""
"For having a different organizer in your event, it is necessary that the "
"organizer have its Odoo Calendar synced with Outlook Calendar."
msgstr "異なる主催者をイベントに参加させるには、主催者のOdooカレンダがOutlookカレンダと同期されている必要があります。"

#. module: microsoft_calendar
#: model:ir.model.fields,field_description:microsoft_calendar.field_microsoft_calendar_account_reset__id
msgid "ID"
msgstr "ID"

#. module: microsoft_calendar
#: model:ir.model.fields,help:microsoft_calendar.field_calendar_event__active
msgid ""
"If the active field is set to false, it will allow you to hide the event "
"alarm information without removing it."
msgstr "アクティブなフィールドがfalseに設定されると、それを削除せずにイベントのアラーム情報を非表示にすることができます。"

#. module: microsoft_calendar
#: model:ir.model.fields,help:microsoft_calendar.field_res_config_settings__cal_microsoft_sync_paused
msgid "Indicates if synchronization with Outlook Calendar is paused or not."
msgstr "Outlookカレンダとの同期が一時停止されているかどうかを示します。"

#. module: microsoft_calendar
#. odoo-python
#: code:addons/microsoft_calendar/models/calendar.py:0
msgid ""
"It is necessary adding the proposed organizer as attendee before saving the "
"event."
msgstr "イベントを保存する前に、提案されたオーガナイザーを出席者として追加する必要があります。"

#. module: microsoft_calendar
#: model:ir.model.fields,field_description:microsoft_calendar.field_res_users__microsoft_last_sync_date
#: model:ir.model.fields,field_description:microsoft_calendar.field_res_users_settings__microsoft_last_sync_date
msgid "Last Sync Date"
msgstr "最終同期日"

#. module: microsoft_calendar
#: model_terms:ir.ui.view,arch_db:microsoft_calendar.view_users_form
msgid "Last Sync Time"
msgstr "最終同期時刻"

#. module: microsoft_calendar
#: model:ir.model.fields,field_description:microsoft_calendar.field_microsoft_calendar_account_reset__write_uid
msgid "Last Updated by"
msgstr "最終更新者"

#. module: microsoft_calendar
#: model:ir.model.fields,field_description:microsoft_calendar.field_microsoft_calendar_account_reset__write_date
msgid "Last Updated on"
msgstr "最終更新日"

#. module: microsoft_calendar
#: model:ir.model.fields,help:microsoft_calendar.field_res_users__microsoft_last_sync_date
#: model:ir.model.fields,help:microsoft_calendar.field_res_users_settings__microsoft_last_sync_date
msgid "Last synchronization date with Outlook Calendar"
msgstr "Outlookカレンダとの最終同期日"

#. module: microsoft_calendar
#: model:ir.model.fields.selection,name:microsoft_calendar.selection__microsoft_calendar_account_reset__delete_policy__dont_delete
msgid "Leave them untouched"
msgstr "未編集のままにする"

#. module: microsoft_calendar
#: model:ir.actions.act_window,name:microsoft_calendar.microsoft_calendar_reset_account_action
#: model:ir.model,name:microsoft_calendar.model_microsoft_calendar_account_reset
msgid "Microsoft Calendar Account Reset"
msgstr "Microsoftカレンダアカウント再設定"

#. module: microsoft_calendar
#: model:ir.model.fields,field_description:microsoft_calendar.field_res_config_settings__cal_microsoft_client_id
msgid "Microsoft Client_id"
msgstr "Microsoft Client_id"

#. module: microsoft_calendar
#: model:ir.model.fields,field_description:microsoft_calendar.field_res_config_settings__cal_microsoft_client_secret
msgid "Microsoft Client_key"
msgstr "Microsoft Client_key"

#. module: microsoft_calendar
#: model:ir.model.fields,field_description:microsoft_calendar.field_res_users__microsoft_calendar_sync_token
#: model:ir.model.fields,field_description:microsoft_calendar.field_res_users_settings__microsoft_calendar_sync_token
msgid "Microsoft Next Sync Token"
msgstr "Microsoft次回同期トークン"

#. module: microsoft_calendar
#: model:ir.model.fields,field_description:microsoft_calendar.field_calendar_event__microsoft_recurrence_master_id
msgid "Microsoft Recurrence Master Id"
msgstr "Microsoft定期マスタID"

#. module: microsoft_calendar
#: model:ir.model.fields,field_description:microsoft_calendar.field_res_config_settings__cal_microsoft_sync_paused
msgid "Microsoft Synchronization Paused"
msgstr "Microsoft同期が停止されました"

#. module: microsoft_calendar
#: model:ir.model.fields,field_description:microsoft_calendar.field_calendar_event__need_sync_m
#: model:ir.model.fields,field_description:microsoft_calendar.field_calendar_recurrence__need_sync_m
#: model:ir.model.fields,field_description:microsoft_calendar.field_microsoft_calendar_sync__need_sync_m
msgid "Need Sync M"
msgstr "同期Mが必要"

#. module: microsoft_calendar
#: model_terms:ir.ui.view,arch_db:microsoft_calendar.view_users_form
msgid "Next Sync Token"
msgstr "次の同期トークン"

#. module: microsoft_calendar
#: model:ir.model.fields,field_description:microsoft_calendar.field_microsoft_calendar_account_reset__sync_policy
msgid "Next Synchronization"
msgstr "次の同期"

#. module: microsoft_calendar
#. odoo-python
#: code:addons/microsoft_calendar/models/calendar.py:0
msgid "Notification"
msgstr "通知"

#. module: microsoft_calendar
#: model:ir.model.fields,field_description:microsoft_calendar.field_calendar_event__microsoft_id
#: model:ir.model.fields,field_description:microsoft_calendar.field_calendar_recurrence__microsoft_id
#: model:ir.model.fields,field_description:microsoft_calendar.field_microsoft_calendar_sync__microsoft_id
msgid "Organizer event Id"
msgstr "イベントオーガナイザID"

#. module: microsoft_calendar
#. odoo-javascript
#: code:addons/microsoft_calendar/static/src/views/microsoft_calendar/microsoft_calendar_controller.xml:0
msgid "Outlook"
msgstr "Outlook"

#. module: microsoft_calendar
#: model_terms:ir.ui.view,arch_db:microsoft_calendar.view_users_form
msgid "Outlook Calendar"
msgstr "Outlook カレンダー"

#. module: microsoft_calendar
#: model:ir.model.fields,field_description:microsoft_calendar.field_res_users__microsoft_synchronization_stopped
#: model:ir.model.fields,field_description:microsoft_calendar.field_res_users_settings__microsoft_synchronization_stopped
msgid "Outlook Synchronization stopped"
msgstr "Outlook同期が停止されました"

#. module: microsoft_calendar
#. odoo-python
#: code:addons/microsoft_calendar/models/calendar.py:0
msgid ""
"Outlook limitation: in a recurrence, an event cannot be moved to or before "
"the day of the previous event, and cannot be moved to or after the day of "
"the following event."
msgstr ""
"Outlookの制限：定期的な予定では、イベントを前のイベントの日またはその前に移動することはできず、次のイベントの日またはその後に移動することはできません。"

#. module: microsoft_calendar
#: model:ir.actions.server,name:microsoft_calendar.ir_cron_sync_all_cals_ir_actions_server
msgid "Outlook: synchronization"
msgstr "Outlook: 同期"

#. module: microsoft_calendar
#: model_terms:ir.ui.view,arch_db:microsoft_calendar.res_config_settings_view_form
msgid "Pause Synchronization"
msgstr "同期を停止"

#. module: microsoft_calendar
#: model_terms:ir.ui.view,arch_db:microsoft_calendar.view_users_form
msgid "Refresh Token"
msgstr "リフレッシュトークン"

#. module: microsoft_calendar
#: model_terms:ir.ui.view,arch_db:microsoft_calendar.view_users_form
msgid "Reset Account"
msgstr "アカウントをリセット"

#. module: microsoft_calendar
#: model_terms:ir.ui.view,arch_db:microsoft_calendar.microsoft_calendar_reset_account_view_form
msgid "Reset Outlook Calendar Account"
msgstr "Outlookカレンダアカウントを再設定"

#. module: microsoft_calendar
#: model:ir.model,name:microsoft_calendar.model_microsoft_calendar_sync
msgid "Synchronize a record with Microsoft Calendar"
msgstr "Microsoftカレンダとレコードを同期する"

#. module: microsoft_calendar
#: model:ir.model.fields.selection,name:microsoft_calendar.selection__microsoft_calendar_account_reset__sync_policy__all
msgid "Synchronize all existing events"
msgstr "全てのイベントを同期する"

#. module: microsoft_calendar
#: model:ir.model.fields.selection,name:microsoft_calendar.selection__microsoft_calendar_account_reset__sync_policy__new
msgid "Synchronize only new events"
msgstr "新しいイベントのみを同期する"

#. module: microsoft_calendar
#. odoo-javascript
#: code:addons/microsoft_calendar/static/src/views/microsoft_calendar/microsoft_calendar_controller.js:0
msgid ""
"The Outlook Synchronization needs to be configured before you can use it, do"
" you want to do it now?"
msgstr "Outlook同期を使用するには、事前に設定が必要です。"

#. module: microsoft_calendar
#: model:ir.model.fields,help:microsoft_calendar.field_microsoft_calendar_account_reset__delete_policy
msgid "This will only affect events for which the user is the owner"
msgstr "これは、ユーザーが所有者であるイベントにのみ影響します"

#. module: microsoft_calendar
#: model_terms:ir.ui.view,arch_db:microsoft_calendar.view_users_form
msgid "Token Validity"
msgstr "トークン有効性"

#. module: microsoft_calendar
#: model:ir.model.fields,field_description:microsoft_calendar.field_calendar_event__ms_universal_event_id
#: model:ir.model.fields,field_description:microsoft_calendar.field_calendar_recurrence__ms_universal_event_id
#: model:ir.model.fields,field_description:microsoft_calendar.field_microsoft_calendar_sync__ms_universal_event_id
msgid "Universal event Id"
msgstr "一般イベントID"

#. module: microsoft_calendar
#: model:ir.model,name:microsoft_calendar.model_res_users
#: model:ir.model.fields,field_description:microsoft_calendar.field_microsoft_calendar_account_reset__user_id
msgid "User"
msgstr "ユーザ"

#. module: microsoft_calendar
#: model:ir.model,name:microsoft_calendar.model_res_users_settings
msgid "User Settings"
msgstr "ユーザ設定"

#. module: microsoft_calendar
#: model_terms:ir.ui.view,arch_db:microsoft_calendar.view_users_form
msgid "User Token"
msgstr "トークンを使用"

#. module: microsoft_calendar
#: model:ir.model.fields,field_description:microsoft_calendar.field_microsoft_calendar_account_reset__delete_policy
msgid "User's Existing Events"
msgstr "ユーザーの既存のイベント"
