# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* web_tour
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-03-19 20:37+0000\n"
"PO-Revision-Date: 2024-09-25 09:42+0000\n"
"Last-Translator: <PERSON><PERSON>, 2025\n"
"Language-Team: Italian (https://app.transifex.com/odoo/teams/41243/it/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: it\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: web_tour
#. odoo-javascript
#: code:addons/web_tour/static/src/tour_service/tour_recorder/tour_recorder.xml:0
msgid "(recording keyboard)"
msgstr "(tastiera registrazione)"

#. module: web_tour
#. odoo-javascript
#: code:addons/web_tour/static/src/tour_service/tour_recorder/tour_recorder.xml:0
msgid "(run:"
msgstr "(esegui:"

#. module: web_tour
#: model:ir.model.constraint,message:web_tour.constraint_web_tour_tour_uniq_name
msgid "A tour already exists with this name . Tour's name must be unique!"
msgstr ""
"Un tour con questo nome esiste già. Il nome del tour deve essere unico!"

#. module: web_tour
#. odoo-javascript
#: code:addons/web_tour/static/src/tour_service/tour_utils.js:0
msgid "Click the top left corner to navigate across apps."
msgstr ""
"Fai clic sull'angolo in alto a sinistra per navigare tra le applicazioni."

#. module: web_tour
#: model:ir.model.fields,field_description:web_tour.field_web_tour_tour_step__content
msgid "Content"
msgstr "Contenuto"

#. module: web_tour
#: model:ir.model.fields,field_description:web_tour.field_web_tour_tour__create_uid
#: model:ir.model.fields,field_description:web_tour.field_web_tour_tour_step__create_uid
msgid "Created by"
msgstr "Creato da"

#. module: web_tour
#: model:ir.model.fields,field_description:web_tour.field_web_tour_tour__create_date
#: model:ir.model.fields,field_description:web_tour.field_web_tour_tour_step__create_date
msgid "Created on"
msgstr "Data creazione"

#. module: web_tour
#: model:ir.model.fields,field_description:web_tour.field_web_tour_tour__custom
msgid "Custom"
msgstr "Personalizzata"

#. module: web_tour
#. odoo-javascript
#: code:addons/web_tour/static/src/tour_service/tour_recorder/tour_recorder.js:0
msgid "Custom tour '%s' couldn't be saved!"
msgstr "Non è stato possibile salvare il tour personalizzato '%s'! "

#. module: web_tour
#. odoo-javascript
#: code:addons/web_tour/static/src/tour_service/tour_recorder/tour_recorder.js:0
msgid "Custom tour '%s' has been added."
msgstr "Il tour personalizzato '%s' è stato aggiunto."

#. module: web_tour
#: model:ir.model.fields,field_description:web_tour.field_web_tour_tour__display_name
#: model:ir.model.fields,field_description:web_tour.field_web_tour_tour_step__display_name
msgid "Display Name"
msgstr "Nome visualizzato"

#. module: web_tour
#: model:ir.actions.server,name:web_tour.tour_export_js_action
msgid "Export JS"
msgstr "Esporta JS"

#. module: web_tour
#: model:ir.model,name:web_tour.model_ir_http
msgid "HTTP Routing"
msgstr "Instradamento HTTP"

#. module: web_tour
#: model:ir.model.fields,field_description:web_tour.field_web_tour_tour__id
#: model:ir.model.fields,field_description:web_tour.field_web_tour_tour_step__id
msgid "ID"
msgstr "ID"

#. module: web_tour
#: model:ir.model.fields,field_description:web_tour.field_web_tour_tour__write_uid
#: model:ir.model.fields,field_description:web_tour.field_web_tour_tour_step__write_uid
msgid "Last Updated by"
msgstr "Ultimo aggiornamento di"

#. module: web_tour
#: model:ir.model.fields,field_description:web_tour.field_web_tour_tour__write_date
#: model:ir.model.fields,field_description:web_tour.field_web_tour_tour_step__write_date
msgid "Last Updated on"
msgstr "Ultimo aggiornamento il"

#. module: web_tour
#: model_terms:ir.ui.view,arch_db:web_tour.tour_list
msgid "Menu"
msgstr "Menù"

#. module: web_tour
#: model:ir.model.fields,field_description:web_tour.field_web_tour_tour__name
msgid "Name"
msgstr "Nome"

#. module: web_tour
#. odoo-javascript
#: code:addons/web_tour/static/src/tour_service/tour_recorder/tour_recorder.xml:0
msgid "Name:"
msgstr "Nome:"

#. module: web_tour
#. odoo-javascript
#: code:addons/web_tour/static/src/tour_service/tour_service.js:0
#: code:addons/web_tour/static/src/widgets/tour_start.xml:0
#: model:ir.model.fields,field_description:web_tour.field_res_users__tour_enabled
msgid "Onboarding"
msgstr "Abilita tour"

#. module: web_tour
#: model:ir.model.fields,field_description:web_tour.field_web_tour_tour__rainbow_man_message
msgid "Rainbow Man Message"
msgstr "Messaggio uomo arcobaleno"

#. module: web_tour
#: model_terms:ir.ui.view,arch_db:web_tour.tour_form
msgid "Rainbow Man Message..."
msgstr "Messaggio uomo arcobaleno..."

#. module: web_tour
#. odoo-javascript
#: code:addons/web_tour/static/src/tour_service/tour_recorder/tour_recorder.xml:0
#: code:addons/web_tour/static/src/views/tour_controller.xml:0
msgid "Record"
msgstr "Record"

#. module: web_tour
#. odoo-javascript
#: code:addons/web_tour/static/src/views/tour_controller.xml:0
msgid "Record Tour"
msgstr "Registra tour"

#. module: web_tour
#: model:ir.model.fields,field_description:web_tour.field_web_tour_tour_step__run
msgid "Run"
msgstr "Esegui"

#. module: web_tour
#. odoo-javascript
#: code:addons/web_tour/static/src/tour_service/tour_recorder/tour_recorder.xml:0
msgid "Save"
msgstr "Salva"

#. module: web_tour
#. odoo-javascript
#: code:addons/web_tour/static/src/tour_service/tour_pointer_state.js:0
msgid "Scroll down to reach the next step."
msgstr "Scorri verso il basso per raggiungere la fase successiva."

#. module: web_tour
#. odoo-javascript
#: code:addons/web_tour/static/src/tour_service/tour_pointer_state.js:0
msgid "Scroll left to reach the next step."
msgstr "Scorri verso sinistra per raggiungere la fase successiva."

#. module: web_tour
#. odoo-javascript
#: code:addons/web_tour/static/src/tour_service/tour_pointer_state.js:0
msgid "Scroll right to reach the next step."
msgstr "Scorri verso destra per raggiungere la fase successiva."

#. module: web_tour
#. odoo-javascript
#: code:addons/web_tour/static/src/tour_service/tour_pointer_state.js:0
msgid "Scroll up to reach the next step."
msgstr "Scorri verso l'alto per raggiungere la fase successiva."

#. module: web_tour
#: model:ir.model.fields,field_description:web_tour.field_web_tour_tour__sequence
#: model:ir.model.fields,field_description:web_tour.field_web_tour_tour_step__sequence
msgid "Sequence"
msgstr "Sequenza"

#. module: web_tour
#: model:ir.model.fields,field_description:web_tour.field_web_tour_tour__sharing_url
msgid "Sharing URL"
msgstr "URL di condivisione"

#. module: web_tour
#. odoo-javascript
#: code:addons/web_tour/static/src/widgets/tour_start.xml:0
msgid "Start Tour"
msgstr "Inizia tour"

#. module: web_tour
#: model:ir.model.fields,field_description:web_tour.field_web_tour_tour__url
msgid "Starting URL"
msgstr "URL iniziale"

#. module: web_tour
#: model:ir.model.fields,field_description:web_tour.field_web_tour_tour__step_ids
msgid "Step"
msgstr "Passo"

#. module: web_tour
#. odoo-javascript
#: code:addons/web_tour/static/src/tour_service/tour_recorder/tour_recorder.xml:0
#: model_terms:ir.ui.view,arch_db:web_tour.tour_form
msgid "Steps"
msgstr "Fasi"

#. module: web_tour
#. odoo-javascript
#: code:addons/web_tour/static/src/tour_service/tour_recorder/tour_recorder.xml:0
msgid "Steps:"
msgstr "Fasi:"

#. module: web_tour
#. odoo-javascript
#: code:addons/web_tour/static/src/widgets/tour_start.xml:0
msgid "Test Tour"
msgstr "Prova tour"

#. module: web_tour
#. odoo-javascript
#: code:addons/web_tour/static/src/widgets/tour_start.xml:0
msgid "Testing"
msgstr "Testing"

#. module: web_tour
#: model_terms:ir.ui.view,arch_db:web_tour.tour_search
msgid "Tip"
msgstr "Consiglio"

#. module: web_tour
#: model:ir.model.fields,field_description:web_tour.field_web_tour_tour_step__tour_id
msgid "Tour"
msgstr "Tour"

#. module: web_tour
#: model:ir.model,name:web_tour.model_web_tour_tour_step
msgid "Tour's step"
msgstr "Fase tour"

#. module: web_tour
#: model:ir.actions.act_window,name:web_tour.tour_action
#: model:ir.model,name:web_tour.model_web_tour_tour
#: model:ir.ui.menu,name:web_tour.menu_tour_action
msgid "Tours"
msgstr "Tour"

#. module: web_tour
#: model:ir.model.fields,field_description:web_tour.field_web_tour_tour_step__trigger
msgid "Trigger"
msgstr "Attivazione"

#. module: web_tour
#. odoo-javascript
#: code:addons/web_tour/static/src/tour_service/tour_recorder/tour_recorder.xml:0
msgid "Url:"
msgstr "Url:"

#. module: web_tour
#: model:ir.model,name:web_tour.model_res_users
msgid "User"
msgstr "Utente"

#. module: web_tour
#: model:ir.model.fields,field_description:web_tour.field_web_tour_tour__user_consumed_ids
msgid "User Consumed"
msgstr "Utente utilizzato"

#. module: web_tour
#: model_terms:ir.ui.view,arch_db:web_tour.tour_form
msgid "e.g. My_Tour"
msgstr "ad es. Il_mio_tour"

#. module: web_tour
#. odoo-javascript
#: code:addons/web_tour/static/src/tour_service/tour_recorder/tour_recorder.xml:0
msgid "name_of_the_tour"
msgstr "name_of_the_tour"

#. module: web_tour
#. odoo-javascript
#: code:addons/web_tour/static/src/tour_service/tour_recorder/tour_recorder.xml:0
msgid "trigger"
msgstr "attiva"
