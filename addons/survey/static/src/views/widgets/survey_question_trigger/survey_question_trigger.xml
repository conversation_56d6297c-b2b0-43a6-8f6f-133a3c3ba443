<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">

    <t t-name="survey.surveyQuestionTrigger">
        <button t-if="this.props.record.data.triggering_question_ids.records.length" disabled="disabled" t-ref="survey_question_trigger"
                class="btn btn-link px-1 py-0 pe-auto" t-att-class="this.state.surveyIconWarning ? 'opacity-100' : 'icon_rotates'">
            <i class="fa fa-fw o_button_icon " t-att-class="this.state.surveyIconWarning ? 'fa-exclamation-triangle text-warning' : 'fa-code-fork'"
               t-att-data-tooltip="this.state.triggerTooltip"/>
        </button>
    </t>

</templates>
