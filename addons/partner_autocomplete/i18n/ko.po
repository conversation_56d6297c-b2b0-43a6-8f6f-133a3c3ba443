# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* partner_autocomplete
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON>, 2024
# <PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-04-07 20:37+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Sarah Park, 2025\n"
"Language-Team: Korean (https://app.transifex.com/odoo/teams/41243/ko/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ko\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: partner_autocomplete
#: model:ir.model.fields,field_description:partner_autocomplete.field_res_partner__additional_info
#: model:ir.model.fields,field_description:partner_autocomplete.field_res_users__additional_info
msgid "Additional info"
msgstr "추가 정보"

#. module: partner_autocomplete
#: model:iap.service,description:partner_autocomplete.iap_service_partner_autocomplete
msgid "Automatically enrich your contact base with corporate data."
msgstr "기업 데이터로 연락처 기반을 자동으로 강화하세요."

#. module: partner_autocomplete
#. odoo-javascript
#: code:addons/partner_autocomplete/static/src/xml/partner_autocomplete.xml:0
msgid "Buy more credits"
msgstr "추카 크레딧 구매"

#. module: partner_autocomplete
#: model:ir.model,name:partner_autocomplete.model_res_company
msgid "Companies"
msgstr "회사"

#. module: partner_autocomplete
#: model:ir.model.fields,field_description:partner_autocomplete.field_res_company__partner_gid
#: model:ir.model.fields,field_description:partner_autocomplete.field_res_partner__partner_gid
#: model:ir.model.fields,field_description:partner_autocomplete.field_res_users__partner_gid
msgid "Company database ID"
msgstr "회사 데이터베이스 ID"

#. module: partner_autocomplete
#: model:ir.model,name:partner_autocomplete.model_res_config_settings
msgid "Config Settings"
msgstr "환경 설정"

#. module: partner_autocomplete
#: model:ir.model,name:partner_autocomplete.model_res_partner
msgid "Contact"
msgstr "연락처"

#. module: partner_autocomplete
#: model:ir.model.fields,field_description:partner_autocomplete.field_res_partner_autocomplete_sync__create_uid
msgid "Created by"
msgstr "작성자"

#. module: partner_autocomplete
#: model:ir.model.fields,field_description:partner_autocomplete.field_res_partner_autocomplete_sync__create_date
msgid "Created on"
msgstr "작성일자"

#. module: partner_autocomplete
#: model:ir.model.fields,field_description:partner_autocomplete.field_res_partner_autocomplete_sync__display_name
msgid "Display Name"
msgstr "표시명"

#. module: partner_autocomplete
#: model:ir.model.fields,field_description:partner_autocomplete.field_res_company__iap_enrich_auto_done
msgid "Enrich Done"
msgstr "제고 완료"

#. module: partner_autocomplete
#: model:iap.service,unit_name:partner_autocomplete.iap_service_partner_autocomplete
msgid "Enrichments"
msgstr "강화"

#. module: partner_autocomplete
#: model:ir.model,name:partner_autocomplete.model_ir_http
msgid "HTTP Routing"
msgstr "HTTP 라우팅"

#. module: partner_autocomplete
#. odoo-javascript
#: code:addons/partner_autocomplete/static/src/js/partner_autocomplete_core.js:0
msgid "IAP Account Token missing"
msgstr "IAP 계정 토큰이 없습니다"

#. module: partner_autocomplete
#: model:ir.model,name:partner_autocomplete.model_iap_autocomplete_api
msgid "IAP Partner Autocomplete API"
msgstr "IAP 협력사 자동완성 API"

#. module: partner_autocomplete
#: model:ir.model.fields,field_description:partner_autocomplete.field_res_partner_autocomplete_sync__id
msgid "ID"
msgstr "ID"

#. module: partner_autocomplete
#: model:ir.model.fields,field_description:partner_autocomplete.field_res_config_settings__partner_autocomplete_insufficient_credit
msgid "Insufficient credit"
msgstr "크레딧 부족"

#. module: partner_autocomplete
#: model:ir.model.fields,field_description:partner_autocomplete.field_res_partner_autocomplete_sync__synched
msgid "Is synched"
msgstr "동기화 여부"

#. module: partner_autocomplete
#: model:ir.model.fields,field_description:partner_autocomplete.field_res_partner_autocomplete_sync__write_uid
msgid "Last Updated by"
msgstr "최근 갱신한 사람"

#. module: partner_autocomplete
#: model:ir.model.fields,field_description:partner_autocomplete.field_res_partner_autocomplete_sync__write_date
msgid "Last Updated on"
msgstr "최근 갱신 일자"

#. module: partner_autocomplete
#. odoo-python
#: code:addons/partner_autocomplete/models/iap_autocomplete_api.py:0
msgid "No account token"
msgstr "계정 토큰 없음"

#. module: partner_autocomplete
#. odoo-javascript
#: code:addons/partner_autocomplete/static/src/js/partner_autocomplete_core.js:0
msgid "Not enough credits for Partner Autocomplete"
msgstr "파트너 자동 완성에 대한 크레딧이 부족합니다"

#. module: partner_autocomplete
#: model:ir.model.fields,field_description:partner_autocomplete.field_res_partner_autocomplete_sync__partner_id
msgid "Partner"
msgstr "협력사"

#. module: partner_autocomplete
#: model:ir.model,name:partner_autocomplete.model_res_partner_autocomplete_sync
msgid "Partner Autocomplete Sync"
msgstr "협력사 자동 완성 동기화"

#. module: partner_autocomplete
#: model:ir.actions.server,name:partner_autocomplete.ir_cron_partner_autocomplete_ir_actions_server
msgid "Partner Autocomplete: Sync with remote DB"
msgstr "협력사 자동 완성: 원격 DB동기화"

#. module: partner_autocomplete
#. odoo-javascript
#: code:addons/partner_autocomplete/static/src/xml/partner_autocomplete.xml:0
msgid "Search Worldwide 🌎"
msgstr "전 세계 검색 🌎"

#. module: partner_autocomplete
#. odoo-javascript
#: code:addons/partner_autocomplete/static/src/js/partner_autocomplete_fieldchar.js:0
#: code:addons/partner_autocomplete/static/src/js/partner_autocomplete_many2one.js:0
msgid "Searching Autocomplete..."
msgstr "자동 완성 검색중..."

#. module: partner_autocomplete
#. odoo-javascript
#: code:addons/partner_autocomplete/static/src/xml/partner_autocomplete.xml:0
msgid "Set Your Account Token"
msgstr "계정 토큰 설정"

#. module: partner_autocomplete
#. odoo-python
#: code:addons/partner_autocomplete/models/iap_autocomplete_api.py:0
msgid "Test mode"
msgstr "테스트 모드"

#. module: partner_autocomplete
#. odoo-python
#: code:addons/partner_autocomplete/models/res_partner.py:0
msgid "Unable to enrich company (no credit was consumed)."
msgstr "회사를 강화할 수 없습니다 (크레딧이 사용되지 않음)."
