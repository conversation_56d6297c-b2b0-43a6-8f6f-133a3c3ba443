# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_sale_loyalty
# 
# Translators:
# NumerSpiral HBG, 2025
# <PERSON>, 2025
# <PERSON> <<EMAIL>>, 2025
# Wil <PERSON>, 2025
# <PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:56+0000\n"
"PO-Revision-Date: 2024-09-25 09:42+0000\n"
"Last-Translator: <PERSON>, 2025\n"
"Language-Team: Portuguese (https://app.transifex.com/odoo/teams/41243/pt/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: pt\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: website_sale_loyalty
#: model_terms:ir.ui.view,arch_db:website_sale_loyalty.modify_code_form
msgid "<strong> - </strong>"
msgstr ""

#. module: website_sale_loyalty
#: model_terms:ir.ui.view,arch_db:website_sale_loyalty.modify_code_form
msgid "<strong>Coupons - </strong>"
msgstr ""

#. module: website_sale_loyalty
#. odoo-python
#: code:addons/website_sale_loyalty/wizard/coupon_share.py:0
msgid "A coupon is needed for coupon programs."
msgstr ""

#. module: website_sale_loyalty
#. odoo-python
#: code:addons/website_sale_loyalty/models/loyalty_rule.py:0
msgid "A coupon with the same code was found."
msgstr ""

#. module: website_sale_loyalty
#: model_terms:ir.ui.view,arch_db:website_sale_loyalty.loyalty_program_view_form_inherit_website_sale_loyalty
msgid "All websites"
msgstr ""

#. module: website_sale_loyalty
#: model:ir.model.fields,field_description:website_sale_loyalty.field_loyalty_program__ecommerce_ok
msgid "Available on Website"
msgstr ""

#. module: website_sale_loyalty
#. odoo-python
#: code:addons/website_sale_loyalty/controllers/payment.py:0
msgid ""
"Cannot process payment: applied reward was changed or has expired.\n"
"Please refresh the page and try again."
msgstr ""

#. module: website_sale_loyalty
#. odoo-javascript
#: code:addons/website_sale_loyalty/static/src/js/portal_loyalty_card.xml:0
#: model_terms:ir.ui.view,arch_db:website_sale_loyalty.modify_code_form
msgid "Claim"
msgstr ""

#. module: website_sale_loyalty
#: model_terms:ir.ui.view,arch_db:website_sale_loyalty.modify_code_form
msgid "Code:"
msgstr "Código:"

#. module: website_sale_loyalty
#: model_terms:ir.ui.view,arch_db:website_sale_loyalty.modify_code_form
msgid "Costs"
msgstr "Custos"

#. module: website_sale_loyalty
#: model_terms:ir.ui.view,arch_db:website_sale_loyalty.layout
msgid "Could not apply the promo code:"
msgstr ""

#. module: website_sale_loyalty
#: model:ir.model.fields,field_description:website_sale_loyalty.field_coupon_share__coupon_id
msgid "Coupon"
msgstr "Cupão"

#. module: website_sale_loyalty
#: model:ir.model,name:website_sale_loyalty.model_coupon_share
msgid "Create links that apply a coupon and redirect to a specific page"
msgstr ""

#. module: website_sale_loyalty
#: model:ir.model.fields,field_description:website_sale_loyalty.field_coupon_share__create_uid
msgid "Created by"
msgstr "Criado por"

#. module: website_sale_loyalty
#: model:ir.model.fields,field_description:website_sale_loyalty.field_coupon_share__create_date
msgid "Created on"
msgstr "Criado em"

#. module: website_sale_loyalty
#: model:ir.model.fields,field_description:website_sale_loyalty.field_sale_order__disabled_auto_rewards
msgid "Disabled Auto Rewards"
msgstr ""

#. module: website_sale_loyalty
#: model:ir.ui.menu,name:website_sale_loyalty.menu_discount_loyalty_type_config
msgid "Discount & Loyalty"
msgstr ""

#. module: website_sale_loyalty
#: model_terms:ir.ui.view,arch_db:website_sale_loyalty.cart_discount
msgid "Discount:"
msgstr "Desconto:"

#. module: website_sale_loyalty
#: model_terms:ir.ui.view,arch_db:website_sale_loyalty.cart_discount
msgid "Discounted amount"
msgstr ""

#. module: website_sale_loyalty
#: model:ir.model.fields,field_description:website_sale_loyalty.field_coupon_share__display_name
msgid "Display Name"
msgstr "Nome a Exibir"

#. module: website_sale_loyalty
#: model_terms:ir.ui.view,arch_db:website_sale_loyalty.coupon_share_view_form
msgid "Done"
msgstr "Concluído"

#. module: website_sale_loyalty
#: model_terms:ir.ui.view,arch_db:website_sale_loyalty.modify_code_form
msgid "Expired Date:"
msgstr ""

#. module: website_sale_loyalty
#: model_terms:ir.ui.view,arch_db:website_sale_loyalty.coupon_share_view_form
msgid "Generate Short Link"
msgstr ""

#. module: website_sale_loyalty
#: model_terms:ir.ui.view,arch_db:website_sale_loyalty.sale_coupon_result
msgid "Gift card or discount code..."
msgstr ""

#. module: website_sale_loyalty
#: model:ir.ui.menu,name:website_sale_loyalty.menu_gift_ewallet_type_config
msgid "Gift cards & eWallet"
msgstr ""

#. module: website_sale_loyalty
#: model:ir.model.fields,field_description:website_sale_loyalty.field_coupon_share__id
msgid "ID"
msgstr "Id."

#. module: website_sale_loyalty
#: model_terms:ir.ui.view,arch_db:website_sale_loyalty.modify_code_form
msgid "Invalid or expired promo code."
msgstr ""

#. module: website_sale_loyalty
#: model:ir.model.fields,field_description:website_sale_loyalty.field_coupon_share__write_uid
msgid "Last Updated by"
msgstr "Última Atualização por"

#. module: website_sale_loyalty
#: model:ir.model.fields,field_description:website_sale_loyalty.field_coupon_share__write_date
msgid "Last Updated on"
msgstr "Última Atualização em"

#. module: website_sale_loyalty
#: model:ir.ui.menu,name:website_sale_loyalty.menu_loyalty
msgid "Loyalty"
msgstr ""

#. module: website_sale_loyalty
#: model:ir.model,name:website_sale_loyalty.model_loyalty_card
msgid "Loyalty Coupon"
msgstr ""

#. module: website_sale_loyalty
#: model:ir.model,name:website_sale_loyalty.model_loyalty_program
msgid "Loyalty Program"
msgstr "Programa de Fidelização"

#. module: website_sale_loyalty
#: model_terms:ir.ui.view,arch_db:website_sale_loyalty.res_config_settings_view_form_inherit_website_sale_loyalty
msgid "Loyalty Programs"
msgstr "Programa de Fidelização"

#. module: website_sale_loyalty
#: model:ir.model,name:website_sale_loyalty.model_loyalty_rule
msgid "Loyalty Rule"
msgstr "Regra de Fidelização"

#. module: website_sale_loyalty
#: model_terms:ir.ui.view,arch_db:website_sale_loyalty.modify_code_form
msgid "Pay with eWallet"
msgstr ""

#. module: website_sale_loyalty
#: model:ir.model.fields,field_description:website_sale_loyalty.field_coupon_share__program_id
msgid "Program"
msgstr "Programa"

#. module: website_sale_loyalty
#: model:ir.model.fields,field_description:website_sale_loyalty.field_coupon_share__program_website_id
msgid "Program Website"
msgstr ""

#. module: website_sale_loyalty
#: model:ir.model.fields,field_description:website_sale_loyalty.field_coupon_share__promo_code
msgid "Promo Code"
msgstr "Código Promocional"

#. module: website_sale_loyalty
#. odoo-python
#: code:addons/website_sale_loyalty/wizard/coupon_share.py:0
msgid "Provide either a coupon or a program."
msgstr ""

#. module: website_sale_loyalty
#: model:ir.model.fields,field_description:website_sale_loyalty.field_coupon_share__redirect
msgid "Redirect"
msgstr "Redirecionar"

#. module: website_sale_loyalty
#: model:ir.model.fields,help:website_sale_loyalty.field_coupon_share__program_website_id
#: model:ir.model.fields,help:website_sale_loyalty.field_loyalty_program__website_id
#: model:ir.model.fields,help:website_sale_loyalty.field_loyalty_rule__website_id
msgid "Restrict to a specific website."
msgstr ""

#. module: website_sale_loyalty
#: model:ir.model,name:website_sale_loyalty.model_sale_order
msgid "Sales Order"
msgstr "Ordem de Venda"

#. module: website_sale_loyalty
#: model:ir.model,name:website_sale_loyalty.model_sale_order_line
msgid "Sales Order Line"
msgstr "Linhas da Ordem de Venda"

#. module: website_sale_loyalty
#. odoo-python
#: code:addons/website_sale_loyalty/wizard/coupon_share.py:0
#: model_terms:ir.ui.view,arch_db:website_sale_loyalty.loyalty_card_view_tree_inherit_website_sale_loyalty
#: model_terms:ir.ui.view,arch_db:website_sale_loyalty.loyalty_program_view_tree_inherit_website_sale_loyalty
msgid "Share"
msgstr "Partilhar"

#. module: website_sale_loyalty
#. odoo-python
#: code:addons/website_sale_loyalty/wizard/coupon_share.py:0
msgid "Share %s"
msgstr ""

#. module: website_sale_loyalty
#: model:ir.model.fields,field_description:website_sale_loyalty.field_coupon_share__share_link
msgid "Share Link"
msgstr "Partilhar Hiperligação"

#. module: website_sale_loyalty
#: model_terms:ir.ui.view,arch_db:website_sale_loyalty.coupon_share_view_form
msgid "Share Loyalty Card"
msgstr ""

#. module: website_sale_loyalty
#: model_terms:ir.ui.view,arch_db:website_sale_loyalty.snippet_options
msgid "Show Discount in Subtotal"
msgstr ""

#. module: website_sale_loyalty
#. odoo-python
#: code:addons/website_sale_loyalty/controllers/main.py:0
msgid ""
"The coupon will be automatically applied when you add something in your "
"cart."
msgstr ""

#. module: website_sale_loyalty
#: model_terms:ir.ui.view,arch_db:website_sale_loyalty.layout
msgid "The following promo code was applied on your order:"
msgstr ""

#. module: website_sale_loyalty
#. odoo-python
#: code:addons/website_sale_loyalty/models/loyalty_rule.py:0
msgid "The promo code must be unique."
msgstr ""

#. module: website_sale_loyalty
#. odoo-python
#: code:addons/website_sale_loyalty/wizard/coupon_share.py:0
msgid "The shared website should correspond to the website of the program."
msgstr ""

#. module: website_sale_loyalty
#: model_terms:ir.ui.view,arch_db:website_sale_loyalty.modify_code_form
msgid "Use"
msgstr ""

#. module: website_sale_loyalty
#: model:ir.model.fields,field_description:website_sale_loyalty.field_coupon_share__website_id
#: model:ir.model.fields,field_description:website_sale_loyalty.field_loyalty_program__website_id
#: model:ir.model.fields,field_description:website_sale_loyalty.field_loyalty_rule__website_id
#: model_terms:ir.ui.view,arch_db:website_sale_loyalty.loyalty_program_view_form_inherit_website_sale_loyalty
msgid "Website"
msgstr "Website"

#. module: website_sale_loyalty
#: model_terms:ir.ui.view,arch_db:website_sale_loyalty.coupon_share_view_form
msgid ""
"You can share this promotion with your customers.\n"
"                            It will be applied at checkout when the customer uses this link."
msgstr ""

#. module: website_sale_loyalty
#: model_terms:ir.ui.view,arch_db:website_sale_loyalty.modify_code_form
msgid "You have"
msgstr "Você tem"

#. module: website_sale_loyalty
#: model_terms:ir.ui.view,arch_db:website_sale_loyalty.modify_code_form
msgid "You have successfully applied the following code:"
msgstr ""

#. module: website_sale_loyalty
#: model_terms:ir.ui.view,arch_db:website_sale_loyalty.modify_code_form
msgid "in your ewallet"
msgstr ""
