# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* spreadsheet_dashboard
# 
# Translators:
# Wil <PERSON>, 2024
# <PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-25 07:49+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON>, 2024\n"
"Language-Team: Polish (https://app.transifex.com/odoo/teams/41243/pl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: pl\n"
"Plural-Forms: nplurals=4; plural=(n==1 ? 0 : (n%10>=2 && n%10<=4) && (n%100<12 || n%100>14) ? 1 : n!=1 && (n%10>=0 && n%10<=1) || (n%10>=5 && n%10<=9) || (n%100>=12 && n%100<=14) ? 2 : 3);\n"

#. module: spreadsheet_dashboard
#. odoo-python
#: code:addons/spreadsheet_dashboard/models/spreadsheet_dashboard.py:0
msgid "%s (copy)"
msgstr "%s (kopiuj)"

#. module: spreadsheet_dashboard
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard_share__access_token
msgid "Access Token"
msgstr "Token dostępu"

#. module: spreadsheet_dashboard
#: model:res.groups,name:spreadsheet_dashboard.group_dashboard_manager
msgid "Admin"
msgstr "Admin"

#. module: spreadsheet_dashboard
#. odoo-javascript
#: code:addons/spreadsheet_dashboard/static/src/bundle/dashboard_action/dashboard_action.xml:0
msgid "An error occured while loading the dashboard"
msgstr "Wystąpił błąd podczas ładowania konsoli"

#. module: spreadsheet_dashboard
#. odoo-javascript
#: code:addons/spreadsheet_dashboard/static/src/bundle/dashboard_action/mobile_search_panel/mobile_search_panel.xml:0
msgid "BACK"
msgstr "POWRÓT"

#. module: spreadsheet_dashboard
#. odoo-javascript
#: code:addons/spreadsheet_dashboard/static/src/bundle/dashboard_action/mobile_search_panel/mobile_search_panel.js:0
msgid "Choose a dashboard...."
msgstr "Wybierz konsolę..."

#. module: spreadsheet_dashboard
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard__company_id
msgid "Company"
msgstr "Firma"

#. module: spreadsheet_dashboard
#: model:ir.ui.menu,name:spreadsheet_dashboard.spreadsheet_dashboard_menu_configuration
msgid "Configuration"
msgstr "Konfiguracja"

#. module: spreadsheet_dashboard
#: model:ir.model,name:spreadsheet_dashboard.model_spreadsheet_dashboard_share
msgid "Copy of a shared dashboard"
msgstr ""

#. module: spreadsheet_dashboard
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard__create_uid
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard_group__create_uid
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard_share__create_uid
msgid "Created by"
msgstr "Utworzył(a)"

#. module: spreadsheet_dashboard
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard__create_date
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard_group__create_date
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard_share__create_date
msgid "Created on"
msgstr "Data utworzenia"

#. module: spreadsheet_dashboard
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard_group__dashboard_ids
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard_share__dashboard_id
msgid "Dashboard"
msgstr "Konsola"

#. module: spreadsheet_dashboard
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard__dashboard_group_id
msgid "Dashboard Group"
msgstr "Grupa konsoli"

#. module: spreadsheet_dashboard
#: model:ir.actions.act_window,name:spreadsheet_dashboard.spreadsheet_dashboard_action_configuration_dashboards
#: model:ir.actions.client,name:spreadsheet_dashboard.ir_actions_dashboard_action
#: model:ir.ui.menu,name:spreadsheet_dashboard.spreadsheet_dashboard_menu_configuration_dashboards
#: model:ir.ui.menu,name:spreadsheet_dashboard.spreadsheet_dashboard_menu_dashboard
#: model:ir.ui.menu,name:spreadsheet_dashboard.spreadsheet_dashboard_menu_root
#: model_terms:ir.ui.view,arch_db:spreadsheet_dashboard.spreadsheet_dashboard_container_view_list
msgid "Dashboards"
msgstr "Konsole"

#. module: spreadsheet_dashboard
#: model_terms:ir.ui.view,arch_db:spreadsheet_dashboard.spreadsheet_dashboard_view_list
msgid "Data"
msgstr "Dane"

#. module: spreadsheet_dashboard
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard__display_name
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard_group__display_name
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard_share__display_name
msgid "Display Name"
msgstr "Nazwa wyświetlana"

#. module: spreadsheet_dashboard
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard_share__excel_export
msgid "Excel Export"
msgstr "Eksport programu Excel"

#. module: spreadsheet_dashboard
#: model:spreadsheet.dashboard.group,name:spreadsheet_dashboard.spreadsheet_dashboard_group_finance
msgid "Finance"
msgstr "Finanse"

#. module: spreadsheet_dashboard
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard__group_ids
msgid "Group"
msgstr "Grupa"

#. module: spreadsheet_dashboard
#: model:ir.model,name:spreadsheet_dashboard.model_spreadsheet_dashboard_group
msgid "Group of dashboards"
msgstr "Grupa konsol"

#. module: spreadsheet_dashboard
#: model:spreadsheet.dashboard.group,name:spreadsheet_dashboard.spreadsheet_dashboard_group_hr
msgid "Human Resources"
msgstr "Kadry"

#. module: spreadsheet_dashboard
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard__id
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard_group__id
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard_share__id
msgid "ID"
msgstr "ID"

#. module: spreadsheet_dashboard
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard__is_published
msgid "Is Published"
msgstr "Opublikowane"

#. module: spreadsheet_dashboard
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard__write_uid
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard_group__write_uid
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard_share__write_uid
msgid "Last Updated by"
msgstr "Ostatnio aktualizowane przez"

#. module: spreadsheet_dashboard
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard__write_date
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard_group__write_date
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard_share__write_date
msgid "Last Updated on"
msgstr "Data ostatniej aktualizacji"

#. module: spreadsheet_dashboard
#. odoo-javascript
#: code:addons/spreadsheet_dashboard/static/src/bundle/dashboard_action/dashboard_action.xml:0
msgid "Loading..."
msgstr "Pobieranie..."

#. module: spreadsheet_dashboard
#: model:spreadsheet.dashboard.group,name:spreadsheet_dashboard.spreadsheet_dashboard_group_logistics
msgid "Logistics"
msgstr "Logistyka"

#. module: spreadsheet_dashboard
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard__main_data_model_ids
msgid "Main Data Model"
msgstr ""

#. module: spreadsheet_dashboard
#: model:spreadsheet.dashboard.group,name:spreadsheet_dashboard.spreadsheet_dashboard_group_marketing
msgid "Marketing"
msgstr "Marketing"

#. module: spreadsheet_dashboard
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard__name
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard_group__name
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard_share__name
msgid "Name"
msgstr "Nazwa"

#. module: spreadsheet_dashboard
#. odoo-javascript
#: code:addons/spreadsheet_dashboard/static/src/bundle/dashboard_action/dashboard_action.xml:0
msgid "No available dashboard"
msgstr "Brak dostępnej konsoli"

#. module: spreadsheet_dashboard
#. odoo-javascript
#: code:addons/spreadsheet_dashboard/static/src/bundle/dashboard_action/mobile_figure_container/mobile_figure_container.xml:0
msgid ""
"Only chart figures are displayed in small screens but this dashboard doesn't"
" contain any"
msgstr ""
"Jedynie wykresy są wyświetlane na małych ekranach lecz ta konsola nie "
"zawiera żadnego"

#. module: spreadsheet_dashboard
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard_group__published_dashboard_ids
msgid "Published Dashboard"
msgstr ""

#. module: spreadsheet_dashboard
#: model:spreadsheet.dashboard.group,name:spreadsheet_dashboard.spreadsheet_dashboard_group_sales
msgid "Sales"
msgstr "Sprzedaż"

#. module: spreadsheet_dashboard
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard__sequence
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard_group__sequence
msgid "Sequence"
msgstr "Sekwencja"

#. module: spreadsheet_dashboard
#: model:spreadsheet.dashboard.group,name:spreadsheet_dashboard.spreadsheet_dashboard_group_project
msgid "Services"
msgstr "Usługi"

#. module: spreadsheet_dashboard
#: model:ir.model,name:spreadsheet_dashboard.model_spreadsheet_dashboard
msgid "Spreadsheet Dashboard"
msgstr "Konsola arkusza"

#. module: spreadsheet_dashboard
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard__spreadsheet_data
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard_share__spreadsheet_data
msgid "Spreadsheet Data"
msgstr "Dane arkusza kalkulacyjnego"

#. module: spreadsheet_dashboard
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard__spreadsheet_file_name
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard_share__spreadsheet_file_name
msgid "Spreadsheet File Name"
msgstr "Nazwa pliku arkusza kalkulacyjnego"

#. module: spreadsheet_dashboard
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard__spreadsheet_binary_data
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard_share__spreadsheet_binary_data
msgid "Spreadsheet file"
msgstr "Plik arkusza kalkulacyjnego"

#. module: spreadsheet_dashboard
#: model_terms:ir.ui.view,arch_db:spreadsheet_dashboard.spreadsheet_dashboard_container_view_form
msgid "Spreadsheets"
msgstr "Arkusze kalkulacyjne"

#. module: spreadsheet_dashboard
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard__thumbnail
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard_share__thumbnail
msgid "Thumbnail"
msgstr "Miniaturka"

#. module: spreadsheet_dashboard
#: model:ir.model.fields,field_description:spreadsheet_dashboard.field_spreadsheet_dashboard_share__full_url
msgid "URL"
msgstr "URL"

#. module: spreadsheet_dashboard
#: model:spreadsheet.dashboard.group,name:spreadsheet_dashboard.spreadsheet_dashboard_group_website
msgid "Website"
msgstr "Strona internetowa"

#. module: spreadsheet_dashboard
#. odoo-python
#: code:addons/spreadsheet_dashboard/models/spreadsheet_dashboard_group.py:0
msgid "You cannot delete %s as it is used in another module."
msgstr "Nie można usunąć %s, ponieważ jest on używany w innym module."

#. module: spreadsheet_dashboard
#. odoo-python
#: code:addons/spreadsheet_dashboard/models/spreadsheet_dashboard_share.py:0
msgid "You don't have access to this dashboard. "
msgstr ""
