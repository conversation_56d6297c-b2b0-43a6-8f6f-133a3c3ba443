<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="base.partner_demo_company_kz" model="res.partner" forcecreate="1">
        <field name="name">KZ Company</field>
        <field name="vat">100241007868</field>
        <field name="street">Prospekt Tauke Khana 654</field>
        <field name="city">Shymkent</field>
        <field name="country_id" ref="base.kz"/>
        <field name="zip">160012</field>
        <field name="email"><EMAIL></field>
        <field name="website">www.kzcompany.com</field>
        <field name="is_company" eval="True"/>
    </record>

    <record id="base.demo_company_kz" model="res.company" forcecreate="1">
        <field name="name">KZ Company</field>
        <field name="partner_id" ref="base.partner_demo_company_kz"/>
    </record>

    <function model="res.company" name="_onchange_country_id">
        <value eval="[ref('base.demo_company_kz')]"/>
    </function>

    <function model="res.users" name="write">
        <value eval="[ref('base.user_root'), ref('base.user_admin'), ref('base.user_demo')]"/>
        <value eval="{'company_ids': [(4, ref('base.demo_company_kz'))]}"/>
    </function>

    <function model="account.chart.template" name="try_loading">
        <value eval="[]"/>
        <value>kz</value>
        <value model="res.company" eval="obj().env.ref('base.demo_company_kz')"/>
        <value name="install_demo" eval="True"/>
    </function>
</odoo>
