<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="account_view_tax_form_l10n_it_edi_extended" model="ir.ui.view">
        <field name="name">account.tax.form.l10n.it.edi.extended</field>
        <field name="model">account.tax</field>
        <field name="priority">20</field>
        <field name="inherit_id" ref="l10n_it.account_tax_form_l10n_it"/>
        <field name="arch" type="xml">
            <xpath expr="//page[@name='advanced_options']" position="inside">
                <group>
                    <field name="l10n_it_withholding_type"  readonly="amount &gt;= 0.0"/>
                    <field name="l10n_it_withholding_reason" invisible="not l10n_it_withholding_type"/>
                    <field name="l10n_it_pension_fund_type"/>
                </group>
            </xpath>
        </field>
    </record>

    <record id="view_invoice_tree_l10n_it_edi_extended" model="ir.ui.view">
        <field name="name">account.invoice.list.l10n.it.edi.extended</field>
        <field name="model">account.move</field>
        <field name="priority">20</field>
        <field name="inherit_id" ref="account.view_invoice_tree"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='amount_untaxed_in_currency_signed']" position="after">
                <field name="l10n_it_amount_vat_signed" string="VAT" sum="Total" optional="hide"/>
                <field name="l10n_it_amount_pension_fund_signed" string="Pension Fund" sum="Total" optional="hide"/>
                <field name="l10n_it_amount_withholding_signed" string="Withholding" sum="Total" optional="hide"/>
                <field name="l10n_it_amount_before_withholding_signed" string="All Taxes Included" sum="Total" optional="hide"/>
            </xpath>
        </field>
    </record>

</odoo>
