# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_skills_slides
# 
# Translators:
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.1alpha1\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-01-08 06:51+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Spanish (Latin America) (https://app.transifex.com/odoo/teams/41243/es_419/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es_419\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: hr_skills_slides
#. odoo-python
#: code:addons/hr_skills_slides/models/hr_employee.py:0
msgid "%(completed)s / %(total)s"
msgstr "%(completed)s / %(total)s"

#. module: hr_skills_slides
#: model:ir.model,name:hr_skills_slides.model_slide_channel_partner
msgid "Channel / Partners (Members)"
msgstr "Canal/contactos (miembros)"

#. module: hr_skills_slides
#: model:hr.resume.line.type,name:hr_skills_slides.resume_type_training
msgid "Completed Internal Training"
msgstr "Completó la capacitación interna"

#. module: hr_skills_slides
#: model:ir.model,name:hr_skills_slides.model_slide_channel
#: model:ir.model.fields,field_description:hr_skills_slides.field_hr_resume_line__channel_id
#: model:ir.model.fields.selection,name:hr_skills_slides.selection__hr_resume_line__display_type__course
msgid "Course"
msgstr "Curso"

#. module: hr_skills_slides
#: model:ir.model.fields,field_description:hr_skills_slides.field_hr_resume_line__course_url
msgid "Course Url"
msgstr "URL del curso"

#. module: hr_skills_slides
#: model_terms:ir.ui.view,arch_db:hr_skills_slides.hr_employee_view_form
msgid "Courses"
msgstr "Cursos"

#. module: hr_skills_slides
#: model:ir.model.fields,field_description:hr_skills_slides.field_hr_employee__courses_completion_text
msgid "Courses Completion Text"
msgstr "Texto de finalización de cursos"

#. module: hr_skills_slides
#: model:ir.model.fields,field_description:hr_skills_slides.field_hr_resume_line__display_type
msgid "Display Type"
msgstr "Mostrar tipo"

#. module: hr_skills_slides
#: model:ir.model,name:hr_skills_slides.model_hr_employee
msgid "Employee"
msgstr "Empleado"

#. module: hr_skills_slides
#: model:ir.model.fields,field_description:hr_skills_slides.field_hr_employee__has_subscribed_courses
msgid "Has Subscribed Courses"
msgstr "Tiene suscripción a cursos"

#. module: hr_skills_slides
#: model:ir.model,name:hr_skills_slides.model_hr_resume_line
msgid "Resume line of an employee"
msgstr "Línea de currículum de un empleado"

#. module: hr_skills_slides
#. odoo-python
#: code:addons/hr_skills_slides/models/slide_channel.py:0
msgid "The employee has completed the course %s"
msgstr "El empleado completó el curso %s"

#. module: hr_skills_slides
#. odoo-python
#: code:addons/hr_skills_slides/models/slide_channel.py:0
msgid "The employee left the course %s"
msgstr "El empleado abandonó el curso %s"

#. module: hr_skills_slides
#. odoo-python
#: code:addons/hr_skills_slides/models/slide_channel.py:0
msgid "The employee subscribed to the course %s"
msgstr "El empleado se suscribió al curso %s"

#. module: hr_skills_slides
#: model:ir.model.fields,field_description:hr_skills_slides.field_hr_employee__subscribed_courses
msgid "eLearning Courses"
msgstr "Cursos de eLearning"
