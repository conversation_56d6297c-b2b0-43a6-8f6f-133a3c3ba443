# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* resource
# 
# Translators:
# Wil Odoo, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-14 20:48+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Wil Odoo, 2025\n"
"Language-Team: Russian (https://app.transifex.com/odoo/teams/41243/ru/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ru\n"
"Plural-Forms: nplurals=4; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<12 || n%100>14) ? 1 : n%10==0 || (n%10>=5 && n%10<=9) || (n%100>=11 && n%100<=14)? 2 : 3);\n"

#. module: resource
#. odoo-python
#: code:addons/resource/models/resource_calendar.py:0
#: code:addons/resource/models/resource_resource.py:0
msgid "%s (copy)"
msgstr "%s (копия)"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.resource_calendar_form
msgid "<span class=\"o_stat_text\">Time Off</span>"
msgstr "<span class=\"o_stat_text\">Свободное время</span>"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.resource_calendar_form
msgid "<span class=\"o_stat_text\">Work Resources</span>"
msgstr "<span class=\"o_stat_text\">Рабочие ресурсы</span>"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.resource_calendar_form
msgid "<span> hours/week</span>"
msgstr "<span> часы/неделя</span>"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar__active
#: model:ir.model.fields,field_description:resource.field_resource_resource__active
msgid "Active"
msgstr "Активный"

#. module: resource
#: model:ir.model.fields.selection,name:resource.selection__resource_calendar_attendance__day_period__afternoon
msgid "Afternoon"
msgstr "После полудня"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.resource_calendar_form
#: model_terms:ir.ui.view,arch_db:resource.resource_resource_form
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_search
#: model_terms:ir.ui.view,arch_db:resource.view_resource_resource_search
msgid "Archived"
msgstr "Архивировано"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.resource_calendar_form
msgid ""
"Are you sure you want to switch to a 1-week calendar? All work entries will "
"be lost."
msgstr ""

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.resource_calendar_form
msgid ""
"Are you sure you want to switch to a 2-week calendar? All work entries will "
"be lost."
msgstr ""

#. module: resource
#. odoo-python
#: code:addons/resource/models/resource_calendar.py:0
msgid "Attendances can't overlap."
msgstr "Посещаемость не может пересекаться."

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_resource__avatar_128
msgid "Avatar 128"
msgstr "Аватар 128"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar__hours_per_day
msgid "Average Hour per Day"
msgstr "Среднее количество часов в день"

#. module: resource
#: model:ir.model.fields,help:resource.field_resource_calendar__hours_per_day
msgid ""
"Average hours per day a resource is supposed to work with this calendar."
msgstr ""
"Среднее количество часов в день, когда ресурс должен работать по этому "
"календарю."

#. module: resource
#: model:ir.model.fields.selection,name:resource.selection__resource_calendar_attendance__day_period__lunch
msgid "Break"
msgstr "Перерыв"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar__two_weeks_calendar
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__two_weeks_calendar
msgid "Calendar in 2 weeks mode"
msgstr "Календарь в режиме 2 недели"

#. module: resource
#: model:ir.actions.act_window,name:resource.resource_calendar_closing_days
msgid "Closing Days"
msgstr "Закрытие дней"

#. module: resource
#: model:ir.model,name:resource.model_res_company
msgid "Companies"
msgstr "Компании"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar__company_id
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves__company_id
#: model:ir.model.fields,field_description:resource.field_resource_mixin__company_id
#: model:ir.model.fields,field_description:resource.field_resource_resource__company_id
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_leaves_search
#: model_terms:ir.ui.view,arch_db:resource.view_resource_resource_search
msgid "Company"
msgstr "Компания"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar__full_time_required_hours
msgid "Company Full Time"
msgstr "Компания Полное время"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar__create_uid
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__create_uid
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves__create_uid
#: model:ir.model.fields,field_description:resource.field_resource_resource__create_uid
msgid "Created by"
msgstr "Создано"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar__create_date
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__create_date
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves__create_date
#: model:ir.model.fields,field_description:resource.field_resource_resource__create_date
msgid "Created on"
msgstr "Создано"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_leaves_search
msgid "Date"
msgstr "Дата"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__day_period
msgid "Day Period"
msgstr "Период дня"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__dayofweek
msgid "Day of Week"
msgstr "День недели"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_res_company__resource_calendar_id
#: model:ir.model.fields,field_description:resource.field_res_users__resource_calendar_id
msgid "Default Working Hours"
msgstr "Рабочие часы по умолчанию"

#. module: resource
#: model:ir.model.fields,help:resource.field_res_users__resource_calendar_id
#: model:ir.model.fields,help:resource.field_resource_mixin__resource_calendar_id
#: model:ir.model.fields,help:resource.field_resource_resource__calendar_id
msgid ""
"Define the working schedule of the resource. If not set, the resource will "
"have fully flexible working hours."
msgstr ""

#. module: resource
#: model_terms:ir.actions.act_window,help:resource.action_resource_calendar_form
msgid ""
"Define working hours and time table that could be scheduled to your project "
"members"
msgstr ""
"Определите рабочее время и расписание, которые могут быть запланированы для "
"участников проекта"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar__display_name
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__display_name
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves__display_name
#: model:ir.model.fields,field_description:resource.field_resource_resource__display_name
msgid "Display Name"
msgstr "Отображаемое имя"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__display_type
msgid "Display Type"
msgstr "Тип отображения"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__duration_days
msgid "Duration (days)"
msgstr "Продолжительность (дней)"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__duration_hours
msgid "Duration (hours)"
msgstr "Длительность(часы)"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_resource__time_efficiency
msgid "Efficiency Factor"
msgstr "Коэффициент эффективности"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_resource__email
msgid "Email"
msgstr "Email"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__date_to
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves__date_to
msgid "End Date"
msgstr "Дата окончания"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar__two_weeks_explanation
msgid "Explanation"
msgstr "Объяснение"

#. module: resource
#: model:ir.model.fields,help:resource.field_resource_resource__share
msgid ""
"External user with limited access, created only for the purpose of sharing "
"data."
msgstr ""
"Внешний пользователь с ограниченным доступом, созданный только для обмена "
"данными."

#. module: resource
#: model:ir.model.fields.selection,name:resource.selection__resource_calendar_attendance__week_type__0
msgid "First"
msgstr "Первый"

#. module: resource
#. odoo-python
#: code:addons/resource/models/resource_calendar_attendance.py:0
msgid "First week"
msgstr "Первая неделя"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar__flexible_hours
msgid "Flexible Hours"
msgstr "Гибкие часы"

#. module: resource
#: model:ir.model.fields.selection,name:resource.selection__resource_calendar_attendance__dayofweek__4
msgid "Friday"
msgstr "Пятница"

#. module: resource
#. odoo-python
#: code:addons/resource/models/resource_calendar.py:0
msgid "Friday Afternoon"
msgstr "Пятница, полдень"

#. module: resource
#. odoo-python
#: code:addons/resource/models/resource_calendar.py:0
msgid "Friday Lunch"
msgstr "Пятничный обед"

#. module: resource
#. odoo-python
#: code:addons/resource/models/resource_calendar.py:0
msgid "Friday Morning"
msgstr "Утро пятницы"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.resource_resource_form
msgid "Fully Flexible"
msgstr ""

#. module: resource
#: model:ir.model.fields,help:resource.field_resource_calendar_attendance__sequence
msgid "Gives the sequence of this line when displaying the resource calendar."
msgstr ""
"Указывает последовательность этой строки при отображении календаря ресурсов."

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar__global_leave_ids
msgid "Global Time Off"
msgstr "Глобальное время отдыха"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_leaves_search
#: model_terms:ir.ui.view,arch_db:resource.view_resource_resource_search
msgid "Group By"
msgstr "Группировать по"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_attendance_form
msgid "Hours"
msgstr "Часов"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.resource_calendar_form
msgid "Hours per Week"
msgstr "Часы в неделю"

#. module: resource
#: model:ir.model.fields.selection,name:resource.selection__resource_resource__resource_type__user
#: model_terms:ir.ui.view,arch_db:resource.view_resource_resource_search
msgid "Human"
msgstr "Человек"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar__id
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__id
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves__id
#: model:ir.model.fields,field_description:resource.field_resource_resource__id
msgid "ID"
msgstr "ID"

#. module: resource
#: model:ir.model.fields,help:resource.field_resource_calendar_leaves__resource_id
msgid ""
"If empty, this is a generic time off for the company. If a resource is set, "
"the time off is only for this resource"
msgstr ""
"Если пусто, то это общий отгул для компании. Если задан ресурс, отгул "
"предоставляется только этому ресурсу"

#. module: resource
#: model:ir.model.fields,help:resource.field_resource_resource__active
msgid ""
"If the active field is set to False, it will allow you to hide the resource "
"record without removing it."
msgstr ""
"Если значение активного поля \"ложно\", это позволит вам скрыть запись "
"ресурса, не удаляя ее."

#. module: resource
#: model:ir.model.fields,help:resource.field_resource_calendar__active
msgid ""
"If the active field is set to false, it will allow you to hide the Working "
"Time without removing it."
msgstr ""
"Если активное поле имеет значение false, это позволит вам скрыть время "
"работы, не удаляя его."

#. module: resource
#. odoo-python
#: code:addons/resource/models/resource_calendar.py:0
msgid ""
"In a calendar with 2 weeks mode, all periods need to be in the sections."
msgstr ""
"В календаре с режимом 2 недели все периоды должны находиться в разделах."

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar__write_uid
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__write_uid
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves__write_uid
#: model:ir.model.fields,field_description:resource.field_resource_resource__write_uid
msgid "Last Updated by"
msgstr "Последнее обновление"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar__write_date
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__write_date
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves__write_date
#: model:ir.model.fields,field_description:resource.field_resource_resource__write_date
msgid "Last Updated on"
msgstr "Последнее обновление"

#. module: resource
#: model:ir.model.fields.selection,name:resource.selection__resource_resource__resource_type__material
#: model_terms:ir.ui.view,arch_db:resource.view_resource_resource_search
msgid "Material"
msgstr "Материал"

#. module: resource
#: model:ir.model.fields.selection,name:resource.selection__resource_calendar_attendance__dayofweek__0
msgid "Monday"
msgstr "Понедельник"

#. module: resource
#. odoo-python
#: code:addons/resource/models/resource_calendar.py:0
msgid "Monday Afternoon"
msgstr "Понедельник, вторая половина дня"

#. module: resource
#. odoo-python
#: code:addons/resource/models/resource_calendar.py:0
msgid "Monday Lunch"
msgstr "Обед в понедельник"

#. module: resource
#. odoo-python
#: code:addons/resource/models/resource_calendar.py:0
msgid "Monday Morning"
msgstr "Утро понедельника"

#. module: resource
#: model:ir.model.fields.selection,name:resource.selection__resource_calendar_attendance__day_period__morning
msgid "Morning"
msgstr "Утро"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar__name
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__name
#: model:ir.model.fields,field_description:resource.field_resource_resource__name
msgid "Name"
msgstr "Имя"

#. module: resource
#: model:ir.model.fields,help:resource.field_resource_calendar__full_time_required_hours
msgid ""
"Number of hours to work on the company schedule to be considered as "
"fulltime."
msgstr ""
"Количество часов, которые необходимо отработать по графику компании, чтобы "
"считаться полной занятостью."

#. module: resource
#: model:ir.model.fields.selection,name:resource.selection__resource_calendar_leaves__time_type__other
msgid "Other"
msgstr "Другое"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_leaves_search
msgid "Period"
msgstr "Период"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_resource__phone
msgid "Phone"
msgstr "Телефон"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves__name
#: model_terms:ir.ui.view,arch_db:resource.resource_calendar_leave_form
#: model_terms:ir.ui.view,arch_db:resource.resource_calendar_leave_tree
msgid "Reason"
msgstr "Причина"

#. module: resource
#: model:ir.model.fields,help:resource.field_resource_resource__user_id
msgid "Related user name for the resource to manage its access."
msgstr "Пользователь управляющий доступом к ресурсу."

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__resource_id
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves__resource_id
#: model:ir.model.fields,field_description:resource.field_resource_mixin__resource_id
#: model:ir.ui.menu,name:resource.menu_resource_config
#: model_terms:ir.ui.view,arch_db:resource.resource_resource_form
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_leaves_search
msgid "Resource"
msgstr "Ресурс"

#. module: resource
#: model:ir.model,name:resource.model_resource_mixin
msgid "Resource Mixin"
msgstr "Смешанный ресурс"

#. module: resource
#: model:ir.actions.act_window,name:resource.action_resource_calendar_leave_tree
#: model:ir.actions.act_window,name:resource.resource_calendar_leaves_action_from_calendar
#: model:ir.ui.menu,name:resource.menu_view_resource_calendar_leaves_search
msgid "Resource Time Off"
msgstr "Время отдыха"

#. module: resource
#: model:ir.model,name:resource.model_resource_calendar_leaves
msgid "Resource Time Off Detail"
msgstr "Подробности отсутствий"

#. module: resource
#: model:ir.model,name:resource.model_resource_calendar
msgid "Resource Working Time"
msgstr "Рабочее время"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__calendar_id
msgid "Resource's Calendar"
msgstr "Календарь ресурса"

#. module: resource
#: model:ir.actions.act_window,name:resource.action_resource_resource_tree
#: model:ir.actions.act_window,name:resource.resource_resource_action_from_calendar
#: model:ir.model,name:resource.model_resource_resource
#: model:ir.model.fields,field_description:resource.field_res_users__resource_ids
#: model:ir.ui.menu,name:resource.menu_resource_resource
#: model_terms:ir.ui.view,arch_db:resource.resource_resource_tree
msgid "Resources"
msgstr "Ресурсы"

#. module: resource
#: model:ir.actions.act_window,name:resource.resource_calendar_resources_leaves
msgid "Resources Time Off"
msgstr "Ресурсы Время отдыха"

#. module: resource
#: model_terms:ir.actions.act_window,help:resource.action_resource_resource_tree
#: model_terms:ir.actions.act_window,help:resource.resource_resource_action_from_calendar
msgid ""
"Resources allow you to create and manage resources that should be involved "
"in a specific project phase. You can also set their efficiency level and "
"workload based on their weekly working hours."
msgstr ""
"Ресурсы позволяют создавать и управлять ресурсами, которые должны быть "
"задействованы на определенной фазе проекта. Вы также можете установить их "
"уровень эффективности и рабочую нагрузку исходя из их недельного рабочего "
"времени."

#. module: resource
#: model:ir.model.fields.selection,name:resource.selection__resource_calendar_attendance__dayofweek__5
msgid "Saturday"
msgstr "Суббота"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.view_resource_resource_search
msgid "Search Resource"
msgstr "Искать ресурс"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_leaves_search
msgid "Search Working Period Time Off"
msgstr "Поиск Период работы Время отдыха"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_search
msgid "Search Working Time"
msgstr "Время работы поиска"

#. module: resource
#: model:ir.model.fields.selection,name:resource.selection__resource_calendar_attendance__week_type__1
msgid "Second"
msgstr "Секунда"

#. module: resource
#. odoo-python
#: code:addons/resource/models/resource_calendar_attendance.py:0
msgid "Second week"
msgstr "Вторая неделя"

#. module: resource
#: model:ir.model.fields.selection,name:resource.selection__resource_calendar_attendance__display_type__line_section
msgid "Section"
msgstr "Раздел"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__sequence
msgid "Sequence"
msgstr "Последовательность"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_resource__share
msgid "Share User"
msgstr "Поделиться Пользователь"

#. module: resource
#. odoo-python
#: code:addons/resource/models/res_company.py:0
msgid "Standard 40 hours/week"
msgstr "Стандартная 40 часовая рабочая неделя"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves__date_from
msgid "Start Date"
msgstr "Дата начала"

#. module: resource
#: model:ir.model.fields,help:resource.field_resource_calendar_attendance__hour_from
msgid ""
"Start and End time of working.\n"
"A specific value of 24:00 is interpreted as 23:59:59.999999."
msgstr ""
"Время начала и окончания работы.\n"
"Конкретное значение 24:00 интерпретируется как 23:59:59.999999."

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__date_from
msgid "Starting Date"
msgstr "Дата начала"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_leaves_search
msgid "Starting Date of Time Off"
msgstr "Дата начала отгула"

#. module: resource
#: model:ir.model.fields.selection,name:resource.selection__resource_calendar_attendance__dayofweek__6
msgid "Sunday"
msgstr "Воскресенье"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.resource_calendar_form
msgid "Switch"
msgstr "Переключатель"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.resource_calendar_form
msgid "Switch to 1 week calendar"
msgstr "Переключение на календарь на 1 неделю"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.resource_calendar_form
msgid "Switch to 2 weeks calendar"
msgstr "Переключитесь на 2-недельный календарь"

#. module: resource
#: model:ir.model.fields,help:resource.field_resource_calendar_attendance__display_type
msgid "Technical field for UX purpose."
msgstr "Техническая область для целей UX."

#. module: resource
#. odoo-python
#: code:addons/resource/models/resource_calendar.py:0
msgid ""
"The current week (from %(first_day)s to %(last_day)s) corresponds to week "
"number %(number)s."
msgstr ""

#. module: resource
#. odoo-python
#: code:addons/resource/models/resource_calendar_leaves.py:0
msgid "The start date of the time off must be earlier than the end date."
msgstr "Дата начала отгула должна быть раньше даты окончания."

#. module: resource
#: model:ir.model.fields,help:resource.field_resource_calendar__tz
#: model:ir.model.fields,help:resource.field_resource_mixin__tz
msgid ""
"This field is used in order to define in which timezone the resources will "
"work."
msgstr ""
"Это поле используется для определения того, в каком часовом поясе будут "
"работать ресурсы."

#. module: resource
#: model:ir.model.fields,help:resource.field_resource_resource__time_efficiency
msgid ""
"This field is used to calculate the expected duration of a work order at "
"this work center. For example, if a work order takes one hour and the "
"efficiency factor is 100%, then the expected duration will be one hour. If "
"the efficiency factor is 200%, however the expected duration will be 30 "
"minutes."
msgstr ""
"Это поле используется для расчета ожидаемой продолжительности выполнения "
"заказа в данном рабочем центре. Например, если заказ занимает один час, а "
"коэффициент эффективности равен 100 %, то ожидаемая продолжительность "
"составит один час. Если коэффициент эффективности равен 200 %, то ожидаемая "
"продолжительность составит 30 минут."

#. module: resource
#: model:ir.model.fields.selection,name:resource.selection__resource_calendar_attendance__dayofweek__3
msgid "Thursday"
msgstr "Четверг"

#. module: resource
#. odoo-python
#: code:addons/resource/models/resource_calendar.py:0
msgid "Thursday Afternoon"
msgstr "Четверг, вторая половина дня"

#. module: resource
#. odoo-python
#: code:addons/resource/models/resource_calendar.py:0
msgid "Thursday Lunch"
msgstr "Обед в четверг"

#. module: resource
#. odoo-python
#: code:addons/resource/models/resource_calendar.py:0
msgid "Thursday Morning"
msgstr "Утро четверга"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar__leave_ids
#: model:ir.model.fields.selection,name:resource.selection__resource_calendar_leaves__time_type__leave
msgid "Time Off"
msgstr "Отпуск"

#. module: resource
#: model_terms:ir.ui.view,arch_db:resource.resource_calendar_leave_form
#: model_terms:ir.ui.view,arch_db:resource.resource_calendar_leave_tree
msgid "Time Off Detail"
msgstr "Детализация отгулов"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves__time_type
msgid "Time Type"
msgstr "Тип времени"

#. module: resource
#: model:ir.model.constraint,message:resource.constraint_resource_resource_check_time_efficiency
msgid "Time efficiency must be strictly positive"
msgstr ""
"Значение эффективности использования времени должно быть положительным"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar__tz
#: model:ir.model.fields,field_description:resource.field_resource_mixin__tz
#: model:ir.model.fields,field_description:resource.field_resource_resource__tz
msgid "Timezone"
msgstr "Часовой пояс"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar__tz_offset
msgid "Timezone offset"
msgstr "Смещение часового пояса"

#. module: resource
#: model:ir.model.fields.selection,name:resource.selection__resource_calendar_attendance__dayofweek__1
msgid "Tuesday"
msgstr "Вторник"

#. module: resource
#. odoo-python
#: code:addons/resource/models/resource_calendar.py:0
msgid "Tuesday Afternoon"
msgstr "Вторник, вторая половина дня"

#. module: resource
#. odoo-python
#: code:addons/resource/models/resource_calendar.py:0
msgid "Tuesday Lunch"
msgstr "Обед во вторник"

#. module: resource
#. odoo-python
#: code:addons/resource/models/resource_calendar.py:0
msgid "Tuesday Morning"
msgstr "Вечер понедельника"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_resource__resource_type
#: model_terms:ir.ui.view,arch_db:resource.view_resource_resource_search
msgid "Type"
msgstr "Тип"

#. module: resource
#: model:ir.model,name:resource.model_res_users
#: model:ir.model.fields,field_description:resource.field_resource_resource__user_id
#: model_terms:ir.ui.view,arch_db:resource.view_resource_resource_search
msgid "User"
msgstr "Пользователь"

#. module: resource
#: model:ir.model.fields.selection,name:resource.selection__resource_calendar_attendance__dayofweek__2
msgid "Wednesday"
msgstr "Среда"

#. module: resource
#. odoo-python
#: code:addons/resource/models/resource_calendar.py:0
msgid "Wednesday Afternoon"
msgstr "Среда, вторая половина дня"

#. module: resource
#. odoo-python
#: code:addons/resource/models/resource_calendar.py:0
msgid "Wednesday Lunch"
msgstr "Обед в среду"

#. module: resource
#. odoo-python
#: code:addons/resource/models/resource_calendar.py:0
msgid "Wednesday Morning"
msgstr "Утро среды"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__week_type
msgid "Week Number"
msgstr "Неделя года"

#. module: resource
#: model:ir.model.fields,help:resource.field_resource_calendar__flexible_hours
msgid ""
"When enabled, it will allow employees to work flexibly, without relying on "
"the company's working schedule (working hours)."
msgstr ""

#. module: resource
#: model:ir.model.fields,help:resource.field_resource_calendar_leaves__time_type
msgid ""
"Whether this should be computed as a time off or as work time (eg: "
"formation)"
msgstr ""
"Должно ли это исчисляться как время отдыха или как рабочее время (например, "
"формирование)"

#. module: resource
#: model:ir.model,name:resource.model_resource_calendar_attendance
msgid "Work Detail"
msgstr "Детали"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__hour_from
msgid "Work from"
msgstr "Работа от"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar_attendance__hour_to
msgid "Work to"
msgstr "Работайте, чтобы"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_res_company__resource_calendar_ids
#: model:ir.model.fields,field_description:resource.field_resource_calendar_leaves__calendar_id
#: model:ir.model.fields,field_description:resource.field_resource_mixin__resource_calendar_id
#: model_terms:ir.ui.view,arch_db:resource.resource_calendar_form
msgid "Working Hours"
msgstr "Часы работы"

#. module: resource
#. odoo-python
#: code:addons/resource/models/resource_calendar.py:0
msgid "Working Hours of %s"
msgstr "Рабочие часы %s"

#. module: resource
#: model:ir.actions.act_window,name:resource.action_resource_calendar_form
#: model:ir.ui.menu,name:resource.menu_resource_calendar
msgid "Working Schedules"
msgstr "Рабочие графики"

#. module: resource
#: model:ir.model.fields,field_description:resource.field_resource_calendar__attendance_ids
#: model:ir.model.fields,field_description:resource.field_resource_resource__calendar_id
#: model_terms:ir.ui.view,arch_db:resource.resource_calendar_form
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_attendance_form
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_attendance_tree
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_search
#: model_terms:ir.ui.view,arch_db:resource.view_resource_calendar_tree
#: model_terms:ir.ui.view,arch_db:resource.view_resource_resource_search
msgid "Working Time"
msgstr "Рабочее время"

#. module: resource
#. odoo-python
#: code:addons/resource/models/resource_calendar.py:0
msgid "You can't delete section between weeks."
msgstr "Вы не можете удалить раздел между неделями."

#. module: resource
#. odoo-python
#: code:addons/resource/models/resource_calendar.py:0
msgid "first"
msgstr "первый"

#. module: resource
#. odoo-python
#: code:addons/resource/models/resource_calendar_attendance.py:0
msgid "other week"
msgstr "другая неделя"

#. module: resource
#. odoo-python
#: code:addons/resource/models/resource_calendar.py:0
msgid "second"
msgstr "секунда"

#. module: resource
#. odoo-python
#: code:addons/resource/models/resource_calendar_attendance.py:0
msgid "this week"
msgstr "на этой неделе"
