<?xml version='1.0' encoding='UTF-8'?>
<Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Invoice-2"
         xmlns:cbc="urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2"
         xmlns:cac="urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2">
    <cbc:CustomizationID>urn:cen.eu:en16931:2017#compliant#urn:fdc:peppol.eu:2017:poacc:billing:3.0
    </cbc:CustomizationID>
    <cbc:ProfileID>urn:fdc:peppol.eu:2017:poacc:billing:01:1.0</cbc:ProfileID>
    <cbc:ID>___ignore___</cbc:ID>
    <cbc:IssueDate>2017-01-01</cbc:IssueDate>
    <cbc:DueDate>2017-01-31</cbc:DueDate>
    <cbc:InvoiceTypeCode>380</cbc:InvoiceTypeCode>
    <cbc:Note>test narration</cbc:Note>
    <cbc:DocumentCurrencyCode>USD</cbc:DocumentCurrencyCode>
    <cbc:BuyerReference>ref_partner_2</cbc:BuyerReference>
    <cac:OrderReference>
        <cbc:ID>ref_move</cbc:ID>
    </cac:OrderReference>
    <cac:AdditionalDocumentReference>
        <cbc:ID>___ignore___</cbc:ID>
        <cbc:Attachment>
            <cbc:EmbeddedDocumentBinaryObject mimeCode="___ignore___" filename="___ignore___">
                ___ignore___
            </cbc:EmbeddedDocumentBinaryObject>
        </cbc:Attachment>
    </cac:AdditionalDocumentReference>
    <cac:AccountingSupplierParty>
        <cac:Party>
            <cbc:EndpointID schemeID="0208">**********</cbc:EndpointID>
            <cac:PartyIdentification>
                <cbc:ID>ref_partner_1</cbc:ID>
            </cac:PartyIdentification>
            <cac:PartyName>
                <cbc:Name>partner_1</cbc:Name>
            </cac:PartyName>
            <cac:PostalAddress>
                <cbc:StreetName>Chaussée de Namur 40</cbc:StreetName>
                <cbc:CityName>Ramillies</cbc:CityName>
                <cbc:PostalZone>1367</cbc:PostalZone>
                <cac:Country>
                    <cbc:IdentificationCode>BE</cbc:IdentificationCode>
                </cac:Country>
            </cac:PostalAddress>
            <cac:PartyTaxScheme>
                <cbc:CompanyID>BE**********</cbc:CompanyID>
                <cac:TaxScheme>
                    <cbc:ID>VAT</cbc:ID>
                </cac:TaxScheme>
            </cac:PartyTaxScheme>
            <cac:PartyLegalEntity>
                <cbc:RegistrationName>partner_1</cbc:RegistrationName>
                <cbc:CompanyID>BE**********</cbc:CompanyID>
            </cac:PartyLegalEntity>
            <cac:Contact>
                <cbc:Name>partner_1</cbc:Name>
            </cac:Contact>
        </cac:Party>
    </cac:AccountingSupplierParty>
    <cac:AccountingCustomerParty>
        <cac:Party>
            <cbc:EndpointID schemeID="0208">**********</cbc:EndpointID>
            <cac:PartyIdentification>
                <cbc:ID>ref_partner_2</cbc:ID>
            </cac:PartyIdentification>
            <cac:PartyName>
                <cbc:Name>partner_2</cbc:Name>
            </cac:PartyName>
            <cac:PostalAddress>
                <cbc:StreetName>Rue des Bourlottes 9</cbc:StreetName>
                <cbc:CityName>Ramillies</cbc:CityName>
                <cbc:PostalZone>1367</cbc:PostalZone>
                <cac:Country>
                    <cbc:IdentificationCode>BE</cbc:IdentificationCode>
                </cac:Country>
            </cac:PostalAddress>
            <cac:PartyTaxScheme>
                <cbc:CompanyID>BE**********</cbc:CompanyID>
                <cac:TaxScheme>
                    <cbc:ID>VAT</cbc:ID>
                </cac:TaxScheme>
            </cac:PartyTaxScheme>
            <cac:PartyLegalEntity>
                <cbc:RegistrationName>partner_2</cbc:RegistrationName>
                <cbc:CompanyID>BE**********</cbc:CompanyID>
            </cac:PartyLegalEntity>
            <cac:Contact>
                <cbc:Name>partner_2</cbc:Name>
            </cac:Contact>
        </cac:Party>
    </cac:AccountingCustomerParty>
    <cac:Delivery>
        <cac:DeliveryLocation>
            <cac:Address>
                <cbc:StreetName>Rue des Bourlottes 9</cbc:StreetName>
                <cbc:CityName>Ramillies</cbc:CityName>
                <cbc:PostalZone>1367</cbc:PostalZone>
                <cac:Country>
                    <cbc:IdentificationCode>BE</cbc:IdentificationCode>
                </cac:Country>
            </cac:Address>
        </cac:DeliveryLocation>
    </cac:Delivery>
    <cac:PaymentMeans>
        <cbc:PaymentMeansCode name="credit transfer">30</cbc:PaymentMeansCode>
        <cbc:PaymentID>___ignore___</cbc:PaymentID>
        <cac:PayeeFinancialAccount>
            <cbc:ID>****************</cbc:ID>
        </cac:PayeeFinancialAccount>
    </cac:PaymentMeans>
    <cac:PaymentTerms>
        <cbc:Note>Payment terms: 30 Days, 2% Early Payment Discount under 7 days</cbc:Note>
    </cac:PaymentTerms>
    <cac:AllowanceCharge>
        <cbc:ChargeIndicator>false</cbc:ChargeIndicator>
        <cbc:AllowanceChargeReasonCode>66</cbc:AllowanceChargeReasonCode>
        <cbc:AllowanceChargeReason>Conditional cash/payment discount</cbc:AllowanceChargeReason>
        <cbc:Amount currencyID="USD">4.00</cbc:Amount>
        <cac:TaxCategory>
            <cbc:ID>S</cbc:ID>
            <cbc:Percent>6.0</cbc:Percent>
            <cac:TaxScheme>
                <cbc:ID>VAT</cbc:ID>
            </cac:TaxScheme>
        </cac:TaxCategory>
    </cac:AllowanceCharge>
    <cac:AllowanceCharge>
        <cbc:ChargeIndicator>false</cbc:ChargeIndicator>
        <cbc:AllowanceChargeReasonCode>66</cbc:AllowanceChargeReasonCode>
        <cbc:AllowanceChargeReason>Conditional cash/payment discount</cbc:AllowanceChargeReason>
        <cbc:Amount currencyID="USD">48.00</cbc:Amount>
        <cac:TaxCategory>
            <cbc:ID>S</cbc:ID>
            <cbc:Percent>21.0</cbc:Percent>
            <cac:TaxScheme>
                <cbc:ID>VAT</cbc:ID>
            </cac:TaxScheme>
        </cac:TaxCategory>
    </cac:AllowanceCharge>
    <cac:AllowanceCharge>
        <cbc:ChargeIndicator>true</cbc:ChargeIndicator>
        <cbc:AllowanceChargeReasonCode>ZZZ</cbc:AllowanceChargeReasonCode>
        <cbc:AllowanceChargeReason>Conditional cash/payment discount</cbc:AllowanceChargeReason>
        <cbc:Amount currencyID="USD">52.00</cbc:Amount>
        <cac:TaxCategory>
            <cbc:ID>E</cbc:ID>
            <cbc:Percent>0.0</cbc:Percent>
            <cac:TaxScheme>
                <cbc:ID>VAT</cbc:ID>
            </cac:TaxScheme>
        </cac:TaxCategory>
    </cac:AllowanceCharge>
    <cac:TaxTotal>
        <cbc:TaxAmount currencyID="USD">505.68</cbc:TaxAmount>
        <cac:TaxSubtotal>
            <cbc:TaxableAmount currencyID="USD">196.00</cbc:TaxableAmount>
            <cbc:TaxAmount currencyID="USD">11.76</cbc:TaxAmount>
            <cac:TaxCategory>
                <cbc:ID>S</cbc:ID>
                <cbc:Percent>6.0</cbc:Percent>
                <cac:TaxScheme>
                    <cbc:ID>VAT</cbc:ID>
                </cac:TaxScheme>
            </cac:TaxCategory>
        </cac:TaxSubtotal>
        <cac:TaxSubtotal>
            <cbc:TaxableAmount currencyID="USD">2352.00</cbc:TaxableAmount>
            <cbc:TaxAmount currencyID="USD">493.92</cbc:TaxAmount>
            <cac:TaxCategory>
                <cbc:ID>S</cbc:ID>
                <cbc:Percent>21.0</cbc:Percent>
                <cac:TaxScheme>
                    <cbc:ID>VAT</cbc:ID>
                </cac:TaxScheme>
            </cac:TaxCategory>
        </cac:TaxSubtotal>
        <cac:TaxSubtotal>
            <cbc:TaxableAmount currencyID="USD">52.00</cbc:TaxableAmount>
            <cbc:TaxAmount currencyID="USD">0.00</cbc:TaxAmount>
            <cac:TaxCategory>
                <cbc:ID>E</cbc:ID>
                <cbc:Percent>0.0</cbc:Percent>
                <cbc:TaxExemptionReason>Exempt from tax</cbc:TaxExemptionReason>
                <cac:TaxScheme>
                    <cbc:ID>VAT</cbc:ID>
                </cac:TaxScheme>
            </cac:TaxCategory>
        </cac:TaxSubtotal>
    </cac:TaxTotal>
    <cac:LegalMonetaryTotal>
        <cbc:LineExtensionAmount currencyID="USD">2600.00</cbc:LineExtensionAmount>
        <cbc:TaxExclusiveAmount currencyID="USD">2600.00</cbc:TaxExclusiveAmount>
        <cbc:TaxInclusiveAmount currencyID="USD">3105.68</cbc:TaxInclusiveAmount>
        <cbc:AllowanceTotalAmount currencyID="USD">52.00</cbc:AllowanceTotalAmount>
        <cbc:ChargeTotalAmount currencyID="USD">52.00</cbc:ChargeTotalAmount>
        <cbc:PrepaidAmount currencyID="USD">0.00</cbc:PrepaidAmount>
        <cbc:PayableAmount currencyID="USD">3105.68</cbc:PayableAmount>
    </cac:LegalMonetaryTotal>
    <cac:InvoiceLine>
        <cbc:ID>___ignore___</cbc:ID>
        <cbc:InvoicedQuantity unitCode="C62">1.0</cbc:InvoicedQuantity>
        <cbc:LineExtensionAmount currencyID="USD">200.00</cbc:LineExtensionAmount>
        <cac:Item>
            <cbc:Description>product_a</cbc:Description>
            <cbc:Name>product_a</cbc:Name>
            <cac:ClassifiedTaxCategory>
                <cbc:ID>S</cbc:ID>
                <cbc:Percent>6.0</cbc:Percent>
                <cac:TaxScheme>
                    <cbc:ID>VAT</cbc:ID>
                </cac:TaxScheme>
            </cac:ClassifiedTaxCategory>
        </cac:Item>
        <cac:Price>
            <cbc:PriceAmount currencyID="USD">200.0</cbc:PriceAmount>
        </cac:Price>
    </cac:InvoiceLine>
    <cac:InvoiceLine>
        <cbc:ID>___ignore___</cbc:ID>
        <cbc:InvoicedQuantity unitCode="DZN">1.0</cbc:InvoicedQuantity>
        <cbc:LineExtensionAmount currencyID="USD">2400.00</cbc:LineExtensionAmount>
        <cac:Item>
            <cbc:Description>product_b</cbc:Description>
            <cbc:Name>product_b</cbc:Name>
            <cac:ClassifiedTaxCategory>
                <cbc:ID>S</cbc:ID>
                <cbc:Percent>21.0</cbc:Percent>
                <cac:TaxScheme>
                    <cbc:ID>VAT</cbc:ID>
                </cac:TaxScheme>
            </cac:ClassifiedTaxCategory>
        </cac:Item>
        <cac:Price>
            <cbc:PriceAmount currencyID="USD">2400.0</cbc:PriceAmount>
        </cac:Price>
    </cac:InvoiceLine>
</Invoice>
