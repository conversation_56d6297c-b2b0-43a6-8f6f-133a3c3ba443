<?xml version='1.0' encoding='UTF-8'?>
<AttachedDocument xmlns:ext="urn:oasis:names:specification:ubl:schema:xsd:CommonExtensionComponents-2" xmlns:ccts="urn:un:unece:uncefact:data:specification:CoreComponentTypeSchemaModule:2" xmlns:xades="http://uri.etsi.org/01903/v1.3.2#" xmlns:cbc="urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2" xmlns:ds="http://www.w3.org/2000/09/xmldsig#" xmlns="urn:oasis:names:specification:ubl:schema:xsd:AttachedDocument-2" xmlns:cac="urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2" xmlns:xades141="http://uri.etsi.org/01903/v1.4.1#">
    <cbc:UBLVersionID>2.1</cbc:UBLVersionID>
    <cbc:CustomizationID>urn:cen.eu:en16931:2017#conformant#urn:fdc:peppol.eu:2017:poacc:billing:international:aunz:3.0</cbc:CustomizationID>
    <cbc:ProfileID>urn:fdc:peppol.eu:2017:poacc:billing:01:1.0</cbc:ProfileID>
    <cbc:ID>AttachedDocument</cbc:ID>
    <cbc:IssueDate>2017-01-01</cbc:IssueDate>
    <cbc:IssueTime>12:00:00</cbc:IssueTime>
    <cbc:DocumentType>string</cbc:DocumentType>
    <cbc:ParentDocumentID>INV/2017/00001</cbc:ParentDocumentID>
    <cac:SenderParty>
      <cbc:EndpointID schemeID="0151">***********</cbc:EndpointID>
      <cac:PartyName>
        <cbc:Name>partner_1</cbc:Name>
      </cac:PartyName>
      <cac:PostalAddress>
        <cbc:StreetName>Spring St.</cbc:StreetName>
        <cbc:CityName>Melbourne</cbc:CityName>
        <cbc:PostalZone>3002</cbc:PostalZone>
        <cac:Country>
          <cbc:IdentificationCode>AU</cbc:IdentificationCode>
        </cac:Country>
      </cac:PostalAddress>
      <cac:PartyTaxScheme>
        <cbc:CompanyID>***********</cbc:CompanyID>
        <cac:TaxScheme>
          <cbc:ID>GST</cbc:ID>
        </cac:TaxScheme>
      </cac:PartyTaxScheme>
      <cac:PartyLegalEntity>
        <cbc:RegistrationName>partner_1</cbc:RegistrationName>
        <cbc:CompanyID schemeID="0151">***********</cbc:CompanyID>
      </cac:PartyLegalEntity>
      <cac:Contact>
        <cbc:Name>partner_1</cbc:Name>
        <cbc:Telephone>+31 180 6 225789</cbc:Telephone>
        <cbc:ElectronicMail><EMAIL></cbc:ElectronicMail>
      </cac:Contact>
    </cac:SenderParty>
    <cac:ReceiverParty>
      <cbc:EndpointID schemeID="0151">***********</cbc:EndpointID>
      <cac:PartyName>
        <cbc:Name>partner_2</cbc:Name>
      </cac:PartyName>
      <cac:PostalAddress>
        <cbc:StreetName>Parliament Dr</cbc:StreetName>
        <cbc:CityName>Canberra</cbc:CityName>
        <cbc:PostalZone>2600</cbc:PostalZone>
        <cac:Country>
          <cbc:IdentificationCode>AU</cbc:IdentificationCode>
        </cac:Country>
      </cac:PostalAddress>
      <cac:PartyTaxScheme>
        <cbc:CompanyID>***********</cbc:CompanyID>
        <cac:TaxScheme>
          <cbc:ID>GST</cbc:ID>
        </cac:TaxScheme>
      </cac:PartyTaxScheme>
      <cac:PartyLegalEntity>
        <cbc:RegistrationName>partner_2</cbc:RegistrationName>
        <cbc:CompanyID schemeID="0151">***********</cbc:CompanyID>
      </cac:PartyLegalEntity>
      <cac:Contact>
        <cbc:Name>partner_2</cbc:Name>
      </cac:Contact>
    </cac:ReceiverParty>
    <cac:Attachment>
        <cac:ExternalReference>
            <cbc:MimeCode>text/xml</cbc:MimeCode>
            <cbc:EncodingCode>UTF-8</cbc:EncodingCode>
            <cbc:Description><![CDATA[<?xml version='1.0' encoding='UTF-8'?>
<Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Invoice-2" xmlns:cac="urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2" xmlns:cbc="urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2">
  <cbc:CustomizationID>urn:cen.eu:en16931:2017#conformant#urn:fdc:peppol.eu:2017:poacc:billing:international:aunz:3.0</cbc:CustomizationID>
  <cbc:ProfileID>urn:fdc:peppol.eu:2017:poacc:billing:01:1.0</cbc:ProfileID>
  <cbc:ID>INV/2017/00001</cbc:ID>
  <cbc:IssueDate>2017-01-01</cbc:IssueDate>
  <cbc:DueDate>2017-02-28</cbc:DueDate>
  <cbc:InvoiceTypeCode>380</cbc:InvoiceTypeCode>
  <cbc:Note>test narration</cbc:Note>
  <cbc:DocumentCurrencyCode>USD</cbc:DocumentCurrencyCode>
  <cbc:BuyerReference>ref_partner_2</cbc:BuyerReference>
  <cac:OrderReference>
    <cbc:ID>___ignore___</cbc:ID>
  </cac:OrderReference>
  <cac:AdditionalDocumentReference>
    <cbc:ID>___ignore___</cbc:ID>
    <cbc:Attachment>
      <cbc:EmbeddedDocumentBinaryObject>___ignore___</cbc:EmbeddedDocumentBinaryObject>
    </cbc:Attachment>
  </cac:AdditionalDocumentReference>
  <cac:AccountingSupplierParty>
    <cac:Party>
      <cbc:EndpointID schemeID="0151">***********</cbc:EndpointID>
      <cac:PartyName>
        <cbc:Name>partner_1</cbc:Name>
      </cac:PartyName>
      <cac:PostalAddress>
        <cbc:StreetName>Spring St.</cbc:StreetName>
        <cbc:CityName>Melbourne</cbc:CityName>
        <cbc:PostalZone>3002</cbc:PostalZone>
        <cac:Country>
          <cbc:IdentificationCode>AU</cbc:IdentificationCode>
        </cac:Country>
      </cac:PostalAddress>
      <cac:PartyTaxScheme>
        <cbc:CompanyID>***********</cbc:CompanyID>
        <cac:TaxScheme>
          <cbc:ID>GST</cbc:ID>
        </cac:TaxScheme>
      </cac:PartyTaxScheme>
      <cac:PartyLegalEntity>
        <cbc:RegistrationName>partner_1</cbc:RegistrationName>
        <cbc:CompanyID schemeID="0151">***********</cbc:CompanyID>
      </cac:PartyLegalEntity>
      <cac:Contact>
        <cbc:Name>partner_1</cbc:Name>
        <cbc:Telephone>+31 180 6 225789</cbc:Telephone>
        <cbc:ElectronicMail><EMAIL></cbc:ElectronicMail>
      </cac:Contact>
    </cac:Party>
  </cac:AccountingSupplierParty>
  <cac:AccountingCustomerParty>
    <cac:Party>
      <cbc:EndpointID schemeID="0151">***********</cbc:EndpointID>
      <cac:PartyName>
        <cbc:Name>partner_2</cbc:Name>
      </cac:PartyName>
      <cac:PostalAddress>
        <cbc:StreetName>Parliament Dr</cbc:StreetName>
        <cbc:CityName>Canberra</cbc:CityName>
        <cbc:PostalZone>2600</cbc:PostalZone>
        <cac:Country>
          <cbc:IdentificationCode>AU</cbc:IdentificationCode>
        </cac:Country>
      </cac:PostalAddress>
      <cac:PartyTaxScheme>
        <cbc:CompanyID>***********</cbc:CompanyID>
        <cac:TaxScheme>
          <cbc:ID>GST</cbc:ID>
        </cac:TaxScheme>
      </cac:PartyTaxScheme>
      <cac:PartyLegalEntity>
        <cbc:RegistrationName>partner_2</cbc:RegistrationName>
        <cbc:CompanyID schemeID="0151">***********</cbc:CompanyID>
      </cac:PartyLegalEntity>
      <cac:Contact>
        <cbc:Name>partner_2</cbc:Name>
      </cac:Contact>
    </cac:Party>
  </cac:AccountingCustomerParty>
  <cac:Delivery>
    <cac:DeliveryLocation>
      <cac:Address>
        <cbc:StreetName>Parliament Dr</cbc:StreetName>
        <cbc:CityName>Canberra</cbc:CityName>
        <cbc:PostalZone>2600</cbc:PostalZone>
        <cac:Country>
          <cbc:IdentificationCode>AU</cbc:IdentificationCode>
        </cac:Country>
      </cac:Address>
    </cac:DeliveryLocation>
  </cac:Delivery>
  <cac:PaymentMeans>
    <cbc:PaymentMeansCode name="credit transfer">30</cbc:PaymentMeansCode>
    <cbc:PaymentID>INV/2017/00001</cbc:PaymentID>
    <cac:PayeeFinancialAccount>
      <cbc:ID>000099998B57</cbc:ID>
    </cac:PayeeFinancialAccount>
  </cac:PaymentMeans>
  <cac:PaymentTerms>
    <cbc:Note>Payment terms: 30% Advance End of Following Month</cbc:Note>
  </cac:PaymentTerms>
  <cac:TaxTotal>
    <cbc:TaxAmount currencyID="USD">268.20</cbc:TaxAmount>
    <cac:TaxSubtotal>
      <cbc:TaxableAmount currencyID="USD">2682.00</cbc:TaxableAmount>
      <cbc:TaxAmount currencyID="USD">268.20</cbc:TaxAmount>
      <cac:TaxCategory>
        <cbc:ID>S</cbc:ID>
        <cbc:Percent>10.0</cbc:Percent>
        <cac:TaxScheme>
          <cbc:ID>GST</cbc:ID>
        </cac:TaxScheme>
      </cac:TaxCategory>
    </cac:TaxSubtotal>
  </cac:TaxTotal>
  <cac:LegalMonetaryTotal>
    <cbc:LineExtensionAmount currencyID="USD">2682.00</cbc:LineExtensionAmount>
    <cbc:TaxExclusiveAmount currencyID="USD">2682.00</cbc:TaxExclusiveAmount>
    <cbc:TaxInclusiveAmount currencyID="USD">2950.20</cbc:TaxInclusiveAmount>
    <cbc:PrepaidAmount currencyID="USD">0.00</cbc:PrepaidAmount>
    <cbc:PayableAmount currencyID="USD">2950.20</cbc:PayableAmount>
  </cac:LegalMonetaryTotal>
  <cac:InvoiceLine>
    <cbc:ID>557</cbc:ID>
    <cbc:InvoicedQuantity unitCode="DZN">2.0</cbc:InvoicedQuantity>
    <cbc:LineExtensionAmount currencyID="USD">1782.00</cbc:LineExtensionAmount>
    <cac:AllowanceCharge>
      <cbc:ChargeIndicator>false</cbc:ChargeIndicator>
      <cbc:AllowanceChargeReasonCode>95</cbc:AllowanceChargeReasonCode>
      <cbc:Amount currencyID="USD">198.00</cbc:Amount>
    </cac:AllowanceCharge>
    <cac:Item>
      <cbc:Description>product_a</cbc:Description>
      <cbc:Name>product_a</cbc:Name>
      <cac:ClassifiedTaxCategory>
        <cbc:ID>S</cbc:ID>
        <cbc:Percent>10.0</cbc:Percent>
        <cac:TaxScheme>
          <cbc:ID>GST</cbc:ID>
        </cac:TaxScheme>
      </cac:ClassifiedTaxCategory>
    </cac:Item>
    <cac:Price>
      <cbc:PriceAmount currencyID="USD">990.0</cbc:PriceAmount>
    </cac:Price>
  </cac:InvoiceLine>
  <cac:InvoiceLine>
    <cbc:ID>558</cbc:ID>
    <cbc:InvoicedQuantity unitCode="C62">10.0</cbc:InvoicedQuantity>
    <cbc:LineExtensionAmount currencyID="USD">1000.00</cbc:LineExtensionAmount>
    <cac:Item>
      <cbc:Description>product_b</cbc:Description>
      <cbc:Name>product_b</cbc:Name>
      <cac:ClassifiedTaxCategory>
        <cbc:ID>S</cbc:ID>
        <cbc:Percent>10.0</cbc:Percent>
        <cac:TaxScheme>
          <cbc:ID>GST</cbc:ID>
        </cac:TaxScheme>
      </cac:ClassifiedTaxCategory>
    </cac:Item>
    <cac:Price>
      <cbc:PriceAmount currencyID="USD">100.0</cbc:PriceAmount>
    </cac:Price>
  </cac:InvoiceLine>
  <cac:InvoiceLine>
    <cbc:ID>559</cbc:ID>
    <cbc:InvoicedQuantity unitCode="C62">-1.0</cbc:InvoicedQuantity>
    <cbc:LineExtensionAmount currencyID="USD">-100.00</cbc:LineExtensionAmount>
    <cac:Item>
      <cbc:Description>product_b</cbc:Description>
      <cbc:Name>product_b</cbc:Name>
      <cac:ClassifiedTaxCategory>
        <cbc:ID>S</cbc:ID>
        <cbc:Percent>10.0</cbc:Percent>
        <cac:TaxScheme>
          <cbc:ID>GST</cbc:ID>
        </cac:TaxScheme>
      </cac:ClassifiedTaxCategory>
    </cac:Item>
    <cac:Price>
      <cbc:PriceAmount currencyID="USD">100.0</cbc:PriceAmount>
    </cac:Price>
  </cac:InvoiceLine>
</Invoice>]]></cbc:Description>
        </cac:ExternalReference>
    </cac:Attachment>
</AttachedDocument>

