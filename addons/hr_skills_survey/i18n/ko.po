# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_skills_survey
# 
# Translators:
# <PERSON>, 2024
# Wil <PERSON>, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:55+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: Korean (https://app.transifex.com/odoo/teams/41243/ko/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ko\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: hr_skills_survey
#: model:hr.resume.line,name:hr_skills_survey.resume_line_valid
msgid "AWS Cloud"
msgstr "AWS 클라우드"

#. module: hr_skills_survey
#: model:ir.model.fields,field_description:hr_skills_survey.field_hr_resume_line__survey_id
#: model:ir.model.fields.selection,name:hr_skills_survey.selection__hr_resume_line__display_type__certification
#: model_terms:ir.ui.view,arch_db:hr_skills_survey.hr_resume_line_view_search
msgid "Certification"
msgstr "인증서"

#. module: hr_skills_survey
#: model:ir.ui.menu,name:hr_skills_survey.hr_employee_certication_report_menu
msgid "Certifications"
msgstr "인증"

#. module: hr_skills_survey
#: model:ir.model.fields,field_description:hr_skills_survey.field_hr_resume_line__department_id
#: model_terms:ir.ui.view,arch_db:hr_skills_survey.hr_resume_line_view_search
msgid "Department"
msgstr "부서"

#. module: hr_skills_survey
#: model:ir.model.fields,field_description:hr_skills_survey.field_hr_resume_line__display_type
msgid "Display Type"
msgstr "표시 유형"

#. module: hr_skills_survey
#: model_terms:ir.ui.view,arch_db:hr_skills_survey.hr_resume_line_view_search
msgid "Employee"
msgstr "직원"

#. module: hr_skills_survey
#: model:ir.actions.act_window,name:hr_skills_survey.hr_employee_certification_report_action
msgid "Employee Certifications"
msgstr "직원 인증서"

#. module: hr_skills_survey
#: model:ir.model.fields,field_description:hr_skills_survey.field_hr_resume_line__expiration_status
msgid "Expiration Status"
msgstr "만료 상태"

#. module: hr_skills_survey
#: model_terms:ir.ui.view,arch_db:hr_skills_survey.hr_resume_line_view_search
msgid "Expiration date"
msgstr "만료 일자"

#. module: hr_skills_survey
#: model:ir.model.fields.selection,name:hr_skills_survey.selection__hr_resume_line__expiration_status__expired
#: model_terms:ir.ui.view,arch_db:hr_skills_survey.hr_resume_line_view_search
msgid "Expired"
msgstr "만료됨"

#. module: hr_skills_survey
#: model:ir.model.fields.selection,name:hr_skills_survey.selection__hr_resume_line__expiration_status__expiring
msgid "Expiring"
msgstr "만료 예정"

#. module: hr_skills_survey
#: model_terms:ir.ui.view,arch_db:hr_skills_survey.hr_resume_line_view_search
msgid "Expiring Soon"
msgstr "곧 만료"

#. module: hr_skills_survey
#: model:hr.resume.line.type,name:hr_skills_survey.resume_type_certification
msgid "Internal Certification"
msgstr "사내 인증"

#. module: hr_skills_survey
#: model:hr.resume.line,name:hr_skills_survey.resume_line_expiring
msgid "MongoDB Developer"
msgstr "MongoDB 개발자"

#. module: hr_skills_survey
#: model:hr.resume.line,name:hr_skills_survey.resume_line_aws
msgid "Oracle DB"
msgstr "Oracle DB"

#. module: hr_skills_survey
#: model:ir.model,name:hr_skills_survey.model_hr_resume_line
msgid "Resume line of an employee"
msgstr "직원의 경력 사항 명세"

#. module: hr_skills_survey
#: model:ir.model.fields,help:hr_skills_survey.field_survey_survey__certification_validity_months
msgid ""
"Specify the number of months the certification is valid after being awarded."
" Enter 0 for certifications that never expire."
msgstr "인증서 발급 후 유효 기간(개월)을 지정합니다. 만료 기간이 없는 인증서는 0을 입력합니다."

#. module: hr_skills_survey
#: model:ir.model,name:hr_skills_survey.model_survey_survey
msgid "Survey"
msgstr "설문 조사"

#. module: hr_skills_survey
#: model:ir.model,name:hr_skills_survey.model_survey_user_input
msgid "Survey User Input"
msgstr "설문 조사 사용자 입력"

#. module: hr_skills_survey
#: model:ir.model.fields.selection,name:hr_skills_survey.selection__hr_resume_line__expiration_status__valid
msgid "Valid"
msgstr "유효"

#. module: hr_skills_survey
#: model_terms:ir.ui.view,arch_db:hr_skills_survey.hr_resume_line_view_search
msgid "Valid Until"
msgstr "유효기간"

#. module: hr_skills_survey
#: model:ir.model.fields,field_description:hr_skills_survey.field_survey_survey__certification_validity_months
msgid "Validity"
msgstr "유효함"

#. module: hr_skills_survey
#: model_terms:ir.ui.view,arch_db:hr_skills_survey.hr_employee_certification_report_view_list
msgid "Validity End"
msgstr "유효 기간 종료"

#. module: hr_skills_survey
#: model_terms:ir.ui.view,arch_db:hr_skills_survey.hr_employee_certification_report_view_list
msgid "Validity Start"
msgstr "유효 기간 시작"
