# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* payment_buckaroo
# 
# Translators:
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:56+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Polish (https://app.transifex.com/odoo/teams/41243/pl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: pl\n"
"Plural-Forms: nplurals=4; plural=(n==1 ? 0 : (n%10>=2 && n%10<=4) && (n%100<12 || n%100>14) ? 1 : n!=1 && (n%10>=0 && n%10<=1) || (n%10>=5 && n%10<=9) || (n%100>=12 && n%100<=14) ? 2 : 3);\n"

#. module: payment_buckaroo
#. odoo-python
#: code:addons/payment_buckaroo/models/payment_transaction.py:0
msgid ""
"An error occurred during processing of your payment (code %s). Please try "
"again."
msgstr ""

#. module: payment_buckaroo
#: model:ir.model.fields.selection,name:payment_buckaroo.selection__payment_provider__code__buckaroo
msgid "Buckaroo"
msgstr "Buckaroo"

#. module: payment_buckaroo
#: model:ir.model.fields,field_description:payment_buckaroo.field_payment_provider__buckaroo_secret_key
msgid "Buckaroo Secret Key"
msgstr ""

#. module: payment_buckaroo
#: model:ir.model.fields,field_description:payment_buckaroo.field_payment_provider__code
msgid "Code"
msgstr "Kod"

#. module: payment_buckaroo
#. odoo-python
#: code:addons/payment_buckaroo/models/payment_transaction.py:0
msgid "No transaction found matching reference %s."
msgstr "Nie znaleziono transakcji pasującej do referencji %s."

#. module: payment_buckaroo
#: model:ir.model,name:payment_buckaroo.model_payment_provider
msgid "Payment Provider"
msgstr "Dostawca Płatności"

#. module: payment_buckaroo
#: model:ir.model,name:payment_buckaroo.model_payment_transaction
msgid "Payment Transaction"
msgstr "Transakcja płatności"

#. module: payment_buckaroo
#. odoo-python
#: code:addons/payment_buckaroo/models/payment_transaction.py:0
msgid "Received data with missing transaction keys"
msgstr ""

#. module: payment_buckaroo
#: model_terms:ir.ui.view,arch_db:payment_buckaroo.payment_provider_form
msgid "Secret Key"
msgstr "Sekretny klucz"

#. module: payment_buckaroo
#: model:ir.model.fields,help:payment_buckaroo.field_payment_provider__buckaroo_website_key
msgid "The key solely used to identify the website with Buckaroo"
msgstr ""

#. module: payment_buckaroo
#: model:ir.model.fields,help:payment_buckaroo.field_payment_provider__code
msgid "The technical code of this payment provider."
msgstr "Kod techniczny tego dostawcy usług płatniczych."

#. module: payment_buckaroo
#. odoo-python
#: code:addons/payment_buckaroo/models/payment_transaction.py:0
msgid "Unknown status code: %s"
msgstr ""

#. module: payment_buckaroo
#: model:ir.model.fields,field_description:payment_buckaroo.field_payment_provider__buckaroo_website_key
msgid "Website Key"
msgstr ""

#. module: payment_buckaroo
#. odoo-python
#: code:addons/payment_buckaroo/models/payment_transaction.py:0
msgid "Your payment was refused (code %s). Please try again."
msgstr ""
