# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* pos_viva_wallet
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-01-27 13:03+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON>, 2025\n"
"Language-Team: Russian (https://app.transifex.com/odoo/teams/41243/ru/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ru\n"
"Plural-Forms: nplurals=4; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<12 || n%100>14) ? 1 : n%10==0 || (n%10>=5 && n%10<=9) || (n%100>=11 && n%100<=14)? 2 : 3);\n"

#. module: pos_viva_wallet
#: model:ir.model.fields,field_description:pos_viva_wallet.field_pos_payment_method__viva_wallet_api_key
msgid "API Key"
msgstr "Ключ API"

#. module: pos_viva_wallet
#. odoo-python
#: code:addons/pos_viva_wallet/models/pos_payment_method.py:0
msgid "Can't create payment method. Please check the data and update it."
msgstr ""

#. module: pos_viva_wallet
#. odoo-python
#: code:addons/pos_viva_wallet/models/pos_payment_method.py:0
msgid "Can't update payment method. Please check the data and update it."
msgstr ""

#. module: pos_viva_wallet
#. odoo-javascript
#: code:addons/pos_viva_wallet/static/src/app/payment_viva_wallet.js:0
msgid "Cannot process transactions with negative amount."
msgstr "Невозможно обработать транзакции с отрицательной суммой."

#. module: pos_viva_wallet
#: model:ir.model.fields,field_description:pos_viva_wallet.field_pos_payment_method__viva_wallet_client_id
msgid "Client ID"
msgstr "ID клиента"

#. module: pos_viva_wallet
#: model:ir.model.fields,field_description:pos_viva_wallet.field_pos_payment_method__viva_wallet_client_secret
msgid "Client secret"
msgstr "Секретный ключ клиента"

#. module: pos_viva_wallet
#. odoo-javascript
#: code:addons/pos_viva_wallet/static/src/app/payment_viva_wallet.js:0
msgid ""
"Could not connect to the Odoo server, please check your internet connection "
"and try again."
msgstr ""
"Не удалось подключиться к серверу Odoo, пожалуйста, проверьте подключение к "
"Интернету и повторите попытку."

#. module: pos_viva_wallet
#. odoo-python
#: code:addons/pos_viva_wallet/models/pos_payment_method.py:0
msgid "It is essential to provide API key for the use of viva wallet"
msgstr "Для использования кошелька viva необходимо предоставить API-ключ"

#. module: pos_viva_wallet
#: model:ir.model.fields,field_description:pos_viva_wallet.field_pos_payment_method__viva_wallet_merchant_id
msgid "Merchant ID"
msgstr "ID продавца"

#. module: pos_viva_wallet
#. odoo-javascript
#: code:addons/pos_viva_wallet/static/src/app/payment_viva_wallet.js:0
msgid "Message from Viva Wallet: %s"
msgstr "Сообщение от Viva Wallet: %s"

#. module: pos_viva_wallet
#. odoo-python
#: code:addons/pos_viva_wallet/models/pos_payment_method.py:0
msgid "Only 'group_pos_user' are allowed to cancel a Viva Wallet payment"
msgstr ""

#. module: pos_viva_wallet
#. odoo-python
#: code:addons/pos_viva_wallet/models/pos_payment_method.py:0
msgid "Only 'group_pos_user' are allowed to get latest transaction status"
msgstr ""
"Только 'group_pos_user' имеют право получать последний статус транзакции"

#. module: pos_viva_wallet
#. odoo-python
#: code:addons/pos_viva_wallet/models/pos_payment_method.py:0
msgid ""
"Only 'group_pos_user' are allowed to get the payment status from Viva Wallet"
msgstr ""

#. module: pos_viva_wallet
#. odoo-python
#: code:addons/pos_viva_wallet/models/pos_payment_method.py:0
msgid ""
"Only 'group_pos_user' are allowed to send a Viva Wallet payment request"
msgstr ""

#. module: pos_viva_wallet
#: model:ir.model,name:pos_viva_wallet.model_pos_payment_method
msgid "Point of Sale Payment Methods"
msgstr "Способы оплаты в торговых точках"

#. module: pos_viva_wallet
#: model:ir.model.fields,help:pos_viva_wallet.field_pos_payment_method__viva_wallet_test_mode
msgid "Run transactions in the test environment."
msgstr "Выполните транзакции в тестовой среде."

#. module: pos_viva_wallet
#: model:ir.model.fields,field_description:pos_viva_wallet.field_pos_payment_method__viva_wallet_terminal_id
msgid "Terminal ID"
msgstr "Идентификатор терминала"

#. module: pos_viva_wallet
#: model:ir.model.fields,field_description:pos_viva_wallet.field_pos_payment_method__viva_wallet_test_mode
msgid "Test mode"
msgstr "Тестовый режим"

#. module: pos_viva_wallet
#. odoo-python
#: code:addons/pos_viva_wallet/models/pos_payment_method.py:0
msgid "There are some issues between us and Viva Wallet, try again later. %s"
msgstr ""
"Между нами и Viva Wallet возникли некоторые проблемы, повторите попытку "
"позже. %s"

#. module: pos_viva_wallet
#. odoo-python
#: code:addons/pos_viva_wallet/models/pos_payment_method.py:0
msgid "There are some issues between us and Viva Wallet, try again later.%s)"
msgstr ""
"Между нами и Viva Wallet возникли некоторые проблемы, повторите попытку "
"позже.%s)"

#. module: pos_viva_wallet
#. odoo-python
#: code:addons/pos_viva_wallet/models/pos_payment_method.py:0
msgid ""
"Unable to retrieve Viva Wallet Bearer Token: Please verify that the Client "
"ID and Client Secret are correct"
msgstr ""

#. module: pos_viva_wallet
#: model:ir.model.fields,help:pos_viva_wallet.field_pos_payment_method__viva_wallet_api_key
#: model:ir.model.fields,help:pos_viva_wallet.field_pos_payment_method__viva_wallet_merchant_id
msgid ""
"Used when connecting to Viva Wallet: "
"https://developer.vivawallet.com/getting-started/find-your-account-"
"credentials/merchant-id-and-api-key/"
msgstr ""
"Используется при подключении к Viva Wallet: "
"https://developer.vivawallet.com/getting-started/find-your-account-"
"credentials/merchant-id-and-api-key/"

#. module: pos_viva_wallet
#: model:ir.model.fields,help:pos_viva_wallet.field_pos_payment_method__viva_wallet_client_id
msgid ""
"Used when connecting to Viva Wallet: "
"https://developer.vivawallet.com/getting-started/find-your-account-"
"credentials/pos-apis-credentials/#find-your-pos-apis-credentials"
msgstr ""
"Используется при подключении к Viva Wallet: "
"https://developer.vivawallet.com/getting-started/find-your-account-"
"credentials/pos-apis-credentials/#find-your-pos-apis-credentials"

#. module: pos_viva_wallet
#: model:ir.model.fields,field_description:pos_viva_wallet.field_pos_payment_method__viva_wallet_bearer_token
msgid "Viva Wallet Bearer Token"
msgstr "Токен-носитель кошелька Viva"

#. module: pos_viva_wallet
#. odoo-javascript
#: code:addons/pos_viva_wallet/static/src/app/payment_viva_wallet.js:0
msgid "Viva Wallet Error"
msgstr "Ошибка кошелька Viva"

#. module: pos_viva_wallet
#: model:ir.model.fields,field_description:pos_viva_wallet.field_pos_payment_method__viva_wallet_latest_response
msgid "Viva Wallet Latest Response"
msgstr "Последний ответ от Viva Wallet"

#. module: pos_viva_wallet
#: model:ir.model.fields,field_description:pos_viva_wallet.field_pos_payment_method__viva_wallet_webhook_endpoint
msgid "Viva Wallet Webhook Endpoint"
msgstr "Конечная точка Viva Wallet Webhook"

#. module: pos_viva_wallet
#: model:ir.model.fields,field_description:pos_viva_wallet.field_pos_payment_method__viva_wallet_webhook_verification_key
msgid "Viva Wallet Webhook Verification Key"
msgstr "Верификационный ключ Viva Wallet Webhook"

#. module: pos_viva_wallet
#. odoo-python
#: code:addons/pos_viva_wallet/models/pos_payment_method.py:0
msgid "Your transaction with Viva Wallet failed. Please try again later."
msgstr ""
"Ваша транзакция с Viva Wallet не удалась. Пожалуйста, попробуйте позже."

#. module: pos_viva_wallet
#: model:ir.model.fields,help:pos_viva_wallet.field_pos_payment_method__viva_wallet_terminal_id
msgid "[Terminal ID of the Viva Wallet terminal], for example: 16002169"
msgstr "[ID терминала Viva Wallet], например: 16002169"

#. module: pos_viva_wallet
#. odoo-python
#: code:addons/pos_viva_wallet/controllers/main.py:0
msgid "received a message for a pos payment provider not registered."
msgstr "получено сообщение о том, что поставщик платежей не зарегистрирован."

#. module: pos_viva_wallet
#. odoo-python
#: code:addons/pos_viva_wallet/controllers/main.py:0
msgid "received a message for a terminal not registered in Odoo: %s"
msgstr "получено сообщение для терминала, не зарегистрированного в Odoo: %s"
