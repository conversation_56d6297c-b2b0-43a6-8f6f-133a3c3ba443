<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record model="ir.module.category" id="base.module_category_human_resources_expenses">
        <field name="description">Helps you manage your expenses.</field>
        <field name="sequence">12</field>
    </record>

    <record id="group_hr_expense_team_approver" model="res.groups">
        <field name="name">Team Approver</field>
        <field name="category_id" ref="base.module_category_human_resources_expenses"/>
        <field name="implied_ids" eval="[(4, ref('base.group_user'))]"/>
    </record>

    <record id="group_hr_expense_user" model="res.groups">
        <field name="name">All Approver</field>
        <field name="category_id" ref="base.module_category_human_resources_expenses"/>
        <field name="implied_ids" eval="[(4, ref('hr_expense.group_hr_expense_team_approver'))]"/>
    </record>

    <record id="group_hr_expense_manager" model="res.groups">
        <field name="name">Administrator</field>
        <field name="category_id" ref="base.module_category_human_resources_expenses"/>
        <field name="implied_ids" eval="[(4, ref('hr_expense.group_hr_expense_user'))]"/>
        <field name="users" eval="[(4, ref('base.user_root')), (4, ref('base.user_admin'))]"/>
    </record>

    <record id="base.default_user" model="res.users">
        <field name="groups_id" eval="[(4,ref('hr_expense.group_hr_expense_manager'))]"/>
    </record>
</odoo>
