# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* web_hierarchy
# 
# Translators:
# <AUTHOR> <EMAIL>, 2024
# <PERSON>, 2024
# <PERSON><PERSON><PERSON><PERSON> <baskhuu<PERSON><EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:56+0000\n"
"PO-Revision-Date: 2024-09-25 09:42+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Mongolian (https://app.transifex.com/odoo/teams/41243/mn/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: mn\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: web_hierarchy
#: model:ir.model,name:web_hierarchy.model_ir_actions_act_window_view
msgid "Action Window View"
msgstr "Үйлдлийн цонх харагдац"

#. module: web_hierarchy
#: model:ir.model,name:web_hierarchy.model_base
msgid "Base"
msgstr "Суурь"

#. module: web_hierarchy
#. odoo-javascript
#: code:addons/web_hierarchy/static/src/hierarchy_model.js:0
msgid "Cannot change the parent because it will cause a cyclic."
msgstr ""

#. module: web_hierarchy
#. odoo-javascript
#: code:addons/web_hierarchy/static/src/hierarchy_card.xml:0
msgid "Fold"
msgstr "Эвхэх"

#. module: web_hierarchy
#: model:ir.model.fields.selection,name:web_hierarchy.selection__ir_actions_act_window_view__view_mode__hierarchy
#: model:ir.model.fields.selection,name:web_hierarchy.selection__ir_ui_view__type__hierarchy
msgid "Hierarchy"
msgstr "Шатлал"

#. module: web_hierarchy
#. odoo-python
#: code:addons/web_hierarchy/models/ir_ui_view.py:0
msgid "Hierarchy child can only be field or template, got %s"
msgstr ""

#. module: web_hierarchy
#. odoo-python
#: code:addons/web_hierarchy/models/ir_ui_view.py:0
msgid "Hierarchy view can contain only one templates tag"
msgstr ""

#. module: web_hierarchy
#. odoo-javascript
#: code:addons/web_hierarchy/static/src/hierarchy_renderer.js:0
msgid ""
"Impossible to update the parent node of the dragged node because no parent "
"has been found."
msgstr ""

#. module: web_hierarchy
#. odoo-python
#: code:addons/web_hierarchy/models/ir_ui_view.py:0
msgid ""
"Invalid attributes (%(invalid_attributes)s) in hierarchy view. Attributes "
"must be in (%(valid_attributes)s)"
msgstr ""

#. module: web_hierarchy
#. odoo-javascript
#: code:addons/web_hierarchy/static/src/hierarchy_controller.xml:0
msgid "New"
msgstr "Шинэ"

#. module: web_hierarchy
#. odoo-javascript
#: code:addons/web_hierarchy/static/src/hierarchy_model.js:0
msgid ""
"The parent of \"%s\" was successfully updated. Reloading records to account "
"for other changes."
msgstr ""

#. module: web_hierarchy
#. odoo-javascript
#: code:addons/web_hierarchy/static/src/hierarchy_model.js:0
msgid "The parent record cannot be the record dragged."
msgstr ""

#. module: web_hierarchy
#. odoo-javascript
#: code:addons/web_hierarchy/static/src/hierarchy_card.xml:0
msgid "Unfold"
msgstr "Дэлгэх"

#. module: web_hierarchy
#: model:ir.model,name:web_hierarchy.model_ir_ui_view
msgid "View"
msgstr "Харах"

#. module: web_hierarchy
#: model:ir.model.fields,field_description:web_hierarchy.field_ir_actions_act_window_view__view_mode
#: model:ir.model.fields,field_description:web_hierarchy.field_ir_ui_view__type
msgid "View Type"
msgstr "Дэлгэцийн төрөл"
