<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record id="view_account_journal_form" model="ir.ui.view">
            <field name="model">account.journal</field>
            <field name="name">account.journal.form</field>
            <field name="inherit_id" ref="l10n_latam_invoice_document.view_account_journal_form"/>
            <field name="arch" type="xml">
                <field name="l10n_latam_use_documents" position="after">
                    <field name="l10n_ec_require_emission" invisible="1"/>
                    <field name="l10n_ec_entity"
                           placeholder="001"
                           invisible="not l10n_ec_require_emission"
                           required="l10n_ec_require_emission"/>
                    <field name="l10n_ec_emission"
                           placeholder="001"
                           invisible="not l10n_ec_require_emission"
                           required="l10n_ec_require_emission"/>
                    <field name="l10n_ec_emission_address_id"
                           invisible="not l10n_ec_require_emission"
                           required="l10n_ec_require_emission"
                           context="{'default_parent_id': company_partner_id, 
                                     'default_type': 'invoice',
                                     'form_view_ref': 'base.view_partner_address_form'
                                     }"/>
                </field>
            </field>
        </record>
    </data>
</odoo>
