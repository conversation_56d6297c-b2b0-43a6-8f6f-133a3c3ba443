# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* purchase_mrp
# 
# Translators:
# <PERSON><PERSON><PERSON>, 2024
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:56+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Romanian (https://app.transifex.com/odoo/teams/41243/ro/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ro\n"
"Plural-Forms: nplurals=3; plural=(n==1?0:(((n%100>19)||((n%100==0)&&(n!=0)))?2:1));\n"

#. module: purchase_mrp
#: model_terms:ir.ui.view,arch_db:purchase_mrp.purchase_order_form_mrp
msgid "<span class=\"o_stat_text\">Manufacturing</span>"
msgstr "<span class=\"o_stat_text\">Producție</span>"

#. module: purchase_mrp
#: model_terms:ir.ui.view,arch_db:purchase_mrp.mrp_production_form_view_purchase
msgid "<span class=\"o_stat_text\">Purchases</span>"
msgstr "<span class=\"o_stat_text\">Achiziții</span>"

#. module: purchase_mrp
#: model:ir.model,name:purchase_mrp.model_report_mrp_report_bom_structure
msgid "BOM Overview Report"
msgstr "Raport general LDM"

#. module: purchase_mrp
#: model:ir.model,name:purchase_mrp.model_mrp_bom
msgid "Bill of Material"
msgstr "Listă de materiale"

#. module: purchase_mrp
#: model:ir.model,name:purchase_mrp.model_mrp_bom_line
msgid "Bill of Material Line"
msgstr "Linie Factură Materiale"

#. module: purchase_mrp
#. odoo-python
#: code:addons/purchase_mrp/models/mrp_bom.py:0
msgid "Components cost share have to be positive or equals to zero."
msgstr ""
"Partajarea costurilor componentelor trebuie să fie pozitivă sau egală cu "
"zero."

#. module: purchase_mrp
#: model:ir.model.fields,field_description:purchase_mrp.field_mrp_bom_line__cost_share
msgid "Cost Share (%)"
msgstr "Partajare cost (%)"

#. module: purchase_mrp
#: model:ir.model.fields,field_description:purchase_mrp.field_purchase_order__mrp_production_count
msgid "Count of MO Source"
msgstr "Număr sursă CP"

#. module: purchase_mrp
#: model:ir.model.fields,field_description:purchase_mrp.field_mrp_production__purchase_order_count
msgid "Count of generated PO"
msgstr "Număr de CA generate"

#. module: purchase_mrp
#: model:ir.model,name:purchase_mrp.model_account_move_line
msgid "Journal Item"
msgstr "Element de jurnal"

#. module: purchase_mrp
#: model:ir.model,name:purchase_mrp.model_report_mrp_report_mo_overview
msgid "MO Overview Report"
msgstr "Raport general CP"

#. module: purchase_mrp
#: model:ir.model,name:purchase_mrp.model_mrp_production
msgid "Manufacturing Order"
msgstr "Comanda de Producție"

#. module: purchase_mrp
#. odoo-python
#: code:addons/purchase_mrp/models/purchase.py:0
msgid "Manufacturing Source of %s"
msgstr "Sursă producție pentru %s"

#. module: purchase_mrp
#. odoo-python
#: code:addons/purchase_mrp/models/stock_move.py:0
msgid ""
"Odoo is not able to generate the anglo saxon entries. The total valuation of"
" %s is zero."
msgstr ""
"Odoo nu este capabil să genereze intrări anglo saxon. Evaluarea totală a%s "
"este 0."

#. module: purchase_mrp
#: model:ir.model,name:purchase_mrp.model_purchase_order
msgid "Purchase Order"
msgstr "Comandă de achiziție"

#. module: purchase_mrp
#: model:ir.model,name:purchase_mrp.model_purchase_order_line
msgid "Purchase Order Line"
msgstr "Linie comandă de achiziție"

#. module: purchase_mrp
#. odoo-python
#: code:addons/purchase_mrp/models/mrp_production.py:0
msgid "Purchase Order generated from %s"
msgstr "Comandă de achiziție generată din %s "

#. module: purchase_mrp
#: model:ir.model,name:purchase_mrp.model_stock_move
msgid "Stock Move"
msgstr "Mișcare de stoc"

#. module: purchase_mrp
#: model:ir.model.fields,help:purchase_mrp.field_mrp_bom_line__cost_share
msgid ""
"The percentage of the component repartition cost when purchasing a kit.The "
"total of all components' cost have to be equal to 100."
msgstr ""
"Procentajul costului de repartizare a componentei la achiziționarea unui "
"kit.Totalul costurilor tuturor componentelor trebuie să fie egal cu 100."

#. module: purchase_mrp
#. odoo-python
#: code:addons/purchase_mrp/models/mrp_bom.py:0
msgid "The total cost share for a BoM's component have to be 100"
msgstr "Costul total al componentei unei LDM trebuie să fie 100"
