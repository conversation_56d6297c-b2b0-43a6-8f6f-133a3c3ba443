# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* purchase_mrp
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:56+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Language-Team: Kurdish (https://app.transifex.com/odoo/teams/41243/ku/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ku\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: purchase_mrp
#: model_terms:ir.ui.view,arch_db:purchase_mrp.purchase_order_form_mrp
msgid "<span class=\"o_stat_text\">Manufacturing</span>"
msgstr ""

#. module: purchase_mrp
#: model_terms:ir.ui.view,arch_db:purchase_mrp.mrp_production_form_view_purchase
msgid "<span class=\"o_stat_text\">Purchases</span>"
msgstr ""

#. module: purchase_mrp
#: model:ir.model,name:purchase_mrp.model_report_mrp_report_bom_structure
msgid "BOM Overview Report"
msgstr ""

#. module: purchase_mrp
#: model:ir.model,name:purchase_mrp.model_mrp_bom
msgid "Bill of Material"
msgstr ""

#. module: purchase_mrp
#: model:ir.model,name:purchase_mrp.model_mrp_bom_line
msgid "Bill of Material Line"
msgstr ""

#. module: purchase_mrp
#. odoo-python
#: code:addons/purchase_mrp/models/mrp_bom.py:0
msgid "Components cost share have to be positive or equals to zero."
msgstr ""

#. module: purchase_mrp
#: model:ir.model.fields,field_description:purchase_mrp.field_mrp_bom_line__cost_share
msgid "Cost Share (%)"
msgstr ""

#. module: purchase_mrp
#: model:ir.model.fields,field_description:purchase_mrp.field_purchase_order__mrp_production_count
msgid "Count of MO Source"
msgstr ""

#. module: purchase_mrp
#: model:ir.model.fields,field_description:purchase_mrp.field_mrp_production__purchase_order_count
msgid "Count of generated PO"
msgstr ""

#. module: purchase_mrp
#: model:ir.model,name:purchase_mrp.model_account_move_line
msgid "Journal Item"
msgstr ""

#. module: purchase_mrp
#: model:ir.model,name:purchase_mrp.model_report_mrp_report_mo_overview
msgid "MO Overview Report"
msgstr ""

#. module: purchase_mrp
#: model:ir.model,name:purchase_mrp.model_mrp_production
msgid "Manufacturing Order"
msgstr ""

#. module: purchase_mrp
#. odoo-python
#: code:addons/purchase_mrp/models/purchase.py:0
msgid "Manufacturing Source of %s"
msgstr ""

#. module: purchase_mrp
#. odoo-python
#: code:addons/purchase_mrp/models/stock_move.py:0
msgid ""
"Odoo is not able to generate the anglo saxon entries. The total valuation of"
" %s is zero."
msgstr ""

#. module: purchase_mrp
#: model:ir.model,name:purchase_mrp.model_purchase_order
msgid "Purchase Order"
msgstr ""

#. module: purchase_mrp
#: model:ir.model,name:purchase_mrp.model_purchase_order_line
msgid "Purchase Order Line"
msgstr ""

#. module: purchase_mrp
#. odoo-python
#: code:addons/purchase_mrp/models/mrp_production.py:0
msgid "Purchase Order generated from %s"
msgstr ""

#. module: purchase_mrp
#: model:ir.model,name:purchase_mrp.model_stock_move
msgid "Stock Move"
msgstr ""

#. module: purchase_mrp
#: model:ir.model.fields,help:purchase_mrp.field_mrp_bom_line__cost_share
msgid ""
"The percentage of the component repartition cost when purchasing a kit.The "
"total of all components' cost have to be equal to 100."
msgstr ""

#. module: purchase_mrp
#. odoo-python
#: code:addons/purchase_mrp/models/mrp_bom.py:0
msgid "The total cost share for a BoM's component have to be 100"
msgstr ""
