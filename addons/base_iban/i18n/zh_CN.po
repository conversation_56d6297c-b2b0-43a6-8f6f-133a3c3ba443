# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* base_iban
# 
# Translators:
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:55+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Chinese (China) (https://app.transifex.com/odoo/teams/41243/zh_CN/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_CN\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: base_iban
#. odoo-javascript
#: code:addons/base_iban/static/src/components/iban_widget/iban_widget.xml:0
msgid "Account isn't a valid IBAN"
msgstr "账户不是有效的IBAN"

#. module: base_iban
#: model:ir.model,name:base_iban.model_res_partner_bank
msgid "Bank Accounts"
msgstr "银行账户"

#. module: base_iban
#. odoo-python
#: code:addons/base_iban/models/res_partner_bank.py:0
msgid "Cannot compute the BBAN because the account number is not an IBAN."
msgstr "不能计算基本银行账号号码因为该账号不是国际银行账号号码。"

#. module: base_iban
#. odoo-python
#: code:addons/base_iban/models/res_partner_bank.py:0
msgid "IBAN"
msgstr "IBAN"

#. module: base_iban
#. odoo-python
#: code:addons/base_iban/models/res_partner_bank.py:0
msgid ""
"The IBAN does not seem to be correct. You should have entered something like this %s\n"
"Where B = National bank code, S = Branch code, C = Account No, k = Check digit"
msgstr ""
"国际银行账号号码看上去不对，您应该输入类似以下%s\n"
"B=国家银行代码，S=分行代码，C=账户号，K=检验位"

#. module: base_iban
#. odoo-python
#: code:addons/base_iban/models/res_partner_bank.py:0
msgid "The IBAN is invalid, it should begin with the country code"
msgstr "IBAN代码不正确，应该以国家代码开头"

#. module: base_iban
#. odoo-python
#: code:addons/base_iban/models/res_partner_bank.py:0
msgid "There is no IBAN code."
msgstr "这是 IBAN 代码."

#. module: base_iban
#. odoo-python
#: code:addons/base_iban/models/res_partner_bank.py:0
msgid "This IBAN does not pass the validation check, please verify it."
msgstr "国际银行账号号码没有通过校验，请改正。"
