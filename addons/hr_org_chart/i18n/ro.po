# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_org_chart
# 
# Translators:
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:55+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Romanian (https://app.transifex.com/odoo/teams/41243/ro/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ro\n"
"Plural-Forms: nplurals=3; plural=(n==1?0:(((n%100>19)||((n%100==0)&&(n!=0)))?2:1));\n"

#. module: hr_org_chart
#: model_terms:ir.ui.view,arch_db:hr_org_chart.hr_employee_view_form_inherit_org_chart
msgid "<span class=\"o_stat_text\">Org Chart</span>"
msgstr "<span class=\"o_stat_text\">Org Chart</span>"

#. module: hr_org_chart
#: model_terms:ir.actions.act_window,help:hr_org_chart.action_hr_employee_org_chart
msgid "Add a new employee"
msgstr "Adăugare angajat nou"

#. module: hr_org_chart
#: model:ir.model,name:hr_org_chart.model_hr_employee_base
msgid "Basic Employee"
msgstr "BAngajat de bază"

#. module: hr_org_chart
#: model:ir.model.fields,field_description:hr_org_chart.field_hr_employee__department_color
#: model:ir.model.fields,field_description:hr_org_chart.field_hr_employee_base__department_color
#: model:ir.model.fields,field_description:hr_org_chart.field_hr_employee_public__department_color
msgid "Department Color"
msgstr "Culoare departament"

#. module: hr_org_chart
#: model:ir.model.fields,field_description:hr_org_chart.field_hr_employee__child_count
#: model:ir.model.fields,field_description:hr_org_chart.field_hr_employee_base__child_count
#: model:ir.model.fields,field_description:hr_org_chart.field_hr_employee_public__child_count
msgid "Direct Subordinates Count"
msgstr "Număr de subordonați direcți"

#. module: hr_org_chart
#: model:ir.model.fields,help:hr_org_chart.field_hr_employee__subordinate_ids
#: model:ir.model.fields,help:hr_org_chart.field_hr_employee_public__subordinate_ids
msgid "Direct and indirect subordinates"
msgstr "Subordonați direcți și indirecți"

#. module: hr_org_chart
#. odoo-javascript
#: code:addons/hr_org_chart/static/src/fields/hr_org_chart.xml:0
msgid "Direct subordinates"
msgstr "Subordonați direcți"

#. module: hr_org_chart
#: model:ir.model,name:hr_org_chart.model_hr_employee
msgid "Employee"
msgstr "Angajat"

#. module: hr_org_chart
#: model_terms:ir.ui.view,arch_db:hr_org_chart.hr_department_hierarchy_view
msgid "Employees"
msgstr "Angajați"

#. module: hr_org_chart
#. odoo-javascript
#: code:addons/hr_org_chart/static/src/fields/hr_org_chart.xml:0
msgid "In order to get an organigram, set a manager and save the record."
msgstr ""
"Pentru a obține o organigramă, setați un manager și salvați înregistrarea."

#. module: hr_org_chart
#. odoo-javascript
#: code:addons/hr_org_chart/static/src/views/hr_employee_hierarchy/hr_employee_hierarchy_controller.xml:0
msgid ""
"In the Organigram you will have a clear overview of the hierarchy of "
"employees."
msgstr ""

#. module: hr_org_chart
#: model:ir.model.fields,field_description:hr_org_chart.field_hr_employee__child_all_count
#: model:ir.model.fields,field_description:hr_org_chart.field_hr_employee_base__child_all_count
#: model:ir.model.fields,field_description:hr_org_chart.field_hr_employee_public__child_all_count
msgid "Indirect Subordinates Count"
msgstr "Număr de subordonați indirecți"

#. module: hr_org_chart
#. odoo-javascript
#: code:addons/hr_org_chart/static/src/fields/hr_org_chart.xml:0
msgid "Indirect subordinates"
msgstr "Subordonați indirecți"

#. module: hr_org_chart
#: model:ir.model.fields,field_description:hr_org_chart.field_hr_employee__is_subordinate
#: model:ir.model.fields,field_description:hr_org_chart.field_hr_employee_public__is_subordinate
msgid "Is Subordinate"
msgstr "Este subordonat"

#. module: hr_org_chart
#. odoo-javascript
#: code:addons/hr_org_chart/static/src/fields/hr_org_chart.xml:0
msgid "More managers"
msgstr "Mai mulți manageri"

#. module: hr_org_chart
#. odoo-javascript
#: code:addons/hr_org_chart/static/src/views/hr_employee_hierarchy/hr_employee_hierarchy_controller.xml:0
msgid "No Data"
msgstr ""

#. module: hr_org_chart
#. odoo-javascript
#: code:addons/hr_org_chart/static/src/fields/hr_org_chart.xml:0
msgid "No hierarchy position."
msgstr "Fără poziție ierarhică."

#. module: hr_org_chart
#. odoo-python
#: code:addons/hr_org_chart/models/hr_org_chart_mixin.py:0
msgid "Operation not supported"
msgstr "Operațiunea nu este acceptată"

#. module: hr_org_chart
#: model:ir.actions.act_window,name:hr_org_chart.action_hr_employee_org_chart
#: model:ir.ui.menu,name:hr_org_chart.menu_hr_employee_org_chart
msgid "Org Chart"
msgstr "Org Chart"

#. module: hr_org_chart
#: model_terms:ir.ui.view,arch_db:hr_org_chart.hr_employee_public_view_form_inherit_org_chart
#: model_terms:ir.ui.view,arch_db:hr_org_chart.hr_employee_view_form_inherit_org_chart
#: model_terms:ir.ui.view,arch_db:hr_org_chart.res_users_view_form
msgid "Organization Chart"
msgstr "Schema de organizare"

#. module: hr_org_chart
#: model:ir.model,name:hr_org_chart.model_hr_employee_public
msgid "Public Employee"
msgstr "Angajați Publici"

#. module: hr_org_chart
#: model_terms:ir.actions.act_window,help:hr_org_chart.action_hr_employee_org_chart
msgid ""
"Quickly find all the information you need for your employees such as contact"
" data, job position, availability, etc."
msgstr ""

#. module: hr_org_chart
#. odoo-javascript
#: code:addons/hr_org_chart/static/src/fields/hr_org_chart.xml:0
msgid "Redirect"
msgstr "Redirecționare"

#. module: hr_org_chart
#. odoo-javascript
#: code:addons/hr_org_chart/static/src/fields/hr_org_chart.xml:0
msgid "See All"
msgstr "Vezi toate"

#. module: hr_org_chart
#: model:ir.model.fields,field_description:hr_org_chart.field_hr_employee__subordinate_ids
#: model:ir.model.fields,field_description:hr_org_chart.field_hr_employee_public__subordinate_ids
msgid "Subordinates"
msgstr "Subordonati"

#. module: hr_org_chart
#. odoo-javascript
#: code:addons/hr_org_chart/static/src/fields/hooks.js:0
msgid "Team"
msgstr "Echipă"

#. module: hr_org_chart
#. odoo-javascript
#: code:addons/hr_org_chart/static/src/fields/hr_org_chart.xml:0
msgid "This employee has no manager or subordinate."
msgstr "Acest angajat nu are niciun manager sau subordonat."

#. module: hr_org_chart
#. odoo-javascript
#: code:addons/hr_org_chart/static/src/fields/hr_org_chart.xml:0
msgid "Total"
msgstr "Total"

#. module: hr_org_chart
#. odoo-javascript
#: code:addons/hr_org_chart/static/src/views/hr_employee_hierarchy/hr_employee_hierarchy_card.xml:0
msgid "people"
msgstr "oameni"
