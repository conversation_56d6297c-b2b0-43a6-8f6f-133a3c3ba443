# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* project_stock
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:56+0000\n"
"PO-Revision-Date: 2024-09-29 00:00+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: German (https://app.transifex.com/odoo/teams/41243/de/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: de\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: project_stock
#. odoo-python
#: code:addons/project_stock/models/project_project.py:0
#: model:ir.embedded.actions,name:project_stock.project_embedded_action_from_wh
msgid "From WH"
msgstr "Aus Lagerhaus"

#. module: project_stock
#: model:ir.model,name:project_stock.model_project_project
#: model:ir.model.fields,field_description:project_stock.field_stock_picking__project_id
msgid "Project"
msgstr "Projekt"

#. module: project_stock
#. odoo-python
#: code:addons/project_stock/models/project_project.py:0
#: model:ir.embedded.actions,name:project_stock.project_embedded_action_all_pickings
msgid "Stock Moves"
msgstr "Lagerbuchungen"

#. module: project_stock
#. odoo-python
#: code:addons/project_stock/models/project_project.py:0
#: model:ir.embedded.actions,name:project_stock.project_embedded_action_to_wh
msgid "To WH"
msgstr "In Lagerhaus"

#. module: project_stock
#: model:ir.model,name:project_stock.model_stock_picking
msgid "Transfer"
msgstr "Weiterleiten"
