<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="stock_move_per_sale_order_line_action" model="ir.actions.act_window">
        <field name="name">Transfers</field>
        <field name="res_model">stock.move</field>
        <field name="view_mode">list,kanban,pivot,graph,form</field>
        <field name="domain">[('sale_line_id', '=', active_id)]</field>
        <field name="context">{}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No stock move found
            </p>
            <p>
                This menu gives you the full traceability of inventory
                operations on a specific product. You can filter on the product
                to see all the past or future movements for the product.
            </p>
        </field>
    </record>

</odoo>
