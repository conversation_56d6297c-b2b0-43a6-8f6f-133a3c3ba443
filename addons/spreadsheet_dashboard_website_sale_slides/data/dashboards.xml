<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="spreadsheet_dashboard_elearning" model="spreadsheet.dashboard">
        <field name="name">eLearning</field>
        <field name="spreadsheet_binary_data" type="base64" file="spreadsheet_dashboard_website_sale_slides/data/files/elearning_dashboard.json"/>
        <field name="main_data_model_ids" eval="[(4, ref('sale.model_sale_order'))]"/>
        <field name="sample_dashboard_file_path">spreadsheet_dashboard_website_sale_slides/data/files/elearning_sample_dashboard.json</field>
        <field name="dashboard_group_id" ref="spreadsheet_dashboard.spreadsheet_dashboard_group_website"/>
        <field name="group_ids" eval="[Command.link(ref('website_slides.group_website_slides_manager'))]"/>
        <field name="sequence">200</field>
        <field name="is_published">True</field>
    </record>

</odoo>
