# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* stock_fleet
# 
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON> <<EMAIL>>, 2024
# <PERSON> <<EMAIL>>, 2024
# <PERSON> <and<PERSON>.<EMAIL>>, 2024
# <PERSON><PERSON><PERSON> <mikael.aker<PERSON>@mariaakerberg.com>, 2024
# <PERSON>, 2024
# Lasse L, 2024
# <PERSON>, 2024
# Frida E, 2024
# <PERSON> <<EMAIL>>, 2025
# Wil Odoo, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-25 07:49+0000\n"
"PO-Revision-Date: 2024-09-29 00:00+0000\n"
"Last-Translator: Wil Odoo, 2025\n"
"Language-Team: Swedish (https://app.transifex.com/odoo/teams/41243/sv/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sv\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: stock_fleet
#. odoo-python
#: code:addons/stock_fleet/models/fleet_vehicle_model.py:0
msgid "%(display_name)s (%(load_capacity)s)"
msgstr "%(display_name)s (%(load_capacity)s)"

#. module: stock_fleet
#. odoo-python
#: code:addons/stock_fleet/models/fleet_vehicle_model.py:0
msgid "%(volume_capacity)s %(volume_uom)s"
msgstr "%(volume_capacity)s %(volume_uom)s"

#. module: stock_fleet
#. odoo-python
#: code:addons/stock_fleet/models/fleet_vehicle_model.py:0
msgid "%(weight_capacity)s %(weight_uom)s"
msgstr "%(weight_capacity)s %(weight_uom)s"

#. module: stock_fleet
#: model_terms:ir.ui.view,arch_db:stock_fleet.report_picking_batch_inherit
msgid "<strong>Dock:</strong>"
msgstr ""

#. module: stock_fleet
#: model_terms:ir.ui.view,arch_db:stock_fleet.report_picking_batch_inherit
msgid "<strong>Vehicle Category:</strong>"
msgstr ""

#. module: stock_fleet
#: model_terms:ir.ui.view,arch_db:stock_fleet.report_picking_batch_inherit
msgid "<strong>Vehicle:</strong>"
msgstr ""

#. module: stock_fleet
#: model:ir.model,name:stock_fleet.model_stock_picking_batch
#: model_terms:ir.ui.view,arch_db:stock_fleet.stock_picking_batch_pivot
msgid "Batch Transfer"
msgstr "Parti Förflyttning"

#. module: stock_fleet
#: model_terms:ir.ui.view,arch_db:stock_fleet.stock_picking_type_kanban_inherit_stock_fleet
msgid "Batches by Route"
msgstr ""

#. module: stock_fleet
#: model_terms:ir.ui.view,arch_db:stock_fleet.stock_picking_type_kanban_inherit_stock_fleet
msgid "Calendar"
msgstr "Kalender"

#. module: stock_fleet
#: model:ir.model,name:stock_fleet.model_fleet_vehicle_model_category
msgid "Category of the model"
msgstr "Modellens kategori"

#. module: stock_fleet
#: model_terms:ir.ui.view,arch_db:stock_fleet.stock_picking_type_kanban_inherit_stock_fleet
msgid "Dock Dispatching"
msgstr ""

#. module: stock_fleet
#: model:ir.model.fields,field_description:stock_fleet.field_stock_picking_batch__dock_id
#: model_terms:ir.ui.view,arch_db:stock_fleet.stock_picking_batch_filter
msgid "Dock Location"
msgstr ""

#. module: stock_fleet
#: model:ir.model.fields,field_description:stock_fleet.field_stock_picking_batch__driver_id
msgid "Driver"
msgstr "Förare"

#. module: stock_fleet
#: model:ir.model.fields,field_description:stock_fleet.field_stock_picking_batch__end_date
msgid "End Date"
msgstr "Slutdatum"

#. module: stock_fleet
#: model_terms:ir.ui.view,arch_db:stock_fleet.stock_picking_batch_graph
msgid "Graph View"
msgstr "Grafvisning"

#. module: stock_fleet
#: model:ir.model,name:stock_fleet.model_stock_location
msgid "Inventory Locations"
msgstr "Inventeringsställe"

#. module: stock_fleet
#: model:ir.model.fields,field_description:stock_fleet.field_stock_location__is_a_dock
msgid "Is a Dock Location"
msgstr ""

#. module: stock_fleet
#: model_terms:ir.ui.view,arch_db:stock_fleet.stock_picking_type_kanban_inherit_stock_fleet
msgid "Manage Batches"
msgstr ""

#. module: stock_fleet
#: model:ir.model.fields,field_description:stock_fleet.field_fleet_vehicle_model_category__volume_capacity
msgid "Max Volume"
msgstr "Max volym"

#. module: stock_fleet
#: model:ir.model.fields,field_description:stock_fleet.field_stock_picking_batch__vehicle_volume_capacity
msgid "Max Volume (m³)"
msgstr ""

#. module: stock_fleet
#: model:ir.model.fields,field_description:stock_fleet.field_fleet_vehicle_model_category__weight_capacity
msgid "Max Weight"
msgstr "Max Vikt"

#. module: stock_fleet
#: model_terms:ir.ui.view,arch_db:stock_fleet.stock_picking_batch_filter
msgid "Next 7 Days"
msgstr ""

#. module: stock_fleet
#: model_terms:ir.ui.view,arch_db:stock_fleet.stock_picking_batch_filter
msgid "Operation Type"
msgstr "Operationstyp"

#. module: stock_fleet
#: model_terms:ir.ui.view,arch_db:stock_fleet.stock_picking_batch_filter
msgid "Own Fleet"
msgstr ""

#. module: stock_fleet
#: model_terms:ir.ui.view,arch_db:stock_fleet.stock_picking_batch_filter
msgid "Scheduled Date"
msgstr "Schemalagt datum"

#. module: stock_fleet
#: model_terms:ir.ui.view,arch_db:stock_fleet.report_picking_batch_inherit
msgid "Sequence"
msgstr "Sekvens"

#. module: stock_fleet
#: model_terms:ir.ui.view,arch_db:stock_fleet.vpicktree
msgid "Shipping Volume"
msgstr ""

#. module: stock_fleet
#: model_terms:ir.ui.view,arch_db:stock_fleet.vpicktree
msgid "Shipping Weight"
msgstr "Leverans Vikt"

#. module: stock_fleet
#: model_terms:ir.ui.view,arch_db:stock_fleet.stock_picking_type_kanban_inherit_stock_fleet
msgid "Statistics"
msgstr "Statistik"

#. module: stock_fleet
#: model_terms:ir.ui.view,arch_db:stock_fleet.stock_picking_batch_filter
msgid "Third Party Carrier"
msgstr ""

#. module: stock_fleet
#: model_terms:ir.ui.view,arch_db:stock_fleet.stock_picking_batch_form
msgid "Third Party Provider"
msgstr ""

#. module: stock_fleet
#: model_terms:ir.ui.view,arch_db:stock_fleet.stock_picking_batch_filter
msgid "Today"
msgstr "Idag"

#. module: stock_fleet
#: model_terms:ir.ui.view,arch_db:stock_fleet.stock_picking_batch_filter
msgid "Tomorrow"
msgstr "Imorgon"

#. module: stock_fleet
#: model_terms:ir.ui.view,arch_db:stock_fleet.vpicktree
msgid "Total Shipping Volume"
msgstr ""

#. module: stock_fleet
#: model_terms:ir.ui.view,arch_db:stock_fleet.vpicktree
msgid "Total Shipping Weight"
msgstr ""

#. module: stock_fleet
#: model:ir.model,name:stock_fleet.model_stock_picking
msgid "Transfer"
msgstr "Överföring"

#. module: stock_fleet
#: model:fleet.vehicle.tag,name:stock_fleet.vehicle_tag_transport
msgid "Transport"
msgstr "Transport"

#. module: stock_fleet
#: model_terms:ir.ui.view,arch_db:stock_fleet.stock_picking_type_kanban_inherit_stock_fleet
msgid "Transport Management"
msgstr ""

#. module: stock_fleet
#: model:ir.model.fields,field_description:stock_fleet.field_stock_picking_batch__vehicle_weight_capacity
msgid "Vehcilce Payload Capacity"
msgstr ""

#. module: stock_fleet
#: model:ir.model.fields,field_description:stock_fleet.field_stock_picking_batch__vehicle_id
#: model_terms:ir.ui.view,arch_db:stock_fleet.stock_picking_batch_filter
msgid "Vehicle"
msgstr "Fordon"

#. module: stock_fleet
#: model:ir.model.fields,field_description:stock_fleet.field_stock_picking_batch__vehicle_category_id
#: model_terms:ir.ui.view,arch_db:stock_fleet.stock_picking_batch_filter
msgid "Vehicle Category"
msgstr ""

#. module: stock_fleet
#: model_terms:ir.ui.view,arch_db:stock_fleet.stock_picking_batch_form
msgid "Volume"
msgstr "Volym"

#. module: stock_fleet
#: model:ir.model.fields,field_description:stock_fleet.field_stock_picking_batch__used_volume_percentage
msgid "Volume %"
msgstr ""

#. module: stock_fleet
#: model:ir.model.fields,field_description:stock_fleet.field_fleet_vehicle_model_category__volume_capacity_uom_name
#: model:ir.model.fields,field_description:stock_fleet.field_stock_picking_batch__volume_uom_name
msgid "Volume unit of measure label"
msgstr "Måttenhet för volym etikett"

#. module: stock_fleet
#: model_terms:ir.ui.view,arch_db:stock_fleet.stock_picking_batch_form
msgid "Weight"
msgstr "Vikt"

#. module: stock_fleet
#: model:ir.model.fields,field_description:stock_fleet.field_stock_picking_batch__used_weight_percentage
msgid "Weight %"
msgstr ""

#. module: stock_fleet
#: model:ir.model.fields,field_description:stock_fleet.field_fleet_vehicle_model_category__weight_capacity_uom_name
#: model:ir.model.fields,field_description:stock_fleet.field_stock_picking_batch__weight_uom_name
msgid "Weight unit of measure label"
msgstr "Vikt måttenhet etikett"

#. module: stock_fleet
#: model:ir.model.fields,field_description:stock_fleet.field_stock_picking__zip
msgid "Zip"
msgstr "Postnummer"

#. module: stock_fleet
#: model_terms:ir.ui.view,arch_db:stock_fleet.stock_picking_batch_form
msgid "semi-truck"
msgstr ""
