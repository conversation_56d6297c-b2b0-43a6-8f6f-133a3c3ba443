<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="product_ribbon_form_view" model="ir.ui.view">
        <field name="name">product.ribbon.form.view</field>
        <field name="model">product.ribbon</field>
        <field name="arch" type="xml">
            <form>
                <sheet>
                    <group>
                        <group>
                            <field name="name" widget="char"/>
                            <field name="position" widget="radio"/>
                        </group>
                        <group>
                            <field name="text_color" widget="color"/>
                            <field name="bg_color" widget="color"/>
                        </group>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <record id="product_ribbon_view_tree" model="ir.ui.view">
        <field name="name">product.ribbon.list</field>
        <field name="model">product.ribbon</field>
        <field name="arch" type="xml">
            <list string="Product Ribbon">
                <field name="name"/>
                <field name="position"/>
                <field name="text_color" widget="color" readonly="1"/>
                <field name="bg_color" widget="color" readonly="1"/>
            </list>
        </field>
    </record>

    <record id="product_ribbon_action" model="ir.actions.act_window">
        <field name="name">Product Ribbons</field>
        <field name="res_model">product.ribbon</field>
        <field name="view_mode">list,form</field>
        <field name="context">{'create': True}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Define a new ribbon
            </p>
        </field>
    </record>

</odoo>
