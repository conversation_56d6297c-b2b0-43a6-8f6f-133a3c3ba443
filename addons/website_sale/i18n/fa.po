# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_sale
# 
# Translators:
# z<PERSON><PERSON> moradi, 2025
# <PERSON>, 2025
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2025
# <PERSON><PERSON><PERSON>y <<EMAIL>>, 2025
# Naser mars, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-03-19 20:36+0000\n"
"PO-Revision-Date: 2024-09-25 09:42+0000\n"
"Last-Translator: Naser mars, 2025\n"
"Language-Team: Persian (https://app.transifex.com/odoo/teams/41243/fa/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fa\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products
msgid "\" category."
msgstr "\" دسته‌بندی."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid ""
"\"Optional\" allows guests to register from the order confirmation email to "
"track their order."
msgstr ""
"\"اختیاری\" به مهمانان اجازه می‌دهد تا از ایمیل تأیید سفارش ثبت‌نام کنند و "
"سفارش خود را پیگیری کنند."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_image_view_kanban
msgid "#{record.name.value}"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product
msgid "%s review"
msgstr "%s بررسی"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product
msgid "%s reviews"
msgstr "بررسی‌ها %s"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "&amp; Delivery"
msgstr "و تحویل"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products_item
msgid "&amp;nbsp;"
msgstr "&amp;nbsp;"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.checkout_layout
msgid "&amp;nbsp;item(s)&amp;nbsp;-&amp;nbsp;"
msgstr "&nbsp;آیتم(ها)&nbsp;-&nbsp;"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products
msgid "'. Showing results for '"
msgstr "'. نمایش نتایج برای'"

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__website__product_page_image_width__100_pc
msgid "100 %"
msgstr "100%"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "100 percent"
msgstr "100درصد"

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__website__product_page_image_width__50_pc
msgid "50 %"
msgstr "50%"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "50 percent"
msgstr "۵۰ درصد"

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__website__product_page_image_width__66_pc
msgid "66 %"
msgstr "66 ٪"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "66 percent"
msgstr "۶۶ درصد"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.checkout_layout
msgid "<b class=\"w-100\">Order summary</b>"
msgstr "<b class=\"w-100\">خلاصه سفارش</b>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.o_wsale_offcanvas
#: model_terms:ir.ui.view,arch_db:website_sale.products_categories_list
#: model_terms:ir.ui.view,arch_db:website_sale.products_categories_list_collapsible
msgid "<b>Categories</b>"
msgstr "<b>دسته بندی ها</b>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment_confirmation_status
msgid "<b>Communication: </b>"
msgstr "<b>ارتباطات: </b>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address_on_payment
msgid "<b>Deliver to pickup point: </b>"
msgstr "<b>تحویل به نقطه تحویل:</b>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address_on_payment
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "<b>Delivery: </b>"
msgstr "<b>تحویل: </b>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.filter_products_price
msgid "<b>Price Range</b>"
msgstr "<b>محدوده قیمت</b>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.o_wsale_offcanvas
msgid "<b>Pricelist</b>"
msgstr "<b>لیست قیمت</b>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.o_wsale_offcanvas
msgid "<b>Sort By</b>"
msgstr "<b>مرتب‌سازی بر اساس</b>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.filter_products_tags
#: model_terms:ir.ui.view,arch_db:website_sale.o_wsale_offcanvas
msgid "<b>Tags</b>"
msgstr "<b>تگ‌ها</b>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_custom_text
msgid ""
"<br/>\n"
"                30-day money-back guarantee<br/>\n"
"                Shipping: 2-3 Business Days"
msgstr ""
"<br/>\n"
"                با ضمانت برگشت 30 روزه پول<br/>\n"
"                حمل و نقل: 2-3 روز کاری"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.extra_info
msgid ""
"<i class=\"fa fa-angle-left me-2 fw-light\"/>\n"
"                                    Return to shipping"
msgstr ""
"<i class=\"fa fa-angle-left me-2 fw-light\"/>\n"
"                                    بازگشت به حمل و نقل"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_buy_now
msgid ""
"<i class=\"fa fa-bolt me-2\"/>\n"
"                Buy now"
msgstr "<i class=\"fa fa-bolt me-2\"/> همین حالا بخرید"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.s_add_to_cart
msgid "<i class=\"fa fa-cart-plus me-2\"/>Add to Cart"
msgstr "<i class=\"fa fa-cart-plus me-2\"/>افزودن به سبد"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid ""
"<i class=\"fa fa-fw fa-bolt\"/>\n"
"                    Buy Now"
msgstr "<i class=\"fa fa-fw fa-bolt\"/> همین حالا بخرید"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_pages_kanban_view
msgid "<i class=\"fa fa-globe me-1\" title=\"Website\"/>"
msgstr "<i class=\"fa fa-globe me-1\" title=\"Website\"/>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address_kanban
#: model_terms:ir.ui.view,arch_db:website_sale.address_on_payment
msgid "<i class=\"fa fa-pencil me-1\"/>Edit"
msgstr "<i class=\"fa fa-pencil me-1\"/>ویرایش"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address_row
msgid ""
"<i class=\"fa fa-plus me-md-2\"/>\n"
"                    <span class=\"d-none d-md-inline\">Add address</span>"
msgstr ""
"<i class=\"fa fa-plus me-md-2\"/>\n"
"                    <span class=\"d-none d-md-inline\">افزودن آدرس</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "<i class=\"fa fa-print me-2\"/>Print"
msgstr "<i class=\"fa fa-print me-2\"/>چاپ"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_order_re_order_btn
msgid ""
"<i class=\"fa fa-rotate-right me-1\"/>\n"
"                    Order Again"
msgstr "<i class=\"fa fa-rotate-right me-1\"/> دوباره سفارش دهید"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product
msgid ""
"<i class=\"fa fa-shopping-cart me-2\"/>\n"
"                                                Add to cart"
msgstr "<i class=\"fa fa-shopping-cart me-2\"/> افزودن به سبد خرید"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "<i class=\"fw-light fa fa-angle-left me-2\"/>Discard"
msgstr "<i class=\"fw-light fa fa-angle-left me-2\"/>لغو"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product
msgid ""
"<i class=\"oi oi-chevron-left d-lg-none me-1\" role=\"presentation\"/>All "
"Products"
msgstr ""
"<i class=\"oi oi-chevron-left d-lg-none me-1\" role=\"presentation\"/>همه "
"محصولات"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product
msgid "<i class=\"oi oi-chevron-left me-1\" role=\"presentation\"/>All Products"
msgstr "<i class=\"oi oi-chevron-left me-1\" role=\"presentation\"/>همه محصولات"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.shop_product_carousel
msgid ""
"<i class=\"oi oi-chevron-left oe_unmovable border bg-white text-900\" "
"role=\"img\" aria-label=\"Previous\" title=\"Previous\"/>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.shop_product_carousel
msgid ""
"<i class=\"oi oi-chevron-right oe_unmovable border bg-white text-900\" "
"role=\"img\" aria-label=\"Next\" title=\"Next\"/>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "<option value=\"\">Country...</option>"
msgstr "<option value=\"\">کشور...</option>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "<option value=\"\">State / Province...</option>"
msgstr "<option value=\"\">ایالت / استان...</option>"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/crm_team.py:0
msgid ""
"<p class=\"o_view_nocontent_smiling_face\">\n"
"                        You can find all abandoned carts here, i.e. the carts generated by your website's visitors from over an hour ago that haven't been confirmed yet.</p>\n"
"                        <p>You should send an email to the customers to encourage them!</p>\n"
"                    "
msgstr ""
"<p class=\"o_view_nocontent_smiling_face\"> می‌توانید تمام سبدهای خرید رها "
"شده را اینجا پیدا کنید، یعنی سبدهایی که توسط بازدیدکنندگان وب‌سایت شما از "
"بیش از یک ساعت پیش ایجاد شده‌اند و هنوز تأیید نشده‌اند.</p> <p>باید به "
"مشتریان ایمیل ارسال کنید تا آنها را تشویق کنید!</p>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sort
msgid "<small class=\"d-none d-lg-inline text-muted\">Sort By:</small>"
msgstr "<small class=\"d-none d-lg-inline text-muted\">مرتب‌سازی بر اساس:</small>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid ""
"<small class=\"form-text text-muted\">\n"
"                                                        Changing company name or VAT number is not allowed once document(s) have been issued for your account. Please contact us directly for this operation.\n"
"                                                    </small>"
msgstr ""
"<small class=\"form-text text-muted\">\n"
"                                                        تغییر نام شرکت یا شماره VAT پس از صدور اسناد برای حسابتان مجاز نیست. لطفا برای این عملیات مستقیماً با ما تماس بگیرید..\n"
"                                                    </small>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products_attributes
msgid ""
"<small class=\"mx-auto\"><b>Clear Filters</b></small>\n"
"                            <i class=\"oi oi-close\" role=\"presentation\"/>"
msgstr ""
"<small class=\"mx-auto\"><b>حذف فیلترها</b></small>\n"
"                            <i class=\"oi oi-close\" role=\"presentation\"/>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.suggested_products_list
msgid ""
"<span class=\"d-md-none fa fa-shopping-cart\"/>\n"
"                            <span class=\"d-none d-md-inline\">Add to cart</span>"
msgstr ""
"<span class=\"d-md-none fa fa-shopping-cart\"/> <span class=\"d-none d-md-"
"inline\">افزودن به سبد خرید</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-globe\" title=\"Values set here are website-"
"specific.\" groups=\"website.group_multi_website\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-globe\" title=\"Values set here are website-"
"specific.\" groups=\"website.group_multi_website\"/>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.s_dynamic_snippet_products_preview_data
msgid ""
"<span class=\"fw-bold\" name=\"product_price\" data-oe-type=\"monetary\" data-oe-expression=\"data['price']\">\n"
"                                                <span class=\"oe_currency_value\">1,500.00</span>\n"
"                                            </span>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.s_dynamic_snippet_products_preview_data
msgid ""
"<span class=\"fw-bold\" name=\"product_price\" data-oe-type=\"monetary\" data-oe-expression=\"data['price']\">\n"
"                                                <span class=\"oe_currency_value\">140.00</span>\n"
"                                            </span>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.s_dynamic_snippet_products_preview_data
msgid ""
"<span class=\"fw-bold\" name=\"product_price\" data-oe-type=\"monetary\" data-oe-expression=\"data['price']\">\n"
"                                                <span class=\"oe_currency_value\">33.00</span>\n"
"                                            </span>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.s_dynamic_snippet_products_preview_data
msgid ""
"<span class=\"fw-bold\" name=\"product_price\" data-oe-type=\"monetary\" data-oe-expression=\"data['price']\">\n"
"                                                <span class=\"oe_currency_value\">750.00</span>\n"
"                                            </span>"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "<span class=\"mx-2 o_wsale_ppr_by\">by</span>"
msgstr "<span class=\"mx-2 o_wsale_ppr_by\">توسط</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.delivery_method
msgid ""
"<span class=\"o_wsale_delivery_price_badge float-end fw-bold\" name=\"price\">\n"
"                    Select to compute delivery rate\n"
"                </span>"
msgstr ""
"<span class=\"o_wsale_delivery_price_badge float-end fw-bold\" name=\"price\">\n"
"                    برای محاسبه نرخ تحویل انتخاب کنید\n"
"                </span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
#: model_terms:ir.ui.view,arch_db:website_sale.extra_info
#: model_terms:ir.ui.view,arch_db:website_sale.navigation_buttons
msgid "<span class=\"px-3\">or</span>"
msgstr "<span class=\"px-3\">یا</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.extra_info
msgid "<span class=\"s_website_form_label_content\">Give us your feedback</span>"
msgstr ""
"<span class=\"s_website_form_label_content\">نظرات خود را به ما بدهید</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.extra_info
msgid "<span class=\"s_website_form_label_content\">Upload a document</span>"
msgstr ""
"<span class=\"s_website_form_label_content\">یک سند را بارگذاری کنید</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.extra_info
msgid "<span class=\"s_website_form_label_content\">Your Reference</span>"
msgstr "<span class=\"s_website_form_label_content\">ارجاع شما</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_template_only_website_form_view
msgid ""
"<span class=\"text-muted\" invisible=\"product_variant_count &lt;= 1\">Based"
" on variants</span>"
msgstr ""
"<span class=\"text-muted\" invisible=\"product_variant_count &lt;= 1\">بر "
"اساس واریانت‌ها</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products
msgid "<span class=\"visually-hidden\">filters active</span>"
msgstr "<span class=\"visually-hidden\">فیلترها فعال</span>"

#. module: website_sale
#: model_terms:web_tour.tour,rainbow_man_message:website_sale.test_01_admin_shop_tour
msgid "<span><b>Good job!</b> You went through all steps of this tour.</span>"
msgstr ""
"<span><b>کارت عالی بود!</b> شما تمام مراحل این تور را طی کردید.</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "<span>Already have an account?</span>"
msgstr "<span>از قبل حساب کاربری دارید؟</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "<span>Order</span>"
msgstr "<span>سفارش</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_document_kanban
msgid "<span>Publish on website</span>"
msgstr "<span>انتشار در سایت</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment_confirmation_status
msgid ""
"<span>Unfortunately your order can not be confirmed as the amount of your payment does not match the amount of your cart.\n"
"                        Please contact the responsible of the shop for more information.</span>"
msgstr ""
"<span>متأسفانه سفارش شما نمی‌تواند تأیید شود زیرا مبلغ پرداخت شما با مبلغ "
"سبد خرید شما مطابقت ندارد. لطفاً برای اطلاعات بیشتر با مسئول فروشگاه تماس "
"بگیرید.</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.view_product_image_form
msgid "<span>Video Preview</span>"
msgstr "<span>پیشنمایش ویدئو</span>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.delivery_form
msgid "<strong>No suitable delivery method could be found.</strong>"
msgstr "<strong>هیچ روش تحویل مناسبی پیدا نشد.</strong>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "<strong>Total:</strong>"
msgstr "<strong>جمع کل:</strong>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.total
msgid "<strong>Total</strong>"
msgstr "<strong>مجموع</strong>"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart_lines
#: model_terms:ir.ui.view,arch_db:website_sale.checkout_layout
msgid "<strong>Warning!</strong>"
msgstr "<strong>هشدار!</strong>"

#. module: website_sale
#: model:mail.template,body_html:website_sale.mail_template_sale_cart_recovery
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 0px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- CONTENT -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 0px 0px 0px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"                    <h1 style=\"color:#A9A9A9;\">THERE'S SOMETHING IN YOUR CART.</h1>\n"
"                    Would you like to complete your purchase?<br/><br/>\n"
"                    <t t-if=\"object.order_line\">\n"
"                        <t t-foreach=\"object.website_order_line\" t-as=\"line\">\n"
"                            <hr/>\n"
"                            <table width=\"100%\">\n"
"                                <tr>\n"
"                                    <td style=\"padding: 10px; width:150px;\">\n"
"                                        <img t-attf-src=\"/web/image/product.product/{{ line.product_id.id }}/image_128\" style=\"width: 100px; height: 100px; object-fit: contain;\" alt=\"Product image\"/>\n"
"                                    </td>\n"
"                                    <td>\n"
"                                        <strong t-out=\"line.product_id.display_name or ''\">[FURN_7800] Desk Combination</strong><br/><t t-out=\"line.name or ''\">[FURN_7800] Desk Combination Desk combination, black-brown: chair + desk + drawer.</t>\n"
"                                    </td>\n"
"                                    <td width=\"100px\" align=\"right\">\n"
"                                        <t t-out=\"int(line.product_uom_qty) or ''\">10000</t> <t t-out=\"line.product_uom.name or ''\">Units</t>\n"
"                                    </td>\n"
"                                </tr>\n"
"                            </table>\n"
"                        </t>\n"
"                        <hr/>\n"
"                    </t>\n"
"                    <div style=\"text-align: center; padding: 16px 0px 16px 0px; font-size: 14px;\">\n"
"                        <a t-attf-href=\"{{ object.get_base_url() }}/shop/cart?access_token={{ object.access_token }}\" target=\"_blank\" style=\"background-color: #875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\">\n"
"                            Resume order\n"
"                        </a>\n"
"                    </div>\n"
"                    <t t-set=\"company\" t-value=\"object.company_id or object.user_id.company_id or user.company_id\"/>\n"
"                    <div style=\"text-align: center;\"><strong>Thank you for shopping with <t t-out=\"company.name or ''\">My Company (San Francisco)</t>!</strong></div>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"            "
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_custom_text
msgid "<u>Terms and Conditions</u>"
msgstr "شرایط و ضوابط"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/controllers/combo_configurator.py:0
msgid "A combo product can't be empty. Please select at least one option."
msgstr ""
"یک محصول ترکیبی نمی تواند خالی باشد. لطفا حداقل یک گزینه را انتخاب کنید."

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_delivery_carrier__website_description
msgid ""
"A description of the Product that you want to communicate to your customers."
" This description will be copied to every Sales Order, Delivery Order and "
"Customer Invoice/Credit Note"
msgstr ""
"شرح محصولی که می خواهید به مشتریان خود اطلاع دهید. این توضیحات در هر سفارش "
"فروش، سفارش تحویل و فاکتور مشتری/یادداشت اعتبار کپی می‌شود."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product
msgid ""
"A detailed, formatted description to promote your product on this page. Use "
"'/' to discover more features."
msgstr ""
"یک توضیح دقیق و قالب‌بندی شده برای تبلیغ محصول شما در این صفحه. از '/' "
"استفاده کنید تا ویژگی‌های بیشتری را کشف کنید."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_template_form_view
msgid ""
"A detailed,formatted description to promote your product on"
"                                      this page. Use '/' to discover more "
"features."
msgstr ""
"توضیحات دقیق و قالب بندی شده برای تبلیغ محصول شما در این صفحه. برای کشف "
"ویژگی های بیشتر از '/' استفاده کنید."

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.product_template_action_website
msgid ""
"A product can be either a physical product or a service that you sell to "
"your customers."
msgstr ""
"یک محصول می‌تواند یک محصول فیزیکی یا یک خدمتی باشد که شما به مشتریان خود "
"می‌فروشید."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce
msgid "Abandoned"
msgstr "رها شده"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_sale_order__is_abandoned_cart
#: model:ir.model.fields,field_description:website_sale.field_sale_report__is_abandoned_cart
msgid "Abandoned Cart"
msgstr "سبد رها شده"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/crm_team.py:0
#: model:ir.actions.act_window,name:website_sale.action_view_abandoned_tree
#: model:ir.ui.menu,name:website_sale.menu_orders_abandoned_orders
msgid "Abandoned Carts"
msgstr "سبدهای رها شده"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.crm_team_view_kanban_dashboard
msgid "Abandoned Carts to Recover"
msgstr "سبدهای رها شده برای بازیابی"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__cart_abandoned_delay
#: model:ir.model.fields,field_description:website_sale.field_website__cart_abandoned_delay
msgid "Abandoned Delay"
msgstr "تاخیر رها شده"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__send_abandoned_cart_email
msgid "Abandoned Email"
msgstr "ایمیل رها شده"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_snippet_filter__product_cross_selling
msgid "About cross selling products"
msgstr "درباره فروش متقابل محصولات"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Accept Terms & Conditions"
msgstr "پذیرش شرایط و ضوابط"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_image_view_kanban
msgid "Acceptable file size"
msgstr "اندازه فایل قابل قبول"

#. module: website_sale
#: model:website.snippet.filter,name:website_sale.dynamic_filter_cross_selling_accessories
msgid "Accessories for Product"
msgstr "لوازم جانبی برای محصول"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_product__accessory_product_ids
#: model:ir.model.fields,help:website_sale.field_product_template__accessory_product_ids
msgid ""
"Accessories show up when the customer reviews the cart before payment "
"(cross-sell strategy)."
msgstr ""
"لوازم جانبی زمانی نمایش داده می‌شوند که مشتری قبل از پرداخت، سبد خرید را "
"بررسی می‌کند (استراتژی فروش متقابل)."

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__accessory_product_ids
#: model:ir.model.fields,field_description:website_sale.field_product_template__accessory_product_ids
msgid "Accessory Products"
msgstr "محصولات لوازم جانبی"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.s_add_to_cart_options
msgid "Action"
msgstr "عمل"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__message_needaction
msgid "Action Needed"
msgstr "نیاز به اقدام"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Add"
msgstr "افزودن"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_product_view_form_easy_inherit_website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_template_form_view
msgid "Add Media"
msgstr "افزودن رسانه"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/xml/website_sale_reorder_modal.xml:0
msgid "Add To Cart"
msgstr "افزودن به سبد"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__add_to_cart_action
#: model:ir.model.fields,field_description:website_sale.field_website__add_to_cart_action
msgid "Add To Cart Action"
msgstr "افزودن به سبد خرید اقدام"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Add a customizable form during checkout (after address)"
msgstr "اضافه کردن فرم قابل تنظیم در طی پرداخت (پس از آدرس)"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid ""
"Add a reference price per UoM on products (i.e $/kg), in addition to the "
"sale price"
msgstr ""
"اضافه کردن قیمت مرجع به ازای هر واحد اندازه‌گیری (UoM) روی محصولات (مثلاً "
"$/کیلوگرم)، علاوه بر قیمت فروش"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_product__compare_list_price
#: model:ir.model.fields,help:website_sale.field_product_template__compare_list_price
#: model:ir.model.fields,help:website_sale.field_res_config_settings__group_product_price_comparison
msgid ""
"Add a strikethrough price to your /shop and product pages for comparison "
"purposes.It will not be displayed if pricelists apply."
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Add a strikethrough price, as a comparison"
msgstr "افزودن قیمت خط خورده، به عنوان مقایسه"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/xml/website_sale_reorder_modal.xml:0
#: model_terms:ir.ui.view,arch_db:website_sale.cart_lines
#: model_terms:ir.ui.view,arch_db:website_sale.product_quantity
msgid "Add one"
msgstr "افزودن یکی"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/snippets/s_add_to_cart/options.js:0
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_add_to_cart
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_banner
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_borderless_2
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_horizontal_card
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_horizontal_card_2
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:website_sale.s_add_to_cart_options
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Add to Cart"
msgstr "افزودن به سبد"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippets
msgid "Add to Cart Button"
msgstr "دکمه افزودن به سبد خرید"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__module_website_sale_autocomplete
msgid "Address Autocomplete"
msgstr "تکمیل خودکار آدرس"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_template_form_view
#: model_terms:ir.ui.view,arch_db:website_sale.products_attributes
msgid "All"
msgstr "همه"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products_categories_list
#: model_terms:ir.ui.view,arch_db:website_sale.s_dynamic_snippet_products_template_options
msgid "All Products"
msgstr "همه کالاها"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__all_pricelist_ids
msgid "All pricelists"
msgstr "تمام لیست قیمت‌ها"

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__website__ecommerce_access__everyone
msgid "All users"
msgstr "همه کاربر ان"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.view_delivery_carrier_form_website_delivery
msgid "All websites"
msgstr "همه سایت ها"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid ""
"Allow customers to pick up their online purchases at your store and pay in "
"person"
msgstr ""
"به مشتریان اجازه دهید خریدهای آنلاین خود را از فروشگاه شما دریافت کنند و "
"شخصاً پرداخت کنند"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Allow shoppers to compare products based on their attributes"
msgstr "اجازه دهید خریداران محصولات را بر اساس ویژگی‌هایشان مقایسه کنند"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Allow signed-in users to save product in a wishlist"
msgstr ""
"به کاربران وارد شده اجازه دهید که محصول را در فهرست علاقه‌مندی‌ها ذخیره کنند"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_pricelist__selectable
msgid "Allow the end user to choose this price list"
msgstr "به مصرف کنندگان نهایی اجازه انتخاب این لیست قیمت بده"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Allow your customer to add products from previous order in their cart."
msgstr ""
"به مشتریان خود اجازه دهید تا محصولات سفارش قبلی را به سبد خرید خود اضافه "
"کنند."

#. module: website_sale
#: model:ir.actions.server,name:website_sale.dynamic_snippet_alternative_products
#: model:ir.model.fields,field_description:website_sale.field_product_product__alternative_product_ids
#: model:ir.model.fields,field_description:website_sale.field_product_template__alternative_product_ids
#: model:website.snippet.filter,name:website_sale.dynamic_filter_cross_selling_alternative_products
msgid "Alternative Products"
msgstr "محصولات جایگزین"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_crm_team__abandoned_carts_amount
msgid "Amount of Abandoned Carts"
msgstr "تعداد سبدهای رها شده"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/controllers/delivery.py:0
msgid "Anonymous express checkout partner for order %s"
msgstr "شریک تسویه حساب سریع ناشناس برای سفارش%s"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "Apartment, suite, etc."
msgstr "آپارتمان، سوئیت، و غیره."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.coupon_form
msgid "Apply"
msgstr "اعمال"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/website_sale.editor.js:0
msgid "Are you sure you want to delete this ribbon?"
msgstr "آیا مطمئن هستید که می خواهید این نوار را حذف کنید؟"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Assignment"
msgstr "تخصیص"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Assignment of online orders"
msgstr "اختصاص سفارش‌های آنلاین"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__message_attachment_count
msgid "Attachment Count"
msgstr "تعداد پیوست ها"

#. module: website_sale
#: model:ir.ui.menu,name:website_sale.menu_product_attribute_action
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Attributes"
msgstr "ویژگی ها"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_multimedia_augmented_reality
msgid "Augmented Reality Tools"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Automatically send abandoned checkout emails"
msgstr "ارسال خودکار ایمیل‌های سبد خرید رها شده"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/product_list/product_list.js:0
msgid "Available options"
msgstr "گزینه های موجود"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__rating_avg
msgid "Average Rating"
msgstr "رتبه‌ی متوسط"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website.py:0
msgid "Back to cart"
msgstr "بازگشت به سبد خرید"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website.py:0
msgid "Back to delivery"
msgstr "بازگشت به تحویل"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Background"
msgstr "پس‌زمینه"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_ribbon__bg_color
msgid "Background Color"
msgstr "رنگ پس‌زمینه"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__base_unit_count
#: model:ir.model.fields,field_description:website_sale.field_product_template__base_unit_count
msgid "Base Unit Count"
msgstr "واحد پایه شمارش"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__base_unit_name
#: model:ir.model.fields,field_description:website_sale.field_product_template__base_unit_name
msgid "Base Unit Name"
msgstr "نام واحد پایه"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__group_show_uom_price
msgid "Base Unit Price"
msgstr "قیمت پایه واحد"

#. module: website_sale
#: model:ir.actions.act_window,name:website_sale.base_unit_action
msgid "Base Units"
msgstr "واحدهای پایه"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_cabinets_bathroom
msgid "Bathroom Cabinets"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "Be aware!"
msgstr "هشیار باش!"

#. module: website_sale
#: model:res.country.group,name:website_sale.benelux
msgid "BeNeLux"
msgstr "بنلوکس"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_furnitures_beds
msgid "Beds"
msgstr ""

#. module: website_sale
#: model:product.pricelist,name:website_sale.list_benelux
msgid "Benelux"
msgstr "بنلوکس"

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__website__product_page_image_spacing__big
msgid "Big"
msgstr "بزرگ"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Big Icons Subtitles"
msgstr "زیرنویس آیکون‌های بزرگ"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "Billing"
msgstr "صورتحساب"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
#: model_terms:ir.ui.view,arch_db:website_sale.billing_address_row
msgid "Billing address"
msgstr "آدرس صورت‌حساب"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address_on_payment
msgid "Billing:"
msgstr "صورتحساب:"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website_snippet_filter.py:0
msgid "Bin"
msgstr "بین"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_bins
msgid "Bins"
msgstr "بین‌ها"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid ""
"Boost your sales with multiple kinds of programs: Coupons, Promotions, Gift "
"Card, Loyalty. Specific conditions can be set (products, customers, minimum "
"purchase amount, period). Rewards can be discounts (% or amount) or free "
"products."
msgstr ""
"فروش خود را با انواع برنامه ها افزایش دهید: کوپن، تبلیغات، کارت هدیه، "
"وفاداری. شرایط خاصی را می توان تنظیم کرد (محصولات، مشتریان، حداقل مقدار "
"خرید، دوره). جوایز می توانند تخفیف (% یا مقدار) یا محصولات رایگان باشند."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Both"
msgstr "هر دو"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Bottom"
msgstr "پایین"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website_snippet_filter.py:0
msgid "Box"
msgstr "باکس"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_boxes
msgid "Boxes"
msgstr "جعبه‌ها"

#. module: website_sale
#: model:product.attribute,name:website_sale.product_attribute_brand
msgid "Brand"
msgstr "برند"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__website_sale_contact_us_button_url
msgid "Button Url"
msgstr "دکمه آدرس اینترنتی"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Button url"
msgstr "دکمه آدرس اینترنتی"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Buttons"
msgstr "دکمه‌ها"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__enabled_buy_now_button
#: model_terms:ir.ui.view,arch_db:website_sale.s_add_to_cart_options
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Buy Now"
msgstr "اکنون خرید کنید"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/snippets/s_add_to_cart/options.js:0
msgid "Buy now"
msgstr "اکنون بخرید"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.s_dynamic_snippet_products_preview_data
msgid "Cabinet with Doors"
msgstr "کابینت با در"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_cabinets
msgid "Cabinets"
msgstr "کابینت‌ها"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__can_image_1024_be_zoomed
msgid "Can Image 1024 be zoomed"
msgstr "تصویر 1024 می‌تواند زوم شود"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_delivery_carrier__can_publish
#: model:ir.model.fields,field_description:website_sale.field_product_template__can_publish
msgid "Can Publish"
msgstr "می تواند منتشر کند"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Cards"
msgstr "کارت‌ها"

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__website__product_page_image_layout__carousel
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Carousel"
msgstr "چرخ فلک"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Cart"
msgstr "سبد خرید"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_sale_order__cart_quantity
msgid "Cart Quantity"
msgstr "تعداد سبد خرید"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__cart_recovery_mail_template
#: model:ir.model.fields,field_description:website_sale.field_website__cart_recovery_mail_template_id
msgid "Cart Recovery Email"
msgstr "ایمیل بازیابی سبد خرید"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_sale_order__cart_recovery_email_sent
msgid "Cart recovery email already sent"
msgstr "ایمیل بازیابی سبد خرید قبلاً ارسال شده است"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Carts are flagged as abandoned after this delay."
msgstr "سبدهای خرید پس از این تاخیر به عنوان رها شده علامت‌گذاری می‌شوند."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_template_form_view
#: model_terms:ir.ui.view,arch_db:website_sale.product_template_view_tree_website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Categories"
msgstr "دسته بندی ها"

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.product_public_category_action
msgid ""
"Categories are used to browse your products through the\n"
"                touchscreen interface."
msgstr ""
"دسته‌های محصول برای مرور محصولات شما از طریق\n"
"             رابط کاربری صفحه لمسی استفاده می‌شوند."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_category_extra_link
msgid "Categories:"
msgstr "دسته‌بندی‌ها:"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_searchbar_input_snippet_options
#: model_terms:ir.ui.view,arch_db:website_sale.s_dynamic_snippet_products_template_options
msgid "Category"
msgstr "دسته بندی"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__website_description
msgid "Category Description"
msgstr "توضیحات دسته بندی"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__website_footer
msgid "Category Footer"
msgstr "فوتر دسته بندی"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_category_extra_link
msgid "Category:"
msgstr "دسته بندی:"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_lamps_ceiling
msgid "Ceiling Lamps"
msgstr ""

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website_snippet_filter.py:0
msgid "Chair"
msgstr "صندلی"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_furnitures_chairs
msgid "Chairs"
msgstr "صندلی ها"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_lamps_chandelier
msgid "Chandeliers"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.delivery_method
msgid "Change location"
msgstr "تغییر آدرس"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/controllers/main.py:0
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid ""
"Changing VAT number is not allowed once document(s) have been issued for "
"your account. Please contact us directly for this operation."
msgstr ""
"تغییر شماره مالیات بر ارزش افزوده پس از صدور سند(ها) برای حساب شما مجاز "
"نیست. لطفاً برای این عملیات مستقیماً با ما تماس بگیرید."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid ""
"Changing company name is not allowed once document(s) have been issued for "
"your account. Please contact us directly for this operation."
msgstr ""
"تغییر نام شرکت زمانی که سند(ها) برای حساب شما صادر شده‌اند مجاز نیست. لطفاً "
"برای انجام این عملیات مستقیماً با ما تماس بگیرید."

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/controllers/main.py:0
msgid ""
"Changing your name is not allowed once invoices have been issued for your "
"account. Please contact us directly for this operation."
msgstr ""
"زمانی که فاکتورهایی برای حساب شما صادر شده باشد، تغییر نام شرکت شما مجاز "
"نیست. لطفا برای این عملیات مستقیما با ما تماس بگیرید."

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website.py:0
msgid "Checkout"
msgstr "پرداخت"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Checkout Pages"
msgstr "صفحات پرداخت"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__child_id
msgid "Children Categories"
msgstr "طبقه بندی‌های فرزند"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.delivery_form
msgid "Choose a delivery method"
msgstr "یک روش تحویل را انتخاب کنید"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/location_selector/location_selector_dialog/location_selector_dialog.js:0
msgid "Choose a pick-up point"
msgstr "یک نقطه تحویل را انتخاب کنید"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/location_selector/location_selector_dialog/location_selector_dialog.js:0
#: code:addons/website_sale/static/src/js/location_selector/map_container/map_container.js:0
msgid "Choose this location"
msgstr "انتخاب این آدرس"

#. module: website_sale
#: model:product.pricelist,name:website_sale.list_christmas
msgid "Christmas"
msgstr "کریسمس"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "City"
msgstr "شهر"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.o_wsale_offcanvas
#: model_terms:ir.ui.view,arch_db:website_sale.products_attributes
msgid "Clear Filters"
msgstr "پاک کردن فیلترها"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__module_website_sale_collect
msgid "Click & Collect"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products
msgid ""
"Click <i>'New'</i> in the top-right corner to create your first product."
msgstr ""
"بر روی <i>'جدید'</i> در گوشه بالا-راست کلیک کن برای ایجاد اولین محصول."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart
msgid "Click here"
msgstr "اینجا کلیک کن"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/tours/website_sale_shop.js:0
msgid "Click here to open the reporting menu"
msgstr "برای باز کردن منوی گزارش‌گیری اینجا کلیک کنید"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/tours/website_sale_shop.js:0
msgid "Click on <em>Save</em> to create the product."
msgstr "روی <em>ذخیره</em> کلیک کنید تا محصول ایجاد شود."

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/tours/website_sale_shop.js:0
msgid "Click on this button so your customers can see it."
msgstr "روی این دکمه کلیک کنید، سپس مشتریان شما این را می‌بینند."

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/notification/cart_notification/cart_notification.xml:0
#: model_terms:ir.ui.view,arch_db:website_sale.o_wsale_offcanvas
msgid "Close"
msgstr "بستن"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/location_selector/location_schedule/location_schedule.js:0
msgid "Closed"
msgstr "بسته شد"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Collapse Category Recursive"
msgstr "جمع‌شدن دسته‌بندی به صورت بازگشتی"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_boxes_collapsible
msgid "Collapsible Boxes"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Collapsible sidebar"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Color"
msgstr "رنگ"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Columns"
msgstr "ستون‌ها"

#. module: website_sale
#: model:ir.ui.menu,name:website_sale.menu_product_combos
msgid "Combo Choices"
msgstr "انتخاب‌های ترکیبی"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.s_dynamic_snippet_products_template_options
msgid ""
"Comma-separated list of parts of product names, barcodes or internal "
"reference"
msgstr "فهرست جدا شده با ویرگول از بخش‌های نام محصولات، بارکدها یا مرجع داخلی"

#. module: website_sale
#: model:ir.model,name:website_sale.model_res_company
msgid "Companies"
msgstr "شرکت‌ها"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/website_sale_form_editor.js:0
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "Company Name"
msgstr "نام شرکت"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__compare_list_price
#: model:ir.model.fields,field_description:website_sale.field_product_template__compare_list_price
msgid "Compare to Price"
msgstr "مقایسه با قیمت"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__group_product_price_comparison
#: model:res.groups,name:website_sale.group_product_price_comparison
msgid "Comparison Price"
msgstr "قیمت مقایسه‌ای"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_desks_components
msgid "Components"
msgstr "اجزا"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Compute shipping cost and ship with Easypost"
msgstr "محاسبه هزینه حمل و نقل و ارسال با Easypost"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Compute shipping cost and ship with Shiprocket"
msgstr "محاسبه هزینه حمل و نقل و ارسال با Shiprocket"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with DHL"
msgstr "محاسبه هزینه حمل و نقل و ارسال با DHL"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with FedEx"
msgstr "محاسبه هزینه حمل و نقل و ارسال با FedEx"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with UPS"
msgstr "محاسبه هزینه حمل و نقل و ارسال با UPS"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with USPS"
msgstr "محاسبه هزینه حمل و نقل و ارسال با USPS"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with bpost"
msgstr "محاسبه هزینه حمل و نقل و ارسال با bpost"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Compute shipping costs on orders"
msgstr "هزینه‌های ارسال در سفارش‌ها محاسبه کن"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.s_dynamic_snippet_products_preview_data
msgid "Conference Chair"
msgstr ""

#. module: website_sale
#: model:ir.model,name:website_sale.model_res_config_settings
msgid "Config Settings"
msgstr "تنظیمات پیکربندی"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Configure Form"
msgstr "پیکربندی فرم"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/product_configurator_dialog/product_configurator_dialog.js:0
msgid "Configure your product"
msgstr "پیکربندی محصول خود را تنظیم کنید"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website.py:0
msgid "Confirm"
msgstr "تایید کردن"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.navigation_buttons
msgid "Confirm Order"
msgstr "تایید سفارش"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment
msgid "Confirm order"
msgstr "تأیید سفارش"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce
msgid "Confirmed"
msgstr "تایید شده"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
msgid "Confirmed Orders"
msgstr "سفارشات تایید شده"

#. module: website_sale
#: model:ir.model,name:website_sale.model_res_partner
msgid "Contact"
msgstr "مخاطب"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product
msgid "Contact Us"
msgstr "تماس با ما"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__contact_us_button_url
msgid "Contact Us Button URL"
msgstr "دکمه تماس با ما URL"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/combo_configurator_dialog/combo_configurator_dialog.xml:0
#: code:addons/website_sale/static/src/js/product_configurator_dialog/product_configurator_dialog.xml:0
msgid "Continue Shopping"
msgstr "ادامه خرید"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website.py:0
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "Continue checkout"
msgstr "ادامه پرداخت"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.extra_info
msgid ""
"Continue checkout\n"
"                                    <i class=\"fa fa-angle-right ms-2 fw-light\"/>"
msgstr ""
"ادامه فرآیند پرداخت\n"
"                                    <i class=\"fa fa-angle-right ms-2 fw-light\"/>"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website.py:0
msgid "Continue shopping"
msgstr "ادامه خرید"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_furnitures_couches
msgid "Couches"
msgstr "کاناپه‌ها"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "Country"
msgstr "کشور"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Create"
msgstr "ایجاد"

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.product_template_action_website
msgid "Create a new product"
msgstr "ایجاد یک محصول جدید"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__create_uid
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__create_uid
#: model:ir.model.fields,field_description:website_sale.field_product_ribbon__create_uid
#: model:ir.model.fields,field_description:website_sale.field_website_base_unit__create_uid
#: model:ir.model.fields,field_description:website_sale.field_website_sale_extra_field__create_uid
msgid "Created by"
msgstr "ایجاد شده توسط"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.s_mega_menu_images_subtitles
#: model_terms:ir.ui.view,arch_db:website_sale.s_mega_menu_little_icons
msgid ""
"Created in 2021, the company is young and dynamic. Discover the composition "
"of the team and their skills."
msgstr ""
"ایجاد شده در سال ۲۰۲۱، این شرکت جوان و پویا است. ترکیب تیم و مهارت‌های آن‌ها"
" را کشف کنید."

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__create_date
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__create_date
#: model:ir.model.fields,field_description:website_sale.field_product_ribbon__create_date
#: model:ir.model.fields,field_description:website_sale.field_website_base_unit__create_date
#: model:ir.model.fields,field_description:website_sale.field_website_sale_extra_field__create_date
msgid "Created on"
msgstr "ایجادشده در"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce_abondand
msgid "Creation Date"
msgstr "تاریخ ایجاد"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.s_dynamic_snippet_products_template_options
msgid "Current Category or All"
msgstr "دسته‌بندی فعلی یا همه"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__base_unit_id
#: model:ir.model.fields,field_description:website_sale.field_product_template__base_unit_id
msgid "Custom Unit of Measure"
msgstr "واحد اندازه‌گیری سفارشی"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
msgid "Customer"
msgstr "مشتری"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__auth_signup_uninvited
msgid "Customer Account"
msgstr "حساب مشتری"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__account_on_checkout
#: model:ir.model.fields,field_description:website_sale.field_website__account_on_checkout
msgid "Customer Accounts"
msgstr "حساب‌های مشتری"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
msgid "Customer Country"
msgstr "کشور مشتری"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_comment
msgid "Customer Reviews"
msgstr "بازخورد مشتریان"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid ""
"Customer needs to be signed in otherwise the mail address is not known.     \n"
"\n"
"- If a potential customer creates one or more abandoned checkouts and then completes a sale before the recovery email gets sent, then the email won't be sent.     \n"
"\n"
"- If user has manually sent a recovery email, the mail will not be sent a second time     \n"
"\n"
"- If a payment processing error occurred when the customer tried to complete their checkout, then the email won't be sent.     \n"
"\n"
"- If your shop does not support shipping to the customer's address, then the email won't be sent.     \n"
"\n"
"- If none of the products in the checkout are available for purchase (empty inventory, for example), then the email won't be sent.     \n"
"\n"
"- If all the products in the checkout are free, and the customer does not visit the shipping page to add a shipping fee or the shipping fee is also free, then the email won't be sent."
msgstr ""
"برای ورود مشتری لازم است در سیستم وارد شده باشد وگرنه نشانی ایمیل او ناشناخته خواهد بود.\n"
"\n"
"- اگر مشتری احتمالی یک یا چند تسویه حساب ناتمام ایجاد کند و سپس قبل از ارسال ایمیل یادآوری خرید را کامل کند، آنگاه ایمیل ارسال نخواهد شد.\n"
"\n"
"- اگر کاربر به صورت دستی یک ایمیل بازیابی ارسال کرده باشد، ایمیل برای بار دوم ارسال نخواهد شد. \n"
"\n"
"- اگر خطایی در پردازش پرداخت زمانی که مشتری سعی در تکمیل تسویه حساب خود داشت رخ داده‌است، ایمیل ارسال نخواهد شد.\n"
"\n"
"- اگر فروشگاه شما از ارسال به نشانی مشتری پشتیبانی نمی‌کند، ایمیل ارسال نخواهد شد.\n"
"\n"
"- اگر هیچ‌یک از محصولات در تسویه حساب برای خرید در دسترس نباشند (مثلاً موجودی خالی)، ایمیل ارسال نخواهد شد.\n"
"\n"
"- اگر تمام محصولات در تسویه حساب رایگان باشند، و مشتری به صفحه حمل و نقل برای افزودن هزینه حمل و نقل نرود یا در صورتی که هزینه حمل و نقل نیز رایگان باشد، ایمیل ارسال نخواهد شد."

#. module: website_sale
#: model:ir.ui.menu,name:website_sale.menu_orders_customers
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Customers"
msgstr "مشتریان"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.s_dynamic_snippet_products_preview_data
msgid "Customizable Desk"
msgstr "میز قابل تنظیم"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Customize Abandoned Email Template"
msgstr "تخصیص الگوی ایمیل رها شده"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/res_config_settings.py:0
msgid "Customize Email Templates"
msgstr "سفارشی‌سازی قالب‌های ایمیل"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "DHL Express Connector"
msgstr "کانکتور DHL Express"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "DHL Shipping Methods"
msgstr "روش های حمل و نقل DHL"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product
msgid "DROP BUILDING BLOCKS HERE TO MAKE THEM AVAILABLE ACROSS ALL PRODUCTS"
msgstr "قطعات ساختمانی را اینجا بیندازید تا برای تمامی محصولات در دسترس باشند"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Default"
msgstr "پیش‌فرض"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Default (1/1)"
msgstr "پیش‌فرض (۱/۱)"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Default (1x1)"
msgstr "پیش‌فرض (1x1)"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__currency_id
msgid "Default Currency"
msgstr "ارز پیش‌فرض"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__pricelist_id
msgid "Default Pricelist if any"
msgstr "اگر لیست قیمتی پیش‌فرض وجود داشته باشد"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Default Sort"
msgstr "مرتب سازی پیش‌فرض"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_product__base_unit_id
#: model:ir.model.fields,help:website_sale.field_product_template__base_unit_id
#: model:ir.model.fields,help:website_sale.field_website_base_unit__name
msgid ""
"Define a custom unit to display in the price per unit of measure field."
msgstr ""
"واحد سفارشی را برای نمایش در فیلد قیمت به ازای واحد اندازه‌گیری تعریف کنید."

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.product_public_category_action
msgid "Define a new category"
msgstr "تعریف یک دسته بندی جدید"

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.product_ribbon_action
msgid "Define a new ribbon"
msgstr "تعریف روبان جدید"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Delete Ribbon"
msgstr "حذف روبان"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website.py:0
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:website_sale.total
msgid "Delivery"
msgstr "تحویل"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address_on_payment
msgid "Delivery &amp;"
msgstr "حمل و "

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_sale_order__amount_delivery
msgid "Delivery Amount"
msgstr "مبلغ تحویل"

#. module: website_sale
#: model:ir.ui.menu,name:website_sale.menu_ecommerce_delivery
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Delivery Methods"
msgstr "روش های تحویل"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
#: model_terms:ir.ui.view,arch_db:website_sale.delivery_address_row
msgid "Delivery address"
msgstr "آدرس تحویل"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_services_delivery_and_installation
msgid "Delivery and Installation"
msgstr "تحویل و نصب"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_searchbar_input_snippet_options
msgid "Description"
msgstr "توصیف"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.view_delivery_carrier_form_website_delivery
msgid "Description displayed on the eCommerce and on online quotations."
msgstr "توضیحات در تجارت الکترونیک و در نقل قول های آنلاین نمایش داده می شود."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_public_category_form_view
msgid "Description displayed on the eCommerce categories page."
msgstr "توضیحات در صفحه دسته بندی های تجارت الکترونیک نمایش داده می شود."

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_delivery_carrier__website_description
msgid "Description for Online Quotations"
msgstr "توضیحات برای نقل قول های آنلاین"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__website_description
#: model:ir.model.fields,field_description:website_sale.field_product_template__website_description
msgid "Description for the website"
msgstr "توضیحات برای وبسایت"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_services_design_and_planning
msgid "Design and Planning"
msgstr ""

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_lamps_desk
msgid "Desk Lamps"
msgstr ""

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_desks
msgid "Desks"
msgstr "میزها"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_product__website_sequence
#: model:ir.model.fields,help:website_sale.field_product_template__website_sequence
msgid "Determine the display order in the Website E-commerce"
msgstr "تعیین ترتیب نمایش در تجارت الکترونیک وبسایت"

#. module: website_sale
#: model:ir.model,name:website_sale.model_digest_digest
msgid "Digest"
msgstr "خلاصه"

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__res_config_settings__account_on_checkout__disabled
#: model:ir.model.fields.selection,name:website_sale.selection__website__account_on_checkout__disabled
msgid "Disabled (buy as guest)"
msgstr "غیرفعال (خرید به عنوان مهمان)"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/xml/website_sale_reorder_modal.xml:0
msgid "Discard"
msgstr "رها کردن"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.coupon_form
msgid "Discount code..."
msgstr "کد تخفیف..."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Discounts, Loyalty & Gift Card"
msgstr "تخفیف‌ها، وفاداری و کارت هدیه"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.s_mega_menu_images_subtitles
#: model_terms:ir.ui.view,arch_db:website_sale.s_mega_menu_little_icons
msgid "Discover our team"
msgstr "تیم ما را بشناسید"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__display_name
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__display_name
#: model:ir.model.fields,field_description:website_sale.field_product_ribbon__display_name
#: model:ir.model.fields,field_description:website_sale.field_website_base_unit__display_name
#: model:ir.model.fields,field_description:website_sale.field_website_sale_extra_field__display_name
msgid "Display Name"
msgstr "نام نمایش داده شده"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Display Product Prices"
msgstr "نمایش قیمت محصولات"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Display Type"
msgstr "نوع نمایش"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_product__base_unit_count
#: model:ir.model.fields,help:website_sale.field_product_template__base_unit_count
msgid ""
"Display base unit price on your eCommerce pages. Set to 0 to hide it for "
"this product."
msgstr ""
"قیمت واحد پایه را در صفحات تجارت الکترونیک خود نمایش دهید. برای پنهان کردن "
"آن برای این محصول، آن را به 0 تنظیم کنید."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_template_form_view
msgid "Displayed in bottom of product pages"
msgstr "نمایش داده شده در پایین صفحات محصول"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_product__base_unit_name
#: model:ir.model.fields,help:website_sale.field_product_template__base_unit_name
msgid ""
"Displays the custom unit for the products if defined or the selected unit of"
" measure otherwise."
msgstr ""
"محصولات را با واحد سفارشی نمایش می‌دهد اگر تعریف شده باشد، وگرنه واحد "
"اندازه‌گیری انتخاب شده را نمایش می‌دهد."

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/digest.py:0
msgid "Do not have access, skip this data for user's digest email"
msgstr "دسترسی ندارید، از این داده ها برای ایمیل خلاصه کاربر صرفنظر کنید"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/website_sale_reorder.js:0
msgid "Do you wish to clear your cart before adding products to it?"
msgstr "آیا می‌خواهید سبد خرید خود را قبل از افزودن محصولات به آن پاک کنید؟"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product
msgid "Documents"
msgstr "اسناد"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/product_document.py:0
msgid ""
"Documents shown on product page cannot be restricted to a specific variant"
msgstr "متون نمایش داده‌شده در صفحه محصول نمی‌توانند به یک نوع خاص محدود شوند"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/tours/website_sale_shop.js:0
msgid "Double click here to set an image describing your product."
msgstr "لطفاً اینجا دوبار کلیک کنید تا تصویری توصیفی از محصول خود تنظیم کنید."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products
msgid ""
"Drag building blocks here to customize the footer for\n"
"                                    \""
msgstr ""
"برای سفارشی کردن پاورقی، بلوک های ایجادی را به اینجا بکشید\n"
"                                    \""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products
msgid ""
"Drag building blocks here to customize the header for\n"
"                                    \""
msgstr ""
"بلوک های ایجادی را به اینجا بکشید تا سرصفحه را سفارشی کنید\n"
"                                    \""

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website_snippet_filter.py:0
msgid "Drawer"
msgstr "کشو"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_drawers
msgid "Drawers"
msgstr "کشوها"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__shop_extra_field_ids
msgid "E-Commerce Extra Fields"
msgstr "فیلدهای اضافی تجارت الکترونیک"

#. module: website_sale
#: model:ir.model,name:website_sale.model_website_sale_extra_field
msgid "E-Commerce Extra Info Shown on product page"
msgstr "افزونه اطلاعات اضافی تجارت الکترونیک نمایش داده شده در صفحه محصول"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_document_form
msgid "E-commerce"
msgstr "تجارت الکترونیک"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_pricelist__code
msgid "E-commerce Promotional Code"
msgstr "کد تبلیغاتی تجارت الکترونیک"

#. module: website_sale
#: model:product.pricelist,name:website_sale.list_europe
msgid "EUR"
msgstr "یورو"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Easypost"
msgstr "ایزی‌پست"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Easypost Shipping Methods"
msgstr "روش های حمل و نقل Easypost"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_sale_pricelist_form_view
msgid "Ecommerce"
msgstr "تجارت الکترونیکی"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__ecommerce_access
#: model:ir.model.fields,field_description:website_sale.field_website__ecommerce_access
msgid "Ecommerce Access"
msgstr "دسترسی تجارت الکترونیک"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_template_form_view
msgid "Ecommerce Description"
msgstr "توضیحات تجارت الکترونیک"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_template_form_view
msgid "Ecommerce Shop"
msgstr "فروشگاه تجارت الکترونیک"

#. module: website_sale
#: model:mail.template,name:website_sale.mail_template_sale_cart_recovery
msgid "Ecommerce: Cart Recovery"
msgstr "تجارت الکترونیک: بازیابی سبد خرید"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment_confirmation_status
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Edit"
msgstr "ویرایش"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/tours/website_sale_shop.js:0
msgid "Edit the price of this product by clicking on the amount."
msgstr "ویرایش قیمت این محصول با کلیک بر روی مقدار."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address_kanban
msgid "Edit this address"
msgstr "ویرایش این نشانی"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_multimedia_education
msgid "Education Tools"
msgstr "ابزارهای آموزش"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
#: model_terms:ir.ui.view,arch_db:website_sale.product_share_buttons
msgid "Email"
msgstr "پست الکترونیک"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__embed_code
msgid "Embed Code"
msgstr "تعبیه کد"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Enforce a customer to be logged in to access 'Shop'"
msgstr "مجبور کردن یک مشتری برای ورود به سیستم برای دسترسی به \"فروشگاه\""

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/product_template_attribute_line/product_template_attribute_line.js:0
msgid "Enter a customized value"
msgstr "یک مقدار سفارشی وارد کنید"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/tours/website_sale_shop.js:0
msgid "Enter a name for your new product"
msgstr "نامی برای محصول جدید خود وارد کنید"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/product_public_category.py:0
msgid "Error! You cannot create recursive categories."
msgstr "خطا! شما نمی‌توانید دسته‌بندی‌های بازگشتی ایجاد کنید."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Extra Images"
msgstr "تصاویر اضافی"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website.py:0
msgid "Extra Info"
msgstr "اطلاعات اضافی"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__product_template_image_ids
#: model:ir.model.fields,field_description:website_sale.field_product_template__product_template_image_ids
msgid "Extra Product Media"
msgstr "مدیا اضافی محصول"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Extra Step"
msgstr "مرحله اضافه "

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__enabled_extra_checkout_step
msgid "Extra Step During Checkout"
msgstr "مرحله اضافی در طول تسویه‌حساب"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__product_variant_image_ids
msgid "Extra Variant Images"
msgstr "تصاویر اضافی واریانت"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_product_view_form_easy_inherit_website_sale
msgid "Extra Variant Media"
msgstr "مدیای اضافی برای انواع مختلف"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.extra_info
msgid "Extra info"
msgstr "اطلاعات اضافی"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_share_buttons
msgid "Facebook"
msgstr "فیسبوک"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website.py:0
msgid "Featured"
msgstr "منتخب"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "FedEx"
msgstr "فدکس"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "FedEx Shipping Methods"
msgstr "روش های حمل و نقل FedEx"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_sale_extra_field__field_id
msgid "Field"
msgstr "فیلد"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_sale_extra_field__label
msgid "Field Label"
msgstr "برچسب فیلد"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_sale_extra_field__name
msgid "Field Name"
msgstr "نام فیلد"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_drawers_file
msgid "File Drawers"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Fill"
msgstr "پر کردن"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "Fill in your address"
msgstr "آدرس خود را وارد کنید"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/product_template.py:0
msgid ""
"Final price may vary based on selection. Tax will be calculated at checkout."
msgstr ""
"قیمت نهایی ممکن است بر اساس انتخاب متفاوت باشد. مالیات در هنگام تسویه حساب "
"محاسبه می شود."

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__fiscal_position_id
msgid "Fiscal Position"
msgstr "موقعیت مالی"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_desks_foldable
msgid "Foldable Desks"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__message_follower_ids
msgid "Followers"
msgstr "دنبال کنندگان"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__message_partner_ids
msgid "Followers (Partners)"
msgstr "پیروان (شرکاء)"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_bins_storage
msgid "Food Storage Bins"
msgstr ""

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/checkout.js:0
msgid "Free"
msgstr "رایگان"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce
msgid "From Website"
msgstr "از وبسایت"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "Full name"
msgstr "نام کامل"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_furnitures
msgid "Furnitures"
msgstr "مبلمان"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_desks_gaming
msgid "Gaming Desks"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Gap"
msgstr "اختلاف "

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid ""
"Generate the invoice automatically when the online payment is confirmed"
msgstr "تولید خودکار فاکتور وقتی پرداخت تایید شده است"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_desks_glass
msgid "Glass Desks"
msgstr ""

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/tours/tour_utils.js:0
#: model:ir.model.fields.selection,name:website_sale.selection__website__add_to_cart_action__go_to_cart
msgid "Go to cart"
msgstr "به سبد خرید بروید"

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__website__product_page_image_layout__grid
#: model_terms:ir.ui.view,arch_db:website_sale.add_grid_or_list_option
#: model_terms:ir.ui.view,arch_db:website_sale.products
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Grid"
msgstr "شبکه"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__shop_gap
msgid "Grid-gap on the shop"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce_abondand
msgid "Group By"
msgstr "گروه‌بندی برمبنای"

#. module: website_sale
#: model:ir.model,name:website_sale.model_ir_http
msgid "HTTP Routing"
msgstr "مسیریابی HTTP"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__has_message
msgid "Has Message"
msgstr "آیا دارای پیام است"

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__product_attribute__visibility__hidden
#: model:ir.model.fields.selection,name:website_sale.selection__website__product_page_image_width__none
msgid "Hidden"
msgstr "پنهان"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__prevent_zero_price_sale
msgid "Hide 'Add To Cart' when price = 0"
msgstr "پنهان کردن 'افزودن به سبد خرید' وقتی قیمت = 0 باشد"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Hours."
msgstr "ساعت‌ها."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_image_view_kanban
msgid "Huge file size. The image should be optimized/reduced."
msgstr "سایز فایل بزرگ است. تصویر باید بهینه‌سازی/کاهش یابد."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.accept_terms_and_conditions
msgid "I agree to the"
msgstr "من موافقم با"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__id
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__id
#: model:ir.model.fields,field_description:website_sale.field_product_ribbon__id
#: model:ir.model.fields,field_description:website_sale.field_website_base_unit__id
#: model:ir.model.fields,field_description:website_sale.field_website_sale_extra_field__id
msgid "ID"
msgstr "شناسه"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_tag_form_view_inherit_website_sale
msgid "If an image is set, the color will not be used on eCommerce."
msgstr "اگر تصویری تنظیم شده باشد، رنگ در تجارت الکترونیکی استفاده نخواهد شد."

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_template__message_needaction
msgid "If checked, new messages require your attention."
msgstr ""
"اگر این گزینه را انتخاب کنید، پیام‌های جدید به توجه شما نیاز خواهند داشت."

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_template__message_has_error
#: model:ir.model.fields,help:website_sale.field_product_template__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "اگر علامت زده شود، برخی از پیام ها دارای خطای تحویل هستند."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "If product price equals 0, replace 'Add to Cart' by 'Contact us'."
msgstr ""
"اگر قیمت محصول برابر با 0 باشد، 'اضافه به سبد خرید' را با 'تماس با ما' "
"جایگزین کنید."

#. module: website_sale
#: model:mail.template,description:website_sale.mail_template_sale_cart_recovery
msgid ""
"If the setting is set, sent to authenticated visitors who abandoned their "
"cart"
msgstr ""
"اگر تنظیمات فعال باشند، به بازدیدکنندگان تأیید شده‌ای که سبد خرید خود را ترک"
" کرده‌اند ارسال شود."

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/controllers/main.py:0
msgid ""
"If you are ordering for an external person, please place your order via the "
"backend. If you wish to change your name or email address, please do so in "
"the account settings or contact your administrator."
msgstr ""
"اگر برای فردی خارجی سفارش می‌دهید، لطفاً سفارش خود را از طریق بک‌اند ثبت "
"کنید. اگر می‌خواهید نام یا آدرس ایمیل خود را تغییر دهید، لطفاً این کار را در"
" تنظیمات حساب انجام دهید یا با مدیر خود تماس بگیرید."

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_pricelist__website_id
msgid ""
"If you want a pricelist to be available on a website,you must fill in this "
"field or make it selectable.Otherwise, the pricelist will not apply to any "
"website."
msgstr ""
"اگر می خواهید لیست قیمت در یک وب سایت موجود باشد، باید این قسمت را پر کنید "
"یا آن را قابل انتخاب کنید. در غیر این صورت، لیست قیمت برای هیچ وب سایتی "
"اعمال نمی شود."

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__image_1920
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__image_1920
#: model:ir.model.fields,field_description:website_sale.field_product_tag__image
#: model_terms:ir.ui.view,arch_db:website_sale.product_searchbar_input_snippet_options
msgid "Image"
msgstr "تصویر"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__image_1024
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__image_1024
msgid "Image 1024"
msgstr "تصویر 1024"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__image_128
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__image_128
msgid "Image 128"
msgstr "تصویر 128"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__image_256
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__image_256
msgid "Image 256"
msgstr "تصویر 256"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__image_512
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__image_512
msgid "Image 512"
msgstr "تصویر 512"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Image Menu"
msgstr "منوی تصویر"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.view_product_image_form
msgid "Image Name"
msgstr "نام تصویر"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Image Spacing"
msgstr "فاصله تصویر"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Image Zoom"
msgstr "بزرگنمایی تصویر"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Images Ratio"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Images Size"
msgstr "اندازه تصاویر"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Images Subtitles"
msgstr "زیرنویس تصویرها"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Images Width"
msgstr "عرض تصاویر"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Instant checkout, instead of adding to cart"
msgstr "فوراً خرید کنید، به جای اضافه کردن به سبد خرید"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/controllers/main.py:0
msgid "Invalid Email! Please enter a valid email address."
msgstr "ایمیل نامعتبر! لطفا یک ایمیل معتبر وارد کنید."

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/controllers/main.py:0
msgid "Invalid image"
msgstr "تصویر نامعتبر"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__module_account
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Invoicing"
msgstr "صدور فاکتور"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Invoicing Policy"
msgstr "خط مشی صدور فاکتور"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__message_is_follower
msgid "Is Follower"
msgstr "آیا دنبال می کند"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_delivery_carrier__is_published
#: model:ir.model.fields,field_description:website_sale.field_product_template__is_published
#: model_terms:ir.ui.view,arch_db:website_sale.product_product_website_tree_view
#: model_terms:ir.ui.view,arch_db:website_sale.product_template_view_tree_website_sale
msgid "Is Published"
msgstr "منتشر شده است"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Issue invoices to customers"
msgstr "برای مشتریان فاکتور صادر کن"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/sale_order.py:0
msgid "It is forbidden to modify a sales order which is not in draft status."
msgstr "تغییر یک سفارش فروش که در وضعیت پیش‌نویس نیست، ممنوع است."

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/controllers/delivery.py:0
msgid ""
"It seems that a delivery method is not compatible with your address. Please "
"refresh the page and try again."
msgstr ""
"به نظر می رسد یک روش تحویل با آدرس شما سازگار نیست. لطفا صفحه را مجددا "
"بارگزاری کرده و دوباره تلاش کنید."

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/controllers/delivery.py:0
msgid ""
"It seems that there is already a transaction for your order; you can't "
"change the delivery method anymore."
msgstr ""
"به نظر می رسد که در حال حاضر یک معامله برای سفارش شما وجود دارد. شما دیگر "
"نمی توانید روش تحویل را تغییر دهید."

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/website_sale_utils.js:0
msgid "Item(s) added to your cart"
msgstr "ایتم(ها) به سبد خرید شما اضافه شد"

#. module: website_sale
#: model:ir.model,name:website_sale.model_account_move
msgid "Journal Entry"
msgstr "سند دفترروزنامه‌"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_cabinets_kitchen
msgid "Kitchen Cabinets"
msgstr ""

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_drawers_kitchen
msgid "Kitchen Drawer Units"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_digest_digest__kpi_website_sale_total_value
msgid "Kpi Website Sale Total Value"
msgstr "مقدار کل فروش سایت کی‌پی‌ای"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website_snippet_filter.py:0
msgid "Lamp"
msgstr "لامپ"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_lamps
msgid "Lamps"
msgstr "لامپها"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Landscape (4/3)"
msgstr "منظره (۴/۳)"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce_abondand
msgid "Last Month"
msgstr "ماه گذشته"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_partner__last_website_so_id
#: model:ir.model.fields,field_description:website_sale.field_res_users__last_website_so_id
msgid "Last Online Sales Order"
msgstr "آخرین سفارش فروش آنلاین"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__write_uid
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__write_uid
#: model:ir.model.fields,field_description:website_sale.field_product_ribbon__write_uid
#: model:ir.model.fields,field_description:website_sale.field_website_base_unit__write_uid
#: model:ir.model.fields,field_description:website_sale.field_website_sale_extra_field__write_uid
msgid "Last Updated by"
msgstr "آخرین بروز رسانی توسط"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__write_date
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__write_date
#: model:ir.model.fields,field_description:website_sale.field_product_ribbon__write_date
#: model:ir.model.fields,field_description:website_sale.field_website_base_unit__write_date
#: model:ir.model.fields,field_description:website_sale.field_website_sale_extra_field__write_date
msgid "Last Updated on"
msgstr "آخرین بروز رسانی در"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce_abondand
msgid "Last Week"
msgstr "هفته قبل"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce_abondand
msgid "Last Year"
msgstr "سال قبل"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_bins_laundry
msgid "Laundry Bins"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Layout"
msgstr "طرح بندی"

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__product_ribbon__position__left
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Left"
msgstr "چپ"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Let the customer enter a delivery address"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Let the customer select a Mondial Relay shipping point"
msgstr "اجازه دهید مشتری یک نقطه ارسال Mondial Relay را انتخاب کند."

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__website__add_to_cart_action__force_dialog
msgid "Let the user decide (dialog)"
msgstr "اجازه دهید کاربر تصمیم بگیرد (گفتگو)"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/tours/website_sale_shop.js:0
msgid "Let's create your first product."
msgstr "بیایید اولین محصول خود را ایجاد کنیم."

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/tours/website_sale_shop.js:0
msgid ""
"Let's now take a look at your eCommerce dashboard to get your eCommerce "
"website ready in no time."
msgstr ""
"حالا بیایید نگاهی به داشبورد تجارت الکترونیکی شما بیندازیم تا وب‌سایت تجارت "
"الکترونیکی شما را به سرعت آماده کنیم."

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website_snippet_filter.py:0
msgid "Lightbulb sold separately"
msgstr "لامپ به صورت جداگانه فروخته می‌شود"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__show_line_subtotals_tax_selection
#: model:ir.model.fields,field_description:website_sale.field_website__show_line_subtotals_tax_selection
msgid "Line Subtotals Tax Display"
msgstr "صفحه نمایش سطر فرعی مالیات"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_share_buttons
msgid "LinkedIn"
msgstr "لینکدین"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.add_grid_or_list_option
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "List"
msgstr "فهرست"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/location_selector/location_selector_dialog/location_selector_dialog.js:0
msgid "List view"
msgstr "نمایش فهرست"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Little Icons"
msgstr "ایکن‌های کوچک"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/location_selector/location_selector_dialog/location_selector_dialog.js:0
msgid "Loading..."
msgstr "بارگذاری..."

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__website__ecommerce_access__logged_in
msgid "Logged in users"
msgstr "کاربران وارد شده"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Logos"
msgstr "لوگوها"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_boxes_luxury
msgid "Luxury Boxes"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Magnifier on hover"
msgstr "ذره‌بین در هنگام شناور شدن"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid ""
"Mail only sent to signed in customers with items available for sale in their"
" cart."
msgstr ""
"فقط به مشتریانی که وارد سایت شده‌اند و در سبد خرید خود اقلامی برای فروش "
"دارند، ایمیل ارسال می‌شود."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Main Image"
msgstr "تصویر اصلی"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Manage Promotions, coupons, loyalty cards, Gift cards & eWallet"
msgstr "مدیریت پروموشن، کوپن، کارت هدیه، وفاداری و کیف پول الکترونیکی"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid ""
"Manage pricelists to apply specific prices per country, customer, products, "
"etc"
msgstr ""
"مدیریت لیست‌های قیمت برای اعمال قیمت‌های خاص به ازای هر کشور، مشتری، محصول و"
" غیره"

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__res_config_settings__account_on_checkout__mandatory
#: model:ir.model.fields.selection,name:website_sale.selection__website__account_on_checkout__mandatory
msgid "Mandatory (no guest checkout)"
msgstr "اجباری (بدون پرداخت مهمان)"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/location_selector/location_selector_dialog/location_selector_dialog.js:0
msgid "Map view"
msgstr "نمایش نقشه"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_cabinets_medicine
msgid "Medicine Cabinets"
msgstr ""

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__website__product_page_image_spacing__medium
msgid "Medium"
msgstr "متوسط"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.s_mega_menu_menu_image_menu
msgid "Mega menu default image"
msgstr "تصویر پیش‌فرض منوی مگا"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__message_has_error
msgid "Message Delivery error"
msgstr "خطای تحویل پیام"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__message_ids
msgid "Messages"
msgstr "پیام ها"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Mondial Relay"
msgstr "رله موندیال"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__module_delivery_mondialrelay
msgid "Mondial Relay Connector"
msgstr "مبدل ارتباطی Mondial Relay"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.accordion_more_information
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "More Information"
msgstr "اطلاعات بیشتر"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippets_options_web_editor
msgid "Move to first"
msgstr "ببر به اول"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippets_options_web_editor
msgid "Move to last"
msgstr "ببر به آخر"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippets_options_web_editor
msgid "Move to next"
msgstr "ببر به بعدی"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippets_options_web_editor
msgid "Move to previous"
msgstr "ببر به قبلی"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Multi Menus"
msgstr "مولتی منوها"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_multimedia
msgid "Multimedia"
msgstr "چند رسانه‌ای"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.header_cart_link
msgid "My Cart"
msgstr "سبد من"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__name
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__name
#: model:ir.model.fields,field_description:website_sale.field_website_base_unit__name
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Name"
msgstr "نام"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website.py:0
msgid "Name (A-Z)"
msgstr "نام (الفبا)"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_sale_order_line__name_short
msgid "Name Short"
msgstr "نام کوتاه"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/product_public_category.py:0
msgid "New"
msgstr "جدید"

#. module: website_sale
#: model:ir.actions.act_window,name:website_sale.product_product_action_add
msgid "New Product"
msgstr "محصول جدید"

#. module: website_sale
#: model:product.ribbon,name:website_sale.new_ribbon
msgid "New!"
msgstr "جدید!"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website.py:0
msgid "Newest Arrivals"
msgstr "جدیدترین ها"

#. module: website_sale
#: model:website.snippet.filter,name:website_sale.dynamic_filter_newest_products
msgid "Newest Products"
msgstr "محصولات جدید"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/xml/website_sale_image_viewer.xml:0
#: model_terms:ir.ui.view,arch_db:website_sale.s_dynamic_snippet_products_preview_data
msgid "Next"
msgstr "بعدی"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/xml/website_sale_image_viewer.xml:0
msgid "Next (Right-Arrow)"
msgstr "بعدی (پیکان راست)"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_drawers_nightstand
msgid "Nightstand Drawers"
msgstr ""

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/xml/website_sale_reorder_modal.xml:0
msgid "No"
msgstr "خیر"

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.action_view_abandoned_tree
msgid "No abandoned carts found"
msgstr "هیچ سبد رها شده‌ای یافت نشد"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products
msgid "No product defined"
msgstr "هیچ محصولی تعریف نشده"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products
msgid "No product defined in this category."
msgstr "هیچ محصولی در این دسته‌بندی تعریف نشده است."

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.website_sale_visitor_product_action
msgid "No product views yet for this visitor"
msgstr "هنوز هیچ نمایشی از محصول برای این بازدیدکننده وجود ندارد"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/location_selector/location_selector_dialog/location_selector_dialog.js:0
msgid "No result"
msgstr "نتیجه‌ای یافت نشد"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products
msgid "No results"
msgstr "هیچ نتیجه‌ای نبود"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products
msgid "No results for \""
msgstr "هیچ نتیجه‌ای برای \""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products
msgid "No results found for '"
msgstr "هیچ نتیجه‌ای برای این موارد یافت نشد"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/controllers/main.py:0
msgid ""
"No shipping method is available for your current order and shipping address."
" Please contact us for more information."
msgstr ""
"هیچ روش حمل و نقلی برای سفارش فعلی و آدرس حمل و نقل شما موجود نیست. لطفا "
"برای اطلاعات بیشتر با ما تماس بگیرید."

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/sale_order.py:0
msgid "No shipping method is selected."
msgstr "هیچ روش ارسال انتخاب نشده است."

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__website__product_page_image_spacing__none
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "None"
msgstr "هیچکدام"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_pages_kanban_view
msgid "Not Published"
msgstr "منتشر نشده"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/product/product.xml:0
msgid "Not available for sale"
msgstr "برای فروش موجود نیست"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/sale_variant_mixin.js:0
msgid "Not available with %s"
msgstr "موجود نیست با %s"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_crm_team__abandoned_carts_count
msgid "Number of Abandoned Carts"
msgstr "تعداد سبدهای رها شده"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__message_needaction_counter
msgid "Number of Actions"
msgstr "تعداد اقدامات"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__message_has_error_counter
msgid "Number of errors"
msgstr "تعداد خطاها"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__shop_ppr
msgid "Number of grid columns on the shop"
msgstr "تعداد ستون‌های شبکه در فروشگاه"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_template__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "تعداد پیام هایی که نیاز به اقدام دارند"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_template__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "تعداد پیامهای با خطای تحویل"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__shop_ppg
msgid "Number of products in the grid on the shop"
msgstr "تعداد محصولات در شبکه فروشگاه"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Odoo Menu"
msgstr "منوی اودو"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_desks_office
msgid "Office Desks"
msgstr "میزهای اداری"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website_snippet_filter.py:0
msgid "On wheels"
msgstr "روی چرخ‌ها"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/tours/website_sale_shop.js:0
msgid "Once you click on <b>Save</b>, your product is updated."
msgstr "هنگامی که روی <b>ذخیره</b> کلیک کنید، محصول شما به‌روزرسانی می‌شود."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "One product might have different attributes (size, color, ...)"
msgstr "یک محصول ممکن است دارای ویژگی‌های مختلفی باشد (اندازه، رنگ، ...)"

#. module: website_sale
#: model:ir.ui.menu,name:website_sale.menu_report_sales
msgid "Online Sales"
msgstr "فروش آنلاین"

#. module: website_sale
#: model:ir.actions.act_window,name:website_sale.sale_report_action_dashboard
msgid "Online Sales Analysis"
msgstr "تجزیه و تحلیل فروش آنلاین"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_sale_order__only_services
msgid "Only Services"
msgstr "فقط سرویسها"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/product_pricelist.py:0
msgid ""
"Only the company's websites are allowed.\n"
"Leave the Company field empty or select a website from that company."
msgstr ""
"فقط وب‌سایت‌های شرکت مجاز هستند.\n"
"فیلد شرکت را خالی بگذارید یا یک وب‌سایت از آن شرکت انتخاب کنید."

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/res_partner.py:0
msgid "Open Sale Orders"
msgstr "باز کردن سفارشات فروش"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.brand_promotion
msgid "Open Source eCommerce"
msgstr "تجارت الکترونیکی متن باز"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/location_selector/location/location.js:0
#: code:addons/website_sale/static/src/js/location_selector/map_container/map_container.js:0
msgid "Opening hours"
msgstr "ساعات فعالیت"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_image_view_kanban
msgid ""
"Optimization required! Reduce the image size or increase your compression "
"settings."
msgstr ""
"بهینه‌سازی لازم است! اندازه تصویر را کاهش دهید یا تنظیمات فشرده‌سازی خود را "
"افزایش دهید."

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__res_config_settings__account_on_checkout__optional
#: model:ir.model.fields.selection,name:website_sale.selection__website__account_on_checkout__optional
msgid "Optional"
msgstr "اختیاری"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Options"
msgstr "گزینه‌ها"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment_confirmation_status
msgid "Or scan me with your banking app."
msgstr "یا مرا با برنامه بانکی خود اسکن کنید."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce_abondand
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce_unpaid
msgid "Order Date"
msgstr "تاریخ سفارش"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_sale_order__website_order_line
msgid "Order Lines displayed on Website"
msgstr "نمایش خطوط سفارش در وب‌سایت"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart
msgid "Order overview"
msgstr "مرور سفارش"

#. module: website_sale
#: model:ir.actions.act_window,name:website_sale.action_orders_ecommerce
#: model:ir.ui.menu,name:website_sale.menu_orders
#: model:ir.ui.menu,name:website_sale.menu_orders_orders
msgid "Orders"
msgstr "سفارشات"

#. module: website_sale
#: model:ir.actions.act_window,name:website_sale.sale_order_action_to_invoice
msgid "Orders To Invoice"
msgstr "سفارشات برای فاکتور"

#. module: website_sale
#: model:product.ribbon,name:website_sale.out_of_stock_ribbon
msgid "Out of stock"
msgstr "ناموجود"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__parent_id
msgid "Parent Category"
msgstr "دسته‌بندی مادر"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__parent_path
msgid "Parent Path"
msgstr "مسیر مادر"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__parents_and_self
msgid "Parents And Self"
msgstr "والدین و خود"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/controllers/main.py:0
msgid "Pay now"
msgstr "اکنون بپرداز"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website.py:0
msgid "Payment"
msgstr "پرداخت"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "Payment Information"
msgstr "اطلاعات پرداخت"

#. module: website_sale
#: model:ir.ui.menu,name:website_sale.menu_ecommerce_payment_methods
msgid "Payment Methods"
msgstr "روشهای پرداخت"

#. module: website_sale
#: model:ir.ui.menu,name:website_sale.menu_ecommerce_payment_providers
msgid "Payment Providers"
msgstr "سرویس دهندگان پرداخت"

#. module: website_sale
#: model:ir.model,name:website_sale.model_payment_token
msgid "Payment Token"
msgstr "توکن پرداخت"

#. module: website_sale
#: model:ir.ui.menu,name:website_sale.menu_ecommerce_payment_tokens
msgid "Payment Tokens"
msgstr "توکن های پرداخت"

#. module: website_sale
#: model:ir.ui.menu,name:website_sale.menu_ecommerce_payment_transactions
msgid "Payment Transactions"
msgstr "تراکنش های پرداخت"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/controllers/payment.py:0
msgid "Payment is already being processed."
msgstr "پرداخت در حال پردازش است."

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website_snippet_filter.py:0
msgid "Pedal-based opening system"
msgstr "سیستم باز شدن مبتنی بر پدال"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "Phone"
msgstr "تلفن"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/website_sale_form_editor.js:0
msgid "Phone Number"
msgstr "شماره تلفن"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Pills"
msgstr ""
"Pills\n"
"حبات"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_share_buttons
msgid "Pinterest"
msgstr "پینترست"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.view_product_image_form
msgid "Please enter a valid Video URL."
msgstr "لطفاً یک URL ویدئوی معتبر وارد کنید."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart
msgid "Please proceed your current cart."
msgstr "لطفاً سبد خرید فعلی خود را ادامه دهید."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Pop-up on Click"
msgstr "روی کلیک پاپ آپ"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Portrait (4/5)"
msgstr "پرتره (۴/۵)"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_ribbon__position
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Position"
msgstr "موقعیت"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__website_sale_prevent_zero_price_sale
msgid "Prevent Sale of Zero Priced Product"
msgstr "جلوگیری از فروش محصول با قیمت صفر"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/xml/website_sale_image_viewer.xml:0
#: model_terms:ir.ui.view,arch_db:website_sale.s_dynamic_snippet_products_preview_data
msgid "Previous"
msgstr "قبلی"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/xml/website_sale_image_viewer.xml:0
msgid "Previous (Left-Arrow)"
msgstr "قبلی (پیکان-چپ)"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/xml/website_sale_reorder_modal.xml:0
#: model_terms:ir.ui.view,arch_db:website_sale.product_searchbar_input_snippet_options
msgid "Price"
msgstr "قیمت"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website.py:0
msgid "Price - High to Low"
msgstr "قیمت - نزولی"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website.py:0
msgid "Price - Low to High"
msgstr "قیمت - صعودی"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Price Filter"
msgstr "فیلتر قیمت"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__base_unit_price
#: model:ir.model.fields,field_description:website_sale.field_product_template__base_unit_price
msgid "Price Per Unit"
msgstr "قیمت به ازای هر واحد"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__pricelist_ids
msgid "Price list available for this Ecommerce/Website"
msgstr "اینترنت/وب سایت این لیست قیمت در دسترس است"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.total
msgid "Price will be updated after choosing a delivery method"
msgstr "قیمت پس از انتخاب روش ارسال به روز می شود"

#. module: website_sale
#: model:ir.model,name:website_sale.model_product_pricelist
msgid "Pricelist"
msgstr "لیست قیمت"

#. module: website_sale
#: model:ir.model,name:website_sale.model_product_pricelist_item
msgid "Pricelist Rule"
msgstr "قانون لیست قیمت"

#. module: website_sale
#: model:ir.ui.menu,name:website_sale.menu_catalog_pricelists
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Pricelists"
msgstr "لیست های قیمت"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Prices displayed on your eCommerce"
msgstr "قیمت‌هایی که در فروشگاه اینترنتی شما نمایش داده می‌شوند"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "Print"
msgstr "چاپ"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/combo_configurator_dialog/combo_configurator_dialog.xml:0
#: code:addons/website_sale/static/src/js/product_configurator_dialog/product_configurator_dialog.xml:0
msgid "Proceed to Checkout"
msgstr "به پرداخت ادامه دهید"

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.action_unpaid_orders_ecommerce
#: model_terms:ir.actions.act_window,help:website_sale.action_view_unpaid_quotation_tree
msgid "Process the order once the payment is received."
msgstr "پردازش سفارش هنگامی که پرداخت دریافت شد."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Prod. Desc."
msgstr "توضیحات محصول"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/xml/website_sale_reorder_modal.xml:0
#: model:ir.model,name:website_sale.model_product_template
#: model:ir.model.fields,field_description:website_sale.field_website_track__product_id
#: model_terms:ir.ui.view,arch_db:website_sale.products
#: model_terms:ir.ui.view,arch_db:website_sale.s_add_to_cart_options
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
#: model_terms:ir.ui.view,arch_db:website_sale.website_sale_visitor_page_view_search
msgid "Product"
msgstr "محصول"

#. module: website_sale
#: model:ir.actions.server,name:website_sale.dynamic_snippet_accessories_action
msgid "Product Accessories"
msgstr "ولوازم جانبی محصول"

#. module: website_sale
#: model:ir.model,name:website_sale.model_product_attribute
msgid "Product Attribute"
msgstr "مشخصه محصول"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.shop_product_carousel
msgid "Product Carousel"
msgstr "چرخ فلک محصول"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
msgid "Product Category"
msgstr "دسته بندی محصول"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__module_website_sale_comparison
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Product Comparison Tool"
msgstr "ابزار مقایسه محصول"

#. module: website_sale
#: model:ir.model,name:website_sale.model_product_document
msgid "Product Document"
msgstr "سند محصول"

#. module: website_sale
#: model:ir.model,name:website_sale.model_product_image
msgid "Product Image"
msgstr "تصویر محصول"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_image_view_kanban
#: model_terms:ir.ui.view,arch_db:website_sale.view_product_image_form
msgid "Product Images"
msgstr "تصاویر محصول"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product
msgid "Product Name"
msgstr "نام محصول"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.s_dynamic_snippet_products_template_options
msgid "Product Names"
msgstr "نام‌های محصول"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Product Page"
msgstr "صفحه محصول"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.view_website_sale_website_form
msgid "Product Page Extra Fields"
msgstr "صفحه محصول فیلدهای اضافی"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__product_page_grid_columns
msgid "Product Page Grid Columns"
msgstr "ستون‌های گرید صفحه محصول"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__product_page_image_layout
msgid "Product Page Image Layout"
msgstr "چیدمان تصویر صفحه محصول"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__product_page_image_spacing
msgid "Product Page Image Spacing"
msgstr "فضای بین تصاویر صفحه محصول"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__product_page_image_width
msgid "Product Page Image Width"
msgstr "عرض تصویر صفحه محصول"

#. module: website_sale
#: model:ir.actions.act_window,name:website_sale.action_product_pages_list
msgid "Product Pages"
msgstr "صفحات محصول"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_public_category_tree_view
msgid "Product Public Categories"
msgstr "طبقه‌بندی‌های عمومی محصول"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Product Reference Price"
msgstr "قیمت مرجع محصول"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_ribbon_view_tree
msgid "Product Ribbon"
msgstr "روبان محصول"

#. module: website_sale
#: model:ir.actions.act_window,name:website_sale.product_ribbon_action
#: model:ir.ui.menu,name:website_sale.product_catalog_product_ribbons
msgid "Product Ribbons"
msgstr "روبان‌های محصول"

#. module: website_sale
#: model:ir.model,name:website_sale.model_product_tag
msgid "Product Tag"
msgstr "برچسب محصول"

#. module: website_sale
#: model:ir.ui.menu,name:website_sale.product_catalog_product_tags
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Product Tags"
msgstr "برچسپ محصول"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Product Tags Filter"
msgstr "فیلتر برچسب‌های محصول"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__product_tmpl_id
msgid "Product Template"
msgstr "قالب محصول"

#. module: website_sale
#: model:ir.model,name:website_sale.model_product_template_attribute_line
msgid "Product Template Attribute Line"
msgstr "خط ویژگی الگوی محصول"

#. module: website_sale
#: model:ir.model,name:website_sale.model_product_template_attribute_value
msgid "Product Template Attribute Value"
msgstr "ویژگی ارزش الگوی محصول"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__product_tmpl_ids
msgid "Product Tmpl"
msgstr "قالب محصول"

#. module: website_sale
#: model:ir.model,name:website_sale.model_product_product
#: model:ir.model.fields,field_description:website_sale.field_product_image__product_variant_id
msgid "Product Variant"
msgstr "گونه محصول"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Product Variants"
msgstr "گونه های محصول"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_visitor__visitor_product_count
#: model_terms:ir.ui.view,arch_db:website_sale.website_sale_visitor_view_form
msgid "Product Views"
msgstr "نمایش محصولات"

#. module: website_sale
#: model:ir.actions.act_window,name:website_sale.website_sale_visitor_product_action
msgid "Product Views History"
msgstr "تاریخچه نمایش‌های محصول"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/controllers/main.py:0
msgid "Product not found"
msgstr "محصول یافت نشد"

#. module: website_sale
#: model:ir.model,name:website_sale.model_product_ribbon
msgid "Product ribbon"
msgstr "روبان محصول"

#. module: website_sale
#: model:ir.actions.act_window,name:website_sale.product_template_action_website
#: model:ir.ui.menu,name:website_sale.menu_catalog
#: model:ir.ui.menu,name:website_sale.menu_catalog_products
#: model:ir.ui.menu,name:website_sale.menu_product_pages
#: model_terms:ir.ui.view,arch_db:website_sale.product_searchbar_input_snippet_options
#: model_terms:ir.ui.view,arch_db:website_sale.products_breadcrumb
#: model_terms:ir.ui.view,arch_db:website_sale.snippets
#: model_terms:ir.ui.view,arch_db:website_sale.website_sale_visitor_page_view_search
#: model_terms:ir.ui.view,arch_db:website_sale.website_sale_visitor_view_form
#: model_terms:ir.ui.view,arch_db:website_sale.website_sale_visitor_view_tree
msgid "Products"
msgstr "محصولات"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Products List"
msgstr "لیست محصولات"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Products Page"
msgstr "صفحه محصولات"

#. module: website_sale
#: model:ir.actions.server,name:website_sale.dynamic_snippet_recently_sold_with_action
msgid "Products Recently Sold With"
msgstr "محصولاتی که اخیراً فروخته شده‌اند با"

#. module: website_sale
#: model:website.snippet.filter,name:website_sale.dynamic_filter_cross_selling_recently_sold_with
msgid "Products Recently Sold With Product"
msgstr "محصولات اخیراً فروخته شده با محصول"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_visitor__product_count
msgid "Products Views"
msgstr "نمایش محصولات"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Promo Code"
msgstr "کد تخفیف"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/product_image.py:0
msgid ""
"Provided video URL for '%s' is not valid. Please enter a valid video URL."
msgstr ""
"آدرس ویدئویی که برای '%s' ارائه شده معتبر نیست. لطفاً یک آدرس ویدئویی معتبر "
"وارد کنید."

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_document__shown_on_product_page
msgid "Publish on website"
msgstr "منتشرشده در سایت"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_pages_kanban_view
#: model_terms:ir.ui.view,arch_db:website_sale.product_template_search_view_website
#: model_terms:ir.ui.view,arch_db:website_sale.view_delivery_carrier_search
msgid "Published"
msgstr "منتشر شده"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Push down"
msgstr "پایین کشیدن"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Push to bottom"
msgstr "کشیدن به ته"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Push to top"
msgstr "کشیدن به بالا"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Push up"
msgstr "بالا کشیدن"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/xml/website_sale_reorder_modal.xml:0
msgid "Quantity"
msgstr "تعداد"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Radio"
msgstr "رادیو"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Rating"
msgstr "رتبه دهی"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__rating_avg_text
msgid "Rating Avg Text"
msgstr "متن میانگین رتبه‌بندی"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__rating_last_feedback
msgid "Rating Last Feedback"
msgstr "رتبه بندی آخرین بازخورد"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__rating_last_image
msgid "Rating Last Image"
msgstr "رتبه بندی آخرین تصویر"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__rating_last_value
msgid "Rating Last Value"
msgstr "رتبه‌بندی آخرین مقدار"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__rating_percentage_satisfaction
msgid "Rating Satisfaction"
msgstr "رتبه‌بندی رضایت"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__rating_last_text
msgid "Rating Text"
msgstr "متن رتبه‌بندی"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__rating_count
msgid "Rating count"
msgstr "تعداد رتبه بندی"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__rating_ids
msgid "Ratings"
msgstr "رتبه‌ها"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/xml/website_sale_reorder_modal.xml:0
msgid "Re-Order"
msgstr "سفارش مجدد"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
#: model_terms:ir.ui.view,arch_db:website_sale.snippets_options_web_editor
msgid "Re-order"
msgstr "سفارش مجدد"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__website_sale_enabled_portal_reorder_button
#: model:ir.model.fields,field_description:website_sale.field_website__enabled_portal_reorder_button
msgid "Re-order From Portal"
msgstr "سفارش مجدد از پورتال"

#. module: website_sale
#: model:ir.actions.server,name:website_sale.dynamic_snippet_latest_sold_products_action
#: model:website.snippet.filter,name:website_sale.dynamic_filter_latest_sold_products
msgid "Recently Sold Products"
msgstr "محصولات اخیراً فروخته‌شده"

#. module: website_sale
#: model:ir.actions.server,name:website_sale.dynamic_snippet_latest_viewed_products_action
#: model:website.snippet.filter,name:website_sale.dynamic_filter_latest_viewed_products
msgid "Recently Viewed Products"
msgstr "اخیراً مشاهده شده‌ها"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_furnitures_recliners
msgid "Recliners"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce_abondand
msgid "Recovery Email Sent"
msgstr "ایمیل بازیابی ارسال شد"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce_abondand
msgid "Recovery Email to Send"
msgstr "ارسال ایمیل بازیابی"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website_snippet_filter.py:0
msgid "Reinforced for heavy loads"
msgstr "برای بارهای سنگین تقویت شده"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_services_relocation_and_moving
msgid "Relocation and Moving"
msgstr "جابجایی و انتقال"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart_lines
#: model_terms:ir.ui.view,arch_db:website_sale.snippets_options_web_editor
msgid "Remove"
msgstr "حذف"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Remove all"
msgstr "حذف همه"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart_lines
msgid "Remove from cart"
msgstr "حذف از سبد"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/xml/website_sale_reorder_modal.xml:0
#: model_terms:ir.ui.view,arch_db:website_sale.cart_lines
#: model_terms:ir.ui.view,arch_db:website_sale.product_quantity
msgid "Remove one"
msgstr "حذف یکی"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_services_repair_and_maintenance
msgid "Repair and Maintenance"
msgstr "نگهداری و تعمیرات"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Replace"
msgstr "جایگزین"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Reset Categories"
msgstr "حذف دسته‌بندی‌ها"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_delivery_carrier__website_id
#: model:ir.model.fields,help:website_sale.field_product_product__website_id
#: model:ir.model.fields,help:website_sale.field_product_public_category__website_id
#: model:ir.model.fields,help:website_sale.field_product_tag__website_id
#: model:ir.model.fields,help:website_sale.field_product_template__website_id
msgid "Restrict to a specific website."
msgstr "محدود به یک وب سایت خاص."

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/sale_order.py:0
msgid "Resume Order"
msgstr "از سرگیری سفارش"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website.py:0
msgid "Review Order"
msgstr "مرور سفارش"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__website_ribbon_id
#: model:ir.model.fields,field_description:website_sale.field_product_template__website_ribbon_id
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Ribbon"
msgstr "روبن"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/website_sale.editor.js:0
#: model:ir.model.fields,field_description:website_sale.field_product_ribbon__name
msgid "Ribbon Name"
msgstr "نام روبان"

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__product_ribbon__position__right
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Right"
msgstr "راست"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_boxes_rustic
msgid "Rustic Boxes"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__is_seo_optimized
#: model:ir.model.fields,field_description:website_sale.field_product_template__is_seo_optimized
msgid "SEO optimized"
msgstr "بهینه سازی شده سئو"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__message_has_sms_error
msgid "SMS Delivery error"
msgstr "خطای تحویل پیامک"

#. module: website_sale
#: model:product.ribbon,name:website_sale.sale_ribbon
msgid "Sale"
msgstr "فروش"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_graph_website
msgid "Sale Analysis"
msgstr "تجزیه و تحلیل فروش"

#. module: website_sale
#: model:ir.actions.act_window,name:website_sale.sale_report_action_carts
#: model_terms:ir.ui.view,arch_db:website_sale.digest_digest_view_form
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
msgid "Sales"
msgstr "فروش"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_pivot_website
msgid "Sales Analysis"
msgstr "تحلیل فروش"

#. module: website_sale
#: model:ir.model,name:website_sale.model_sale_report
msgid "Sales Analysis Report"
msgstr "گزارش تحلیل فروش"

#. module: website_sale
#: model:ir.model,name:website_sale.model_sale_order
msgid "Sales Order"
msgstr "سفارش فروش"

#. module: website_sale
#: model:ir.model,name:website_sale.model_sale_order_line
msgid "Sales Order Line"
msgstr "سطر سفارش‌فروش"

#. module: website_sale
#: model:ir.model,name:website_sale.model_crm_team
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__salesteam_id
#: model:ir.model.fields,field_description:website_sale.field_website__salesteam_id
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Sales Team"
msgstr "تیم فروش"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__salesperson_id
#: model:ir.model.fields,field_description:website_sale.field_website__salesperson_id
msgid "Salesperson"
msgstr "فروشنده"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.billing_address_row
msgid "Same as delivery address"
msgstr "همان آدرس تحویل"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_add_to_cart
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_banner
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_borderless_1
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_borderless_2
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_card_group
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_centered
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_horizontal_card
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_horizontal_card_2
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_mini_image
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_mini_name
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_mini_price
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_view_detail
msgid "Sample"
msgstr "نمونه"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "Save address"
msgstr "ذخیره آدرس"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce_abondand
msgid "Search Abandoned Sales Orders"
msgstr "جستجوی سفارشات فروش رها شده"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Search Bar"
msgstr "جستجوی خط"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Select"
msgstr "انتخاب"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/tours/website_sale_shop.js:0
msgid ""
"Select <b>New Product</b> to create it and manage its properties to boost "
"your sales."
msgstr ""
"برای ایجاد <b>محصول جدید</b> را انتخاب کنید و خصوصیات آن را برای افزایش فروش"
" آن مدیریت کنید."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Select Quantity"
msgstr "انتخاب تعداد"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.delivery_method
msgid "Select a location"
msgstr "انتخاب موقعیت مکانی"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_sale_pricelist_form_view
msgid "Select a website"
msgstr "انتخاب وبسایت"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_pricelist__selectable
msgid "Selectable"
msgstr "گزینش پذیر"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_order_view_form_cart_recovery
msgid "Send a Recovery Email"
msgstr "ارسال ایمیل بازیابی"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Send after"
msgstr "ارسال بعد"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_order_view_form_cart_recovery
msgid "Send by Email"
msgstr "ارسال از طریق ایمیل"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__send_abandoned_cart_email
msgid "Send email to customers who abandoned their cart."
msgstr "ارسال ایمیل به مشتریانی که سبد خرید خود را رها کرده‌اند."

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__seo_name
#: model:ir.model.fields,field_description:website_sale.field_product_template__seo_name
msgid "Seo name"
msgstr "نام سئو"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__sequence
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__sequence
#: model:ir.model.fields,field_description:website_sale.field_website_sale_extra_field__sequence
msgid "Sequence"
msgstr "دنباله"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_services
msgid "Services"
msgstr "محصولات"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_share_buttons
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Share"
msgstr "اشتراک‌گذاری"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__group_delivery_invoice_address
msgid "Shipping Address"
msgstr "نشانی ارسال"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Shipping Costs"
msgstr "هزینه های ارسال"

#. module: website_sale
#: model:ir.model,name:website_sale.model_delivery_carrier
msgid "Shipping Methods"
msgstr "روش ارسال"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Shiprocket"
msgstr "Shiprocket"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Shiprocket Shipping Methods"
msgstr "روش‌های حمل و نقل Shiprocket"

#. module: website_sale
#: model:website.menu,name:website_sale.menu_shop
msgid "Shop"
msgstr "فروشگاه"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.checkout
msgid "Shop - Checkout"
msgstr "فروشگاه - تسویه حساب"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Shop - Checkout Process"
msgstr "فروشگاه - فرآیند پرداخت"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "Shop - Confirmed"
msgstr "فروشگاه - تأیید شده"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Shop - Products"
msgstr "فروشگاه - محصولات"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment
msgid "Shop - Select Payment Method"
msgstr "فروشگاه - انتخاب روش پرداخت"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__shop_default_sort
msgid "Shop Default Sort"
msgstr "مرتب‌سازی پیش‌فرض فروشگاه"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Shop, products, cart and wishlist visibility"
msgstr "مشاهده فروشگاه، محصولات، سبد خرید و لیست علاقه مندی ها"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products_add_to_cart
msgid "Shopping cart"
msgstr "سبد خرید"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Show B2B Fields"
msgstr "نمایش فیلدهای B2B"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Show Empty"
msgstr "نمایش خالی"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_document_search
msgid "Show on Ecommerce"
msgstr "نمایش در تجارت الکترونیک"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.s_dynamic_snippet_products_template_options
msgid "Show variants"
msgstr "نمایش گونه‌ها"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Show/hide shopping cart"
msgstr "نمایش/پنهان کردن سبد خرید"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website.py:0
msgid "Sign In"
msgstr "ورود"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "Sign Up"
msgstr "ثبت نام"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "Sign in"
msgstr "ورود به سیستم"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Sign in/up at checkout"
msgstr "ورود/ثبت‌نام در هنگام تسویه حساب"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website_snippet_filter.py:0
msgid "Sit comfortably"
msgstr "در راحتی بنشینید"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Size"
msgstr "اندازه"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__website_size_x
#: model:ir.model.fields,field_description:website_sale.field_product_template__website_size_x
msgid "Size X"
msgstr "اندازه X"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__website_size_y
#: model:ir.model.fields,field_description:website_sale.field_product_template__website_size_y
msgid "Size Y"
msgstr "اندازه Y"

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__website__product_page_image_spacing__small
msgid "Small"
msgstr "کوچک"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_furnitures_sofas
msgid "Sofas"
msgstr "کاناپه"

#. module: website_sale
#: model:product.ribbon,name:website_sale.sold_out_ribbon
msgid "Sold out"
msgstr "ناموجود"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/controllers/main.py:0
msgid "Some required fields are empty."
msgstr "بعضی فیلدهای اجباری خالی هستند."

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/controllers/main.py:0
msgid "Sorry, we are unable to ship your order."
msgstr "با عرض پوزش، ما نمی توانیم سفارش شما را ارسال کنیم"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Sort by"
msgstr "مرتب‌سازی بر اساس"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_product_normal_website_form_view
#: model_terms:ir.ui.view,arch_db:website_sale.product_product_view_form_easy_inherit_website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_template_only_website_form_view
msgid "Specify unit"
msgstr "مشخص کردن واحد"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.s_mega_menu_menus_logos
msgid "Spring collection has arrived!"
msgstr "کلکسیون بهاری وارد شده است!"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_boxes_stackable
msgid "Stackable Boxes"
msgstr "جعبه نگهداری"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_desks_standing
msgid "Standing Desks"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "State / Province"
msgstr "ایالت / استان"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
msgid "Status"
msgstr "وضعیت"

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__website__add_to_cart_action__stay
msgid "Stay on Product Page"
msgstr "در صفحه محصول بمانید"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_cabinets_storage
msgid "Storage Cabinets"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "Street and Number"
msgstr "خیابان و شماره"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Style"
msgstr "استایل"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.total
msgid "Subtotal"
msgstr "جمع جزء"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_product__alternative_product_ids
#: model:ir.model.fields,help:website_sale.field_product_template__alternative_product_ids
msgid ""
"Suggest alternatives to your customer (upsell strategy). Those products show"
" up on the product page."
msgstr ""
"پیشنهاد گزینه‌های جایگزین به مشتری خود (استراتژی فروش بیشتر). این محصولات در"
" صفحه محصول نمایش داده می‌شوند."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Suggested Accessories"
msgstr "لوازم پیشنهادی"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.suggested_products_list
msgid "Suggested accessories"
msgstr "لوازم پیشنهادی"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_template_form_view
msgid "Suggested accessories in the eCommerce cart"
msgstr "پیشنهاد شده لوازم جانبی در سبد خرید تجارت الکترونیک"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.s_dynamic_snippet_products_template_options
msgid "Tags"
msgstr "برچسب‌ها"

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__website__show_line_subtotals_tax_selection__tax_excluded
#: model_terms:ir.ui.view,arch_db:website_sale.tax_indication
msgid "Tax Excluded"
msgstr "بدون مالیات"

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__website__show_line_subtotals_tax_selection__tax_included
#: model_terms:ir.ui.view,arch_db:website_sale.tax_indication
msgid "Tax Included"
msgstr "شامل مالیات"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Tax Indication"
msgstr "نشان مالیات"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_sale_order__amount_delivery
msgid "Tax included or excluded depending on the website configuration."
msgstr "پیکربندی وب‌سایت تعیین می‌کند که مالیات شامل یا مستثنی شود."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.total
msgid "Taxes"
msgstr "مالیات ها"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Text"
msgstr "متن"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_ribbon__text_color
msgid "Text Color"
msgstr "رنگ متن"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website__prevent_zero_price_sale_text
msgid "Text to show instead of price"
msgstr "متنی که به جای قیمت نمایش داده می‌شود"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "Thank you for your order."
msgstr "از سفارش شما سپاس گذاریم."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.brand_promotion
msgid "The #1"
msgstr "شماره #1"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/controllers/payment.py:0
msgid "The access token is invalid."
msgstr "توکن دسترسی نامعتبر است."

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/controllers/payment.py:0
msgid "The cart has already been paid. Please refresh the page."
msgstr "سبد خرید قبلا پرداخت شده است. لطفا صفحه را رفرش کنید"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/controllers/payment.py:0
msgid "The cart has been updated. Please refresh the page."
msgstr "سبد خرید به‌روز شد. لطفاً صفحه را تازه‌سازی کنید."

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/sale_order.py:0
msgid ""
"The company of the website you are trying to sell from (%(website_company)s)"
" is different than the one you want to use (%(company)s)"
msgstr ""
"شرکت وب‌سایتی که می‌خواهید از (%(website_company)s) بفروشید با (%(company)s)"
" که می‌خواهید از آن استفاده کنید متفاوت است"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_delivery_carrier__website_url
#: model:ir.model.fields,help:website_sale.field_product_product__website_url
#: model:ir.model.fields,help:website_sale.field_product_template__website_url
msgid "The full URL to access the document through the website."
msgstr "آدرس URL کامل برای دسترسی سند در وبسایت."

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/sale_order.py:0
msgid ""
"The given combination does not exist therefore it cannot be added to cart."
msgstr ""
"ترکیب داده شده وجود ندارد، بنابراین نمی‌توان آن را به سبد خرید اضافه کرد."

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/sale_order.py:0
msgid "The given product does not exist therefore it cannot be added to cart."
msgstr ""
"محصول داده شده وجود ندارد بنابراین نمی‌توان آن را به سبد خرید اضافه کرد."

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/controllers/combo_configurator.py:0
#: code:addons/website_sale/models/sale_order.py:0
msgid ""
"The given product does not have a price therefore it cannot be added to "
"cart."
msgstr ""
"محصول داده شده قیمتی ندارد بنابراین نمی‌توان آن را به سبد خرید اضافه کرد."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid ""
"The mode selected here applies as invoicing policy of any new product "
"created but not of products already existing."
msgstr ""
"انتخاب حالت در اینجا به عنوان سیاست صدور فاکتور برای هر محصول جدید ایجاد‌شده"
" اعمال می‌شود، اما برای محصولات موجود در حال حاضر اعمال نمی‌شود."

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/controllers/payment.py:0
msgid "The order has been cancelled."
msgstr "سفارش لغو شده است."

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_product__public_categ_ids
#: model:ir.model.fields,help:website_sale.field_product_template__public_categ_ids
msgid ""
"The product will be available in each mentioned eCommerce category. Go to "
"Shop > Edit Click on the page and enable 'Categories' to view all eCommerce "
"categories."
msgstr ""
"محصول در هر دسته‌بندی تجارت الکترونیک ذکر شده در دسترس خواهد بود. به فروشگاه"
" بروید و > ویرایش کلیک کنید و صفحه را فعال کنید تا 'دسته‌بندی‌ها' تمام "
"دسته‌بندی‌های تجارت الکترونیک را مشاهده کنید."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.s_mega_menu_images_subtitles
#: model_terms:ir.ui.view,arch_db:website_sale.s_mega_menu_little_icons
msgid "The team"
msgstr "تیم"

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.action_view_abandoned_tree
msgid "The time to mark a cart as abandoned can be changed in the settings."
msgstr ""
"می‌توان زمان علامت‌گذاری سبد خرید به عنوان رها شده را در تنظیمات تغییر داد."

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/product_product.py:0
msgid ""
"The value of Base Unit Count must be greater than 0. Use 0 to hide the price"
" per unit on this product."
msgstr ""
"مقدار شمارش واحد پایه باید بزرگتر از 0 باشد. از 0 برای مخفی کردن قیمت هر "
"واحد در این محصول استفاده کنید."

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.action_orders_ecommerce
msgid "There is no confirmed order from the website"
msgstr "از وبسایت هیچ سفارش تایید شده‌ای وجود ندارد"

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.action_unpaid_orders_ecommerce
#: model_terms:ir.actions.act_window,help:website_sale.action_view_unpaid_quotation_tree
msgid "There is no unpaid order from the website yet"
msgstr "هنوز هیچ سفارشی پرداخت نشده از سایت موجود نیست"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/location_selector/map_container/map_container.js:0
msgid "There was an error loading the map"
msgstr "هنگام بارگیری نقشه خطایی روی داد"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product
msgid "This combination does not exist."
msgstr "این ترکیب وجود ندارد"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.accordion_more_information
msgid "This content will be shared across all product pages."
msgstr "این محتوا در تمام صفحات محصول به اشتراک گذاشته خواهد شد."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart
msgid "This is your current cart."
msgstr "این سبد خرید فعلی شماست."

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/res_partner.py:0
msgid ""
"This partner has an open cart. Please note that the pricelist will not be "
"updated on that cart. Also, the cart might not be visible for the customer "
"until you update the pricelist of that cart."
msgstr ""
"این شریک یک سبد خرید باز دارد. لطفاً توجه داشته باشید که فهرست قیمت در آن "
"سبد به‌روزرسانی نخواهد شد. همچنین، این امکان وجود دارد که سبد برای مشتری "
"قابل دیدن نباشد تا زمانی که فهرست قیمت آن سبد را به‌روزرسانی کنید."

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/snippets/s_add_to_cart/000.js:0
msgid "This product does not exist therefore it cannot be added to cart."
msgstr "این محصول وجود ندارد، بنابراین نمی‌توان آن را به سبد خرید اضافه کرد."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product
msgid "This product has no valid combination."
msgstr "این محصول هیچ ترکیب معتبری ندارد"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product
msgid "This product is no longer available."
msgstr "این محصول دیگر موجود نیست."

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/website_sale_reorder.js:0
msgid "This product is not available for purchase."
msgstr "این محصول برای خرید در دسترس نیست."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products_item
msgid "This product is unpublished."
msgstr "این محصول منتشر نشده است."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.coupon_form
msgid "This promo code is not available."
msgstr "کد تبلیغاتی موجود نیست."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.s_dynamic_snippet_products_preview_data
msgid "Three-Seat Sofa"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Thumbnails"
msgstr "تصاویر کوچک"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid ""
"To send invitations in B2B mode, open a contact or select several ones in "
"list view and click on 'Portal Access Management' option in the dropdown "
"menu *Action*."
msgstr ""
"برای ارسال دعوت نامه‌ها در حالت B2B، یک مخاطب را باز کنید یا چند مورد را در "
"لیست‌مشاهده انتخاب کنید و روی گزینه 'Portal Access Management' در منوی "
"بازشدنی اقدام کلیک کنید."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Top"
msgstr "بالا"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Top Bar"
msgstr "نوار بالا"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/xml/website_sale_reorder_modal.xml:0
msgid "Total"
msgstr "مجموع"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_website_visitor__product_count
msgid "Total number of product viewed"
msgstr "تعداد کل محصولات مشاهده شده"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_website_visitor__visitor_product_count
msgid "Total number of views on products"
msgstr "تعداد کل بازدیدها از محصولات"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/combo_configurator_dialog/combo_configurator_dialog.js:0
#: code:addons/website_sale/static/src/js/product_list/product_list.js:0
msgid "Total: %s"
msgstr "جمع کل: %s"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_lamps_touch
msgid "Touch Lamps"
msgstr ""

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_bins_toy
msgid "Toy Bins"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_website_snippet_filter__product_cross_selling
msgid ""
"True only for product filters that require a product_id because they relate "
"to cross selling"
msgstr ""
"برای فیلترهای محصولی که نیاز به شناسه محصول دارند درست است زیرا به فروش "
"متقابل مربوط می‌شوند"

#. module: website_sale
#: model:res.groups,name:website_sale.group_show_uom_price
msgid "UOM Price Display for eCommerce"
msgstr "نمایش قیمت واحد (UOM) برای تجارت الکترونیکی"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "UPS"
msgstr "یو پی اس"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_image__video_url
msgid "URL of a video for showcasing your product."
msgstr "آدرس اینترنتی یک ویدیو برای نمایش محصول شما."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "USPS"
msgstr "اداره پست ایالات متحده آمریکا"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "USPS Shipping Methods"
msgstr "روش های حمل و نقل USPS"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_drawers_underbed
msgid "Under-bed Drawers"
msgstr ""

#. module: website_sale
#: model:ir.model,name:website_sale.model_website_base_unit
msgid "Unit of Measure for price per unit on eCommerce products."
msgstr "واحد اندازه برای قیمت به ازای هر واحد در محصولات تجارت الکترونیک."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.view_sales_order_filter_ecommerce
msgid "Unpaid"
msgstr "مرخصی بدون حقوق"

#. module: website_sale
#: model:ir.actions.act_window,name:website_sale.action_unpaid_orders_ecommerce
#: model:ir.actions.act_window,name:website_sale.action_view_unpaid_quotation_tree
#: model:ir.ui.menu,name:website_sale.menu_orders_unpaid_orders
msgid "Unpaid Orders"
msgstr "سفارش‌های پرداخت نشده"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_product_view_form_normalized
#: model_terms:ir.ui.view,arch_db:website_sale.products_item
msgid "Unpublished"
msgstr "منتشر نشده"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/tours/website_sale_shop.js:0
msgid "Upload a file from your local library."
msgstr "از کتابخانه محلی خود یک فایل بارگذاری کنید."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "Use Google Places API to validate addresses entered by your visitors"
msgstr ""
"از Google Places API برای اعتبارسنجی آدرس‌های وارد شده توسط بازدیدکنندگان "
"خود استفاده کنید"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/controllers/main.py:0
msgid "VAT"
msgstr "ارزش افزوده"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.s_add_to_cart_options
msgid "Variant"
msgstr "گونه"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__variant_ribbon_id
msgid "Variant Ribbon"
msgstr "روبان نوع‌بندی"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Variants"
msgstr "گونه ها"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Vertical (2/3)"
msgstr "عمودی (2/3)"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_image__video_url
#: model_terms:ir.ui.view,arch_db:website_sale.view_product_image_form
msgid "Video URL"
msgstr "URL ویدیو"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_banner
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_centered
msgid "View Product"
msgstr "مشاهده محصول"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/notification/add_to_cart_notification/add_to_cart_notification.xml:0
msgid "View cart"
msgstr "مشاهده سبد خرید"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.dynamic_filter_template_product_product_view_detail
msgid "View product"
msgstr "دیدن محصول"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/xml/website_sale_image_viewer.xml:0
msgid "Viewer"
msgstr "بیننده"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_boxes_vintage
msgid "Vintage Boxes"
msgstr ""

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_multimedia_virtual_design
msgid "Virtual Design Tools"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_attribute__visibility
msgid "Visibility"
msgstr "قابلیت مشاهده"

#. module: website_sale
#: model:ir.model.fields.selection,name:website_sale.selection__product_attribute__visibility__visible
msgid "Visible"
msgstr "نمايان"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_delivery_carrier__website_published
#: model:ir.model.fields,field_description:website_sale.field_product_template__website_published
msgid "Visible on current website"
msgstr "قابل دید در وبسایت حاضر"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_tag__visible_on_ecommerce
msgid "Visible on eCommerce"
msgstr "قابل مشاهده در تجارت الکترونیک"

#. module: website_sale
#: model:ir.model,name:website_sale.model_website_track
msgid "Visited Pages"
msgstr "صفحات بازدید شده"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_website_visitor__product_ids
#: model_terms:ir.ui.view,arch_db:website_sale.website_sale_visitor_view_kanban
msgid "Visited Products"
msgstr "محصولات بازدید شده"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_sale_visitor_page_view_graph
msgid "Visitor Product Views"
msgstr "بازدیدکنندگان محصول"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.website_sale_visitor_page_view_tree
msgid "Visitor Product Views History"
msgstr "تاریخچه بازدیدهای محصول بازدیدکنندگان"

#. module: website_sale
#: model:product.public.category,name:website_sale.public_category_furnitures_wardrobes
msgid "Wardrobes"
msgstr ""

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/website_sale_utils.js:0
#: model:ir.model.fields,field_description:website_sale.field_sale_order__shop_warning
#: model:ir.model.fields,field_description:website_sale.field_sale_order_line__shop_warning
#: model_terms:ir.ui.view,arch_db:website_sale.cart_lines
#: model_terms:ir.ui.view,arch_db:website_sale.checkout_layout
msgid "Warning"
msgstr "هشدار"

#. module: website_sale
#: model:product.template,name:website_sale.product_product_1_product_template
msgid "Warranty"
msgstr "وضیعت گارانتی"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.s_dynamic_snippet_products_preview_data
msgid "Warranty (2 year)"
msgstr "وارانتی (2 سال)"

#. module: website_sale
#: model:product.template,description_sale:website_sale.product_product_1_product_template
msgid ""
"Warranty, issued to the purchaser of an article by its manufacturer, "
"promising to repair or replace it if necessary within a specified period of "
"time."
msgstr ""
"گارانتی، که به خریدار یک محصول توسط سازنده آن صادر می‌شود و وعده می‌دهد که "
"در صورت لزوم در یک بازه زمانی مشخص، آن را تعمیر یا تعویض کند."

#. module: website_sale
#: model:ir.model,name:website_sale.model_website
#: model:ir.model.fields,field_description:website_sale.field_account_bank_statement_line__website_id
#: model:ir.model.fields,field_description:website_sale.field_account_move__website_id
#: model:ir.model.fields,field_description:website_sale.field_delivery_carrier__website_id
#: model:ir.model.fields,field_description:website_sale.field_product_pricelist__website_id
#: model:ir.model.fields,field_description:website_sale.field_product_product__website_id
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__website_id
#: model:ir.model.fields,field_description:website_sale.field_product_tag__website_id
#: model:ir.model.fields,field_description:website_sale.field_product_template__website_id
#: model:ir.model.fields,field_description:website_sale.field_sale_order__website_id
#: model:ir.model.fields,field_description:website_sale.field_sale_report__website_id
#: model:ir.model.fields,field_description:website_sale.field_website_sale_extra_field__website_id
#: model_terms:ir.ui.view,arch_db:website_sale.sale_report_view_search_website
msgid "Website"
msgstr "تارنما"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_product_view_form_normalized
msgid "Website Category"
msgstr "دسته‌بندی وبسایت"

#. module: website_sale
#: model:ir.model,name:website_sale.model_website_menu
msgid "Website Menu"
msgstr "منوی وبسایت"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_template__website_message_ids
msgid "Website Messages"
msgstr "پیام های وب سایت"

#. module: website_sale
#: model:ir.model,name:website_sale.model_product_public_category
#: model:ir.model.fields,field_description:website_sale.field_product_product__public_categ_ids
#: model:ir.model.fields,field_description:website_sale.field_product_template__public_categ_ids
msgid "Website Product Category"
msgstr "دسته بندی محصولات وب سایت"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_public_category_form_view
msgid "Website Public Categories"
msgstr "دسته بندی های عمومی وب سایت"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__website_sequence
#: model:ir.model.fields,field_description:website_sale.field_product_template__website_sequence
msgid "Website Sequence"
msgstr "توالی وب سایت"

#. module: website_sale
#: model:ir.actions.act_url,name:website_sale.action_open_website
msgid "Website Shop"
msgstr "فروشگاه وب سایت"

#. module: website_sale
#: model:ir.model,name:website_sale.model_website_snippet_filter
msgid "Website Snippet Filter"
msgstr "فیلتر قطعه وب سایت"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_delivery_carrier__website_url
#: model:ir.model.fields,field_description:website_sale.field_product_product__website_url
#: model:ir.model.fields,field_description:website_sale.field_product_template__website_url
msgid "Website URL"
msgstr "URL وبسایت"

#. module: website_sale
#: model:ir.model,name:website_sale.model_website_visitor
msgid "Website Visitor"
msgstr "بازدید کننده وبسایت"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_template__website_message_ids
msgid "Website communication history"
msgstr "تاریخچه ارتباط با وبسایت"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__website_meta_description
#: model:ir.model.fields,field_description:website_sale.field_product_template__website_meta_description
msgid "Website meta description"
msgstr "توضیحات متا وبسایت"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__website_meta_keywords
#: model:ir.model.fields,field_description:website_sale.field_product_template__website_meta_keywords
msgid "Website meta keywords"
msgstr "کلیدواژه‌های متا وبسایت"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__website_meta_title
#: model:ir.model.fields,field_description:website_sale.field_product_template__website_meta_title
msgid "Website meta title"
msgstr "عنوان متا وبسایت"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_public_category__website_meta_og_img
#: model:ir.model.fields,field_description:website_sale.field_product_template__website_meta_og_img
msgid "Website opengraph image"
msgstr "تصویر opengraph وب سایت"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_account_bank_statement_line__website_id
#: model:ir.model.fields,help:website_sale.field_account_move__website_id
msgid "Website through which this invoice was created for eCommerce orders."
msgstr ""
"وب سایتی که از طریق آن این فاکتور برای سفارشات تجارت الکترونیک ایجاد شده "
"است."

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_sale_order__website_id
msgid "Website through which this order was placed for eCommerce orders."
msgstr ""
"وب سایتی که از طریق آن این سفارش برای سفارشات تجارت الکترونیک انجام شده است."

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_crm_team__website_ids
msgid "Websites"
msgstr "وبسایتها"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "What should be done on \"Add to Cart\"?"
msgstr "در «افزودن به سبد خرید» چه باید کرد؟"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_share_buttons
msgid "WhatsApp"
msgstr "واتس‌اَپ"

#. module: website_sale
#: model:ir.model.fields,help:website_sale.field_product_tag__visible_on_ecommerce
msgid "Whether the tag is displayed on the eCommerce."
msgstr "آیا برچسب در فروشگاه الکترونیکی نمایش داده شود."

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website_snippet_filter.py:0
msgid "Whiteboard"
msgstr "وایت برد"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Wide (16/9)"
msgstr ""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "Wider (21/9)"
msgstr ""

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_res_config_settings__module_website_sale_wishlist
msgid "Wishlists"
msgstr "لیست آرزوها"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website_snippet_filter.py:0
msgid "With three feet"
msgstr "با سه پا(قدم)"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/xml/website_sale_reorder_modal.xml:0
msgid "Yes"
msgstr "بله"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid ""
"You are editing your <b>delivery and billing</b> addresses\n"
"                                        at the same time!<br/>\n"
"                                        If you want to modify your billing address, create a"
msgstr ""
"شما در حال ویرایش آدرس‌های <b>تحویل و فاکتور</b> \n"
"                                        به صورت همزمان هستید!<br/>\n"
"                                        اگر می خواهید آدرس صورتحساب خود را تغییر دهید،  ایجاد کنید"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/controllers/main.py:0
msgid "You can't use a video as the product's main image."
msgstr "شما نمی‌توانید از ویدیو به عنوان تصویر اصلی محصول استفاده کنید."

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.sale_report_action_carts
#: model_terms:ir.actions.act_window,help:website_sale.sale_report_action_dashboard
msgid "You don't have any order from the website"
msgstr "شما هیچ سفارشی از وب سایت ندارید"

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.sale_order_action_to_invoice
msgid "You don't have any order to invoice from the website"
msgstr "شما هیچ سفارشی برای فاکتور از وب سایت ندارید"

#. module: website_sale
#: model:mail.template,subject:website_sale.mail_template_sale_cart_recovery
msgid "You left items in your cart!"
msgstr "شما موارد را در سبد خرید خود جا گذاشته اید!"

#. module: website_sale
#: model_terms:ir.actions.act_window,help:website_sale.action_view_abandoned_tree
msgid ""
"You'll find here all the carts abandoned by your visitors.\n"
"                If they completed their address, you should send them a recovery email!"
msgstr ""
"در اینجا تمام چرخ دستی های رها شده توسط بازدیدکنندگان خود را خواهید یافت.\n"
"       اگر آدرس خود را تکمیل کردند، باید یک ایمیل بازیابی برای آنها ارسال کنید!"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.alternative_products
msgid ""
"Your Dynamic Snippet will be displayed here...\n"
"                                This message is displayed because youy did not provide both a filter and a template to use."
msgstr ""
"قطعه پویا شما در اینجا نمایش داده می شود...\n"
"این پیام به این دلیل نمایش داده می شود که شما هم فیلتر و هم الگوی استفاده را ارائه نکرده اید."

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/website_sale_form_editor.js:0
msgid "Your Email"
msgstr "ایمیل شما"

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/website_sale_form_editor.js:0
msgid "Your Name"
msgstr "نام"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
#: model_terms:ir.ui.view,arch_db:website_sale.billing_address_row
msgid "Your address"
msgstr "نشانی شما"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart_lines
#: model_terms:ir.ui.view,arch_db:website_sale.checkout_layout
msgid "Your cart is empty!"
msgstr "سبد خرید شما خالی است!"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/controllers/delivery.py:0
msgid "Your cart is empty."
msgstr "سبد خرید شما خالی است!"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/sale_order.py:0
msgid "Your cart is not ready to be paid, please verify previous steps."
msgstr "سبد خرید شما آماده پرداخت نیست، لطفاً مراحل قبلی را بررسی کنید."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.payment_confirmation_status
msgid "Your payment has been authorized."
msgstr "این پرداخت مجاز است."

#. module: website_sale
#. odoo-javascript
#: code:addons/website_sale/static/src/js/location_selector/location_selector_dialog/location_selector_dialog.js:0
msgid "Your postal code"
msgstr "کد پستی شما"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart
msgid "Your previous cart has already been completed."
msgstr "سبد خرید قبلی شما قبلا تکمیل شده است."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "Zip Code"
msgstr "کد پستی"

#. module: website_sale
#: model:ir.ui.menu,name:website_sale.menu_delivery_zip_prefix
msgid "Zip Prefix"
msgstr "پیشوند زیپ"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "bpost"
msgstr "پست بلژیک"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.res_config_settings_view_form
msgid "bpost Shipping Methods"
msgstr "روش های ارسال bpost"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.s_dynamic_snippet_products_template_options
msgid "e.g. lamp,bin"
msgstr "مثال: لامپ، سطل"

#. module: website_sale
#. odoo-python
#: code:addons/website_sale/models/website.py:0
#: model:ir.ui.menu,name:website_sale.menu_ecommerce
#: model:ir.ui.menu,name:website_sale.menu_ecommerce_settings
msgid "eCommerce"
msgstr "تجارت الکترونیکی"

#. module: website_sale
#: model:ir.actions.act_window,name:website_sale.product_public_category_action
#: model:ir.ui.menu,name:website_sale.menu_catalog_categories
#: model_terms:ir.ui.view,arch_db:website_sale.snippet_options
msgid "eCommerce Categories"
msgstr "دسته‌بندی‌های تجارت الکترونیک"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_product_product__description_ecommerce
#: model:ir.model.fields,field_description:website_sale.field_product_template__description_ecommerce
msgid "eCommerce Description"
msgstr "توضیحات تجارت الکترونیک"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_attribute_view_form
msgid "eCommerce Filter"
msgstr "فیلتر تجارت الکترونیک"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.attribute_tree_view
msgid "eCommerce Filter Visibility"
msgstr "قابلیت مشاهده فیلتر تجارت الکترونیک"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.product_template_form_view
msgid "eCommerce Media"
msgstr "رسانه تجارت الکترونیک"

#. module: website_sale
#: model:ir.model.fields,field_description:website_sale.field_digest_digest__kpi_website_sale_total
msgid "eCommerce Sales"
msgstr "فروش تجارت الکترونیک"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.header_cart_link
msgid "eCommerce cart"
msgstr "سبد خرید تجارت الکترونیک"

#. module: website_sale
#: model:ir.actions.server,name:website_sale.ir_cron_send_availability_email_ir_actions_server
msgid "eCommerce: send email to customers about their abandoned cart"
msgstr ""
"بازرگانی الکترونیکی: ارسال ایمیل به مشتریان درباره سبد خرید رها شده آنها"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart
msgid "if you want to merge your previous cart into current cart."
msgstr "اگر می‌خواهید سبد خرید قبلی خود را با سبد خرید فعلی ادغام کنید."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart
msgid ""
"if you want to restore your previous cart. Your current cart will be "
"replaced with your previous cart."
msgstr ""
"اگر می‌خواهید سبد خرید قبلی خود را بازگردانید. سبد خرید فعلی شما با سبد خرید"
" قبلی شما جایگزین خواهد شد."

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.products
msgid "in category \""
msgstr "در دسته‌بندی \""

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.address
msgid "new address"
msgstr "آدرس جدید"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.cart_lines
msgid "remove"
msgstr "حذف"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.accept_terms_and_conditions
msgid "terms &amp; conditions"
msgstr "شرایط و ضوابط"

#. module: website_sale
#: model_terms:ir.ui.view,arch_db:website_sale.confirmation
msgid "to follow your order."
msgstr "تا سفارش شما را دنبال کند"
