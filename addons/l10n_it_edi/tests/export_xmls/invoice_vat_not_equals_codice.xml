<p:FatturaElettronica xmlns:p="http://ivaservizi.agenziaentrate.gov.it/docs/xsd/fatture/v1.2"
                      xmlns:ds="http://www.w3.org/2000/09/xmldsig#"
                      xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                      xsi:schemaLocation="http://ivaservizi.agenziaentrate.gov.it/docs/xsd/fatture/v1.2 http://www.fatturapa.gov.it/export/fatturazione/sdi/fatturapa/v1.2/Schema_del_file_xml_FatturaPA_versione_1.2.xsd"
                      versione="FPR12">
    <FatturaElettronicaHeader>
        <DatiTrasmissione>
            <IdTrasmittente>
                <IdPaese>IT</IdPaese>
                <IdCodice>07149930583</IdCodice>
            </IdTrasmittente>
            <ProgressivoInvio>V202200001</ProgressivoInvio>
            <FormatoTrasmissione>FPR12</FormatoTrasmissione>
            <CodiceDestinatario>0000000</CodiceDestinatario>
            <ContattiTrasmittente>
                <Telefono>0266766700</Telefono>
                <Email><EMAIL></Email>
            </ContattiTrasmittente>
        </DatiTrasmissione>
        <CedentePrestatore>
            <DatiAnagrafici>
                <IdFiscaleIVA>
                    <IdPaese>IT</IdPaese>
                    <IdCodice>01698911003</IdCodice>
                </IdFiscaleIVA>
                <CodiceFiscale>07149930583</CodiceFiscale>
                <Anagrafica>
                    <Denominazione>company_2_data</Denominazione>
                </Anagrafica>
                <RegimeFiscale>RF01</RegimeFiscale>
            </DatiAnagrafici>
            <Sede>
                <Indirizzo>1234 Test Street</Indirizzo>
                <CAP>12345</CAP>
                <Comune>Prova</Comune>
                <Nazione>IT</Nazione>
            </Sede>
        </CedentePrestatore>
        <CessionarioCommittente>
            <DatiAnagrafici>
                <IdFiscaleIVA>
                    <IdPaese>IT</IdPaese>
                    <IdCodice>00465840031</IdCodice>
                </IdFiscaleIVA>
                <CodiceFiscale>93026890017</CodiceFiscale>
                <Anagrafica>
                    <Denominazione>Alessi</Denominazione>
                </Anagrafica>
            </DatiAnagrafici>
            <Sede>
                <Indirizzo>Via Privata Alessi 6</Indirizzo>
                <CAP>28887</CAP>
                <Comune>Milan</Comune>
                <Nazione>IT</Nazione>
            </Sede>
        </CessionarioCommittente>
    </FatturaElettronicaHeader>
    <FatturaElettronicaBody>
        <DatiGenerali>
            <DatiGeneraliDocumento>
                <TipoDocumento>TD01</TipoDocumento>
                <Divisa>EUR</Divisa>
                <Data>2022-03-24</Data>
                <Numero>INV/2022/00001</Numero>
                <ImportoTotaleDocumento>976.49</ImportoTotaleDocumento>
            </DatiGeneraliDocumento>
        </DatiGenerali>
        <DatiBeniServizi>
            <DettaglioLinee>
                <NumeroLinea>1</NumeroLinea>
                <Descrizione>line1</Descrizione>
                <Quantita>1.00</Quantita>
                <PrezzoUnitario>800.40000000</PrezzoUnitario>
                <PrezzoTotale>800.40000000</PrezzoTotale>
                <AliquotaIVA>22.00</AliquotaIVA>
            </DettaglioLinee>
            <DatiRiepilogo>
                <AliquotaIVA>22.00</AliquotaIVA>
                <ImponibileImporto>800.40</ImponibileImporto>
                <Imposta>176.09</Imposta>
                <EsigibilitaIVA>I</EsigibilitaIVA>
            </DatiRiepilogo>
        </DatiBeniServizi>
        <DatiPagamento>
            <CondizioniPagamento>TP02</CondizioniPagamento>
            <DettaglioPagamento>
                <ModalitaPagamento>MP05</ModalitaPagamento>
                <DataScadenzaPagamento>2022-03-24</DataScadenzaPagamento>
                <ImportoPagamento>976.49</ImportoPagamento>
                <CodicePagamento>INV/2022/00001</CodicePagamento>
            </DettaglioPagamento>
        </DatiPagamento>
    </FatturaElettronicaBody>
</p:FatturaElettronica>
