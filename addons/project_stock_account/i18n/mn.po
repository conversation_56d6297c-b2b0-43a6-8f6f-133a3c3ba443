# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* project_stock_account
# 
# Translators:
# <PERSON><PERSON><PERSON><PERSON> <baskhu<PERSON><PERSON><PERSON><PERSON>@gmail.com>, 2024
# <AUTHOR> <EMAIL>, 2024
# <PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:56+0000\n"
"PO-Revision-Date: 2024-09-29 00:00+0000\n"
"Last-Translator: <PERSON>, 2024\n"
"Language-Team: Mongolian (https://app.transifex.com/odoo/teams/41243/mn/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: mn\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: project_stock_account
#. odoo-python
#: code:addons/project_stock_account/models/stock_move.py:0
msgid ""
"'%(missing_plan_names)s' analytic plan(s) required on the project "
"'%(project_name)s' linked to the stock picking."
msgstr ""

#. module: project_stock_account
#: model:ir.model.fields,field_description:project_stock_account.field_stock_picking_type__analytic_costs
msgid "Analytic Costs"
msgstr ""

#. module: project_stock_account
#: model:ir.model,name:project_stock_account.model_account_analytic_line
msgid "Analytic Line"
msgstr "Шинжилгээний мөр"

#. module: project_stock_account
#: model:ir.model,name:project_stock_account.model_account_analytic_applicability
msgid "Analytic Plan's Applicabilities"
msgstr ""

#. module: project_stock_account
#: model:ir.model.fields,field_description:project_stock_account.field_account_analytic_line__category
msgid "Category"
msgstr "Ангилал"

#. module: project_stock_account
#: model:ir.model.fields,field_description:project_stock_account.field_account_analytic_applicability__business_domain
msgid "Domain"
msgstr "Домэйн"

#. module: project_stock_account
#: model:ir.model.fields.selection,name:project_stock_account.selection__account_analytic_line__category__picking_entry
msgid "Inventory Transfer"
msgstr ""

#. module: project_stock_account
#. odoo-python
#: code:addons/project_stock_account/models/project_project.py:0
msgid "Materials"
msgstr ""

#. module: project_stock_account
#: model:ir.model,name:project_stock_account.model_stock_picking_type
msgid "Picking Type"
msgstr "Баримтын төрөл"

#. module: project_stock_account
#: model:ir.model,name:project_stock_account.model_project_project
msgid "Project"
msgstr "Төсөл"

#. module: project_stock_account
#: model:ir.model,name:project_stock_account.model_stock_move
msgid "Stock Move"
msgstr "Барааны хөдөлгөөн"

#. module: project_stock_account
#: model:ir.model.fields.selection,name:project_stock_account.selection__account_analytic_applicability__business_domain__stock_picking
msgid "Stock Picking"
msgstr "Агуулахын баримт"

#. module: project_stock_account
#: model:ir.model.fields,help:project_stock_account.field_stock_picking_type__analytic_costs
msgid ""
"Validating stock pickings will generate analytic entries for the selected "
"project. Products set for re-invoicing will also be billed to the customer."
msgstr ""
