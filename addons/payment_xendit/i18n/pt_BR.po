# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* payment_xendit
# 
# Translators:
# <PERSON><PERSON><PERSON>, 2024
# <PERSON>, 2024
# Wil <PERSON>, 2024
# <PERSON><PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:56+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2024\n"
"Language-Team: Portuguese (Brazil) (https://app.transifex.com/odoo/teams/41243/pt_BR/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: pt_BR\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: payment_xendit
#. odoo-python
#: code:addons/payment_xendit/models/payment_transaction.py:0
msgid ""
"An error occurred during the processing of your payment (%s). Please try "
"again."
msgstr ""
"Ocorreu um erro durante o processamento de seu pagamento (%s). Tente "
"novamente."

#. module: payment_xendit
#: model_terms:ir.ui.view,arch_db:payment_xendit.inline_form
msgid "Card Code"
msgstr "Código do cartão"

#. module: payment_xendit
#: model_terms:ir.ui.view,arch_db:payment_xendit.inline_form
msgid "Card Holder First Name"
msgstr "Nome do titular do cartão"

#. module: payment_xendit
#: model_terms:ir.ui.view,arch_db:payment_xendit.inline_form
msgid "Card Holder Last Name"
msgstr "Sobrenome do titular do cartão"

#. module: payment_xendit
#: model_terms:ir.ui.view,arch_db:payment_xendit.inline_form
msgid "Card Number"
msgstr "Número do cartão"

#. module: payment_xendit
#: model:ir.model.fields,field_description:payment_xendit.field_payment_provider__code
msgid "Code"
msgstr "Código"

#. module: payment_xendit
#. odoo-python
#: code:addons/payment_xendit/models/payment_provider.py:0
msgid "Could not establish the connection to the API."
msgstr "Não foi possível estabelecer a conexão com a API."

#. module: payment_xendit
#: model_terms:ir.ui.view,arch_db:payment_xendit.inline_form
msgid "Email"
msgstr "E-mail"

#. module: payment_xendit
#: model_terms:ir.ui.view,arch_db:payment_xendit.inline_form
msgid "Expiration"
msgstr "Expiração"

#. module: payment_xendit
#. odoo-javascript
#: code:addons/payment_xendit/static/src/js/payment_form.js:0
msgid "Invalid CVN"
msgstr "CVN inválido"

#. module: payment_xendit
#. odoo-javascript
#: code:addons/payment_xendit/static/src/js/payment_form.js:0
msgid "Invalid Card Number"
msgstr "Número de cartão inválido"

#. module: payment_xendit
#. odoo-javascript
#: code:addons/payment_xendit/static/src/js/payment_form.js:0
msgid "Invalid Date"
msgstr "Data inválida"

#. module: payment_xendit
#: model_terms:ir.ui.view,arch_db:payment_xendit.inline_form
msgid "John"
msgstr "João"

#. module: payment_xendit
#: model_terms:ir.ui.view,arch_db:payment_xendit.inline_form
msgid "MM"
msgstr "MM"

#. module: payment_xendit
#. odoo-python
#: code:addons/payment_xendit/models/payment_transaction.py:0
msgid "No transaction found matching reference %s."
msgstr "Nenhuma transação encontrada com a referência %s."

#. module: payment_xendit
#: model:ir.model,name:payment_xendit.model_payment_provider
msgid "Payment Provider"
msgstr "Provedor de serviços de pagamento"

#. module: payment_xendit
#: model:ir.model,name:payment_xendit.model_payment_transaction
msgid "Payment Transaction"
msgstr "Transação do pagamento"

#. module: payment_xendit
#. odoo-javascript
#: code:addons/payment_xendit/static/src/js/payment_form.js:0
msgid "Payment processing failed"
msgstr "Falha no processamento do pagamento"

#. module: payment_xendit
#: model_terms:ir.ui.view,arch_db:payment_xendit.inline_form
msgid "Phone Number"
msgstr "Número de telefone"

#. module: payment_xendit
#: model_terms:ir.ui.view,arch_db:payment_xendit.payment_provider_form_xendit
msgid "Public Key"
msgstr "Chave pública"

#. module: payment_xendit
#. odoo-python
#: code:addons/payment_xendit/models/payment_transaction.py:0
msgid "Received data with missing reference."
msgstr "Dados recebidos com referência ausente."

#. module: payment_xendit
#: model_terms:ir.ui.view,arch_db:payment_xendit.payment_provider_form_xendit
msgid "Secret Key"
msgstr "Chave secreta"

#. module: payment_xendit
#: model_terms:ir.ui.view,arch_db:payment_xendit.inline_form
msgid "Smith"
msgstr "Silva"

#. module: payment_xendit
#. odoo-python
#: code:addons/payment_xendit/models/payment_provider.py:0
msgid ""
"The communication with the API failed. Xendit gave us the following "
"information: '%s'"
msgstr ""
"A comunicação com a API falhou. O Xendit nos forneceu as seguintes "
"informações: '%s'"

#. module: payment_xendit
#: model:ir.model.fields,help:payment_xendit.field_payment_provider__code
msgid "The technical code of this payment provider."
msgstr "O código técnico deste provedor de pagamento."

#. module: payment_xendit
#. odoo-python
#: code:addons/payment_xendit/models/payment_transaction.py:0
msgid "The transaction is not linked to a token."
msgstr "A transação não está vinculada a um token."

#. module: payment_xendit
#: model_terms:ir.ui.view,arch_db:payment_xendit.payment_provider_form_xendit
msgid "Webhook Token"
msgstr "Token do webhook"

#. module: payment_xendit
#: model:ir.model.fields.selection,name:payment_xendit.selection__payment_provider__code__xendit
msgid "Xendit"
msgstr "Xendit"

#. module: payment_xendit
#: model:ir.model.fields,field_description:payment_xendit.field_payment_provider__xendit_public_key
msgid "Xendit Public Key"
msgstr "Chave pública do Xendit"

#. module: payment_xendit
#: model:ir.model.fields,field_description:payment_xendit.field_payment_provider__xendit_secret_key
msgid "Xendit Secret Key"
msgstr "Xendit - Chave secreta"

#. module: payment_xendit
#: model:ir.model.fields,field_description:payment_xendit.field_payment_provider__xendit_webhook_token
msgid "Xendit Webhook Token"
msgstr "Xendit - Token do webhook"

#. module: payment_xendit
#: model_terms:ir.ui.view,arch_db:payment_xendit.inline_form
msgid "YYYY"
msgstr "AAAA"

#. module: payment_xendit
#: model_terms:ir.ui.view,arch_db:payment_xendit.inline_form
msgid "<EMAIL>"
msgstr "<EMAIL>"
