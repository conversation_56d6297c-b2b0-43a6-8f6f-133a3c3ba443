<?xml version="1.0" encoding="UTF-8" ?>
<templates xml:space="preserve">

    <t t-name="website_slides.SlideXPProgressBar" owl="1">
        <div class="progress">
            <div class="progress-bar" t-att-class="{'level-up': props.levelUp}" role="progressbar"
                 t-att-aria-valuenow="props.previousRank.progress" aria-valuemin="0" aria-valuemax="100" aria-label="Progress bar"
                 t-attf-style="width: #{state.rankProgressPercentage}%"/>
        </div>
        <small class="float-start text-primary fw-bold" t-att-class="{'d-none': state.hideRankBounds}"
               t-out="state.rankLowerBound"/>
        <small t-if="state.rankUpperBound" class="float-end fw-bold"
               t-att-class="{'d-none': state.hideRankBounds}" t-out="state.rankUpperBound"/>
        <div class="o_wlides_xp_progressbar_tooltip tooltip fade show bs-tooltip-top position-relative" t-att-class="{'level-up': props.levelUp}" role="tooltip"
             t-attf-style="left: #{state.rankProgressPercentage}%">
            <div class="tooltip-arrow"/>
            <div class="tooltip-inner d-flex justify-content-center w-100" t-out="state.userKarma"/>
        </div>
    </t>

</templates>
