# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_qr_code_sepa
# 
# Translators:
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 08:39+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Thai (https://app.transifex.com/odoo/teams/41243/th/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: th\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: account_qr_code_sepa
#: model:ir.model,name:account_qr_code_sepa.model_res_partner_bank
msgid "Bank Accounts"
msgstr "บัญชีธนาคาร"

#. module: account_qr_code_sepa
#. odoo-python
#: code:addons/account_qr_code_sepa/models/res_bank.py:0
msgid "Can't generate a SEPA QR Code with the %s currency."
msgstr "ไม่สามารถสร้างรหัส QR SEPA ด้วยสกุลเงิน %s ได้"

#. module: account_qr_code_sepa
#. odoo-python
#: code:addons/account_qr_code_sepa/models/res_bank.py:0
msgid "Can't generate a SEPA QR code if the account type isn't IBAN."
msgstr "ไม่สามารถสร้างรหัส QR SEPA ได้ หากประเภทบัญชีไม่ใช่ IBAN"

#. module: account_qr_code_sepa
#. odoo-python
#: code:addons/account_qr_code_sepa/models/res_bank.py:0
msgid "Can't generate a SEPA QR code with a non SEPA iban."
msgstr "ไม่สามารถสร้าง QR โค้ด SEPA ด้วย iban ที่ไม่ใช่ SEPA ได้"

#. module: account_qr_code_sepa
#. odoo-python
#: code:addons/account_qr_code_sepa/models/res_bank.py:0
msgid "SEPA Credit Transfer QR"
msgstr "QR การโอนเครดิต SEPA"

#. module: account_qr_code_sepa
#. odoo-python
#: code:addons/account_qr_code_sepa/models/res_bank.py:0
msgid ""
"The account receiving the payment must have an account holder name or "
"partner name set."
msgstr ""
"บัญชีที่รับชำระเงินจะต้องมีชื่อเจ้าของบัญชีหรือชื่อพาร์ทเนอร์ที่ได้ตั้งค่าไว้"
