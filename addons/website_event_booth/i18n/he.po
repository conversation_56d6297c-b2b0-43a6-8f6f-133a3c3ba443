# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_event_booth
# 
# Translators:
# yael terner, 2024
# <PERSON>, 2024
# <AUTHOR> <EMAIL>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON> <<EMAIL>>, 2024
# or balmas, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-03-19 20:37+0000\n"
"PO-Revision-Date: 2024-09-25 09:42+0000\n"
"Last-Translator: or balmas, 2025\n"
"Language-Team: Hebrew (https://app.transifex.com/odoo/teams/41243/he/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: he\n"
"Plural-Forms: nplurals=3; plural=(n == 1 && n % 1 == 0) ? 0 : (n == 2 && n % 1 == 0) ? 1: 2;\n"

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_registration_details
msgid ""
"<i class=\"fa fa-exclamation-triangle me-2\" role=\"img\" aria-label=\"Error\" title=\"Error\"/>\n"
"                    <span class=\"o_wbooth_registration_error_message\"/>"
msgstr ""

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_registration
msgid ""
"<i class=\"fa fa-exclamation-triangle\"/>\n"
"                                        <span>Sorry, several booths are now sold out. Please change your choices before validating again.</span>"
msgstr ""

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_registration
msgid "<i class=\"fa fa-map-o me-1\"/>View Plan"
msgstr "<i class=\"fa fa-map-o me-1\"/> צפייה בתוכנית"

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_layout
msgid "<span class=\"fa fa-gear me-1\"/> Configure Booths"
msgstr "<span class=\"fa fa-gear me-1\"/> הגדר דוכנים"

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_registration
msgid "<span class=\"text-nowrap\">Sold Out</span>"
msgstr "<span class=\"text-nowrap\">אזל</span>"

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_registration
msgid "<span>Book my Booth<small>(s)</small></span>"
msgstr ""

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_registration_details
msgid "<span>Book my Booths</span>"
msgstr "<span>הזמנת הדוכנים שלי</span>"

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_order_progress
msgid ""
"<span>Booth Selection</span><span class=\"fa fa-angle-right d-inline-block "
"align-middle mx-2 mx-lg-3 opacity-75\"/>"
msgstr ""

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_order_progress
msgid "<span>Confirmed</span>"
msgstr "<span>מאושר</span>"

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_registration_details
msgid ""
"<span>Email</span>\n"
"                            <span> *</span>"
msgstr ""
"<span>מייל</span>\n"
"                            <span> *</span>"

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_registration_details
msgid ""
"<span>Name</span>\n"
"                            <span> *</span>"
msgstr ""
"<span>שם</span>\n"
"                            <span> *</span>"

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_type_view_form
msgid "Booth Menu Item"
msgstr "פריט תפריט דוכן"

#. module: website_event_booth
#: model:ir.model.fields,field_description:website_event_booth.field_event_event__booth_menu
msgid "Booth Register"
msgstr "רישום דוכן"

#. module: website_event_booth
#. odoo-javascript
#: code:addons/website_event_booth/static/src/xml/event_booth_registration_templates.xml:0
msgid "Booth Registration completed!"
msgstr "רישום דוכן הסתיים!"

#. module: website_event_booth
#. odoo-javascript
#: code:addons/website_event_booth/static/src/js/booth_register.js:0
msgid "Booth registration failed."
msgstr "רישום דוכן נכשל."

#. module: website_event_booth
#: model:ir.model.fields,field_description:website_event_booth.field_event_type__booth_menu
msgid "Booths on Website"
msgstr "דוכנים באתר"

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_layout
msgid "Check our"
msgstr ""

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_registration
msgid "Choose your type of booth"
msgstr "בחר את סוג הדוכן"

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_registration_details
msgid "Contact Details"
msgstr "פרטי איש קשר"

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_order_progress
msgid ""
"Contact Details<span class=\"fa fa-angle-right d-inline-block align-middle "
"mx-2 mx-lg-3 opacity-75\"/>"
msgstr ""

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_registration
msgid "Contact Us"
msgstr "צור קשר"

#. module: website_event_booth
#: model:ir.model,name:website_event_booth.model_event_event
msgid "Event"
msgstr "אירוע"

#. module: website_event_booth
#: model:ir.model.fields.selection,name:website_event_booth.selection__website_event_menu__menu_type__booth
msgid "Event Booth Menus"
msgstr "תפריטי דוכן אירוע"

#. module: website_event_booth
#: model:ir.model.fields,field_description:website_event_booth.field_event_event__booth_menu_ids
msgid "Event Booths Menus"
msgstr "תפריטי דוכני אירוע"

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_layout
msgid "Event Finished"
msgstr ""

#. module: website_event_booth
#: model:ir.model,name:website_event_booth.model_event_type
msgid "Event Template"
msgstr "תבנית אירוע"

#. module: website_event_booth
#: model:ir.model.fields,field_description:website_event_booth.field_event_event__exhibition_map
msgid "Exhibition Map"
msgstr "מפת אולם"

#. module: website_event_booth
#. odoo-python
#: code:addons/website_event_booth/models/event_event.py:0
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_registration
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_registration_details
msgid "Get A Booth"
msgstr "מציאת דוכן"

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_registration_details
msgid "Go back"
msgstr "חזור"

#. module: website_event_booth
#. odoo-javascript
#: code:addons/website_event_booth/static/src/js/booth_register.js:0
msgid "It looks like your email is linked to an existing account."
msgstr ""

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_layout
msgid "It's no longer possible to book a booth."
msgstr ""

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_layout
msgid "Link to list of future events"
msgstr ""

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_layout
msgid "List of Future Events"
msgstr ""

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_registration
msgid "Location"
msgstr "מיקום"

#. module: website_event_booth
#: model:ir.model.fields,field_description:website_event_booth.field_website_event_menu__menu_type
msgid "Menu Type"
msgstr "סוג תפריט"

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_registration_details
msgid "Phone"
msgstr "טלפון"

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_registration_details
msgid "Please Sign In."
msgstr ""

#. module: website_event_booth
#. odoo-javascript
#: code:addons/website_event_booth/static/src/js/booth_register.js:0
msgid "Please fill out the form correctly."
msgstr ""

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_layout
msgid "Registration Not Open."
msgstr ""

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_registration
msgid "Sorry, all the booths are sold out."
msgstr "סליחה, כל הדוכנים הוזמנו."

#. module: website_event_booth
#. odoo-javascript
#: code:addons/website_event_booth/static/src/js/booth_register.js:0
msgid "The booth category doesn't exist."
msgstr ""

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_layout
msgid "This event is not open to exhibitors registration at this time."
msgstr ""

#. module: website_event_booth
#: model:ir.model,name:website_event_booth.model_website_event_menu
msgid "Website Event Menu"
msgstr "תפריט אירועים באתר האינטרנט"

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_registration
msgid "if you have any question."
msgstr "אם יש לך שאלות."

#. module: website_event_booth
#: model_terms:ir.ui.view,arch_db:website_event_booth.event_booth_layout
msgid "list of future events"
msgstr "רשימת אירועים עתידיים"
