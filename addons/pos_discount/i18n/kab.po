# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * pos_discount
#
# Translators:
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:55+0000\n"
"PO-Revision-Date: 2015-09-08 06:41+0000\n"
"Last-Translator: <PERSON>\n"
"Language-Team: <PERSON><PERSON><PERSON> (http://www.transifex.com/odoo/odoo-9/language/kab/)\n"
"Language: kab\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: pos_discount
#. odoo-python
#: code:addons/pos_discount/models/pos_config.py:0
msgid ""
"A discount product is needed to use the Global Discount feature. Go to Point "
"of Sale > Configuration > Settings to set it."
msgstr ""

#. module: pos_discount
#: model:ir.model.fields,help:pos_discount.field_pos_config__iface_discount
msgid "Allow the cashier to give discounts on the whole order."
msgstr ""

#. module: pos_discount
#: model:ir.model,name:pos_discount.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: pos_discount
#. odoo-javascript
#: code:addons/pos_discount/static/src/overrides/components/discount_button/discount_button.xml:0
msgid "Discount"
msgstr "Tuǧǧit"

#. module: pos_discount
#: model_terms:ir.ui.view,arch_db:pos_discount.res_config_settings_view_form
msgid "Discount %"
msgstr ""

#. module: pos_discount
#. odoo-javascript
#: code:addons/pos_discount/static/src/overrides/components/discount_button/discount_button.js:0
#: model:ir.model.fields,field_description:pos_discount.field_pos_config__discount_pc
#: model:ir.model.fields,field_description:pos_discount.field_res_config_settings__pos_discount_pc
msgid "Discount Percentage"
msgstr "Amfidi n tuǧǧit"

#. module: pos_discount
#: model:ir.model.fields,field_description:pos_discount.field_pos_config__discount_product_id
#: model_terms:ir.ui.view,arch_db:pos_discount.res_config_settings_view_form
msgid "Discount Product"
msgstr "Afris n tuǧǧit"

#. module: pos_discount
#. odoo-javascript
#: code:addons/pos_discount/static/src/overrides/components/discount_button/discount_button.js:0
msgid "No discount product found"
msgstr ""

#. module: pos_discount
#. odoo-javascript
#: code:addons/pos_discount/static/src/overrides/components/discount_button/discount_button.js:0
msgid "No tax"
msgstr ""

#. module: pos_discount
#: model:ir.model.fields,field_description:pos_discount.field_pos_config__iface_discount
msgid "Order Discounts"
msgstr ""

#. module: pos_discount
#: model:ir.model,name:pos_discount.model_pos_config
msgid "Point of Sale Configuration"
msgstr ""

#. module: pos_discount
#: model:ir.model,name:pos_discount.model_pos_session
msgid "Point of Sale Session"
msgstr ""

#. module: pos_discount
#: model:ir.model.fields,field_description:pos_discount.field_res_config_settings__pos_discount_product_id
msgid "Pos Discount Product"
msgstr ""

#. module: pos_discount
#. odoo-javascript
#: code:addons/pos_discount/static/src/overrides/components/discount_button/discount_button.js:0
msgid "Tax: %s"
msgstr ""

#. module: pos_discount
#: model:ir.model.fields,help:pos_discount.field_pos_config__discount_pc
#: model:ir.model.fields,help:pos_discount.field_res_config_settings__pos_discount_pc
msgid "The default discount percentage when clicking on the Discount button"
msgstr ""

#. module: pos_discount
#. odoo-javascript
#: code:addons/pos_discount/static/src/overrides/components/discount_button/discount_button.js:0
msgid ""
"The discount product seems misconfigured. Make sure it is flagged as 'Can be "
"Sold' and 'Available in Point of Sale'."
msgstr ""

#. module: pos_discount
#: model:ir.model.fields,help:pos_discount.field_pos_config__discount_product_id
msgid "The product used to apply the discount on the ticket."
msgstr ""
