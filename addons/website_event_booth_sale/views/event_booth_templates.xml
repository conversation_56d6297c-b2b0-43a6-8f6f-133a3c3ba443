<?xml version="1.0" encoding="UTF-8" ?>
<odoo>

    <template id="event_booth_registration" inherit_id="website_event_booth.event_booth_registration">
        <xpath expr="//label//span[hasclass('booth_category_price')]" position="replace">
            <t t-if="booth_category.price">
                <t t-if="(booth_category.price - booth_category.price_reduce) &gt; 0">
                    <del t-if="website.show_line_subtotals_tax_selection == 'tax_excluded'"
                         class="text-danger me-1"
                         t-field="booth_category.price"
                         t-options="{'widget': 'monetary', 'from_currency': event.company_id.sudo().currency_id, 'display_currency': website.currency_id}"/>
                    <del t-else=""
                         class="text-danger me-1"
                         t-field="booth_category.price_incl"
                         t-options="{'widget': 'monetary', 'from_currency': event.company_id.sudo().currency_id, 'display_currency': website.currency_id}"/>
                </t>
                <h5 t-if="website.show_line_subtotals_tax_selection == 'tax_excluded'"
                      t-field="booth_category.price_reduce"
                      t-options="{'widget': 'monetary', 'from_currency': event.company_id.sudo().currency_id, 'display_currency': website.currency_id}"/>
                <h5 t-else=""
                      t-field="booth_category.price_reduce_taxinc"
                      t-options="{'widget': 'monetary', 'from_currency': event.company_id.sudo().currency_id, 'display_currency': website.currency_id}"/>
            </t>
            <h5 t-else="">Free</h5>
        </xpath>
        <xpath expr="//input[@name='booth_category_id']" position="attributes">
            <attribute name="t-att-data-price">
                event.company_id.sudo().currency_id._convert(
                    booth_category.price_reduce_taxinc if website.show_line_subtotals_tax_selection == 'tax_included' else booth_category.price_reduce,
                    website.currency_id,
                    event.company_id,
                    datetime.date.today()
                ) or '0'
            </attribute>
        </xpath>
        <xpath expr="//button[hasclass('o_wbooth_registration_submit')]" position="before">
            <div class="o_wbooth_booth_total_price d-none text-end">
                <div class="fw-bold">
                    <span>Total</span>
                    <span t-out="float(0)"
                          t-options="{'widget': 'monetary', 'from_currency': event.company_id.sudo().currency_id, 'display_currency': website.currency_id}"/>
                </div>
            </div>
        </xpath>
    </template>

    <template id="event_booth_order_progress" inherit_id="website_event_booth.event_booth_order_progress">
        <xpath expr="//li[last()]//span" position="replace">
            <t t-if="has_payment_step">Payment</t>
            <t t-else="">Confirmed</t>
        </xpath>
    </template>

</odoo>
