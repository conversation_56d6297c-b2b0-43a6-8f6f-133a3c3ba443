<?xml version='1.0' encoding='UTF-8'?>
<TaxInvoiceBulk xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="TaxInvoice.xsd">
  <TIN>1234567890123456</TIN>
  <ListOfTaxInvoice>
    <TaxInvoice>
      <TaxInvoiceDate>2019-05-01</TaxInvoiceDate>
      <TaxInvoiceOpt>Normal</TaxInvoiceOpt>
      <TrxCode>04</TrxCode>
      <AddInfo/>
      <CustomDoc/>
      <CustomDocMonthYear/>
      <RefDesc>INV/2019/00001</RefDesc>
      <FacilityStamp/>
      <SellerIDTKU>1234567890123456000000</SellerIDTKU>
      <BuyerTin>1234567890123457</BuyerTin>
      <BuyerDocument>TIN</BuyerDocument>
      <BuyerCountry>IDN</BuyerCountry>
      <BuyerDocumentNumber/>
      <BuyerName>partner_a</BuyerName>
      <BuyerAdress>Indonesia</BuyerAdress>
      <BuyerEmail/>
      <BuyerIDTKU>1234567890123457000000</BuyerIDTKU>
      <ListOfGoodService>
        <GoodService>
          <Opt>A</Opt>
          <Code>000000</Code>
          <Name>product_a</Name>
          <Unit>UM.0018</Unit>
          <Price>100000.0</Price>
          <Qty>1.0</Qty>
          <TotalDiscount>0.0</TotalDiscount>
          <TaxBase>100000.0</TaxBase>
          <OtherTaxBase>91666.67</OtherTaxBase>
          <VATRate>12</VATRate>
          <VAT>11000.0</VAT>
          <STLGRate>0.0</STLGRate>
          <STLG>0.0</STLG>
        </GoodService>
      </ListOfGoodService>
    </TaxInvoice>
  </ListOfTaxInvoice>
</TaxInvoiceBulk>
