# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_expense
# 
# Translators:
# <PERSON>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# Konsta Aavaranta, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <ossi.manty<PERSON><PERSON>@obs-solutions.fi>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <kari.l<PERSON><PERSON>@emsystems.fi>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-01-05 12:32+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON> <kari.l<PERSON><PERSON>@emsystems.fi>, 2024\n"
"Language-Team: Finnish (https://app.transifex.com/odoo/teams/41243/fi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fi\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: sale_expense
#: model:ir.model.fields,field_description:sale_expense.field_sale_order__expense_count
msgid "# of Expenses"
msgstr "# Kuluista"

#. module: sale_expense
#: model:ir.model.fields,field_description:sale_expense.field_hr_expense__can_be_reinvoiced
#: model:ir.model.fields,field_description:sale_expense.field_hr_expense_split__can_be_reinvoiced
msgid "Can be reinvoiced"
msgstr "Voidaan laskuttaa asiaalta"

#. module: sale_expense
#: model:ir.model.fields,field_description:sale_expense.field_hr_expense__sale_order_id
#: model:ir.model.fields,field_description:sale_expense.field_hr_expense_split__sale_order_id
msgid "Customer to Reinvoice"
msgstr "Asiakas, jolta laskutetaan"

#. module: sale_expense
#: model:ir.model,name:sale_expense.model_hr_expense
msgid "Expense"
msgstr "Kulu"

#. module: sale_expense
#: model:ir.model.fields,field_description:sale_expense.field_product_product__expense_policy_tooltip
#: model:ir.model.fields,field_description:sale_expense.field_product_template__expense_policy_tooltip
msgid "Expense Policy Tooltip"
msgstr "Kulukäytännön työkaluvihje"

#. module: sale_expense
#: model:ir.model,name:sale_expense.model_hr_expense_sheet
msgid "Expense Report"
msgstr "Kuluraportti"

#. module: sale_expense
#: model:ir.model,name:sale_expense.model_hr_expense_split
msgid "Expense Split"
msgstr "Kulujen jako"

#. module: sale_expense
#: model:ir.actions.act_window,name:sale_expense.hr_expense_action_from_sale_order
#: model:ir.model.fields,field_description:sale_expense.field_sale_order__expense_ids
#: model_terms:ir.ui.view,arch_db:sale_expense.sale_order_form_view_inherit
msgid "Expenses"
msgstr "Kulut"

#. module: sale_expense
#. odoo-python
#: code:addons/sale_expense/models/product_template.py:0
msgid "Expenses of this category may not be added to a Sales Order."
msgstr "Tähän luokkaan kuuluvia kuluja ei voi lisätä myyntitilaukseen."

#. module: sale_expense
#. odoo-python
#: code:addons/sale_expense/models/product_template.py:0
msgid ""
"Expenses will be added to the Sales Order at their actual cost when posted."
msgstr ""
"Kulut lisätään myyntitilaukseen niiden todellisten kustannusten mukaan, kun "
"ne kirjataan."

#. module: sale_expense
#. odoo-python
#: code:addons/sale_expense/models/product_template.py:0
msgid ""
"Expenses will be added to the Sales Order at their sales price (product "
"price, pricelist, etc.) when posted."
msgstr ""
"Kulut lisätään myyntitilaukseen niiden myyntihintaan (tuotteen hinta, "
"hinnasto jne.), kun ne kirjataan."

#. module: sale_expense
#: model:ir.model.fields,help:sale_expense.field_hr_expense__sale_order_id
msgid ""
"If the category has an expense policy, it will be reinvoiced on this sales "
"order"
msgstr ""
"Jos luokalla on kulukäytäntö, se laskutetaan uudelleen tässä "
"myyntitilauksessa"

#. module: sale_expense
#: model_terms:ir.ui.view,arch_db:sale_expense.product_product_view_form_inherit_sale_expense
msgid "Invoicing"
msgstr "Laskutus"

#. module: sale_expense
#: model:ir.model,name:sale_expense.model_account_move
msgid "Journal Entry"
msgstr "Päiväkirjan kirjaus"

#. module: sale_expense
#: model:ir.model,name:sale_expense.model_account_move_line
msgid "Journal Item"
msgstr "Päiväkirjatapahtuma"

#. module: sale_expense
#: model:ir.model,name:sale_expense.model_product_template
msgid "Product"
msgstr "Tuote"

#. module: sale_expense
#. odoo-python
#: code:addons/sale_expense/models/hr_expense_sheet.py:0
msgid "Reinvoiced Sales Orders"
msgstr "Uudelleen laskutetut myyntitilaukset"

#. module: sale_expense
#: model:ir.model.fields,field_description:sale_expense.field_hr_expense_sheet__sale_order_count
msgid "Sale Order Count"
msgstr "Myyntitilausten lukumäärä"

#. module: sale_expense
#: model:ir.model,name:sale_expense.model_sale_order
msgid "Sales Order"
msgstr "Myyntitilaus"

#. module: sale_expense
#: model_terms:ir.ui.view,arch_db:sale_expense.hr_expense_sheet_view_form
msgid "Sales Orders"
msgstr "Myyntitilaukset"
