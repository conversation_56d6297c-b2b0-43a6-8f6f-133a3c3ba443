# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* event_sms
# 
# Translators:
# <PERSON><PERSON>, 2024
# <PERSON>, 2024
# <PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON>, 2024
# JanaAvalah, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:55+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: JanaAvalah, 2024\n"
"Language-Team: Estonian (https://app.transifex.com/odoo/teams/41243/et/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: et\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: event_sms
#: model:ir.model,name:event_sms.model_event_mail
msgid "Event Automated Mailing"
msgstr "Sündmuste automatiseeritud postitamine"

#. module: event_sms
#: model:sms.template,name:event_sms.sms_template_data_event_registration
msgid "Event: Registration"
msgstr "Sündmus: Registreerimine"

#. module: event_sms
#: model:sms.template,name:event_sms.sms_template_data_event_reminder
msgid "Event: Reminder"
msgstr "Sündmus: Meeldetuletus"

#. module: event_sms
#: model:ir.model,name:event_sms.model_event_type_mail
msgid "Mail Scheduling on Event Category"
msgstr "Sündmuse kategooria kirjade ajastamine"

#. module: event_sms
#: model:sms.template,body:event_sms.sms_template_data_event_reminder
msgid ""
"Ready for \"{{ object.event_id.name }}\" {{ object.event_date_range }}?\n"
"{{ 'It starts at %s' % format_time(time=object.event_begin_date, tz=object.event_id.date_tz, time_format='short', lang_code=object.partner_id.lang) + (', at %s' % object.event_id.address_inline if object.event_id.address_inline else '') + '.\\nSee you there!' if object.event_id.address_inline or 'website_published' not in object.event_id._fields else 'Join us on %s/event/%i!' % (object.get_base_url(), object.event_id.id) }}"
msgstr ""

#. module: event_sms
#: model:ir.model,name:event_sms.model_event_mail_registration
msgid "Registration Mail Scheduler"
msgstr "Registreerimiskirja planeerija"

#. module: event_sms
#. odoo-javascript
#: code:addons/event_sms/static/src/template_reference_field/field_event_mail_template_reference.xml:0
#: model:ir.model.fields.selection,name:event_sms.selection__event_mail__notification_type__sms
#: model:ir.model.fields.selection,name:event_sms.selection__event_mail__template_ref__sms_template
#: model:ir.model.fields.selection,name:event_sms.selection__event_type_mail__notification_type__sms
#: model:ir.model.fields.selection,name:event_sms.selection__event_type_mail__template_ref__sms_template
msgid "SMS"
msgstr "SMS"

#. module: event_sms
#: model:ir.model,name:event_sms.model_sms_template
msgid "SMS Templates"
msgstr "Sõnumi mallid"

#. module: event_sms
#: model:ir.model.fields,field_description:event_sms.field_event_mail__notification_type
#: model:ir.model.fields,field_description:event_sms.field_event_type_mail__notification_type
msgid "Send"
msgstr "Saada"

#. module: event_sms
#: model:ir.model.fields,field_description:event_sms.field_event_mail__template_ref
#: model:ir.model.fields,field_description:event_sms.field_event_type_mail__template_ref
msgid "Template"
msgstr "Mall"

#. module: event_sms
#: model:sms.template,body:event_sms.sms_template_data_event_registration
msgid ""
"{{ object.event_id.organizer_id.name or object.event_id.company_id.name or "
"user.env.company.name }}: We are happy to confirm your registration for the "
"{{ object.event_id.name }} event."
msgstr ""
"{{ object.event_id.organizer_id.name or object.event_id.company_id.name or "
"user.env.company.name }}: We are happy to confirm your registration for the "
"{{ object.event_id.name }} event."
