# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * auth_signup
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.saas~18\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-05-16 13:49+0000\n"
"PO-Revision-Date: 2017-10-02 11:26+0000\n"
"Language-Team: Faroese (https://www.transifex.com/odoo/teams/41243/fo/)\n"
"Language: fo\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.res_users_view_form
msgid "<strong>A password reset has been requested for this user. An email containing the following link has been sent:</strong>"
msgstr ""

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.res_users_view_form
msgid "<strong>An invitation email containing the following subscription link has been sent:</strong>"
msgstr ""

#. module: auth_signup
#: model:mail.template,body_html:auth_signup.mail_template_data_unregistered_users
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"background-color: #FFFFFF; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: #FFFFFF; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- CONTENT -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <t t-set=\"invited_users\" t-value=\"ctx.get('invited_users', [])\"></t>\n"
"                <td style=\"text-align : left\">\n"
"                    <span style=\"font-size: 20px; font-weight: bold;\">\n"
"                        Pending Invitations\n"
"                    </span><br><br>\n"
"                </td>\n"
"                <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"                    <div>\n"
"                        Dear <t t-out=\"object.name or ''\">Mitchell Admin</t>,<br> <br>\n"
"                        You added the following user(s) to your database but they haven't registered yet:\n"
"                        <ul>\n"
"                            <t t-foreach=\"invited_users\" t-as=\"invited_user\">\n"
"                                <li t-out=\"invited_user or ''\"><EMAIL></li>\n"
"                            </t>\n"
"                        </ul>\n"
"                        Follow up with them so they can access your database and start working with you.\n"
"                        <br><br>\n"
"                        Have a nice day!<br>\n"
"                        --<br>The <t t-out=\"object.company_id.name or ''\">YourCompany</t> Team\n"
"                    </div>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\">\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"</table>\n"
"            "
msgstr ""

#. module: auth_signup
#: model:mail.template,body_html:auth_signup.set_password_email
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #FFFFFF; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: #FFFFFF; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- HEADER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\">\n"
"                    <span style=\"font-size: 10px;\">Welcome to Odoo</span><br>\n"
"                    <span style=\"font-size: 20px; font-weight: bold;\">\n"
"                        <t t-out=\"object.name or ''\">Marc Demo</t>\n"
"                    </span>\n"
"                </td><td valign=\"middle\" align=\"right\" t-if=\"not object.company_id.uses_default_logo\">\n"
"                    <img t-attf-src=\"/logo.png?company={{ object.company_id.id }}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" t-att-alt=\"object.company_id.name\">\n"
"                </td></tr>\n"
"                <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\">\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- CONTENT -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"                    <div>\n"
"                        Dear <t t-out=\"object.name or ''\">Marc Demo</t>,<br><br>\n"
"                        You have been invited by <t t-out=\"object.create_uid.name or ''\">OdooBot</t> of <t t-out=\"object.company_id.name or ''\">YourCompany</t> to connect on Odoo.\n"
"                        <div style=\"margin: 16px 0px 16px 0px;\">\n"
"                            <a t-att-href=\"object.signup_url\" style=\"background-color: #875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\">\n"
"                                Accept invitation\n"
"                            </a>\n"
"                        </div>\n"
"                        <t t-set=\"website_url\" t-value=\"object.get_base_url()\"></t>\n"
"                        Your Odoo domain is: <b><a t-att-href=\"website_url\" t-out=\"website_url or ''\">http://yourcompany.odoo.com</a></b><br>\n"
"                        Your sign in email is: <b><a t-attf-href=\"/web/login?login={{ object.email }}\" target=\"_blank\" t-out=\"object.email or ''\"><EMAIL></a></b><br><br>\n"
"                        Never heard of Odoo? It’s an all-in-one business software loved by 7+ million users. It will considerably improve your experience at work and increase your productivity.\n"
"                        <br><br>\n"
"                        Have a look at the <a href=\"https://www.odoo.com/page/tour?utm_source=db&amp;utm_medium=auth\" style=\"color: #875A7B;\">Odoo Tour</a> to discover the tool.\n"
"                        <br><br>\n"
"                        Enjoy Odoo!<br>\n"
"                        --<br>The <t t-out=\"object.company_id.name or ''\">YourCompany</t> Team\n"
"                    </div>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\">\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- FOOTER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; font-size: 11px; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\" align=\"left\">\n"
"                    <t t-out=\"object.company_id.name or ''\">YourCompany</t>\n"
"                </td></tr>\n"
"                <tr><td valign=\"middle\" align=\"left\" style=\"opacity: 0.7;\">\n"
"                    <t t-out=\"object.company_id.phone or ''\">******-123-4567</t>\n"
"                    <t t-if=\"object.company_id.email\">\n"
"                        | <a t-att-href=\"'mailto:%s' % object.company_id.email\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.company_id.email or ''\"><EMAIL></a>\n"
"                    </t>\n"
"                    <t t-if=\"object.company_id.website\">\n"
"                        | <a t-att-href=\"'%s' % object.company_id.website\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.company_id.website or ''\">http://www.example.com</a>\n"
"                    </t>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"<!-- POWERED BY -->\n"
"<tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"      <tr><td style=\"text-align: center; font-size: 13px;\">\n"
"        Powered by <a target=\"_blank\" href=\"https://www.odoo.com?utm_source=db&amp;utm_medium=auth\" style=\"color: #875A7B;\">Odoo</a>\n"
"      </td></tr>\n"
"    </table>\n"
"</td></tr>\n"
"</table>"
msgstr ""

#. module: auth_signup
#: model:mail.template,body_html:auth_signup.reset_password_email
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #FFFFFF; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: #FFFFFF; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- HEADER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\">\n"
"                    <span style=\"font-size: 10px;\">Your Account</span><br>\n"
"                    <span style=\"font-size: 20px; font-weight: bold;\">\n"
"                        <t t-out=\"object.name or ''\">Marc Demo</t>\n"
"                    </span>\n"
"                </td><td valign=\"middle\" align=\"right\" t-if=\"not object.company_id.uses_default_logo\">\n"
"                    <img t-attf-src=\"/logo.png?company={{ object.company_id.id }}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" t-att-alt=\"object.company_id.name\">\n"
"                </td></tr>\n"
"                <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\">\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- CONTENT -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"                    <div>\n"
"                        Dear <t t-out=\"object.name or ''\">Marc Demo</t>,<br><br>\n"
"                        A password reset was requested for the Odoo account linked to this email.\n"
"                        You may change your password by following this link which will remain valid during 24 hours:<br>\n"
"                        <div style=\"margin: 16px 0px 16px 0px;\">\n"
"                            <a t-att-href=\"object.signup_url\" style=\"background-color: #875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\">\n"
"                                Change password\n"
"                            </a>\n"
"                        </div>\n"
"                        If you do not expect this, you can safely ignore this email.<br><br>\n"
"                        Thanks,\n"
"                        <t t-if=\"user.signature\">\n"
"                            <br>\n"
"                            <t t-out=\"user.signature or ''\">--<br>Mitchell Admin</t>\n"
"                        </t>\n"
"                    </div>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\">\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- FOOTER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; font-size: 11px; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\" align=\"left\">\n"
"                    <t t-out=\"object.company_id.name or ''\">YourCompany</t>\n"
"                </td></tr>\n"
"                <tr><td valign=\"middle\" align=\"left\" style=\"opacity: 0.7;\">\n"
"                    <t t-out=\"object.company_id.phone or ''\">******-123-4567</t>\n"
"\n"
"                    <t t-if=\"object.company_id.email\">\n"
"                        | <a t-att-href=\"'mailto:%s' % object.company_id.email\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.company_id.email or ''\"><EMAIL></a>\n"
"                    </t>\n"
"                    <t t-if=\"object.company_id.website\">\n"
"                        | <a t-att-href=\"'%s' % object.company_id.website\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.company_id.website or ''\">http://www.example.com</a>\n"
"                    </t>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"<!-- POWERED BY -->\n"
"<tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"      <tr><td style=\"text-align: center; font-size: 13px;\">\n"
"        Powered by <a target=\"_blank\" href=\"https://www.odoo.com?utm_source=db&amp;utm_medium=auth\" style=\"color: #875A7B;\">Odoo</a>\n"
"      </td></tr>\n"
"    </table>\n"
"</td></tr>\n"
"</table>\n"
"            "
msgstr ""

#. module: auth_signup
#: model:mail.template,body_html:auth_signup.mail_template_user_signup_account_created
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #FFFFFF; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: #FFFFFF; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- HEADER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\">\n"
"                    <span style=\"font-size: 10px;\">Your Account</span><br>\n"
"                    <span style=\"font-size: 20px; font-weight: bold;\">\n"
"                        <t t-out=\"object.name or ''\">Marc Demo</t>\n"
"                    </span>\n"
"                </td><td valign=\"middle\" align=\"right\" t-if=\"not object.company_id.uses_default_logo\">\n"
"                    <img t-attf-src=\"/logo.png?company={{ object.company_id.id }}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" t-att-alt=\"object.company_id.name\">\n"
"                </td></tr>\n"
"                <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\">\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- CONTENT -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"                    <div>\n"
"                        Dear <t t-out=\"object.name or ''\">Marc Demo</t>,<br><br>\n"
"                        Your account has been successfully created!<br>\n"
"                        Your login is <strong><t t-out=\"object.email or ''\"><EMAIL></t></strong><br>\n"
"                        To gain access to your account, you can use the following link:\n"
"                        <div style=\"margin: 16px 0px 16px 0px;\">\n"
"                            <a t-attf-href=\"/web/login?auth_login={{object.email}}\" style=\"background-color: #875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\">\n"
"                                Go to My Account\n"
"                            </a>\n"
"                        </div>\n"
"                        Thanks,<br>\n"
"                        <t t-if=\"user.signature\">\n"
"                            <br>\n"
"                            <t t-out=\"user.signature or ''\">--<br>Mitchell Admin</t>\n"
"                        </t>\n"
"                    </div>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\">\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- FOOTER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; font-size: 11px; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\" align=\"left\">\n"
"                    <t t-out=\"object.company_id.name or ''\">YourCompany</t>\n"
"                </td></tr>\n"
"                <tr><td valign=\"middle\" align=\"left\" style=\"opacity: 0.7;\">\n"
"                    <t t-out=\"object.company_id.phone or ''\">******-123-4567</t>\n"
"                    <t t-if=\"object.company_id.email\">\n"
"                        | <a t-attf-href=\"'mailto:%s' % {{ object.company_id.email }}\" style=\"text-decoration:none; color: #454748;\"><t t-out=\"object.company_id.email or ''\"><EMAIL></t></a>\n"
"                    </t>\n"
"                    <t t-if=\"object.company_id.website\">\n"
"                        | <a t-attf-href=\"'%s' % {{ object.company_id.website }}\" style=\"text-decoration:none; color: #454748;\">\n"
"                            <t t-out=\"object.company_id.website or ''\">http://www.example.com</t>\n"
"                        </a>\n"
"                    </t>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"<!-- POWERED BY -->\n"
"<tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"      <tr><td style=\"text-align: center; font-size: 13px;\">\n"
"        Powered by <a target=\"_blank\" href=\"https://www.odoo.com?utm_source=db&amp;utm_medium=auth\" style=\"color: #875A7B;\">Odoo</a>\n"
"      </td></tr>\n"
"    </table>\n"
"</td></tr>\n"
"</table>"
msgstr ""

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.signup
msgid "Already have an account?"
msgstr ""

#. module: auth_signup
#. odoo-python
#: code:addons/auth_signup/controllers/main.py:0
msgid "Another user is already registered using this email address."
msgstr ""

#. module: auth_signup
#. odoo-python
#: code:addons/auth_signup/controllers/main.py:0
msgid "Authentication Failed."
msgstr ""

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.reset_password
msgid "Back to Login"
msgstr ""

#. module: auth_signup
#. odoo-python
#: code:addons/auth_signup/models/res_users.py:0
msgid "Cannot send email: user %s has no email address."
msgstr ""

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.res_users_view_form
msgid "Close"
msgstr ""

#. module: auth_signup
#: model:ir.model,name:auth_signup.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.fields
msgid "Confirm Password"
msgstr ""

#. module: auth_signup
#: model:ir.model.fields.selection,name:auth_signup.selection__res_users__state__active
msgid "Confirmed"
msgstr ""

#. module: auth_signup
#: model:ir.model,name:auth_signup.model_res_partner
msgid "Contact"
msgstr ""

#. module: auth_signup
#. odoo-python
#: code:addons/auth_signup/controllers/main.py:0
msgid "Could not create a new account."
msgstr ""

#. module: auth_signup
#. odoo-python
#: code:addons/auth_signup/controllers/main.py:0
msgid "Could not reset your password"
msgstr ""

#. module: auth_signup
#: model:ir.model.fields,field_description:auth_signup.field_res_config_settings__auth_signup_uninvited
msgid "Customer Account"
msgstr ""

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.res_config_settings_view_form
msgid "Default Access Rights"
msgstr ""

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.login
msgid "Don't have an account?"
msgstr ""

#. module: auth_signup
#: model:ir.model.fields,field_description:auth_signup.field_res_config_settings__auth_signup_reset_password
#: model_terms:ir.ui.view,arch_db:auth_signup.res_config_settings_view_form
msgid "Enable password reset from Login page"
msgstr ""

#. module: auth_signup
#: model:ir.model.fields.selection,name:auth_signup.selection__res_config_settings__auth_signup_uninvited__b2c
msgid "Free sign up"
msgstr ""

#. module: auth_signup
#: model:ir.model,name:auth_signup.model_ir_http
msgid "HTTP Routing"
msgstr ""

#. module: auth_signup
#. odoo-python
#: code:addons/auth_signup/controllers/main.py:0
msgid "Invalid signup token"
msgstr ""

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.res_config_settings_view_form
msgid "Let your customers log in to see their documents"
msgstr ""

#. module: auth_signup
#: model:ir.model.fields.selection,name:auth_signup.selection__res_users__state__new
msgid "Never Connected"
msgstr ""

#. module: auth_signup
#. odoo-python
#: code:addons/auth_signup/models/res_users.py:0
msgid "No account found for this login"
msgstr ""

#. module: auth_signup
#. odoo-python
#: code:addons/auth_signup/controllers/main.py:0
msgid "No login provided."
msgstr ""

#. module: auth_signup
#: model:ir.model.fields.selection,name:auth_signup.selection__res_config_settings__auth_signup_uninvited__b2b
msgid "On invitation"
msgstr ""

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.fields
msgid "Password"
msgstr ""

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.res_config_settings_view_form
msgid "Password Reset"
msgstr ""

#. module: auth_signup
#: model:mail.template,subject:auth_signup.reset_password_email
msgid "Password reset"
msgstr ""

#. module: auth_signup
#. odoo-python
#: code:addons/auth_signup/controllers/main.py:0
msgid "Password reset instructions sent to your email"
msgstr ""

#. module: auth_signup
#. odoo-python
#: code:addons/auth_signup/controllers/main.py:0
msgid "Passwords do not match; please retype them."
msgstr ""

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.login_successful
msgid "Registration successful."
msgstr ""

#. module: auth_signup
#: model:mail.template,subject:auth_signup.mail_template_data_unregistered_users
msgid "Reminder for unregistered users"
msgstr ""

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.login
#: model_terms:ir.ui.view,arch_db:auth_signup.reset_password
msgid "Reset Password"
msgstr ""

#. module: auth_signup
#: model:ir.actions.server,name:auth_signup.action_send_password_reset_instructions
#: model_terms:ir.ui.view,arch_db:auth_signup.res_users_view_form
msgid "Send Password Reset Instructions"
msgstr ""

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.res_users_view_form
msgid "Send an Invitation Email"
msgstr ""

#. module: auth_signup
#: model:mail.template,description:auth_signup.mail_template_data_unregistered_users
msgid "Sent automatically to admin if new user haven't responded to the invitation"
msgstr ""

#. module: auth_signup
#: model:mail.template,description:auth_signup.set_password_email
msgid "Sent to new user after you invited them"
msgstr ""

#. module: auth_signup
#: model:mail.template,description:auth_signup.mail_template_user_signup_account_created
msgid "Sent to portal user who registered themselves"
msgstr ""

#. module: auth_signup
#: model:mail.template,description:auth_signup.reset_password_email
msgid "Sent to user who requested a password reset"
msgstr ""

#. module: auth_signup
#: model:mail.template,name:auth_signup.set_password_email
msgid "Settings: New Portal Signup"
msgstr ""

#. module: auth_signup
#: model:mail.template,name:auth_signup.mail_template_user_signup_account_created
msgid "Settings: New User Invite"
msgstr ""

#. module: auth_signup
#: model:mail.template,name:auth_signup.mail_template_data_unregistered_users
msgid "Settings: Unregistered User Reminder"
msgstr ""

#. module: auth_signup
#: model:mail.template,name:auth_signup.reset_password_email
msgid "Settings: User Reset Password"
msgstr ""

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.signup
msgid "Sign up"
msgstr ""

#. module: auth_signup
#: model:ir.model.fields,field_description:auth_signup.field_res_partner__signup_expiration
#: model:ir.model.fields,field_description:auth_signup.field_res_users__signup_expiration
msgid "Signup Expiration"
msgstr ""

#. module: auth_signup
#: model:ir.model.fields,field_description:auth_signup.field_res_partner__signup_token
#: model:ir.model.fields,field_description:auth_signup.field_res_users__signup_token
msgid "Signup Token"
msgstr ""

#. module: auth_signup
#: model:ir.model.fields,field_description:auth_signup.field_res_partner__signup_type
#: model:ir.model.fields,field_description:auth_signup.field_res_users__signup_type
msgid "Signup Token Type"
msgstr ""

#. module: auth_signup
#: model:ir.model.fields,field_description:auth_signup.field_res_partner__signup_valid
#: model:ir.model.fields,field_description:auth_signup.field_res_users__signup_valid
msgid "Signup Token is Valid"
msgstr ""

#. module: auth_signup
#: model:ir.model.fields,field_description:auth_signup.field_res_partner__signup_url
#: model:ir.model.fields,field_description:auth_signup.field_res_users__signup_url
msgid "Signup URL"
msgstr ""

#. module: auth_signup
#. odoo-python
#: code:addons/auth_signup/models/res_users.py:0
msgid "Signup is not allowed for uninvited users"
msgstr ""

#. module: auth_signup
#. odoo-python
#: code:addons/auth_signup/models/res_partner.py:0
msgid "Signup token '%s' is no longer valid"
msgstr ""

#. module: auth_signup
#. odoo-python
#: code:addons/auth_signup/models/res_partner.py:0
msgid "Signup token '%s' is not valid"
msgstr ""

#. module: auth_signup
#. odoo-python
#: code:addons/auth_signup/models/res_users.py:0
msgid "Signup: invalid template user"
msgstr ""

#. module: auth_signup
#. odoo-python
#: code:addons/auth_signup/models/res_users.py:0
msgid "Signup: no login given for new user"
msgstr ""

#. module: auth_signup
#. odoo-python
#: code:addons/auth_signup/models/res_users.py:0
msgid "Signup: no name or partner given for new user"
msgstr ""

#. module: auth_signup
#: model:ir.model.fields,field_description:auth_signup.field_res_users__state
msgid "Status"
msgstr ""

#. module: auth_signup
#: model:ir.model.fields,field_description:auth_signup.field_res_config_settings__auth_signup_template_user_id
msgid "Template user for new users created through signup"
msgstr ""

#. module: auth_signup
#. odoo-python
#: code:addons/auth_signup/controllers/main.py:0
msgid "The form was not properly filled in."
msgstr ""

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.res_config_settings_view_form
msgid "To send invitations in B2B mode, open a contact or select several ones in list view and click on 'Portal Access Management' option in the dropdown menu *Action*."
msgstr ""

#. module: auth_signup
#: model:ir.model,name:auth_signup.model_res_users
msgid "User"
msgstr ""

#. module: auth_signup
#: model:ir.actions.server,name:auth_signup.ir_cron_auth_signup_send_pending_user_reminder_ir_actions_server
msgid "Users: Notify About Unregistered Users"
msgstr ""

#. module: auth_signup
#: model:mail.template,subject:auth_signup.mail_template_user_signup_account_created
msgid "Welcome to {{ object.company_id.name }}!"
msgstr ""

#. module: auth_signup
#. odoo-python
#: code:addons/auth_signup/models/res_users.py:0
msgid "You cannot perform this action on an archived user."
msgstr ""

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.fields
#: model_terms:ir.ui.view,arch_db:auth_signup.reset_password
msgid "Your Email"
msgstr ""

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.fields
msgid "Your Name"
msgstr ""

#. module: auth_signup
#: model_terms:ir.ui.view,arch_db:auth_signup.fields
msgid "e.g. John Doe"
msgstr ""

#. module: auth_signup
#: model:mail.template,subject:auth_signup.set_password_email
msgid "{{ object.create_uid.name }} from {{ object.company_id.name }} invites you to connect to Odoo"
msgstr ""
