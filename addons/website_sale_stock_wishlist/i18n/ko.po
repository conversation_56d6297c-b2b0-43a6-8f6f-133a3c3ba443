# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_sale_stock_wishlist
# 
# Translators:
# Wil Odoo, 2024
# Sarah Park, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:56+0000\n"
"PO-Revision-Date: 2024-09-25 09:42+0000\n"
"Last-Translator: Sarah Park, 2025\n"
"Language-Team: Korean (https://app.transifex.com/odoo/teams/41243/ko/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ko\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: website_sale_stock_wishlist
#: model_terms:ir.ui.view,arch_db:website_sale_stock_wishlist.product_wishlist
msgid ""
"<i class=\"fa fa-bell\"/>\n"
"                        We'll notify you once the product is back in stock."
msgstr ""
"<i class=\"fa fa-bell\"/>\n"
"                        재입고 될 경우 즉시 안내드리겠습니다."

#. module: website_sale_stock_wishlist
#: model_terms:ir.ui.view,arch_db:website_sale_stock_wishlist.product_wishlist
msgid ""
"<i class=\"fa fa-times text-danger\"/>\n"
"                                Invalid email"
msgstr ""
"<i class=\"fa fa-times text-danger\"/>\n"
"                                잘못된 이메일"

#. module: website_sale_stock_wishlist
#: model_terms:ir.ui.view,arch_db:website_sale_stock_wishlist.product_wishlist
msgid ""
"<small>\n"
"                            <i class=\"fa fa-envelope-o\"/>\n"
"                            Get notified when back in stock\n"
"                        </small>"
msgstr ""
"<small>\n"
"                            <i class=\"fa fa-envelope-o\"/>\n"
"                            재입고 알림 받기\n"
"                        </small>"

#. module: website_sale_stock_wishlist
#: model_terms:ir.ui.view,arch_db:website_sale_stock_wishlist.product_wishlist
msgid ""
"<span class=\"fa fa-fw fa-shopping-cart\"/>\n"
"                <span class=\"d-none d-md-inline\">Add</span>"
msgstr ""
"<span class=\"fa fa-fw fa-shopping-cart\"/>\n"
"                <span class=\"d-none d-md-inline\">추가</span>"

#. module: website_sale_stock_wishlist
#. odoo-javascript
#: code:addons/website_sale_stock_wishlist/static/src/xml/product_availability.xml:0
msgid "Add to wishlist"
msgstr "위시리스트에 추가"

#. module: website_sale_stock_wishlist
#. odoo-javascript
#: code:addons/website_sale_stock_wishlist/static/src/xml/product_availability.xml:0
msgid "Added to your wishlist"
msgstr "위시리스트 추가 완료"

#. module: website_sale_stock_wishlist
#: model:ir.model,name:website_sale_stock_wishlist.model_product_template
msgid "Product"
msgstr "품목"

#. module: website_sale_stock_wishlist
#: model:ir.model,name:website_sale_stock_wishlist.model_product_wishlist
msgid "Product Wishlist"
msgstr "찜한 상품"

#. module: website_sale_stock_wishlist
#. odoo-javascript
#: code:addons/website_sale_stock_wishlist/static/src/xml/product_availability.xml:0
msgid "Save for later"
msgstr "저장 후 나중에 보기"

#. module: website_sale_stock_wishlist
#: model:ir.model.fields,field_description:website_sale_stock_wishlist.field_product_wishlist__stock_notification
msgid "Stock Notification"
msgstr "입고 알림"

#. module: website_sale_stock_wishlist
#: model_terms:ir.ui.view,arch_db:website_sale_stock_wishlist.product_wishlist
msgid "Temporarily out of stock"
msgstr "일시 품절"

#. module: website_sale_stock_wishlist
#: model_terms:ir.ui.view,arch_db:website_sale_stock_wishlist.product_wishlist
msgid "<EMAIL>"
msgstr "<EMAIL>"
