# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_mrp
# 
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <AUTHOR> <EMAIL>, 2024
# <PERSON>, 2024
# <PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:56+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Will Sensors, 2024\n"
"Language-Team: Latvian (https://app.transifex.com/odoo/teams/41243/lv/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: lv\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n != 0 ? 1 : 2);\n"

#. module: sale_mrp
#: model_terms:ir.ui.view,arch_db:sale_mrp.sale_order_portal_content_inherit_sale_mrp
msgid "<i class=\"fa fa-fw fa-check\"/> <b>Manufactured</b>"
msgstr ""

#. module: sale_mrp
#: model_terms:ir.ui.view,arch_db:sale_mrp.sale_order_portal_content_inherit_sale_mrp
msgid "<i class=\"fa fa-fw fa-clock-o\"/> <b>Confirmed</b>"
msgstr ""

#. module: sale_mrp
#: model_terms:ir.ui.view,arch_db:sale_mrp.sale_order_portal_content_inherit_sale_mrp
msgid "<i class=\"fa fa-fw fa-clock-o\"/> <b>In progress</b>"
msgstr ""

#. module: sale_mrp
#: model_terms:ir.ui.view,arch_db:sale_mrp.sale_order_portal_content_inherit_sale_mrp
msgid "<i class=\"fa fa-fw fa-times\"/> <b>Cancelled</b>"
msgstr ""

#. module: sale_mrp
#: model_terms:ir.ui.view,arch_db:sale_mrp.sale_order_form_mrp
msgid "<span class=\"o_stat_text\">Manufacturing</span>"
msgstr ""

#. module: sale_mrp
#: model_terms:ir.ui.view,arch_db:sale_mrp.mrp_production_form_view_sale
msgid "<span class=\"o_stat_text\">Sale</span>"
msgstr "<span class=\"o_stat_text\">Pārdošana</span>"

#. module: sale_mrp
#: model_terms:ir.ui.view,arch_db:sale_mrp.sale_order_portal_content_inherit_sale_mrp
msgid "<strong>Manufacturing Orders</strong>"
msgstr ""

#. module: sale_mrp
#. odoo-python
#: code:addons/sale_mrp/models/mrp_bom.py:0
msgid ""
"As long as there are some sale order lines that must be delivered/invoiced and are related to these bills of materials, you can not remove them.\n"
"The error concerns these products: %s"
msgstr ""

#. module: sale_mrp
#: model:ir.model,name:sale_mrp.model_mrp_bom
msgid "Bill of Material"
msgstr "Recepte"

#. module: sale_mrp
#: model:ir.model.fields,field_description:sale_mrp.field_sale_order__mrp_production_count
msgid "Count of MO generated"
msgstr ""

#. module: sale_mrp
#: model:ir.model.fields,field_description:sale_mrp.field_mrp_production__sale_order_count
msgid "Count of Source SO"
msgstr ""

#. module: sale_mrp
#: model_terms:ir.ui.view,arch_db:sale_mrp.sale_order_portal_content_inherit_sale_mrp
msgid "Date:"
msgstr "Datums:"

#. module: sale_mrp
#: model:ir.model,name:sale_mrp.model_account_move_line
msgid "Journal Item"
msgstr "Žurnāla ieraksts"

#. module: sale_mrp
#: model:ir.model,name:sale_mrp.model_mrp_production
msgid "Manufacturing Order"
msgstr "Ražošanas orderis"

#. module: sale_mrp
#. odoo-python
#: code:addons/sale_mrp/models/sale_order.py:0
msgid "Manufacturing Orders Generated by %s"
msgstr ""

#. module: sale_mrp
#: model:ir.model.fields,field_description:sale_mrp.field_sale_order__mrp_production_ids
msgid "Manufacturing orders associated with this sales order."
msgstr ""

#. module: sale_mrp
#: model:ir.model.fields,field_description:sale_mrp.field_mrp_production__sale_line_id
msgid "Origin sale order line"
msgstr ""

#. module: sale_mrp
#: model:ir.model,name:sale_mrp.model_stock_move_line
msgid "Product Moves (Stock Move Line)"
msgstr ""

#. module: sale_mrp
#: model:ir.model,name:sale_mrp.model_sale_order
msgid "Sales Order"
msgstr "Pārdošanas pasūtījums"

#. module: sale_mrp
#: model:ir.model,name:sale_mrp.model_sale_order_line
msgid "Sales Order Line"
msgstr "Pasūtījuma Rinda"

#. module: sale_mrp
#. odoo-python
#: code:addons/sale_mrp/models/mrp_production.py:0
msgid "Sources Sale Orders of %s"
msgstr ""

#. module: sale_mrp
#: model:ir.model,name:sale_mrp.model_stock_rule
msgid "Stock Rule"
msgstr "Noliktavas noteikums"
