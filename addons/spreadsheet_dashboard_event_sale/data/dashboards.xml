<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="spreadsheet_dashboard_events" model="spreadsheet.dashboard">
        <field name="name">Events</field>
        <field name="spreadsheet_binary_data" type="base64" file="spreadsheet_dashboard_event_sale/data/files/events_dashboard.json"/>
        <field name="main_data_model_ids" eval="[(4, ref('event.model_event_event'))]"/>
        <field name="sample_dashboard_file_path">spreadsheet_dashboard_event_sale/data/files/events_sample_dashboard.json</field>
        <field name="dashboard_group_id" ref="spreadsheet_dashboard.spreadsheet_dashboard_group_marketing"/>
        <field name="group_ids" eval="[Command.link(ref('event.group_event_manager'))]"/>
        <field name="sequence">60</field>
        <field name="is_published">True</field>
    </record>

</odoo>
