# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* board
# 
# Translators:
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:56+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Chinese (China) (https://app.transifex.com/odoo/teams/41243/zh_CN/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_CN\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: board
#. odoo-javascript
#: code:addons/board/static/src/board_controller.xml:0
msgid ""
"\"Add to\n"
"                  Dashboard\""
msgstr ""
"\"添加到\n"
"                  仪表板\""

#. module: board
#. odoo-javascript
#: code:addons/board/static/src/add_to_board/add_to_board.xml:0
msgid "Add"
msgstr "添加"

#. module: board
#. odoo-javascript
#: code:addons/board/static/src/add_to_board/add_to_board.xml:0
msgid "Add to my dashboard"
msgstr "添加到我的仪表板"

#. module: board
#. odoo-javascript
#: code:addons/board/static/src/board_controller.js:0
msgid "Are you sure that you want to remove this item?"
msgstr "是否确实要移除此项目？"

#. module: board
#: model:ir.model,name:board.model_board_board
msgid "Board"
msgstr "仪表板"

#. module: board
#. odoo-javascript
#: code:addons/board/static/src/board_controller.xml:0
msgid "Change Layout"
msgstr "更改布局"

#. module: board
#. odoo-javascript
#: code:addons/board/static/src/add_to_board/add_to_board.js:0
msgid "Could not add filter to dashboard"
msgstr "无法添加筛选到仪表板"

#. module: board
#. odoo-javascript
#: code:addons/board/static/src/add_to_board/add_to_board.xml:0
msgid "Dashboard"
msgstr "仪表板"

#. module: board
#: model:ir.model.fields,field_description:board.field_board_board__id
msgid "ID"
msgstr "ID"

#. module: board
#. odoo-javascript
#: code:addons/board/static/src/board_action.xml:0
msgid "Invalid action"
msgstr "无效动作"

#. module: board
#. odoo-javascript
#: code:addons/board/static/src/board_controller.xml:0
msgid "Layout"
msgstr "布局"

#. module: board
#: model:ir.actions.act_window,name:board.open_board_my_dash_action
#: model:ir.ui.menu,name:board.menu_board_my_dash
#: model_terms:ir.ui.view,arch_db:board.board_my_dash_view
msgid "My Dashboard"
msgstr "我的仪表板"

#. module: board
#. odoo-javascript
#: code:addons/board/static/src/add_to_board/add_to_board.js:0
msgid "Please refresh your browser for the changes to take effect."
msgstr "请刷新您的浏览器以使更改生效。"

#. module: board
#. odoo-javascript
#: code:addons/board/static/src/board_controller.xml:0
msgid ""
"To add your first report into this dashboard, go to any\n"
"                  menu, switch to list or graph view, and click"
msgstr ""
"若要将第一个报表添加到此仪表板，请转到“任何\n"
"                  菜单中，切换到列表或图形视图，然后单击"

#. module: board
#. odoo-javascript
#: code:addons/board/static/src/board_controller.xml:0
msgid ""
"You can filter and group data before inserting into the\n"
"                  dashboard using the search options."
msgstr ""
"使用搜素选项插入到仪表板之前\n"
"                  可以对数据进行筛选和分组。."

#. module: board
#. odoo-javascript
#: code:addons/board/static/src/board_controller.xml:0
msgid "Your personal dashboard is empty"
msgstr "您的个人仪表板是空的"

#. module: board
#. odoo-javascript
#: code:addons/board/static/src/board_controller.xml:0
msgid "in the extended search options."
msgstr "在扩展搜索选项。"

#. module: board
#. odoo-javascript
#: code:addons/board/static/src/add_to_board/add_to_board.js:0
msgid "“%s” added to dashboard"
msgstr "\"%s\"已添加到仪表板"
