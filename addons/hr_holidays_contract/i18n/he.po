# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_holidays_contract
# 
# Translators:
# <AUTHOR> <EMAIL>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:55+0000\n"
"PO-Revision-Date: 2024-09-29 00:00+0000\n"
"Last-Translator: ZVI BLONDER <<EMAIL>>, 2024\n"
"Language-Team: Hebrew (https://app.transifex.com/odoo/teams/41243/he/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: he\n"
"Plural-Forms: nplurals=3; plural=(n == 1 && n % 1 == 0) ? 0 : (n == 2 && n % 1 == 0) ? 1: 2;\n"

#. module: hr_holidays_contract
#. odoo-python
#: code:addons/hr_holidays_contract/models/hr_leave.py:0
msgid ""
"A leave cannot be set across multiple contracts with different working schedules.\n"
"\n"
"Please create one time off for each contract.\n"
"\n"
"Time off:\n"
"%(time_off)s\n"
"\n"
"Contracts:\n"
"%(contracts)s"
msgstr ""

#. module: hr_holidays_contract
#: model:ir.model,name:hr_holidays_contract.model_hr_employee_base
msgid "Basic Employee"
msgstr "עובד רגיל"

#. module: hr_holidays_contract
#. odoo-python
#: code:addons/hr_holidays_contract/models/hr_contract.py:0
msgid ""
"Changing the contract on this employee changes their working schedule in a "
"period they already took leaves. Changing this working schedule changes the "
"duration of these leaves in such a way the employee no longer has the "
"required allocation for them. Please review these leaves and/or allocations "
"before changing the contract."
msgstr ""

#. module: hr_holidays_contract
#: model:ir.model,name:hr_holidays_contract.model_hr_contract
msgid "Employee Contract"
msgstr "חוזה עובד"

#. module: hr_holidays_contract
#: model:ir.model,name:hr_holidays_contract.model_hr_leave
msgid "Time Off"
msgstr "מאשר חופשות"
