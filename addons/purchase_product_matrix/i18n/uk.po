# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* purchase_product_matrix
# 
# Translators:
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:55+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Ukrainian (https://app.transifex.com/odoo/teams/41243/uk/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: uk\n"
"Plural-Forms: nplurals=4; plural=(n % 1 == 0 && n % 10 == 1 && n % 100 != 11 ? 0 : n % 1 == 0 && n % 10 >= 2 && n % 10 <= 4 && (n % 100 < 12 || n % 100 > 14) ? 1 : n % 1 == 0 && (n % 10 ==0 || (n % 10 >=5 && n % 10 <=9) || (n % 100 >=11 && n % 100 <=14 )) ? 2: 3);\n"

#. module: purchase_product_matrix
#: model:ir.model.fields,field_description:purchase_product_matrix.field_purchase_order_line__product_template_attribute_value_ids
msgid "Attribute Values"
msgstr "Значення атрибутів"

#. module: purchase_product_matrix
#. odoo-javascript
#: code:addons/purchase_product_matrix/static/src/js/purchase_product_field.js:0
msgid "Edit Configuration"
msgstr "Редагувати налаштування"

#. module: purchase_product_matrix
#: model:ir.model.fields,field_description:purchase_product_matrix.field_purchase_order__grid
msgid "Grid"
msgstr "Сітка"

#. module: purchase_product_matrix
#: model:ir.model.fields,field_description:purchase_product_matrix.field_purchase_order__grid_product_tmpl_id
msgid "Grid Product Tmpl"
msgstr "Шаблон сітки товарів"

#. module: purchase_product_matrix
#: model:ir.model.fields,field_description:purchase_product_matrix.field_purchase_order__grid_update
msgid "Grid Update"
msgstr "Оновлення сітки"

#. module: purchase_product_matrix
#: model:ir.model.fields,help:purchase_product_matrix.field_purchase_order__report_grids
msgid ""
"If set, the matrix of configurable products will be shown on the report of "
"this order."
msgstr ""
"Якщо встановлено, матрицю налаштовуваних товарів буде показано у звіті цього"
" замовлення."

#. module: purchase_product_matrix
#: model:ir.model.fields,field_description:purchase_product_matrix.field_purchase_order_line__is_configurable_product
msgid "Is the product configurable?"
msgstr "Чи є цей товар налаштовуваним?"

#. module: purchase_product_matrix
#: model:ir.model.fields,field_description:purchase_product_matrix.field_purchase_order__report_grids
msgid "Print Variant Grids"
msgstr "Друкувати сітки варіантів"

#. module: purchase_product_matrix
#: model_terms:ir.ui.view,arch_db:purchase_product_matrix.purchase_order_form_matrix
msgid "Product"
msgstr "Товар"

#. module: purchase_product_matrix
#: model:ir.model.fields,field_description:purchase_product_matrix.field_purchase_order_line__product_template_id
msgid "Product Template"
msgstr "Шаблон товару"

#. module: purchase_product_matrix
#: model:ir.model.fields,field_description:purchase_product_matrix.field_purchase_order_line__product_no_variant_attribute_value_ids
msgid "Product attribute values that do not create variants"
msgstr "Значення атрибутів товару, які не створюють варіанти"

#. module: purchase_product_matrix
#: model:ir.model,name:purchase_product_matrix.model_purchase_order
msgid "Purchase Order"
msgstr "Замовлення на купівлю"

#. module: purchase_product_matrix
#: model:ir.model,name:purchase_product_matrix.model_purchase_order_line
msgid "Purchase Order Line"
msgstr "Рядок замовлення на купівлю"

#. module: purchase_product_matrix
#: model:ir.model.fields,help:purchase_product_matrix.field_purchase_order__grid_product_tmpl_id
msgid "Technical field for product_matrix functionalities."
msgstr "Технічне поле для функціоналу product_matrix."

#. module: purchase_product_matrix
#: model:ir.model.fields,help:purchase_product_matrix.field_purchase_order__grid
msgid ""
"Technical storage of grid. \n"
"If grid_update, will be loaded on the PO. \n"
"If not, represents the matrix to open."
msgstr ""
"Технічне зберігання сітки. \n"
"Якщо grid_update, буде завантажено на PO. \n"
"Якщо ні, представляє матрицю для відкриття."

#. module: purchase_product_matrix
#: model:ir.model.fields,help:purchase_product_matrix.field_purchase_order__grid_update
msgid "Whether the grid field contains a new matrix to apply or not."
msgstr "Чи містить поле сітки нову матрицю, яку слід застосувати чи ні."

#. module: purchase_product_matrix
#. odoo-python
#: code:addons/purchase_product_matrix/models/purchase.py:0
msgid ""
"You cannot change the quantity of a product present in multiple purchase "
"lines."
msgstr ""
"Ви не можете змінити кількість товару, яка представлена у кількох рядках "
"купівлі."
