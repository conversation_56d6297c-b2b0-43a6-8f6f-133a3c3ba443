# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_work_entry_holidays
# 
# Translators:
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 08:39+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Indonesian (https://app.transifex.com/odoo/teams/41243/id/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: id\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: hr_work_entry_holidays
#: model:ir.model,name:hr_work_entry_holidays.model_hr_contract
msgid "Employee Contract"
msgstr "Ko<PERSON><PERSON>"

#. module: hr_work_entry_holidays
#: model:ir.model,name:hr_work_entry_holidays.model_hr_work_entry
msgid "HR Work Entry"
msgstr "Entri Kerja HR"

#. module: hr_work_entry_holidays
#: model:ir.model,name:hr_work_entry_holidays.model_hr_work_entry_type
msgid "HR Work Entry Type"
msgstr "Tipe Entri Kerja HR"

#. module: hr_work_entry_holidays
#: model_terms:ir.ui.view,arch_db:hr_work_entry_holidays.work_entry_type_leave_form_inherit
msgid "Payroll"
msgstr "Payroll"

#. module: hr_work_entry_holidays
#: model:ir.model.fields,field_description:hr_work_entry_holidays.field_hr_work_entry__leave_state
msgid "Status"
msgstr "Status"

#. module: hr_work_entry_holidays
#: model:ir.model.fields,help:hr_work_entry_holidays.field_hr_work_entry__leave_state
msgid ""
"The status is set to 'To Submit', when a time off request is created.\n"
"The status is 'To Approve', when time off request is confirmed by user.\n"
"The status is 'Refused', when time off request is refused by manager.\n"
"The status is 'Approved', when time off request is approved by manager."
msgstr ""
"Status adalah 'Untuk Diserahkan', saat permintaan waktu cuti dibuat.\n"
"Status adalah 'Untuk Disetujui', saat permintaan waktu cuti dikonfirmasi user.\n"
"Status adalah 'Ditolak', saat permintaan waktu cuti ditolak oleh manager.\n"
"Status adalah 'Disetujui', saat permintaan waktu cuti disetujui oleh manager."

#. module: hr_work_entry_holidays
#: model:ir.model,name:hr_work_entry_holidays.model_hr_leave
#: model:ir.model.fields,field_description:hr_work_entry_holidays.field_hr_work_entry__leave_id
msgid "Time Off"
msgstr "Cuti"

#. module: hr_work_entry_holidays
#: model:ir.model,name:hr_work_entry_holidays.model_hr_leave_type
#: model:ir.model.fields,field_description:hr_work_entry_holidays.field_hr_work_entry_type__leave_type_ids
msgid "Time Off Type"
msgstr "Tipe Cuti"

#. module: hr_work_entry_holidays
#: model:ir.model.fields,field_description:hr_work_entry_holidays.field_hr_leave_type__work_entry_type_id
msgid "Work Entry Type"
msgstr "Tipe Entri Kerja"

#. module: hr_work_entry_holidays
#: model:ir.model.fields,help:hr_work_entry_holidays.field_hr_work_entry_type__leave_type_ids
msgid "Work entry used in the payslip."
msgstr "Entri kerja digunakan di payslip."
