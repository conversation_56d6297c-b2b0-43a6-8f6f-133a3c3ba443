<?xml version="1.0"?>
<odoo>
    <record id="event_track_tag_category_view_form" model="ir.ui.view">
        <field name="name">event.track.tag.category.view.form</field>
        <field name="model">event.track.tag.category</field>
        <field name="arch" type="xml">
            <form string="Track Tag Category">
                <sheet>
                    <group>
                        <field name="name"/>
                        <field name="tag_ids" context="{'default_category_id': id}">
                            <list string="Tags" editable="bottom">
                                <field name="sequence" widget="handle"/>
                                <field name="name"/>
                                <field name="color" widget="color_picker"/>
                            </list>
                        </field>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <record id="event_track_tag_category_view_list" model="ir.ui.view">
        <field name="name">event.track.tag.category.view.list</field>
        <field name="model">event.track.tag.category</field>
        <field name="arch" type="xml">
            <list string="Track Tags Category">
                <field name="sequence" widget="handle"/>
                <field name="name"/>
                <field name="tag_ids" widget="many2many_tags" options="{'color_field': 'color'}"/>
            </list>
        </field>
    </record>

    <record id="event_track_tag_category_action" model="ir.actions.act_window">
        <field name="name">Track Tag Categories</field>
        <field name="res_model">event.track.tag.category</field>
        <field name="view_mode">list,form</field>
    </record>

    <record model="ir.ui.view" id="view_event_track_tag_form">
        <field name="name">Track Tags</field>
        <field name="model">event.track.tag</field>
        <field name="arch" type="xml">
            <form string="Event Track Tag">
                <sheet>
                    <group>
                        <field name="name"/>
                        <field name="color" widget="color_picker"/>
                    <field name="category_id"/>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <record model="ir.ui.view" id="view_event_track_tag_tree">
        <field name="name">Tracks Tag</field>
        <field name="model">event.track.tag</field>
        <field name="arch" type="xml">
            <list string="Event Track Tag" editable="bottom">
                <field name="sequence" widget="handle"/>
                <field name="name"/>
                <field name="category_id"/>
                <field name="color" widget="color_picker"/>
            </list>
        </field>
    </record>

    <record model="ir.actions.act_window" id="action_event_track_tag">
        <field name="name">Track Tags</field>
        <field name="res_model">event.track.tag</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a Track Tag
            </p><p>
                Add tags to your tracks to help your attendees browse your event web pages.
            </p>
        </field>
    </record>

</odoo>
