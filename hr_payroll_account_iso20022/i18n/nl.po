# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_payroll_account_iso20022
# 
# Translators:
# <PERSON><PERSON>, 2025
# Wil Odoo, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-03-26 20:47+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Wil Odoo, 2025\n"
"Language-Team: Dutch (https://app.transifex.com/odoo/teams/41243/nl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: nl\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: hr_payroll_account_iso20022
#: model_terms:ir.ui.view,arch_db:hr_payroll_account_iso20022.view_employee_form
msgid ""
"<span class=\"d-flex gap-2 w-50\">\n"
"                            <span invisible=\"is_trusted_bank_account\" class=\"text-muted\">Untrusted</span>\n"
"                            <span invisible=\"not is_trusted_bank_account\" class=\"text-success\">Trusted</span>\n"
"                        </span>"
msgstr ""
"<span class=\"d-flex gap-2 w-50\">\n"
"                            <span invisible=\"is_trusted_bank_account\" class=\"text-muted\">Niet vertrouwd</span>\n"
"                            <span invisible=\"not is_trusted_bank_account\" class=\"text-success\">Vertrouwd</span>\n"
"                        </span>"

#. module: hr_payroll_account_iso20022
#: model_terms:ir.ui.view,arch_db:hr_payroll_account_iso20022.view_employee_form
msgid "Bank Account"
msgstr "Bankrekening"

#. module: hr_payroll_account_iso20022
#: model:ir.model.fields,field_description:hr_payroll_account_iso20022.field_hr_payroll_payment_report_wizard__journal_id
msgid "Bank Journal"
msgstr "Bankdagboek"

#. module: hr_payroll_account_iso20022
#: model_terms:ir.ui.view,arch_db:hr_payroll_account_iso20022.hr_payslip_run_view_form
#: model_terms:ir.ui.view,arch_db:hr_payroll_account_iso20022.hr_payslip_view_form
msgid "Create Payment Report"
msgstr "Betalingsrapport maken"

#. module: hr_payroll_account_iso20022
#: model:ir.model,name:hr_payroll_account_iso20022.model_hr_employee
msgid "Employee"
msgstr "Werknemer"

#. module: hr_payroll_account_iso20022
#: model:hr.payroll.dashboard.warning,name:hr_payroll_account_iso20022.hr_payroll_dashboard_warning_employee_invalid_bank_account
msgid "Employees With Invalid Bank Accounts"
msgstr "Werknemers met ongeldige bankrekeningen"

#. module: hr_payroll_account_iso20022
#: model:hr.payroll.dashboard.warning,name:hr_payroll_account_iso20022.hr_payroll_dashboard_warning_employee_no_country
msgid "Employees Without Any Country"
msgstr "Werknemers zonder land"

#. module: hr_payroll_account_iso20022
#: model:hr.payroll.dashboard.warning,name:hr_payroll_account_iso20022.hr_payroll_dashboard_warning_untrusted_bank_accounts
msgid "Employees with untrusted Bank Account numbers"
msgstr "Werknemers met onbetrouwbare bankrekeningnummers"

#. module: hr_payroll_account_iso20022
#: model:ir.model.fields,field_description:hr_payroll_account_iso20022.field_hr_payroll_payment_report_wizard__export_format
msgid "Export Format"
msgstr "Exportformaat"

#. module: hr_payroll_account_iso20022
#: model:ir.model,name:hr_payroll_account_iso20022.model_hr_payroll_payment_report_wizard
msgid "HR Payroll Payment Report Wizard"
msgstr "Wizard HR-Loonrapportage"

#. module: hr_payroll_account_iso20022
#: model_terms:ir.ui.view,arch_db:hr_payroll_account_iso20022.hr_payslip_run_view_form
#: model_terms:ir.ui.view,arch_db:hr_payroll_account_iso20022.hr_payslip_view_form
msgid "Mark as paid"
msgstr "Markeren als betaald"

#. module: hr_payroll_account_iso20022
#: model:ir.model,name:hr_payroll_account_iso20022.model_hr_payslip
msgid "Pay Slip"
msgstr "Loonstrook"

#. module: hr_payroll_account_iso20022
#: model:ir.model,name:hr_payroll_account_iso20022.model_hr_payslip_run
msgid "Payslip Batches"
msgstr "Loonstrookbatches"

#. module: hr_payroll_account_iso20022
#: model:ir.model.fields.selection,name:hr_payroll_account_iso20022.selection__hr_payroll_payment_report_wizard__export_format__sepa
msgid "SEPA"
msgstr "SEPA"

#. module: hr_payroll_account_iso20022
#: model:ir.model.fields,field_description:hr_payroll_account_iso20022.field_hr_employee__is_trusted_bank_account
msgid "Send Money"
msgstr "Betalen"

#. module: hr_payroll_account_iso20022
#: model:ir.model.fields,help:hr_payroll_account_iso20022.field_hr_employee__is_trusted_bank_account
msgid ""
"Sending fake invoices with a fraudulent account number is a common phishing "
"practice. To protect yourself, always verify new bank account numbers, "
"preferably by calling the vendor, as phishing usually happens when their "
"emails are compromised. Once verified, you can activate the ability to send "
"money."
msgstr ""
"Het verzenden van valse facturen met een frauduleus rekeningnummer is een "
"veel voorkomende phishingpraktijk. Om jezelf te beschermen moet je altijd "
"nieuwe rekeningsnummers controleren, bij voorkeur door de leverancier te "
"bellen, want phishing gebeurt meestal wanneer hun e-mails gecompromitteerd "
"zijn. Eenmaal gecontroleerd, kan je de functie om geld te sturen "
"inschakelen."

#. module: hr_payroll_account_iso20022
#. odoo-python
#: code:addons/hr_payroll_account_iso20022/wizard/hr_payroll_payment_report_wizard.py:0
msgid "Some employees (%s) don't have a valid name on the work contact."
msgstr "Sommige werknemers (%s) hebben geen geldige naam op het werkcontact."

#. module: hr_payroll_account_iso20022
#. odoo-python
#: code:addons/hr_payroll_account_iso20022/wizard/hr_payroll_payment_report_wizard.py:0
msgid "Some employees (%s) don't have a work contact."
msgstr "Sommige werknemers (%s) hebben geen werkcontact."

#. module: hr_payroll_account_iso20022
#: model:ir.model.fields.selection,name:hr_payroll_account_iso20022.selection__hr_payroll_payment_report_wizard__export_format__iso20022_ch
msgid "Swiss ISO20022"
msgstr "Zwitserse ISO20022"

#. module: hr_payroll_account_iso20022
#. odoo-python
#: code:addons/hr_payroll_account_iso20022/models/hr_employee.py:0
msgid ""
"The following employees have invalid bank accounts and could not be trusted:\n"
"%s"
msgstr ""
"De volgende werknemers hebben ongeldige bankrekeningen en zijn niet te vertrouwen:\n"
"%s"

#. module: hr_payroll_account_iso20022
#. odoo-python
#: code:addons/hr_payroll_account_iso20022/wizard/hr_payroll_payment_report_wizard.py:0
msgid ""
"The journal '%s' requires a proper IBAN account to pay via SEPA. Please "
"configure it first."
msgstr ""
"Het dagboek '%s' vereist een geldig IBAN account bij betalingen via SEPA. "
"Stel dit eerst in."

#. module: hr_payroll_account_iso20022
#: model_terms:ir.ui.view,arch_db:hr_payroll_account_iso20022.view_employee_form
msgid "Trust Bank Account"
msgstr "Bankrekening vertrouwen"

#. module: hr_payroll_account_iso20022
#: model:ir.model.fields,field_description:hr_payroll_account_iso20022.field_hr_payslip__iso20022_uetr
msgid "UETR"
msgstr "UETR"

#. module: hr_payroll_account_iso20022
#: model:ir.model.fields,help:hr_payroll_account_iso20022.field_hr_payslip__iso20022_uetr
msgid "Unique end-to-end transaction reference"
msgstr "Unique end-to-end transaction reference"

#. module: hr_payroll_account_iso20022
#: model_terms:ir.ui.view,arch_db:hr_payroll_account_iso20022.view_employee_form
msgid "Untrust Bank Account"
msgstr "Bankrekening niet vertrouwen"

#. module: hr_payroll_account_iso20022
#. odoo-python
#: code:addons/hr_payroll_account_iso20022/models/hr_employee.py:0
msgid "You do not have the right to trust or un-trust a bank account."
msgstr "Je hebt niet het recht om een bankrekening te vertrouwen of niet."
