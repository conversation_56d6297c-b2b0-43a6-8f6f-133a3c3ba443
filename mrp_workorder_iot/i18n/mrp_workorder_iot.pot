# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* mrp_workorder_iot
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-25 09:13+0000\n"
"PO-Revision-Date: 2024-10-25 09:13+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: mrp_workorder_iot
#: model:ir.model.fields,field_description:mrp_workorder_iot.field_iot_trigger__action
msgid "Action"
msgstr ""

#. module: mrp_workorder_iot
#: model:ir.model.fields,field_description:mrp_workorder_iot.field_quality_check__boxes
msgid "Boxes"
msgstr ""

#. module: mrp_workorder_iot
#: model:ir.model.fields.selection,name:mrp_workorder_iot.selection__iot_trigger__action__canc
msgid "Cancel"
msgstr ""

#. module: mrp_workorder_iot
#. odoo-javascript
#: code:addons/mrp_workorder_iot/static/src/mrp_display/mrp_quality_check_confirmation_dialog.js:0
msgid "Check IoT Box connection. Try restarting if needed."
msgstr ""

#. module: mrp_workorder_iot
#: model:ir.model.fields.selection,name:mrp_workorder_iot.selection__iot_trigger__action__clmo
msgid "Close MO"
msgstr ""

#. module: mrp_workorder_iot
#: model:ir.model.fields.selection,name:mrp_workorder_iot.selection__iot_trigger__action__clwo
msgid "Close WO"
msgstr ""

#. module: mrp_workorder_iot
#. odoo-javascript
#: code:addons/mrp_workorder_iot/static/src/mrp_display/mrp_quality_check_confirmation_dialog.js:0
msgid "Connection failed"
msgstr ""

#. module: mrp_workorder_iot
#: model:ir.model.fields,field_description:mrp_workorder_iot.field_iot_trigger__create_uid
msgid "Created by"
msgstr ""

#. module: mrp_workorder_iot
#: model:ir.model.fields,field_description:mrp_workorder_iot.field_iot_trigger__create_date
msgid "Created on"
msgstr ""

#. module: mrp_workorder_iot
#: model:ir.model.fields,field_description:mrp_workorder_iot.field_iot_trigger__device_id
msgid "Device"
msgstr ""

#. module: mrp_workorder_iot
#: model:ir.model.fields,field_description:mrp_workorder_iot.field_iot_trigger__display_name
msgid "Display Name"
msgstr ""

#. module: mrp_workorder_iot
#: model:ir.model.fields.selection,name:mrp_workorder_iot.selection__iot_trigger__action__fini
msgid "Finish"
msgstr ""

#. module: mrp_workorder_iot
#: model:ir.model.fields,field_description:mrp_workorder_iot.field_iot_trigger__id
msgid "ID"
msgstr ""

#. module: mrp_workorder_iot
#: model:ir.model,name:mrp_workorder_iot.model_iot_device
msgid "IOT Device"
msgstr ""

#. module: mrp_workorder_iot
#: model:ir.model,name:mrp_workorder_iot.model_iot_trigger
msgid "IOT Trigger"
msgstr ""

#. module: mrp_workorder_iot
#: model_terms:ir.ui.view,arch_db:mrp_workorder_iot.iot_device_view_form
#: model_terms:ir.ui.view,arch_db:mrp_workorder_iot.mrp_workcenter_view_form_iot
msgid "IoT Triggers"
msgstr ""

#. module: mrp_workorder_iot
#: model:ir.model.fields,field_description:mrp_workorder_iot.field_iot_trigger__key
msgid "Key"
msgstr ""

#. module: mrp_workorder_iot
#: model:ir.model.fields,field_description:mrp_workorder_iot.field_iot_trigger__write_uid
msgid "Last Updated by"
msgstr ""

#. module: mrp_workorder_iot
#: model:ir.model.fields,field_description:mrp_workorder_iot.field_iot_trigger__write_date
msgid "Last Updated on"
msgstr ""

#. module: mrp_workorder_iot
#: model:ir.model.fields.selection,name:mrp_workorder_iot.selection__iot_trigger__action__next
msgid "Next"
msgstr ""

#. module: mrp_workorder_iot
#: model:ir.model.fields.selection,name:mrp_workorder_iot.selection__iot_trigger__action__pack
msgid "Pack"
msgstr ""

#. module: mrp_workorder_iot
#: model:ir.model.fields.selection,name:mrp_workorder_iot.selection__iot_trigger__action__paus
msgid "Pause"
msgstr ""

#. module: mrp_workorder_iot
#. odoo-javascript
#: code:addons/mrp_workorder_iot/static/src/mrp_display/pedal_status_button.xml:0
msgid "Pedal Status"
msgstr ""

#. module: mrp_workorder_iot
#: model:ir.model.fields.selection,name:mrp_workorder_iot.selection__iot_trigger__action__prev
msgid "Previous"
msgstr ""

#. module: mrp_workorder_iot
#: model:ir.model.fields.selection,name:mrp_workorder_iot.selection__iot_trigger__action__prsl
msgid "Print Delivery Slip"
msgstr ""

#. module: mrp_workorder_iot
#: model:ir.model.fields.selection,name:mrp_workorder_iot.selection__iot_trigger__action__prnt
msgid "Print Labels"
msgstr ""

#. module: mrp_workorder_iot
#: model:ir.model.fields.selection,name:mrp_workorder_iot.selection__iot_trigger__action__prop
msgid "Print Operation"
msgstr ""

#. module: mrp_workorder_iot
#: model:ir.model,name:mrp_workorder_iot.model_quality_check
msgid "Quality Check"
msgstr ""

#. module: mrp_workorder_iot
#: model:ir.model.fields.selection,name:mrp_workorder_iot.selection__iot_trigger__action__reco
msgid "Record Production"
msgstr ""

#. module: mrp_workorder_iot
#: model:ir.model.fields.selection,name:mrp_workorder_iot.selection__iot_trigger__action__scra
msgid "Scrap"
msgstr ""

#. module: mrp_workorder_iot
#: model:ir.model.fields,field_description:mrp_workorder_iot.field_iot_trigger__sequence
msgid "Sequence"
msgstr ""

#. module: mrp_workorder_iot
#: model:ir.model.fields.selection,name:mrp_workorder_iot.selection__iot_trigger__action__skip
msgid "Skip"
msgstr ""

#. module: mrp_workorder_iot
#: model:ir.model.fields.selection,name:mrp_workorder_iot.selection__iot_trigger__action__picture
msgid "Take Picture"
msgstr ""

#. module: mrp_workorder_iot
#: model:ir.model.fields,field_description:mrp_workorder_iot.field_iot_device__trigger_ids
msgid "Trigger"
msgstr ""

#. module: mrp_workorder_iot
#: model:ir.model.fields,field_description:mrp_workorder_iot.field_mrp_workcenter__trigger_ids
msgid "Triggers"
msgstr ""

#. module: mrp_workorder_iot
#: model:ir.model.fields.selection,name:mrp_workorder_iot.selection__iot_trigger__action__vali
msgid "Validate"
msgstr ""

#. module: mrp_workorder_iot
#: model:ir.model,name:mrp_workorder_iot.model_mrp_workcenter
msgid "Work Center"
msgstr ""

#. module: mrp_workorder_iot
#: model:ir.model.fields,field_description:mrp_workorder_iot.field_iot_trigger__workcenter_id
msgid "Workcenter"
msgstr ""
