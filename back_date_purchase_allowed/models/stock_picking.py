# -*- coding: utf-8 -*-
# Softprime Consulting Pvt Ltd
# Copyright (C) Softprime Consulting Pvt Ltd
# All Rights Reserved
# https://softprimeconsulting.com/
from datetime import datetime, timedelta
from odoo.exceptions import UserError
import pytz
from odoo import models, api, _, fields
from odoo.tools import DEFAULT_SERVER_DATETIME_FORMAT
from odoo.exceptions import ValidationError


class StockPicking(models.Model):
    _inherit = 'stock.picking'

    def button_validate(self):
        """Validate the stock move and set the correct dates with timezone handling."""
        print('button validate')
        if self.purchase_id and self.purchase_id.date_order and self.purchase_id.is_back_order:
            if not self.env.user.allowed_back_date_purchase:
                raise ValidationError(_("You are not allowed to post backorder pickings."))
            if self.purchase_id.date_order != self.scheduled_date:
                raise ValidationError(_(
                    f"Scheduled date ({self.scheduled_date.date()}) must match the purchase order date ({self.purchase_id.date_order.date()})."
                ))
            ctx = self.env.context.copy()
            ctx.update({'force_period_date': self.scheduled_date})
        result = super().button_validate()
        # If backdate purchase is allowed, update date_deadline and move line dates
        if self.purchase_id and self.purchase_id.date_order and self.purchase_id.is_back_order and self.env.user.allowed_back_date_purchase:
            self.date_deadline = self.scheduled_date
            # Update move lines and moves with the adjusted scheduled date
            for move in self.move_ids_without_package:
                move.reservation_date = self.scheduled_date.date()
                move.date_deadline = self.scheduled_date
                move.date = self.scheduled_date
            for line in self.move_line_ids_without_package:
                line.date = self.scheduled_date
        return result

    def _action_done(self):
        print('action_done')
        if self.env.user.allowed_back_date_purchase and self.purchase_id and self.purchase_id.is_back_order:
            ctx = self.env.context.copy()
            ctx.update({'force_period_date': self.scheduled_date})
        res = super(StockPicking, self)._action_done()
        if self.env.user.allowed_back_date_purchase and self.purchase_id and self.purchase_id.is_back_order:
            self.date_done = self.scheduled_date
        return res
