# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* voip_onsip
#
# Translators:
# Q<PERSON>jo<PERSON>, 2023
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.5alpha1+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-01-17 10:16+0000\n"
"PO-Revision-Date: 2022-09-22 05:49+0000\n"
"Last-Translator: Qaidjohar Barbhaya, 2023\n"
"Language-Team: Gujarati (https://app.transifex.com/odoo/teams/41243/gu/)\n"
"Language: gu\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: voip_onsip
#: model:ir.model,name:voip_onsip.model_res_config_settings
msgid "Config Settings"
msgstr "Config Settings"

#. module: voip_onsip
#: model:ir.model.fields,field_description:voip_onsip.field_res_users__onsip_auth_username
#: model:ir.model.fields,field_description:voip_onsip.field_res_users_settings__onsip_auth_username
msgid "OnSIP Auth Username"
msgstr ""

#. module: voip_onsip
#: model_terms:ir.ui.view,arch_db:voip_onsip.res_config_settings_view_form
msgid "OnSIP Domain"
msgstr ""

#. module: voip_onsip
#: model:ir.model.fields,help:voip_onsip.field_res_config_settings__wsServer
msgid "The URL of your WebSocket"
msgstr ""

#. module: voip_onsip
#: model:ir.model,name:voip_onsip.model_res_users
msgid "User"
msgstr "User"

#. module: voip_onsip
#: model:ir.model,name:voip_onsip.model_res_users_settings
msgid "User Settings"
msgstr ""

#. module: voip_onsip
#: model:ir.model.fields,field_description:voip_onsip.field_res_config_settings__wsServer
msgid "WebSocket"
msgstr ""
