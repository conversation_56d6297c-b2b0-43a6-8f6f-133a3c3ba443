# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* voip_onsip
# 
# Translators:
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:29+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Russian (https://app.transifex.com/odoo/teams/41243/ru/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ru\n"
"Plural-Forms: nplurals=4; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<12 || n%100>14) ? 1 : n%10==0 || (n%10>=5 && n%10<=9) || (n%100>=11 && n%100<=14)? 2 : 3);\n"

#. module: voip_onsip
#: model:ir.model.fields,field_description:voip_onsip.field_res_users_settings__onsip_auth_username
msgid "OnSIP Auth Username"
msgstr "Имя пользователя OnSIP Auth"

#. module: voip_onsip
#: model_terms:ir.ui.view,arch_db:voip_onsip.voip_provider_tree_view_inherit
msgid "OnSIP Domain"
msgstr "Домен OnSIP"

#. module: voip_onsip
#: model:ir.model.fields,field_description:voip_onsip.field_res_users__onsip_auth_username
msgid "Onsip Auth Username"
msgstr ""

#. module: voip_onsip
#: model:ir.model.fields,help:voip_onsip.field_voip_provider__ws_server
msgid "The URL of your WebSocket"
msgstr "URL вашего WebSocket"

#. module: voip_onsip
#: model:ir.model,name:voip_onsip.model_res_users
msgid "User"
msgstr "Пользователь"

#. module: voip_onsip
#: model:ir.model,name:voip_onsip.model_res_users_settings
msgid "User Settings"
msgstr "Настройки пользователя"

#. module: voip_onsip
#: model:ir.model,name:voip_onsip.model_voip_provider
msgid "VoIP Provider"
msgstr ""

#. module: voip_onsip
#: model:ir.model.fields,field_description:voip_onsip.field_voip_provider__ws_server
msgid "WebSocket"
msgstr "WebSocket"
