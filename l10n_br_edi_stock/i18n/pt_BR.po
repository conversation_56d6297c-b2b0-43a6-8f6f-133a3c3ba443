# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_br_edi_stock
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-02-04 18:36+0000\n"
"PO-Revision-Date: 2025-02-05 12:51-0300\n"
"Last-Translator: \n"
"Language-Team: \n"
"Language: pt_BR\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"
"X-Generator: Poedit 3.0.1\n"

#. module: l10n_br_edi_stock
#. odoo-python
#: code:addons/l10n_br_edi_stock/models/account_move.py:0
msgid "%(amount)s of %(total)s"
msgstr "%(amount)s de %(total)s"

#. module: l10n_br_edi_stock
#: model:ir.model.fields,field_description:l10n_br_edi_stock.field_stock_package_type__l10n_br_brand
msgid "Brand"
msgstr "Marca"

#. module: l10n_br_edi_stock
#: model:ir.model.fields,help:l10n_br_edi_stock.field_stock_package_type__l10n_br_brand
msgid "Brazil: brand of the packaging."
msgstr "Brasil: marca da embalagem."

#. module: l10n_br_edi_stock
#: model:ir.model.fields,help:l10n_br_edi_stock.field_account_bank_statement_line__l10n_br_package_ids
#: model:ir.model.fields,help:l10n_br_edi_stock.field_account_move__l10n_br_package_ids
msgid ""
"Brazil: packages to include in the NF-e used on the deliveries linked to "
"this sales transaction."
msgstr ""
"Brasil: pacotes a incluir na NF-e usada em entregas vinculadas e essa "
"transação de venda."

#. module: l10n_br_edi_stock
#: model:ir.model.fields,help:l10n_br_edi_stock.field_account_bank_statement_line__l10n_br_plate_number
#: model:ir.model.fields,help:l10n_br_edi_stock.field_account_move__l10n_br_plate_number
msgid "Brazil: vehicle plate number of the delivery vehicle."
msgstr "Brasil: número da placa do veículo de entrega."

#. module: l10n_br_edi_stock
#: model_terms:ir.ui.view,arch_db:l10n_br_edi_stock.view_move_form
msgid "Delivery"
msgstr "Entrega"

#. module: l10n_br_edi_stock
#: model:ir.model,name:l10n_br_edi_stock.model_account_move
msgid "Journal Entry"
msgstr "Lançamento no diário"

#. module: l10n_br_edi_stock
#: model:ir.model.fields,field_description:l10n_br_edi_stock.field_stock_quant_package__l10n_br_move_id
msgid "L10N Br Move"
msgstr "Movimentação L10N BR"

#. module: l10n_br_edi_stock
#: model:ir.model.fields,field_description:l10n_br_edi_stock.field_account_bank_statement_line__l10n_br_picking_count
#: model:ir.model.fields,field_description:l10n_br_edi_stock.field_account_move__l10n_br_picking_count
msgid "L10N Br Picking Count"
msgstr "Total de separações L10N Br"

#. module: l10n_br_edi_stock
#: model:ir.model,name:l10n_br_edi_stock.model_stock_quant_package
#: model:ir.model.fields,field_description:l10n_br_edi_stock.field_account_bank_statement_line__l10n_br_package_ids
#: model:ir.model.fields,field_description:l10n_br_edi_stock.field_account_move__l10n_br_package_ids
msgid "Packages"
msgstr "Pacotes"

#. module: l10n_br_edi_stock
#: model:ir.model.fields,field_description:l10n_br_edi_stock.field_account_bank_statement_line__l10n_br_plate_number
#: model:ir.model.fields,field_description:l10n_br_edi_stock.field_account_move__l10n_br_plate_number
msgid "Plate Number"
msgstr "Número da placa"

#. module: l10n_br_edi_stock
#: model:ir.model.fields,field_description:l10n_br_edi_stock.field_account_bank_statement_line__l10n_br_related_package_ids
#: model:ir.model.fields,field_description:l10n_br_edi_stock.field_account_move__l10n_br_related_package_ids
msgid "Related Packages"
msgstr "Pacotes relacionados"

#. module: l10n_br_edi_stock
#: model:ir.model,name:l10n_br_edi_stock.model_sale_order
msgid "Sales Order"
msgstr "Pedido de venda"

#. module: l10n_br_edi_stock
#: model:ir.model,name:l10n_br_edi_stock.model_stock_package_type
msgid "Stock package type"
msgstr "Tipo de pacote do estoque"

#. module: l10n_br_edi_stock
#: model:ir.model.fields,help:l10n_br_edi_stock.field_stock_quant_package__l10n_br_move_id
msgid ""
"Technical field that assigns this package to an invoice for Brazilian EDI."
msgstr ""
"Campo técnico que atribui este pacote a uma fatura de EDI brasileiro."

#. module: l10n_br_edi_stock
#. odoo-python
#: code:addons/l10n_br_edi_stock/models/account_move.py:0
msgid "Volume"
msgstr "Volume"
