# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_be_hr_payroll_account
# 
# Translators:
# <PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.2alpha1+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-02-26 09:19+0000\n"
"PO-Revision-Date: 2025-02-07 17:07+0000\n"
"Last-Translator: <PERSON>, 2025\n"
"Language-Team: Dutch (https://app.transifex.com/odoo/teams/41243/nl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: nl\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: l10n_be_hr_payroll_account
#: model:ir.model,name:l10n_be_hr_payroll_account.model_l10n_be_274_xx
msgid "274.XX Sheets"
msgstr "274.XX aangiftes"

#. module: l10n_be_hr_payroll_account
#: model:ir.model,name:l10n_be_hr_payroll_account.model_account_chart_template
msgid "Account Chart Template"
msgstr "Grootboekschema sjabloon"

#. module: l10n_be_hr_payroll_account
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_account.field_l10n_be_274_xx__move_id
msgid "Accounting Entry"
msgstr "Boeking"

#. module: l10n_be_hr_payroll_account
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll_account.l10n_be_274_XX_view_form
msgid "Accounting Entry Generated"
msgstr "Gegenereerde boeking"

#. module: l10n_be_hr_payroll_account
#. odoo-python
#: code:addons/l10n_be_hr_payroll_account/models/l10n_be_274_XX.py:0
msgid "Adjustment Entry"
msgstr "Aanpassingsboeking"

#. module: l10n_be_hr_payroll_account
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_account.field_res_config_settings__exemption_bachelor_account_id
msgid "Bachelors"
msgstr "Bachelor"

#. module: l10n_be_hr_payroll_account
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_account.field_res_config_settings__exemption_bachelor_capping_account_id
msgid "Bachelors Capping"
msgstr "Bachelors Capping"

#. module: l10n_be_hr_payroll_account
#: model:ir.model,name:l10n_be_hr_payroll_account.model_res_company
msgid "Companies"
msgstr "Bedrijven"

#. module: l10n_be_hr_payroll_account
#: model:ir.model,name:l10n_be_hr_payroll_account.model_res_config_settings
msgid "Config Settings"
msgstr "Configuratie instellingen"

#. module: l10n_be_hr_payroll_account
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_account.field_res_config_settings__exemption_doctor_master_account_id
msgid "Doctors/Civil Engineers/Masters"
msgstr "Dokters/Burgerlijk Ingenieurs/Masters"

#. module: l10n_be_hr_payroll_account
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_account.field_res_company__exemption_bachelor_account_id
msgid "Exemption Bachelor Account"
msgstr "Vrijstelling Bachelor Account"

#. module: l10n_be_hr_payroll_account
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_account.field_res_company__exemption_bachelor_capping_account_id
msgid "Exemption Bachelor Capping Account"
msgstr "Vrijstelling Bachelor Capping Account"

#. module: l10n_be_hr_payroll_account
#. odoo-python
#: code:addons/l10n_be_hr_payroll_account/models/l10n_be_274_XX.py:0
msgid "Exemption Capping for bachelors"
msgstr "Vijstelling Capping voor Bachelors"

#. module: l10n_be_hr_payroll_account
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_account.field_res_company__exemption_doctor_master_account_id
msgid "Exemption Doctor Master Account"
msgstr "Vrijstelling Dokter Master Account"

#. module: l10n_be_hr_payroll_account
#. odoo-python
#: code:addons/l10n_be_hr_payroll_account/models/l10n_be_274_XX.py:0
msgid "Exemption for bachelors"
msgstr "Vrijstelling voor bachelors"

#. module: l10n_be_hr_payroll_account
#. odoo-python
#: code:addons/l10n_be_hr_payroll_account/models/l10n_be_274_XX.py:0
msgid "Exemption for doctors/civil engineers/masters"
msgstr "Vrijstelling voor dokters/burgerlijke ingenieurs/masters"

#. module: l10n_be_hr_payroll_account
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll_account.res_config_settings_view_form
msgid "Journal Entries Accounts"
msgstr "Rekeningen boekingen"

#. module: l10n_be_hr_payroll_account
#. odoo-python
#: code:addons/l10n_be_hr_payroll_account/models/l10n_be_274_XX.py:0
msgid "Please define a default journal on the exmption journal!"
msgstr "Definieer een standaard dagboek op het vrijstellingsdagboek!"

#. module: l10n_be_hr_payroll_account
#. odoo-python
#: code:addons/l10n_be_hr_payroll_account/models/l10n_be_274_XX.py:0
msgid ""
"Please make sure that the journal and the accounts are correctly configured "
"on the Payroll Settings."
msgstr ""
"Controleer of het dagboek en de rekeningen correct zijn ingesteld in de "
"Lonen-instellingen."

#. module: l10n_be_hr_payroll_account
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll_account.l10n_be_274_XX_view_form
msgid "Post Journal Entries"
msgstr "Boekingen boeken"

#. module: l10n_be_hr_payroll_account
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_account.field_res_company__exemption_journal_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll_account.field_res_config_settings__exemption_journal_id
msgid "Salary Journal"
msgstr "Loondagboek"

#. module: l10n_be_hr_payroll_account
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll_account.l10n_be_274_XX_view_form
msgid ""
"The withholing taxes exemption has been posted on the accounting entries:"
msgstr "De vrijstelling van bronbelasting is in de boekhouding verwerkt:"

#. module: l10n_be_hr_payroll_account
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll_account.res_config_settings_view_form
msgid "Withholding Taxes Exemption"
msgstr "Vrijstelling van bronbelasting"

#. module: l10n_be_hr_payroll_account
#. odoo-python
#: code:addons/l10n_be_hr_payroll_account/models/l10n_be_274_XX.py:0
msgid "Withholding Taxes Exemption for %s"
msgstr "Vrijstelling van bronbelasting voor %s"
