# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_bank_statement_import_ofx
# 
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:26+0000\n"
"PO-Revision-Date: 2024-09-25 09:43+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Romanian (https://app.transifex.com/odoo/teams/41243/ro/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ro\n"
"Plural-Forms: nplurals=3; plural=(n==1?0:(((n%100>19)||((n%100==0)&&(n!=0)))?2:1));\n"

#. module: account_bank_statement_import_ofx
#: model:ir.model,name:account_bank_statement_import_ofx.model_account_journal
msgid "Journal"
msgstr "Jurnal"

#. module: account_bank_statement_import_ofx
#. odoo-python
#: code:addons/account_bank_statement_import_ofx/models/account_journal.py:0
msgid "The library 'ofxparse' is missing, OFX import cannot proceed."
msgstr "Biblioteca „ofxparse” lipsește, importul OFX nu poate continua."
