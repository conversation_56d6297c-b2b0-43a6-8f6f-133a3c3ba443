<?xml version="1.0" encoding="UTF-8" ?>
<odoo>
    <!-- Form view -->
    <record id="sale_order_form_view" model="ir.ui.view">
        <field name="name">sale.order.form.view.vg.sale</field>
        <field name="model">sale.order</field>
        <field name="inherit_id" ref="sale.view_order_form"/>
        <field name="arch" type="xml">
            <field name="partner_id" position="after">
                <field name="customer_code" readonly="1"/>
                <field name="purchase_order_ref" invisible="1"/>
            </field>
            <field name="validity_date" position="before">
                <field name="branch_id" required="True"
                       readonly="state != 'draft'" options="{'no_open':True,'no_create':True}"/>
            </field>
            <field name="sale_order_template_id" position="after">
                <field name="analytic_account_id" options="{'no_open':True,'no_create':True}"/>
                <field name="is_advance_invoice" widget="boolean_toggle"/>
                <field name="is_custom_invoice" widget="boolean_toggle"/>
                <field name="advance_ref" invisible="not is_advance_invoice" readonly="1"/>
            </field>
            <xpath expr="//header" position="inside">
                <button name="action_print_advance_invoice" type="object" string="Print Advance Invoice"
                        invisible="not is_advance_invoice" class="btn-primary"/>
                <button name="action_print_custom_invoice" type="object" string="Print Custom Invoice"
                        invisible="not is_custom_invoice" class="btn-primary"/>
                <button name="action_print_sale_order_pdf" type="object" string="Print Sale Order"
                        invisible="state != 'sale'" class="btn-primary"/>
            </xpath>
            <field name="user_id" position="after">
                <field name="employee_id" required="1" readonly="state != 'draft'"/>
            </field>
            <field name="order_type" position="after">
                <field name="warehouse_domain_ids" widget="many2many_tags" force_save="1"
                       options="{'no_create':True,'no_open':True}" invisible="1"/>
                <field name="custom_warehouse_id" required="1" force_save="1" readonly="state != 'draft' "
                       domain="[('id','in',warehouse_domain_ids)]" options="{'no_create':True,'no_open':True}"/>
                <field name="is_specific_customer_sale" force_save="1" invisible="1"/>
                <field name="warehouse_id" invisible="1" force_save="1"/>
            </field>
            <!--            <xpath expr="//page[@name='order_lines']//field[@name='order_line']//list//field[@name='name']"-->
            <!--                   position="attributes">-->
            <!--                <attribute name="column_invisible">0</attribute>-->
            <!--            </xpath>-->
            <xpath expr="//page[@name='order_lines']//field[@name='order_line']//list//field[@name='product_uom_qty']"
                   position="after">
                <field name="advance_invoice_desc"
                       column_invisible="not parent.is_advance_invoice or not parent.is_custom_invoice"
                       readonly="state != 'draft'"/>
                <field name="advance_invoice_uom_id"
                       column_invisible="not parent.is_advance_invoice or not parent.is_custom_invoice"
                       options="{'no_create':True,'no_create':True}" readonly="state != 'draft'"/>
            </xpath>
        </field>
    </record>
</odoo>