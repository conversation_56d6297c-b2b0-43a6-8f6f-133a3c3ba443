<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Template -->
    <template id="custom_invoice_template">
        <t t-call="web.html_container">
            <t t-call="web.external_layout">
                <t t-foreach="docs" t-as="o">
                    <div class="page">

                        <!-- Header info -->
                        <div style="position: relative;width:100%;margin-bottom:20px;">
                            <!-- Sold To Section -->
                            <div style="float:left;display:inline-block;width: 33%;padding: 5px;">
                                <div style="font-weight: bold; font-size: 18px;">Sold To:</div>
                                <div style="font-size: 16px;">
                                    <t t-esc="o.partner_id.name"/>
                                </div>
                            </div>

                            <!-- Credit Invoice Section -->
                            <div style="float:left;display:inline-block;width: 33%; text-align: center; font-weight: bold;  padding: 5px;">
                                <h4>Credit Invoice</h4>
                            </div>

                            <!-- Invoice Info Section -->
                            <div style="float:left;display:inline-block;width: 33%; padding: 5px;">
                                <table class="table table-borderless">
                                    <tbody>
                                        <tr>
                                            <td class="text-center fw-bold">Invoice</td>
                                            <td class="text-center text-nowrap">

                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="text-center fw-bold">Date</td>
                                            <td class="text-center">
                                                <t t-esc="o.date_order"/>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <table class="table table-bordered">
                            <thead>
                                <tr>
                                    <th class="text-center fw-bold">Location</th>
                                    <th class="text-center fw-bold text-nowrap">Originator</th>
                                    <th class="text-center fw-bold text-nowrap">S.O.No</th>
                                    <th class="text-center fw-bold text-nowrap">Customer P.O.No.</th>
                                    <th class="text-center fw-bold text-nowrap">Delivery No.</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>
                                        <t t-esc="o.order_type"/>
                                    </td>
                                    <td>
                                        <t t-esc="o.user_id.name"/>
                                    </td>
                                    <td>
                                        <t t-esc="o.name"/>
                                    </td>
                                    <td>
                                        <t t-esc="o.purchase_order_ref"/>
                                    </td>
                                    <td>

                                    </td>
                                </tr>
                            </tbody>
                        </table>

                        <table class="table table-bordered">
                            <thead>
                                <tr>
                                    <th class="text-center fw-bold">No.</th>
                                    <th class="text-center fw-bold text-nowrap">ITEM DESCRIPTION</th>
                                    <th class="text-center fw-bold text-nowrap">UOM</th>
                                    <th class="text-center fw-bold text-nowrap">QUANTITY</th>
                                    <th class="text-center fw-bold text-nowrap">UNIT PRICE</th>
                                    <th class="text-center fw-bold text-nowrap">EXTENDED PRICE</th>
                                </tr>
                            </thead>
                            <tbody>
                                <t t-set="index" t-value="0"/>
                                <t t-foreach="o.order_line" t-as="line">
                                    <tr>
                                        <td class="text-center">
                                            <t t-esc="index + 1"/>
                                            <t t-set="index" t-value="index + 1"/>
                                        </td>
                                        <td>
                                            <t t-esc="line.advance_invoice_desc"/>
                                        </td>
                                        <td class="text-center">
                                            <t t-esc="line.advance_invoice_uom_id.name"/>
                                        </td>
                                        <td class="text-end">
                                            <t t-esc="line.product_uom_qty"/>
                                        </td>
                                        <td class="text-end">
                                            <t t-esc="line.price_unit"/>
                                        </td>
                                        <td class="text-end" style="padding-right:1px;">
                                            <span t-field="line.price_subtotal"/>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td colspan="5">
                                            <span style="position:relative;padding-left:5px;font-weight:bold;">Total :
                                            </span>
                                            <span t-out="o.company_id.currency_id.name"/>
                                            <span t-out="o.currency_id.with_context(lang=o.partner_id.lang).amount_to_text(o.amount_total)"/>
                                        </td>
                                        <td class="text-end">
                                            <span t-field="o.amount_total"/>
                                        </td>
                                    </tr>
                                </t>
                            </tbody>
                        </table>
                    </div>
                </t>
            </t>
        </t>
    </template>


    <!-- PaperFormate -->
    <record id="custom_invoice_paperformate" model="report.paperformat">
        <field name="name">Custom Invoice Paperformate</field>
        <field name="default" eval="True"/>
        <field name="format">A4</field>
        <field name="orientation">Portrait</field>
        <field name="margin_top">35</field>
        <field name="margin_bottom">32</field>
        <field name="margin_left">1</field>
        <field name="margin_right">1</field>
        <field name="header_line" eval="False"/>
        <field name="header_spacing">35</field>
        <field name="dpi">90</field>
    </record>

    <!-- Action custom invoice -->
    <record id="action_custom_invoice_report" model="ir.actions.report">
        <field name="name">Custom Invoice</field>
        <field name="model">sale.order</field>
        <field name="report_type">qweb-pdf</field>
        <field name="paperformat_id" ref="custom_invoice_paperformate"/>
        <field name="report_name">vg_sale.custom_invoice_template</field>
        <field name="report_file">vg_sale.custom_invoice_template</field>
        <field name="print_report_name">'Custom Invoice'</field>
        <field name="binding_model_id" ref="model_sale_order"/>
        <field name="binding_type">report</field>
    </record>
</odoo>



