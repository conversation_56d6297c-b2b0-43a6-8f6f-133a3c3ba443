from odoo import fields, models, api, _
from odoo.exceptions import ValidationError


class SaleOrder(models.Model):
    _inherit = 'sale.order'

    @api.model
    def _default_employee(self):
        employee_id = self.env['hr.employee'].search([
            ('user_id', '=', self.env.user.id),
            ('company_id', '=', self.env.company.id)
        ], limit=1)
        return employee_id if employee_id else False

    analytic_account_id = fields.Many2one(
        comodel_name='account.analytic.account',
        string='Analytic Account',
        compute='_compute_analytic_account',
        store=True
    )
    advance_ref = fields.Char(
        string='Advance Ref',
        compute="_compute_advance_ref",
        store=True,
        readonly=False,
        copy=False,
        tracking=True
    )
    warehouse_domain_ids = fields.Many2many(
        comodel_name='stock.warehouse',
        relation='stock_warehouse_rel',
        column1='sale_order_id',
        column2='stock_warehouse_id',
        compute='_compute_warehouse_domain',
        store=True,
        string='Warehouse Domain'
    )
    branch_id = fields.Many2one(
        comodel_name='multi.branch',
        string='Branch',
        domain=lambda self: [('company_id', '=', self.env.company.id)]
    )
    is_advance_invoice = fields.Boolean(string='Is Advance Invoice', default=False)
    is_custom_invoice = fields.Boolean(string='Is Custom Invoice', default=False)
    warehouse_id = fields.Many2one('stock.warehouse', string='Warehouse', required=True, store=True)
    # Create a custom warehouse field to minimize errors caused by the default warehouse_id field
    custom_warehouse_id = fields.Many2one('stock.warehouse', string='Warehouse ', store=True)
    customer_code = fields.Char(string='Customer Code', related='partner_id.customer_reference', store=True)
    employee_id = fields.Many2one('hr.employee', string='Employee', default=_default_employee)
    purchase_order_ref = fields.Char(string='Purchase Order Ref', compute='_compute_purchase_order_ref', store=True)
    is_specific_customer_sale = fields.Boolean(string='Is Specific Customer Sale', default=False)

    @api.onchange('order_type')
    def set_custom_warehouse(self):
        self.custom_warehouse_id = False

    @api.depends('order_type')
    def _compute_warehouse_domain(self):
        for rec in self:
            rec.warehouse_domain_ids = False
            if rec.is_specific_customer_sale:
                if rec.order_type in ['mts', 'mto']:
                    warehouse_ids = self.env['stock.warehouse'].sudo().search([
                        ('order_type', '=', rec.order_type),
                        ('company_id', '=', rec.company_id.id),
                        ('customer_id', '=', rec.partner_id.id),
                    ])
                    if warehouse_ids:
                        rec.warehouse_domain_ids = warehouse_ids.ids
            else:
                if not rec.company_id:
                    raise ValidationError(_("Please provide the company in sale order."))
                if rec.order_type in ['mts', 'mto']:
                    warehouse_ids = self.env['stock.warehouse'].sudo().search([
                        ('order_type', '=', rec.order_type),
                        ('company_id', '=', rec.company_id.id)
                    ])
                    if warehouse_ids:
                        rec.warehouse_domain_ids = warehouse_ids.ids

    @api.onchange('custom_warehouse_id')
    def update_warehouse_id(self):
        if self.custom_warehouse_id:
            self.warehouse_id = self.custom_warehouse_id.id

    @api.onchange('warehouse_id')
    def update_correct_warehouse(self):
        """ if user on change the origin warehouse_id field then update custom_warehouse_id """
        if self.custom_warehouse_id:
            self.warehouse_id = self.custom_warehouse_id.id

    @api.depends('purchase_order_count')
    def _compute_purchase_order_ref(self):
        for order in self:
            po_ids = order._get_purchase_orders()
            if po_ids and po_ids[0]:
                order.purchase_order_ref = po_ids[0]['name']
            else:
                order.purchase_order_ref = ''

    @api.depends('name', 'is_advance_invoice')
    def _compute_advance_ref(self):
        for rec in self:
            if rec.name == "New":
                rec.advance_ref = "New"
            elif rec.is_advance_invoice:
                rec.advance_ref = f"A/{rec.name[1:]}"
            else:
                rec.advance_ref = ""

    @api.depends('branch_id', 'order_line')
    def _compute_analytic_account(self):
        for rec in self:
            rec.analytic_account_id = False
            if rec.branch_id and rec.branch_id.analytic_account_id:
                rec.analytic_account_id = rec.branch_id.analytic_account_id.id

            analytic_data = {
                rec.analytic_account_id.id: 100,
            } if rec.analytic_account_id else {}

            if rec.order_line:
                rec.order_line.write({'analytic_distribution': analytic_data})

    def action_print_advance_invoice(self):
        if not self.order_line:
            raise ValidationError(_("Please first add items to the order line before print pdf."))
        return self.env.ref("vg_sale.action_print_advance_invoice").report_action(self)

    def action_print_custom_invoice(self):
        if not self.order_line:
            raise ValidationError(_("Please first add items to the order line before print pdf."))
        return self.env.ref("vg_sale.action_custom_invoice_report").report_action(self)

    def action_print_sale_order_pdf(self):
        if not self.order_line:
            raise ValidationError(_("Please first add items to the order line before print pdf."))
        return self.env.ref("vg_sale.action_print_sale_order").report_action(self)

    def action_confirm(self):
        """ update branch and mto boolean from sale order to purchase order after confirm sale order """
        if not self.custom_warehouse_id:
            raise ValidationError(_("Please select warehouse."))
        res = super(SaleOrder, self).action_confirm()
        purchase_order_ids = self._get_purchase_orders()
        if self.order_type == 'mto' and purchase_order_ids:
            purchase_order_ids.write({'branch_id': self.branch_id.id, 'is_b2b_purchase': True})
        return res
