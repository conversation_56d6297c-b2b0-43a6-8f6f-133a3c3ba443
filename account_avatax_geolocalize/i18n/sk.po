# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_avatax_geolocalize
# 
# Translators:
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:26+0000\n"
"PO-Revision-Date: 2024-09-25 09:43+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Slovak (https://app.transifex.com/odoo/teams/41243/sk/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sk\n"
"Plural-Forms: nplurals=4; plural=(n % 1 == 0 && n == 1 ? 0 : n % 1 == 0 && n >= 2 && n <= 4 ? 1 : n % 1 != 0 ? 2: 3);\n"

#. module: account_avatax_geolocalize
#: model_terms:ir.ui.view,arch_db:account_avatax_geolocalize.res_partner_form_inherit
msgid "Compute Localization"
msgstr ""

#. module: account_avatax_geolocalize
#: model_terms:ir.ui.view,arch_db:account_avatax_geolocalize.res_partner_form_inherit
msgid "Compute based on address"
msgstr ""

#. module: account_avatax_geolocalize
#: model:ir.model,name:account_avatax_geolocalize.model_res_partner
msgid "Contact"
msgstr "Kontakt"

#. module: account_avatax_geolocalize
#: model:ir.model.fields,field_description:account_avatax_geolocalize.field_res_partner__is_avatax_valid
#: model:ir.model.fields,field_description:account_avatax_geolocalize.field_res_users__is_avatax_valid
msgid "Is Avatax Valid"
msgstr ""

#. module: account_avatax_geolocalize
#: model_terms:ir.ui.view,arch_db:account_avatax_geolocalize.res_partner_form_inherit
msgid "Refresh"
msgstr "Obnoviť"

#. module: account_avatax_geolocalize
#: model_terms:ir.ui.view,arch_db:account_avatax_geolocalize.res_partner_form_inherit
msgid "Refresh Localization"
msgstr ""

#. module: account_avatax_geolocalize
#: model:ir.model,name:account_avatax_geolocalize.model_avatax_validate_address
msgid "Suggests validated addresses from Avatax"
msgstr ""
