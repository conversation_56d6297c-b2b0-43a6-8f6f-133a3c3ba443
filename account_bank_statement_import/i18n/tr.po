# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_bank_statement_import
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2025
# <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2025
# <PERSON><PERSON>ü<PERSON>ü<PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:26+0000\n"
"PO-Revision-Date: 2024-09-25 09:43+0000\n"
"Last-Translator: <PERSON><PERSON>, 2025\n"
"Language-Team: Turkish (https://app.transifex.com/odoo/teams/41243/tr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: tr\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: account_bank_statement_import
#. odoo-python
#: code:addons/account_bank_statement_import/models/account_journal.py:0
msgid "%d transactions had already been imported and were ignored."
msgstr "%d işlemi zaten içe aktarılmış ve gözardı edildi."

#. module: account_bank_statement_import
#. odoo-python
#: code:addons/account_bank_statement_import/models/account_journal.py:0
msgid "1 transaction had already been imported and was ignored."
msgstr "1 işlem zaten içe aktarılmış ve gözardı edildi."

#. module: account_bank_statement_import
#: model:ir.model.constraint,message:account_bank_statement_import.constraint_account_bank_statement_line_unique_import_id
msgid "A bank account transactions can be imported only once!"
msgstr "Banka hesap hareketleri yalnızca bir kez içe aktarılabilir!"

#. module: account_bank_statement_import
#: model:ir.model,name:account_bank_statement_import.model_account_bank_statement_line
msgid "Bank Statement Line"
msgstr "Banka Hesap Ekstresi Kalemi"

#. module: account_bank_statement_import
#: model:ir.model,name:account_bank_statement_import.model_account_setup_bank_manual_config
msgid "Bank setup manual config"
msgstr "Banka kurulum yapılandırması"

#. module: account_bank_statement_import
#. odoo-python
#: code:addons/account_bank_statement_import/models/account_journal.py:0
msgid ""
"Cannot find in which journal import this statement. Please manually select a"
" journal."
msgstr ""
"Bu kaydın hangi yevmiyeye aktarılacağı bulunamıyor. Lütfen manuel olarak bir"
" yevmiye seçin."

#. module: account_bank_statement_import
#. odoo-python
#: code:addons/account_bank_statement_import/models/account_bank_statement.py:0
msgid "Click \"New\" or upload a %s."
msgstr "\"Yeni\"ye tıklayın veya bir tane yükleyin %s."

#. module: account_bank_statement_import
#. odoo-python
#: code:addons/account_bank_statement_import/models/account_journal.py:0
msgid ""
"Could not make sense of the given file.\n"
"Did you install the module to support this type of file?"
msgstr ""
"İlgili dosya ilişkilendirilemedi.\n"
"Bu dosyayı destekleyecek modülü yüklediniz mi?"

#. module: account_bank_statement_import
#. odoo-python
#: code:addons/account_bank_statement_import/models/account_journal.py:0
msgid "Go to Apps"
msgstr ""

#. module: account_bank_statement_import
#: model_terms:ir.ui.view,arch_db:account_bank_statement_import.journal_dashboard_view_inherit
msgid "Import File"
msgstr "Dosya İçe Aktarma"

#. module: account_bank_statement_import
#: model:ir.model.fields,field_description:account_bank_statement_import.field_account_bank_statement_line__unique_import_id
msgid "Import ID"
msgstr "ID İçe Aktar"

#. module: account_bank_statement_import
#. odoo-javascript
#: code:addons/account_bank_statement_import/static/src/account_bank_statement_import_model.js:0
msgid "Import Template for Bank Statements"
msgstr ""

#. module: account_bank_statement_import
#: model:ir.model,name:account_bank_statement_import.model_account_journal
msgid "Journal"
msgstr "Yevmiye"

#. module: account_bank_statement_import
#. odoo-python
#: code:addons/account_bank_statement_import/models/account_journal.py:0
msgid "Manual (or import %(import_formats)s)"
msgstr ""

#. module: account_bank_statement_import
#. odoo-python
#: code:addons/account_bank_statement_import/models/account_journal.py:0
msgid "No attachment was provided"
msgstr "Hiçbir ek sağlanmadı"

#. module: account_bank_statement_import
#. odoo-python
#: code:addons/account_bank_statement_import/models/account_journal.py:0
msgid "No currency found matching '%s'."
msgstr "Eşleşen para birimi bulunamadı '%s'."

#. module: account_bank_statement_import
#. odoo-python
#: code:addons/account_bank_statement_import/models/account_bank_statement.py:0
msgid "No transactions matching your filters were found."
msgstr "Filtrelerinizle eşleşen işlem bulunamadı."

#. module: account_bank_statement_import
#. odoo-python
#: code:addons/account_bank_statement_import/models/account_bank_statement.py:0
msgid "Nothing to do here!"
msgstr "Yapacak bir iş yok!"

#. module: account_bank_statement_import
#. odoo-python
#: code:addons/account_bank_statement_import/models/account_journal.py:0
msgid ""
"The account of this statement (%(account)s) is not the same as the journal "
"(%(journal)s)."
msgstr ""
"Bu ekstreye ait (%(account)s) hesabı, yevmiye (%(journal)s) ile aynı değil."

#. module: account_bank_statement_import
#. odoo-python
#: code:addons/account_bank_statement_import/models/account_journal.py:0
msgid ""
"The currency of the bank statement (%(code)s) is not the same as the "
"currency of the journal (%(journal)s)."
msgstr ""
"Banka hesap esktresinin para birimi (%(code)s) yevmiye para birimi ile aynı "
"değil (%(journal)s)."

#. module: account_bank_statement_import
#. odoo-python
#: code:addons/account_bank_statement_import/models/account_journal.py:0
msgid "The following files could not be imported:\n"
msgstr "Aşağıdaki dosyalar içeri alınamadı\n"

#. module: account_bank_statement_import
#. odoo-python
#: code:addons/account_bank_statement_import/models/account_journal.py:0
msgid ""
"This file doesn't contain any statement for account %s.\n"
"If it contains transactions for more than one account, it must be imported on each of them."
msgstr ""
"Bu dosya %s hesabına ait bir ekstre içermiyor. \n"
"Birden fazla hesap için işlemleri içeriyorsa, her bir hesap için ayrı aktarılmalıdır."

#. module: account_bank_statement_import
#. odoo-python
#: code:addons/account_bank_statement_import/models/account_journal.py:0
msgid ""
"This file doesn't contain any transaction for account %s.\n"
"If it contains transactions for more than one account, it must be imported on each of them."
msgstr ""
"Bu dosya %s hesabına ait bir hareket içermiyor.\n"
"Birden fazla hesap için işlemleri içeriyorsa, her bir hesap için ayrı aktarılmalıdır."

#. module: account_bank_statement_import
#. odoo-python
#: code:addons/account_bank_statement_import/models/account_journal.py:0
msgid "View successfully imported statements"
msgstr "Başarıyla aktarılmış ekstreleri görüntüleyin."

#. module: account_bank_statement_import
#. odoo-python
#: code:addons/account_bank_statement_import/models/account_journal.py:0
msgid "You already have imported that file."
msgstr "Bu dosyayı daha önce içeri aktarmıştınız."

#. module: account_bank_statement_import
#. odoo-python
#: code:addons/account_bank_statement_import/models/account_journal.py:0
msgid "You have to set a Default Account for the journal: %s"
msgstr "%s yevmiyesi için bir ön tanımlı hesap belirlemelisiniz."

#. module: account_bank_statement_import
#. odoo-python
#: code:addons/account_bank_statement_import/models/account_journal.py:0
msgid "You uploaded an invalid or empty file."
msgstr "Geçersiz veya boş bir dosya yüklediniz."
