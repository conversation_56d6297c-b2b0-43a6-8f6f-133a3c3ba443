# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_ch_hr_payroll_elm_transmission
# 
# Translators:
# <PERSON>, 2025
# <PERSON> <<EMAIL>>, 2025
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2025
# <PERSON><PERSON>, 2025
# <PERSON><PERSON> <<EMAIL>>, 2025
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-04-09 11:56+0000\n"
"PO-Revision-Date: 2025-02-12 11:36+0000\n"
"Last-Translator: <PERSON><PERSON>, 2025\n"
"Language-Team: Dutch (https://app.transifex.com/odoo/teams/41243/nl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: nl\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,help:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_salary_certificate_profile__l10n_ch_cs_free_meals
msgid ""
"\n"
"    Cafeteria Meals / Lunch Vouchers:\n"
"    - Option to have lower-cost meals at lunchtime or in the evening\n"
"    - Expense allowance for the main meal eaten outside on more than half of the working days\n"
"    "
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,help:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_salary_certificate_profile__l10n_ch_cs_free_transport
msgid ""
"\n"
"    Free transportation between home and workplace, this setting should be checked only:\n"
"    - If the worker should not incur any costs for commuting this setting should be checked\n"
"    -The commuting expenses are not reimbursed to the worker in section 2.3"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,help:l10n_ch_hr_payroll_elm_transmission.field_hr_contract__l10n_ch_permanent_staff_public_admin
msgid ""
"\n"
"A flag that allows for the clear identification of core personnel within public administrations. \n"
"This flag will only be used by public administrations (municipalities, cities, districts, cantons, the Confederation, etc.) and churches. \n"
"It will enable the distinction between core staff and various external mandates (such as exam experts, interpreters, etc.) and other engagements that are not part of the permanent workforce.\n"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,help:l10n_ch_hr_payroll_elm_transmission.field_hr_employee__l10n_ch_residence_type
msgid ""
"\n"
"Daily: \n"
"For PIS (Persons subject to Source tax) who do not have a residence or a place of stay in Switzerland, \n"
"the registered office or permanent establishment of the company is decisive. \n"
"This also applies notably to predefined categories (e.g., board member fees, exported employee participations, and special agreements with France).\n"
"\n"
"Weekly: \n"
"For PIS who do not have a residence but have a weekly place of stay in Switzerland, \n"
"the canton and municipality of the weekly stay (based on the address of the weekly stay) are decisive.\n"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,help:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_source_tax_institution__company_number
msgid ""
"\n"
"If a company manages separate payrolls (e.g., for branches or subsidiaries), the ACI may assign individual company numbers under a global DPI. \n"
"Use this field for any additional company number defined by the ACI."
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,help:l10n_ch_hr_payroll_elm_transmission.field_hr_employee_is_line__correction_method
msgid ""
"\n"
"Pick wheter this correction should be performed automatically based on current employee information, or if you wish to enter all source tax values manually\n"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,help:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_source_tax_institution__dpi_number
msgid ""
"\n"
"The DPI (Tax Source Identification) number is assigned by the ACI (Cantonal Tax Authority). \n"
"For new declarations, request the DPI number from the ACI before submitting. \n"
"Each entity or branch may have a separate DPI number or use a global DPI with separate company numbers for different declarations."
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,help:l10n_ch_hr_payroll_elm_transmission.field_hr_employee__l10n_ch_flex_profiling
msgid ""
"\n"
"This variable can only be provided if a prior agreement has been established between the OFS and the company as part of the Profiling process. \n"
"It involves additional information required to account for the specific characteristics of certain companies (e.g., to define the staff included).\n"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.actions.report,print_report_name:l10n_ch_hr_payroll_elm_transmission.action_report_payslip_ch_elm
msgid "'Payslip - %s' % (object.employee_id.name)"
msgstr "'Loonstrook - %s' % (object.employee_id.name)"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.report_company_master_data_document
msgid "- Master Data -"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.hr_contract_view_form
msgid "/ Hour"
msgstr "/ Uur"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.hr_contract_view_form
msgid "/ Lesson"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.hr_contract_view_form
msgid "/ month"
msgstr "/ maand"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_contract__laa_solution_number__0
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_payslip__laa_solution_number__0
msgid "0 - Not insured"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_contract__laa_solution_number__1
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_payslip__laa_solution_number__1
msgid "1 - Occupational and Non-Occupational Insured, with deductions"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_salary_rule__l10n_ch_salary_certificate__1
msgid "1. Salary"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_salary_rule__l10n_ch_salary_certificate__10_1
msgid "10.1 Regular contributions"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_salary_rule__l10n_ch_salary_certificate__10_2
msgid "10.2 Purchasing contribution"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_employee__l10n_ch_no_nationality__11
msgid "11 - Unknown"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_salary_rule__l10n_ch_salary_certificate__11
msgid "11. Net salary / Pension"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_salary_rule__l10n_ch_salary_certificate__12
msgid "12. Withholding tax deduction"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_salary_rule__l10n_ch_salary_certificate__13_1_1
msgid "13.1.1. Actual expenses - Trip, room and board"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_salary_rule__l10n_ch_salary_certificate__13_1_2
msgid "13.1.2. Actual expenses - Others"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_salary_rule__l10n_ch_salary_certificate__13_2_1
msgid "13.2.1. Overall expenses - Representation"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_salary_rule__l10n_ch_salary_certificate__13_2_2
msgid "13.2.2. Overall expenses - Car"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_salary_rule__l10n_ch_salary_certificate__13_2_3
msgid "13.2.3. Overall expenses - Other"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_salary_rule__l10n_ch_salary_certificate__13_3
msgid "13.3. Contributions to further education"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.swissdec_wage_types_tree
msgid "13e Mois"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_declaration/declare_salary_renderer.xml:0
msgid "13th"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.swissdec_wage_types_tree
msgid "13th Hourly"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.hr_contract_view_form
msgid "13th Month"
msgstr "13de maand"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_salary_rule__l10n_ch_13th_month_included
msgid "13th Month Included"
msgstr "13de Maand inbegrepen"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_salary_rule__l10n_ch_salary_certificate__14
msgid "14. Further fringe benefits"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_contract__l10n_ch_14th_month
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.hr_contract_view_form
msgid "14th Month"
msgstr "14de Maand"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_contract__laa_solution_number__2
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_payslip__laa_solution_number__2
msgid "2 - Occupational and Non-Occupational Insured, without deductions"
msgstr "2 - Beroeps- en niet-beroepsverzekerden, zonder inhoudingen"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_salary_rule__l10n_ch_salary_certificate__2_1
msgid "2.1 Room and board"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_salary_rule__l10n_ch_salary_certificate__2_2
msgid "2.2 Personal use of the company car"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_salary_rule__l10n_ch_salary_certificate__2_3
msgid "2.3 Personal use of the company car"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_employee__l10n_ch_no_nationality__22
msgid "22 - Stateless"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_contract__laa_solution_number__3
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_payslip__laa_solution_number__3
msgid "3 - Only Occupational Insured"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_salary_rule__l10n_ch_salary_certificate__3
msgid "3. Irregular Benefits"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.res_config_settings_view_form
msgid "30 Day Method"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_res_company__l10n_ch_30_day_method
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_res_config_settings__l10n_ch_30_day_method
msgid "30-Day Calculation Method"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_declaration/declare_salary_renderer.xml:0
msgid "3rd Party Pay"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_1978
msgid "3rd pillar A paid by employer"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_1977
msgid "3rd pillar B paid by employer"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_salary_rule__l10n_ch_salary_certificate__4
msgid "4. Capital Benefits"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_salary_rule__l10n_ch_5_cents_rounding
msgid "5 cents rounding"
msgstr "5 cents Afronding"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_salary_rule__l10n_ch_salary_certificate__5
msgid "5. Ownership right in accordance with supplement"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_accident_insurance_line_rate__employer_aanp_part__50
msgid "50 %"
msgstr "50 %"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_salary_rule__l10n_ch_salary_certificate__6
msgid "6. Board of directors’ compensation"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_salary_rule__l10n_ch_salary_certificate__7
msgid "7. Other benefits"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_salary_rule__l10n_ch_salary_certificate__8
msgid "8. Gross Salary Total / Pension"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_salary_rule__l10n_ch_salary_certificate__9
msgid "9. Contributions OASI/DI/IC/UI/NBUV"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_report_payslip
msgid "<span class=\"fw-bold\">Advance Paid</span>"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_report_payslip
msgid "<span class=\"fw-bold\">Net Salary</span>"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_report_payslip
msgid "<span class=\"fw-bold\">Net to Pay</span>"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.hr_employee_view_swissdec_form
msgid "<span class=\"o_address_country\">Switzerland</span>"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_swissdec_transmitter_form
msgid ""
"<span class=\"o_stat_text\">\n"
"                                    Declarations\n"
"                                </span>"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.view_l10n_ch_swissdec_job_result_form
msgid ""
"<span class=\"o_stat_text\">\n"
"                                    Proof of Insurances\n"
"                                </span>"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.hr_employee_view_swissdec_form
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.hr_payslip_view_form_inherit
msgid ""
"<span class=\"o_stat_text\">\n"
"                            Absences\n"
"                        </span>"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.hr_contract_view_form
msgid ""
"<span class=\"o_stat_text\">\n"
"                            One-time Payments\n"
"                        </span>"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.hr_payslip_view_form_inherit
msgid ""
"<span class=\"o_stat_text\">\n"
"                            Source-Tax Correction\n"
"                        </span>\n"
"                        <span class=\"o_stat_value\">\n"
"                            1\n"
"                        </span>"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_insurance_report_view_transmission_form
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.view_l10n_ch_certificate_report_form
msgid ""
"<span class=\"o_stat_text\">\n"
"                            Wage Statements\n"
"                        </span>"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.view_l10n_ch_tax_rate_import_wizard_inherit
msgid ""
"<span invisible=\"import_mode == 'automatic'\" class=\"text-muted\">Tax "
"files (.txt format) can be found at "
"https://www.estv.admin.ch/estv/fr/accueil/impot-federal-direct/impot-a-la-"
"source/baremes-cantonaux.html</span>"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.view_l10n_ch_tax_rate_import_wizard_inherit
msgid ""
"<span invisible=\"import_mode == 'manual'\" class=\"text-muted\">Automatic "
"import might take a few minutes if you chose to import all Cantons at "
"once.</span>"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.view_l10n_ch_hr_contract_wage_form_calendar
msgid ""
"<span invisible=\"uom != 'percentage'\">%</span>\n"
"                            <span invisible=\"uom != 'hours'\">Hours</span>"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.report_individual_account
msgid "<span> - </span>"
msgstr "<span> - </span>"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.hr_contract_view_form
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.hr_employee_view_swissdec_form
msgid "<span>%</span>"
msgstr "<span>%</span>"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.hr_employee_view_swissdec_form
msgid "<span>/ 20 Days per Month</span>"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_report_payslip
msgid "<span>6500</span>"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_report_payslip
msgid "<span>6510</span>"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_report_payslip
msgid "<span>6600</span>"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_report_payslip
msgid "<span>9000</span>"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_report_payslip
msgid "<span>9001</span>"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.hr_employee_view_swissdec_form
msgid "<span>CFH / Month</span>"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.hr_contract_view_form
msgid "<span>CHF / Month</span>"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.hr_contract_view_form
msgid "<span>Days / Year</span>"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.hr_contract_view_form
msgid "<span>Hours</span>"
msgstr "<span>Uren</span>"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.report_individual_account
msgid "<span>Individual Account - </span>"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.hr_contract_view_form
msgid "<span>Lessons</span>"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_report_payslip
msgid "<span>Vacation Pay Balance</span>"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_report_payslip
msgid "<span>Vacation Pay Provision</span>"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_report_payslip
msgid "<strong>AVS Number: </strong>"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_report_payslip
msgid "<strong>Activity Rate: </strong>"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_report_payslip
msgid "<strong>Address: </strong>"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.report_company_master_data_document
msgid "<strong>Code:</strong>"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.report_company_master_data_document
msgid "<strong>Contract Number:</strong>"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.report_company_master_data_document
msgid "<strong>Customer Number:</strong>"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_report_payslip
msgid "<strong>Entry Date: </strong>"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.report_company_master_data_document
msgid "<strong>Group Name:</strong>"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.report_company_master_data_document
msgid "<strong>Group Unit:</strong>"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.report_company_master_data_document
msgid "<strong>Indemnity Fund Number:</strong>"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.report_company_master_data_document
msgid "<strong>Insurance Code (AK-Nr.):</strong>"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.report_company_master_data_document
msgid "<strong>Insurance Code:</strong>"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.report_company_master_data_document
msgid "<strong>Insurance Number:</strong>"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_report_payslip
msgid "<strong>Job Position: </strong>"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.report_company_master_data_document
msgid "<strong>LAA Insurance From:</strong>"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.report_company_master_data_document
msgid "<strong>LAA Insurance ID:</strong>"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.report_company_master_data_document
msgid "<strong>LPP Insurance From:</strong>"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.report_company_master_data_document
msgid "<strong>LPP Insurance ID:</strong>"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.report_company_master_data_document
msgid "<strong>Member Number:</strong>"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.report_company_master_data_document
msgid "<strong>Minimum Contributing Age:</strong>"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_report_payslip
msgid "<strong>Name: </strong>"
msgstr "<strong>Naam: </strong>"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.report_company_master_data_document
msgid "<strong>Name:</strong>"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.report_company_master_data_document
msgid "<strong>No LAA Reason:</strong>"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.report_company_master_data_document
msgid "<strong>No LPP Reason:</strong>"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.report_company_master_data_document
msgid "<strong>Retirement Age (Female):</strong>"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.report_company_master_data_document
msgid "<strong>Retirement Age (Male):</strong>"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_report_payslip
msgid "<strong>Source-Tax Code: </strong>"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.report_company_master_data_document
msgid "<strong>Sub-Number:</strong>"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.report_company_master_data_document
msgid "<strong>UID-BFS Number:</strong>"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.report_company_master_data_document
msgid "<strong>UID-BFS:</strong>"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_report_payslip
msgid "<strong>Withdrawal Date: </strong>"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_is_report_view_form
msgid ""
"A replacement declaration can only be sent after consultation with all the concerned Tax Authorities.\n"
"                    <br/>\n"
"                    Only the last declaration can be substituted"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_salary_rule__l10n_ch_aanp_included
msgid "AANP Included"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_rule_9020
msgid "AC Base"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_social_insurance_view_form
msgid "AC Employee Rate"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_social_insurance_view_form
msgid "AC Employer Rate"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_declaration/declare_salary_renderer.xml:0
msgid "AC Exempt:"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_rule_ac_open
msgid "AC OPEN"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_rule_9021
msgid "AC Salary"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_rule_ac_max
msgid "AC Salary Threshold"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_declaration/declare_salary_renderer.xml:0
msgid "AC Salary:"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_social_insurance_view_form
msgid "AC Threshold"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_rule_compl_ac
msgid "ACC"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_social_insurance_view_form
msgid "ACC Employee Rate"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_rule_compl_ac_comp
msgid "ACC Employer Contributions"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_social_insurance_view_form
msgid "ACC Employer Rate"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_rule_acc_salary
msgid "ACC Salary"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_rule_acc_salary_max
msgid "ACC Salary Maximum"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_social_insurance_view_form
msgid "ACC Threshold"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_tax_rate_import_wizard__canton__ag
msgid "AG"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_result/salary_result.xml:0
msgid "AHV-AVS Quittance"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_rule_7011
msgid "AHV/IV/EO Administrative Costs"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_rule_7010
msgid "AHV/IV/EO Employer contribution"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_rule_5010
msgid "AHV/IV/EO contribution"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_tax_rate_import_wizard__canton__ai
msgid "AI"
msgstr "IA"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_rule_5020
msgid "ALV Contributions"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_rule_5020_comp
msgid "ALV Employer Contributions"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_2070
msgid "ALV compensation"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_tax_rate_import_wizard__canton__ar
msgid "AR"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_rule_as_days
msgid "AS Days"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_swissdec_job_result__domain__ahv-avs
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_ema_declaration_view_form
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_insurance_report_view_transmission_form
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.report_wage_types_document
msgid "AVS"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_social_insurance_view_form
msgid "AVS / AC Rates"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model,name:l10n_ch_hr_payroll_elm_transmission.model_ch_yearly_report
msgid "AVS / LAA / LAAC / IJM Yearly Report"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_rule_9010
msgid "AVS Base"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_insurance_report_view_transmission_form
msgid "AVS Exempt Report"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_declaration/declare_salary_renderer.xml:0
msgid "AVS Exempt:"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_rule_avs_exoneration
msgid "AVS Exoneration"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_rule_avs_franchise
msgid "AVS Franchise"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model,name:l10n_ch_hr_payroll_elm_transmission.model_l10n_ch_avs_split_lines
msgid "AVS Income Split Lines"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.report_company_master_data_document
msgid "AVS Institutions"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-python
#: code:addons/l10n_ch_hr_payroll_elm_transmission/models/l10n_ch_avs.py:0
msgid "AVS Insurance code is not plausible."
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_rule_9012
msgid "AVS OPEN"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_insurance_report_view_transmission_form
msgid "AVS Report"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_declaration/declare_salary_renderer.xml:0
msgid "AVS Salaries"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_rule_9011
msgid "AVS Salary"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_declaration/declare_salary_renderer.xml:0
msgid "AVS Salary:"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.swissdec_wage_types_form
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.swissdec_wage_types_tree
msgid "AVS/AC"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_salary_rule__l10n_ch_ac_included
msgid "AVS/AC Included"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-python
#: code:addons/l10n_ch_hr_payroll_elm_transmission/models/hr_employee.py:0
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_hr_leave_employee_view_dashboard
msgid "Absences"
msgstr "Afwezigheden"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model,name:l10n_ch_hr_payroll_elm_transmission.model_l10n_ch_swissdec_transmitter
msgid "Abstract Swissdec Transmitter"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_2032
msgid "Accident - Partial Pension"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_2031
msgid "Accident - Pension"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_2030
msgid "Accident - daily allowance"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.hr_contract_view_form
msgid "Accident Insurance"
msgstr "Ongevallenverzekering"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:hr.salary.rule,note:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_rule_5040
msgid "Accident Insurance (Non Occupational Rates)"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.leave.type,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_swissdec_accident_lt
msgid "Accident leave"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_declaration/declare_salary_renderer.xml:0
msgid "Accounting Period:"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_ch_yearly_report__message_needaction
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_salary_rule__message_needaction
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_ema_declaration__message_needaction
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_is_report__message_needaction
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_lpp_basis_report__message_needaction
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_salary_certificate__message_needaction
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_statistic_report__message_needaction
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_swissdec_declaration__message_needaction
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_swissdec_job_result__message_needaction
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_swissdec_transmitter__message_needaction
msgid "Action Needed"
msgstr "Actie gevraagd"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_ch_yearly_report__actionable_warnings
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_ema_declaration__actionable_warnings
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_is_report__actionable_warnings
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_lpp_basis_report__actionable_warnings
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_salary_certificate__actionable_warnings
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_statistic_report__actionable_warnings
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_swissdec_transmitter__actionable_warnings
msgid "Actionable Warnings"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_ch_yearly_report__active
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_employee_is_line__active
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_accident_insurance__active
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_additional_accident_insurance__active
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_compensation_fund__active
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_ema_declaration__active
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_is_report__active
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_location_unit__active
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_lpp_basis_report__active
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_lpp_insurance__active
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_salary_certificate__active
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_sickness_insurance__active
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_social_insurance__active
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_source_tax_institution__active
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_statistic_report__active
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_swissdec_transmitter__active
msgid "Active"
msgstr "Actief"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_rule_activity_rate
msgid "Activity Rate"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_lpp_mutation__reason__activityrate
msgid "Activity Rate Change"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_rule_activity_rate_total
msgid "Activity Rate Total"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_absence_leave_view_form
msgid "Add a description..."
msgstr "Voeg een omschrijving toe..."

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/client_action/interoperability_client.xml:0
msgid "Addition Result"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:hr.salary.rule,note:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_rule_laac_comp_1
#: model_terms:hr.salary.rule,note:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_rule_laac_comp_2
msgid "Additional Accident Insurance (LAAC)"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_rule_laac1
msgid "Additional Accident Insurance 1"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_rule_laac_comp_1
msgid "Additional Accident Insurance 1 Employer Contributions"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_rule_laac_2
msgid "Additional Accident Insurance 2"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_rule_laac_comp_2
msgid "Additional Accident Insurance 2 Employer Contributions"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.actions.act_window,name:l10n_ch_hr_payroll_elm_transmission.action_l10n_ch_additional_accident_insurance
msgid "Additional Accident Insurances"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_additional_accident_insurance_line_view_form
msgid "Additional Accident Insurances Solutions"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_avs_splits__additional_delivery_date
msgid "Additional Delivery Date"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_hr_contract_wage__description
msgid "Additional Description"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.view_l10n_ch_salary_certificate_profile_form
msgid "Additional Information"
msgstr "Aanvullende informatie"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.hr_employee_view_swissdec_form
msgid "Additional Informations"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.hr_contract_view_form
msgid "Additional Insurances"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_res_company__l10n_ch_additional_line
msgid "Additional Line"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_salary_certificate_profile__l10n_ch_cs_additional_text
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.view_l10n_ch_salary_certificate_profile_form
msgid "Additional Text"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:hr.salary.rule,note:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_rule_compl_ac
#: model_terms:hr.salary.rule,note:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_rule_compl_ac_comp
msgid "Additional Unemployment Insurance"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.hr_payslip_view_form_inherit
msgid "Additional Wage Types"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.hr_employee_view_swissdec_form
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.report_company_master_data_document
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.view_l10n_ch_salary_certificate_profile_form
msgid "Address"
msgstr "Adres"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.report_company_master_data_document
msgid "Admin Fees"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_social_insurance_view_form
msgid "Admin Fees %"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_social_insurance_avs_line__admin_fees
msgid "Administrative Costs (%)"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_1110
msgid "Advancement"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.hr_payslip_view_form_inherit
msgid "After Departure Payment"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.res_config_settings_view_form
msgid "Agricole Company"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_employee__l10n_ch_agricole_company
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_res_company__l10n_ch_agricole_company
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_res_config_settings__l10n_ch_agricole_company
msgid "Agricultural Company"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_rule_5082
msgid "Alimony Deduction"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.view_l10n_ch_hr_payslip_monthly_wizard_form
msgid "All Contract Types"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.view_l10n_ch_hr_payslip_monthly_wizard_form
msgid "All Departments"
msgstr "Alle afdelingen"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.view_l10n_ch_hr_payslip_monthly_wizard_form
msgid "All Employees"
msgstr "Alle werknemers"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.view_l10n_ch_hr_payslip_monthly_wizard_form
msgid "All Workplaces"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_1102
msgid "Allowance for dirty work"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_declaration/declare_salary_renderer.xml:0
msgid "Allowances"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_hr_contract_wage__amount
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_swiss_wage_component__amount
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_dialog_message_field__swissdec_answer_value_type__amount
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_dialog_message_field__swissdec_value_type__amount
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_report_payslip
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.view_l10n_ch_hr_contract_wage_form_calendar
msgid "Amount"
msgstr "Bedrag"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_dialog_message_field__field_type__answer
msgid "Answer"
msgstr "Antwoord"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_payslip_is_log_line__code__isdtsalaryaperiodic
msgid "Aperiodic ST Determinant Salary"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.hr_payslip_line_view_search
msgid "Appears on Payslip"
msgstr "Verschijnt op salarisstrook"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_employee__l10n_ch_spouse_residence_canton__ar
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_employee__l10n_ch_weekly_residence_canton__ar
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_employee_is_line_correction__l10n_ch_source_tax_canton__ar
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_payslip_is_log_line__source_tax_canton__ar
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_is_mutation__qst_canton__ar
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_salary_certificate_profile__l10n_ch_cs_employee_parti_fair_market_value_canton__ar
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_salary_certificate_profile__l10n_ch_cs_expense_expatriate_ruling_approved_canton__ar
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_salary_certificate_profile__l10n_ch_cs_expense_policy_approved_canton__ar
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_source_tax_institution__canton__ar
msgid "Appenzell Rhodes-Extérieures"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_employee__l10n_ch_spouse_residence_canton__ai
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_employee__l10n_ch_weekly_residence_canton__ai
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_employee_is_line_correction__l10n_ch_source_tax_canton__ai
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_payslip_is_log_line__source_tax_canton__ai
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_is_mutation__qst_canton__ai
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_salary_certificate_profile__l10n_ch_cs_employee_parti_fair_market_value_canton__ai
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_salary_certificate_profile__l10n_ch_cs_expense_expatriate_ruling_approved_canton__ai
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_salary_certificate_profile__l10n_ch_cs_expense_policy_approved_canton__ai
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_source_tax_institution__canton__ai
msgid "Appenzell Rhodes-Intérieures"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.hr_employee_view_swissdec_form
msgid "Application Settings"
msgstr "Applicatie instellingen"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_salary_certificate_profile__l10n_ch_cs_expense_policy_approved_canton
msgid "Approval Canton"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_salary_certificate_profile__l10n_ch_cs_expense_policy_approved_date
msgid "Approval Date"
msgstr "Goedkeuringsdatum"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_salary_certificate_profile__l10n_ch_cs_expense_policy__approved
msgid "Approved"
msgstr "Goedgekeurd"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.hr_employee_view_swissdec_form
msgid "Approvers"
msgstr "Goedkeurders"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__ch_yearly_report__month__4
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_ema_declaration__month__4
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_hr_payslip_montlhy_wizard__month__4
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_lpp_basis_report__month__4
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_master_data_report__month__4
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_salary_certificate__month__4
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_statistic_report__month__4
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_swissdec_transmitter__month__4
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.report_individual_account
msgid "April"
msgstr "April"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.hr_employee_view_swissdec_form
msgid "Archived"
msgstr "Gearchiveerd"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_employee__l10n_ch_spouse_residence_canton__ag
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_employee__l10n_ch_weekly_residence_canton__ag
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_employee_is_line_correction__l10n_ch_source_tax_canton__ag
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_payslip_is_log_line__source_tax_canton__ag
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_is_mutation__qst_canton__ag
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_salary_certificate_profile__l10n_ch_cs_employee_parti_fair_market_value_canton__ag
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_salary_certificate_profile__l10n_ch_cs_expense_expatriate_ruling_approved_canton__ag
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_salary_certificate_profile__l10n_ch_cs_expense_policy_approved_canton__ag
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_source_tax_institution__canton__ag
msgid "Argovie"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_declaration/declare_salary_renderer.xml:0
msgid "Ascertained"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_result/salary_result.xml:0
msgid "Ascertained Taxable Earning"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_ch_yearly_report__message_attachment_count
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_salary_rule__message_attachment_count
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_ema_declaration__message_attachment_count
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_is_report__message_attachment_count
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_lpp_basis_report__message_attachment_count
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_salary_certificate__message_attachment_count
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_statistic_report__message_attachment_count
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_swissdec_declaration__message_attachment_count
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_swissdec_job_result__message_attachment_count
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_swissdec_transmitter__message_attachment_count
msgid "Attachment Count"
msgstr "Aantal bijlagen"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_ch_yearly_report__attachment_ids
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_ema_declaration__attachment_ids
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_is_report__attachment_ids
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_lpp_basis_report__attachment_ids
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_salary_certificate__attachment_ids
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_statistic_report__attachment_ids
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_swissdec_job_result__attachment_ids
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_swissdec_transmitter__attachment_ids
msgid "Attachments"
msgstr "Bijlagen"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_1219
msgid "Attendance bonus"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.hr_employee_view_swissdec_form
msgid "Attendance/Point of Sale"
msgstr "Aanwezigheid/kassa"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_3036
msgid "Attendant care allowance"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__ch_yearly_report__month__8
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_ema_declaration__month__8
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_hr_payslip_montlhy_wizard__month__8
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_lpp_basis_report__month__8
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_master_data_report__month__8
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_salary_certificate__month__8
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_statistic_report__month__8
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_swissdec_transmitter__month__8
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.report_individual_account
msgid "August"
msgstr "Augustus"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_1021
msgid "Authorities and commissions"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_is_mutation__auto_generated
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_lpp_mutation__auto_generated
msgid "Auto Generated"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_employee_is_line__correction_method__auto
msgid "Automatic"
msgstr "Automatisch"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_tax_rate_import_wizard__import_mode__automatic
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.view_l10n_ch_tax_rate_import_wizard_inherit
msgid "Automatic Import"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_1015
msgid "Auxiliary wages"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_ema_declaration__avs_institution_ids
msgid "Avs Institution"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_avs_split_lines__avs_split_id
msgid "Avs Split"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_avs_splits__avs_split_lines
msgid "Avs Split Lines"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_result/salary_result.xml:0
msgid "Awaiting Correction From Company"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_tax_rate_import_wizard__canton__be
msgid "BE"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_tax_rate_import_wizard__canton__bl
msgid "BL"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_tax_rate_import_wizard__canton__bs
msgid "BS"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_result/salary_result.xml:0
msgid "BU-AP-Total:"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_location_unit__bur_ree_number
msgid "BUR-REE-Number"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.report_company_master_data_document
msgid "BUR/REE #"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.ui.menu,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_menu_lpp_basis_declaration
msgid "BVG-LPP Basis Declaration"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model,name:l10n_ch_hr_payroll_elm_transmission.model_l10n_ch_lpp_basis_report
msgid "BVG-LPP Basis Yearly Declaration"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model,name:l10n_ch_hr_payroll_elm_transmission.model_l10n_ch_lpp_mutation
msgid "BVG-LPP Mutations"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_3037
msgid "Backpayment Attendant care allowance"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_3001
msgid "Backpayment Child allowance"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_3011
msgid "Backpayment Education allowance"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_3015_fcf
msgid "Backpayment Education allowance paid by FCF (Geneva)"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_3033
msgid "Backpayment Household allowance"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_swiss_wage_component__salary_base
msgid "Base"
msgstr "Basis"

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_declaration/declare_salary_renderer.xml:0
msgid "Base Salary:"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.hr_payslip_view_form_inherit
msgid "Base Wage Components"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_salary_certificate_profile__l10n_ch_cs_expense_policy__receipts
msgid "Based on Receipts"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,help:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_hr_employee_children__deduction_start
msgid "Beginning of the right to the child deduction"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_employee__l10n_ch_spouse_residence_canton__be
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_employee__l10n_ch_weekly_residence_canton__be
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_employee_is_line_correction__l10n_ch_source_tax_canton__be
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_payslip_is_log_line__source_tax_canton__be
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_is_mutation__qst_canton__be
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_salary_certificate_profile__l10n_ch_cs_employee_parti_fair_market_value_canton__be
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_salary_certificate_profile__l10n_ch_cs_expense_expatriate_ruling_approved_canton__be
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_salary_certificate_profile__l10n_ch_cs_expense_policy_approved_canton__be
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_source_tax_institution__canton__be
msgid "Berne"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_hr_employee_children__birthdate
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.hr_employee_view_swissdec_form
msgid "Birthdate"
msgstr "Geboortedatum"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.hr_employee_view_swissdec_form
msgid "Birthday"
msgstr "Verjaardag"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_employee__l10n_ch_relationship_ceo__ownerbloodrelation
msgid "Blood relative with the owner"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_1500
msgid "Board fees"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_1501
msgid "Board indemnity"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_1210
msgid "Bonus"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_1111
msgid "Bonus for drilling"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_dialog_message_field__swissdec_answer_value_type__boolean
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_dialog_message_field__swissdec_value_type__boolean
msgid "Boolean"
msgstr "Boolean"

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_result/salary_result.xml:0
msgid "Branch Identifier:"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_employee_monthly_values__bvg_lpp_annual_basis
msgid "Bvg Lpp Annual Basis"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_employee__l10n_ch_spouse_residence_canton__bl
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_employee__l10n_ch_weekly_residence_canton__bl
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_employee_is_line_correction__l10n_ch_source_tax_canton__bl
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_payslip_is_log_line__source_tax_canton__bl
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_is_mutation__qst_canton__bl
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_salary_certificate_profile__l10n_ch_cs_employee_parti_fair_market_value_canton__bl
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_salary_certificate_profile__l10n_ch_cs_expense_expatriate_ruling_approved_canton__bl
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_salary_certificate_profile__l10n_ch_cs_expense_policy_approved_canton__bl
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_source_tax_institution__canton__bl
msgid "Bâle-Campagne"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_employee__l10n_ch_spouse_residence_canton__bs
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_employee__l10n_ch_weekly_residence_canton__bs
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_employee_is_line_correction__l10n_ch_source_tax_canton__bs
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_payslip_is_log_line__source_tax_canton__bs
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_is_mutation__qst_canton__bs
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_salary_certificate_profile__l10n_ch_cs_employee_parti_fair_market_value_canton__bs
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_salary_certificate_profile__l10n_ch_cs_expense_expatriate_ruling_approved_canton__bs
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_salary_certificate_profile__l10n_ch_cs_expense_policy_approved_canton__bs
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_source_tax_institution__canton__bs
msgid "Bâle-Ville"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_swissdec_job_result__domain__fak-caf
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_ema_declaration_view_form
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_insurance_report_view_transmission_form
msgid "CAF"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_declaration/declare_salary_renderer.xml:0
msgid "CAF Entry :"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.report_company_master_data_document
msgid "CAF Institutions"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_insurance_report_view_transmission_form
msgid "CAF Report"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_declaration/declare_salary_renderer.xml:0
msgid "CAF Salaries"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_salary_rule__l10n_ch_caf_statement
msgid "CAF Statement"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_declaration/declare_salary_renderer.xml:0
msgid "CAF Withdrawal:"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_wage_type_search_view
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.swissdec_wage_types_tree
msgid "CH Code"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/dialog_messages/dialog_message.xml:0
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_declaration/declare_salary_renderer.xml:0
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_hr_contract_wage__uom__currency
msgid "CHF"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_ema_declaration__caf_institution_ids
msgid "Caf Institution"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_hr_payroll_employee_lang_view_form
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.view_l10n_ch_avs_splits_form
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.view_l10n_ch_hr_payslip_monthly_wizard_form
msgid "Cancel"
msgstr "Annuleren"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_is_mutation__qst_canton
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_location_unit__canton
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_source_tax_institution__canton
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_tax_rate_import_wizard__canton
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.hr_payslip_view_form_inherit
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.report_company_master_data_document
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.view_l10n_ch_salary_certificate_profile_form
msgid "Canton"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_result/salary_result.xml:0
msgid "Canton :"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_tax_rate_import_wizard__canton_mode
msgid "Canton Importation"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,help:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_tax_rate_import_wizard__canton
msgid ""
"Canton for which to download the official tax file (only relevant if you "
"choose 'Import one canton')."
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_declaration/declare_salary_renderer.xml:0
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_result/salary_result.xml:0
msgid "Canton:"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_declaration/declare_salary_renderer.xml:0
msgid "Capital"
msgstr "Kapitaal"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_salary_certificate_profile__l10n_ch_cs_car_policy
msgid "Car Policy"
msgstr "Autobeleid"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,help:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_swiss_wage_component__code
msgid ""
"Careful, the Code is used in many references, changing it could lead to "
"unwanted changes."
msgstr ""
"Let op, de Code wordt in veel verwijzingen gebruikt, het wijzigen ervan kan "
"leiden tot ongewenste wijzigingen."

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_rule_5110
msgid "Cash Advantage Correction"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_1955
msgid "Cash benefit"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_result/salary_result.xml:0
msgid "Category Code:"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_result/salary_result.xml:0
msgid "Category Totals"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.swissdec_wage_types_form
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.swissdec_wage_types_tree
msgid "Certificat de salaire"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.report_wage_types_document
msgid "Certificate"
msgstr "Certificaat"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_salary_certificate_profile__certificate_template_id
msgid "Certificate Template"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_salary_certificate_profile__l10n_ch_certificate_type
msgid "Certificate Type"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model,name:l10n_ch_hr_payroll_elm_transmission.model_l10n_ch_hr_payroll_employee_gender_wizard
msgid "Change Employee Gender"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model,name:l10n_ch_hr_payroll_elm_transmission.model_l10n_ch_hr_payroll_employee_gender_wizard_line
msgid "Change Employee Gender Line"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_result/salary_result.xml:0
msgid "Changes Considered up to:"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/client_action/interoperability_client.xml:0
#: model:ir.actions.client,name:l10n_ch_hr_payroll_elm_transmission.swissdec_interoperability_action
msgid "Check Interoperability"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.res_config_settings_view_form
msgid "Check this box if your company is in the agricole sector."
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_salary_certificate_profile__l10n_ch_child_allowance_indirect
msgid "Child Allowances paid by FCF"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_3000
msgid "Child allowance"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_3005_fcf
msgid "Child allowance back payment paid by FCF (Geneva)"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_3004_fcf
msgid "Child allowance paid by FCF (Geneva)"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_3034
msgid "Childbirth allowance"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_3038
msgid "Childbirth allowance paid by FCF (Geneva)"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_employee_is_line_correction__children
msgid "Children Deduction"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.hr_employee_view_swissdec_form
msgid "Children Information"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_1202
msgid "Christmas Gratification"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_employee_is_line_correction__l10n_ch_church_tax
msgid "Church Tax"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.hr_employee_view_swissdec_form
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.view_l10n_ch_salary_certificate_profile_form
msgid "City"
msgstr "Plaats"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_lpp_mutation__reason__civilstate
msgid "Civil Status"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.actions.act_window,help:l10n_ch_hr_payroll_elm_transmission.action_l10n_ch_salary_certificate_profile
msgid "Click to create a new salary certificate profile."
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.view_l10n_ch_tax_rate_import_wizard_inherit
msgid "Close"
msgstr "Sluiten"

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_declaration/declare_salary_renderer.xml:0
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_result/salary_result.xml:0
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_payslip_is_log_line__code
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_lpp_insurance_line__code
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_swiss_wage_component__code
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_report_payslip
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_wage_type_search_view
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.report_individual_account
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.report_wage_types_document
msgid "Code"
msgstr "Code"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,help:l10n_ch_hr_payroll_elm_transmission.field_hr_salary_rule__l10n_ch_wage_statement
msgid ""
"Code meaning:\n"
"- I: Gross base salary and regular allowances\n"
"    - Ordinary salary paid, such as monthly, hourly, piece rate, working from home, etc.\n"
"    - Regular allowances paid, such as allowances for position or for length of service,\n"
"      residence, housing, travel or cost of living allowances.\n"
"    - Tips paid subject to AVS contributions.\n"
"    - Regular payments (at each pay) of a commission, turnover contribution or other bonus\n"
"      paid regularly.\n"
"- J: Gross amount of compensation for shift work, Sunday or night work and other arduousness\n"
"     bonuses (compensation for on-call duty, dirty work, etc.).\n"
"- K: Total amount of family allowances paid by the employer in the form of child allowances,\n"
"     vocational training allowances, household allowances or care allowances.\n"
"- Y: Benefits provided by insurance or similar institutions and which have an impact on\n"
"     employee contributions\n"
"- L: Amount of AVS/AI/APG/AC/ (1st pillar) and AANP (employee’s share) contributions.\n"
"     Not included:\n"
"        - the employer’s share,\n"
"        - the Parifonds,\n"
"        - daily allowance insurance in the event of IJM illness,\n"
"        - LAAC supplementary accident insurance\n"
"        - Social contributions must in principle be transmitted in the form of negative values.\n"
"- M: Amount of ordinary contributions (employee's share) to LPP professional pension provision or\n"
"     the 2nd pillar, in accordance with legal, statutory or regulatory provisions.\n"
"     The amount indicated should not include redemption contributions.\n"
"     Regular BVG-LPP contributions must in principle be transmitted as negative values."
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,help:l10n_ch_hr_payroll_elm_transmission.field_hr_salary_rule__l10n_ch_yearly_statement
msgid ""
"Code meaning:\n"
"- O: 13th salary paid (with the 14th and following) provided that it is not in the form of a bonus\n"
"- P: Overtime pay\n"
"- Q: Sporadic Benefits, e.g.\n"
"        - bonuses,\n"
"        - merit-based rewards,\n"
"        - participation in profit or turnover,\n"
"        - engagement bonuses and severance pay,\n"
"        - loyalty bonuses, bonuses and gifts for length of service,\n"
"        - fixed moving compensation,\n"
"        - Christmas bonuses,\n"
"        - compensation for members of the board of directors (attendance fees, fees, etc.).\n"
"- R: Fringe Benefits\n"
"        - board and lodging (section 2.1 of the salary certificate);\n"
"      - the private share of the company car (section 2.2 of the salary certificate);\n"
"      - other ancillary salary benefits (section 2.3 of the salary certificate);\n"
"      - participation rights (section 5 of the salary certificate).\n"
"- S: Capital Payment: Capital benefits of a pension nature paid by the employer directly to the\n"
"     employee and likely to be taxed at a reduced rate.\n"
"      - severance pay of a provident nature;\n"
"      - capital benefits of a pension nature;\n"
"      - salary payments after death.\n"
"- T: Other Benefits: All other services covered on an optional basis by the employer, although\n"
"     they are generally due by the employee.\n"
"        - partial or total coverage of contributions owed by the employee to LPP professional\n"
"          insurance, including executive insurance;\n"
"        - payments to occupational pension institutions (2nd pillar) made by the employer in\n"
"          favor of the employee (purchase contributions);\n"
"        - payment of insurance contributions in favor of the employee and members of his family\n"
"          (health insurance, optional 3rd pillar b insurance, life insurance, etc.);\n"
"        - payment of contributions to recognized forms of linked individual pension provision (3rd pillar a)."
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/components/swissdec_notification.xml:0
msgid "Code:"
msgstr "Code:"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__res_company__l10n_ch_statistics_convention__cla-association
msgid "Collective agreement of an association"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__res_company__l10n_ch_statistics_convention__cla-businessorgovernment
msgid "Collective labor agreement of a company or a public administration"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_1218
msgid "Commission"
msgstr "Commissie"

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_result/salary_result.xml:0
msgid "Commission:"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_1072
msgid "Commitment allowance"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_1130
msgid "Commitment bonus"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model,name:l10n_ch_hr_payroll_elm_transmission.model_res_company
msgid "Companies"
msgstr "Bedrijven"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_ch_yearly_report__company_id
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_accident_insurance__company_id
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_additional_accident_insurance__company_id
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_compensation_fund__company_id
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_ema_declaration__company_id
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_hr_payslip_montlhy_wizard__company_id
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_is_report__company_id
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_lpp_basis_report__company_id
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_lpp_insurance__company_id
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_master_data_report__company_id
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_salary_certificate__company_id
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_salary_certificate_profile__company_id
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_sickness_insurance__company_id
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_social_insurance__company_id
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_source_tax_institution__company_id
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_statistic_report__company_id
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_swissdec_transmitter__company_id
#: model:ir.ui.menu,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_menu_swissdec_company_data
msgid "Company"
msgstr "Bedrijf"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule.category,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_company_contributions
msgid "Company Contributions"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_compensation_fund_view_form
msgid "Company Information"
msgstr "Bedrijfsgegevens"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.actions.report,name:l10n_ch_hr_payroll_elm_transmission.action_l10n_ch_company_master_data_report
msgid "Company Master Data Report"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_source_tax_institution__company_number
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.report_company_master_data_document
msgid "Company Number"
msgstr "Ondernemingsnummer"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.report_company_master_data_document
msgid "Company Part"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_accident_insurance_group_view_form
msgid "Company Part (%)"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.view_l10n_ch_salary_certificate_profile_form
msgid "Company Policies"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.report_company_master_data_document
msgid "Company Rate"
msgstr "Bedrijfstarief"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_social_insurance_avs_line__employer_rate
msgid "Company Rate (%)"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.actions.report,name:l10n_ch_hr_payroll_elm_transmission.action_l10n_ch_company_wage_type_report
msgid "Company Wage Type Report"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.hr_contract_view_form
msgid "Compensation Fund"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_1101
msgid "Compensation for arduous work"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_1074
msgid "Compensation for inconvenience"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_rule_1161
msgid "Compensation for public holidays"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_1032
msgid "Compensation for replacement"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_1100
msgid "Compensation for site inconvenience"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_1104
msgid "Compensation for underground work"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_swissdec_declaration_form
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.view_l10n_ch_swissdec_job_result_form
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.view_l10n_ch_swissdec_job_result_tree
msgid "Completion"
msgstr "Voltooiing"

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_result/salary_result.xml:0
msgid "Completion Release Missing"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_swissdec_job_result__completion_url
msgid "Completion Url"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,help:l10n_ch_hr_payroll_elm_transmission.field_res_company__l10n_ch_30_day_method
#: model:ir.model.fields,help:l10n_ch_hr_payroll_elm_transmission.field_res_config_settings__l10n_ch_30_day_method
msgid "Compute Salaries based on the 30 day method"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_employee__l10n_ch_concubinage
msgid "Concubinage"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model,name:l10n_ch_hr_payroll_elm_transmission.model_res_config_settings
msgid "Config Settings"
msgstr "Configuratie instellingen"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_employee_is_line_form
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_hr_payroll_employee_lang_view_form
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.view_l10n_ch_avs_splits_form
msgid "Confirm"
msgstr "Bevestigen"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_employee_is_line__state__confirmed
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_avs_splits__state__confirmed
msgid "Confirmed"
msgstr "Bevestigd"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.hr_employee_view_swissdec_form
msgid "Contact Information"
msgstr "Contactgegevens"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.res_config_settings_view_form
msgid "Contact Person"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_res_config_settings__l10n_ch_contact_person_email
msgid "Contact Person Email"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_res_config_settings__l10n_ch_contact_person_name
msgid "Contact Person Name"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_res_config_settings__l10n_ch_contact_person_phone
msgid "Contact Person Phone"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_leave__l10n_ch_continued_pay_percentage
msgid "Continued Pay %"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_hr_contract_wage__contract_id
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_lpp_mutation__contract_id
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_swiss_wage_component__contract_id
msgid "Contract"
msgstr "Contract"

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_result/salary_result.xml:0
msgid "Contract Number:"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_contract__contract_type_id
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_hr_payslip_montlhy_wizard__contract_type
msgid "Contract Type"
msgstr "Contracttype"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.ui.menu,name:l10n_ch_hr_payroll_elm_transmission.menu_hr_payroll_contracts_swissdec_root
msgid "Contracts"
msgstr "Contracten"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_contract__l10n_ch_contractual_annual_wage
msgid "Contractual Annual Wage"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.hr_contract_view_form
msgid "Contractual Rate"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.hr_contract_view_form
msgid "Contributions"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_declaration/declare_salary_renderer.xml:0
msgid "Contributory Salary:"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_result/salary_result.xml:0
msgid "Correct Code:"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_payslip_is_log_line__corrected_slip_id
msgid "Corrected Slip"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_employee_is_manual_correction_form
msgid "Correction"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_employee_is_line__correction_date
msgid "Correction Date"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_declaration/declare_salary_renderer.xml:0
msgid "Correction Details"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_employee_is_line__correction_method
msgid "Correction Method"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_result/salary_result.xml:0
msgid "Correction Month:"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_employee_is_line_correction__tax_code
msgid "Correction Source-Tax Code"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_employee_is_line__correction_type
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_payslip_is_log_line__correction_type
msgid "Correction Type"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_rule_5100
msgid "Correction benefits in kind"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_employee_is_line__correction_type__aci
msgid "Correction by the ACI"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_employee_is_line__correction_type__dpi
msgid "Correction by the DPI"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_employee_is_line_form
msgid "Corrections"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_result/salary_result.xml:0
msgid "Corrections by Month"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_1034
msgid "Cost-of-living allowance"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-python
#: code:addons/l10n_ch_hr_payroll_elm_transmission/wizard/l10n_ch_import_tax_rates_wizard.py:0
msgid "Could not download file from URL"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.hr_employee_view_swissdec_form
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.view_l10n_ch_salary_certificate_profile_form
msgid "Country"
msgstr "Land"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.view_l10n_ch_hr_payslip_monthly_wizard_form
msgid "Create"
msgstr "Aanmaken"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_insurance_report_view_transmission_form
msgid "Create EIV File"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.actions.act_window,help:l10n_ch_hr_payroll_elm_transmission.action_l10n_ch_source_tax_institution
msgid "Create your first Source Tax Institution"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_employee_is_line_correction__create_uid
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_accident_group__create_uid
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_avs_split_lines__create_uid
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_avs_splits__create_uid
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_dialog_message__create_uid
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_dialog_message_field__create_uid
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_ema_declaration__create_uid
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_employee_monthly_values__create_uid
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_employee_yearly_values__create_uid
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_hr_contract_wage__create_uid
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_hr_payroll_employee_gender_wizard__create_uid
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_hr_payroll_employee_gender_wizard_line__create_uid
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_hr_payslip_montlhy_wizard__create_uid
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_is_mutation__create_uid
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_lpp_basis_report__create_uid
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_lpp_basis_report_line__create_uid
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_lpp_insurance_line__create_uid
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_lpp_mutation__create_uid
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_master_data_report__create_uid
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_salary_certificate_profile__create_uid
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_source_tax_institution__create_uid
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_statistic_report__create_uid
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_swiss_wage_component__create_uid
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_swissdec_declaration__create_uid
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_swissdec_job_result__create_uid
msgid "Created by"
msgstr "Aangemaakt door"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_employee_is_line_correction__create_date
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_accident_group__create_date
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_avs_split_lines__create_date
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_avs_splits__create_date
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_dialog_message__create_date
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_dialog_message_field__create_date
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_ema_declaration__create_date
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_employee_monthly_values__create_date
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_employee_yearly_values__create_date
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_hr_contract_wage__create_date
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_hr_payroll_employee_gender_wizard__create_date
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_hr_payroll_employee_gender_wizard_line__create_date
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_hr_payslip_montlhy_wizard__create_date
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_is_mutation__create_date
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_lpp_basis_report__create_date
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_lpp_basis_report_line__create_date
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_lpp_insurance_line__create_date
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_lpp_mutation__create_date
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_master_data_report__create_date
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_salary_certificate_profile__create_date
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_source_tax_institution__create_date
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_statistic_report__create_date
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_swiss_wage_component__create_date
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_swissdec_declaration__create_date
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_swissdec_job_result__create_date
msgid "Created on"
msgstr "Aangemaakt op"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_dialog_message__swissdec_creation
msgid "Creation Time"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_swissdec_job_result__credential_key
msgid "Credential Key"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_swissdec_job_result__credential_password
msgid "Credential Password"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_employee__l10n_ch_cross_border_commuter
msgid "Cross Border Commuter"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_employee__l10n_ch_cross_border_start
msgid "Cross Border Commuter Start Date"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.hr_employee_view_swissdec_form
msgid "Cross Border Information"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_result/salary_result.xml:0
msgid "Crossborder Declaration Quittance"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_hr_contract_wage__currency_id
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_swiss_wage_component__currency_id
msgid "Currency"
msgstr "Valuta"

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_result/salary_result.xml:0
msgid "Current Month:"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_contract__l10n_ch_current_occupation_rate
msgid "Current Occupation rate"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_result/salary_result.xml:0
msgid "Customer Indentity:"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_result/salary_result.xml:0
msgid "Customer Number:"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__res_company__l10n_ch_transmission_language__de
msgid "DE - German"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_2025
msgid "DI allowance"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_2026
msgid "DI pension"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_location_unit__dpi_number
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.report_company_master_data_document
msgid "DPI Number"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_employee__l10n_ch_residence_type__daily
msgid "Daily"
msgstr "Dagelijks"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:hr.salary.rule,note:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_rule_ijm_1
#: model_terms:hr.salary.rule,note:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_rule_ijm_2
#: model_terms:hr.salary.rule,note:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_rule_ijm_comp_1
#: model_terms:hr.salary.rule,note:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_rule_ijm_comp_2
msgid "Daily Sickness Insurance"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_rule_ijm_1
msgid "Daily Sickness Insurance 1"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_rule_ijm_comp_1
msgid "Daily Sickness Insurance 1 Employer"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_rule_ijm_2
msgid "Daily Sickness Insurance 2"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_rule_ijm_comp_2
msgid "Daily Sickness Insurance 2 Employer"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_2035
msgid "Daily sickness allowance"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_1250
msgid "Damage prevention bonus"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_dialog_message_field__swissdec_answer_value_type__date
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_dialog_message_field__swissdec_value_type__date
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_absence_leave_view_form
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.view_l10n_ch_salary_certificate_profile_form
msgid "Date"
msgstr "Datum"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_avs_split_lines__date_from
msgid "Date From"
msgstr "Datum vanaf"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_avs_split_lines__date_to
msgid "Date To"
msgstr "Datum t/m"

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_result/salary_result.xml:0
msgid "Date of Birth:"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,help:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_avs_splits__additional_delivery_date
msgid "Date of manual announcement when a split is not possible"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_dialog_message_field__swissdec_answer_value_type__datetime
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_dialog_message_field__swissdec_value_type__datetime
msgid "DateTime"
msgstr "DatumTijd"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_absence_leave_view_form
msgid "Dates"
msgstr "Datums"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__ch_yearly_report__month__12
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_ema_declaration__month__12
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_hr_payslip_montlhy_wizard__month__12
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_lpp_basis_report__month__12
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_master_data_report__month__12
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_salary_certificate__month__12
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_statistic_report__month__12
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_swissdec_transmitter__month__12
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.report_individual_account
msgid "December"
msgstr "December"

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-python
#: code:addons/l10n_ch_hr_payroll_elm_transmission/models/l10n_ch_swissdec_declaration_result.py:0
#: code:addons/l10n_ch_hr_payroll_elm_transmission/models/l10n_ch_transmission_mixin.py:0
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_swissdec_job_result__declaration_id
msgid "Declaration"
msgstr "Aangifte"

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-python
#: code:addons/l10n_ch_hr_payroll_elm_transmission/models/l10n_ch_transmission_mixin.py:0
msgid "Declaration %(month)s/%(year)s"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_swissdec_declaration__swissdec_declaration_id
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_swissdec_job_result__swissdec_declaration_id
msgid "Declaration ID"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_swissdec_declaration__res_id
msgid "Declaration Model Id"
msgstr "Id model aangifte"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_swissdec_declaration__res_model
msgid "Declaration Model Name"
msgstr "Modelnaam aangifte"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_swissdec_job_result__general_state
msgid "Declaration Status"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_ch_yearly_report__substituted_declaration_id
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_ema_declaration__substituted_declaration_id
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_is_report__substituted_declaration_id
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_lpp_basis_report__substituted_declaration_id
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_salary_certificate__substituted_declaration_id
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_statistic_report__substituted_declaration_id
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_swissdec_transmitter__substituted_declaration_id
msgid "Declaration To Substitute"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_declaration/declare_salary_renderer.xml:0
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_result/salary_result.xml:0
msgid "Declaration:"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-python
#: code:addons/l10n_ch_hr_payroll_elm_transmission/models/l10n_ch_transmission_mixin.py:0
msgid "Declaration_%s_request.xml"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-python
#: code:addons/l10n_ch_hr_payroll_elm_transmission/models/l10n_ch_swissdec_declaration.py:0
#: code:addons/l10n_ch_hr_payroll_elm_transmission/models/l10n_ch_transmission_mixin.py:0
msgid "Declaration_%s_response.xml"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-python
#: code:addons/l10n_ch_hr_payroll_elm_transmission/models/l10n_ch_transmission_mixin.py:0
msgid "Declarations"
msgstr "Verklaringen"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_swissdec_transmitter_form
msgid "Declare Salary"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_hr_employee_children__deduction_end
msgid "Deduction End"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_rule_5080
msgid "Deduction Private portion of company car"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_hr_employee_children__deduction_start
msgid "Deduction Start"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_rule_5081
msgid "Deduction for Prosecution Office"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_report_payslip
msgid "Deductions"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_employee__l10n_ch_relationship_ceo
msgid "Degree of Relationship with the owner"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_res_config_settings__l10n_ch_delegate_city
msgid "Delegate City"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_res_config_settings__l10n_ch_delegate_country_id
msgid "Delegate Country"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_res_company__l10n_ch_swissdec_delegate_ch_uid
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_res_config_settings__l10n_ch_swissdec_delegate_ch_uid
msgid "Delegate Identification Number (IDE-OFS)"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-python
#: code:addons/l10n_ch_hr_payroll_elm_transmission/models/res_company.py:0
msgid "Delegate Identification Number (IDE-OFS) checksum is not correct"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-python
#: code:addons/l10n_ch_hr_payroll_elm_transmission/models/res_company.py:0
msgid ""
"Delegate Identification Number (IDE-OFS) does not match the right format"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_res_company__l10n_ch_swissdec_delegate_name
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_res_config_settings__l10n_ch_swissdec_delegate_name
msgid "Delegate Name"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_res_company__l10n_ch_delegate_Po_Box
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_res_config_settings__l10n_ch_delegate_Po_Box
msgid "Delegate PO. Box"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.res_config_settings_view_form
msgid "Delegate Payroll"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_res_company__l10n_ch_uses_delegate
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_res_config_settings__l10n_ch_uses_delegate
msgid "Delegate Payroll Accounting"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_res_config_settings__l10n_ch_delegate_state_id
msgid "Delegate State"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_res_config_settings__l10n_ch_delegate_street
msgid "Delegate Street"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_res_config_settings__l10n_ch_delegate_street2
msgid "Delegate Street 2"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_res_config_settings__l10n_ch_delegate_zip
msgid "Delegate ZIP"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_hr_payslip_montlhy_wizard__department_id
msgid "Department"
msgstr "Afdeling"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.hr_employee_view_swissdec_form
msgid "Departure"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,help:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_location_unit__bur_ree_number
msgid ""
"Depending on the structure of the company and the number of workplaces, "
"there are one or more REE numbers."
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_dialog_message__swissdec_Description
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_salary_certificate__name
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.hr_payslip_view_form_inherit
msgid "Description"
msgstr "Omschrijving"

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_result/salary_result.xml:0
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/status_result/status_result.xml:0
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_swissdec_declaration_form
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.view_l10n_ch_swissdec_job_result_tree
msgid "Details"
msgstr "Details"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_swissdec_declaration_form
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.view_l10n_ch_swissdec_job_result_tree
msgid "Dialog"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_dialog_message__dialog_field_ids
msgid "Dialog Field"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/dialog_messages/dialog_message.js:0
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_dialog_message_field__dialog_message_id
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_swissdec_job_result__dialog_message_ids
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.view_elm_dialog_message_form
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.view_elm_dialog_message_tree
msgid "Dialog Message"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-python
#: code:addons/l10n_ch_hr_payroll_elm_transmission/models/l10n_ch_swissdec_declaration_result.py:0
msgid "Dialog Messages"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_swissdec_job_result__dialog_response_json
msgid "Dialog Response Json"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_1503
msgid "Director fees"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_1510
msgid "Directors' profit-sharing"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_leave__l10n_ch_disability_percentage
msgid "Disability %"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_employee_is_line_correction__display_name
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_accident_group__display_name
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_avs_split_lines__display_name
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_avs_splits__display_name
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_dialog_message__display_name
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_dialog_message_field__display_name
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_ema_declaration__display_name
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_employee_monthly_values__display_name
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_employee_yearly_values__display_name
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_hr_contract_wage__display_name
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_hr_payroll_employee_gender_wizard__display_name
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_hr_payroll_employee_gender_wizard_line__display_name
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_hr_payslip_montlhy_wizard__display_name
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_is_mutation__display_name
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_lpp_basis_report__display_name
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_lpp_basis_report_line__display_name
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_lpp_insurance_line__display_name
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_lpp_mutation__display_name
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_master_data_report__display_name
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_salary_certificate_profile__display_name
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_source_tax_institution__display_name
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_statistic_report__display_name
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_swiss_wage_component__display_name
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_swissdec_declaration__display_name
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_swissdec_job_result__display_name
msgid "Display Name"
msgstr "Schermnaam"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_swissdec_job_result__domain
msgid "Domain"
msgstr "Domein"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_dialog_message_field__swissdec_answer_value_type__double
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_dialog_message_field__swissdec_value_type__double
msgid "Double"
msgstr "Dubbel"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.view_l10n_ch_swissdec_job_result_form
msgid "Download Verified Source-Tax Statement"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-python
#: code:addons/l10n_ch_hr_payroll_elm_transmission/wizard/l10n_ch_import_tax_rates_wizard.py:0
msgid "Downloaded file is not a valid ZIP or is corrupted."
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_source_tax_institution__dpi_number
msgid "Dpi Number"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_employee_is_line__state__draft
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_avs_splits__state__draft
msgid "Draft"
msgstr "Concept"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_1103
msgid "Dust allowance"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.swissdec_wage_types_form
msgid "Décompte CAF"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-python
#: code:addons/l10n_ch_hr_payroll_elm_transmission/models/l10n_ch_declaration_yearly_retrospective.py:0
msgid "EIV File Successfully Generated"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-python
#: code:addons/l10n_ch_hr_payroll_elm_transmission/models/l10n_ch_declaration_yearly_retrospective.py:0
msgid "EIV_%s.xml"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.actions.act_window,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_ema_declaration_action
msgid "EMA Declaration"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_employee__l10n_ch_spouse_residence_canton__ex
msgid "EX - Foreign"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_3010
msgid "Education allowance"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_3014_fcf
msgid "Education allowance paid by FCF (Geneva)"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_salary_certificate_profile__l10n_ch_cs_expense_policy__rz52
msgid "Effective Expenses according to Rz 52"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_hr_elm_6020
msgid "Effective expenses expatriates"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.hr_employee_view_swissdec_form
msgid "Eligible Children Deduction"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.res_config_settings_view_form
msgid "Email"
msgstr "E-mail"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model,name:l10n_ch_hr_payroll_elm_transmission.model_hr_employee
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_employee_is_line_correction__employee_id
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_avs_splits__employee_id
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_employee_monthly_values__employee_id
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_employee_yearly_values__employee_id
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_hr_contract_wage__employee_id
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_hr_payroll_employee_gender_wizard_line__employee_id
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_hr_payslip_montlhy_wizard__employee_ids
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_is_mutation__employee_id
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_lpp_basis_report_line__employee_id
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_lpp_mutation__employee_id
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_salary_certificate_profile__employee_id
msgid "Employee"
msgstr "Werknemer"

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_declaration/declare_salary_renderer.xml:0
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_result/salary_result.xml:0
msgid "Employee #:"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model,name:l10n_ch_hr_payroll_elm_transmission.model_l10n_ch_hr_employee_children
msgid "Employee Children"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model,name:l10n_ch_hr_payroll_elm_transmission.model_hr_contract
msgid "Employee Contract"
msgstr "Arbeidsovereenkomst"

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_result/salary_result.xml:0
msgid "Employee Contributions:"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.ui.menu,name:l10n_ch_hr_payroll_elm_transmission.menu_l10n_ch_employee_yearly_values
msgid "Employee History"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.hr_employee_view_swissdec_form
msgid "Employee Number"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.view_l10n_ch_salary_certificate_profile_form
msgid "Employee Participation"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_salary_certificate_profile__l10n_ch_cs_car_policy__emppart
msgid "Employee Pays at least 0.9% per month"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_social_insurance_view_form
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.report_company_master_data_document
msgid "Employee Rate"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_social_insurance_avs_line__employee_rate
msgid "Employee Rate (%)"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_is_mutation__employee_snapshot_id
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_lpp_mutation__employee_snapshot_id
msgid "Employee Snapshot"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.constraint,message:l10n_ch_hr_payroll_elm_transmission.constraint_l10n_ch_salary_certificate_profile_ch_certificate_unique
msgid "Employee can only have one wage statement running at the same time."
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_1962
msgid "Employee options"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_1974
msgid "Employee portion CM assumed by employer"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_1972
msgid "Employee portion PP assumed by employer"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_1973
msgid "Employee portion benefits purchase PP assumed by employer"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_1971
msgid "Employee portion of KTG assumed by employer"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_1961
msgid "Employee shares"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-python
#: code:addons/l10n_ch_hr_payroll_elm_transmission/models/l10n_ch_declaration_salary_certificates.py:0
#: code:addons/l10n_ch_hr_payroll_elm_transmission/models/l10n_ch_declaration_yearly_retrospective.py:0
msgid ""
"Employee with number %s was either archived or deleted. Wage statement will "
"not be sent automatically."
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-python
#: code:addons/l10n_ch_hr_payroll_elm_transmission/models/hr_employee.py:0
msgid "Employee's Birthday cannot be greater than today."
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.hr_employee_view_swissdec_form
msgid "Employee's Name"
msgstr "Naam werknemer"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.ui.menu,name:l10n_ch_hr_payroll_elm_transmission.menu_hr_payroll_employees_swissdec_root
msgid "Employees"
msgstr "Werknemers"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.report_company_master_data_document
msgid "Employer AANP Part"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_accident_insurance_line_rate__employer_aanp_part
msgid "Employer Aanp Part"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_result/salary_result.xml:0
msgid "Employer Contributions:"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_social_insurance_view_form
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.report_company_master_data_document
msgid "Employer Rate"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.hr_contract_view_form
msgid "Employment Type"
msgstr "Soort dienstverband"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,help:l10n_ch_hr_payroll_elm_transmission.field_res_company__l10n_ch_uses_delegate
#: model:ir.model.fields,help:l10n_ch_hr_payroll_elm_transmission.field_res_config_settings__l10n_ch_uses_delegate
msgid ""
"Enable this option if you delegate payroll accounting tasks to an external "
"provider"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.hr_employee_view_swissdec_form
msgid "End Date"
msgstr "Einddatum"

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-python
#: code:addons/l10n_ch_hr_payroll_elm_transmission/models/l10n_ch_children.py:0
msgid "End of deduction period cannot be before the starting period"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model,name:l10n_ch_hr_payroll_elm_transmission.model_l10n_ch_ema_declaration
msgid "Entry / Withdrawal / Mutation Declaration for AVS / CAF / LPP"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.ui.menu,name:l10n_ch_hr_payroll_elm_transmission.menu_l10n_ch_ema_declaration
msgid "Entry / Withdrawal / Mutations"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_declaration/declare_salary_renderer.xml:0
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_result/salary_result.xml:0
msgid "Entry :"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_is_mutation__reason__entrycompany
msgid "Entry : Entry In Company"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_contract__l10n_ch_lpp_entry_reason__entrycompany
msgid "Entry In Company"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_contract__l10n_ch_lpp_entry_reason
msgid "Entry Reason"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_contract__l10n_ch_lpp_entry_valid_as_of
msgid "Entry Valid As Of"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_is_mutation__reason__entrycanton
msgid "Entry: Canton Change"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_is_mutation__reason__entryother
msgid "Entry: Other"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_result/salary_result.xml:0
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/status_result/status_result.xml:0
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_swissdec_job_result__general_state__error
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_swissdec_job_result__result_state__error
msgid "Error"
msgstr "Fout"

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/components/swissdec_notification.xml:0
msgid "Errors"
msgstr "Fouten"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_social_insurance_view_form
msgid "Exoneration Amount"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_social_insurance_view_form
msgid "Exoneration and Thresholds"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_salary_certificate_profile__l10n_ch_cs_expense_expatriate_ruling_approved
msgid "Expatriate Ruling Approved"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_1953
msgid "Expatriate benefits in kind"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_result/salary_result.xml:0
msgid "Expected Delivery Date:"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_salary_certificate_profile__l10n_ch_cs_expense_policy
msgid "Expense Policy"
msgstr "Declaratiebeleid"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_report_payslip
msgid "Expenses"
msgstr "Kosten"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_hr_elm_6000
msgid "Expenses travel, car, overnight accommodation"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_salary_rule__l10n_ch_code
msgid "External Code"
msgstr "Externe code"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_1060
msgid "Extra work"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_salary_certificate_profile__l10n_ch_cs_free_transport
msgid "F. Free Transportion"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_result/salary_result.xml:0
msgid "FAK-CAF Quittance"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_declaration/declare_salary_renderer.xml:0
msgid "FAK-CAF-ContributorySalary:"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_rule_5070
msgid "FCF Contribution"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_rule_7070
msgid "FCF Employer Contribution"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_tax_rate_import_wizard__canton__fr
msgid "FR"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__res_company__l10n_ch_transmission_language__fr
msgid "FR - French"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_swiss_wage_component__rate
msgid "Factor"
msgstr "Factor"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_salary_certificate_profile__l10n_ch_cs_employee_parti_fair_market_value
msgid "Fair Market Value"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.hr_employee_view_swissdec_form
msgid "Family"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_declaration/declare_salary_renderer.xml:0
msgid "Family Allowances:"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_declaration/declare_salary_renderer.xml:0
msgid "Family Income"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.hr_employee_view_swissdec_form
msgid "Family Status"
msgstr "Gezinsstatus"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_3030
msgid "Family allowance"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_3031
msgid "Family allowance Backpayment"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_1040
msgid "Family cost-of-living allowance"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_result/salary_result.xml:0
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/status_result/status_result.xml:0
msgid "Fault Information"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__ch_yearly_report__month__2
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_ema_declaration__month__2
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_hr_payslip_montlhy_wizard__month__2
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_lpp_basis_report__month__2
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_master_data_report__month__2
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_salary_certificate__month__2
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_statistic_report__month__2
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_swissdec_transmitter__month__2
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.report_individual_account
msgid "February"
msgstr "Februari"

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-python
#: code:addons/l10n_ch_hr_payroll_elm_transmission/models/l10n_ch_swissdec_declaration_result.py:0
msgid "Federal Office of Statistic"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_hr_employee_children__sex__f
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_hr_payroll_employee_gender_wizard_line__gender__female
msgid "Female"
msgstr "Vrouwelijk"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.report_company_master_data_document
msgid "Female Rate"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_result/salary_result.xml:0
msgid "Female Total:"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_result/salary_result.xml:0
msgid "Female Totals"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_dialog_message_field__field_type
msgid "Field Type"
msgstr "Soort veld"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_dialog_message__status__finished
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_swissdec_declaration__state__finished
msgid "Finished"
msgstr "Klaar"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_employee__l10n_ch_legal_first_name
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.hr_employee_view_swissdec_form
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_hr_employee_children_view_form
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.view_l10n_ch_salary_certificate_profile_form
msgid "First Name"
msgstr "Voornaam"

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/client_action/interoperability_client.xml:0
msgid "First Operand Is Correct"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_employee__l10n_ch_flex_profiling
msgid "Flex Profiling"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_ch_yearly_report__message_follower_ids
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_salary_rule__message_follower_ids
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_ema_declaration__message_follower_ids
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_is_report__message_follower_ids
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_lpp_basis_report__message_follower_ids
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_salary_certificate__message_follower_ids
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_statistic_report__message_follower_ids
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_swissdec_declaration__message_follower_ids
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_swissdec_job_result__message_follower_ids
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_swissdec_transmitter__message_follower_ids
msgid "Followers"
msgstr "Volgers"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_ch_yearly_report__message_partner_ids
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_salary_rule__message_partner_ids
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_ema_declaration__message_partner_ids
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_is_report__message_partner_ids
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_lpp_basis_report__message_partner_ids
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_salary_certificate__message_partner_ids
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_statistic_report__message_partner_ids
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_swissdec_declaration__message_partner_ids
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_swissdec_job_result__message_partner_ids
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_swissdec_transmitter__message_partner_ids
msgid "Followers (Partners)"
msgstr "Volgers (Partners)"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_employee__l10n_ch_foreign_tax_id
msgid "Foreign Tax-ID"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_employee__l10n_ch_relationship_ceo__ownerfosterchild
msgid "Foster Child of the owner"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_rule_1205
msgid "Fourteenth Month"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_rule_1208_hourly
msgid "Fourteenth Month (Hourly)"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_rule_14_prov
msgid "Fourteenth Month Provision"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_rule_14_prov_hourly
msgid "Fourteenth Month Provision (Hourly)"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_1902
msgid "Free accommodation"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_1900
msgid "Free meals"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_1901
msgid "Free room"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_employee__l10n_ch_spouse_residence_canton__fr
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_employee__l10n_ch_weekly_residence_canton__fr
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_employee_is_line_correction__l10n_ch_source_tax_canton__fr
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_payslip_is_log_line__source_tax_canton__fr
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_is_mutation__qst_canton__fr
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_salary_certificate_profile__l10n_ch_cs_employee_parti_fair_market_value_canton__fr
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_salary_certificate_profile__l10n_ch_cs_expense_expatriate_ruling_approved_canton__fr
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_salary_certificate_profile__l10n_ch_cs_expense_policy_approved_canton__fr
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_source_tax_institution__canton__fr
msgid "Fribourg"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_declaration/declare_salary_renderer.xml:0
msgid "Fringe"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_absence_leave_view_form
msgid "From"
msgstr "Van"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_1031
msgid "Function allowance"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_1980
msgid "Further training (salary certificate)"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_salary_certificate_profile__l10n_ch_cs_free_meals
msgid "G. Free meals or Lunch Vouchers"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_tax_rate_import_wizard__canton__ge
msgid "GE"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_tax_rate_import_wizard__canton__gl
msgid "GL"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_tax_rate_import_wizard__canton__gr
msgid "GR"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_hr_payroll_employee_gender_wizard_line__gender
msgid "Gender"
msgstr "Geslacht"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_rule_5400
msgid "General AVS Deduction in %"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_rule_7400
msgid "General AVS Deduction in % Employer"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.swissdec_wage_types_form
msgid "General Domain"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.res_config_settings_view_form
msgid "General Settings"
msgstr "Algemeen"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_swissdec_declaration__general_warnings
msgid "General Warnings"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.hr_employee_view_swissdec_form
msgid "Generate"
msgstr "Genereren"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.view_l10n_ch_master_data_report_wizard_form
msgid "Generate Data"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model,name:l10n_ch_hr_payroll_elm_transmission.model_l10n_ch_hr_payslip_montlhy_wizard
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.view_l10n_ch_hr_payslip_monthly_wizard_form
msgid "Generate Monthly Pay"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.actions.act_window,name:l10n_ch_hr_payroll_elm_transmission.action_l10n_ch_hr_payslip_monthly_wizard
msgid "Generate Monthly Payslips"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.view_l10n_ch_swissdec_job_result_form
msgid "Generate Proof Of Insurance"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_employee__l10n_ch_spouse_residence_canton__ge
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_employee__l10n_ch_weekly_residence_canton__ge
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_employee_is_line_correction__l10n_ch_source_tax_canton__ge
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_payslip_is_log_line__source_tax_canton__ge
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_is_mutation__qst_canton__ge
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_salary_certificate_profile__l10n_ch_cs_employee_parti_fair_market_value_canton__ge
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_salary_certificate_profile__l10n_ch_cs_expense_expatriate_ruling_approved_canton__ge
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_salary_certificate_profile__l10n_ch_cs_expense_policy_approved_canton__ge
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_source_tax_institution__canton__ge
msgid "Genève"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.view_l10n_ch_swissdec_job_result_form
msgid "Get Dialog"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_swissdec_declaration_form
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.view_l10n_ch_swissdec_job_result_form
msgid "Get Result"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-python
#: code:addons/l10n_ch_hr_payroll_elm_transmission/models/l10n_ch_swissdec_declaration_result.py:0
msgid "Get Result Archive %s"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_swissdec_declaration_form
msgid "Get Status"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-python
#: code:addons/l10n_ch_hr_payroll_elm_transmission/models/l10n_ch_swissdec_declaration.py:0
msgid "Get Status Archive"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-python
#: code:addons/l10n_ch_hr_payroll_elm_transmission/models/l10n_ch_swissdec_declaration.py:0
msgid "Get_Status_%s_request.xml"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_employee__l10n_ch_spouse_residence_canton__gl
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_employee__l10n_ch_weekly_residence_canton__gl
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_employee_is_line_correction__l10n_ch_source_tax_canton__gl
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_payslip_is_log_line__source_tax_canton__gl
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_is_mutation__qst_canton__gl
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_salary_certificate_profile__l10n_ch_cs_employee_parti_fair_market_value_canton__gl
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_salary_certificate_profile__l10n_ch_cs_expense_expatriate_ruling_approved_canton__gl
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_salary_certificate_profile__l10n_ch_cs_expense_policy_approved_canton__gl
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_source_tax_institution__canton__gl
msgid "Glaris"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_salary_certificate__tax_rectificate_type__global
msgid "Global Replacement"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_insurance_report_view_transmission_form
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.view_l10n_ch_certificate_report_form
msgid "Global Wage Statements"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_1204
msgid "Gratification"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_employee__l10n_ch_spouse_residence_canton__gr
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_employee__l10n_ch_weekly_residence_canton__gr
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_employee_is_line_correction__l10n_ch_source_tax_canton__gr
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_payslip_is_log_line__source_tax_canton__gr
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_is_mutation__qst_canton__gr
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_salary_certificate_profile__l10n_ch_cs_employee_parti_fair_market_value_canton__gr
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_salary_certificate_profile__l10n_ch_cs_expense_expatriate_ruling_approved_canton__gr
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_salary_certificate_profile__l10n_ch_cs_expense_policy_approved_canton__gr
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_source_tax_institution__canton__gr
msgid "Grisons"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_declaration/declare_salary_renderer.xml:0
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.report_wage_types_document
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.swissdec_wage_types_form
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.swissdec_wage_types_tree
msgid "Gross"
msgstr "Bruto"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_salary_rule__l10n_ch_gross_included
msgid "Gross Included"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_rule_5000
msgid "Gross Salary"
msgstr "Brutoloon"

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_declaration/declare_salary_renderer.xml:0
msgid "Gross Salary:"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_accident_insurance_line_rate__group_id
msgid "Group"
msgstr "Groep"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.view_l10n_ch_employee_yearly_values_search
msgid "Group By"
msgstr "Groeperen op"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_accident_group__group_unit
msgid "Group Unit"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_employee__l10n_ch_pre_defined_tax_scale__hen
msgid "HEN - Honorary Board of Directors residing abroad, without Church tax"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_employee__l10n_ch_pre_defined_tax_scale__hey
msgid "HEY - Honorary Board of Directors residing abroad, with Church tax"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.hr_employee_view_swissdec_form
msgid "HR Settings"
msgstr "Personeelsbeheer instellingen"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.res_config_settings_view_form
msgid "HR-RC Name"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_contract__l10n_ch_has_hourly
msgid "Has Hourly Wage"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_contract__l10n_ch_has_lesson
msgid "Has Lesson Wage"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_swissdec_job_result__has_lpp_contributions
msgid "Has Lpp Contributions"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_ch_yearly_report__has_message
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_salary_rule__has_message
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_ema_declaration__has_message
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_is_report__has_message
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_lpp_basis_report__has_message
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_salary_certificate__has_message
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_statistic_report__has_message
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_swissdec_declaration__has_message
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_swissdec_job_result__has_message
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_swissdec_transmitter__has_message
msgid "Has Message"
msgstr "Heeft bericht"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_contract__l10n_ch_has_monthly
msgid "Has Monthly Wage"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_swissdec_job_result__has_proof_of_insurance
msgid "Has Proof Of Insurance"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_swissdec_job_result__has_st_corrections
msgid "Has St Corrections"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_employee_monthly_values__person
msgid "Historical Field Values"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_contract__l10n_ch_contractual_holidays_rate
msgid "Holiday Compensation"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.hr_contract_view_form
msgid "Holidays"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_rule_1005
#: model:hr.work.entry.type,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_swissdec_hourly_wt
msgid "Hourly Salary"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_1306
msgid "Hourly Salary for military service/civil defense"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.work.entry.type,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_swissdec_accident_wt_hourly
msgid "Hourly Salary in case of Accident"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.work.entry.type,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_swissdec_illness_wt_hourly
msgid "Hourly Salary in case of Illness"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.work.entry.type,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_swissdec_maternity_wt_hourly
msgid "Hourly Salary in case of Maternity / Paternity Leave"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.work.entry.type,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_swissdec_military_wt_hourly
msgid "Hourly Salary in case of Military Leave"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_1307
msgid "Hourly Wages in the event of Maternity/Paternity Leave"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_1304
msgid "Hourly Wages in the event of an accident"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_1305
msgid "Hourly Wages in the event of illness"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-python
#: code:addons/l10n_ch_hr_payroll_elm_transmission/models/l10n_ch_hr_contract_wage.py:0
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_hr_contract_wage__uom__hours
msgid "Hours"
msgstr "Uren"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_3032
msgid "Household allowance"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_1050
msgid "Housing allowance"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_employee__l10n_ch_relationship_ceo__ownerhusband
msgid "Husband of the owner"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_employee_is_line_correction__id
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_accident_group__id
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_avs_split_lines__id
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_avs_splits__id
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_dialog_message__id
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_dialog_message_field__id
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_ema_declaration__id
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_employee_monthly_values__id
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_employee_yearly_values__id
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_hr_contract_wage__id
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_hr_payroll_employee_gender_wizard__id
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_hr_payroll_employee_gender_wizard_line__id
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_hr_payslip_montlhy_wizard__id
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_is_mutation__id
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_lpp_basis_report__id
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_lpp_basis_report_line__id
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_lpp_insurance_line__id
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_lpp_mutation__id
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_master_data_report__id
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_salary_certificate_profile__id
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_source_tax_institution__id
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_statistic_report__id
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_swiss_wage_component__id
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_swissdec_declaration__id
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_swissdec_job_result__id
msgid "ID"
msgstr "ID"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_swissdec_job_result__domain__ktg-amc
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_insurance_report_view_transmission_form
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.report_wage_types_document
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.swissdec_wage_types_form
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.swissdec_wage_types_tree
msgid "IJM"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.report_company_master_data_document
msgid "IJM (Sickness Insurance)"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_rule_ijm_base
msgid "IJM Base"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_declaration/declare_salary_renderer.xml:0
msgid "IJM Code:"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_salary_rule__l10n_ch_ijm_included
msgid "IJM Included"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.report_company_master_data_document
msgid "IJM Institutions"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_insurance_report_view_transmission_form
msgid "IJM Report"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_declaration/declare_salary_renderer.xml:0
msgid "IJM Salaries"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_rule_ijm_salary_1
msgid "IJM Salary Main Insurance"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_rule_ijm_salary_max_1
msgid "IJM Salary Maximum Main Insurance"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_rule_ijm_salary_max_2
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_rule_ijm_salary_min_2
msgid "IJM Salary Maximum Second Insurance"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_rule_ijm_salary_min_1
msgid "IJM Salary Minimum Main Insurance"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_rule_ijm_salary_2
msgid "IJM Salary Second Insurance"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_sickness_insurance_view_form
msgid "IJM Solution"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.report_company_master_data_document
msgid "IJM Solutions"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.swissdec_wage_types_form
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.swissdec_wage_types_tree
msgid "IS"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model,name:l10n_ch_hr_payroll_elm_transmission.model_hr_employee_is_line
msgid "IS Entry / Withdrawals / Mutations"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model,name:l10n_ch_hr_payroll_elm_transmission.model_hr_payslip_is_log_line
msgid "IS Log lines"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__res_company__l10n_ch_transmission_language__it
msgid "IT - Italian"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-python
#: code:addons/l10n_ch_hr_payroll_elm_transmission/models/l10n_ch_laa.py:0
#: code:addons/l10n_ch_hr_payroll_elm_transmission/models/l10n_ch_lpp.py:0
msgid "Identification Number (IDE-OFS) checksum is not correct"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-python
#: code:addons/l10n_ch_hr_payroll_elm_transmission/models/l10n_ch_laa.py:0
#: code:addons/l10n_ch_hr_payroll_elm_transmission/models/l10n_ch_lpp.py:0
msgid "Identification Number (IDE-OFS) does not match the right format"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,help:l10n_ch_hr_payroll_elm_transmission.field_ch_yearly_report__message_needaction
#: model:ir.model.fields,help:l10n_ch_hr_payroll_elm_transmission.field_hr_salary_rule__message_needaction
#: model:ir.model.fields,help:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_ema_declaration__message_needaction
#: model:ir.model.fields,help:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_is_report__message_needaction
#: model:ir.model.fields,help:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_lpp_basis_report__message_needaction
#: model:ir.model.fields,help:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_salary_certificate__message_needaction
#: model:ir.model.fields,help:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_statistic_report__message_needaction
#: model:ir.model.fields,help:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_swissdec_declaration__message_needaction
#: model:ir.model.fields,help:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_swissdec_job_result__message_needaction
#: model:ir.model.fields,help:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_swissdec_transmitter__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Indien aangevinkt vragen nieuwe berichten je aandacht."

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,help:l10n_ch_hr_payroll_elm_transmission.field_ch_yearly_report__message_has_error
#: model:ir.model.fields,help:l10n_ch_hr_payroll_elm_transmission.field_ch_yearly_report__message_has_sms_error
#: model:ir.model.fields,help:l10n_ch_hr_payroll_elm_transmission.field_hr_salary_rule__message_has_error
#: model:ir.model.fields,help:l10n_ch_hr_payroll_elm_transmission.field_hr_salary_rule__message_has_sms_error
#: model:ir.model.fields,help:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_ema_declaration__message_has_error
#: model:ir.model.fields,help:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_ema_declaration__message_has_sms_error
#: model:ir.model.fields,help:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_is_report__message_has_error
#: model:ir.model.fields,help:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_is_report__message_has_sms_error
#: model:ir.model.fields,help:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_lpp_basis_report__message_has_error
#: model:ir.model.fields,help:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_lpp_basis_report__message_has_sms_error
#: model:ir.model.fields,help:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_salary_certificate__message_has_error
#: model:ir.model.fields,help:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_salary_certificate__message_has_sms_error
#: model:ir.model.fields,help:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_statistic_report__message_has_error
#: model:ir.model.fields,help:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_statistic_report__message_has_sms_error
#: model:ir.model.fields,help:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_swissdec_declaration__message_has_error
#: model:ir.model.fields,help:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_swissdec_declaration__message_has_sms_error
#: model:ir.model.fields,help:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_swissdec_job_result__message_has_error
#: model:ir.model.fields,help:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_swissdec_job_result__message_has_sms_error
#: model:ir.model.fields,help:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_swissdec_transmitter__message_has_error
#: model:ir.model.fields,help:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_swissdec_transmitter__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Indien aangevinkt hebben sommige berichten een leveringsfout."

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_social_insurance_view_form
msgid "If no LAA, specify reason"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_social_insurance_view_form
msgid "If no LPP, specify reason"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,help:l10n_ch_hr_payroll_elm_transmission.field_hr_contract__l10n_ch_contractual_vacation_pay
msgid ""
"If unselected, vacation pay should be paid manually the moment the employee "
"takes his vacation."
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,help:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_social_insurance__no_laa_reason
msgid ""
"If your company doesn't have a main LAA insurance, state the reason here."
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,help:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_social_insurance__no_lpp_reason
msgid ""
"If your company doesn't have a main LPP insurance, state the reason here."
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.res_config_settings_view_form
msgid ""
"If your company transmits data to the Federal Office of Statistic, complete "
"the following information."
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.res_config_settings_view_form
msgid ""
"If your payroll accounting is delegated to a fiduciary for example, mention "
"their information here."
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_swissdec_job_result__general_state__ignored
msgid "Ignored"
msgstr "Genegeerd"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.leave.type,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_swissdec_illness_lt
msgid "Illness leave"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_leave_type__l10n_ch_swissdec_payroll_impact
msgid "Impacts Swiss Payroll"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.view_l10n_ch_swissdec_job_result_form
msgid "Import LPP Contributions"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_tax_rate_import_wizard__import_mode
msgid "Import Mode"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.view_l10n_ch_swissdec_job_result_form
msgid "Import Source-Tax Corrections"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.view_l10n_ch_tax_rate_import_wizard_inherit
msgid "Import Tax Rates"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_tax_rate_import_wizard__canton_mode__all
msgid "Import every canton"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.view_l10n_ch_tax_rate_import_wizard_inherit
msgid "Import from Tax Administration"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_tax_rate_import_wizard__canton_mode__single
msgid "Import one canton"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.report_company_master_data_document
msgid "In-House ID"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_location_unit__in_house_id
msgid "InHouseID"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_1216
msgid "Incentive for improvement proposals"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_avs_split_lines__income
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_report_payslip
msgid "Income"
msgstr "Omzet"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_avs_splits__income_to_split
msgid "Income To Split"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_ch_yearly_report__incomplete_declaration
msgid "Incomplete Declaration"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_compensation_fund_view_form
msgid "Indemnity Fund Number"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_1020
msgid "Indemnity for absence"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.report_individual_account
msgid "Individual Account -"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_salary_certificate__tax_rectificate_type__individual
msgid "Individual Replacement"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__res_company__l10n_ch_statistics_convention__individualcontract
msgid "Individual employment contract"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/components/swissdec_notification.xml:0
msgid "Information"
msgstr "Informatie"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.hr_payslip_view_form_inherit
msgid "Input Data"
msgstr "Invoergegevens"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_lpp_insurance_line__institution_id
msgid "Institution"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-python
#: code:addons/l10n_ch_hr_payroll_elm_transmission/models/l10n_ch_swissdec_declaration_result.py:0
msgid "Institution Completion"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_result/salary_result.xml:0
msgid "Institution ID:"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_swissdec_job_result__institution_id_ref
msgid "Institution Id Ref"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_result/salary_result.xml:0
msgid "Institution Name:"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-python
#: code:addons/l10n_ch_hr_payroll_elm_transmission/models/l10n_ch_swissdec_declaration_result.py:0
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.view_l10n_ch_swissdec_job_result_form
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.view_l10n_ch_swissdec_job_result_status_form
msgid "Institution Result"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_swissdec_declaration_form
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.view_l10n_ch_swissdec_job_result_tree
msgid "Institution Results"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-python
#: code:addons/l10n_ch_hr_payroll_elm_transmission/models/l10n_ch_swissdec_declaration_result.py:0
msgid "Institution Status"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_result/salary_result.xml:0
msgid "Institution:"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_result/salary_result.xml:0
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/status_result/status_result.xml:0
msgid "InstitutionName:"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_ema_declaration_view_form
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_insurance_report_view_transmission_form
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_is_report_view_form
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_statistic_report_view_form
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_swissdec_declaration_form
msgid "Institutions"
msgstr "Instellingen"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_accident_group__insurance_id
msgid "Insurance"
msgstr "Verzekering"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_compensation_fund__insurance_code
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_additional_accident_insurance_view_form
msgid "Insurance Code"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_social_insurance_view_form
msgid "Insurance Code (AK-Nr.)"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_accident_insurance__insurance_company
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_additional_accident_insurance__insurance_company
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_compensation_fund__insurance_company
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_lpp_insurance__insurance_company
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_sickness_insurance__insurance_company
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_social_insurance__insurance_company
msgid "Insurance Company"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_employee_is_line_correction__insurance_days
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_payslip_is_log_line__code__asdays
msgid "Insurance Days"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.swissdec_wage_types_form
msgid "Insurance Domain"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_accident_insurance_view_form
msgid "Insurance Number"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.ui.menu,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_menu_swissdec_institutions_insurance
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.hr_contract_view_form
msgid "Insurances"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_accident_insurance_view_form
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_additional_accident_insurance_view_form
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_sickness_insurance_view_form
msgid "Insurer Information"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_dialog_message_field__swissdec_answer_value_type__integer
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_dialog_message_field__swissdec_value_type__integer
msgid "Integer"
msgstr "Geheel getal"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_contract__l10n_ch_interim_worker
msgid "Interim Worker"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/client_action/interoperability_client.xml:0
msgid "Interoperability Test"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_contract__l10n_ch_lpp_withdrawal_reason__interruptionofemployment
msgid "Interruption Of Work"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.leave.type,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_swissdec_interruption_of_work_lt
#: model:hr.work.entry.type,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_swissdec_interruption_wt
msgid "Interruption of Work"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-python
#: code:addons/l10n_ch_hr_payroll_elm_transmission/models/hr_employee.py:0
msgid "Invalid Italian Tax-ID pattern"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_contract__irregular_working_time
msgid "Irregular Working Time"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_employee_is_line_correction__is_correction_id
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_is_mutation__is_correction_id
msgid "Is Correction"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_employee_is_line__is_ema_ids
msgid "Is Ema"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_ch_yearly_report__message_is_follower
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_salary_rule__message_is_follower
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_ema_declaration__message_is_follower
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_is_report__message_is_follower
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_lpp_basis_report__message_is_follower
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_salary_certificate__message_is_follower
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_statistic_report__message_is_follower
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_swissdec_declaration__message_is_follower
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_swissdec_job_result__message_is_follower
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_swissdec_transmitter__message_is_follower
msgid "Is Follower"
msgstr "Is een volger"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_employee_is_line__is_log_line_ids
msgid "Is Log Line"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_employee_monthly_values__is_mutations
msgid "Is Mutations"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_tax_rate_import_wizard__canton__ju
msgid "JU"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__ch_yearly_report__month__1
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_ema_declaration__month__1
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_hr_payslip_montlhy_wizard__month__1
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_lpp_basis_report__month__1
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_master_data_report__month__1
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_salary_certificate__month__1
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_statistic_report__month__1
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_swissdec_transmitter__month__1
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.report_individual_account
msgid "January"
msgstr "Januari"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_swissdec_declaration__job_key
msgid "Job Key"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.hr_employee_view_swissdec_form
msgid "Job Position"
msgstr "Functie"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_rule_5300
msgid "Joint Commission"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_rule_7300
msgid "Joint Commission Employer"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_rule_5310
msgid "Joint National Commission"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_rule_7310
msgid "Joint National Commission Employer"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_1231
msgid "Jubilee gift"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__ch_yearly_report__month__7
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_ema_declaration__month__7
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_hr_payslip_montlhy_wizard__month__7
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_lpp_basis_report__month__7
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_master_data_report__month__7
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_salary_certificate__month__7
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_statistic_report__month__7
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_swissdec_transmitter__month__7
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.report_individual_account
msgid "July"
msgstr "Juli"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__ch_yearly_report__month__6
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_ema_declaration__month__6
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_hr_payslip_montlhy_wizard__month__6
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_lpp_basis_report__month__6
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_master_data_report__month__6
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_salary_certificate__month__6
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_statistic_report__month__6
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_swissdec_transmitter__month__6
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.report_individual_account
msgid "June"
msgstr "Juni"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_employee__l10n_ch_spouse_residence_canton__ju
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_employee__l10n_ch_weekly_residence_canton__ju
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_employee_is_line_correction__l10n_ch_source_tax_canton__ju
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_payslip_is_log_line__source_tax_canton__ju
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_is_mutation__qst_canton__ju
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_salary_certificate_profile__l10n_ch_cs_employee_parti_fair_market_value_canton__ju
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_salary_certificate_profile__l10n_ch_cs_expense_expatriate_ruling_approved_canton__ju
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_salary_certificate_profile__l10n_ch_cs_expense_policy_approved_canton__ju
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_source_tax_institution__canton__ju
msgid "Jura"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_result/salary_result.xml:0
msgid "KTG-AMC Quittance"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_employee__l10n_ch_residence_type
msgid "Kind of residence"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_salary_rule__l10n_ch_13th_month_hourly_included
msgid "L10N Ch 13Th Month Hourly Included"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_res_company__l10n_ch_avs_institution_ids
msgid "L10N Ch Avs Institution"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_res_company__l10n_ch_caf_institution_ids
msgid "L10N Ch Caf Institution"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_res_company__l10n_ch_contact_person_email
msgid "L10N Ch Contact Person Email"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_res_company__l10n_ch_contact_person_name
msgid "L10N Ch Contact Person Name"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_res_company__l10n_ch_contact_person_phone
msgid "L10N Ch Contact Person Phone"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_contract__l10n_ch_contract_wage_ids
msgid "L10N Ch Contract Wage"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_salary_certificate_profile__l10n_ch_cs_employee_parti_fair_market_value_canton
msgid "L10N Ch Cs Employee Parti Fair Market Value Canton"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_salary_certificate_profile__l10n_ch_cs_employee_parti_fair_market_value_date
msgid "L10N Ch Cs Employee Parti Fair Market Value Date"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_salary_certificate_profile__l10n_ch_cs_expense_expatriate_ruling_approved_canton
msgid "L10N Ch Cs Expense Expatriate Ruling Approved Canton"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_salary_certificate_profile__l10n_ch_cs_expense_expatriate_ruling_approved_date
msgid "L10N Ch Cs Expense Expatriate Ruling Approved Date"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_ch_yearly_report__l10n_ch_declare_salary_data
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_ema_declaration__l10n_ch_declare_salary_data
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_is_report__l10n_ch_declare_salary_data
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_lpp_basis_report__l10n_ch_declare_salary_data
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_salary_certificate__l10n_ch_declare_salary_data
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_statistic_report__l10n_ch_declare_salary_data
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_swissdec_transmitter__l10n_ch_declare_salary_data
msgid "L10N Ch Declare Salary Data"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_res_company__l10n_ch_delegate_city
msgid "L10N Ch Delegate City"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_res_company__l10n_ch_delegate_country_id
msgid "L10N Ch Delegate Country"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_res_company__l10n_ch_delegate_state_id
msgid "L10N Ch Delegate State"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_res_company__l10n_ch_delegate_street
msgid "L10N Ch Delegate Street"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_res_company__l10n_ch_delegate_street2
msgid "L10N Ch Delegate Street2"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_res_company__l10n_ch_delegate_zip
msgid "L10N Ch Delegate Zip"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_res_company__l10n_ch_ijm_institution_ids
msgid "L10N Ch Ijm Institution"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_payslip__l10n_ch_is_correction
msgid "L10N Ch Is Correction"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_employee__l10n_ch_is_mutations
msgid "L10N Ch Is Mutations"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_salary_rule__l10n_ch_is_periodic
msgid "L10N Ch Is Periodic"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_payslip__l10n_ch_laa_group
msgid "L10N Ch Laa Group"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_res_company__l10n_ch_laa_institution_ids
msgid "L10N Ch Laa Institution"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_res_company__l10n_ch_laac_institution_ids
msgid "L10N Ch Laac Institution"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_payslip__l10n_ch_location_unit_id
msgid "L10N Ch Location Unit"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_res_company__l10n_ch_lpp_institution_ids
msgid "L10N Ch Lpp Institution"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_contract__l10n_ch_lpp_mutations
msgid "L10N Ch Lpp Mutations"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_payslip__l10n_ch_monthly_snapshot
msgid "L10N Ch Monthly Snapshot"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_salary_certificate_profile__l10n_ch_provision_salary_city
msgid "L10N Ch Provision Salary City"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_salary_certificate_profile__l10n_ch_provision_salary_country
msgid "L10N Ch Provision Salary Country"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_salary_certificate_profile__l10n_ch_provision_salary_first_name
msgid "L10N Ch Provision Salary First Name"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_salary_certificate_profile__l10n_ch_provision_salary_last_name
msgid "L10N Ch Provision Salary Last Name"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_salary_certificate_profile__l10n_ch_provision_salary_street
msgid "L10N Ch Provision Salary Street"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_salary_certificate_profile__l10n_ch_provision_salary_street2
msgid "L10N Ch Provision Salary Street2"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_salary_certificate_profile__l10n_ch_provision_salary_zip
msgid "L10N Ch Provision Salary Zip"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_salary_rule__l10n_ch_salary_certificate
msgid "L10N Ch Salary Certificate"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_employee__l10n_ch_salary_certificate_profiles
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_res_company__l10n_ch_salary_certificate_profiles
msgid "L10N Ch Salary Certificate Profiles"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_employee__l10n_ch_spouse_street
msgid "L10N Ch Spouse Street"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_employee__l10n_ch_spouse_work_end_date
msgid "L10N Ch Spouse Work End Date"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_res_company__l10n_ch_st_institution_ids
msgid "L10N Ch St Institution"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_payslip__l10n_ch_swiss_wage_ids
msgid "L10N Ch Swiss Wage"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_ch_yearly_report__l10n_ch_swissdec_declaration_ids
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_ema_declaration__l10n_ch_swissdec_declaration_ids
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_is_report__l10n_ch_swissdec_declaration_ids
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_lpp_basis_report__l10n_ch_swissdec_declaration_ids
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_salary_certificate__l10n_ch_swissdec_declaration_ids
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_statistic_report__l10n_ch_swissdec_declaration_ids
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_swissdec_transmitter__l10n_ch_swissdec_declaration_ids
msgid "L10N Ch Swissdec Declaration"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_ch_yearly_report__l10n_ch_swissdec_declaration_ids_size
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_ema_declaration__l10n_ch_swissdec_declaration_ids_size
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_is_report__l10n_ch_swissdec_declaration_ids_size
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_lpp_basis_report__l10n_ch_swissdec_declaration_ids_size
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_salary_certificate__l10n_ch_swissdec_declaration_ids_size
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_statistic_report__l10n_ch_swissdec_declaration_ids_size
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_swissdec_transmitter__l10n_ch_swissdec_declaration_ids_size
msgid "L10N Ch Swissdec Declaration Ids Size"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_leave__l10n_ch_swissdec_work_interruption
msgid "L10N Ch Swissdec Work Interruption"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_res_company__l10n_ch_transmission_language
msgid "L10N Ch Transmission Language"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_payslip__l10n_ch_txb_code
msgid "L10N Ch Txb Code"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_salary_rule__l10n_ch_vacation_pay_included
msgid "L10N Ch Vacation Pay Included"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_res_company__l10n_ch_work_location_ids
msgid "L10N Ch Work Location"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_swissdec_job_result__domain__uvg-laa
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_insurance_report_view_transmission_form
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.report_wage_types_document
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.swissdec_wage_types_form
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.swissdec_wage_types_tree
msgid "LAA"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_rule_9030
msgid "LAA Base"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_contract__l10n_ch_laa_group
msgid "LAA Code"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_declaration/declare_salary_renderer.xml:0
msgid "LAA Code:"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.hr_contract_view_form
msgid "LAA Group"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model,name:l10n_ch_hr_payroll_elm_transmission.model_l10n_ch_accident_group
msgid "LAA Group category"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_accident_insurance_view_form
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.report_company_master_data_document
msgid "LAA Groups"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.report_company_master_data_document
msgid "LAA Institutions"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_social_insurance_view_form
msgid "LAA Insurance (Accident)"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_insurance_report_view_transmission_form
msgid "LAA Report"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_declaration/declare_salary_renderer.xml:0
msgid "LAA Salaries"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_rule_9031
msgid "LAA Salary"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_rule_laa_max
msgid "LAA Salary Maximum"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.hr_contract_view_form
msgid "LAA Solution"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_swissdec_job_result__domain__uvgz-laac
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.report_wage_types_document
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.swissdec_wage_types_form
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.swissdec_wage_types_tree
msgid "LAAC"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_rule_laac_base
msgid "LAAC Base"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_declaration/declare_salary_renderer.xml:0
msgid "LAAC Code:"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_salary_rule__l10n_ch_laac_included
msgid "LAAC Included"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.report_company_master_data_document
msgid "LAAC Institutions"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_insurance_report_view_transmission_form
msgid "LAAC Report"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_declaration/declare_salary_renderer.xml:0
msgid "LAAC Salaries"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_rule_laac_salary_1
msgid "LAAC Salary Main Insurance"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_rule_laac_salary_max_1
msgid "LAAC Salary Maximum Main Insurance"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_rule_laac_salary_max_2
msgid "LAAC Salary Maximum Second Insurance"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_rule_laac_salary_min_1
msgid "LAAC Salary Minimum Main Insurance"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_rule_laac_salary_min_2
msgid "LAAC Salary Minimum Second Insurance"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_rule_laac_salary_2
msgid "LAAC Salary Second Insurance"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_additional_accident_insurance_view_form
msgid "LAAC Solution"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.report_company_master_data_document
msgid "LAAC Solutions"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_swissdec_job_result__domain__bvg-lpp
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.hr_contract_view_form
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_ema_declaration_view_form
msgid "LPP"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_declaration/declare_salary_renderer.xml:0
msgid "LPP Annual Basis :"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model,name:l10n_ch_hr_payroll_elm_transmission.model_l10n_ch_lpp_basis_report_line
msgid "LPP Basis Line"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.actions.act_window,name:l10n_ch_hr_payroll_elm_transmission.action_l10n_ch_lpp_basis_report
msgid "LPP Basis Reports"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_lpp_mutation__reason__changebvg-lpp-code
msgid "LPP Code Change"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_contract__l10n_ch_lpp_solutions
msgid "LPP Codes"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_contract__lpp_company_amount
msgid "LPP Company Contributions"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_declaration/declare_salary_renderer.xml:0
msgid "LPP Contributions"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_leave__l10n_ch_lpp_interruption
msgid "LPP Contributions Interruption"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-python
#: code:addons/l10n_ch_hr_payroll_elm_transmission/models/l10n_ch_swissdec_declaration_result.py:0
msgid "LPP Contributions successfully imported in the following contract: %s"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.swissdec_wage_types_form
msgid "LPP Domain"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_contract__lpp_employee_amount
msgid "LPP Employee Contributions"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_declaration/declare_salary_renderer.xml:0
msgid "LPP Entry -"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.swissdec_wage_types_form
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.swissdec_wage_types_tree
msgid "LPP Facteur"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_salary_rule__l10n_ch_lpp_factor
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.report_wage_types_document
msgid "LPP Factor"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_salary_rule__l10n_ch_lpp_forecast
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.report_wage_types_document
msgid "LPP Forecast"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.report_company_master_data_document
msgid "LPP Institutions"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_social_insurance_view_form
msgid "LPP Insurance (Pension Fund)"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_declaration/declare_salary_renderer.xml:0
msgid "LPP Mutation -"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_salary_rule__l10n_ch_lpp_retroactive
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.report_wage_types_document
msgid "LPP Retroactive"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_declaration/declare_salary_renderer.xml:0
msgid "LPP Salaries"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_lpp_insurance_view_form
msgid "LPP Solution"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model,name:l10n_ch_hr_payroll_elm_transmission.model_l10n_ch_lpp_insurance_line
msgid "LPP Solutions"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.hr_contract_view_form
msgid "LPP Status"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_declaration/declare_salary_renderer.xml:0
msgid "LPP Withdrawal -"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_employee_yearly_values__yearly_prospective
msgid "LPP Yearly Prospective"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.swissdec_wage_types_form
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.swissdec_wage_types_tree
msgid "LPP prévisionnel"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.swissdec_wage_types_form
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.swissdec_wage_types_tree
msgid "LPP rétroactif"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_tax_rate_import_wizard__canton__lu
msgid "LU"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_accident_insurance__laa_group_ids
msgid "Laa Group"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_contract__laa_solution_number
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_payslip__laa_solution_number
msgid "Laa Solution Number"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.hr_employee_view_swissdec_form
msgid "Language"
msgstr "Taal"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,help:l10n_ch_hr_payroll_elm_transmission.field_res_config_settings__l10n_ch_transmission_language
msgid "Language in which communication with institutions is done"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_employee__l10n_ch_legal_last_name
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_hr_employee_children__last_name
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.hr_employee_view_swissdec_form
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.view_l10n_ch_salary_certificate_profile_form
msgid "Last Name"
msgstr "Achternaam"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_swissdec_job_result__last_raw_response
msgid "Last Raw Response"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_employee_is_line_correction__write_uid
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_accident_group__write_uid
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_avs_split_lines__write_uid
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_avs_splits__write_uid
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_dialog_message__write_uid
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_dialog_message_field__write_uid
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_ema_declaration__write_uid
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_employee_monthly_values__write_uid
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_employee_yearly_values__write_uid
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_hr_contract_wage__write_uid
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_hr_payroll_employee_gender_wizard__write_uid
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_hr_payroll_employee_gender_wizard_line__write_uid
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_hr_payslip_montlhy_wizard__write_uid
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_is_mutation__write_uid
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_lpp_basis_report__write_uid
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_lpp_basis_report_line__write_uid
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_lpp_insurance_line__write_uid
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_lpp_mutation__write_uid
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_master_data_report__write_uid
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_salary_certificate_profile__write_uid
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_source_tax_institution__write_uid
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_statistic_report__write_uid
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_swiss_wage_component__write_uid
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_swissdec_declaration__write_uid
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_swissdec_job_result__write_uid
msgid "Last Updated by"
msgstr "Laatst bijgewerkt door"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_employee_is_line_correction__write_date
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_accident_group__write_date
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_avs_split_lines__write_date
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_avs_splits__write_date
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_dialog_message__write_date
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_dialog_message_field__write_date
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_ema_declaration__write_date
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_employee_monthly_values__write_date
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_employee_yearly_values__write_date
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_hr_contract_wage__write_date
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_hr_payroll_employee_gender_wizard__write_date
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_hr_payroll_employee_gender_wizard_line__write_date
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_hr_payslip_montlhy_wizard__write_date
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_is_mutation__write_date
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_lpp_basis_report__write_date
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_lpp_basis_report_line__write_date
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_lpp_insurance_line__write_date
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_lpp_mutation__write_date
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_master_data_report__write_date
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_salary_certificate_profile__write_date
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_source_tax_institution__write_date
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_statistic_report__write_date
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_swiss_wage_component__write_date
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_swissdec_declaration__write_date
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_swissdec_job_result__write_date
msgid "Last Updated on"
msgstr "Laatst bijgewerkt op"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_1230
msgid "Length of service gift"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_1030
msgid "Length-of-service allowance"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_rule_1006
#: model:hr.work.entry.type,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_swissdec_lesson_wt
msgid "Lesson Salary"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_accident_group__line_ids
msgid "Line"
msgstr "Regel"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_hr_payroll_employee_gender_wizard__line_ids
msgid "Lines"
msgstr "Regels"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.hr_employee_view_swissdec_form
msgid "Location"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_salary_certificate_profile__l10n_ch_cs_employee_participation_taxable_income_locked
msgid "Locked Options"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_2000
msgid "Loss-of-earnings indemnity (APG)"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_1232
msgid "Loyalty bonus"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_lpp_basis_report__lpp_basis_line_ids
msgid "Lpp Basis Line"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_lpp_basis_report_line__lpp_calculated_basis
msgid "Lpp Calculated Basis"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_lpp_basis_report_line__lpp_declared_basis
msgid "Lpp Declared Basis"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_ema_declaration__lpp_institution_ids
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_lpp_basis_report_line__lpp_institution
msgid "Lpp Institution"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_employee_monthly_values__lpp_mutations
msgid "Lpp Mutations"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_employee__l10n_ch_spouse_residence_canton__lu
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_employee__l10n_ch_weekly_residence_canton__lu
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_employee_is_line_correction__l10n_ch_source_tax_canton__lu
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_payslip_is_log_line__source_tax_canton__lu
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_is_mutation__qst_canton__lu
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_salary_certificate_profile__l10n_ch_cs_employee_parti_fair_market_value_canton__lu
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_salary_certificate_profile__l10n_ch_cs_expense_expatriate_ruling_approved_canton__lu
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_salary_certificate_profile__l10n_ch_cs_expense_policy_approved_canton__lu
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_source_tax_institution__canton__lu
msgid "Lucerne"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_hr_elm_6050
msgid "Lump sum car expenses"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_hr_elm_6035
msgid "Lump sum professional expenses expatriates"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_1411
msgid "Lump-sum benefit (subject to AHV)"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_hr_elm_6040
msgid "Lump-sum representation expenses"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_1410
msgid "Lump-sum retirement benefit"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_employee__l10n_ch_pre_defined_tax_scale__men
msgid "MEN - Monetary Value Services residing abroad, without Church tax"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_employee__l10n_ch_pre_defined_tax_scale__mey
msgid "MEY - Monetary Value Services residing abroad, with Church tax"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_hr_employee_children__sex__m
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_hr_payroll_employee_gender_wizard_line__gender__male
msgid "Male"
msgstr "Mannelijk"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.report_company_master_data_document
msgid "Male Rate"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_result/salary_result.xml:0
msgid "Male Total:"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_result/salary_result.xml:0
msgid "Male Totals"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.actions.act_window,help:l10n_ch_hr_payroll_elm_transmission.action_l10n_ch_avs_splits
msgid "Manage the splitting of negative AVS salaries for employees."
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_employee_is_line__correction_method__manual
msgid "Manual"
msgstr "Handmatig"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_employee_is_line__manual_correction_ids
msgid "Manual Correction"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_tax_rate_import_wizard__import_mode__manual
msgid "Manual File Import"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.view_l10n_ch_tax_rate_import_wizard_inherit
msgid "Manual Import"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__ch_yearly_report__month__3
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_ema_declaration__month__3
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_hr_payslip_montlhy_wizard__month__3
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_lpp_basis_report__month__3
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_master_data_report__month__3
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_salary_certificate__month__3
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_statistic_report__month__3
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_swissdec_transmitter__month__3
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.report_individual_account
msgid "March"
msgstr "Maart"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.hr_employee_view_swissdec_form
msgid "Marriage Partner"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.ui.menu,name:l10n_ch_hr_payroll_elm_transmission.menu_l10n_ch_master_data_report
msgid "Master Data"
msgstr "Stamgegevens"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_master_data_report__master_report_pdf_file
msgid "Master Report"
msgstr "Hoofdrapport"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_master_data_report__master_report_pdf_filename
msgid "Master Report Pdf Filename"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.leave.type,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_swissdec_maternity_lt
msgid "Maternity / Paternity leave"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_2040
msgid "Maternity/Paternity allowance"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__ch_yearly_report__month__5
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_ema_declaration__month__5
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_hr_payslip_montlhy_wizard__month__5
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_lpp_basis_report__month__5
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_master_data_report__month__5
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_salary_certificate__month__5
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_statistic_report__month__5
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_swissdec_transmitter__month__5
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.report_individual_account
msgid "May"
msgstr "Mei"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_social_insurance_view_form
msgid "Member Number"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.res_config_settings_view_form
msgid ""
"Mention here the contact person responsible for Payroll communication with "
"Institutions."
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model,name:l10n_ch_hr_payroll_elm_transmission.model_ir_ui_menu
msgid "Menu"
msgstr "Menu"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.view_elm_dialog_message_form
msgid "Message"
msgstr "Bericht"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_ch_yearly_report__message_has_error
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_salary_rule__message_has_error
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_ema_declaration__message_has_error
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_is_report__message_has_error
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_lpp_basis_report__message_has_error
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_salary_certificate__message_has_error
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_statistic_report__message_has_error
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_swissdec_declaration__message_has_error
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_swissdec_job_result__message_has_error
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_swissdec_transmitter__message_has_error
msgid "Message Delivery error"
msgstr "Bericht afleverfout"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_ch_yearly_report__message_ids
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_salary_rule__message_ids
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_ema_declaration__message_ids
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_is_report__message_ids
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_lpp_basis_report__message_ids
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_salary_certificate__message_ids
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_statistic_report__message_ids
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_swissdec_declaration__message_ids
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_swissdec_job_result__message_ids
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_swissdec_transmitter__message_ids
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.view_l10n_ch_swissdec_job_result_form
msgid "Messages"
msgstr "Berichten"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_employee_monthly_values__employee_meta_data
msgid "Metadata"
msgstr "Metadata"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_2005
msgid "Military compensation benefit (CCM)"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_2020
msgid "Military insurance compensation"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_2021
msgid "Military insurance pension"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.leave.type,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_swissdec_military_lt
msgid "Military leave"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_social_insurance_view_form
msgid "Minimum Contributing Age"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-python
#: code:addons/l10n_ch_hr_payroll_elm_transmission/models/l10n_ch_employee_monthly_values.py:0
msgid "Missing"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_ch_yearly_report__month
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_ema_declaration__month
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_employee_monthly_values__month
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_hr_payslip_montlhy_wizard__month
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_is_report__month
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_lpp_basis_report__month
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_master_data_report__month
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_salary_certificate__month
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_statistic_report__month
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_swissdec_declaration__month
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_swissdec_transmitter__month
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_swissdec_transmitter_form
msgid "Month"
msgstr "Maand"

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_declaration/declare_salary_renderer.xml:0
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_result/salary_result.xml:0
msgid "Month:"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-python
#: code:addons/l10n_ch_hr_payroll_elm_transmission/wizard/l10n_ch_payslip_batch_wizard.py:0
msgid "Monthly Batch %(year)s-%(month)s"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model,name:l10n_ch_hr_payroll_elm_transmission.model_l10n_ch_hr_contract_wage
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.view_l10n_ch_hr_contract_wage_tree
msgid "Monthly Recurring Wages"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_social_insurance_view_form
msgid "Monthly Retired Employees AVS/AC Exoneration"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_rule_1000
#: model:hr.work.entry.type,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_swissdec_monthly_wt
msgid "Monthly Salary"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_rule_1001
msgid "Monthly Salary Correction"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_salary_rule__l10n_ch_wage_statement
msgid "Monthly Statistic"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model,name:l10n_ch_hr_payroll_elm_transmission.model_l10n_ch_statistic_report
msgid "Monthly Statistic Report"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_employee_monthly_values__monthly_statistics
msgid "Monthly Statistic Salaries"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.ui.menu,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_menu_monthly_transmission
msgid "Monthly Transmission"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_employee_yearly_values__monthly_value_ids
msgid "Monthly Value"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.view_l10n_ch_employee_yearly_values_form
msgid "Monthly Values"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.hr_contract_view_form
msgid "Monthly Wage Types"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.hr_payslip_view_form_inherit
msgid "Monthly Wages"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_is_mutation__qst_municipality
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.hr_payslip_view_form_inherit
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.report_company_master_data_document
msgid "Municipality"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_location_unit__municipality
msgid "Municipality ID"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_declaration/declare_salary_renderer.xml:0
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_result/salary_result.xml:0
msgid "Municipality ID:"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_is_mutation__reason__childrendeduction
msgid "Mutation : Children Deduction"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_is_mutation__reason__churchtax
msgid "Mutation : Church Tax"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_is_mutation__reason__civilstate
msgid "Mutation : Civil Status"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_is_mutation__reason__others
msgid "Mutation : Other"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_is_mutation__reason__partnerworkplacechangechabroad
msgid "Mutation : Partner Workplace CH/EX"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_is_mutation__reason__partnerwork
msgid "Mutation : Partner's Work"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_is_mutation__reason__residence
msgid "Mutation : Residence"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.hr_contract_view_form
msgid "Mutations"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_result/salary_result.xml:0
msgid "NBU-BU-ANP-AP-Total:"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_tax_rate_import_wizard__canton__ne
msgid "NE"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_employee__l10n_ch_pre_defined_tax_scale__non
msgid "NON - Not Subject to Source Tax, without Church Tax"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_employee__l10n_ch_pre_defined_tax_scale__noy
msgid "NOY - Not Subject to Source Tax, with Church Tax"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_tax_rate_import_wizard__canton__nw
msgid "NW"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_ch_yearly_report__name
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_accident_group__name
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_ema_declaration__name
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_hr_payslip_montlhy_wizard__name
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_is_report__name
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_lpp_basis_report__name
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_lpp_insurance_line__name
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_salary_certificate_profile__name
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_source_tax_institution__name
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_statistic_report__name
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_swissdec_declaration__name
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_swissdec_transmitter__name
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_report_payslip
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_wage_type_search_view
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.report_individual_account
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.report_wage_types_document
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.res_config_settings_view_form
msgid "Name"
msgstr "Naam"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_employee__l10n_ch_country_id_code
msgid "Nationality Country Code"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_result/salary_result.xml:0
msgid "Nationality:"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.actions.act_window,name:l10n_ch_hr_payroll_elm_transmission.action_l10n_ch_avs_splits
#: model:ir.model,name:l10n_ch_hr_payroll_elm_transmission.model_l10n_ch_avs_splits
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.view_l10n_ch_avs_splits_form
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.view_l10n_ch_avs_splits_tree
msgid "Negative AVS Salary Splitting"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_rule_6600
msgid "Net Paid"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_rule_6500
msgid "Net Salary"
msgstr "Nettoloon"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_4900
msgid "Net/Gross compensation"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_employee__l10n_ch_spouse_residence_canton__ne
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_employee__l10n_ch_weekly_residence_canton__ne
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_employee_is_line_correction__l10n_ch_source_tax_canton__ne
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_payslip_is_log_line__source_tax_canton__ne
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_is_mutation__qst_canton__ne
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_salary_certificate_profile__l10n_ch_cs_employee_parti_fair_market_value_canton__ne
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_salary_certificate_profile__l10n_ch_cs_expense_expatriate_ruling_approved_canton__ne
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_salary_certificate_profile__l10n_ch_cs_expense_policy_approved_canton__ne
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_source_tax_institution__canton__ne
msgid "Neuchâtel"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_declaration/declare_salary_renderer.xml:0
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_result/salary_result.xml:0
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_payslip_is_log_line__correction_type__new
msgid "New"
msgstr "Nieuw"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_absence_leave_view_form
msgid "New Absence"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.ui.menu,name:l10n_ch_hr_payroll_elm_transmission.menu_l10n_ch_hr_payslip_monthly_wizard
msgid "New Batch"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-python
#: code:addons/l10n_ch_hr_payroll_elm_transmission/models/l10n_ch_declaration_yearly_retrospective.py:0
msgid "New OASI Split"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-python
#: code:addons/l10n_ch_hr_payroll_elm_transmission/wizard/l10n_ch_payslip_batch_wizard.py:0
msgid "New Payslip"
msgstr "Nieuwe loonstrook"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_employee__l10n_ch_spouse_residence_canton__nw
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_employee__l10n_ch_weekly_residence_canton__nw
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_employee_is_line_correction__l10n_ch_source_tax_canton__nw
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_payslip_is_log_line__source_tax_canton__nw
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_is_mutation__qst_canton__nw
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_salary_certificate_profile__l10n_ch_cs_employee_parti_fair_market_value_canton__nw
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_salary_certificate_profile__l10n_ch_cs_expense_expatriate_ruling_approved_canton__nw
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_salary_certificate_profile__l10n_ch_cs_expense_policy_approved_canton__nw
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_source_tax_institution__canton__nw
msgid "Nidwald"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_1075
msgid "Night shift allowance"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_1076
msgid "Night work allowance"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_employee__l10n_ch_concubinage__noconcubinage
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_dialog_message_field__swissdec_answer_default_YesNoUnknown__no
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_dialog_message_field__swissdec_answer_value_YesNoUnknown__no
msgid "No"
msgstr "Nee"

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-python
#: code:addons/l10n_ch_hr_payroll_elm_transmission/models/l10n_ch_laa.py:0
msgid "No AAP/AANP threshold found for date %s"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-python
#: code:addons/l10n_ch_hr_payroll_elm_transmission/models/l10n_ch_avs.py:0
msgid "No AVS rates found for date %s"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-python
#: code:addons/l10n_ch_hr_payroll_elm_transmission/models/l10n_ch_caf.py:0
msgid "No CAF rates found for date %s"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_salary_certificate_profile__l10n_ch_cs_car_policy__none
msgid "No Company Car"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_social_insurance__no_laa_reason
msgid "No Laa Reason"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_social_insurance__no_lpp_reason
msgid "No Lpp Reason"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_contract__wage_type__notimeconstraint
msgid "No Time Constraint"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_result/salary_result.xml:0
msgid "No data available"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.actions.act_window,help:l10n_ch_hr_payroll_elm_transmission.action_l10n_ch_certificate
#: model_terms:ir.actions.act_window,help:l10n_ch_hr_payroll_elm_transmission.action_l10n_ch_lpp_basis_report
#: model_terms:ir.actions.act_window,help:l10n_ch_hr_payroll_elm_transmission.l10n_ch_ema_declaration_action
#: model_terms:ir.actions.act_window,help:l10n_ch_hr_payroll_elm_transmission.l10n_ch_st_declaration_action
#: model_terms:ir.actions.act_window,help:l10n_ch_hr_payroll_elm_transmission.l10n_ch_statistic_declaration_action
#: model_terms:ir.actions.act_window,help:l10n_ch_hr_payroll_elm_transmission.l10n_ch_yearly_retrospective_action
msgid "No data yet!"
msgstr "Nog geen gegevens!"

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-python
#: code:addons/l10n_ch_hr_payroll_elm_transmission/models/l10n_ch_swissdec_declaration_result.py:0
msgid ""
"No matched employees were found in the system for %s Proof of insurances"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_rule_5040
msgid "Non-Occupational Accident Insurance Contribution"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_rule_5040_comp
msgid "Non-Occupational Accident Insurance Employer Contribution"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.report_company_master_data_document
msgid "Non-Occupational Rate"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_accident_insurance_group_view_form
msgid "Non-Occupational Rate (%)"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_1131
msgid "Non-commitment indemnity"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_result/salary_result.xml:0
msgid "Not Supported"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_dialog_message__notifications
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_swissdec_declaration_form
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.view_l10n_ch_swissdec_job_result_tree
msgid "Notifications"
msgstr "Meldingen"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__ch_yearly_report__month__11
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_ema_declaration__month__11
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_hr_payslip_montlhy_wizard__month__11
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_lpp_basis_report__month__11
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_master_data_report__month__11
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_salary_certificate__month__11
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_statistic_report__month__11
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_swissdec_transmitter__month__11
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.report_individual_account
msgid "November"
msgstr "November"

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_result/salary_result.xml:0
msgid "Number Of Onwership Right Detail Tags:"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_ch_yearly_report__message_needaction_counter
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_salary_rule__message_needaction_counter
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_ema_declaration__message_needaction_counter
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_is_report__message_needaction_counter
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_lpp_basis_report__message_needaction_counter
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_salary_certificate__message_needaction_counter
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_statistic_report__message_needaction_counter
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_swissdec_declaration__message_needaction_counter
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_swissdec_job_result__message_needaction_counter
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_swissdec_transmitter__message_needaction_counter
msgid "Number of Actions"
msgstr "Aantal acties"

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_result/salary_result.xml:0
msgid "Number of Female Persons:"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_result/salary_result.xml:0
msgid "Number of Male Persons:"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_declaration/declare_salary_renderer.xml:0
msgid "Number of Pension Statements:"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_result/salary_result.xml:0
msgid "Number of TaxAnnuity Tags:"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_result/salary_result.xml:0
msgid "Number of TaxSalary Tags:"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_declaration/declare_salary_renderer.xml:0
msgid "Number of Wage Statements:"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_ch_yearly_report__message_has_error_counter
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_salary_rule__message_has_error_counter
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_ema_declaration__message_has_error_counter
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_is_report__message_has_error_counter
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_lpp_basis_report__message_has_error_counter
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_salary_certificate__message_has_error_counter
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_statistic_report__message_has_error_counter
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_swissdec_declaration__message_has_error_counter
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_swissdec_job_result__message_has_error_counter
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_swissdec_transmitter__message_has_error_counter
msgid "Number of errors"
msgstr "Aantal fouten"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.hr_contract_view_form
msgid "Number of holidays per year at 100% Occupation Rate"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,help:l10n_ch_hr_payroll_elm_transmission.field_ch_yearly_report__message_needaction_counter
#: model:ir.model.fields,help:l10n_ch_hr_payroll_elm_transmission.field_hr_salary_rule__message_needaction_counter
#: model:ir.model.fields,help:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_ema_declaration__message_needaction_counter
#: model:ir.model.fields,help:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_is_report__message_needaction_counter
#: model:ir.model.fields,help:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_lpp_basis_report__message_needaction_counter
#: model:ir.model.fields,help:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_salary_certificate__message_needaction_counter
#: model:ir.model.fields,help:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_statistic_report__message_needaction_counter
#: model:ir.model.fields,help:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_swissdec_declaration__message_needaction_counter
#: model:ir.model.fields,help:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_swissdec_job_result__message_needaction_counter
#: model:ir.model.fields,help:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_swissdec_transmitter__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Aantal berichten die actie vereisen"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,help:l10n_ch_hr_payroll_elm_transmission.field_ch_yearly_report__message_has_error_counter
#: model:ir.model.fields,help:l10n_ch_hr_payroll_elm_transmission.field_hr_salary_rule__message_has_error_counter
#: model:ir.model.fields,help:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_ema_declaration__message_has_error_counter
#: model:ir.model.fields,help:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_is_report__message_has_error_counter
#: model:ir.model.fields,help:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_lpp_basis_report__message_has_error_counter
#: model:ir.model.fields,help:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_salary_certificate__message_has_error_counter
#: model:ir.model.fields,help:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_statistic_report__message_has_error_counter
#: model:ir.model.fields,help:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_swissdec_declaration__message_has_error_counter
#: model:ir.model.fields,help:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_swissdec_job_result__message_has_error_counter
#: model:ir.model.fields,help:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_swissdec_transmitter__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Aantal berichten met leveringsfout"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.ui.menu,name:l10n_ch_hr_payroll_elm_transmission.menu_l10n_ch_avs_split
msgid "OASI Splitting"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_tax_rate_import_wizard__canton__ow
msgid "OW"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_employee__l10n_ch_spouse_residence_canton__ow
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_employee__l10n_ch_weekly_residence_canton__ow
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_employee_is_line_correction__l10n_ch_source_tax_canton__ow
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_payslip_is_log_line__source_tax_canton__ow
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_is_mutation__qst_canton__ow
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_salary_certificate_profile__l10n_ch_cs_employee_parti_fair_market_value_canton__ow
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_salary_certificate_profile__l10n_ch_cs_expense_expatriate_ruling_approved_canton__ow
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_salary_certificate_profile__l10n_ch_cs_expense_policy_approved_canton__ow
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_source_tax_institution__canton__ow
msgid "Obwald"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.hr_contract_view_form
msgid "Occupation Rate"
msgstr "Bezettingsgraad"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.hr_contract_view_form
msgid "Occupation rate is computed according to the Working place hours"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_contract__l10n_ch_other_employers_occupation_rate
msgid "Occupation rate other employers"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_rule_aap_comp
msgid "Occupational Accident Insurance Employer Contributions"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.report_company_master_data_document
msgid "Occupational Rate"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_accident_insurance_group_view_form
msgid "Occupational Rate (%)"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__ch_yearly_report__month__10
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_ema_declaration__month__10
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_hr_payslip_montlhy_wizard__month__10
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_lpp_basis_report__month__10
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_master_data_report__month__10
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_salary_certificate__month__10
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_statistic_report__month__10
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_swissdec_transmitter__month__10
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.report_individual_account
msgid "October"
msgstr "Oktober"

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_declaration/declare_salary_renderer.xml:0
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_result/salary_result.xml:0
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_payslip_is_log_line__correction_type__old
msgid "Old"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:hr.salary.rule,note:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_rule_5010
#: model_terms:hr.salary.rule,note:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_rule_7011
msgid ""
"Old Age &amp; Survivor's Insurance, Disability Insurance &amp; Loss of "
"Earnings"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_social_insurance_view_form
msgid "Old-age and Survivors' Insurance (AVS)"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_1071
msgid "On-call duty allowance"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_hr_contract_wage__type__one_time
msgid "One Time Payment"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_contract__one_time_wage_count
msgid "One Time Wage Count"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.view_l10n_ch_hr_contract_wage_form_calendar
msgid "One-Time Wage Payment"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.view_l10n_ch_hr_contract_wage_calendar
msgid "One-Time Wages Calendar"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.constraint,message:l10n_ch_hr_payroll_elm_transmission.constraint_l10n_ch_source_tax_institution_ch_qst_canton_unique
msgid "Only one Source-Tax Institution per Canton is possible."
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-python
#: code:addons/l10n_ch_hr_payroll_elm_transmission/models/l10n_ch_declaration_source_tax.py:0
msgid "Only the last Source-Tax Declaration can be substituted."
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-python
#: code:addons/l10n_ch_hr_payroll_elm_transmission/models/hr_employee.py:0
msgid "Oops, this employee has no contract yet."
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_employee__l10n_ch_tax_scale_type__categoryopen
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_payslip_is_log_line__tax_at_source_category__categoryopen
msgid "Open"
msgstr "Openen"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_employee__l10n_ch_open_tax_scale
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_employee_is_line_correction__l10n_ch_open_tax_scale
msgid "Open Tax Scale"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_swissdec_job_result__general_state__notsupported
msgid "Operation Not Supported"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_swissdec_job_result__result_state__notsupported
msgid "Operation Not supported"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_1975
msgid "Optional employer contribution AANP"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_1976
msgid "Optional employer contribution LAAC"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_salary_certificate__original_date
msgid "Original Date"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_employee_is_line_form
msgid "Original ST Code"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_declaration/declare_salary_renderer.xml:0
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_lpp_mutation__reason__others
msgid "Other"
msgstr "Overige"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_social_insurance_view_form
msgid "Other Company Insurances"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_hr_elm_6030
msgid "Other Effective Expenses"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_contract__l10n_ch_other_employers
msgid "Other Employers"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_employee__l10n_ch_other_employment
msgid "Other Employment"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_employee__l10n_ch_total_activity_type
msgid "Other Employment Details"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_salary_certificate_profile__l10n_ch_cs_other_fringe_benefits
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.view_l10n_ch_salary_certificate_profile_form
msgid "Other Fringe Benefits"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_hr_elm_6070
msgid "Other lump sum expenses"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_contract__l10n_ch_lpp_entry_reason__others
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_contract__l10n_ch_lpp_withdrawal_reason__others
msgid "Others"
msgstr "Andere"

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_declaration/declare_salary_renderer.xml:0
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_rule_1065
msgid "Overtime"
msgstr "Tijd overschreden"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.work.entry.type,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_swissdec_overtime_wt
msgid "Overtime 100%"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_rule_1061
#: model:hr.work.entry.type,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_swissdec_overtime_125_wt
msgid "Overtime 125%"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_rule_1067
msgid "Overtime after departure"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.res_config_settings_view_form
msgid "P.O. Box"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.hr_employee_view_swissdec_form
msgid "PIN Code"
msgstr "PIN code"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_employee__l10n_ch_po_box
msgid "PO. Box"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_hr_elm_5051
msgid "PP Benefits Purchase"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_hr_elm_7051
msgid "PP Benefits Purchase Employer Contributions"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_hr_elm_5112
msgid "PP Benefits Purchase Employer Contributions Compensation"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_hr_elm_5052
msgid "PP Complementary Contributions"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_hr_elm_7052
msgid "PP Complementary Employer Contributions"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_hr_elm_5050
msgid "PP Contributions"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_hr_elm_7050
msgid "PP Employer Contributions"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_hr_elm_5111
msgid "PP Employer Contributions Compensation"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_2015
msgid "Parifonds"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_2027
msgid "Partial DI pension"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_lpp_mutation__reason__partialretirement
msgid "Partial Retirement"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_2022
msgid "Partial pension military insurance (AM)"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_hr_payslip_montlhy_wizard__pay_13th
msgid "Pay 13th Month"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_contract__l10n_ch_contractual_vacation_pay
msgid "Pay Holiday Compensation each month"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_leave__l10n_ch_pay_interruption
msgid "Pay Interruption"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_hr_contract_wage__date_start
msgid "Pay Period"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model,name:l10n_ch_hr_payroll_elm_transmission.model_hr_payslip
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_swiss_wage_component__payslip_id
msgid "Pay Slip"
msgstr "Loonstrook"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_1420
msgid "Payment of salary after death"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.ui.menu,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_hr_payroll_elm_payroll_reporting
msgid "Payroll Data"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_employee_monthly_values__payroll_month_closed
msgid "Payroll Month Closed"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_lpp_insurance_view_form
msgid "Payroll Unit"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_result/salary_result.xml:0
msgid "Payroll Unit:"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_employee_is_line_correction__payslip_id
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_payslip_is_log_line__payslip_id
msgid "Payslip"
msgstr "Loonstrook"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.hr_payslip_view_form_inherit
msgid "Payslip Line"
msgstr "Loonstrookregel"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.actions.act_window,name:l10n_ch_hr_payroll_elm_transmission.action_hr_payslip_line_pivot
#: model:ir.ui.menu,name:l10n_ch_hr_payroll_elm_transmission.menu_hr_payslip_line_pivot
msgid "Payslip Lines"
msgstr "Salarisstrookregels"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.hr_payslip_line_view_pivot
msgid "Payslip Lines Pivot"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-python
#: code:addons/l10n_ch_hr_payroll_elm_transmission/wizard/l10n_ch_payslip_batch_wizard.py:0
msgid "Payslip Run"
msgstr "Loonstrookronde"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,help:l10n_ch_hr_payroll_elm_transmission.field_hr_employee_is_line_correction__payslip_id
msgid "Payslip you wish to correct"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_employee_is_line__payslips_to_correct
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_payslip_is_log_line__allowed_correction_payslips_ids
msgid "Payslips To Correct"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_employee_is_line__state__pending
msgid "Pending Correction"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_salary_certificate_profile__l10n_ch_certificate_type__taxannuity
msgid "Pension Statement"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_1214
msgid "Performance bonus"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.hr_employee_view_swissdec_form
msgid "Period"
msgstr "Periode"

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_declaration/declare_salary_renderer.xml:0
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_result/salary_result.xml:0
msgid "Period:"
msgstr "Periode:"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_payslip_is_log_line__code__isdtsalaryperiodic
msgid "Periodic ST Determinant Salary"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_contract__l10n_ch_permanent_staff_public_admin
msgid "Permanent Staff for Public Administrations"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.hr_employee_view_swissdec_form
msgid "Personal Information"
msgstr "Persoonlijke gegevens"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.hr_employee_view_swissdec_form
msgid "Personal Informations"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.res_config_settings_view_form
msgid "Phone"
msgstr "Telefoon"

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/client_action/interoperability_client.xml:0
msgid "Ping"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/client_action/interoperability_client.xml:0
msgid "Ping Endpoint"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/client_action/interoperability_client.xml:0
msgid "Ping Failed"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/client_action/interoperability_client.xml:0
msgid "Ping Successful"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.actions.act_window,name:l10n_ch_hr_payroll_elm_transmission.action_l10n_ch_hr_contract_wage
msgid "Planned Wage Types"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,help:l10n_ch_hr_payroll_elm_transmission.field_hr_contract__l10n_ch_lpp_entry_valid_as_of
msgid "Please Provide the validity date of the last LPP Entry"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,help:l10n_ch_hr_payroll_elm_transmission.field_hr_contract__l10n_ch_lpp_withdrawal_valid_as_of
msgid "Please Provide the validity date of the last LPP Withdrawal"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.view_l10n_ch_swissdec_job_result_form
msgid "Poll Result"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_employee__l10n_ch_tax_scale_type__categorypredefined
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_payslip_is_log_line__tax_at_source_category__categorypredefined
msgid "Predefined Category"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_employee__l10n_ch_pre_defined_tax_scale
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_employee_is_line_correction__l10n_ch_pre_defined_tax_scale
msgid "Predefined Tax Scale"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_swissdec_transmitter_form
msgid "Prepare Data"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_salary_certificate__previous_declaration
msgid "Previous Declaration"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_dialog_message__swissdec_Previous
msgid "Previous Message"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_1209
msgid "Previous year's bonus payment"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.hr_employee_view_swissdec_form
msgid "Print Badge"
msgstr "Badge afdrukken"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_insurance_report_view_transmission_form
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.view_l10n_ch_certificate_report_form
msgid "Print all Wage Statements in one file"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.hr_employee_view_swissdec_form
msgid "Private Address"
msgstr "Privé adres"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_1910
msgid "Private share of company car"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_dialog_message_field__swissdec_answer_value_type__problem
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_dialog_message_field__swissdec_value_type__problem
msgid "Problem"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_result/salary_result.xml:0
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_dialog_message__status__processing
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_swissdec_declaration__state__processing
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_swissdec_job_result__general_state__processing
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_swissdec_job_result__result_state__processing
msgid "Processing"
msgstr "In behandeling"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_1010
msgid "Professional Fees"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_1211
msgid "Profit sharing"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_swissdec_job_result__proof_of_insurance_count
msgid "Proof Of Insurance Count"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-python
#: code:addons/l10n_ch_hr_payroll_elm_transmission/models/l10n_ch_swissdec_declaration_result.py:0
msgid "Proof of Insurances %(year)s-%(name)s"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-python
#: code:addons/l10n_ch_hr_payroll_elm_transmission/models/l10n_ch_swissdec_declaration_result.py:0
msgid "Proof of Inusrance generated for %s employees"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-python
#: code:addons/l10n_ch_hr_payroll_elm_transmission/models/l10n_ch_swissdec_declaration_result.py:0
msgid "Proof_of_insurance_%(year)s_%(name)s"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_salary_certificate_profile__l10n_ch_provision_salary
msgid "Provision of Salary"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_contract__l10n_ch_contractual_public_holidays_rate
msgid "Public Holiday Compensation"
msgstr "Compensatie feestdag"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.hr_contract_view_form
msgid "Public Holidays"
msgstr "Feestdagen"

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/components/swissdec_notification.xml:0
msgid "Quality Level:"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_report_payslip
msgid "Rate"
msgstr "Hoeveel"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_employee_is_line_correction__rate_determinant_salary
msgid "Rate Determinant Salary"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.report_company_master_data_document
msgid "Rates"
msgstr "Wisselkoersen"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_is_mutation__reason
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_lpp_mutation__reason
msgid "Reason"
msgstr "Reden"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_1215
msgid "Recognition bonus"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.view_l10n_ch_certificate_report_form
msgid "Rectificates"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_hr_contract_wage__type__recurrent
msgid "Recurrent Payment"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_declaration/declare_salary_renderer.xml:0
msgid "Reference AVS Salary:"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_payslip_is_log_line__is_correction_id
msgid "Related Correction"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.hr_employee_view_swissdec_form
msgid "Related User"
msgstr "Gekoppelde gebruiker"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_swissdec_job_result__result_state__completionreleaseismissing
msgid "Release Missing"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_salary_certificate_profile__l10n_ch_relocation_costs
msgid "Relocation Costs Paid by the Company"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_1950
msgid "Rental housing rent reduction"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_ch_yearly_report__replacement_declaration
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_ema_declaration__replacement_declaration
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_is_report__replacement_declaration
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_lpp_basis_report__replacement_declaration
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_salary_certificate__replacement_declaration
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_statistic_report__replacement_declaration
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_swissdec_transmitter__replacement_declaration
msgid "Replacement Declaration"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_lpp_basis_report_line__report_id
msgid "Report"
msgstr "Rapport"

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_result/salary_result.xml:0
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/status_result/status_result.xml:0
msgid "RequestID:"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.hr_employee_view_swissdec_form
msgid "Residence Canton"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_lpp_mutation__reason__residence
msgid "Residence Change"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.hr_employee_view_swissdec_form
msgid "Residence Municipality"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.hr_employee_view_swissdec_form
msgid "Residence Status"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_1033
msgid "Residence allowance"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.hr_employee_view_swissdec_form
msgid "Residency"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.hr_employee_view_swissdec_form
msgid "Residency Type"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_swissdec_job_result__success_state
msgid "Response Status"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_result/salary_result.xml:0
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/status_result/status_result.xml:0
msgid "ResponseID:"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.view_l10n_ch_swissdec_job_result_form
msgid "Result"
msgstr "Resultaat"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_swissdec_job_result__result_meta_data
msgid "Result Meta Data"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_swissdec_job_result__result_response_json
msgid "Result Response Json"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_swissdec_job_result__result_state
msgid "Result Status"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_swissdec_declaration__l10n_ch_swissdec_job_result_ids
msgid "Results"
msgstr "Resultaten"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_swissdec_job_result__success_state__result
msgid "Results Available"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_contract__l10n_ch_lpp_entry_reason__interruptionofemployment
msgid "Resuming Work After an Interruption"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_contract__l10n_ch_lpp_withdrawal_reason__retirement
msgid "Retirement"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_social_insurance_view_form
msgid "Retirement Age (Female)"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_social_insurance_view_form
msgid "Retirement Age (Male)"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_result/salary_result.xml:0
msgid "Reversal Details"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_declaration/declare_salary_renderer.xml:0
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_result/salary_result.xml:0
msgid "Reversed Month:"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_salary_certificate_profile__l10n_ch_cs_employee_participation_taxable_income_reversional
msgid "Reversional to Shares"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_employee__l10n_ch_pre_defined_tax_scale__sfn
msgid "SFN - Special Agreement with France Tariff"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_tax_rate_import_wizard__canton__sg
msgid "SG"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_tax_rate_import_wizard__canton__sh
msgid "SH"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_ch_yearly_report__message_has_sms_error
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_salary_rule__message_has_sms_error
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_ema_declaration__message_has_sms_error
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_is_report__message_has_sms_error
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_lpp_basis_report__message_has_sms_error
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_salary_certificate__message_has_sms_error
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_statistic_report__message_has_sms_error
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_swissdec_declaration__message_has_sms_error
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_swissdec_job_result__message_has_sms_error
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_swissdec_transmitter__message_has_sms_error
msgid "SMS Delivery error"
msgstr "SMS fout bij versturen"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_tax_rate_import_wizard__canton__so
msgid "SO"
msgstr "SO"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.report_wage_types_document
msgid "ST"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_payslip_is_log_line__code__isdtsalary
msgid "ST Rate Determinant Salary"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_payslip_is_log_line__code__issalary
msgid "ST Salary"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.hr_payslip_view_form_inherit
msgid "ST-Code"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_hr_employee_children__l10n_ch_sv_as_number
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.hr_employee_view_swissdec_form
msgid "SV-AS Number"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_tax_rate_import_wizard__canton__sz
msgid "SZ"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_employee__l10n_ch_spouse_residence_canton__sg
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_employee__l10n_ch_weekly_residence_canton__sg
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_employee_is_line_correction__l10n_ch_source_tax_canton__sg
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_payslip_is_log_line__source_tax_canton__sg
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_is_mutation__qst_canton__sg
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_salary_certificate_profile__l10n_ch_cs_employee_parti_fair_market_value_canton__sg
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_salary_certificate_profile__l10n_ch_cs_expense_expatriate_ruling_approved_canton__sg
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_salary_certificate_profile__l10n_ch_cs_expense_policy_approved_canton__sg
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_source_tax_institution__canton__sg
msgid "Saint-Gall"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.hr_contract_view_form
msgid "Salary"
msgstr "Loon"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_rule_6510
msgid "Salary Advance Already Paid"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_salary_certificate_profile__l10n_ch_certificate_type__taxsalary
msgid "Salary Certificate"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model,name:l10n_ch_hr_payroll_elm_transmission.model_l10n_ch_salary_certificate_profile
msgid "Salary Certificate Profile"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_lpp_mutation__reason__changesalary
msgid "Salary Change"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_declaration/declare_salary_renderer.js:0
msgid "Salary Data"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-python
#: code:addons/l10n_ch_hr_payroll_elm_transmission/models/l10n_ch_transmission_mixin.py:0
msgid "Salary Declaration Archive"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model,name:l10n_ch_hr_payroll_elm_transmission.model_hr_salary_rule
msgid "Salary Rule"
msgstr "Salarisregel"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_1018
msgid "Salary by task"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_1017
msgid "Salary for cleaning"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_1302
msgid "Salary for military service/civil defense"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_1016
msgid "Salary for working from home"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.work.entry.type,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_swissdec_accident_wt
msgid "Salary in case of Accident"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.work.entry.type,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_swissdec_illness_wt
msgid "Salary in case of Illness"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.work.entry.type,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_swissdec_maternity_wt
msgid "Salary in case of Maternity / Paternity Leave"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.work.entry.type,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_swissdec_military_wt
msgid "Salary in case of Military Leave"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_1217
msgid "Sales bonus"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.view_elm_dialog_message_form
msgid "Save"
msgstr "Opslaan"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_employee__l10n_ch_spouse_residence_canton__sh
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_employee__l10n_ch_weekly_residence_canton__sh
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_employee_is_line_correction__l10n_ch_source_tax_canton__sh
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_payslip_is_log_line__source_tax_canton__sh
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_is_mutation__qst_canton__sh
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_salary_certificate_profile__l10n_ch_cs_employee_parti_fair_market_value_canton__sh
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_salary_certificate_profile__l10n_ch_cs_expense_expatriate_ruling_approved_canton__sh
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_salary_certificate_profile__l10n_ch_cs_expense_policy_approved_canton__sh
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_source_tax_institution__canton__sh
msgid "Schaffhouse"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.hr_employee_view_swissdec_form
msgid "Schedule"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_employee__l10n_ch_spouse_residence_canton__sz
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_employee__l10n_ch_weekly_residence_canton__sz
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_employee_is_line_correction__l10n_ch_source_tax_canton__sz
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_payslip_is_log_line__source_tax_canton__sz
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_is_mutation__qst_canton__sz
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_salary_certificate_profile__l10n_ch_cs_employee_parti_fair_market_value_canton__sz
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_salary_certificate_profile__l10n_ch_cs_expense_expatriate_ruling_approved_canton__sz
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_salary_certificate_profile__l10n_ch_cs_expense_policy_approved_canton__sz
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_source_tax_institution__canton__sz
msgid "Schwytz"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.view_l10n_ch_employee_yearly_values_search
msgid "Search Yearly Snapshots"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:hr.salary.rule,note:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_rule_laac_2
msgid "Second Additional Accident Insurance (LAAC)"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-python
#: code:addons/l10n_ch_hr_payroll_elm_transmission/models/res_company.py:0
msgid "Second Operand does not match the right pattern"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/client_action/interoperability_client.xml:0
msgid "Second Operand:"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_dialog_message_field__field_type__section
msgid "Section"
msgstr "Sectie"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.actions.act_window,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_hr_payroll_employee_gender_wizard_action
msgid "Select Employee Gender"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.res_config_settings_view_form
msgid "Select here Industry specific calculation methods."
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,help:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_tax_rate_import_wizard__canton_mode
msgid ""
"Select whether to import tax rates for all cantons at once, or for a single "
"canton."
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.view_l10n_ch_swissdec_job_result_form
msgid "Send Reply"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_insurance_report_view_transmission_form
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.view_l10n_ch_certificate_report_form
msgid "Send Wage Statements"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__ch_yearly_report__month__9
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_ema_declaration__month__9
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_hr_payslip_montlhy_wizard__month__9
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_lpp_basis_report__month__9
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_master_data_report__month__9
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_salary_certificate__month__9
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_statistic_report__month__9
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_swissdec_transmitter__month__9
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.report_individual_account
msgid "September"
msgstr "September"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_dialog_message_field__sequence
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_swiss_wage_component__sequence
msgid "Sequence"
msgstr "Reeks"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_employee_is_line_form
msgid "Set to Draft"
msgstr "Zet op concept"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.swissdec_wage_types_form
msgid "Settings"
msgstr "Instellingen"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_1400
msgid "Severance pay (not subject to AHV)"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_1401
msgid "Severance pay (subject to AHV)"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_hr_employee_children__sex
msgid "Sex"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_result/salary_result.xml:0
msgid "Sex:"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_2065
msgid "Short-time work hourly"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_2060
msgid "Short-time work monthly"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_employee__l10n_ch_relationship_ceo__ownersiblings
msgid "Siblings with the owner"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_hr_payroll_employee_gender_wizard__slip_ids
msgid "Slip"
msgstr "Loonstrook"

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_declaration/declare_salary_renderer.xml:0
msgid "Social Contributions"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.hr_contract_view_form
msgid "Social Insurance"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_declaration/declare_salary_renderer.xml:0
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_result/salary_result.xml:0
msgid "Social Insurance #:"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_employee__l10n_ch_spouse_residence_canton__so
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_employee__l10n_ch_weekly_residence_canton__so
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_employee_is_line_correction__l10n_ch_source_tax_canton__so
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_payslip_is_log_line__source_tax_canton__so
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_is_mutation__qst_canton__so
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_salary_certificate_profile__l10n_ch_cs_employee_parti_fair_market_value_canton__so
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_salary_certificate_profile__l10n_ch_cs_expense_expatriate_ruling_approved_canton__so
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_salary_certificate_profile__l10n_ch_cs_expense_policy_approved_canton__so
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_source_tax_institution__canton__so
msgid "Soleure"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_additional_accident_insurance_line__solution_code
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_sickness_insurance_line__solution_code
msgid "Solution Code"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_additional_accident_insurance_line__solution_number
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_sickness_insurance_line__solution_number
msgid "Solution Number"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_additional_accident_insurance_line__solution_type
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_sickness_insurance_line__solution_type
msgid "Solution Type"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_lpp_insurance__solutions_ids
msgid "Solutions"
msgstr "Oplossingen"

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-python
#: code:addons/l10n_ch_hr_payroll_elm_transmission/models/l10n_ch_swissdec_declaration_dialog.py:0
msgid "Some mandatory answers were not filled"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_swissdec_job_result__domain__taxatsource
msgid "Source Tax"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_employee_is_line_correction__source_tax_amount
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_payslip_is_log_line__code__is
msgid "Source Tax Amount"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_employee_is_line_correction__source_tax_aperiodic_determinant_salary
msgid "Source Tax Aperiodic Determinant Salary"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_rule_9065
msgid "Source Tax Base"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_employee__l10n_ch_source_tax_canton
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_payslip_is_log_line__source_tax_canton
msgid "Source Tax Canton"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_employee__l10n_ch_tax_code
msgid "Source Tax Code"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-python
#: code:addons/l10n_ch_hr_payroll_elm_transmission/models/l10n_ch_swissdec_declaration_result.py:0
msgid "Source Tax Correction (%(link)s) imported for %(employees)s"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.hr_employee_view_swissdec_form
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_employee_is_line_form
msgid "Source Tax Entry / Withdrawal / Mutations"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_salary_rule__l10n_ch_source_tax_included
msgid "Source Tax Included"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model,name:l10n_ch_hr_payroll_elm_transmission.model_l10n_ch_source_tax_institution
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_is_report__source_tax_institution_ids
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.view_l10n_ch_source_tax_institution_form
msgid "Source Tax Institution"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.actions.act_window,name:l10n_ch_hr_payroll_elm_transmission.action_l10n_ch_source_tax_institution
#: model:ir.ui.menu,name:l10n_ch_hr_payroll_elm_transmission.menu_l10n_ch_source_tax_institution
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.report_company_master_data_document
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.view_l10n_ch_source_tax_institution_tree
msgid "Source Tax Institutions"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_employee__l10n_ch_source_tax_municipality
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_payslip_is_log_line__source_tax_municipality
msgid "Source Tax Municipality"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_employee_is_line_correction__source_tax_periodic_determinant_salary
msgid "Source Tax Periodic Determinant Salary"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-python
#: code:addons/l10n_ch_hr_payroll_elm_transmission/models/l10n_ch_swissdec_declaration_result.py:0
msgid "Source Tax Reversal"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_employee_is_line_correction__source_tax_salary
msgid "Source Tax Salary"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_employee_is_line_form
msgid "Source Tax Values"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_rule_5060
#: model:ir.ui.menu,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_menu_swissdec_institutions_source_tax
msgid "Source-Tax"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-python
#: code:addons/l10n_ch_hr_payroll_elm_transmission/models/l10n_ch_swissdec_declaration_result.py:0
msgid "Source-Tax (%s)"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_rule_5060_manual
msgid "Source-Tax : Manual Correction"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_rule_5061
msgid "Source-Tax Adjustment"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_rule_5061_nk
msgid "Source-Tax Adjustment after departure"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_employee_is_line_correction__l10n_ch_source_tax_canton
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_is_report_view_form
msgid "Source-Tax Canton"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-python
#: code:addons/l10n_ch_hr_payroll_elm_transmission/models/hr_payslip.py:0
msgid "Source-Tax Correction"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.actions.act_window,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_st_declaration_action
msgid "Source-Tax Declaration"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_rule_9072
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_rule_ISSALARYDATP
msgid "Source-Tax Determinant Aperiodic Salary"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_rule_ISSALARYDTP
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_employees_9071
msgid "Source-Tax Determinant Periodic Salary"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model,name:l10n_ch_hr_payroll_elm_transmission.model_hr_employee_is_line_correction
msgid "Source-Tax Manual Correction"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_employee_is_line_correction__l10n_ch_source_tax_municipality
msgid "Source-Tax Municipality"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model,name:l10n_ch_hr_payroll_elm_transmission.model_l10n_ch_is_mutation
msgid "Source-Tax Mutations"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_rule_9075
msgid "Source-Tax Rate"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_rule_9073
msgid "Source-Tax Rate Determinant Salary"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_is_report_view_form
msgid "Source-Tax Report"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_rule_9070
msgid "Source-Tax Salary"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_salary_certificate_profile__l10n_ch_source_tax_settlement_letter
msgid "Source-Tax Settlement Letter"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_employee_is_manual_correction_form
msgid "Source-Tax Values"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_1979
msgid "Source-Tax paid by employer"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.hr_employee_view_swissdec_form
msgid "Source-tax"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_payslip_is_log_line__code__isworkeddays
msgid "Source-tax Worked Days"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_payslip_is_log_line__code__isworkeddaysinch
msgid "Source-tax Worked Days in Switzerland"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_employee__l10n_ch_no_nationality
msgid "Special Nationality Status"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_1212
msgid "Special allowance"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_employee__l10n_ch_tax_specially_approved
msgid "Specially Approved by the ACI"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,help:l10n_ch_hr_payroll_elm_transmission.field_hr_contract__l10n_ch_lpp_withdrawal_reason
msgid "Specify here the entry in LPP reason."
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,help:l10n_ch_hr_payroll_elm_transmission.field_hr_contract__l10n_ch_lpp_entry_reason
msgid "Specify here the withdrawal from LPP reason."
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-python
#: code:addons/l10n_ch_hr_payroll_elm_transmission/models/l10n_ch_declaration_yearly_retrospective.py:0
msgid "Split AVS Salary"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_declaration/declare_salary_renderer.xml:0
msgid "Sporadic"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_declaration/declare_salary_renderer.xml:0
msgid "Sporadic Benefits"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.hr_employee_view_swissdec_form
msgid "Spouse"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_employee__l10n_ch_spouse_birthday
msgid "Spouse Birthday"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_employee__l10n_ch_spouse_first_name
msgid "Spouse First Name"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_employee__l10n_ch_spouse_revenues
msgid "Spouse Has Income"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_employee__l10n_ch_spouse_last_name
msgid "Spouse Last Name"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_employee__l10n_ch_spouse_residence_canton
msgid "Spouse Residence Canton"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_employee__l10n_ch_spouse_city
msgid "Spouse Residence City"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_employee__l10n_ch_spouse_country_id
msgid "Spouse Residence Country"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_employee__l10n_ch_spouse_zip
msgid "Spouse Residence ZIP-Code"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_result/salary_result.xml:0
msgid "Staff Overview"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.hr_employee_view_swissdec_form
msgid "Start Date"
msgstr "Begindatum"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_employee_is_line__state
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_employee_is_line_correction__state
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_avs_splits__state
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_swissdec_declaration__state
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.hr_employee_view_swissdec_form
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.res_config_settings_view_form
msgid "State"
msgstr "Status"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_swissdec_job_result__domain__statistic
#: model:ir.ui.menu,name:l10n_ch_hr_payroll_elm_transmission.menu_l10n_ch_statistic_report
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.res_config_settings_view_form
msgid "Statistic"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.report_wage_types_document
msgid "Statistic (M)"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.report_wage_types_document
msgid "Statistic (Y)"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.actions.act_window,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_statistic_declaration_action
msgid "Statistic Declaration"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_statistic_report_view_form
msgid "Statistic Report"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_declaration/declare_salary_renderer.xml:0
msgid "Statistic Salaries"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_res_company__l10n_ch_statistics_convention
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_res_config_settings__l10n_ch_statistics_convention
msgid "Statistics Pay Agreement"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_res_company__l10n_ch_statistics_payroll_unit
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_res_config_settings__l10n_ch_statistics_payroll_unit
msgid "Statistics Payroll Unit"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.swissdec_wage_types_form
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.swissdec_wage_types_tree
msgid "Statistique année"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.swissdec_wage_types_form
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.swissdec_wage_types_tree
msgid "Statistique mois"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_dialog_message__status
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_hr_contract_wage__contract_state
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.hr_employee_view_swissdec_form
msgid "Status"
msgstr "Status"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_swissdec_job_result__status_response_json
msgid "Status Response Json"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,help:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_hr_contract_wage__contract_state
msgid "Status of the contract"
msgstr "Status van het contract"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_dialog_message__swissdec_story_id
msgid "Story ID"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.hr_employee_view_swissdec_form
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.view_l10n_ch_salary_certificate_profile_form
msgid "Street 2..."
msgstr "Straat 2..."

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.hr_employee_view_swissdec_form
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.view_l10n_ch_salary_certificate_profile_form
msgid "Street..."
msgstr "Straat..."

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_dialog_message_field__swissdec_answer_value_type__string
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_dialog_message_field__swissdec_value_type__string
msgid "String"
msgstr "String"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.hr_employee_view_swissdec_form
msgid "Study Level"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_social_insurance_view_form
msgid "Sub-Number"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.hr_employee_view_swissdec_form
msgid "Subject To Source Tax"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_2010
msgid "Subsidiary military fund"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/client_action/interoperability_client.xml:0
msgid "Subtraction Result"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_result/salary_result.xml:0
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_swissdec_job_result__general_state__success
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_swissdec_job_result__result_state__success
msgid "Success"
msgstr "Succes"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_1213
msgid "Success bonus"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_1073
msgid "Sunday work allowance"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model,name:l10n_ch_hr_payroll_elm_transmission.model_l10n_ch_swiss_wage_component
msgid "Swiss Basic Wage Components"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.view_l10n_ch_employee_monthly_values_form
msgid "Swiss Employee Monthly Values"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.view_l10n_ch_employee_yearly_values_form
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.view_l10n_ch_employee_yearly_values_tree
msgid "Swiss Employee Yearly Values"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model,name:l10n_ch_hr_payroll_elm_transmission.model_l10n_ch_employee_monthly_values
#: model:ir.model,name:l10n_ch_hr_payroll_elm_transmission.model_l10n_ch_employee_yearly_values
msgid "Swiss Employee yearly history"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.actions.act_window,name:l10n_ch_hr_payroll_elm_transmission.action_l10n_ch_master_data_report_wizard
#: model:ir.model,name:l10n_ch_hr_payroll_elm_transmission.model_l10n_ch_master_data_report
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.view_l10n_ch_master_data_report_wizard_form
msgid "Swiss Master Data Report"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model,name:l10n_ch_hr_payroll_elm_transmission.model_l10n_ch_tax_rate_import_wizard
msgid "Swiss Payroll: Extended tax rate import wizard (website download)"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model,name:l10n_ch_hr_payroll_elm_transmission.model_l10n_ch_individual_account
msgid "Swiss Payroll: Individual Account"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model,name:l10n_ch_hr_payroll_elm_transmission.model_l10n_ch_monthly_summary
msgid "Swiss Payroll: Monthly Summary"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_social_insurance_view_form
msgid "Swiss Social Insurance (AVS)"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_hr_payroll_salary_rule
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_hr_payroll_salary_rule_tree
msgid "Swiss Wage Type Code"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model,name:l10n_ch_hr_payroll_elm_transmission.model_l10n_ch_accident_insurance
msgid "Swiss: Accident Insurances (AAP/AANP)"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model,name:l10n_ch_hr_payroll_elm_transmission.model_l10n_ch_accident_insurance_line_rate
msgid "Swiss: Accident Insurances Line Rate (AAP/AANP)"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model,name:l10n_ch_hr_payroll_elm_transmission.model_l10n_ch_additional_accident_insurance
msgid "Swiss: Additional Accident Insurances (LAAC)"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model,name:l10n_ch_hr_payroll_elm_transmission.model_l10n_ch_additional_accident_insurance_line
msgid "Swiss: Additional Accident Insurances Line (LAAC)"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model,name:l10n_ch_hr_payroll_elm_transmission.model_l10n_ch_compensation_fund
msgid "Swiss: Family Allowance (CAF)"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model,name:l10n_ch_hr_payroll_elm_transmission.model_l10n_ch_lpp_insurance
msgid "Swiss: LPP Insurances"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model,name:l10n_ch_hr_payroll_elm_transmission.model_l10n_ch_sickness_insurance
msgid "Swiss: Sickness Insurances (IJM)"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model,name:l10n_ch_hr_payroll_elm_transmission.model_l10n_ch_sickness_insurance_line
msgid "Swiss: Sickness Insurances Line (IJM)"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model,name:l10n_ch_hr_payroll_elm_transmission.model_l10n_ch_social_insurance
msgid "Swiss: Social Insurances (AVS, AC)"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model,name:l10n_ch_hr_payroll_elm_transmission.model_l10n_ch_social_insurance_avs_line
msgid "Swiss: Social Insurances - AVS Line"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_dialog_message_field__swissdec_id
msgid "Swissdec"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_dialog_message_field__swissdec_answer_default_Amount
msgid "Swissdec Answer Default Amount"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_dialog_message_field__swissdec_answer_default_Boolean
msgid "Swissdec Answer Default Boolean"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_dialog_message_field__swissdec_answer_default_Date
msgid "Swissdec Answer Default Date"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_dialog_message_field__swissdec_answer_default_DateTime
msgid "Swissdec Answer Default Datetime"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_dialog_message_field__swissdec_answer_default_Double
msgid "Swissdec Answer Default Double"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_dialog_message_field__swissdec_answer_default_Integer
msgid "Swissdec Answer Default Integer"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_dialog_message_field__swissdec_answer_default_String
msgid "Swissdec Answer Default String"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_dialog_message_field__swissdec_answer_default_YesNoUnknown
msgid "Swissdec Answer Default Yesnounknown"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_dialog_message_field__swissdec_answer_optional
msgid "Swissdec Answer Optional"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_dialog_message_field__swissdec_answer_value_Amount
msgid "Swissdec Answer Value Amount"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_dialog_message_field__swissdec_answer_value_Boolean
msgid "Swissdec Answer Value Boolean"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_dialog_message_field__swissdec_answer_value_Date
msgid "Swissdec Answer Value Date"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_dialog_message_field__swissdec_answer_value_DateTime
msgid "Swissdec Answer Value Datetime"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_dialog_message_field__swissdec_answer_value_Double
msgid "Swissdec Answer Value Double"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_dialog_message_field__swissdec_answer_value_Integer
msgid "Swissdec Answer Value Integer"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_dialog_message_field__swissdec_answer_value_String
msgid "Swissdec Answer Value String"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_dialog_message_field__swissdec_answer_value_type
msgid "Swissdec Answer Value Type"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_dialog_message_field__swissdec_answer_value_YesNoUnknown
msgid "Swissdec Answer Value Yesnounknown"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model,name:l10n_ch_hr_payroll_elm_transmission.model_l10n_ch_swissdec_declaration
msgid "Swissdec Declaration"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model,name:l10n_ch_hr_payroll_elm_transmission.model_l10n_ch_dialog_message
msgid "Swissdec Dialog Message"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model,name:l10n_ch_hr_payroll_elm_transmission.model_l10n_ch_dialog_message_field
msgid "Swissdec Dialog Message Field"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_dialog_message_field__swissdec_has_default
msgid "Swissdec Has Default"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model,name:l10n_ch_hr_payroll_elm_transmission.model_l10n_ch_swissdec_job_result
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_dialog_message__swissdec_job_id
msgid "Swissdec Job"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_dialog_message_field__swissdec_label
msgid "Swissdec Label"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_dialog_message_field__swissdec_section_id_ref
msgid "Swissdec Section Id Ref"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_dialog_message__swissdec_StandardDialogID
msgid "Swissdec Standarddialogid"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.ui.menu,name:l10n_ch_hr_payroll_elm_transmission.menu_l10n_ch_transmission
msgid "Swissdec Transmission"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_dialog_message_field__swissdec_value
msgid "Swissdec Value"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_dialog_message_field__swissdec_value_type
msgid "Swissdec Value Type"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.actions.report,name:l10n_ch_hr_payroll_elm_transmission.action_report_payslip_ch_elm
msgid "Switzerland: Payslip"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/client_action/interoperability_client.xml:0
msgid "System DateTime"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/client_action/interoperability_client.xml:0
msgid "System Datetime :"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_tax_rate_import_wizard__canton__tg
msgid "TG"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_tax_rate_import_wizard__canton__ti
msgid "TI"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_insurance_report_view_transmission_form
msgid "TXB Report"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.hr_employee_view_swissdec_form
msgid "Tags"
msgstr "Labels"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_employee__l10n_ch_tax_scale_type__taxatsourcecode
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_payslip_is_log_line__tax_at_source_category__taxatsourcecode
msgid "Tariff Code"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_swissdec_job_result__domain__tax
msgid "Tax"
msgstr "Btw"

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_declaration/declare_salary_renderer.xml:0
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_result/salary_result.xml:0
msgid "Tax At Source"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_result/salary_result.xml:0
msgid "Tax At Source Quittance"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_declaration/declare_salary_renderer.xml:0
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_result/salary_result.xml:0
msgid "Tax At Source Salaries"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_result/salary_result.xml:0
msgid "Tax At Source:"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_ch_yearly_report__tax_certificates
msgid "Tax Certificates"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_ch_yearly_report__tax_cross_border_institutions
msgid "Tax Cross Border Institutions"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_swissdec_job_result__domain__taxcrossborder
msgid "Tax Crossborder"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_salary_certificate__tax_rectificate_employee_ids
msgid "Tax Rectificate Employee"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_salary_certificate__tax_rectificate_type
msgid "Tax Rectificate Type"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_declaration/declare_salary_renderer.xml:0
msgid "Tax Salaries"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.actions.act_window,name:l10n_ch_hr_payroll_elm_transmission.action_l10n_ch_certificate
msgid "Tax Salaries Declaration"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model,name:l10n_ch_hr_payroll_elm_transmission.model_l10n_ch_salary_certificate
msgid "Tax Salaries Rectification"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_employee_is_line_correction__l10n_ch_tax_scale
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.hr_employee_view_swissdec_form
msgid "Tax Scale"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_employee__l10n_ch_tax_scale_type
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_employee_is_line_correction__l10n_ch_tax_scale_type
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_payslip_is_log_line__tax_at_source_category
msgid "Tax Scale Type"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_tax_rate_import_wizard__year
msgid "Tax Year"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model,name:l10n_ch_hr_payroll_elm_transmission.model_l10n_ch_is_report
msgid "Tax at Source Monthly Report"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_employee_monthly_values__additional_particular
msgid "Tax-At-Source Additional Particular"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_insurance_report_view_transmission_form
msgid "Tax-Crossborder"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-python
#: code:addons/l10n_ch_hr_payroll_elm_transmission/models/l10n_ch_swissdec_declaration_result.py:0
msgid "Tax-Crossborder (%s)"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_declaration/declare_salary_renderer.xml:0
msgid "Taxable"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_declaration/declare_salary_renderer.xml:0
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_result/salary_result.xml:0
msgid "Taxable Earning"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_result/salary_result.xml:0
msgid "Taxable Earning:"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_1960
msgid "Taxable profit-sharing rights"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_1070
msgid "Team Work Allowance"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.hr_payslip_view_form_inherit
msgid "Technical Lines"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule.category,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_technical_category
msgid "Technical Wage Type"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_1112
msgid "Tenacity bonus"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_employee__l10n_ch_spouse_residence_canton__ti
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_employee__l10n_ch_weekly_residence_canton__ti
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_employee_is_line_correction__l10n_ch_source_tax_canton__ti
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_payslip_is_log_line__source_tax_canton__ti
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_is_mutation__qst_canton__ti
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_salary_certificate_profile__l10n_ch_cs_employee_parti_fair_market_value_canton__ti
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_salary_certificate_profile__l10n_ch_cs_expense_expatriate_ruling_approved_canton__ti
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_salary_certificate_profile__l10n_ch_cs_expense_policy_approved_canton__ti
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_source_tax_institution__canton__ti
msgid "Tessin"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_swissdec_declaration_form
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.view_l10n_ch_swissdec_job_result_form
msgid "Test"
msgstr "Test"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_ch_yearly_report__test_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_ema_declaration__test_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_is_report__test_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_lpp_basis_report__test_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_salary_certificate__test_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_statistic_report__test_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_swissdec_declaration__test_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_swissdec_job_result__test_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_swissdec_transmitter__test_transmission
msgid "Test Transmission"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,help:l10n_ch_hr_payroll_elm_transmission.field_hr_employee__l10n_ch_country_id_code
msgid ""
"The ISO country code in two chars. \n"
"You can use this field for quick search."
msgstr ""
"De ISO-landcode in twee letters.\n"
"Je kunt dit veld gebruiken voor snelzoeken."

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_result/salary_result.xml:0
msgid ""
"The completion release information is missing and required by the "
"Institution."
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,help:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_swiss_wage_component__contract_id
msgid "The contract this worked days should be applied to"
msgstr "Het contract waarop deze gewerkte dagen moet worden toegepast"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,help:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_hr_contract_wage__description
msgid "The description you want to display on the payslip"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-python
#: code:addons/l10n_ch_hr_payroll_elm_transmission/models/l10n_ch_swissdec_declaration_result.py:0
msgid "The following Corrections were imported, please confirm them:"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_hr_payroll_employee_lang_view_form
msgid ""
"The following employees have an invalid gender for the selected salary structure.\n"
"                        <br/>\n"
"                        Please assign them a gender below before continuing."
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.constraint,message:l10n_ch_hr_payroll_elm_transmission.constraint_l10n_ch_employee_monthly_values_ch_month_constraint
msgid "The month must be between 1 and 12."
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-python
#: code:addons/l10n_ch_hr_payroll_elm_transmission/models/hr_leave.py:0
msgid ""
"The selected period is covered by a validated payslip. You can't create a "
"time off for that period."
msgstr ""
"De geselecteerde periode wordt gedekt door een gevalideerde loonstrook. Je "
"kunt voor die periode geen verlof creëren."

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,help:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_hr_contract_wage__date_start
msgid "The wage type payment will be applied in the month covering this date"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-python
#: code:addons/l10n_ch_hr_payroll_elm_transmission/models/l10n_ch_monthly_summary.py:0
msgid "There is no paid or done payslips over the selected period."
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_2050
msgid "Third Party Correction"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_rule_1207_hourly
msgid "Thirteen Month (Hourly)"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_rule_13_prov
msgid "Thirteen Month Provision"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_rule_13_prov_hourly
msgid "Thirteen Month Provision (Hourly)"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_rule_1200
msgid "Thirteenth Month"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.hr_payslip_line_view_search
msgid "This Year"
msgstr "Dit jaar"

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-python
#: code:addons/l10n_ch_hr_payroll_elm_transmission/models/l10n_ch_salary_certificate_profile.py:0
msgid "This action can only be performed on templates"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-python
#: code:addons/l10n_ch_hr_payroll_elm_transmission/models/res_company.py:0
#, python-format
msgid "This feature is only allowed in production environments."
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_result/salary_result.xml:0
msgid "This operation is not supported by the institution."
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.report_company_master_data_document
msgid "Threshold"
msgstr "Drempel"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_employee__l10n_ch_spouse_residence_canton__tg
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_employee__l10n_ch_weekly_residence_canton__tg
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_employee_is_line_correction__l10n_ch_source_tax_canton__tg
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_payslip_is_log_line__source_tax_canton__tg
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_is_mutation__qst_canton__tg
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_salary_certificate_profile__l10n_ch_cs_employee_parti_fair_market_value_canton__tg
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_salary_certificate_profile__l10n_ch_cs_expense_expatriate_ruling_approved_canton__tg
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_salary_certificate_profile__l10n_ch_cs_expense_policy_approved_canton__tg
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_source_tax_institution__canton__tg
msgid "Thurgovie"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model,name:l10n_ch_hr_payroll_elm_transmission.model_hr_leave
msgid "Time Off"
msgstr "Verlof"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model,name:l10n_ch_hr_payroll_elm_transmission.model_hr_leave_type
msgid "Time Off Type"
msgstr "Verloftype"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_1920
msgid "Tips subject to AHV contributions"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_dialog_message__swissdec_Title
msgid "Title"
msgstr "Titel"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_absence_leave_view_form
msgid "To"
msgstr "Tot"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_salary_certificate_profile__l10n_ch_cs_car_policy__toclarify
msgid "To be clarified"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_report_payslip
msgid "To pay on"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_report_payslip
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.report_individual_account
msgid "Total"
msgstr "Totaal"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.hr_payslip_view_form_inherit
msgid "Total Amount"
msgstr "Totaalbedrag"

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_result/salary_result.xml:0
msgid "Total Commission:"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_result/salary_result.xml:0
msgid "Total Contributory Salary:"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_result/salary_result.xml:0
msgid "Total Family Income Supplement:"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_employee__l10n_ch_total_activity_type__gross
msgid "Total Gross Monthly Income"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_employee__l10n_ch_other_activity_gross
msgid "Total Income"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_employee__l10n_ch_other_activity_percentage
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_employee__l10n_ch_total_activity_type__percentage
msgid "Total Percentage"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_result/salary_result.xml:0
msgid "Total Tax At Source:"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_result/salary_result.xml:0
msgid "Total Taxable Earning:"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_result/salary_result.xml:0
msgid "Total-AHV-AVS-Incomes:"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_result/salary_result.xml:0
msgid "Total-ALV-AC-Incomes:"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_result/salary_result.xml:0
msgid "Total-ALV-AC-Open:"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_result/salary_result.xml:0
msgid "Total-ALVZ-ACS-Incomes:"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_result/salary_result.xml:0
msgid "Total-FLG-LFA-FamilyIncome-Supplement:"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_result/salary_result.xml:0
msgid "Total-FLG-LFA-Incomes:"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_result/salary_result.xml:0
msgid "Totals for Current Month"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_1303
msgid "Training and development salary"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_1056
msgid "Transfer allowance"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.ui.menu,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_menu_swissdec_transmission
msgid "Transmission"
msgstr "Transmissie"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_swissdec_declaration__transmission_date
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_swissdec_job_result__transmission_date
msgid "Transmission Date"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_result/salary_result.xml:0
msgid "Transmission Date:"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_res_config_settings__l10n_ch_transmission_language
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.res_config_settings_view_form
msgid "Transmission Language"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_result/salary_result.xml:0
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/status_result/status_result.xml:0
msgid "TransmissionDate:"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.view_l10n_ch_salary_certificate_profile_form
msgid "Transportation and Meals"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_1055
msgid "Travel allowance"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_hr_contract_wage__type
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_swiss_wage_component__work_entry_type_id
msgid "Type"
msgstr "Soort"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_lpp_insurance__uid_bfs
msgid "UID-BFS"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_accident_insurance_view_form
msgid "UID-BFS Number"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_tax_rate_import_wizard__canton__ur
msgid "UR"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_result/salary_result.xml:0
msgid "UVG-LAA Master Total:"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_result/salary_result.xml:0
msgid "UVG-LAA Quittance"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_result/salary_result.xml:0
msgid "UVGZ-LAAC Master Total:"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_result/salary_result.xml:0
msgid "UVGZ-LAAC Quittance"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_accident_insurance__uid_bfs_number
msgid "Uid Bfs Number"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/client_action/interoperability_client.xml:0
msgid "UmlautString"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/client_action/interoperability_client.xml:0
msgid "UmlautString Is Correct"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:hr.salary.rule,note:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_rule_5020_comp
msgid ""
"Unemployment Insurance\n"
"Source: https://www.cvcicaisseavs.ch/en/employer/unemployment-insurance-ac.html"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_social_insurance_view_form
msgid "Unemployment Insurance (AC)"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_hr_contract_wage__uom
msgid "Unit"
msgstr "Eenheid"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_employee__l10n_ch_relationship_ceo__unknown
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_employee__l10n_ch_total_activity_type__unknown
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_dialog_message_field__swissdec_answer_default_YesNoUnknown__unknown
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_dialog_message_field__swissdec_answer_value_YesNoUnknown__unknown
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.hr_employee_view_swissdec_form
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_accident_insurance_view_form
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_hr_employee_children_view_form
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_lpp_insurance_view_form
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.res_config_settings_view_form
msgid "Unknown"
msgstr "Onbekend"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_salary_certificate_profile__l10n_ch_cs_employee_participation_taxable_income_unlisted
msgid "Unlisted Stock Options"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.work.entry.type,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_swissdec_unpaid_wt
msgid "Unpaid Salary"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.leave.type,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_swissdec_unpaid_lt
msgid "Unpaid leave"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_employee__l10n_ch_relationship_ceo__unrelated
msgid "Unrelated to the owner"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_social_insurance_view_form
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.report_company_master_data_document
msgid "Until"
msgstr "T/m"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.view_l10n_ch_salary_certificate_profile_form
msgid "Update All Wage Statements"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_employee__l10n_ch_spouse_residence_canton__ur
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_employee__l10n_ch_weekly_residence_canton__ur
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_employee_is_line_correction__l10n_ch_source_tax_canton__ur
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_payslip_is_log_line__source_tax_canton__ur
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_is_mutation__qst_canton__ur
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_salary_certificate_profile__l10n_ch_cs_employee_parti_fair_market_value_canton__ur
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_salary_certificate_profile__l10n_ch_cs_expense_expatriate_ruling_approved_canton__ur
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_salary_certificate_profile__l10n_ch_cs_expense_policy_approved_canton__ur
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_source_tax_institution__canton__ur
msgid "Uri"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_tax_rate_import_wizard__canton__vd
msgid "VD"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_tax_rate_import_wizard__canton__vs
msgid "VS"
msgstr "VS"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.swissdec_wage_types_tree
msgid "Vacation"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_rule_hol_alw_base
msgid "Vacation Base"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_rule_1160
msgid "Vacation compensation"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_rule_vacation_balance
msgid "Vacation pay Balance"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_rule_vacation_provision
msgid "Vacation pay Provision"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_1163
msgid "Vacation payment after departure"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_1162
msgid "Vacation payments"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_employee__l10n_ch_spouse_residence_canton__vs
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_employee__l10n_ch_weekly_residence_canton__vs
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_employee_is_line_correction__l10n_ch_source_tax_canton__vs
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_payslip_is_log_line__source_tax_canton__vs
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_is_mutation__qst_canton__vs
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_salary_certificate_profile__l10n_ch_cs_employee_parti_fair_market_value_canton__vs
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_salary_certificate_profile__l10n_ch_cs_expense_expatriate_ruling_approved_canton__vs
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_salary_certificate_profile__l10n_ch_cs_expense_policy_approved_canton__vs
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_source_tax_institution__canton__vs
msgid "Valais"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_employee_is_line__valid_as_of
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_is_mutation__valid_as_of
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_lpp_mutation__valid_as_of
msgid "Valid As Of"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_result/salary_result.xml:0
msgid "Valid As Of:"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_salary_certificate_profile__valid_from
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_social_insurance_view_form
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.report_company_master_data_document
msgid "Valid From"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_payslip__l10n_ch_validation_errors
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_employee_monthly_values__validation_errors
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_employee_yearly_values__validation_errors
msgid "Validation Errors"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-python
#: code:addons/l10n_ch_hr_payroll_elm_transmission/models/l10n_ch_salary_certificate_profile.py:0
msgid "Validity date must start on the first of the month"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_dialog_message_field__field_type__value
msgid "Value"
msgstr "Waarde"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_employee__l10n_ch_spouse_residence_canton__vd
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_employee__l10n_ch_weekly_residence_canton__vd
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_employee_is_line_correction__l10n_ch_source_tax_canton__vd
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_payslip_is_log_line__source_tax_canton__vd
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_is_mutation__qst_canton__vd
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_salary_certificate_profile__l10n_ch_cs_employee_parti_fair_market_value_canton__vd
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_salary_certificate_profile__l10n_ch_cs_expense_expatriate_ruling_approved_canton__vd
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_salary_certificate_profile__l10n_ch_cs_expense_policy_approved_canton__vd
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_source_tax_institution__canton__vd
msgid "Vaud"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-python
#: code:addons/l10n_ch_hr_payroll_elm_transmission/models/l10n_ch_swissdec_declaration_result.py:0
msgid "Verified Source-Tax Statement"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_swissdec_declaration__state__plausibility
msgid "Verifying Plausibility"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_salary_certificate_profile__l10n_ch_cs_employee_participation_taxable_income_virtual
msgid "Virtual Participation"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.report_company_master_data_document
msgid "Wage From"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.hr_employee_view_swissdec_form
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.view_l10n_ch_salary_certificate_profile_form
msgid "Wage Statement"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_ch_yearly_report__wage_statement_count
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_salary_certificate__wage_statement_count
msgid "Wage Statement Count"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.view_l10n_ch_salary_certificate_profile_form
msgid "Wage Statement Name"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.actions.act_window,name:l10n_ch_hr_payroll_elm_transmission.action_l10n_ch_salary_certificate_profile
msgid "Wage Statement Profile"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.ui.menu,name:l10n_ch_hr_payroll_elm_transmission.menu_l10n_ch_cs_profiles
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_insurance_report_view_transmission_form
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.view_l10n_ch_certificate_report_form
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.view_l10n_ch_salary_certificate_profile_tree
msgid "Wage Statements"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-python
#: code:addons/l10n_ch_hr_payroll_elm_transmission/models/l10n_ch_declaration_salary_certificates.py:0
#: code:addons/l10n_ch_hr_payroll_elm_transmission/models/l10n_ch_declaration_yearly_retrospective.py:0
msgid "Wage Statements %s"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.report_company_master_data_document
msgid "Wage To"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_contract__wage_type
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_hr_contract_wage__input_type_id
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_swiss_wage_component__name
msgid "Wage Type"
msgstr "Soort salaris"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_master_data_report__wage_type_pdf_filename
msgid "Wage Type Pdf Filename"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_master_data_report__wage_type_pdf_file
msgid "Wage Type Report"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.hr_payslip_view_form_inherit
msgid "Wage Types"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.report_wage_types_document
msgid "Wage Types Report -"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__res_company__l10n_ch_statistics_convention__collectivecontractoutside-cla
msgid "Wage agreement outside of a collective agreement"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-python
#: code:addons/l10n_ch_hr_payroll_elm_transmission/models/l10n_ch_declaration_salary_certificates.py:0
#: code:addons/l10n_ch_hr_payroll_elm_transmission/models/l10n_ch_declaration_yearly_retrospective.py:0
msgid "Wage_statement_%(year)s_%(name)s"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.hr_contract_view_form
msgid "Wages"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_1299
msgid "Wages in the event of Maternity/Paternity Leave"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_1300
msgid "Wages in the event of an accident"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_1301
msgid "Wages in the event of illness"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_dialog_message__status__waiting
msgid "Waiting Reply"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_2075
msgid "Waiting day"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_swissdec_job_result__success_state__completionandresult
msgid "Waiting for Completion"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_swissdec_job_result__success_state__dialogandresult
msgid "Waiting for Dialog Response"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_swissdec_job_result__result_state__waiting
msgid "Waiting for Results"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_swissdec_declaration__state__waiting
msgid "Waiting for Status"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/components/swissdec_notification.xml:0
msgid "Warnings"
msgstr "Waarschuwingen"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_ch_yearly_report__website_message_ids
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_salary_rule__website_message_ids
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_ema_declaration__website_message_ids
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_is_report__website_message_ids
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_lpp_basis_report__website_message_ids
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_salary_certificate__website_message_ids
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_statistic_report__website_message_ids
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_swissdec_declaration__website_message_ids
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_swissdec_job_result__website_message_ids
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_swissdec_transmitter__website_message_ids
msgid "Website Messages"
msgstr "Websiteberichten"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,help:l10n_ch_hr_payroll_elm_transmission.field_ch_yearly_report__website_message_ids
#: model:ir.model.fields,help:l10n_ch_hr_payroll_elm_transmission.field_hr_salary_rule__website_message_ids
#: model:ir.model.fields,help:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_ema_declaration__website_message_ids
#: model:ir.model.fields,help:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_is_report__website_message_ids
#: model:ir.model.fields,help:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_lpp_basis_report__website_message_ids
#: model:ir.model.fields,help:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_salary_certificate__website_message_ids
#: model:ir.model.fields,help:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_statistic_report__website_message_ids
#: model:ir.model.fields,help:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_swissdec_declaration__website_message_ids
#: model:ir.model.fields,help:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_swissdec_job_result__website_message_ids
#: model:ir.model.fields,help:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_swissdec_transmitter__website_message_ids
msgid "Website communication history"
msgstr "Website communicatie geschiedenis"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_3035
msgid "Wedding allowance"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_employee__l10n_ch_residence_type__weekly
msgid "Weekly"
msgstr "Wekelijks"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_contract__l10n_ch_weekly_hours
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.report_company_master_data_document
msgid "Weekly Hours"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_contract__l10n_ch_weekly_lessons
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.report_company_master_data_document
msgid "Weekly Lessons"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.hr_employee_view_swissdec_form
msgid "Weekly Residence Address"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_employee__l10n_ch_weekly_residence_canton
msgid "Weekly Residence Canton"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_employee__l10n_ch_weekly_residence_address_city
msgid "Weekly Residence City"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_employee__l10n_ch_weekly_residence_municipality
msgid "Weekly Residence Municipality"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_employee__l10n_ch_weekly_residence_address_street
msgid "Weekly Residence Street"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_employee__l10n_ch_weekly_residence_address_zip
msgid "Weekly Residence ZIP-Code"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,help:l10n_ch_hr_payroll_elm_transmission.field_hr_salary_rule__l10n_ch_aanp_included
msgid ""
"Whether the amount is included in the basis to compute the accident "
"insurance deduction"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,help:l10n_ch_hr_payroll_elm_transmission.field_hr_salary_rule__l10n_ch_laac_included
msgid ""
"Whether the amount is included in the basis to compute the additional "
"accident insurance deduction"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,help:l10n_ch_hr_payroll_elm_transmission.field_hr_salary_rule__l10n_ch_ijm_included
#: model:ir.model.fields,help:l10n_ch_hr_payroll_elm_transmission.field_hr_salary_rule__l10n_ch_source_tax_included
msgid ""
"Whether the amount is included in the basis to compute the daily sick pay "
"deduction"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,help:l10n_ch_hr_payroll_elm_transmission.field_hr_salary_rule__l10n_ch_ac_included
msgid ""
"Whether the amount is included in the basis to compute the "
"retirement/unemployement deduction"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_employee__l10n_ch_relationship_ceo__ownerwife
msgid "Wife of the owner"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_is_mutation__reason__withdrawalnat
msgid "Withdrawal : Naturalisation"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_contract__l10n_ch_lpp_withdrawal_reason__withdrawalcompany
msgid "Withdrawal From Company"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_contract__l10n_ch_lpp_withdrawal_reason
msgid "Withdrawal Reason"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_contract__l10n_ch_lpp_withdrawal_valid_as_of
msgid "Withdrawal Valid As Of"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_declaration/declare_salary_renderer.xml:0
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_result/salary_result.xml:0
msgid "Withdrawal:"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_is_mutation__reason__withdrawalsettled
msgid "Withdrawal: C-Permit"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_is_mutation__reason__withdrawalcanton
msgid "Withdrawal: Canton Change"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_is_mutation__reason__withdrawalother
msgid "Withdrawal: Other"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_is_mutation__reason__withdrawalcompany
msgid "Withdrawal: Withdrawal From Company"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_salary_certificate_profile__l10n_ch_cs_employee_participation_taxable_income
msgid "Without Taxable Income"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_hr_payroll_employee_gender_wizard_line__wizard_id
msgid "Wizard"
msgstr "Wizard"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.hr_employee_view_swissdec_form
msgid "Work Canton"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.hr_employee_view_swissdec_form
msgid "Work Information"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_leave_type__l10n_ch_swissdec_work_interruption
msgid "Work Interruption"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.report_company_master_data_document
msgid "Work Locations"
msgstr "Werklocaties"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model,name:l10n_ch_hr_payroll_elm_transmission.model_l10n_ch_location_unit
msgid "Work Place - Swiss Payroll"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-python
#: code:addons/l10n_ch_hr_payroll_elm_transmission/models/hr_leave.py:0
msgid ""
"Work interruptions must Start on the first of the month and end on the last "
"of the month."
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_employee_is_line_correction__worked_days
msgid "Worked Days"
msgstr "Gewerkte dagen"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_employee_is_line_correction__worked_days_in_switzerland
msgid "Worked Days In Switzerland"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_rule_worked_days
msgid "Worked Days Total"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:hr.salary.rule,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_elm_rule_ch_days
msgid "Worked Days in CH"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_employee__l10n_ch_working_days_in_ch
msgid "Working Days in Switzerland"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_hr_payslip_montlhy_wizard__workplace_id
msgid "Workplace"
msgstr "Werkplaats"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_ch_yearly_report__year
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_avs_splits__year
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_ema_declaration__year
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_employee_monthly_values__year
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_employee_yearly_values__year
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_hr_payslip_montlhy_wizard__year
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_is_report__year
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_lpp_basis_report__year
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_master_data_report__year
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_salary_certificate__year
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_statistic_report__year
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_swissdec_declaration__year
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_swissdec_transmitter__year
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_swissdec_transmitter_form
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.view_l10n_ch_employee_yearly_values_search
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.view_l10n_ch_hr_payslip_monthly_wizard_form
msgid "Year"
msgstr "Jaar"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,help:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_tax_rate_import_wizard__year
msgid "Year for which to download the tax rate file"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_social_insurance_view_form
msgid "Yearly AC Maximum Salary"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_social_insurance_view_form
msgid "Yearly ACC Maximum Salary"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.actions.act_window,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_yearly_retrospective_action
msgid "Yearly Salary Declaration"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.actions.act_window,name:l10n_ch_hr_payroll_elm_transmission.action_l10n_ch_employee_yearly_values
msgid "Yearly Snapshot"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_hr_salary_rule__l10n_ch_yearly_statement
msgid "Yearly Statistic"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.ui.menu,name:l10n_ch_hr_payroll_elm_transmission.l10n_ch_menu_yearly_transmission
msgid "Yearly Transmission"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_declaration/declare_salary_renderer.xml:0
#: model:ir.model.fields,field_description:l10n_ch_hr_payroll_elm_transmission.field_l10n_ch_employee_monthly_values__yearly_values_id
msgid "Yearly Values"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.constraint,message:l10n_ch_hr_payroll_elm_transmission.constraint_l10n_ch_employee_yearly_values_ch_yearly_snapshot_unique
msgid "Yearly values for this employee already exists for the year."
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_dialog_message_field__swissdec_answer_default_YesNoUnknown__yes
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_dialog_message_field__swissdec_answer_value_YesNoUnknown__yes
msgid "Yes"
msgstr "Ja"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_employee__l10n_ch_concubinage__sharecustodyandhigherincome
msgid "Yes with a shared custody and higher income"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_employee__l10n_ch_concubinage__solecustody
msgid "Yes with the sole custody"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_employee__l10n_ch_concubinage__adultchildandhigherincome
msgid "Yes, with an Adult Child and higher income"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_dialog_message_field__swissdec_answer_value_type__yesnounknown
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_dialog_message_field__swissdec_value_type__yesnounknown
msgid "YesNoUnknown"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-python
#: code:addons/l10n_ch_hr_payroll_elm_transmission/models/l10n_ch_transmission_mixin.py:0
msgid "You can only transmit data for the current or previous year."
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-python
#: code:addons/l10n_ch_hr_payroll_elm_transmission/models/l10n_ch_declaration_source_tax.py:0
msgid ""
"You cannot replace a Source-Tax Declaration, please indicate the declaration to be substituted.\n"
" A replacement declaration can only be sent after consultation with all the concerned Tax Authorities."
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-python
#: code:addons/l10n_ch_hr_payroll_elm_transmission/models/l10n_ch_avs_income_splits.py:0
#: code:addons/l10n_ch_hr_payroll_elm_transmission/models/l10n_ch_swissdec_declaration.py:0
#: code:addons/l10n_ch_hr_payroll_elm_transmission/models/l10n_ch_transmission_mixin.py:0
msgid "You must be logged in a Swiss company to use this feature"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-javascript
#: code:addons/l10n_ch_hr_payroll_elm_transmission/static/src/fields/salary_result/salary_result.xml:0
msgid "Your request is being processed by the Institution"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_tax_rate_import_wizard__canton__zg
msgid "ZG"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_tax_rate_import_wizard__canton__zh
msgid "ZH"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.hr_employee_view_swissdec_form
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.view_l10n_ch_salary_certificate_profile_form
msgid "ZIP"
msgstr "Postcode"

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_employee__l10n_ch_spouse_residence_canton__zg
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_employee__l10n_ch_weekly_residence_canton__zg
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_employee_is_line_correction__l10n_ch_source_tax_canton__zg
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_payslip_is_log_line__source_tax_canton__zg
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_is_mutation__qst_canton__zg
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_salary_certificate_profile__l10n_ch_cs_employee_parti_fair_market_value_canton__zg
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_salary_certificate_profile__l10n_ch_cs_expense_expatriate_ruling_approved_canton__zg
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_salary_certificate_profile__l10n_ch_cs_expense_policy_approved_canton__zg
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_source_tax_institution__canton__zg
msgid "Zoug"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_employee__l10n_ch_spouse_residence_canton__zh
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_employee__l10n_ch_weekly_residence_canton__zh
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_employee_is_line_correction__l10n_ch_source_tax_canton__zh
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__hr_payslip_is_log_line__source_tax_canton__zh
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_is_mutation__qst_canton__zh
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_salary_certificate_profile__l10n_ch_cs_employee_parti_fair_market_value_canton__zh
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_salary_certificate_profile__l10n_ch_cs_expense_expatriate_ruling_approved_canton__zh
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_salary_certificate_profile__l10n_ch_cs_expense_policy_approved_canton__zh
#: model:ir.model.fields.selection,name:l10n_ch_hr_payroll_elm_transmission.selection__l10n_ch_source_tax_institution__canton__zh
msgid "Zurich"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.view_l10n_ch_source_tax_institution_form
msgid "e.g. \"ACI Berne\""
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_accident_insurance_view_form
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_additional_accident_insurance_view_form
msgid "e.g. \"Accident Insurance Gastrosocial\""
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_compensation_fund_view_form
msgid "e.g. \"Family Allowance AK Bern\""
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_lpp_insurance_view_form
msgid "e.g. \"LPP Insurance AK Bern\""
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_sickness_insurance_view_form
msgid "e.g. \"Sickness Insurance Gastrosocial\""
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_social_insurance_view_form
msgid "e.g. '001'"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_social_insurance_view_form
msgid "e.g. '003.000'"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_social_insurance_view_form
msgid "e.g. '100-9976.9'"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_social_insurance_view_form
msgid "e.g. 'Social Insurance AK Bern'"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_swissdec_transmitter_form
msgid "e.g. 2024 Yearly Declaration"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.l10n_ch_location_unit_view_form
msgid "e.g. A92978109"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#: model_terms:ir.ui.view,arch_db:l10n_ch_hr_payroll_elm_transmission.view_l10n_ch_hr_payslip_monthly_wizard_form
msgid "e.g. September 2025 - Monthly Batch"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-python
#: code:addons/l10n_ch_hr_payroll_elm_transmission/models/l10n_ch_swissdec_declaration_result.py:0
msgid "get_result_response_%s.xml"
msgstr ""

#. module: l10n_ch_hr_payroll_elm_transmission
#. odoo-python
#: code:addons/l10n_ch_hr_payroll_elm_transmission/models/l10n_ch_swissdec_declaration_result.py:0
msgid "get_resultµ_request_%s.xml"
msgstr ""
