# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* social_crm
# 
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# Lasse L, 2024
# <PERSON> <<EMAIL>>, 2024
# <PERSON>, 2024
# <PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:28+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON>, 2024\n"
"Language-Team: Swedish (https://app.transifex.com/odoo/teams/41243/sv/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sv\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: social_crm
#: model_terms:ir.ui.view,arch_db:social_crm.social_post_to_lead_description
msgid "<span class=\"me-1\">posted on</span>"
msgstr ""

#. module: social_crm
#: model_terms:ir.ui.view,arch_db:social_crm.social_post_to_lead_description
msgid "<span class=\"ms-1\">at</span>"
msgstr ""

#. module: social_crm
#: model_terms:ir.ui.view,arch_db:social_crm.social_post_view_form
msgid ""
"<span class=\"o_stat_text\" invisible=\"not use_leads\">Leads</span>\n"
"                        <span class=\"o_stat_text\" invisible=\"use_leads\">Opportunities</span>"
msgstr ""
"<span class=\"o_stat_text\" invisible=\"not use_leads\">Kundämnen</span>\n"
"                        <span class=\"o_stat_text\" invisible=\"use_leads\">Möjligheter</span>"

#. module: social_crm
#: model_terms:ir.ui.view,arch_db:social_crm.social_post_to_lead_view_form
msgid "Cancel"
msgstr "Avbryt"

#. module: social_crm
#: model:ir.model.fields,field_description:social_crm.field_social_post_to_lead__conversion_source
msgid "Conversion Source"
msgstr "Konvertering Källa"

#. module: social_crm
#: model_terms:ir.ui.view,arch_db:social_crm.social_post_to_lead_view_form
msgid "Convert"
msgstr "Konvertera"

#. module: social_crm
#: model:ir.actions.act_window,name:social_crm.social_post_to_lead_action
#: model_terms:ir.ui.view,arch_db:social_crm.social_post_to_lead_view_form
msgid "Convert Post to Lead"
msgstr "Konvertera inlägg till leads"

#. module: social_crm
#: model:ir.model,name:social_crm.model_social_post_to_lead
msgid "Convert Social Post to Lead"
msgstr "Konvertera sociala inlägg till leads"

#. module: social_crm
#. odoo-javascript
#: code:addons/social_crm/static/src/xml/social_crm_button.xml:0
msgid "Create Lead"
msgstr "Skapa kundämne"

#. module: social_crm
#: model:ir.model.fields.selection,name:social_crm.selection__social_post_to_lead__action__create
msgid "Create a new customer"
msgstr "Skapa en ny kund"

#. module: social_crm
#: model:ir.model.fields,field_description:social_crm.field_social_post_to_lead__create_uid
msgid "Created by"
msgstr "Skapad av"

#. module: social_crm
#: model:ir.model.fields,field_description:social_crm.field_social_post_to_lead__create_date
msgid "Created on"
msgstr "Skapad den"

#. module: social_crm
#: model:ir.model.fields,field_description:social_crm.field_social_post_to_lead__partner_id
#: model_terms:ir.ui.view,arch_db:social_crm.social_post_to_lead_view_form
msgid "Customer"
msgstr "Kund"

#. module: social_crm
#: model:ir.model.fields,field_description:social_crm.field_social_post_to_lead__display_name
msgid "Display Name"
msgstr "Visningsnamn"

#. module: social_crm
#: model:ir.model.fields.selection,name:social_crm.selection__social_post_to_lead__action__nothing
msgid "Do not link to a customer"
msgstr "Länka inte till en kund"

#. module: social_crm
#: model:ir.model.fields.selection,name:social_crm.selection__social_post_to_lead__conversion_source__comment
msgid "From a comment"
msgstr "Från en kommentar"

#. module: social_crm
#: model:ir.model.fields.selection,name:social_crm.selection__social_post_to_lead__conversion_source__stream_post
msgid "From a stream post"
msgstr "Från en flödesinlägg"

#. module: social_crm
#: model:ir.model.fields,field_description:social_crm.field_social_post_to_lead__id
msgid "ID"
msgstr "ID"

#. module: social_crm
#: model:ir.model.fields,field_description:social_crm.field_social_post_to_lead__write_uid
msgid "Last Updated by"
msgstr "Senast uppdaterad av"

#. module: social_crm
#: model:ir.model.fields,field_description:social_crm.field_social_post_to_lead__write_date
msgid "Last Updated on"
msgstr "Senast uppdaterad den"

#. module: social_crm
#: model:ir.model.fields,field_description:social_crm.field_social_post__leads_opportunities_count
msgid "Leads / Opportunities count"
msgstr "Antal leads/möjligheter"

#. module: social_crm
#: model_terms:ir.ui.view,arch_db:social_crm.social_post_view_kanban
msgid "Leads:"
msgstr "Leads:"

#. module: social_crm
#: model:ir.model.fields.selection,name:social_crm.selection__social_post_to_lead__action__exist
msgid "Link to an existing customer"
msgstr "Länka till en befintlig kund"

#. module: social_crm
#: model_terms:ir.ui.view,arch_db:social_crm.social_post_view_kanban
msgid "Opportunities:"
msgstr "Möjligheter:"

#. module: social_crm
#: model:ir.model.fields,field_description:social_crm.field_social_post_to_lead__author_name
msgid "Post Author Name"
msgstr "Publiceras författarens namn"

#. module: social_crm
#: model:ir.model.fields,field_description:social_crm.field_social_post_to_lead__post_content
msgid "Post Content"
msgstr "Inläggsinnehåll"

#. module: social_crm
#: model:ir.model.fields,field_description:social_crm.field_social_post_to_lead__post_datetime
msgid "Post Datetime"
msgstr "Publicera Datum"

#. module: social_crm
#: model:ir.model.fields,field_description:social_crm.field_social_post_to_lead__post_image_urls
msgid "Post Images URLs"
msgstr ""

#. module: social_crm
#: model:ir.model.fields,field_description:social_crm.field_social_post_to_lead__post_link
msgid "Post Link"
msgstr "Länk till inlägg"

#. module: social_crm
#: model:ir.model.fields,field_description:social_crm.field_social_post_to_lead__action
msgid "Related Customer"
msgstr "Relaterad kund"

#. module: social_crm
#. odoo-python
#: code:addons/social_crm/wizard/social_post_to_lead.py:0
msgid "Request from %s"
msgstr "Begäran från %s"

#. module: social_crm
#: model:ir.model.fields,field_description:social_crm.field_social_post_to_lead__social_account_id
msgid "Social Account"
msgstr "Socialt konto"

#. module: social_crm
#: model:ir.model,name:social_crm.model_social_post
msgid "Social Post"
msgstr "Social postning"

#. module: social_crm
#: model_terms:ir.ui.view,arch_db:social_crm.social_post_to_lead_description
msgid "Social Post Image {{image_url_index}}"
msgstr ""

#. module: social_crm
#: model:ir.model.fields,field_description:social_crm.field_social_post_to_lead__social_stream_post_id
msgid "Social Stream Post"
msgstr "Inlägg om socialaflöden"

#. module: social_crm
#. odoo-python
#: code:addons/social_crm/wizard/social_post_to_lead.py:0
msgid "The lead has been created successfully."
msgstr "Leadet har skapats framgångsrikt."

#. module: social_crm
#: model:ir.model.fields,field_description:social_crm.field_social_post__use_leads
msgid "Use Leads"
msgstr "Använd kundämnen"

#. module: social_crm
#: model:ir.model.fields,field_description:social_crm.field_social_post_to_lead__utm_campaign_id
msgid "Utm Campaign"
msgstr "UTM-kampanj"

#. module: social_crm
#: model:ir.model.fields,field_description:social_crm.field_social_post_to_lead__utm_medium_id
msgid "Utm Medium"
msgstr "UTM-Medium"

#. module: social_crm
#: model:ir.model.fields,field_description:social_crm.field_social_post_to_lead__utm_source_id
msgid "Utm Source"
msgstr "UTM-Källa"
