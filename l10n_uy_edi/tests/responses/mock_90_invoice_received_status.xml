<s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"
    xmlns:u="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd">
    <s:Header>
        <o:Security s:mustUnderstand="1"
            xmlns:o="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd">
            <u:Timestamp u:Id="_0">
                <u:Created>2024-07-12T17:47:46.505Z</u:Created>
                <u:Expires>2024-07-12T17:52:46.505Z</u:Expires>
            </u:Timestamp>
        </o:Security>
    </s:Header>
    <s:Body>
        <InvokeResponse xmlns="http://www.uruware.com/ucfe/inbox/webservice">
            <InvokeResult xmlns:a="http://schemas.datacontract.org/2004/07/Uruware.Ucfe.Inbox.WebService"
                xmlns:i="http://www.w3.org/2001/XMLSchema-instance">
                <a:ErrorCode>0</a:ErrorCode>
                <a:ErrorMessage i:nil="true"/>
                <a:Resp>
                    <Adenda i:nil="true"/>
                    <CaeNroDesde i:nil="true"/>
                    <CaeNroHasta i:nil="true"/>
                    <Certificado i:nil="true"/>
                    <CertificadoParaFirmarCfe i:nil="true"/>
                    <ClaveCertificadoFirma i:nil="true"/>
                    <CodComercio>Sumila-8485</CodComercio>
                    <CodRta>00</CodRta>
                    <CodTerminal>FC-8485</CodTerminal>
                    <CodigoSeguridad>emil/h</CodigoSeguridad>
                    <DatosQr i:nil="true"/>
                    <EstadoEnDgiCfeRecibido i:nil="true"/>
                    <EstadoSituacion i:nil="true"/>
                    <Etiquetas i:nil="true"/>
                    <FechaFirma>2024-07-12T14:47:14.0000000-03:00</FechaFirma>
                    <FechaReq i:nil="true"/>
                    <HoraReq i:nil="true"/>
                    <IdCae i:nil="true"/>
                    <IdReq>1</IdReq>
                    <ImagenQr i:nil="true"/>
                    <MensajeRta i:nil="true"/>
                    <NumeroCfe>130</NumeroCfe>
                    <RangoDesde i:nil="true"/>
                    <RangoHasta i:nil="true"/>
                    <RutEmisor i:nil="true"/>
                    <Serie>A</Serie>
                    <TipoCfe>101</TipoCfe>
                    <TipoMensaje>361</TipoMensaje>
                    <TipoNotificacion i:nil="true"/>
                    <Uuid>am1777-17_uy_edi_test</Uuid>
                    <VencimientoCae i:nil="true"/>
                    <XmlCfeFirmado i:nil="true"/>
                </a:Resp>
                <a:RespEnc i:nil="true"/>
            </InvokeResult>
        </InvokeResponse>
    </s:Body>
</s:Envelope>
