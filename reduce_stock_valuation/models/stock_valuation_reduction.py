from odoo import models, fields, api
from odoo.exceptions import UserError


class StockValuationReduction(models.Model):
    _name = 'stock.valuation.reduction'
    _description = 'Stock Valuation Reduction'
    _inherit = ['mail.thread', 'mail.activity.mixin']

    name = fields.Char('Reference', required=True, copy=False, readonly=True, default='New')
    date = fields.Date('Date', required=True, default=fields.Date.context_today)
    company_id = fields.Many2one('res.company', 'Company', required=True, default=lambda self: self.env.company)
    state = fields.Selection([
        ('draft', 'Draft'),
        ('done', 'Done'),
        ('cancel', 'Cancelled')
    ], string='Status', default='draft', tracking=True)

    line_ids = fields.One2many('stock.valuation.reduction.line', 'reduction_id', 'Lines')
    journal_id = fields.Many2one('account.journal', 'Journal', required=True)
    move_id = fields.Many2one('account.move', 'Journal Entry', readonly=True)

    @api.model
    def create(self, vals):
        if vals.get('name', 'New') == 'New':
            vals['name'] = self.env['ir.sequence'].next_by_code('stock.valuation.reduction') or 'New'
        return super().create(vals)

    def action_confirm(self):
        if not self.line_ids:
            raise UserError("Please add at least one line.")

        # Create journal entry
        self._create_journal_entry()

        # Create stock valuation layers
        self._create_stock_valuation_layers()

        self.state = 'done'

    def _create_journal_entry(self):
        move_vals = {
            'journal_id': self.journal_id.id,
            'date': self.date,
            'ref': self.name,
            'company_id': self.company_id.id,
            'line_ids': []
        }

        total_amount = 0
        for line in self.line_ids:
            # Debit - Stock Valuation Reduction Account
            move_vals['line_ids'].append((0, 0, {
                'name': f'Stock Valuation Reduction - {line.product_id.name}',
                'account_id': self._get_valuation_reduction_account().id,
                'debit': line.reduction_amount,
                'credit': 0,
                'product_id': line.product_id.id,
            }))

            # Credit - Stock Valuation Account
            move_vals['line_ids'].append((0, 0, {
                'name': f'Stock Valuation - {line.product_id.name}',
                'account_id': line.product_id.categ_id.property_stock_valuation_account_id.id,
                'debit': 0,
                'credit': line.reduction_amount,
                'product_id': line.product_id.id,
            }))

            total_amount += line.reduction_amount

        move = self.env['account.move'].create(move_vals)
        move.action_post()
        self.move_id = move.id

    def _create_stock_valuation_layers(self):
        for line in self.line_ids:
            # Get current stock quantity
            quants = self.env['stock.quant'].search([
                ('product_id', '=', line.product_id.id),
                ('location_id.usage', '=', 'internal'),
                ('company_id', '=', self.company_id.id)
            ])

            total_qty = sum(quants.mapped('quantity'))
            if total_qty <= 0:
                continue

            # Create negative SVL to reduce valuation
            svl_vals = {
                'product_id': line.product_id.id,
                'company_id': self.company_id.id,
                'quantity': 0,  # No quantity change, just value change
                'unit_cost': 0,
                'value': -line.reduction_amount,  # Negative value to reduce
                'description': f'Stock Valuation Reduction - {self.name}',
                'stock_move_id': False,
                'account_move_id': self.move_id.id,
            }

            self.env['stock.valuation.layer'].create(svl_vals)

            # Update product standard price if needed
            if line.update_standard_price:
                new_value = line.product_id.standard_price * total_qty - line.reduction_amount
                new_standard_price = new_value / total_qty if total_qty > 0 else 0
                line.product_id.standard_price = new_standard_price

    def _get_valuation_reduction_account(self):
        # You can configure this account in company settings or use a default one
        account = self.env['account.account'].search([
            ('code', '=', '510001'),  # Example account code
            ('company_id', '=', self.company_id.id)
        ], limit=1)

        if not account:
            # Create or find a suitable expense account
            account = self.env['account.account'].search([
                ('account_type', '=', 'expense'),
                ('company_id', '=', self.company_id.id)
            ], limit=1)

        return account

    def action_cancel(self):
        if self.move_id and self.move_id.state == 'posted':
            # Reverse the journal entry
            reverse_move = self.move_id._reverse_moves([{
                'date': fields.Date.context_today(self),
                'ref': f'Reversal of {self.name}'
            }])
            reverse_move.action_post()

        self.state = 'cancel'


class StockValuationReductionLine(models.Model):
    _name = 'stock.valuation.reduction.line'
    _description = 'Stock Valuation Reduction Line'

    reduction_id = fields.Many2one('stock.valuation.reduction', 'Reduction', required=True, ondelete='cascade')
    product_id = fields.Many2one('product.product', 'Product', required=True, domain=[('type', '=', 'product')])
    current_value = fields.Float('Current Stock Value', compute='_compute_current_value', store=True)
    reduction_amount = fields.Float('Reduction Amount', required=True)
    reason = fields.Text('Reason')
    update_standard_price = fields.Boolean('Update Standard Price', default=True)

    @api.depends('product_id')
    def _compute_current_value(self):
        for line in self:
            if line.product_id:
                quants = self.env['stock.quant'].search([
                    ('product_id', '=', line.product_id.id),
                    ('location_id.usage', '=', 'internal'),
                    ('company_id', '=', line.reduction_id.company_id.id)
                ])
                total_qty = sum(quants.mapped('quantity'))
                line.current_value = total_qty * line.product_id.standard_price
            else:
                line.current_value = 0