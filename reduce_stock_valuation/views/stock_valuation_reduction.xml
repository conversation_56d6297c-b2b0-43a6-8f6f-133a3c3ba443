<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="view_stock_valuation_reduction_form" model="ir.ui.view">
        <field name="name">stock.valuation.reduction.form</field>
        <field name="model">stock.valuation.reduction</field>
        <field name="arch" type="xml">
            <form string="Stock Valuation Reduction">
                <header>
                    <button name="action_confirm" string="Confirm" type="object" class="oe_highlight" states="draft"/>
                    <button name="action_cancel" string="Cancel" type="object" states="draft,done"/>
                    <field name="state" widget="statusbar" statusbar_visible="draft,done"/>
                </header>
                <sheet>
                    <group>
                        <group>
                            <field name="name"/>
                            <field name="date"/>
                            <field name="company_id"/>
                        </group>
                        <group>
                            <field name="journal_id"/>
                            <field name="move_id" readonly="1"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Lines">
                            <field name="line_ids">
                                <tree editable="bottom">
                                    <field name="product_id"/>
                                    <field name="current_value"/>
                                    <field name="reduction_amount"/>
                                    <field name="update_standard_price"/>
                                    <field name="reason"/>
                                </tree>
                            </field>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <record id="view_stock_valuation_reduction_tree" model="ir.ui.view">
        <field name="name">stock.valuation.reduction.tree</field>
        <field name="model">stock.valuation.reduction</field>
        <field name="arch" type="xml">
            <tree>
                <field name="name"/>
                <field name="date"/>
                <field name="company_id"/>
                <field name="state"/>
            </tree>
        </field>
    </record>

    <record id="action_stock_valuation_reduction" model="ir.actions.act_window">
        <field name="name">Stock Valuation Reduction</field>
        <field name="res_model">stock.valuation.reduction</field>
        <field name="view_mode">tree,form</field>
    </record>
</odoo>