# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * hr_holidays_gantt
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.saas~18+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-01-17 10:16+0000\n"
"PO-Revision-Date: 2017-09-20 11:33+0000\n"
"Language-Team: <PERSON><PERSON><PERSON> (https://www.transifex.com/odoo/teams/41243/kab/)\n"
"Language: kab\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: hr_holidays_gantt
#. odoo-python
#: code:addons/hr_holidays_gantt/models/hr_leave.py:0
msgid " and"
msgstr ""

#. module: hr_holidays_gantt
#. odoo-python
#: code:addons/hr_holidays_gantt/models/hr_leave.py:0
msgid "%(employee)s %(time_off_type)s%(period_leaves)s. \n"
msgstr ""

#. module: hr_holidays_gantt
#. odoo-python
#: code:addons/hr_holidays_gantt/models/hr_leave.py:0
msgid "%(prefix)s from the %(dfrom)s to the %(dto)s"
msgstr ""

#. module: hr_holidays_gantt
#. odoo-python
#: code:addons/hr_holidays_gantt/models/hr_leave.py:0
msgid "%(prefix)s from the %(dfrom_date)s at %(dfrom)s to the %(dto_date)s at %(dto)s"
msgstr ""

#. module: hr_holidays_gantt
#: model_terms:ir.ui.view,arch_db:hr_holidays_gantt.hr_leave_report_calendar_view_gantt
msgid "<i class=\"fa fa-long-arrow-right\" title=\"Arrow\"/>"
msgstr ""

#. module: hr_holidays_gantt
#: model_terms:ir.ui.view,arch_db:hr_holidays_gantt.hr_leave_allocation_gantt_view
#: model_terms:ir.ui.view,arch_db:hr_holidays_gantt.hr_leave_gantt_view
msgid "Days"
msgstr ""

#. module: hr_holidays_gantt
#: model:ir.model,name:hr_holidays_gantt.model_hr_leave
#: model_terms:ir.ui.view,arch_db:hr_holidays_gantt.hr_leave_report_calendar_view_gantt
msgid "Time Off"
msgstr ""

#. module: hr_holidays_gantt
#: model:ir.model,name:hr_holidays_gantt.model_hr_leave_report_calendar
msgid "Time Off Calendar"
msgstr ""

#. module: hr_holidays_gantt
#. odoo-python
#: code:addons/hr_holidays_gantt/models/hr_leave.py:0
msgid "has requested time off"
msgstr ""

#. module: hr_holidays_gantt
#. odoo-python
#: code:addons/hr_holidays_gantt/models/hr_leave.py:0
msgid "is on time off"
msgstr ""
