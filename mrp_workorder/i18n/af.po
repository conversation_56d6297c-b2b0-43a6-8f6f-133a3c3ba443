# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* mrp_workorder
#
# Translators:
# <PERSON>, 2022
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-05-16 16:01+0000\n"
"PO-Revision-Date: 2022-09-22 05:47+0000\n"
"Last-Translator: <PERSON>, 2022\n"
"Language-Team: Afrikaans (https://www.transifex.com/odoo/teams/41243/af/)\n"
"Language: af\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: mrp_workorder
#. odoo-python
#: code:addons/mrp_workorder/wizard/propose_change.py:0
msgid "%(user_name)s suggests to delete this instruction"
msgstr ""

#. module: mrp_workorder
#. odoo-python
#: code:addons/mrp_workorder/wizard/propose_change.py:0
msgid "%(user_name)s suggests to use this document as instruction"
msgstr ""

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.res_config_settings_view_form
msgid "<i class=\"fa fa-print\"/> Print barcode commands"
msgstr ""

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workorder_operation_form_view
msgid "<span class=\"o_stat_text\">Instructions</span>"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_bom__activity_ids
msgid "Activities"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_bom__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_bom__activity_state
msgid "Activity State"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_bom__activity_type_icon
msgid "Activity Type Icon"
msgstr ""

#. module: mrp_workorder
#. odoo-python
#: code:addons/mrp_workorder/models/mrp_workorder.py:0
#: model_terms:ir.ui.view,arch_db:mrp_workorder.view_mrp_workorder_additional_product_wizard
msgid "Add By-Product"
msgstr ""

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/components/menuPopup.xml:0
msgid "Add By-product"
msgstr ""

#. module: mrp_workorder
#. odoo-python
#. odoo-javascript
#: code:addons/mrp_workorder/models/mrp_workorder.py:0
#: code:addons/mrp_workorder/static/src/components/menuPopup.xml:0
#: model_terms:ir.ui.view,arch_db:mrp_workorder.view_mrp_workorder_additional_product_wizard
msgid "Add Component"
msgstr ""

#. module: mrp_workorder
#. odoo-python
#. odoo-javascript
#: code:addons/mrp_workorder/models/mrp_workorder.py:0
#: code:addons/mrp_workorder/static/src/components/menuPopup.xml:0
msgid "Add a Step"
msgstr ""

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.view_mrp_workorder_additional_product_wizard
msgid "Add product"
msgstr ""

#. module: mrp_workorder
#: model:ir.model,name:mrp_workorder.model_mrp_workorder_additional_product
msgid "Additional Product"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__allow_producing_quantity_change
msgid "Allow Changes to Producing Quantity"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_point_test_type__allow_registration
msgid "Allow Registration"
msgstr ""

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.view_routing_steps_search
msgid "Archived"
msgstr ""

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_production_tree_view_planning
msgid "Availability"
msgstr ""

#. module: mrp_workorder
#: model:ir.model,name:mrp_workorder.model_report_mrp_report_bom_structure
msgid "BOM Overview Report"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder___barcode_scanned
msgid "Barcode Scanned"
msgstr ""

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/components/summary_step.xml:0
msgid "Best Time! Congratulations!"
msgstr ""

#. module: mrp_workorder
#: model:ir.model,name:mrp_workorder.model_mrp_bom
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_point__bom_id
msgid "Bill of Material"
msgstr ""

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/components/menuPopup.xml:0
msgid "Block"
msgstr ""

#. module: mrp_workorder
#. odoo-python
#: code:addons/mrp_workorder/models/quality.py:0
#: code:addons/mrp_workorder/wizard/propose_change.py:0
msgid "BoM feedback %s (%s)"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_point__bom_product_ids
msgid "Bom Product"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields.selection,name:mrp_workorder.selection__mrp_workorder_additional_product__type__byproduct
msgid "By-Product"
msgstr ""

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workorder_view_form_tablet
msgid "CONTINUE"
msgstr ""

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.quality_check_view_form_tablet
msgid "CONTINUE PRODUCTION"
msgstr ""

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/components/menuPopup.xml:0
msgid "Cancel"
msgstr "Gekanselleer"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder_additional_product__product_uom_category_id
msgid "Category"
msgstr ""

#. module: mrp_workorder
#: model:ir.model,name:mrp_workorder.model_change_production_qty
msgid "Change Production Qty"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__check_ids
#: model:ir.model.fields,field_description:mrp_workorder.field_stock_move_line__quality_check_ids
msgid "Check"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_production__check_ids
msgid "Checks"
msgstr ""

#. module: mrp_workorder
#: model:product.attribute,name:mrp_workorder.product_attribute_color_radio
msgid "Color"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_propose_change__comment
msgid "Comment"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder_additional_product__company_id
msgid "Company"
msgstr "Maatskappy"

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/components/summary_step.xml:0
msgid "Completion Time:"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_check__component_id
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_point__component_ids
#: model:ir.model.fields.selection,name:mrp_workorder.selection__mrp_workorder_additional_product__type__component
msgid "Component"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_check__component_qty_to_do
msgid "Component Qty To Do"
msgstr ""

#. module: mrp_workorder
#: model:ir.model,name:mrp_workorder.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_check__consumption
msgid "Consumption"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,help:mrp_workorder.field_mrp_workorder_additional_product__product_uom_category_id
msgid "Conversion between Units of Measure can only occur if they belong to the same category. The conversion will be made based on the ratios."
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder_additional_product__create_uid
#: model:ir.model.fields,field_description:mrp_workorder.field_propose_change__create_uid
msgid "Created by"
msgstr "Geskep deur"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder_additional_product__create_date
#: model:ir.model.fields,field_description:mrp_workorder.field_propose_change__create_date
msgid "Created on"
msgstr "Geskep op"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workorder_view_form_tablet
msgid "Creates a new serial/lot number"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__current_quality_check_id
msgid "Current Quality Check"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields.selection,name:mrp_workorder.selection__quality_point__source_document__step
msgid "Custom"
msgstr ""

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workorder_view_form_inherit_workorder
msgid "Date"
msgstr "Datum"

#. module: mrp_workorder
#: model:ir.model.fields,help:mrp_workorder.field_quality_point__test_type_id
msgid "Defines the type of the quality control point."
msgstr ""

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/components/menuPopup.xml:0
msgid "Delete this Step"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_check__is_deleted
msgid "Deleted in production"
msgstr ""

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.add_quality_check_from_tablet
#: model_terms:ir.ui.view,arch_db:mrp_workorder.view_mrp_workorder_additional_product_wizard
#: model_terms:ir.ui.view,arch_db:mrp_workorder.view_propose_change_wizard
msgid "Discard"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder_additional_product__display_name
#: model:ir.model.fields,field_description:mrp_workorder.field_propose_change__display_name
msgid "Display Name"
msgstr "Vertoningsnaam"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.add_quality_check_from_tablet
msgid "Document"
msgstr "Dokument"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__qty_done
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_check__qty_done
msgid "Done"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,help:mrp_workorder.field_mrp_workorder_additional_product__product_tracking
#: model:ir.model.fields,help:mrp_workorder.field_quality_check__component_tracking
msgid "Ensure the traceability of a storable product in your warehouse."
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_check__finished_lot_id
msgid "Finished Lot/Serial"
msgstr ""

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workorder_view_form_inherit_workorder
msgid "Finished Lot/Serial Number"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__finished_product_check_ids
msgid "Finished Product Check"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_check__finished_product_sequence
msgid "Finished Product Sequence Number"
msgstr ""

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workorder_view_form_inherit_workorder
msgid "Finished Steps"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,help:mrp_workorder.field_mrp_bom__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr ""

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/components/summary_step.xml:0
msgid "Good Job!"
msgstr ""

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.quality_point_view_form_inherit_mrp
msgid "Google Slide Link"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_point__worksheet_url
msgid "Google doc URL"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder_additional_product__id
#: model:ir.model.fields,field_description:mrp_workorder.field_propose_change__id
msgid "ID"
msgstr "ID"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_bom__activity_exception_icon
msgid "Icon"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,help:mrp_workorder.field_mrp_bom__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_check__worksheet_document
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_point__worksheet_document
msgid "Image/PDF"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,help:mrp_workorder.field_mrp_workorder__move_line_id
#: model:ir.model.fields,help:mrp_workorder.field_quality_check__move_line_id
msgid "In case of Quality Check by Quantity, Move Line on which the Quality Check applies"
msgstr ""

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.add_quality_check_from_tablet
msgid "Instruction"
msgstr ""

#. module: mrp_workorder
#. odoo-python
#: code:addons/mrp_workorder/models/quality.py:0
msgid "Instruction:"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_routing_workcenter__quality_point_count
msgid "Instructions"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,help:mrp_workorder.field_mrp_workorder__move_line_ids
msgid "Inventory moves for which you must scan a lot number at this work order"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_check__component_tracking
msgid "Is Component Tracked"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__is_last_unfinished_wo
msgid "Is Last Work Order To Process"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__is_last_lot
msgid "Is Last lot"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__is_first_started_wo
msgid "Is The first Work Order"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_point__is_workorder_step
msgid "Is Workorder Step"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_check__is_user_working
msgid "Is the Current User Working"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder_additional_product__write_uid
#: model:ir.model.fields,field_description:mrp_workorder.field_propose_change__write_uid
msgid "Last Updated by"
msgstr "Laas Opgedateer deur"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder_additional_product__write_date
#: model:ir.model.fields,field_description:mrp_workorder.field_propose_change__write_date
msgid "Last Updated on"
msgstr "Laas Opgedateer op"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workorder_view_form_tablet
#: model_terms:ir.ui.view,arch_db:mrp_workorder.quality_check_view_form_tablet
msgid "Lot"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__lot_id
msgid "Lot/Serial"
msgstr ""

#. module: mrp_workorder
#: model:res.groups,name:mrp_workorder.group_mrp_wo_tablet_timer
msgid "Manage Work Order timer on Tablet View"
msgstr ""

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_production_tree_view_planning
msgid "Manufacturing Orders"
msgstr ""

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workorder_view_form_tablet
msgid "Mark as Done"
msgstr ""

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workorder_view_form_tablet
msgid "Mark as Done and Close MO"
msgstr ""

#. module: mrp_workorder
#. odoo-python
#: code:addons/mrp_workorder/models/mrp_workorder.py:0
msgid "Menu"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__move_line_ids
msgid "Moves to Track"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_bom__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_propose_change__note
msgid "New Instruction"
msgstr ""

#. module: mrp_workorder
#. odoo-python
#: code:addons/mrp_workorder/wizard/propose_change.py:0
msgid "New Instruction suggested by %(user_name)s"
msgstr ""

#. module: mrp_workorder
#. odoo-python
#: code:addons/mrp_workorder/models/quality.py:0
msgid "New Step suggested by %(user_name)s"
msgstr ""

#. module: mrp_workorder
#. odoo-python
#: code:addons/mrp_workorder/wizard/propose_change.py:0
msgid "New Title suggested:"
msgstr ""

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.quality_check_view_form_tablet
msgid "Next"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_bom__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_bom__activity_date_deadline
msgid "Next Activity Deadline"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_bom__activity_summary
msgid "Next Activity Summary"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_bom__activity_type_id
msgid "Next Activity Type"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_check__next_check_id
msgid "Next Check"
msgstr ""

#. module: mrp_workorder
#: model_terms:ir.actions.act_window,help:mrp_workorder.action_mrp_workorder_show_steps
msgid "No manufacturing steps defined yet!"
msgstr ""

#. module: mrp_workorder
#: model_terms:ir.actions.act_window,help:mrp_workorder.mrp_workorder_action_tablet
msgid "No work orders to do!"
msgstr ""

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_production_workorder_tree_editable_view_inherit_workorder
msgid "Open Tablet View"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_alert__workorder_id
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_check__workorder_id
msgid "Operation"
msgstr ""

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workorder_view_form_inherit_workorder
msgid "Operator"
msgstr ""

#. module: mrp_workorder
#: model:ir.ui.menu,name:mrp_workorder.menu_mrp_dashboard
msgid "Overview"
msgstr ""

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workorder_view_form_tablet
msgid "PAUSE"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields.selection,name:mrp_workorder.selection__quality_point__test_report_type__pdf
msgid "PDF"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__picture
#: model:ir.model.fields,field_description:mrp_workorder.field_propose_change__picture
msgid "Picture"
msgstr ""

#. module: mrp_workorder
#: model:ir.ui.menu,name:mrp_workorder.menu_mrp_workorder_production
msgid "Planning by Production"
msgstr ""

#. module: mrp_workorder
#: model:ir.ui.menu,name:mrp_workorder.menu_mrp_workorder_workcenter
msgid "Planning by Workcenter"
msgstr ""

#. module: mrp_workorder
#. odoo-python
#: code:addons/mrp_workorder/models/quality.py:0
msgid "Please ensure the quantity to produce is greater than 0."
msgstr ""

#. module: mrp_workorder
#. odoo-python
#: code:addons/mrp_workorder/models/quality.py:0
msgid "Please enter a Lot/SN."
msgstr ""

#. module: mrp_workorder
#. odoo-python
#: code:addons/mrp_workorder/models/quality.py:0
msgid "Please enter a positive quantity."
msgstr ""

#. module: mrp_workorder
#. odoo-python
#: code:addons/mrp_workorder/models/mrp_workorder.py:0
msgid "Please set the quantity you are currently producing. It should be different from zero."
msgstr ""

#. module: mrp_workorder
#. odoo-python
#: code:addons/mrp_workorder/models/quality.py:0
msgid "Please upload a picture."
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_check__previous_check_id
msgid "Previous Check"
msgstr ""

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.quality_check_view_form_tablet
msgid "Print Labels"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder_additional_product__product_id
msgid "Product"
msgstr ""

#. module: mrp_workorder
#: model:ir.model,name:mrp_workorder.model_stock_move_line
msgid "Product Moves (Stock Move Line)"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_point__component_id
msgid "Product To Register"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder_additional_product__product_uom_id
msgid "Product Uom"
msgstr ""

#. module: mrp_workorder
#: model:ir.model,name:mrp_workorder.model_mrp_production
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_alert__production_id
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_check__production_id
msgid "Production Order"
msgstr ""

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workorder_view_form_tablet
msgid "Production Workcenter"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_point__product_ids
msgid "Products"
msgstr ""

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.add_quality_check_from_tablet
#: model_terms:ir.ui.view,arch_db:mrp_workorder.view_propose_change_wizard
msgid "Propose Change"
msgstr ""

#. module: mrp_workorder
#: model:ir.model,name:mrp_workorder.model_propose_change
msgid "Propose a change in the production"
msgstr ""

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/components/summary_step.xml:0
msgid "Quality"
msgstr ""

#. module: mrp_workorder
#: model:ir.model,name:mrp_workorder.model_quality_alert
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__quality_alert_ids
msgid "Quality Alert"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__quality_alert_count
msgid "Quality Alert Count"
msgstr ""

#. module: mrp_workorder
#: model:ir.model,name:mrp_workorder.model_quality_check
msgid "Quality Check"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__quality_check_fail
msgid "Quality Check Fail"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__quality_check_todo
msgid "Quality Check Todo"
msgstr ""

#. module: mrp_workorder
#: model:ir.model,name:mrp_workorder.model_quality_point
msgid "Quality Control Point"
msgstr ""

#. module: mrp_workorder
#: model:ir.model,name:mrp_workorder.model_quality_point_test_type
msgid "Quality Control Test Type"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_routing_workcenter__quality_point_ids
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__quality_point_ids
msgid "Quality Point"
msgstr ""

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.view_routing_steps_search
msgid "Quality Point Steps"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,help:mrp_workorder.field_quality_point__product_ids
msgid "Quality Point will apply to every selected Products."
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__quality_state
msgid "Quality State"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder_additional_product__product_qty
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_production_tree_view_planning
#: model_terms:ir.ui.view,arch_db:mrp_workorder.quality_check_view_form_tablet
#: model_terms:ir.ui.view,arch_db:mrp_workorder.view_mrp_workorder_additional_product_wizard
msgid "Quantity"
msgstr "Hoeveelheid"

#. module: mrp_workorder
#. odoo-python
#: code:addons/mrp_workorder/wizard/propose_change.py:0
msgid "Reason:"
msgstr ""

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workorder_view_form_tablet
msgid "Record production"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__additional
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_check__additional
msgid "Register additional product"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_point__bom_active
msgid "Related Bill of Material Active"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_check__component_remaining_qty
msgid "Remaining Quantity for Component"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields.selection,name:mrp_workorder.selection__propose_change__change_type__remove_step
msgid "Remove Current Step"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_point__test_report_type
msgid "Report Type"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__user_id
msgid "Responsible"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_bom__activity_user_id
msgid "Responsible User"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_check__result
msgid "Result"
msgstr ""

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/components/menuPopup.xml:0
msgid "Scrap"
msgstr ""

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workorder_view_form_tablet
#: model_terms:ir.ui.view,arch_db:mrp_workorder.quality_check_view_form_tablet
msgid "Serial"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields.selection,name:mrp_workorder.selection__propose_change__change_type__set_picture
msgid "Set Picture"
msgstr ""

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/components/menuPopup.xml:0
msgid "Set a New picture"
msgstr ""

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_routing_workcenter_tree_view_inherited
msgid "Show Instructions"
msgstr ""

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.res_config_settings_view_form
msgid "Show the timer on the work order screen"
msgstr ""

#. module: mrp_workorder
#: model:product.template,description_sale:mrp_workorder.product_template_stool
msgid "Small wooden stool"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields.selection,name:mrp_workorder.selection__quality_point__source_document__operation
msgid "Specific Page of Operation Worksheet"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,help:mrp_workorder.field_mrp_bom__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_point__operation_id
msgid "Step"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_point__source_document
msgid "Step Document"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_propose_change__step_id
msgid "Step to change"
msgstr ""

#. module: mrp_workorder
#: model:ir.actions.act_window,name:mrp_workorder.action_mrp_workorder_show_steps
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__quality_point_count
msgid "Steps"
msgstr ""

#. module: mrp_workorder
#: model:ir.model,name:mrp_workorder.model_stock_move
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__move_id
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_check__move_id
msgid "Stock Move"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__move_line_id
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_check__move_line_id
msgid "Stock Move Line"
msgstr ""

#. module: mrp_workorder
#: model:product.template,name:mrp_workorder.product_template_stool
msgid "Stool"
msgstr ""

#. module: mrp_workorder
#: model:product.template,name:mrp_workorder.product_template_stool_foot
msgid "Stool Foot"
msgstr ""

#. module: mrp_workorder
#: model:product.template,name:mrp_workorder.product_template_stool_top
msgid "Stool Top"
msgstr ""

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/components/menuPopup.xml:0
msgid "Suggest a Worksheet improvement"
msgstr ""

#. module: mrp_workorder
#: model:ir.actions.client,name:mrp_workorder.tablet_client_action
msgid "Tablet Client Action"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__test_type
msgid "Technical name"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__test_type_id
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_point__test_type_id
msgid "Test Type"
msgstr ""

#. module: mrp_workorder
#. odoo-python
#: code:addons/mrp_workorder/models/mrp_workorder.py:0
msgid "This workcenter isn't expected to have open workorders during this period. Work hours :"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_res_config_settings__group_mrp_wo_tablet_timer
msgid "Timer"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_check__title
#: model_terms:ir.ui.view,arch_db:mrp_workorder.add_quality_check_from_tablet
msgid "Title"
msgstr "Titel"

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_production_tree_view_planning
msgid "Total Qty"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder_additional_product__product_tracking
msgid "Tracking"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder_additional_product__type
#: model_terms:ir.ui.view,arch_db:mrp_workorder.quality_check_workorder_form
msgid "Type"
msgstr "Soort"

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_propose_change__change_type
msgid "Type of Change"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,help:mrp_workorder.field_mrp_bom__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr ""

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workorder_view_form_tablet
msgid "Unblock"
msgstr ""

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_production_tree_view_planning
msgid "Unit of Measure"
msgstr "Maateenheid"

#. module: mrp_workorder
#: model:ir.actions.server,name:mrp_workorder.production_order_unplan_server_action
msgid "Unplan orders"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_check__component_uom_id
msgid "UoM"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields.selection,name:mrp_workorder.selection__propose_change__change_type__update_step
msgid "Update Current Step"
msgstr ""

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/components/menuPopup.xml:0
msgid "Update Instructions"
msgstr ""

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.quality_point_view_form_inherit_mrp
msgid "Upload your PDF file."
msgstr ""

#. module: mrp_workorder
#: model_terms:ir.actions.act_window,help:mrp_workorder.action_mrp_workorder_show_steps
msgid "Use steps to show instructions on a worksheet to operators, or trigger quality checks at specific steps of the work order."
msgstr ""

#. module: mrp_workorder
#: model_terms:ir.actions.act_window,help:mrp_workorder.mrp_workorder_action_tablet
msgid ""
"Use the table work center control panel to register operations in the shop floor directly.\n"
"            The tablet provides worksheets for your workers and allow them to scrap products, track time,\n"
"            launch a maintenance request, perform quality tests, etc."
msgstr ""

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.quality_check_view_form_tablet
msgid "VALIDATE"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,help:mrp_workorder.field_mrp_workorder___barcode_scanned
msgid "Value of the last barcode scanned."
msgstr ""

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/components/summary_step.xml:0
msgid "Well done, you're in the Top 10!"
msgstr ""

#. module: mrp_workorder
#: model:ir.model,name:mrp_workorder.model_mrp_workcenter
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_alert__workcenter_id
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_check__workcenter_id
#: model_terms:ir.ui.view,arch_db:mrp_workorder.quality_alert_view_search_inherit_mrp_workorder
msgid "Work Center"
msgstr ""

#. module: mrp_workorder
#: model:ir.model,name:mrp_workorder.model_mrp_routing_workcenter
msgid "Work Center Usage"
msgstr ""

#. module: mrp_workorder
#: model:ir.model,name:mrp_workorder.model_mrp_workorder
msgid "Work Order"
msgstr ""

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.quality_point_view_form_inherit_mrp
#: model_terms:ir.ui.view,arch_db:mrp_workorder.quality_point_view_tree
msgid "Work Order Operation"
msgstr ""

#. module: mrp_workorder
#: model:ir.actions.act_window,name:mrp_workorder.mrp_workorder_action_tablet
#: model:ir.ui.menu,name:mrp_workorder.mrp_workorder_menu_planning
msgid "Work Orders"
msgstr ""

#. module: mrp_workorder
#: model:ir.actions.server,name:mrp_workorder.action_mrp_workorder_dependencies_workcenter
msgid "Work Orders Planning"
msgstr ""

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workcenter_view_kanban_inherit_workorder
msgid "Work orders"
msgstr ""

#. module: mrp_workorder
#: model_terms:ir.actions.act_window,help:mrp_workorder.mrp_workorder_action_tablet
msgid ""
"Work orders are operations to do as part of a manufacturing order.\n"
"            Operations are defined in the bill of materials or added in the manufacturing order directly."
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_check__working_state
msgid "Workcenter Status"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder_additional_product__workorder_id
#: model:ir.model.fields,field_description:mrp_workorder.field_propose_change__workorder_id
msgid "Workorder"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_check__worksheet_page
#: model:ir.model.fields,field_description:mrp_workorder.field_quality_point__worksheet_page
msgid "Worksheet Page"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_mrp_workorder__worksheet_page
msgid "Worksheet page"
msgstr ""

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/components/summary_step.xml:0
msgid "Wow, you made the Top 5!"
msgstr ""

#. module: mrp_workorder
#. odoo-python
#: code:addons/mrp_workorder/wizard/change_production_qty.py:0
msgid "You cannot update the quantity to do of an ongoing manufacturing order for which quality checks have been performed."
msgstr ""

#. module: mrp_workorder
#. odoo-python
#: code:addons/mrp_workorder/models/quality.py:0
msgid "You did not set a lot/serial number for the final product"
msgstr ""

#. module: mrp_workorder
#. odoo-python
#: code:addons/mrp_workorder/models/mrp_workorder.py:0
msgid "You should provide a lot/serial number for the final product"
msgstr ""

#. module: mrp_workorder
#. odoo-python
#: code:addons/mrp_workorder/models/mrp_workorder.py:0
msgid "You still need to do the quality checks!"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields.selection,name:mrp_workorder.selection__quality_point__test_report_type__zpl
msgid "ZPL"
msgstr ""

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workorder_view_form_tablet
msgid "back"
msgstr ""

#. module: mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp_workorder.mrp_workorder_view_form_tablet
msgid "menu"
msgstr ""

#. module: mrp_workorder
#. odoo-javascript
#: code:addons/mrp_workorder/static/src/components/summary_step.xml:0
msgid "minutes"
msgstr ""

#. module: mrp_workorder
#: model:ir.model.fields,field_description:mrp_workorder.field_propose_change__title
msgid "title"
msgstr ""

#. module: mrp_workorder
#: model:product.template,description_sale:mrp_workorder.product_template_stool_foot
msgid "wooden stool foot"
msgstr ""

#. module: mrp_workorder
#: model:product.template,description_sale:mrp_workorder.product_template_stool_top
msgid "wooden stool top"
msgstr ""
