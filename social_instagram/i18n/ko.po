# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* social_instagram
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON>, 2024
# <PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:28+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Sarah Park, 2024\n"
"Language-Team: Korean (https://app.transifex.com/odoo/teams/41243/ko/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ko\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: social_instagram
#: model_terms:ir.ui.view,arch_db:social_instagram.instagram_preview
msgid "(between 1.91:1 and 4:5)."
msgstr "(1.91:1에서 4:5 사이)"

#. module: social_instagram
#: model_terms:ir.ui.view,arch_db:social_instagram.instagram_preview
msgid "34 SECONDS AGO"
msgstr "34초 전"

#. module: social_instagram
#: model_terms:ir.ui.view,arch_db:social_instagram.social_stream_post_view_kanban
msgid "<i class=\"fa fa-comments me-1\" title=\"Comments\"/>"
msgstr "<i class=\"fa fa-comments me-1\" title=\"Comments\"/>"

#. module: social_instagram
#: model_terms:ir.ui.view,arch_db:social_instagram.instagram_preview
msgid ""
"<i class=\"fa fa-exclamation-triangle me-1\"/>\n"
"                At least one image is required when posting on Instagram."
msgstr ""
"<i class=\"fa fa-exclamation-triangle me-1\"/>\n"
"                인스타그램에 게시할 때는 최소 한 장 이상의 이미지가 필요합니다."

#. module: social_instagram
#: model_terms:ir.ui.view,arch_db:social_instagram.instagram_preview
msgid ""
"<i class=\"fa fa-exclamation-triangle me-1\"/>\n"
"                You can only post up to 10 images at once."
msgstr ""
"<i class=\"fa fa-exclamation-triangle me-1\"/>\n"
"                한 번에 최대 10개 이미지만 게시할 수 있습니다."

#. module: social_instagram
#: model_terms:ir.ui.view,arch_db:social_instagram.instagram_preview
msgid ""
"<i class=\"fa fa-exclamation-triangle me-1\"/>\n"
"                Your image appears to be corrupted, please try loading it again."
msgstr ""
"<i class=\"fa fa-exclamation-triangle me-1\"/>\n"
"                이미지가 손상된 것 같습니다. 다시 시도해 보세요."

#. module: social_instagram
#: model_terms:ir.ui.view,arch_db:social_instagram.social_stream_post_view_kanban
msgid "<i class=\"fa fa-heart me-1\" title=\"Likes\"/>"
msgstr "<i class=\"fa fa-heart me-1\" title=\"Likes\"/>"

#. module: social_instagram
#: model_terms:ir.ui.view,arch_db:social_instagram.instagram_preview
msgid "<span class=\"fw-bold pe-1\">My_instagram_page</span>"
msgstr "<span class=\"fw-bold pe-1\">My_instagram_page</span>"

#. module: social_instagram
#: model_terms:ir.ui.view,arch_db:social_instagram.instagram_preview
msgid "<span class=\"mt-1 fw-bold\">My Instagram Page</span>"
msgstr "<span class=\"mt-1 fw-bold\">내 인스타그램 페이지</span>"

#. module: social_instagram
#: model:ir.model.fields,field_description:social_instagram.field_social_post__instagram_access_token
#: model:ir.model.fields,field_description:social_instagram.field_social_post_template__instagram_access_token
msgid "Access Token"
msgstr "사용 권한 토큰"

#. module: social_instagram
#: model_terms:ir.ui.view,arch_db:social_instagram.res_config_settings_view_form
msgid "App ID"
msgstr "앱 ID"

#. module: social_instagram
#: model_terms:ir.ui.view,arch_db:social_instagram.res_config_settings_view_form
msgid "App Secret"
msgstr "앱 비밀번호"

#. module: social_instagram
#. odoo-python
#: code:addons/social_instagram/models/social_post.py:0
msgid "At least one image is required when posting on Instagram."
msgstr "인스타그램에 게시하려면 이미지가 하나 이상 필요합니다."

#. module: social_instagram
#. odoo-javascript
#: code:addons/social_instagram/static/src/xml/social_instagram_templates.xml:0
#: model_terms:ir.ui.view,arch_db:social_instagram.social_stream_post_view_kanban
msgid "Author Image"
msgstr "작성자 이미지"

#. module: social_instagram
#: model:ir.model.fields,help:social_instagram.field_res_config_settings__instagram_use_own_account
msgid ""
"Check this if you want to use your personal Instagram Developer Account "
"instead of the provided one."
msgstr "제공된 계정 대신 개인 인스타그램 개발자 계정을 사용하려면 이 옵션을 선택하세요."

#. module: social_instagram
#: model:ir.model,name:social_instagram.model_res_config_settings
msgid "Config Settings"
msgstr "환경 설정"

#. module: social_instagram
#. odoo-python
#: code:addons/social_instagram/controllers/main.py:0
msgid "Could not find any account to add."
msgstr "추가할 계정을 찾을 수 없습니다."

#. module: social_instagram
#: model:ir.model.fields,field_description:social_instagram.field_social_post__display_instagram_preview
#: model:ir.model.fields,field_description:social_instagram.field_social_post_template__display_instagram_preview
msgid "Display Instagram Preview"
msgstr "인스타그램 미리보기 표시"

#. module: social_instagram
#: model:ir.model.fields,help:social_instagram.field_social_account__instagram_facebook_account_id
msgid ""
"Facebook Account ID provided by the Facebook API, this should never be set manually.\n"
"        The Instagram (\"Professional\") account is always linked to a Facebook account."
msgstr ""
"페이스북 API에서 제공하는 페이스북 계정 ID는 수동으로 설정해서는 안됩니다.\n"
"        인스타그램 (\"프로페셔널\") 계정은 항상 페이스북 계정과 연동됩니다."

#. module: social_instagram
#: model:ir.model.fields,field_description:social_instagram.field_social_post__has_instagram_account
#: model:ir.model.fields,field_description:social_instagram.field_social_post_template__has_instagram_account
msgid "Has Instagram Account"
msgstr "인스타그램 계정 있음"

#. module: social_instagram
#: model_terms:ir.ui.view,arch_db:social_instagram.social_post_template_view_form
msgid "Images"
msgstr "이미지"

#. module: social_instagram
#: model:ir.model.fields.selection,name:social_instagram.selection__social_media__media_type__instagram
#: model:social.media,name:social_instagram.social_media_instagram
#: model_terms:ir.ui.view,arch_db:social_instagram.social_post_template_view_form
msgid "Instagram"
msgstr "인스타그램"

#. module: social_instagram
#: model:ir.model.fields,field_description:social_instagram.field_social_account__instagram_access_token
msgid "Instagram Access Token"
msgstr "인스타그램 액세스 토큰"

#. module: social_instagram
#: model:ir.model.fields,help:social_instagram.field_social_account__instagram_access_token
msgid ""
"Instagram Access Token provided by the Facebook API, this should never be set manually.\n"
"        It's used to authenticate requests when posting to or reading information from this account."
msgstr ""
"페이스북 API에서 제공하는 페이스북 페이지 액세스 토큰으로, 수동으로 설정해서는 안됩니다. \n"
"            이 계정에 게시물을 올리거나 정보를 읽을 때 요청을 인증하는  용도로 사용됩니다."

#. module: social_instagram
#: model:ir.model.fields,field_description:social_instagram.field_social_account__instagram_account_id
msgid "Instagram Account ID"
msgstr "인스타그램 계정 ID"

#. module: social_instagram
#: model:ir.model.fields,help:social_instagram.field_social_account__instagram_account_id
msgid ""
"Instagram Account ID provided by the Facebook API, this should never be set "
"manually."
msgstr "페이스북 API에서 제공하는 인스타그램 계정 ID는 수동으로 설정해서는 안됩니다."

#. module: social_instagram
#: model:ir.model.fields,field_description:social_instagram.field_res_config_settings__instagram_app_id
msgid "Instagram App ID"
msgstr "인스타그램 앱 ID"

#. module: social_instagram
#: model:ir.model.fields,field_description:social_instagram.field_res_config_settings__instagram_client_secret
msgid "Instagram App Secret"
msgstr "인스타그램 앱 비밀키"

#. module: social_instagram
#. odoo-javascript
#: code:addons/social_instagram/static/src/js/stream_post_kanban_record.js:0
#: model:ir.model.fields,field_description:social_instagram.field_social_stream_post__instagram_comments_count
msgid "Instagram Comments"
msgstr "인스타그램 댓글"

#. module: social_instagram
#: model_terms:ir.ui.view,arch_db:social_instagram.res_config_settings_view_form
msgid "Instagram Developer Account"
msgstr "인스타그램 개발자 계정"

#. module: social_instagram
#: model:ir.model.fields,field_description:social_instagram.field_social_account__instagram_facebook_account_id
msgid "Instagram Facebook Account ID"
msgstr "인스타그램 페이스북 계정 ID"

#. module: social_instagram
#: model:ir.model.fields,field_description:social_instagram.field_social_stream_post__instagram_facebook_author_id
msgid "Instagram Facebook Author ID"
msgstr "인스타그램 페이스북 작성자 ID"

#. module: social_instagram
#: model:ir.model.fields,field_description:social_instagram.field_social_post__instagram_image_ids
#: model:ir.model.fields,field_description:social_instagram.field_social_post_template__instagram_image_ids
msgid "Instagram Images"
msgstr "인스타그램 이미지"

#. module: social_instagram
#: model:ir.model.fields,field_description:social_instagram.field_social_stream_post__instagram_likes_count
msgid "Instagram Likes"
msgstr "인스타그램 좋아요"

#. module: social_instagram
#: model:ir.model.fields,field_description:social_instagram.field_social_post__instagram_message
#: model:ir.model.fields,field_description:social_instagram.field_social_post_template__instagram_message
msgid "Instagram Message"
msgstr "인스타그램 메시지"

#. module: social_instagram
#: model:ir.model.fields,field_description:social_instagram.field_social_live_post__instagram_post_id
#: model:ir.model.fields,field_description:social_instagram.field_social_stream_post__instagram_post_id
msgid "Instagram Post ID"
msgstr "인스타그램 게시물 ID"

#. module: social_instagram
#: model:ir.model.fields,field_description:social_instagram.field_social_stream_post__instagram_post_link
msgid "Instagram Post URL"
msgstr "인스타그램 게시물 URL"

#. module: social_instagram
#: model:social.stream.type,name:social_instagram.stream_type_instagram_posts
msgid "Instagram Posts"
msgstr "인스타그램 게시물"

#. module: social_instagram
#: model:ir.model.fields,field_description:social_instagram.field_social_post__instagram_preview
#: model:ir.model.fields,field_description:social_instagram.field_social_post_template__instagram_preview
msgid "Instagram Preview"
msgstr "인스타그램 미리보기"

#. module: social_instagram
#. odoo-python
#: code:addons/social_instagram/controllers/main.py:0
msgid "Instagram did not provide a valid access token."
msgstr "인스타그램에서 유효한 액세스 토큰을 제공하지 않았습니다."

#. module: social_instagram
#. odoo-javascript
#: code:addons/social_instagram/static/src/xml/social_instagram_templates.xml:0
msgid "Likes"
msgstr "좋아요"

#. module: social_instagram
#: model:ir.model.fields,field_description:social_instagram.field_social_media__media_type
msgid "Media Type"
msgstr "매체 유형"

#. module: social_instagram
#: model_terms:ir.ui.view,arch_db:social_instagram.social_post_template_view_form
msgid "Message"
msgstr "메시지"

#. module: social_instagram
#. odoo-python
#: code:addons/social_instagram/models/social_stream_post.py:0
msgid ""
"Please confirm that commenting is enabled for this post on the platform."
msgstr "플랫폼에 이 게시물에 대한 댓글 기능이 활성화되어 있는지 확인하세요."

#. module: social_instagram
#. odoo-python
#: code:addons/social_instagram/models/social_post_template.py:0
msgid ""
"Please specify either a Instagram Message or upload some Instagram Images."
msgstr "인스타그램 메시지를 지정하거나 이미지를 업로드하세요."

#. module: social_instagram
#: model_terms:ir.ui.view,arch_db:social_instagram.instagram_preview
msgid "Post Image"
msgstr "이미지 게시"

#. module: social_instagram
#. odoo-python
#: code:addons/social_instagram/controllers/main.py:0
msgid "Read More about Instagram Accounts"
msgstr "인스타그램 계정에 대해 자세히 알아보기"

#. module: social_instagram
#: model:ir.model,name:social_instagram.model_social_account
msgid "Social Account"
msgstr "소셜 계정"

#. module: social_instagram
#: model:ir.model,name:social_instagram.model_social_live_post
msgid "Social Live Post"
msgstr "소셜 실시간 게시물"

#. module: social_instagram
#: model:ir.model,name:social_instagram.model_social_media
msgid "Social Media"
msgstr "소셜미디어"

#. module: social_instagram
#: model:ir.model,name:social_instagram.model_social_post
msgid "Social Post"
msgstr "소셜 게시"

#. module: social_instagram
#: model:ir.model,name:social_instagram.model_social_post_template
msgid "Social Post Template"
msgstr "소셜 게시물 템플릿"

#. module: social_instagram
#: model:ir.model,name:social_instagram.model_social_stream
msgid "Social Stream"
msgstr "소셜 스트림"

#. module: social_instagram
#: model:ir.model,name:social_instagram.model_social_stream_post
msgid "Social Stream Post"
msgstr "소셜 스트림 게시물"

#. module: social_instagram
#: model:ir.model.fields,help:social_instagram.field_social_stream_post__instagram_facebook_author_id
msgid ""
"The Facebook ID of this Instagram post author, used to fetch the profile "
"picture."
msgstr "프로필 사진을 가져오는 데 사용되는 인스타그램 게시물 작성자의 페이스북 ID입니다."

#. module: social_instagram
#: model_terms:ir.ui.view,arch_db:social_instagram.instagram_preview
msgid "The following images are not in the correct format (jpg/jpeg)."
msgstr "다음 이미지는 올바른 형식(jpg/jpeg)이 아닙니다."

#. module: social_instagram
#. odoo-python
#: code:addons/social_instagram/models/social_post.py:0
msgid ""
"The following images are not in the correct format (jpg/jpeg).\n"
"\n"
"%(images)s"
msgstr ""
"다음 이미지는 올바른 형식 (jpg/jpeg)이 아닙니다.\n"
"\n"
"%(images)s"

#. module: social_instagram
#: model_terms:ir.ui.view,arch_db:social_instagram.instagram_preview
msgid "The following images do not meet the"
msgstr "다음 이미지는"

#. module: social_instagram
#. odoo-python
#: code:addons/social_instagram/models/social_post.py:0
msgid ""
"The following images do not meet the required aspect ratio (between 1.91:1 and 4:5).\n"
"\n"
"%(images)s"
msgstr ""
"다음 이미지의 가로 및 세로 비율이 필수 범위 (1.91:1 ~ 4:5 사이)에 있지 않습니다.\n"
"\n"
"%(images)s"

#. module: social_instagram
#. odoo-python
#: code:addons/social_instagram/controllers/main.py:0
msgid "There was a authentication issue during your request."
msgstr "요청하는 동안 인증에 문제가 발생했습니다."

#. module: social_instagram
#. odoo-python
#: code:addons/social_instagram/controllers/main.py:0
msgid "Unauthorized. Please contact your administrator."
msgstr "승인할 수 없습니다. 관리자에게 문의해 주세요."

#. module: social_instagram
#: model:ir.model.fields,field_description:social_instagram.field_res_config_settings__instagram_use_own_account
msgid "Use your own Instagram Account"
msgstr "나만의 인스타그램 계정 사용"

#. module: social_instagram
#: model:ir.model.fields,help:social_instagram.field_social_post__instagram_access_token
#: model:ir.model.fields,help:social_instagram.field_social_post_template__instagram_access_token
msgid "Used to allow access to Instagram to retrieve the post image"
msgstr "인스타그램에 액세스하여 게시물 이미지를 검색할 수 있도록 허용합니다."

#. module: social_instagram
#: model:ir.model.fields,help:social_instagram.field_social_media__media_type
msgid ""
"Used to make comparisons when we need to restrict some features to a "
"specific media ('facebook', 'x', ...)."
msgstr "일부 기능을 특정 미디어 ( 'facebook', 'x' 등)로 제한해야 할 때 비교하는 데 사용됩니다."

#. module: social_instagram
#: model:ir.model.fields,help:social_instagram.field_social_post__instagram_image_ids
#: model:ir.model.fields,help:social_instagram.field_social_post_template__instagram_image_ids
msgid "Will attach images to your posts."
msgstr "게시물에 이미지를 첨부합니다."

#. module: social_instagram
#. odoo-python
#: code:addons/social_instagram/models/social_post.py:0
msgid "You can only post up to 10 images at once."
msgstr "한 번에 최대 10개까지만 이미지를 게시할 수 있습니다."

#. module: social_instagram
#. odoo-python
#: code:addons/social_instagram/models/social_media.py:0
msgid "You don't have an active subscription. Please buy one here: %s"
msgstr "활성 구독이 없습니다. 여기에서 구입하십시오 : %s"

#. module: social_instagram
#. odoo-python
#: code:addons/social_instagram/controllers/main.py:0
msgid ""
"You need to link your Instagram page to your Facebook account to post with Odoo Social.\n"
" Please create one and make sure it is linked to your account."
msgstr ""
"Odoo 소셜에 게시하려면 인스타그램 페이지를 페이스북 계정에 연동시켜야 합니다.\n"
" 계정을 생성하고 연동이 완료되었는지 확인하십시오."

#. module: social_instagram
#. odoo-python
#: code:addons/social_instagram/models/social_post.py:0
msgid "Your image appears to be corrupted, please try loading it again."
msgstr "이미지가 손상되었습니다. 다시 불러오기를 시도해 보세요."

#. module: social_instagram
#: model_terms:ir.ui.view,arch_db:social_instagram.instagram_preview
msgid "required aspect ratio"
msgstr "필수 화면 비율"
