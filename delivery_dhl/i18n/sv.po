# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* delivery_dhl
# 
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON> <<EMAIL>>, 2024
# <PERSON> <<EMAIL>>, 2024
# <PERSON> <and<PERSON>.<EMAIL>>, 2024
# <PERSON>, 2024
# <PERSON><PERSON><PERSON> <mika<PERSON>.a<PERSON><PERSON>@mariaakerberg.com>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:25+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Swedish (https://app.transifex.com/odoo/teams/41243/sv/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sv\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: delivery_dhl
#: model_terms:ir.ui.view,arch_db:delivery_dhl.view_delivery_carrier_form_with_provider_dhl
msgid "'rate': {}, 'ship': {}, 'return': {}"
msgstr ""

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__0
msgid "0 - Logistics Services"
msgstr ""

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__1
msgid "1 - Domestic Express 12:00"
msgstr ""

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__2
msgid "2 - B2C"
msgstr ""

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__3
msgid "3 - B2C"
msgstr ""

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__4
msgid "4 - Jetline"
msgstr ""

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__5
msgid "5 - Sprintline"
msgstr ""

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__6
msgid "6 - Secureline"
msgstr ""

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_label_template__6x4_a4_pdf
msgid "6X4_A4_PDF"
msgstr ""

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_label_template__6x4_pdf
msgid "6X4_PDF"
msgstr "6X4_PDF"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_label_template__6x4_thermal
msgid "6X4_thermal"
msgstr ""

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__7
msgid "7 - Express Easy"
msgstr ""

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__8
msgid "8 - Express Easy"
msgstr ""

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_label_template__8x4_a4_pdf
msgid "8X4_A4_PDF"
msgstr ""

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_label_template__8x4_a4_tc_pdf
msgid "8X4_A4_TC_PDF"
msgstr ""

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_label_template__8x4_ci_pdf
msgid "8X4_CI_PDF"
msgstr ""

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_label_template__8x4_ci_thermal
msgid "8X4_CI_thermal"
msgstr ""

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_label_template__8x4_pdf
msgid "8X4_PDF"
msgstr ""

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_label_template__8x4_ru_a4_pdf
msgid "8X4_RU_A4_PDF"
msgstr ""

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_label_template__8x4_thermal
msgid "8X4_thermal"
msgstr ""

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__9
msgid "9 - Europack"
msgstr ""

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__a
msgid "A - Auto Reversals"
msgstr ""

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_region_code__am
msgid "America"
msgstr "Amerika"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_region_code__ap
msgid "Asia Pacific"
msgstr "Asien, stilla havet"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__b
msgid "B - Break Bulk Express"
msgstr ""

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__c
msgid "C - Medical Express"
msgstr ""

#. module: delivery_dhl
#: model:ir.model.fields,field_description:delivery_dhl.field_stock_package_type__package_carrier_type
msgid "Carrier"
msgstr "Bärare"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_package_dimension_unit__c
msgid "Centimeters"
msgstr "Centimeter"

#. module: delivery_dhl
#: model:ir.model.fields,help:delivery_dhl.field_delivery_carrier__dhl_dutiable
msgid "Check this if your package is dutiable."
msgstr "Kryssa i om ditt paket är tullpliktigt."

#. module: delivery_dhl
#: model_terms:ir.ui.view,arch_db:delivery_dhl.view_delivery_carrier_form_with_provider_dhl
msgid "Custom Data"
msgstr ""

#. module: delivery_dhl
#: model:ir.model.fields,field_description:delivery_dhl.field_delivery_carrier__dhl_custom_data_request
msgid "Custom data for DHL requests,"
msgstr ""

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__d
msgid "D - Express Worldwide"
msgstr "D - Express Worldwide"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__delivery_type__dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__stock_package_type__package_carrier_type__dhl
msgid "DHL"
msgstr "DHL"

#. module: delivery_dhl
#: model:ir.model.fields,field_description:delivery_dhl.field_delivery_carrier__dhl_account_number
msgid "DHL Account Number"
msgstr "DHL-kontonummer"

#. module: delivery_dhl
#: model_terms:ir.ui.view,arch_db:delivery_dhl.view_delivery_carrier_form_with_provider_dhl
msgid "DHL Configuration"
msgstr "DHL-konfiguration"

#. module: delivery_dhl
#. odoo-python
#: code:addons/delivery_dhl/models/delivery_dhl.py:0
msgid "DHL Documents"
msgstr ""

#. module: delivery_dhl
#: model:delivery.carrier,name:delivery_dhl.delivery_carrier_dhl_eu_dom
#: model:product.template,name:delivery_dhl.product_product_delivery_dhl_eu_dom_product_template
msgid "DHL EU"
msgstr ""

#. module: delivery_dhl
#: model:delivery.carrier,name:delivery_dhl.delivery_carrier_dhl_eu_intl
#: model:product.template,name:delivery_dhl.product_product_delivery_dhl_eu_intl_product_template
msgid "DHL EU -> International"
msgstr ""

#. module: delivery_dhl
#: model:ir.model.fields,field_description:delivery_dhl.field_delivery_carrier__dhl_default_package_type_id
msgid "DHL Package Type"
msgstr ""

#. module: delivery_dhl
#: model:ir.model.fields,field_description:delivery_dhl.field_delivery_carrier__dhl_password
msgid "DHL Password"
msgstr "DHL-lösenord"

#. module: delivery_dhl
#: model:ir.model.fields,field_description:delivery_dhl.field_delivery_carrier__dhl_product_code
msgid "DHL Product"
msgstr ""

#. module: delivery_dhl
#: model_terms:ir.ui.view,arch_db:delivery_dhl.res_config_settings_view_form_sale
#: model_terms:ir.ui.view,arch_db:delivery_dhl.res_config_settings_view_form_stock
msgid "DHL Shipping Methods"
msgstr "DHL leveranser"

#. module: delivery_dhl
#. odoo-python
#: code:addons/delivery_dhl/models/dhl_request.py:0
msgid "DHL Site ID is missing, please modify your delivery method settings."
msgstr ""
"DHL platsid saknas, vänligen ändra dina inställningar för leveransmetod"

#. module: delivery_dhl
#: model:ir.model.fields,field_description:delivery_dhl.field_delivery_carrier__dhl_SiteID
msgid "DHL SiteID"
msgstr "DHL Platsid"

#. module: delivery_dhl
#: model:delivery.carrier,name:delivery_dhl.delivery_carrier_dhl_us_dom
#: model:product.template,name:delivery_dhl.product_product_delivery_dhl_us_dom_product_template
msgid "DHL US"
msgstr ""

#. module: delivery_dhl
#: model:delivery.carrier,name:delivery_dhl.delivery_carrier_dhl_us_intl
#: model:product.template,name:delivery_dhl.product_product_delivery_dhl_us_intl_product_template
msgid "DHL US -> International"
msgstr ""

#. module: delivery_dhl
#. odoo-python
#: code:addons/delivery_dhl/models/dhl_request.py:0
msgid ""
"DHL account number is missing, please modify your delivery method settings."
msgstr ""
"DHL kontonummer saknas, vänligen  ändra inställningarna för leveransmetod"

#. module: delivery_dhl
#. odoo-python
#: code:addons/delivery_dhl/models/dhl_request.py:0
msgid "DHL doesn't support products with name greater than 75 characters."
msgstr ""

#. module: delivery_dhl
#. odoo-python
#: code:addons/delivery_dhl/models/dhl_request.py:0
msgid "DHL password is missing, please modify your delivery method settings."
msgstr ""
"DHL lösenord saknas, vänligen ändra dina inställningar för leveransmetod. "

#. module: delivery_dhl
#: model:ir.model.fields,field_description:delivery_dhl.field_delivery_carrier__dhl_duty_payment
msgid "Dhl Duty Payment"
msgstr ""

#. module: delivery_dhl
#: model:ir.model.fields,field_description:delivery_dhl.field_delivery_carrier__dhl_dutiable
msgid "Dutiable Material"
msgstr "Tullpliktig vara"

#. module: delivery_dhl
#: model_terms:ir.ui.view,arch_db:delivery_dhl.view_delivery_carrier_form_with_provider_dhl
msgid "Duties paid by"
msgstr ""

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__e
msgid "E - Express 9:00"
msgstr ""

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_label_image_format__epl2
msgid "EPL2"
msgstr ""

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_region_code__eu
msgid "Europe"
msgstr "Europa"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__f
msgid "F - Freight Worldwide"
msgstr ""

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__g
msgid "G - Domestic Economy Select"
msgstr ""

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__h
msgid "H - Economy Select"
msgstr ""

#. module: delivery_dhl
#. odoo-python
#: code:addons/delivery_dhl/models/delivery_dhl.py:0
msgid "Hint: The destination may not require the dutiable option."
msgstr ""

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__i
msgid "I - Break Bulk Economy"
msgstr ""

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_package_dimension_unit__i
msgid "Inches"
msgstr "Tum"

#. module: delivery_dhl
#. odoo-python
#: code:addons/delivery_dhl/models/delivery_dhl.py:0
msgid "Invalid syntax for DHL custom data."
msgstr ""

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__j
msgid "J - Jumbo Box"
msgstr ""

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__k
msgid "K - Express 9:00"
msgstr "K - Express 9:00"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_package_weight_unit__k
msgid "Kilograms"
msgstr "Kilogram"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__l
msgid "L - Express 10:30"
msgstr ""

#. module: delivery_dhl
#: model_terms:ir.ui.view,arch_db:delivery_dhl.view_delivery_carrier_form_with_provider_dhl
msgid "Label Format"
msgstr ""

#. module: delivery_dhl
#: model:ir.model.fields,field_description:delivery_dhl.field_delivery_carrier__dhl_label_image_format
msgid "Label Image Format"
msgstr ""

#. module: delivery_dhl
#: model:ir.model.fields,field_description:delivery_dhl.field_delivery_carrier__dhl_label_template
msgid "Label Template"
msgstr ""

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__m
msgid "M - Express 10:30"
msgstr ""

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__n
msgid "N - Domestic Express"
msgstr "N - Inrikes Express"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__o
msgid "O - DOM Express 10:30"
msgstr ""

#. module: delivery_dhl
#: model_terms:ir.ui.view,arch_db:delivery_dhl.view_delivery_carrier_form_with_provider_dhl
msgid "Options"
msgstr "Alternativ"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__p
msgid "P - Express Worldwide"
msgstr ""

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_label_image_format__pdf
msgid "PDF"
msgstr "PDF"

#. module: delivery_dhl
#: model:ir.model.fields,field_description:delivery_dhl.field_delivery_carrier__dhl_package_dimension_unit
msgid "Package Dimension Unit"
msgstr "Enhet för paketstorlek"

#. module: delivery_dhl
#: model:ir.model.fields,field_description:delivery_dhl.field_delivery_carrier__dhl_package_weight_unit
msgid "Package Weight Unit"
msgstr "Enhet för paketvikt"

#. module: delivery_dhl
#. odoo-python
#: code:addons/delivery_dhl/models/dhl_request.py:0
msgid ""
"Please define an incoterm in the associated sale order or set a default "
"incoterm for the company in the accounting's settings."
msgstr ""

#. module: delivery_dhl
#. odoo-python
#: code:addons/delivery_dhl/models/dhl_request.py:0
msgid "Please provide at least one item to ship."
msgstr "Vänligen ange minst ett objekt att frakta"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_package_weight_unit__l
msgid "Pounds"
msgstr "Pund"

#. module: delivery_dhl
#: model:ir.model.fields,field_description:delivery_dhl.field_delivery_carrier__delivery_type
msgid "Provider"
msgstr "Leverantör"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__q
msgid "Q - Medical Express"
msgstr ""

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__r
msgid "R - GlobalMail Business"
msgstr ""

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_duty_payment__r
msgid "Recipient"
msgstr "Mottagare"

#. module: delivery_dhl
#: model:ir.model.fields,field_description:delivery_dhl.field_delivery_carrier__dhl_region_code
msgid "Region"
msgstr "Region"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__s
msgid "S - Same Day"
msgstr ""

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_duty_payment__s
msgid "Sender"
msgstr "Sändare"

#. module: delivery_dhl
#. odoo-python
#: code:addons/delivery_dhl/models/delivery_dhl.py:0
msgid "Shipment created into DHL <br/> <b>Tracking Number: </b>%s"
msgstr ""

#. module: delivery_dhl
#: model:ir.model,name:delivery_dhl.model_delivery_carrier
msgid "Shipping Methods"
msgstr "Leveranssätt"

#. module: delivery_dhl
#: model:ir.model,name:delivery_dhl.model_stock_package_type
msgid "Stock package type"
msgstr "Lager Emballagetyp"

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__t
msgid "T - Express 12:00"
msgstr "T - Express 12:00"

#. module: delivery_dhl
#. odoo-python
#: code:addons/delivery_dhl/models/dhl_request.py:0
msgid ""
"The address of the customer is missing or wrong (Missing field(s) :\n"
" %s)"
msgstr ""

#. module: delivery_dhl
#. odoo-python
#: code:addons/delivery_dhl/models/dhl_request.py:0
msgid ""
"The address of your company warehouse is missing or wrong (Missing field(s) :\n"
" %s)"
msgstr ""
"Adressen för ditt företagslager saknas eller är fel. (saknade fält) : \n"
"%s)"

#. module: delivery_dhl
#: model:ir.model.fields,help:delivery_dhl.field_delivery_carrier__dhl_custom_data_request
msgid ""
"The custom data in DHL is organized like the inside of a json file.\n"
"        There are 3 possible keys: 'rate', 'ship', 'return', to which you can add your custom data.\n"
"        More info on https://xmlportal.dhl.com/"
msgstr ""

#. module: delivery_dhl
#. odoo-python
#: code:addons/delivery_dhl/models/dhl_request.py:0
msgid ""
"The estimated shipping price cannot be computed because the weight is missing for the following product(s): \n"
" %s"
msgstr ""

#. module: delivery_dhl
#. odoo-python
#: code:addons/delivery_dhl/models/delivery_dhl.py:0
msgid ""
"There is no price available for this shipping, you should rather try with "
"the DHL product %s"
msgstr ""

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__u
msgid "U - Express Worldwide"
msgstr ""

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__v
msgid "V - Europack"
msgstr ""

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__w
msgid "W - Economy Select"
msgstr ""

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__x
msgid "X - Express Envelope"
msgstr ""

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__y
msgid "Y - Express 12:00"
msgstr ""

#. module: delivery_dhl
#. odoo-python
#: code:addons/delivery_dhl/models/delivery_dhl.py:0
msgid "You can't cancel DHL shipping without pickup date."
msgstr "Det går inte att avbryta DHL-frakt utan hämtningsdatum."

#. module: delivery_dhl
#. odoo-python
#: code:addons/delivery_dhl/models/delivery_dhl.py:0
msgid "You cannot delete the commercial invoice sequence."
msgstr ""

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_product_code__z
msgid "Z - Destination Charges"
msgstr ""

#. module: delivery_dhl
#: model:ir.model.fields.selection,name:delivery_dhl.selection__delivery_carrier__dhl_label_image_format__zpl2
msgid "ZPL2"
msgstr ""
