<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record model="ir.ui.view" id="view_res_bank_form">
        <field name="name">res.bank.form</field>
        <field name="model">res.bank</field>
        <field name="inherit_id" ref="base.view_res_bank_form" />
        <field name="arch" type="xml">
            <field name="name" position="after">
                <field name="country_code" invisible="1" />
                <field name="l10n_jp_zengin_name_kana" invisible="country_code != 'JP'"/>
            </field>
            <field name="bic" position="after">
                <field name="l10n_jp_zengin_branch_name" invisible="country_code != 'JP'" />
                <field name="l10n_jp_zengin_branch_name_kana" invisible="country_code != 'JP'" />
                <field name="l10n_jp_zengin_branch_code" invisible="country_code != 'JP'" />
            </field>
        </field>
    </record>
</odoo>
