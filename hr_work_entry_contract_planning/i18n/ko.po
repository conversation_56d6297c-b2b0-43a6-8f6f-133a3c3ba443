# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_work_entry_contract_planning
# 
# Translators:
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:26+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Korean (https://app.transifex.com/odoo/teams/41243/ko/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ko\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: hr_work_entry_contract_planning
#: model:ir.model.fields,help:hr_work_entry_contract_planning.field_hr_contract__work_entry_source
msgid ""
"\n"
"        Defines the source for work entries generation\n"
"\n"
"        Working Schedule: Work entries will be generated from the working hours below.\n"
"        Attendances: Work entries will be generated from the employee's attendances. (requires Attendance app)\n"
"        Planning: Work entries will be generated from the employee's planning. (requires Planning app)\n"
"    "
msgstr ""
"\n"
"        근무 항목 생성에 대한 기준 정의\n"
"\n"
"        근무 일정: 아래의 근무 시간에서 근무 항목을 생성합니다.\n"
"        근태: 직원 근태에서 근무 항목을 생서합니다. (근태관리 앱 필요)\n"
"        일정 수립: 직원의 일정에서 근무 항목을 생성합니다. (일정 수립 앱 필요)\n"
"    "

#. module: hr_work_entry_contract_planning
#: model:ir.model,name:hr_work_entry_contract_planning.model_hr_contract
msgid "Employee Contract"
msgstr "근로 계약서"

#. module: hr_work_entry_contract_planning
#: model:ir.model,name:hr_work_entry_contract_planning.model_hr_work_entry
msgid "HR Work Entry"
msgstr "인사 근로 항목"

#. module: hr_work_entry_contract_planning
#: model:ir.model.fields.selection,name:hr_work_entry_contract_planning.selection__hr_contract__work_entry_source__planning
msgid "Planning"
msgstr "일정 관리"

#. module: hr_work_entry_contract_planning
#: model:ir.model,name:hr_work_entry_contract_planning.model_planning_slot
msgid "Planning Shift"
msgstr "교대 근무 계획"

#. module: hr_work_entry_contract_planning
#: model:ir.model.fields,field_description:hr_work_entry_contract_planning.field_hr_work_entry__planning_slot_id
msgid "Planning Slot"
msgstr "슬롯 계획"

#. module: hr_work_entry_contract_planning
#: model:ir.model,name:hr_work_entry_contract_planning.model_hr_work_entry_regeneration_wizard
msgid "Regenerate Employee Work Entries"
msgstr "직원 작업 항목 재생성"

#. module: hr_work_entry_contract_planning
#. odoo-python
#: code:addons/hr_work_entry_contract_planning/models/planning_slot.py:0
msgid ""
"This shift record is linked to a validated working entry. You can't delete "
"it."
msgstr "이 교대 근무 기록은 승인된 작업 항목과 연결되어 있습니다. 삭제할 수 없습니다."

#. module: hr_work_entry_contract_planning
#. odoo-python
#: code:addons/hr_work_entry_contract_planning/models/planning_slot.py:0
msgid ""
"This shift record is linked to a validated working entry. You can't modify "
"it."
msgstr "이 교대 근무 기록은 승인된 작업 항목과 연결되어 있습니다.  수정할 수 없습니다."

#. module: hr_work_entry_contract_planning
#: model:ir.model.fields,field_description:hr_work_entry_contract_planning.field_hr_contract__work_entry_source
msgid "Work Entry Source"
msgstr "작업 항목 원본"
