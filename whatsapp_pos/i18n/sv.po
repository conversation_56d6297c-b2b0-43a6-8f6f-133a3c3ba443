# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* whatsapp_pos
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2024
# Lasse L, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:29+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Lasse L, 2024\n"
"Language-Team: Swedish (https://app.transifex.com/odoo/teams/41243/sv/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sv\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: whatsapp_pos
#: model:ir.model,name:whatsapp_pos.model_res_config_settings
msgid "Config Settings"
msgstr "Inställningar"

#. module: whatsapp_pos
#. odoo-python
#: code:addons/whatsapp_pos/models/res_config_settings.py:0
msgid "Invoice Whatsapp template should have a phone field"
msgstr "Whatsapp-mallen för fakturor bör ha ett telefonfält"

#. module: whatsapp_pos
#: model:ir.model.fields,field_description:whatsapp_pos.field_pos_config__invoice_template_id
#: model:ir.model.fields,field_description:whatsapp_pos.field_res_config_settings__pos_invoice_template_id
msgid "Invoice template"
msgstr "Fakturamall"

#. module: whatsapp_pos
#: model:ir.model,name:whatsapp_pos.model_pos_config
msgid "Point of Sale Configuration"
msgstr "Kassakonfigurering"

#. module: whatsapp_pos
#: model:ir.model,name:whatsapp_pos.model_pos_order
msgid "Point of Sale Orders"
msgstr "Kassaorder"

#. module: whatsapp_pos
#. odoo-python
#: code:addons/whatsapp_pos/models/res_config_settings.py:0
msgid "Receipt Whatsapp template should have Image Header Type"
msgstr "Kvitto Whatsapp-mall bör ha bildhuvudtyp"

#. module: whatsapp_pos
#. odoo-python
#: code:addons/whatsapp_pos/models/res_config_settings.py:0
msgid "Receipt Whatsapp template should have a phone field"
msgstr "Kvitto Whatsapp-mall bör ha ett telefonfält"

#. module: whatsapp_pos
#: model:ir.model.fields,field_description:whatsapp_pos.field_pos_config__receipt_template_id
#: model:ir.model.fields,field_description:whatsapp_pos.field_res_config_settings__pos_receipt_template_id
msgid "Receipt template"
msgstr "Mall för kvitto"

#. module: whatsapp_pos
#: model:ir.model,name:whatsapp_pos.model_whatsapp_composer
msgid "Send WhatsApp Wizard"
msgstr ""

#. module: whatsapp_pos
#. odoo-python
#: code:addons/whatsapp_pos/models/pos_order.py:0
msgid "Send Whatsapp"
msgstr ""

#. module: whatsapp_pos
#: model_terms:ir.ui.view,arch_db:whatsapp_pos.res_config_settings_view_form
msgid "Send receipts Using WhatsApp"
msgstr "Skicka kvitton med WhatsApp"

#. module: whatsapp_pos
#: model:ir.model.fields,field_description:whatsapp_pos.field_pos_config__whatsapp_enabled
#: model:ir.model.fields,field_description:whatsapp_pos.field_res_config_settings__pos_whatsapp_enabled
msgid "WhatsApp Enabled"
msgstr "WhatsApp aktiverat"

#. module: whatsapp_pos
#: model:ir.model,name:whatsapp_pos.model_whatsapp_template
msgid "WhatsApp Template"
msgstr ""

#. module: whatsapp_pos
#. odoo-python
#: code:addons/whatsapp_pos/models/whatsapp_composer.py:0
msgid "WhatsApp messages triggered successfully!"
msgstr ""

#. module: whatsapp_pos
#: model_terms:ir.ui.view,arch_db:whatsapp_pos.view_pos_form_whatsapp_pos_inherit
msgid "Whatsapp"
msgstr ""
