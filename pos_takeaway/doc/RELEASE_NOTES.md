## Module <pos_takeaway>

#### 30.4.2025
#### Version 18.0.1.0.0
##### ADD
-Initial Commit for POS Restaurant Dine-in/TakeAway

Usage
-----

**Selecting Order Type in POS**

1. Start a new order in the POS interface
2. Add products to the order
3. Click on the "Order Type" button (initially shows as "Eat In")
4. Select the desired order type from the dialog:
   - Eat In: For customers dining in the restaurant
   - Take Out: For customers taking food away
   - Delivery: For orders that need to be delivered to customers
5. For delivery orders, if no customer is assigned, you will be prompted to select a customer

**Order Type in Backend**

The order type is visible in the POS order list view and can be filtered or grouped by:

1. Go to Point of Sale > Orders > Orders
2. The "Order Type" column shows the selected type with color coding
3. Use the search filters to filter by specific order types
4. In the form view, the order type can be changed (for draft orders)

**Technical Information**

The module implements the following technical features:

* Extends the POS order model with a selection field for order type
* Uses computed fields for backward compatibility
* Implements a custom dialog for order type selection in the POS frontend
* Integrates with Odoo's partner selection for delivery orders
* Uses color decorations in list views for visual distinction