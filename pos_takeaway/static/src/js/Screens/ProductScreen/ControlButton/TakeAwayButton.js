/** @odoo-module **/
import { Component, useRef } from "@odoo/owl";
import { usePos } from "@point_of_sale/app/store/pos_hook";
import { useService } from "@web/core/utils/hooks";
import { ControlButtons } from "@point_of_sale/app/screens/product_screen/control_buttons/control_buttons";
import { Dialog } from "@web/core/dialog/dialog";
import { patch } from "@web/core/utils/patch";

/**
 * Order Type Selection Dialog
 */
export class OrderTypeSelectionDialog extends Component {
    static template = 'OrderTypeSelectionDialog';
    static components = { Dialog };
    static props = {
        close: Function,
    };
    
    setup() {
        this.pos = usePos();
    }

    async selectOrderType(orderType) {
        const selectedOrder = this.pos.get_order();
        const originalOrderType = selectedOrder.order_type;
        
        // For delivery orders, ensure a customer is selected&& !selectedOrder.partner_id
        if (orderType === 'delivery') {
            // Close the current dialog first
            this.props.close();
            selectedOrder.order_type = orderType;
            
            // // Use the proper partner selection method in Odoo 18.0
            // const partner = await this.pos.selectPartner();
            
            // if (selectedOrder.partner_id) {
            //     // Customer was selected, proceed with delivery
            //     selectedOrder.order_type = orderType;
            //     selectedOrder.is_takeaway = true;
            //     selectedOrder.is_dine_in = false;
                
            //     if (this.pos.config.is_generate_token && !selectedOrder.generate_token) {
            //         this.pos.config.pos_token += 1;
            //         selectedOrder.generate_token = true;
            //     }
            // } else {
            //     // Customer selection was canceled, keep original order type
            //     selectedOrder.order_type = originalOrderType;
            //     selectedOrder.is_takeaway = originalOrderType !== 'eat_in';
            //     selectedOrder.is_dine_in = originalOrderType === 'eat_in';
            //     return;
            // }
        } else {
            // For non-delivery orders or delivery with customer already set
            selectedOrder.order_type = orderType;
            
            // Set the legacy fields for backward compatibility
            if (orderType === 'eat_in') {
                selectedOrder.is_takeaway = false;
                selectedOrder.is_dine_in = true;
                if (this.pos.config.is_generate_token && selectedOrder.generate_token) {
                    this.pos.config.pos_token -= 1;
                    selectedOrder.generate_token = false;
                }
            } else {
                selectedOrder.is_takeaway = true;
                selectedOrder.is_dine_in = false;
                if (this.pos.config.is_generate_token && !selectedOrder.generate_token) {
                    this.pos.config.pos_token += 1;
                    selectedOrder.generate_token = true;
                }
            }
            
            this.props.close();
        }
    }
}

// Patch the ControlButtons component to add our order type button functionality
patch(ControlButtons.prototype, {
    setup() {
        super.setup();
        this.pos = usePos();
        this.dialogService = useService("dialog");
        
        // Initialize order with default values if needed
        const order = this.pos.get_order();
        if (order) {
            order.order_type = order.order_type || 'eat_in';
            order.is_takeaway = order.order_type !== 'eat_in';
            order.is_dine_in = order.order_type === 'eat_in';
        }
    },
    
    async selectOrderType() {
        const selectedOrder = this.pos.get_order();
        if (selectedOrder.is_empty()) {
            return alert('Please add products first!');
        }
        
        this.dialogService.add(OrderTypeSelectionDialog);
    },
    
    get currentOrderType() {
        const order = this.pos.get_order();
        return order ? order.order_type : 'eat_in';
    },
    
    get isEatIn() {
        return this.currentOrderType === 'eat_in';
    },
    
    get isTakeOut() {
        return this.currentOrderType === 'take_out';
    },
    
    get isDelivery() {
        return this.currentOrderType === 'delivery';
    }
});
