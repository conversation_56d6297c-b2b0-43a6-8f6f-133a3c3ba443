# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* stock_barcode_mrp
# 
# Translators:
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-16 20:47+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON><PERSON>, 2025\n"
"Language-Team: Kurdish (https://app.transifex.com/odoo/teams/41243/ku/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ku\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: stock_barcode_mrp
#: model_terms:ir.ui.view,arch_db:stock_barcode_mrp.res_config_settings_view_form
msgid "<i class=\"fa fa-print\"/> Print barcode commands and operation types"
msgstr ""

#. module: stock_barcode_mrp
#: model_terms:ir.ui.view,arch_db:stock_barcode_mrp.res_config_settings_view_form
msgid "<i class=\"fa fa-print\"/> Print barcode demo sheet"
msgstr ""

#. module: stock_barcode_mrp
#. odoo-javascript
#: code:addons/stock_barcode_mrp/static/src/components/main.js:0
msgid "Add Component"
msgstr ""

#. module: stock_barcode_mrp
#: model_terms:ir.ui.view,arch_db:stock_barcode_mrp.view_picking_type_form
msgid "Allow full order validation"
msgstr ""

#. module: stock_barcode_mrp
#: model:ir.model.fields,help:stock_barcode_mrp.field_stock_move_line__pick_type_create_components_lots
msgid "Allow to create new lot/serial numbers for the components"
msgstr ""

#. module: stock_barcode_mrp
#: model_terms:ir.ui.view,arch_db:stock_barcode_mrp.view_picking_type_form
msgid "Allow user to produce all even if no components were scanned"
msgstr ""

#. module: stock_barcode_mrp
#. odoo-javascript
#: code:addons/stock_barcode_mrp/static/src/components/main.js:0
msgid "Are you sure you want to cancel this manufacturing order?"
msgstr ""

#. module: stock_barcode_mrp
#: model:ir.actions.client,name:stock_barcode_mrp.stock_barcode_mo_client_action
msgid "Barcode MO Client Action"
msgstr ""

#. module: stock_barcode_mrp
#: model_terms:ir.ui.view,arch_db:stock_barcode_mrp.res_config_settings_view_form
msgid "Barcode Nomenclature"
msgstr ""

#. module: stock_barcode_mrp
#. odoo-javascript
#: code:addons/stock_barcode_mrp/static/src/models/barcode_mrp_model.js:0
msgid "Cancel Manufacturing Order"
msgstr ""

#. module: stock_barcode_mrp
#. odoo-javascript
#: code:addons/stock_barcode_mrp/static/src/components/main.js:0
msgid "Cancel manufacturing order?"
msgstr ""

#. module: stock_barcode_mrp
#: model_terms:ir.ui.view,arch_db:stock_barcode_mrp.res_config_settings_view_form
msgid "Configure Product Barcodes"
msgstr ""

#. module: stock_barcode_mrp
#: model_terms:ir.ui.view,arch_db:stock_barcode_mrp.mrp_barcode_form
#: model_terms:ir.ui.view,arch_db:stock_barcode_mrp.mrp_product_selector
msgid "Confirm"
msgstr "دڵنیاکردنەوە"

#. module: stock_barcode_mrp
#: model:ir.model.fields,field_description:stock_barcode_mrp.field_stock_picking_type__count_mo_confirmed
msgid "Count Mo Confirmed"
msgstr ""

#. module: stock_barcode_mrp
#: model:ir.model.fields,field_description:stock_barcode_mrp.field_stock_move_line__pick_type_create_components_lots
msgid "Create New Lots/Serial Numbers for Components"
msgstr ""

#. module: stock_barcode_mrp
#: model_terms:ir.actions.act_window,help:stock_barcode_mrp.mrp_action_kanban
msgid "Create a new Manufacturing Order"
msgstr ""

#. module: stock_barcode_mrp
#: model_terms:ir.ui.view,arch_db:stock_barcode_mrp.mrp_product_selector
msgid "Destination Location"
msgstr ""

#. module: stock_barcode_mrp
#: model_terms:ir.ui.view,arch_db:stock_barcode_mrp.mrp_barcode_form
#: model_terms:ir.ui.view,arch_db:stock_barcode_mrp.mrp_product_selector
msgid "Discard"
msgstr "ڕەتکردنەوە"

#. module: stock_barcode_mrp
#: model:ir.model.fields,field_description:stock_barcode_mrp.field_mrp_production__is_completed
msgid "Is Completed"
msgstr ""

#. module: stock_barcode_mrp
#: model:ir.model.fields,field_description:stock_barcode_mrp.field_stock_move_line__manual_consumption
msgid "Manual Consumption"
msgstr ""

#. module: stock_barcode_mrp
#: model:ir.model,name:stock_barcode_mrp.model_mrp_production
msgid "Manufacturing Order"
msgstr ""

#. module: stock_barcode_mrp
#: model:ir.model.fields,field_description:stock_barcode_mrp.field_mrp_production__move_byproduct_line_ids
msgid "Move Byproduct Line"
msgstr ""

#. module: stock_barcode_mrp
#: model:ir.model.fields,field_description:stock_barcode_mrp.field_mrp_production__move_raw_line_ids
msgid "Move Raw Line"
msgstr ""

#. module: stock_barcode_mrp
#: model:ir.model.fields,field_description:stock_barcode_mrp.field_mrp_production__backorder_ids
msgid "Mrp Production"
msgstr ""

#. module: stock_barcode_mrp
#. odoo-javascript
#: code:addons/stock_barcode_mrp/static/src/models/barcode_mrp_model.js:0
msgid "New"
msgstr "نوێ"

#. module: stock_barcode_mrp
#. odoo-python
#: code:addons/stock_barcode_mrp/models/mrp_production.py:0
msgid "No Manufacturing Order ready for this %(barcode_type)s"
msgstr ""

#. module: stock_barcode_mrp
#. odoo-python
#: code:addons/stock_barcode_mrp/models/mrp_production.py:0
msgid "No product or order found for barcode %s"
msgstr ""

#. module: stock_barcode_mrp
#: model:ir.actions.act_window,name:stock_barcode_mrp.mrp_action_kanban
msgid "Operations"
msgstr ""

#. module: stock_barcode_mrp
#: model:ir.model,name:stock_barcode_mrp.model_stock_picking_type
msgid "Picking Type"
msgstr ""

#. module: stock_barcode_mrp
#. odoo-javascript
#: code:addons/stock_barcode_mrp/static/src/models/barcode_mrp_model.js:0
msgid "Print Finished Product Label (PDF)"
msgstr ""

#. module: stock_barcode_mrp
#. odoo-javascript
#: code:addons/stock_barcode_mrp/static/src/models/barcode_mrp_model.js:0
msgid "Print Finished Product Label (ZPL)"
msgstr ""

#. module: stock_barcode_mrp
#. odoo-javascript
#: code:addons/stock_barcode_mrp/static/src/models/barcode_mrp_model.js:0
msgid "Print Production Order"
msgstr ""

#. module: stock_barcode_mrp
#. odoo-javascript
#: code:addons/stock_barcode_mrp/static/src/models/barcode_mrp_model.js:0
msgid "Produce"
msgstr ""

#. module: stock_barcode_mrp
#. odoo-javascript
#: code:addons/stock_barcode_mrp/static/src/models/barcode_mrp_model.js:0
msgid "Produce All"
msgstr ""

#. module: stock_barcode_mrp
#. odoo-javascript
#: code:addons/stock_barcode_mrp/static/src/components/header.xml:0
msgid "Producing"
msgstr ""

#. module: stock_barcode_mrp
#: model_terms:ir.ui.view,arch_db:stock_barcode_mrp.mrp_product_selector
msgid "Product"
msgstr "بەرهەم"

#. module: stock_barcode_mrp
#: model:ir.model,name:stock_barcode_mrp.model_stock_move_line
msgid "Product Moves (Stock Move Line)"
msgstr ""

#. module: stock_barcode_mrp
#: model:ir.model,name:stock_barcode_mrp.model_product_product
msgid "Product Variant"
msgstr "جۆری بەرهەم"

#. module: stock_barcode_mrp
#. odoo-javascript
#: code:addons/stock_barcode_mrp/static/src/models/barcode_mrp_model.js:0
msgid "Product not Allowed"
msgstr ""

#. module: stock_barcode_mrp
#: model_terms:ir.ui.view,arch_db:stock_barcode_mrp.mrp_product_selector
msgid "Quantity"
msgstr "بڕ"

#. module: stock_barcode_mrp
#. odoo-javascript
#: code:addons/stock_barcode_mrp/static/src/models/barcode_mrp_model.js:0
msgid "Scan a component"
msgstr ""

#. module: stock_barcode_mrp
#. odoo-python
#: code:addons/stock_barcode_mrp/models/mrp_production.py:0
msgid "Scan a product to filter the orders."
msgstr ""

#. module: stock_barcode_mrp
#. odoo-javascript
#: code:addons/stock_barcode_mrp/static/src/kanban/stock_barcode_kanban_renderer.js:0
msgid ""
"Scan an %(bold_start)s order %(bold_end)s or a %(bold_start)s product "
"%(bold_end)s to filter your records"
msgstr ""

#. module: stock_barcode_mrp
#. odoo-javascript
#: code:addons/stock_barcode_mrp/static/src/models/barcode_mrp_model.js:0
msgid "Scan your final product or more components"
msgstr ""

#. module: stock_barcode_mrp
#: model_terms:ir.ui.view,arch_db:stock_barcode_mrp.mrp_product_selector
#: model_terms:ir.ui.view,arch_db:stock_barcode_mrp.stock_move_line_product_selector
msgid "Serial/Lot Number"
msgstr ""

#. module: stock_barcode_mrp
#: model:ir.model,name:stock_barcode_mrp.model_stock_move
msgid "Stock Move"
msgstr ""

#. module: stock_barcode_mrp
#. odoo-javascript
#: code:addons/stock_barcode_mrp/static/src/models/barcode_mrp_model.js:0
msgid "The Manufacturing Order has been cancelled"
msgstr ""

#. module: stock_barcode_mrp
#. odoo-javascript
#: code:addons/stock_barcode_mrp/static/src/models/barcode_picking_model.js:0
msgid ""
"The lines with a kit have been replaced with their components. Please check "
"the picking before the final validation."
msgstr ""

#. module: stock_barcode_mrp
#. odoo-javascript
#: code:addons/stock_barcode_mrp/static/src/models/barcode_mrp_model.js:0
msgid "The manufacturing order has been validated"
msgstr ""

#. module: stock_barcode_mrp
#. odoo-javascript
#: code:addons/stock_barcode_mrp/static/src/models/barcode_mrp_model.js:0
msgid "This order is already done"
msgstr ""

#. module: stock_barcode_mrp
#. odoo-javascript
#: code:addons/stock_barcode_mrp/static/src/models/barcode_mrp_model.js:0
msgid "This order is cancelled"
msgstr ""

#. module: stock_barcode_mrp
#. odoo-javascript
#: code:addons/stock_barcode_mrp/static/src/models/barcode_mrp_model.js:0
msgid "To produce more products create a new MO."
msgstr ""

#. module: stock_barcode_mrp
#: model:ir.model,name:stock_barcode_mrp.model_stock_picking
msgid "Transfer"
msgstr "گواستنەوە"

#. module: stock_barcode_mrp
#: model_terms:ir.actions.act_window,help:stock_barcode_mrp.mrp_action_kanban
msgid "Transfers allow you to move products from one location to another."
msgstr ""

#. module: stock_barcode_mrp
#: model_terms:ir.ui.view,arch_db:stock_barcode_mrp.mrp_product_selector
msgid "Unit of Measure"
msgstr ""

#. module: stock_barcode_mrp
#: model:ir.model.fields,help:stock_barcode_mrp.field_stock_move_line__manual_consumption
msgid ""
"When activated, then the registration of consumption for that component is recorded manually exclusively.\n"
"If not activated, and any of the components consumption is edited manually on the manufacturing order, Odoo assumes manual consumption also."
msgstr ""

#. module: stock_barcode_mrp
#: model:ir.model,name:stock_barcode_mrp.model_mrp_production_backorder
msgid "Wizard to mark as done or create back order"
msgstr ""

#. module: stock_barcode_mrp
#. odoo-javascript
#: code:addons/stock_barcode_mrp/static/src/models/barcode_mrp_model.js:0
msgid "You are expected to scan one or more products."
msgstr ""

#. module: stock_barcode_mrp
#. odoo-javascript
#: code:addons/stock_barcode_mrp/static/src/models/barcode_mrp_model.js:0
msgid "You can't add the final product of a MO as a byproduct."
msgstr ""
