# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* snailmail_account_followup
# 
# Translators:
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:28+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Russian (https://app.transifex.com/odoo/teams/41243/ru/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ru\n"
"Plural-Forms: nplurals=4; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<12 || n%100>14) ? 1 : n%10==0 || (n%10>=5 && n%10<=9) || (n%100>=11 && n%100<=14)? 2 : 3);\n"

#. module: snailmail_account_followup
#: model_terms:ir.ui.view,arch_db:snailmail_account_followup.manual_reminder_view_form_inherit_snailmail
msgid ""
"<i class=\"fa fa-info-circle\" role=\"img\" aria-label=\"Warning\" "
"title=\"The letter will be sent using the IAP service from Odoo.&#10;Make "
"sure you have enough credits on your account or proceed to a recharge.\"/>"
msgstr ""

#. module: snailmail_account_followup
#: model_terms:ir.ui.view,arch_db:snailmail_account_followup.manual_reminder_view_form_inherit_snailmail
msgid "By post"
msgstr "По почте"

#. module: snailmail_account_followup
#: model:ir.model,name:snailmail_account_followup.model_res_partner
msgid "Contact"
msgstr "Контакты"

#. module: snailmail_account_followup
#: model:ir.model,name:snailmail_account_followup.model_account_followup_followup_line
msgid "Follow-up Criteria"
msgstr "Критерии Последовательности"

#. module: snailmail_account_followup
#: model:ir.model,name:snailmail_account_followup.model_account_followup_report
msgid "Follow-up Report"
msgstr "Последующий отчет"

#. module: snailmail_account_followup
#: model:ir.model.fields,field_description:snailmail_account_followup.field_account_followup_followup_line__send_letter
msgid "Send a Letter"
msgstr "Отправить письмо"

#. module: snailmail_account_followup
#: model:ir.model.fields,field_description:snailmail_account_followup.field_account_followup_manual_reminder__snailmail
msgid "Snailmail"
msgstr "Snailmail"

#. module: snailmail_account_followup
#: model:ir.model.fields,field_description:snailmail_account_followup.field_account_followup_manual_reminder__snailmail_cost
msgid "Stamps"
msgstr "Штамп-карты"

#. module: snailmail_account_followup
#: model:ir.model,name:snailmail_account_followup.model_account_followup_manual_reminder
msgid "Wizard for sending manual reminders to clients"
msgstr "Мастер для отправки ручных напоминаний клиентам"

#. module: snailmail_account_followup
#. odoo-python
#: code:addons/snailmail_account_followup/models/account_followup_report.py:0
msgid ""
"You are trying to send a letter by post, but no follow-up contact has any "
"address set"
msgstr ""
"Вы пытаетесь отправить письмо по почте, но ни один из последующих контактов "
"не имеет установленного адреса"
