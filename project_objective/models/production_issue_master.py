from odoo import fields, models


class ProductionIssueMaster(models.Model):
    _name = 'production.issue.master'
    _description = 'production issue master'
    _inherit = [
        'portal.mixin',
        'mail.thread.cc',
        'mail.activity.mixin',
        'rating.mixin',
        'mail.tracking.duration.mixin',
        'html.field.history.mixin',
    ]
    _rec_name = 'name'
    _order = 'id desc'

    name = fields.Char('Name', required=True, tracking=True)

