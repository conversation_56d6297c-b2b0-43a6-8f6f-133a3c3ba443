# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* social_facebook
# 
# Translators:
# <PERSON><PERSON>, 2024
# <PERSON><PERSON><PERSON>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON> <<EMAIL>>, 2024
# <PERSON>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# Anna, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:28+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Anna, 2024\n"
"Language-Team: Estonian (https://app.transifex.com/odoo/teams/41243/et/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: et\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: social_facebook
#: model_terms:ir.ui.view,arch_db:social_facebook.facebook_preview
msgid "<b class=\"d-block mb-2\">Facebook Page</b>"
msgstr "<b class=\"d-block mb-2\">Facebook'i leht</b>"

#. module: social_facebook
#: model_terms:ir.ui.view,arch_db:social_facebook.social_stream_post_view_kanban
msgid "<i class=\"fa fa-comments me-1\" title=\"Comments\"/>"
msgstr "<i class=\"fa fa-comments me-1\" title=\"Comments\"/>"

#. module: social_facebook
#: model:ir.model.fields,field_description:social_facebook.field_social_live_post__facebook_post_id
msgid "Actual Facebook ID of the post"
msgstr "Postituse tegelik Facebook'i ID"

#. module: social_facebook
#. odoo-python
#: code:addons/social_facebook/models/social_stream_post.py:0
msgid "An error occurred."
msgstr "Ilmnes viga"

#. module: social_facebook
#: model_terms:ir.ui.view,arch_db:social_facebook.res_config_settings_view_form
msgid "App ID"
msgstr "Rakenduse ID"

#. module: social_facebook
#: model_terms:ir.ui.view,arch_db:social_facebook.res_config_settings_view_form
msgid "App Secret"
msgstr "App Secret"

#. module: social_facebook
#. odoo-javascript
#: code:addons/social_facebook/static/src/xml/social_facebook_templates.xml:0
#: model_terms:ir.ui.view,arch_db:social_facebook.social_stream_post_view_kanban
msgid "Author Image"
msgstr "Autori pilt"

#. module: social_facebook
#: model:ir.model.fields,help:social_facebook.field_res_config_settings__facebook_use_own_account
msgid ""
"Check this if you want to use your personal Facebook Developer Account "
"instead of the provided one."
msgstr ""
"Märgi see valik, enda isiklikku Facebook'i arendaja kontot pakutud konto "
"asemel."

#. module: social_facebook
#: model:ir.model.fields,field_description:social_facebook.field_social_stream_post__facebook_comments_count
msgid "Comments"
msgstr "Kommentaarid"

#. module: social_facebook
#: model:ir.model,name:social_facebook.model_res_config_settings
msgid "Config Settings"
msgstr "Seadistused"

#. module: social_facebook
#: model:ir.model.fields,field_description:social_facebook.field_social_post__display_facebook_preview
#: model:ir.model.fields,field_description:social_facebook.field_social_post_template__display_facebook_preview
msgid "Display Facebook Preview"
msgstr "Kuva Facebook'i eelvaadet"

#. module: social_facebook
#: model:ir.model.fields.selection,name:social_facebook.selection__social_media__media_type__facebook
#: model:social.media,name:social_facebook.social_media_facebook
#: model_terms:ir.ui.view,arch_db:social_facebook.social_post_template_view_form
msgid "Facebook"
msgstr "Facebook"

#. module: social_facebook
#: model:ir.model.fields,field_description:social_facebook.field_social_account__facebook_access_token
msgid "Facebook Access Token"
msgstr "Facebooki ligipääsuvõti"

#. module: social_facebook
#: model:ir.model.fields,field_description:social_facebook.field_social_account__facebook_account_id
msgid "Facebook Account ID"
msgstr "Facebook'i konto ID"

#. module: social_facebook
#: model:ir.model.fields,field_description:social_facebook.field_res_config_settings__facebook_app_id
msgid "Facebook App ID"
msgstr "Facebook rakenduse ID"

#. module: social_facebook
#: model:ir.model.fields,field_description:social_facebook.field_res_config_settings__facebook_client_secret
msgid "Facebook App Secret"
msgstr "Facebook rakenduse Secret"

#. module: social_facebook
#: model:ir.model.fields,field_description:social_facebook.field_social_stream_post__facebook_author_id
msgid "Facebook Author ID"
msgstr "Facebook autori ID"

#. module: social_facebook
#. odoo-javascript
#: code:addons/social_facebook/static/src/js/stream_post_kanban_record.js:0
msgid "Facebook Comments"
msgstr "Facebook kommentaarid"

#. module: social_facebook
#: model_terms:ir.ui.view,arch_db:social_facebook.res_config_settings_view_form
msgid "Facebook Developer Account"
msgstr "Facebook arendaja konto"

#. module: social_facebook
#: model:ir.model.fields,field_description:social_facebook.field_social_post__facebook_image_ids
#: model:ir.model.fields,field_description:social_facebook.field_social_post_template__facebook_image_ids
msgid "Facebook Images"
msgstr ""

#. module: social_facebook
#: model:ir.model.fields,field_description:social_facebook.field_social_post__facebook_message
#: model:ir.model.fields,field_description:social_facebook.field_social_post_template__facebook_message
msgid "Facebook Message"
msgstr ""

#. module: social_facebook
#: model:ir.model.fields,help:social_facebook.field_social_account__facebook_access_token
msgid ""
"Facebook Page Access Token provided by the Facebook API, this should never be set manually.\n"
"            It's used to authenticate requests when posting to or reading information from this account."
msgstr ""

#. module: social_facebook
#: model:ir.model.fields,help:social_facebook.field_social_account__facebook_account_id
msgid ""
"Facebook Page ID provided by the Facebook API, this should never be set "
"manually."
msgstr ""

#. module: social_facebook
#: model:ir.model.fields,field_description:social_facebook.field_social_stream_post__facebook_post_id
msgid "Facebook Post ID"
msgstr "Facebook postituse ID"

#. module: social_facebook
#: model:ir.model.fields,field_description:social_facebook.field_social_post__facebook_preview
#: model:ir.model.fields,field_description:social_facebook.field_social_post_template__facebook_preview
msgid "Facebook Preview"
msgstr "Facebook'i eelvaade"

#. module: social_facebook
#. odoo-python
#: code:addons/social_facebook/controllers/main.py:0
msgid "Facebook did not provide a valid access token or it may have expired."
msgstr ""

#. module: social_facebook
#. odoo-python
#: code:addons/social_facebook/controllers/main.py:0
msgid "Facebook did not provide a valid access token."
msgstr ""

#. module: social_facebook
#: model:ir.model.fields,field_description:social_facebook.field_social_post__has_facebook_account
#: model:ir.model.fields,field_description:social_facebook.field_social_post_template__has_facebook_account
msgid "Has Facebook Account"
msgstr ""

#. module: social_facebook
#: model_terms:ir.ui.view,arch_db:social_facebook.social_post_template_view_form
msgid "Images"
msgstr "Pildid"

#. module: social_facebook
#: model:ir.model.fields,field_description:social_facebook.field_social_stream_post__facebook_is_event_post
msgid "Is event post"
msgstr "On sündmuse postitus"

#. module: social_facebook
#. odoo-javascript
#: code:addons/social_facebook/static/src/xml/social_facebook_templates.xml:0
#: model_terms:ir.ui.view,arch_db:social_facebook.social_stream_post_view_kanban
msgid "Like"
msgstr "Meeldib"

#. module: social_facebook
#: model:ir.model.fields,field_description:social_facebook.field_social_stream_post__facebook_likes_count
msgid "Likes"
msgstr "Meeldimised"

#. module: social_facebook
#: model:ir.model.fields,field_description:social_facebook.field_social_media__media_type
msgid "Media Type"
msgstr "Meedia tüüp"

#. module: social_facebook
#: model_terms:ir.ui.view,arch_db:social_facebook.social_post_template_view_form
msgid "Message"
msgstr "Sõnum"

#. module: social_facebook
#: model:social.stream.type,name:social_facebook.stream_type_page_mentions
msgid "Page Mentions"
msgstr "Lehe mainimised"

#. module: social_facebook
#: model:social.stream.type,name:social_facebook.stream_type_page_posts
msgid "Page Posts"
msgstr "Lehe postitused"

#. module: social_facebook
#. odoo-python
#: code:addons/social_facebook/models/social_post_template.py:0
msgid ""
"Please specify either a Facebook Message or upload some Facebook Images."
msgstr ""

#. module: social_facebook
#: model_terms:ir.ui.view,arch_db:social_facebook.facebook_preview
msgid "Post Image"
msgstr "Postita pilt"

#. module: social_facebook
#. odoo-python
#: code:addons/social_facebook/models/social_stream_post.py:0
msgid ""
"Post not found. It could be because the post has been deleted on the Social "
"Platform."
msgstr ""
"Postitust ei leitud. Põhjus võib olla selles, et postitus on sotsiaalmeedias"
" kustutatud."

#. module: social_facebook
#: model_terms:ir.ui.view,arch_db:social_facebook.facebook_preview
msgid "Published by Facebook Page •"
msgstr "Avaldatud Facebooki lehelt  •"

#. module: social_facebook
#: model:ir.model.fields,field_description:social_facebook.field_social_stream_post__facebook_reach
msgid "Reach"
msgstr "Jõua"

#. module: social_facebook
#: model:ir.model.fields,field_description:social_facebook.field_social_stream_post__facebook_reactions_count
msgid "Reactions Count"
msgstr ""

#. module: social_facebook
#. odoo-python
#: code:addons/social_facebook/controllers/main.py:0
msgid "Read More about Facebook Pages"
msgstr ""

#. module: social_facebook
#. odoo-javascript
#: code:addons/social_facebook/static/src/xml/social_facebook_templates.xml:0
#: model:ir.model.fields,field_description:social_facebook.field_social_stream_post__facebook_shares_count
#: model_terms:ir.ui.view,arch_db:social_facebook.social_stream_post_view_kanban
msgid "Shares"
msgstr "Jagamised"

#. module: social_facebook
#: model:ir.model,name:social_facebook.model_social_account
msgid "Social Account"
msgstr "Sotsiaalkonto"

#. module: social_facebook
#: model:ir.model,name:social_facebook.model_social_live_post
msgid "Social Live Post"
msgstr "Sotsiaalne otsepostitus"

#. module: social_facebook
#: model:ir.model,name:social_facebook.model_social_media
msgid "Social Media"
msgstr "Sotsiaalmeedia"

#. module: social_facebook
#: model:ir.model,name:social_facebook.model_social_post
msgid "Social Post"
msgstr "Sotsiaalmeedia postitus"

#. module: social_facebook
#: model:ir.model,name:social_facebook.model_social_post_template
msgid "Social Post Template"
msgstr "Sotsiaalmeedia postituse mall"

#. module: social_facebook
#: model:ir.model,name:social_facebook.model_social_stream
msgid "Social Stream"
msgstr "Sotsiaalmeedia voog"

#. module: social_facebook
#: model:ir.model,name:social_facebook.model_social_stream_post
msgid "Social Stream Post"
msgstr "Sotsiaalmeedia striiming postitus"

#. module: social_facebook
#. odoo-python
#: code:addons/social_facebook/controllers/main.py:0
msgid "Unauthorized. Please contact your administrator. "
msgstr "Autoriseerimata. Palun võtke ühendust oma administraatoriga."

#. module: social_facebook
#. odoo-python
#: code:addons/social_facebook/models/social_stream_post.py:0
msgid "Unknown"
msgstr "Tundmatu"

#. module: social_facebook
#: model:ir.model.fields,field_description:social_facebook.field_res_config_settings__facebook_use_own_account
msgid "Use your own Facebook Account"
msgstr "Kasutage enda Facebook'i kontot"

#. module: social_facebook
#: model:ir.model.fields,help:social_facebook.field_social_media__media_type
msgid ""
"Used to make comparisons when we need to restrict some features to a "
"specific media ('facebook', 'x', ...)."
msgstr ""

#. module: social_facebook
#: model:ir.model.fields,field_description:social_facebook.field_social_stream_post__facebook_user_likes
msgid "User Likes"
msgstr "Kasutaja meeldimised"

#. module: social_facebook
#. odoo-javascript
#: code:addons/social_facebook/static/src/xml/social_facebook_templates.xml:0
#: model_terms:ir.ui.view,arch_db:social_facebook.social_stream_post_view_kanban
msgid "Views"
msgstr "Vaated"

#. module: social_facebook
#. odoo-python
#: code:addons/social_facebook/models/social_live_post.py:0
#: code:addons/social_facebook/models/social_post.py:0
msgid ""
"We could not upload your image, try reducing its size and posting it again "
"(error: %s)."
msgstr ""
"Me ei saanud teie pilti üles laadida. Proovige vähendada pildi suurust ja "
"postitada uuesti. (viga: %s)."

#. module: social_facebook
#: model:ir.model.fields,help:social_facebook.field_social_post__facebook_image_ids
#: model:ir.model.fields,help:social_facebook.field_social_post_template__facebook_image_ids
msgid "Will attach images to your posts."
msgstr "Lisab pilte sinu postitustele."

#. module: social_facebook
#. odoo-python
#: code:addons/social_facebook/models/social_media.py:0
msgid "You don't have an active subscription. Please buy one here: %s"
msgstr "Teil pole aktiivset tellimust. Palun osta üks siit: %s"

#. module: social_facebook
#. odoo-python
#: code:addons/social_facebook/controllers/main.py:0
msgid ""
"You need to be the manager of a Facebook Page to post with Odoo Social.\n"
" Please create one and make sure it is linked to your account."
msgstr ""

#. module: social_facebook
#: model_terms:ir.ui.view,arch_db:social_facebook.social_stream_post_view_kanban
msgid "added an event"
msgstr "lisas sündmuse"

#. module: social_facebook
#: model_terms:ir.ui.view,arch_db:social_facebook.facebook_preview
msgid "• <i class=\"fa fa-globe\"/>"
msgstr "• <i class=\"fa fa-globe\"/>"
