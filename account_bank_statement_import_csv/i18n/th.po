# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_bank_statement_import_csv
# 
# Translators:
# Wil <PERSON>do<PERSON>, 2024
# <PERSON><PERSON><PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-19 09:52+0000\n"
"PO-Revision-Date: 2024-09-25 09:43+0000\n"
"Last-Translator: Ra<PERSON><PERSON><PERSON>, 2024\n"
"Language-Team: Thai (https://app.transifex.com/odoo/teams/41243/th/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: th\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: account_bank_statement_import_csv
#: model:ir.model,name:account_bank_statement_import_csv.model_base_import_import
msgid "Base Import"
msgstr "การนำเข้าฐาน"

#. module: account_bank_statement_import_csv
#. odoo-javascript
#: code:addons/account_bank_statement_import_csv/static/src/bank_statement_csv_import_action.js:0
msgid "Import Bank Statement"
msgstr "นำเข้าใบแจ้งยอดธนาคาร"

#. module: account_bank_statement_import_csv
#: model:ir.model,name:account_bank_statement_import_csv.model_account_journal
msgid "Journal"
msgstr "สมุดรายวัน"

#. module: account_bank_statement_import_csv
#. odoo-python
#: code:addons/account_bank_statement_import_csv/wizard/account_bank_statement_import_csv.py:0
msgid "Make sure that an Amount or Debit and Credit is in the file."
msgstr "ตรวจสอบให้แน่ใจว่ามีจำนวนเงินหรือเดบิตและเครดิตอยู่ในไฟล์"

#. module: account_bank_statement_import_csv
#. odoo-python
#: code:addons/account_bank_statement_import_csv/models/account_journal.py:0
msgid "Mixing CSV files with other file types is not allowed."
msgstr "ไม่อนุญาตให้ผสมไฟล์ CSV กับไฟล์ประเภทอื่น"

#. module: account_bank_statement_import_csv
#. odoo-python
#: code:addons/account_bank_statement_import_csv/models/account_journal.py:0
msgid "Only one CSV file can be selected."
msgstr "สามารถเลือกไฟล์ CSV ได้เพียงไฟล์เดียวเท่านั้น"

#. module: account_bank_statement_import_csv
#. odoo-python
#: code:addons/account_bank_statement_import_csv/wizard/account_bank_statement_import_csv.py:0
msgid "Rows must be sorted by date."
msgstr "แถวจะต้องจัดเรียงตามวันที่"
