<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record id="sale_order_cash_credit_wizard_form" model="ir.ui.view">
            <field name="name">credit.cash.wizard.payment.form</field>
            <field name="model">credit.cash.wizard</field>
            <field name="arch" type="xml">
                <form>
                    <sheet>
                        <group>
                            <group>
                                <field name="sale_id"/>
                                <field name="payment_type" string="Payment Method" readonly="1"/>
                                <field name="company_id" invisible="1"/>
                                <field name="currency_id" options="{'no_create': True, 'no_open': True}"/>
                            </group>
                            <group>
                                <field name="amount" string="Payment Amount"
                                       readonly="payment_type == 'credit'"/>
                            </group>
                        </group>
                    </sheet>
                    <footer>
                        <button string="Confirm" type="object" name="action_process_payment" class="oe_highlight"/>
                        <button string="Cancel" class="btn-default oe_highlight" special="cancel"/>
                    </footer>
                </form>
            </field>
        </record>

        <record id="action_sale_order_cash_credit_wizard" model="ir.actions.act_window">
            <field name="name">Sale Order Payment Wizard</field>
            <field name="res_model">credit.cash.wizard</field>
            <field name="view_mode">form</field>
            <field name="view_id" ref="sale_order_cash_credit_wizard_form"/>
            <field name="target">new</field>
        </record>
    </data>
</odoo>
