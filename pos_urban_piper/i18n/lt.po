# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* pos_urban_piper
# 
# Translators:
# <PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <AUTHOR> <EMAIL>, 2024
# <PERSON>run<PERSON>. <<EMAIL>>, 2024
# UAB "Draugiš<PERSON> sprendimai" <<EMAIL>>, 2024
# Nerijus, 2024
# <PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON>Laurea <<EMAIL>>, 2024
# Audr<PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <edgara<PERSON>@focusate.eu>, 2024
# Martin <PERSON>x, 2024
# Linas <PERSON> <<EMAIL>>, 2024
# Wil Odoo, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-01-27 13:56+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Wil Odoo, 2025\n"
"Language-Team: Lithuanian (https://app.transifex.com/odoo/teams/41243/lt/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: lt\n"
"Plural-Forms: nplurals=4; plural=(n % 10 == 1 && (n % 100 > 19 || n % 100 < 11) ? 0 : (n % 10 >= 2 && n % 10 <=9) && (n % 100 > 19 || n % 100 < 11) ? 1 : n % 1 != 0 ? 2: 3);\n"

#. module: pos_urban_piper
#. odoo-python
#: code:addons/pos_urban_piper/models/res_config_settings.py:0
msgid ""
"(%(provider_name)s) delivery is only available in the following countries: \n"
"%(country_names)s"
msgstr ""

#. module: pos_urban_piper
#. odoo-javascript
#: code:addons/pos_urban_piper/static/src/point_of_sale_overrirde/override/screens/ticket_screen/ticket_screen.xml:0
msgid "Accept"
msgstr "Priimti"

#. module: pos_urban_piper
#: model:ir.model.fields.selection,name:pos_urban_piper.selection__pos_order__delivery_status__acknowledged
msgid "Acknowledged"
msgstr ""

#. module: pos_urban_piper
#. odoo-javascript
#: code:addons/pos_urban_piper/static/src/point_of_sale_overrirde/app/order_info_popup/order_info_popup.js:0
msgid "Aggregator (Handled by Platform)"
msgstr ""

#. module: pos_urban_piper
#: model:ir.model.fields,help:pos_urban_piper.field_pos_config__name
msgid "An internal identification of the point of sale."
msgstr "Vidinė pardavimo taško identifikacija."

#. module: pos_urban_piper
#: model_terms:ir.ui.view,arch_db:pos_urban_piper.res_config_settings_view_form_pos_urban_piper
msgid "Api Key"
msgstr ""

#. module: pos_urban_piper
#: model:ir.model.fields,field_description:pos_urban_piper.field_pos_delivery_provider__available_country_ids
msgid "Available Countries"
msgstr ""

#. module: pos_urban_piper
#: model:ir.model.fields,field_description:pos_urban_piper.field_product_product__urbanpiper_pos_config_ids
#: model:ir.model.fields,field_description:pos_urban_piper.field_product_template__urbanpiper_pos_config_ids
msgid "Available on Food Delivery"
msgstr ""

#. module: pos_urban_piper
#. odoo-javascript
#: code:addons/pos_urban_piper/static/src/point_of_sale_overrirde/override/screens/product_screen/product_screen.js:0
#: code:addons/pos_urban_piper/static/src/point_of_sale_overrirde/override/screens/product_screen/product_screen.xml:0
msgid "Back"
msgstr "Grįžti"

#. module: pos_urban_piper
#: model:ir.model.fields.selection,name:pos_urban_piper.selection__pos_order__delivery_status__cancelled
msgid "Cancelled"
msgstr "Atšauktas"

#. module: pos_urban_piper
#. odoo-javascript
#: code:addons/pos_urban_piper/static/src/point_of_sale_overrirde/app/order_info_popup/order_info_popup.js:0
msgid "Card on Delivery"
msgstr ""

#. module: pos_urban_piper
#. odoo-javascript
#: code:addons/pos_urban_piper/static/src/point_of_sale_overrirde/app/order_info_popup/order_info_popup.js:0
msgid "Cash"
msgstr "Grynieji"

#. module: pos_urban_piper
#. odoo-javascript
#: code:addons/pos_urban_piper/static/src/point_of_sale_overrirde/app/order_info_popup/order_info_popup.js:0
msgid "Channel"
msgstr "Kanalas"

#. module: pos_urban_piper
#: model:ir.model.fields,help:pos_urban_piper.field_product_product__urbanpiper_pos_config_ids
#: model:ir.model.fields,help:pos_urban_piper.field_product_template__urbanpiper_pos_config_ids
msgid "Check this if the product is available for food delivery."
msgstr ""

#. module: pos_urban_piper
#: model:ir.model.fields,help:pos_urban_piper.field_pos_payment_method__is_delivery_payment
msgid "Check this if this payment method is used for online delivery orders."
msgstr ""

#. module: pos_urban_piper
#. odoo-javascript
#: code:addons/pos_urban_piper/static/src/point_of_sale_overrirde/app/order_info_popup/order_info_popup.xml:0
msgid "Close"
msgstr "Uždaryti"

#. module: pos_urban_piper
#. odoo-javascript
#: code:addons/pos_urban_piper/static/src/point_of_sale_overrirde/override/screens/ticket_screen/ticket_screen.xml:0
msgid "Complete Order"
msgstr ""

#. module: pos_urban_piper
#: model:ir.model.fields.selection,name:pos_urban_piper.selection__pos_order__delivery_status__completed
msgid "Completed"
msgstr "Atlikta"

#. module: pos_urban_piper
#: model:ir.model,name:pos_urban_piper.model_res_config_settings
msgid "Config Settings"
msgstr "Konfigūracijos nustatymai"

#. module: pos_urban_piper
#. odoo-javascript
#: code:addons/pos_urban_piper/static/src/point_of_sale_overrirde/override/screens/ticket_screen/ticket_screen.js:0
msgid "Connectivity Issue"
msgstr ""

#. module: pos_urban_piper
#: model:ir.model.fields,help:pos_urban_piper.field_pos_delivery_provider__available_country_ids
msgid "Countries where this provider is available"
msgstr ""

#. module: pos_urban_piper
#: model_terms:ir.ui.view,arch_db:pos_urban_piper.res_config_settings_view_form_pos_urban_piper
msgid "Create Store"
msgstr ""

#. module: pos_urban_piper
#: model:ir.model.fields,field_description:pos_urban_piper.field_pos_delivery_provider__create_uid
#: model:ir.model.fields,field_description:pos_urban_piper.field_product_urban_piper_status__create_uid
msgid "Created by"
msgstr "Sukūrė"

#. module: pos_urban_piper
#: model:ir.model.fields,field_description:pos_urban_piper.field_pos_delivery_provider__create_date
#: model:ir.model.fields,field_description:pos_urban_piper.field_product_urban_piper_status__create_date
msgid "Created on"
msgstr "Sukurta"

#. module: pos_urban_piper
#. odoo-javascript
#: code:addons/pos_urban_piper/static/src/point_of_sale_overrirde/app/order_info_popup/order_info_popup.js:0
msgid "Customer Email"
msgstr "Kliento el. paštas"

#. module: pos_urban_piper
#. odoo-javascript
#: code:addons/pos_urban_piper/static/src/point_of_sale_overrirde/app/order_info_popup/order_info_popup.js:0
msgid "Customer Info"
msgstr ""

#. module: pos_urban_piper
#. odoo-javascript
#: code:addons/pos_urban_piper/static/src/point_of_sale_overrirde/app/order_info_popup/order_info_popup.js:0
msgid "Customer Name"
msgstr "Kliento vardas"

#. module: pos_urban_piper
#. odoo-javascript
#: code:addons/pos_urban_piper/static/src/point_of_sale_overrirde/app/order_info_popup/order_info_popup.js:0
msgid "Customer Phone"
msgstr ""

#. module: pos_urban_piper
#. odoo-javascript
#: code:addons/pos_urban_piper/static/src/point_of_sale_overrirde/app/order_info_popup/order_info_popup.js:0
#: code:addons/pos_urban_piper/static/src/point_of_sale_overrirde/override/screens/receipt_screen/receipt/order_receipt.xml:0
msgid "Delivery Address"
msgstr "Pristatymo adresas"

#. module: pos_urban_piper
#. odoo-javascript
#: code:addons/pos_urban_piper/static/src/point_of_sale_overrirde/override/screens/ticket_screen/ticket_screen.js:0
msgid "Delivery Channel"
msgstr ""

#. module: pos_urban_piper
#: model:product.template,name:pos_urban_piper.product_delivery_charges_product_template
msgid "Delivery Charges"
msgstr ""

#. module: pos_urban_piper
#: model:ir.model.fields,field_description:pos_urban_piper.field_pos_order__delivery_identifier
msgid "Delivery ID"
msgstr ""

#. module: pos_urban_piper
#: model:ir.model.fields,field_description:pos_urban_piper.field_pos_order__delivery_json
msgid "Delivery JSON"
msgstr ""

#. module: pos_urban_piper
#. odoo-javascript
#: code:addons/pos_urban_piper/static/src/point_of_sale_overrirde/override/screens/ticket_screen/ticket_screen.js:0
msgid "Delivery Order Status"
msgstr ""

#. module: pos_urban_piper
#. odoo-javascript
#: code:addons/pos_urban_piper/static/src/point_of_sale_overrirde/override/screens/ticket_screen/ticket_screen.xml:0
msgid "Delivery Partner"
msgstr ""

#. module: pos_urban_piper
#: model:ir.model.fields,field_description:pos_urban_piper.field_pos_payment_method__is_delivery_payment
msgid "Delivery Payment"
msgstr ""

#. module: pos_urban_piper
#. odoo-javascript
#: code:addons/pos_urban_piper/static/src/point_of_sale_overrirde/app/order_info_popup/order_info_popup.js:0
msgid "Delivery Person"
msgstr ""

#. module: pos_urban_piper
#: model:ir.model.fields,field_description:pos_urban_piper.field_pos_order__delivery_provider_id
#: model:ir.model.fields,field_description:pos_urban_piper.field_pos_payment_method__delivery_provider_id
msgid "Delivery Provider"
msgstr ""

#. module: pos_urban_piper
#: model:ir.model.fields,field_description:pos_urban_piper.field_pos_config__urbanpiper_delivery_provider_ids
msgid "Delivery Providers"
msgstr ""

#. module: pos_urban_piper
#: model:ir.model.fields,field_description:pos_urban_piper.field_pos_order__delivery_rider_json
msgid "Delivery Rider JSON"
msgstr ""

#. module: pos_urban_piper
#: model:ir.model.fields,field_description:pos_urban_piper.field_pos_order__delivery_status
msgid "Delivery Status"
msgstr "Pristatymo būsena"

#. module: pos_urban_piper
#. odoo-javascript
#: code:addons/pos_urban_piper/static/src/point_of_sale_overrirde/app/order_info_popup/order_info_popup.js:0
msgid "Delivery Time/Time-Slot"
msgstr ""

#. module: pos_urban_piper
#. odoo-javascript
#: code:addons/pos_urban_piper/static/src/point_of_sale_overrirde/app/order_info_popup/order_info_popup.xml:0
msgid "Details"
msgstr "Papildoma informacija"

#. module: pos_urban_piper
#. odoo-javascript
#: code:addons/pos_urban_piper/static/src/point_of_sale_overrirde/override/screens/ticket_screen/ticket_screen.xml:0
msgid "Dispatch Order"
msgstr ""

#. module: pos_urban_piper
#: model:ir.model.fields.selection,name:pos_urban_piper.selection__pos_order__delivery_status__dispatched
msgid "Dispatched"
msgstr ""

#. module: pos_urban_piper
#: model:ir.model.fields,field_description:pos_urban_piper.field_pos_delivery_provider__display_name
#: model:ir.model.fields,field_description:pos_urban_piper.field_product_urban_piper_status__display_name
msgid "Display Name"
msgstr "Rodomas pavadinimas"

#. module: pos_urban_piper
#. odoo-javascript
#: code:addons/pos_urban_piper/static/src/point_of_sale_overrirde/app/delivery_button/delivery_button.xml:0
msgid "Done"
msgstr "Atlikta"

#. module: pos_urban_piper
#. odoo-javascript
#: code:addons/pos_urban_piper/static/src/point_of_sale_overrirde/override/screens/ticket_screen/ticket_screen.xml:0
msgid "Edit"
msgstr "Redaguoti"

#. module: pos_urban_piper
#: model:ir.model.fields.selection,name:pos_urban_piper.selection__product_template__urbanpiper_meal_type__3
msgid "Eggetarian"
msgstr ""

#. module: pos_urban_piper
#. odoo-javascript
#: code:addons/pos_urban_piper/static/src/point_of_sale_overrirde/override/screens/ticket_screen/ticket_screen.js:0
msgid "Error"
msgstr "Klaida"

#. module: pos_urban_piper
#. odoo-javascript
#: code:addons/pos_urban_piper/static/src/point_of_sale_overrirde/override/screens/ticket_screen/ticket_screen.js:0
msgid "Error to send delivery order in preparation display."
msgstr ""

#. module: pos_urban_piper
#. odoo-javascript
#: code:addons/pos_urban_piper/static/src/point_of_sale_overrirde/override/store/pos_store.js:0
msgid "Error to send in preparation display."
msgstr ""

#. module: pos_urban_piper
#: model_terms:ir.ui.view,arch_db:pos_urban_piper.res_config_settings_view_form_pos_urban_piper
msgid "Fill this form to get Username &amp; Api key"
msgstr ""

#. module: pos_urban_piper
#: model_terms:ir.ui.view,arch_db:pos_urban_piper.res_config_settings_view_form_pos_urban_piper
msgid "Fiscal Position"
msgstr "Mokestinė aplinka"

#. module: pos_urban_piper
#: model:ir.model.fields,help:pos_urban_piper.field_pos_config__urbanpiper_fiscal_position_id
msgid "Fiscal position for Urban Piper sync menu."
msgstr ""

#. module: pos_urban_piper
#: model_terms:ir.ui.view,arch_db:pos_urban_piper.res_config_settings_view_form_pos_urban_piper
msgid "Food Delivery Platforms"
msgstr ""

#. module: pos_urban_piper
#: model:ir.model.fields,field_description:pos_urban_piper.field_pos_order__prep_time
msgid "Food Preparation Time"
msgstr ""

#. module: pos_urban_piper
#: model:ir.model.fields.selection,name:pos_urban_piper.selection__pos_order__delivery_status__food_ready
msgid "Food Ready"
msgstr ""

#. module: pos_urban_piper
#. odoo-javascript
#: code:addons/pos_urban_piper/static/src/point_of_sale_overrirde/app/order_info_popup/order_info_popup.js:0
msgid "Fulfilment Mode"
msgstr ""

#. module: pos_urban_piper
#. odoo-python
#: code:addons/pos_urban_piper/models/res_config_settings.py:0
msgid "Go to Company configuration"
msgstr ""

#. module: pos_urban_piper
#: model:ir.model.fields,help:pos_urban_piper.field_product_product__urban_piper_status_ids
#: model:ir.model.fields,help:pos_urban_piper.field_product_template__urban_piper_status_ids
msgid ""
"Handle products with urban piper and pos config - Product is linked or not "
"with appropriate store."
msgstr ""

#. module: pos_urban_piper
#. odoo-javascript
#: code:addons/pos_urban_piper/static/src/point_of_sale_overrirde/app/order_info_popup/order_info_popup.js:0
msgid "HungerStation Code"
msgstr ""

#. module: pos_urban_piper
#: model:ir.model.fields,field_description:pos_urban_piper.field_pos_delivery_provider__id
#: model:ir.model.fields,field_description:pos_urban_piper.field_product_urban_piper_status__id
msgid "ID"
msgstr "ID"

#. module: pos_urban_piper
#: model:ir.model.fields,help:pos_urban_piper.field_product_product__is_alcoholic_on_urbanpiper
#: model:ir.model.fields,help:pos_urban_piper.field_product_template__is_alcoholic_on_urbanpiper
msgid "Indicates if the product contains alcohol."
msgstr ""

#. module: pos_urban_piper
#. odoo-javascript
#: code:addons/pos_urban_piper/static/src/point_of_sale_overrirde/override/screens/ticket_screen/ticket_screen.xml:0
msgid "Info"
msgstr "Informacija"

#. module: pos_urban_piper
#. odoo-javascript
#: code:addons/pos_urban_piper/static/src/point_of_sale_overrirde/override/screens/ticket_screen/ticket_screen.js:0
msgid "Invalid Product"
msgstr ""

#. module: pos_urban_piper
#. odoo-javascript
#: code:addons/pos_urban_piper/static/src/point_of_sale_overrirde/override/screens/ticket_screen/ticket_screen.js:0
msgid "Invalid Variant/Addons"
msgstr ""

#. module: pos_urban_piper
#: model:ir.model.fields,field_description:pos_urban_piper.field_product_product__is_alcoholic_on_urbanpiper
#: model:ir.model.fields,field_description:pos_urban_piper.field_product_template__is_alcoholic_on_urbanpiper
msgid "Is Alchoholic"
msgstr ""

#. module: pos_urban_piper
#: model:ir.model.fields,field_description:pos_urban_piper.field_product_urban_piper_status__is_product_linked
msgid "Is Product Linked?"
msgstr ""

#. module: pos_urban_piper
#: model:ir.model.fields,field_description:pos_urban_piper.field_product_product__is_recommended_on_urbanpiper
#: model:ir.model.fields,field_description:pos_urban_piper.field_product_template__is_recommended_on_urbanpiper
msgid "Is Recommended"
msgstr ""

#. module: pos_urban_piper
#: model:ir.model.fields,help:pos_urban_piper.field_pos_order__delivery_rider_json
msgid "JSON data of the delivery rider."
msgstr ""

#. module: pos_urban_piper
#: model:ir.model.fields,help:pos_urban_piper.field_pos_order__delivery_json
msgid "JSON data of the order."
msgstr ""

#. module: pos_urban_piper
#: model:ir.model.fields,field_description:pos_urban_piper.field_pos_delivery_provider__journal_code
msgid "Journal Short Code"
msgstr ""

#. module: pos_urban_piper
#: model:ir.model.fields,field_description:pos_urban_piper.field_pos_config__urbanpiper_last_sync_date
#: model:ir.model.fields,field_description:pos_urban_piper.field_res_config_settings__pos_urbanpiper_last_sync_date
#: model_terms:ir.ui.view,arch_db:pos_urban_piper.res_config_settings_view_form_pos_urban_piper
msgid "Last Sync on"
msgstr ""

#. module: pos_urban_piper
#: model:ir.model.fields,field_description:pos_urban_piper.field_pos_delivery_provider__write_uid
#: model:ir.model.fields,field_description:pos_urban_piper.field_product_urban_piper_status__write_uid
msgid "Last Updated by"
msgstr "Paskutinį kartą atnaujino"

#. module: pos_urban_piper
#: model:ir.model.fields,field_description:pos_urban_piper.field_pos_delivery_provider__write_date
#: model:ir.model.fields,field_description:pos_urban_piper.field_product_urban_piper_status__write_date
msgid "Last Updated on"
msgstr "Paskutinį kartą atnaujinta"

#. module: pos_urban_piper
#: model:ir.model.fields,help:pos_urban_piper.field_pos_config__urbanpiper_last_sync_date
msgid "Last sync date for menu sync."
msgstr ""

#. module: pos_urban_piper
#. odoo-javascript
#: code:addons/pos_urban_piper/static/src/point_of_sale_overrirde/override/screens/ticket_screen/ticket_screen.xml:0
msgid "Mark as ready"
msgstr ""

#. module: pos_urban_piper
#: model:ir.model.fields,field_description:pos_urban_piper.field_product_product__urbanpiper_meal_type
#: model:ir.model.fields,field_description:pos_urban_piper.field_product_template__urbanpiper_meal_type
msgid "Meal Type"
msgstr ""

#. module: pos_urban_piper
#: model:ir.model.fields.selection,name:pos_urban_piper.selection__product_template__urbanpiper_meal_type__4
msgid "N/A"
msgstr "nėra"

#. module: pos_urban_piper
#. odoo-javascript
#: code:addons/pos_urban_piper/static/src/point_of_sale_overrirde/app/order_info_popup/order_info_popup.js:0
#: model:ir.model.fields,field_description:pos_urban_piper.field_pos_delivery_provider__name
msgid "Name"
msgstr "Pavadinimas"

#. module: pos_urban_piper
#: model:ir.model.fields,help:pos_urban_piper.field_pos_delivery_provider__name
msgid "Name of the delivery provider i.e. Zomato, UberEats, etc."
msgstr ""

#. module: pos_urban_piper
#. odoo-javascript
#: code:addons/pos_urban_piper/static/src/point_of_sale_overrirde/app/delivery_button/delivery_button.xml:0
msgid "New"
msgstr "Naujas"

#. module: pos_urban_piper
#. odoo-javascript
#: code:addons/pos_urban_piper/static/src/point_of_sale_overrirde/override/store/pos_store.js:0
msgid "New online order received."
msgstr ""

#. module: pos_urban_piper
#: model:ir.model.fields.selection,name:pos_urban_piper.selection__product_template__urbanpiper_meal_type__2
msgid "Non-Vegetarian"
msgstr ""

#. module: pos_urban_piper
#. odoo-javascript
#: code:addons/pos_urban_piper/static/src/point_of_sale_overrirde/app/order_info_popup/order_info_popup.js:0
msgid "Not Specified"
msgstr ""

#. module: pos_urban_piper
#: model_terms:ir.ui.view,arch_db:pos_urban_piper.product_template_only_form_view_inherit_pos_urban_piper
msgid "Not available for online orders."
msgstr ""

#. module: pos_urban_piper
#. odoo-javascript
#: code:addons/pos_urban_piper/static/src/point_of_sale_overrirde/app/delivery_button/delivery_button.xml:0
msgid "Ongoing"
msgstr "Vykstantis"

#. module: pos_urban_piper
#: model:ir.model.fields,field_description:pos_urban_piper.field_res_config_settings__pos_urbanpiper_payment_methods_ids
msgid "Online Delivery Payment Methods"
msgstr ""

#. module: pos_urban_piper
#: model:ir.model,name:pos_urban_piper.model_pos_delivery_provider
#: model:ir.model.fields,field_description:pos_urban_piper.field_res_config_settings__pos_urbanpiper_delivery_provider_ids
msgid "Online Delivery Providers"
msgstr ""

#. module: pos_urban_piper
#. odoo-javascript
#: code:addons/pos_urban_piper/static/src/point_of_sale_overrirde/app/order_info_popup/order_info_popup.js:0
msgid "Order ID"
msgstr ""

#. module: pos_urban_piper
#. odoo-javascript
#: code:addons/pos_urban_piper/static/src/point_of_sale_overrirde/app/order_info_popup/order_info_popup.js:0
msgid "Order Info"
msgstr ""

#. module: pos_urban_piper
#. odoo-javascript
#: code:addons/pos_urban_piper/static/src/point_of_sale_overrirde/app/order_info_popup/order_info_popup.js:0
msgid "Order OTP"
msgstr ""

#. module: pos_urban_piper
#. odoo-javascript
#: code:addons/pos_urban_piper/static/src/point_of_sale_overrirde/override/screens/ticket_screen/ticket_screen.xml:0
msgid "Order Status"
msgstr "Užsakymo būsena"

#. module: pos_urban_piper
#. odoo-javascript
#: code:addons/pos_urban_piper/static/src/point_of_sale_overrirde/app/order_info_popup/order_info_popup.js:0
msgid "Order Time"
msgstr ""

#. module: pos_urban_piper
#. odoo-javascript
#: code:addons/pos_urban_piper/static/src/point_of_sale_overrirde/override/store/pos_store.js:0
msgid "Order does not load from server"
msgstr ""

#. module: pos_urban_piper
#: model:product.template,name:pos_urban_piper.product_other_charges_product_template
msgid "Other Charges"
msgstr ""

#. module: pos_urban_piper
#. odoo-javascript
#: code:addons/pos_urban_piper/static/src/point_of_sale_overrirde/override/screens/ticket_screen/ticket_screen.js:0
msgid "Others"
msgstr "Kiti"

#. module: pos_urban_piper
#. odoo-javascript
#: code:addons/pos_urban_piper/static/src/point_of_sale_overrirde/override/screens/ticket_screen/ticket_screen.js:0
msgid "Out of Delivery Radius"
msgstr ""

#. module: pos_urban_piper
#. odoo-javascript
#: code:addons/pos_urban_piper/static/src/point_of_sale_overrirde/app/order_info_popup/order_info_popup.js:0
msgid "Outlet"
msgstr ""

#. module: pos_urban_piper
#: model_terms:ir.ui.view,arch_db:pos_urban_piper.res_config_settings_view_form_pos_urban_piper
msgid "POS ID"
msgstr ""

#. module: pos_urban_piper
#: model:product.template,name:pos_urban_piper.product_packaging_charges_product_template
msgid "Packaging Charges"
msgstr ""

#. module: pos_urban_piper
#. odoo-javascript
#: code:addons/pos_urban_piper/static/src/point_of_sale_overrirde/app/order_info_popup/order_info_popup.js:0
msgid "Payment Gateway"
msgstr ""

#. module: pos_urban_piper
#. odoo-javascript
#: code:addons/pos_urban_piper/static/src/point_of_sale_overrirde/app/order_info_popup/order_info_popup.js:0
msgid "Payment Mode"
msgstr ""

#. module: pos_urban_piper
#: model:ir.model.fields,help:pos_urban_piper.field_pos_config__urbanpiper_payment_methods_ids
msgid "Payment methods for Urban Piper sync menu."
msgstr ""

#. module: pos_urban_piper
#. odoo-javascript
#: code:addons/pos_urban_piper/static/src/point_of_sale_overrirde/app/order_info_popup/order_info_popup.js:0
msgid "Paytm"
msgstr ""

#. module: pos_urban_piper
#. odoo-javascript
#: code:addons/pos_urban_piper/static/src/point_of_sale_overrirde/app/order_info_popup/order_info_popup.js:0
msgid "Phone"
msgstr "Telefonas"

#. module: pos_urban_piper
#: model:ir.model.fields.selection,name:pos_urban_piper.selection__pos_order__delivery_status__placed
msgid "Placed"
msgstr ""

#. module: pos_urban_piper
#: model:ir.model.fields,field_description:pos_urban_piper.field_pos_config__name
msgid "Point of Sale"
msgstr "Pardavimo taškas"

#. module: pos_urban_piper
#: model:ir.model,name:pos_urban_piper.model_pos_config
msgid "Point of Sale Configuration"
msgstr "Pardavimo taško konfigūracija"

#. module: pos_urban_piper
#: model:ir.model,name:pos_urban_piper.model_pos_order
msgid "Point of Sale Orders"
msgstr "PT užsakymai"

#. module: pos_urban_piper
#: model:ir.model,name:pos_urban_piper.model_pos_payment_method
msgid "Point of Sale Payment Methods"
msgstr "Pardavimo taško mokėjimo būdai"

#. module: pos_urban_piper
#: model:ir.model,name:pos_urban_piper.model_pos_payment
msgid "Point of Sale Payments"
msgstr "Pardavimų Taško Mokėjimai"

#. module: pos_urban_piper
#: model:ir.model,name:pos_urban_piper.model_pos_session
msgid "Point of Sale Session"
msgstr "Pardavimo taško sesija"

#. module: pos_urban_piper
#: model:ir.model.fields,help:pos_urban_piper.field_pos_config__urbanpiper_store_identifier
msgid "Pos ID from Urban Piper (Atlas)"
msgstr ""

#. module: pos_urban_piper
#. odoo-javascript
#: code:addons/pos_urban_piper/static/src/point_of_sale_overrirde/app/order_info_popup/order_info_popup.js:0
msgid "Prepaid"
msgstr ""

#. module: pos_urban_piper
#: model:ir.model,name:pos_urban_piper.model_pos_preparation_display_order
msgid "Preparation orders"
msgstr ""

#. module: pos_urban_piper
#: model:ir.model.fields,help:pos_urban_piper.field_pos_order__prep_time
msgid "Preparation time for the food as provided by UrbanPiper."
msgstr ""

#. module: pos_urban_piper
#: model:ir.model,name:pos_urban_piper.model_product_pricelist
#: model_terms:ir.ui.view,arch_db:pos_urban_piper.res_config_settings_view_form_pos_urban_piper
msgid "Pricelist"
msgstr "Kainoraštis"

#. module: pos_urban_piper
#: model:ir.model.fields,help:pos_urban_piper.field_pos_config__urbanpiper_pricelist_id
msgid "Pricelist for Urban Piper sync menu."
msgstr ""

#. module: pos_urban_piper
#: model:ir.model,name:pos_urban_piper.model_product_template
#: model:ir.model.fields,field_description:pos_urban_piper.field_product_urban_piper_status__product_tmpl_id
msgid "Product"
msgstr "Produktas"

#. module: pos_urban_piper
#: model:ir.model.fields,help:pos_urban_piper.field_product_urban_piper_status__is_product_linked
msgid "Product Status on Urban piper."
msgstr ""

#. module: pos_urban_piper
#. odoo-javascript
#: code:addons/pos_urban_piper/static/src/point_of_sale_overrirde/override/screens/ticket_screen/ticket_screen.js:0
msgid "Product is out of Stock"
msgstr ""

#. module: pos_urban_piper
#: model:ir.model.fields,help:pos_urban_piper.field_product_product__urbanpiper_meal_type
#: model:ir.model.fields,help:pos_urban_piper.field_product_template__urbanpiper_meal_type
msgid "Product type i.e. Veg, Non-Veg, etc."
msgstr ""

#. module: pos_urban_piper
#: model:ir.model.fields,field_description:pos_urban_piper.field_pos_delivery_provider__image_128
msgid "Provider Image"
msgstr ""

#. module: pos_urban_piper
#: model:ir.model.fields,field_description:pos_urban_piper.field_pos_delivery_provider__technical_name
msgid "Provider Name"
msgstr ""

#. module: pos_urban_piper
#: model:ir.model.constraint,message:pos_urban_piper.constraint_pos_delivery_provider_technical_name_uniq
msgid "Provider Name must be unique."
msgstr ""

#. module: pos_urban_piper
#: model:ir.model.fields,help:pos_urban_piper.field_product_product__is_recommended_on_urbanpiper
#: model:ir.model.fields,help:pos_urban_piper.field_product_template__is_recommended_on_urbanpiper
msgid "Recommended products on food platforms."
msgstr ""

#. module: pos_urban_piper
#: model_terms:ir.ui.view,arch_db:pos_urban_piper.res_config_settings_view_form_pos_urban_piper
msgid "Refresh webhooks"
msgstr ""

#. module: pos_urban_piper
#: model:ir.model.fields,field_description:pos_urban_piper.field_pos_config__urbanpiper_webhook_url
msgid "Register Urbanpiper Webhook URL"
msgstr ""

#. module: pos_urban_piper
#: model:ir.model.fields,help:pos_urban_piper.field_res_config_settings__pos_urbanpiper_webhook_url
msgid "Register webhook with Urbanpiper."
msgstr ""

#. module: pos_urban_piper
#. odoo-javascript
#: code:addons/pos_urban_piper/static/src/point_of_sale_overrirde/override/screens/ticket_screen/ticket_screen.xml:0
msgid "Reject"
msgstr "Atmesti"

#. module: pos_urban_piper
#. odoo-javascript
#: code:addons/pos_urban_piper/static/src/point_of_sale_overrirde/override/screens/ticket_screen/ticket_screen.js:0
msgid "Reject Order"
msgstr ""

#. module: pos_urban_piper
#. odoo-javascript
#: code:addons/pos_urban_piper/static/src/point_of_sale_overrirde/override/screens/ticket_screen/ticket_screen.js:0
msgid "Rejecting this order is not allowed for \"%(providerName)s\""
msgstr ""

#. module: pos_urban_piper
#: model:ir.model.fields,help:pos_urban_piper.field_pos_order__delivery_provider_id
#: model:ir.model.fields,help:pos_urban_piper.field_pos_payment_method__delivery_provider_id
msgid ""
"Responsible delivery provider for online order, e.g., UberEats, Zomato."
msgstr ""

#. module: pos_urban_piper
#. odoo-javascript
#: code:addons/pos_urban_piper/static/src/point_of_sale_overrirde/override/store/pos_store.js:0
msgid "Review Orders"
msgstr ""

#. module: pos_urban_piper
#. odoo-javascript
#: code:addons/pos_urban_piper/static/src/point_of_sale_overrirde/override/screens/ticket_screen/ticket_screen.js:0
msgid "Rider is Not Available"
msgstr ""

#. module: pos_urban_piper
#: model_terms:ir.ui.view,arch_db:pos_urban_piper.res_config_settings_view_form_pos_urban_piper
msgid "Set the POS ID in Urban Piper in Locations."
msgstr ""

#. module: pos_urban_piper
#: model:ir.model.fields,help:pos_urban_piper.field_pos_delivery_provider__journal_code
msgid "Short code of the journal to be used for journal creation"
msgstr ""

#. module: pos_urban_piper
#. odoo-javascript
#: code:addons/pos_urban_piper/static/src/point_of_sale_overrirde/app/order_info_popup/order_info_popup.js:0
msgid "Simpl (Deferred Payment)"
msgstr ""

#. module: pos_urban_piper
#. odoo-javascript
#: code:addons/pos_urban_piper/static/src/point_of_sale_overrirde/app/order_info_popup/order_info_popup.js:0
msgid "Status"
msgstr "Būsena"

#. module: pos_urban_piper
#: model:ir.model.fields,help:pos_urban_piper.field_pos_order__delivery_status
msgid "Status of the order as provided by UrbanPiper."
msgstr ""

#. module: pos_urban_piper
#: model:ir.model.constraint,message:pos_urban_piper.constraint_pos_config_urbanpiper_store_identifier_uniq
msgid "Store ID must be unique for every pos configuration."
msgstr ""

#. module: pos_urban_piper
#. odoo-javascript
#: code:addons/pos_urban_piper/static/src/point_of_sale_overrirde/override/screens/ticket_screen/ticket_screen.js:0
msgid "Store is Busy"
msgstr ""

#. module: pos_urban_piper
#. odoo-javascript
#: code:addons/pos_urban_piper/static/src/point_of_sale_overrirde/override/screens/ticket_screen/ticket_screen.js:0
msgid "Store is Closed"
msgstr ""

#. module: pos_urban_piper
#: model:ir.model.fields,help:pos_urban_piper.field_pos_config__urbanpiper_webhook_url
msgid "Store webhook url (base url) for security."
msgstr ""

#. module: pos_urban_piper
#: model:ir.model.fields,field_description:pos_urban_piper.field_product_product__urban_piper_status_ids
#: model:ir.model.fields,field_description:pos_urban_piper.field_product_template__urban_piper_status_ids
msgid "Stores"
msgstr ""

#. module: pos_urban_piper
#: model_terms:ir.ui.view,arch_db:pos_urban_piper.res_config_settings_view_form_pos_urban_piper
msgid "Sync Menu"
msgstr ""

#. module: pos_urban_piper
#: model:ir.model,name:pos_urban_piper.model_ir_config_parameter
msgid "System Parameter"
msgstr "Sistemos parametrai"

#. module: pos_urban_piper
#. odoo-javascript
#: code:addons/pos_urban_piper/static/src/point_of_sale_overrirde/app/order_info_popup/order_info_popup.js:0
msgid "Talabat Code"
msgstr ""

#. module: pos_urban_piper
#. odoo-javascript
#: code:addons/pos_urban_piper/static/src/point_of_sale_overrirde/app/order_info_popup/order_info_popup.js:0
msgid "Talabat Short Code"
msgstr ""

#. module: pos_urban_piper
#: model:ir.model.fields,help:pos_urban_piper.field_pos_delivery_provider__technical_name
msgid "Technical name of the provider used by UrbanPiper"
msgstr ""

#. module: pos_urban_piper
#: model:ir.model.fields,help:pos_urban_piper.field_res_config_settings__urbanpiper_apikey
msgid "The API key for accessing the UrbanPiper services."
msgstr ""

#. module: pos_urban_piper
#: model:ir.model.fields,help:pos_urban_piper.field_res_config_settings__pos_urbanpiper_store_identifier
msgid "The POS ID associated with UrbanPiper."
msgstr ""

#. module: pos_urban_piper
#: model:ir.model.fields,help:pos_urban_piper.field_res_config_settings__pos_urbanpiper_last_sync_date
msgid "The date and time of the last synchronization with UrbanPiper."
msgstr ""

#. module: pos_urban_piper
#: model:ir.model.fields,help:pos_urban_piper.field_pos_config__urbanpiper_delivery_provider_ids
#: model:ir.model.fields,help:pos_urban_piper.field_res_config_settings__pos_urbanpiper_delivery_provider_ids
msgid "The delivery providers used for online delivery through UrbanPiper."
msgstr ""

#. module: pos_urban_piper
#: model:ir.model.fields,help:pos_urban_piper.field_res_config_settings__pos_urbanpiper_fiscal_position_id
msgid "The fiscal position used for UrbanPiper transactions."
msgstr ""

#. module: pos_urban_piper
#: model:ir.model.fields,help:pos_urban_piper.field_res_config_settings__pos_urbanpiper_payment_methods_ids
msgid "The payment methods used for online delivery through UrbanPiper."
msgstr ""

#. module: pos_urban_piper
#: model:ir.model.fields,help:pos_urban_piper.field_res_config_settings__pos_urbanpiper_pricelist_id
msgid "The pricelist used for UrbanPiper orders."
msgstr ""

#. module: pos_urban_piper
#: model:ir.model.fields,help:pos_urban_piper.field_res_config_settings__urbanpiper_username
msgid "The username for the UrbanPiper account."
msgstr ""

#. module: pos_urban_piper
#. odoo-javascript
#: code:addons/pos_urban_piper/static/src/point_of_sale_overrirde/app/delivery_button/delivery_button.xml:0
msgid "There are no active order(s)."
msgstr ""

#. module: pos_urban_piper
#. odoo-javascript
#: code:addons/pos_urban_piper/static/src/point_of_sale_overrirde/override/screens/ticket_screen/ticket_screen.js:0
msgid "Total Missmatch"
msgstr ""

#. module: pos_urban_piper
#: model:ir.model.fields,help:pos_urban_piper.field_pos_order__delivery_identifier
msgid "Unique delivery ID provided by UrbanPiper."
msgstr ""

#. module: pos_urban_piper
#. odoo-python
#: code:addons/pos_urban_piper/models/pos_config.py:0
#: model_terms:ir.ui.view,arch_db:pos_urban_piper.product_template_only_form_view_inherit_pos_urban_piper
msgid "Urban Piper"
msgstr ""

#. module: pos_urban_piper
#: model:ir.model.fields,field_description:pos_urban_piper.field_product_urban_piper_status__config_id
msgid "Urban Piper Config"
msgstr ""

#. module: pos_urban_piper
#: model:ir.model.fields,field_description:pos_urban_piper.field_pos_config__urbanpiper_fiscal_position_id
msgid "Urban Piper Fiscal Position"
msgstr ""

#. module: pos_urban_piper
#: model_terms:ir.ui.view,arch_db:pos_urban_piper.res_config_settings_view_form_pos_urban_piper
msgid "Urban Piper Location"
msgstr ""

#. module: pos_urban_piper
#: model:ir.model.fields,field_description:pos_urban_piper.field_pos_config__urbanpiper_store_identifier
msgid "Urban Piper POS ID"
msgstr ""

#. module: pos_urban_piper
#: model:ir.model.fields,field_description:pos_urban_piper.field_pos_config__urbanpiper_payment_methods_ids
msgid "Urban Piper Payment Methods"
msgstr ""

#. module: pos_urban_piper
#: model:ir.model.fields,field_description:pos_urban_piper.field_pos_config__urbanpiper_pricelist_id
msgid "Urban Piper Pricelist"
msgstr ""

#. module: pos_urban_piper
#: model:ir.model.fields,field_description:pos_urban_piper.field_res_config_settings__pos_urbanpiper_webhook_url
msgid "Urban Piper Webhook URL"
msgstr ""

#. module: pos_urban_piper
#: model:ir.model,name:pos_urban_piper.model_product_urban_piper_status
msgid "Urban piper product status"
msgstr ""

#. module: pos_urban_piper
#: model:ir.model.fields,field_description:pos_urban_piper.field_res_config_settings__urbanpiper_apikey
msgid "UrbanPiper API Key"
msgstr ""

#. module: pos_urban_piper
#. odoo-python
#: code:addons/pos_urban_piper/models/pos_config.py:0
msgid "UrbanPiper API Key is required.\n"
msgstr ""

#. module: pos_urban_piper
#. odoo-python
#: code:addons/pos_urban_piper/models/res_config_settings.py:0
msgid "UrbanPiper Delivery Providers are required."
msgstr ""

#. module: pos_urban_piper
#: model:ir.model.fields,field_description:pos_urban_piper.field_res_config_settings__pos_urbanpiper_fiscal_position_id
msgid "UrbanPiper Fiscal Position"
msgstr ""

#. module: pos_urban_piper
#: model:ir.model.fields,field_description:pos_urban_piper.field_res_config_settings__pos_urbanpiper_pricelist_id
msgid "UrbanPiper Pricelist"
msgstr ""

#. module: pos_urban_piper
#. odoo-python
#: code:addons/pos_urban_piper/models/pos_config.py:0
msgid "UrbanPiper Store ID is required.\n"
msgstr ""

#. module: pos_urban_piper
#: model:ir.model.fields,field_description:pos_urban_piper.field_res_config_settings__pos_urbanpiper_store_identifier
msgid "UrbanPiper Store Identifier"
msgstr ""

#. module: pos_urban_piper
#. odoo-python
#: code:addons/pos_urban_piper/models/pos_config.py:0
msgid "UrbanPiper Username is required.\n"
msgstr ""

#. module: pos_urban_piper
#: model:account.fiscal.position,name:pos_urban_piper.pos_account_fiscal_position_urbanpiper
msgid "Urbanpiper"
msgstr ""

#. module: pos_urban_piper
#: model:ir.model.fields,field_description:pos_urban_piper.field_res_config_settings__urbanpiper_username
msgid "UrbanPiper Username"
msgstr ""

#. module: pos_urban_piper
#: model_terms:ir.ui.view,arch_db:pos_urban_piper.res_config_settings_view_form_pos_urban_piper
msgid "Username"
msgstr "Vartotojo vardas"

#. module: pos_urban_piper
#. odoo-javascript
#: code:addons/pos_urban_piper/static/src/point_of_sale_overrirde/override/screens/ticket_screen/ticket_screen.js:0
msgid "Variants/Addons out of Stock"
msgstr ""

#. module: pos_urban_piper
#: model:ir.model.fields.selection,name:pos_urban_piper.selection__product_template__urbanpiper_meal_type__1
msgid "Vegetarian"
msgstr ""

#. module: pos_urban_piper
#. odoo-javascript
#: code:addons/pos_urban_piper/static/src/point_of_sale_overrirde/app/order_info_popup/order_info_popup.js:0
msgid "Wallet Credit"
msgstr ""

#. module: pos_urban_piper
#. odoo-python
#: code:addons/pos_urban_piper/models/ir_config_parameter.py:0
msgid "You cannot change the pos_urban_piper.uuid."
msgstr ""

#. module: pos_urban_piper
#. odoo-python
#: code:addons/pos_urban_piper/models/res_config_settings.py:0
msgid ""
"Your company %s needs to have a correct city in order create the store."
msgstr ""
