# Softprime Consulting Pvt Ltd
# Copyright (C) Softprime Consulting Pvt Ltd
# All Rights Reserved
# https://softprimeconsulting.com/
from odoo import _, api, fields, models
from odoo.exceptions import UserError, ValidationError


class WaterElectricityBillLine(models.Model):
    _name = "water.electricity.bill.line"
    _description = "Water Electricity Bill Line"

    bill_id = fields.Many2one("water.electricity.bill", string="Bill Reference")
    contract_id = fields.Many2one("res.tenant.contract", string="Contract")
    property_name = fields.Char(string="Property Name")
    floor = fields.Char(string="Floor")
    meter_code = fields.Char(string="Meter Code")
    electricity_type = fields.Selection(
        [("single_phase", "Single Phase Electricity"), ("three_phase", "Three-Phase Electricity")],
        string='Electricity Type')
    currency_id = fields.Many2one("res.currency", string="Currency", required=True,
                                  default=lambda self: self.env.user.company_id.currency_id)
    company_id = fields.Many2one("res.company", string="Company", required=True, default=lambda self: self.env.company)
    last_meter_reading = fields.Float(string="Last Meter Reading", digits=(4, 3))
    current_meter_reading = fields.Float(string="Current Meter Reading", digits=(4, 3))
    unit_consumed = fields.Float(string="Unit Consumed", compute="_compute_unit_consumed", store=True, digits=(4, 3))
    rudus = fields.Float(string="KW RUDUS")
    total_rudus = fields.Float(string="Total RUDUS", compute="_compute_total_rudus")
    rate = fields.Float(string="Rate")
    is_current_month = fields.Boolean(copy=False, string="Current Month")
    total_amount = fields.Float(string="Total Amount", compute="_compute_total_amount", store=True)
    month = fields.Selection(
        [
            ("january", "January"),
            ("february", "February"),
            ("march", "March"),
            ("april", "April"),
            ("may", "May"),
            ("june", "June"),
            ("july", "July"),
            ("august", "August"),
            ("september", "September"),
            ("october", "October"),
            ("november", "November"),
            ("december", "December"),
        ],
        string="Month"
    )
    reason = fields.Char(string="Reason")
    remaining_amount = fields.Float(string="Remaining Amount", compute="_compute_remaining_amount", store=True)
    bill_state = fields.Selection(related="bill_id.state", string="Status", readonly=True, store=False)

    @api.depends("total_amount", "bill_id.payment_amount")
    def _compute_remaining_amount(self):
        """Compute the remaining amount based on the total amount and proportional payment."""
        for line in self:
            if line.bill_id and line.bill_id.subtotal:
                proportion = line.total_amount / line.bill_id.subtotal if line.bill_id.subtotal else 0
                line.remaining_amount = line.total_amount - (line.bill_id.payment_amount * proportion)
            else:
                line.remaining_amount = line.total_amount

    @api.onchange("current_meter_reading")
    def _onchange_current_meter_reading(self):
        if self.current_meter_reading < self.last_meter_reading:
            raise ValidationError(_("Current meter reading cannot be less than the last meter reading."))

    @api.depends("rudus", "unit_consumed")
    def _compute_total_rudus(self):
        """Compute total rudus"""
        for rec in self:
            rec.total_rudus = rec.rudus * rec.unit_consumed

    @api.onchange("company_id")
    def _onchange_company_id(self):
        if self.company_id:
            self.rate = self.company_id.electricity_rate_per_unit

    @api.depends("last_meter_reading", "current_meter_reading")
    def _compute_unit_consumed(self):
        for rec in self:
            rec.unit_consumed = max(0, rec.current_meter_reading - rec.last_meter_reading)

    @api.onchange("contract_id")
    def _onchange_contract_id(self):
        if self.contract_id:
            self.property_name = self.contract_id.property_id.name
            self.floor = self.contract_id.floor
            self.meter_code = self.contract_id.meter_code

    @api.depends("unit_consumed", "rate", "rate", "rudus", "electricity_type")
    def _compute_total_amount(self):
        """returns total of consumed with per rate"""
        for rec in self:
            if rec.electricity_type == "single_phase":
                rec.total_amount = rec.unit_consumed * rec.rate
            else:
                rec.total_amount = (rec.unit_consumed * rec.rate) * rec.rudus


class WaterElectricityBillWaterLine(models.Model):
    _name = "water.electricity.bill.water.line"
    _description = "Water Electricity Bill Water Line"

    bill_id = fields.Many2one("water.electricity.bill", string="Bill")
    contract_id = fields.Many2one("res.tenant.contract", string="Contract")
    property_name = fields.Char(string="Property Name")
    floor = fields.Char(string="Floor")
    meter_code = fields.Char(string="Meter Code")
    last_meter_reading = fields.Float(string="Last Meter Reading", digits=(4, 3))
    current_meter_reading = fields.Float(string="Current Meter Reading", digits=(4, 3))
    unit_consumed = fields.Float(string="Unit Consumed", compute="_compute_unit_consumeds", store=True, digits=(4, 3))
    rate = fields.Float(string="Rate")
    total_amount = fields.Float(string="Total Amount", compute="_compute_total_amounts", store=True)
    currency_id = fields.Many2one("res.currency", string="Currency", required=True,
                                  default=lambda self: self.env.user.company_id.currency_id)
    company_id = fields.Many2one("res.company", string="Company", required=True, default=lambda self: self.env.company)
    remaining_amounts = fields.Float(string="Remaining Amount", compute="_compute_water_remaining_amount", store=True)
    bill_status = fields.Selection(related="bill_id.state", string="Status", readonly=True, store=False)

    @api.depends("total_amount", "bill_id.payment_amount")
    def _compute_water_remaining_amount(self):
        """Compute the remaining amount based on the total amount and proportional payment."""
        for line in self:
            if line.bill_id and line.bill_id.subtotal:
                proportion = line.total_amount / line.bill_id.subtotal if line.bill_id.subtotal else 0
                line.remaining_amounts = line.total_amount - (line.bill_id.payment_amount * proportion)
            else:
                line.remaining_amounts = line.total_amount

    @api.onchange("current_meter_reading")
    def _onchange_current_meter_reading(self):
        if self.current_meter_reading < self.last_meter_reading:
            raise ValidationError(_("Current meter reading cannot be less than the last meter reading."))

    @api.onchange("company_id")
    def _onchange_company_id(self):
        if self.company_id:
            self.rate = self.company_id.water_rate_per_unit

    @api.depends("unit_consumed", "rate")
    def _compute_total_amounts(self):
        """returns total of consumed with per rate"""
        for rec in self:
            rec.total_amount = rec.unit_consumed * rec.rate

    @api.depends("last_meter_reading", "current_meter_reading")
    def _compute_unit_consumeds(self):
        for rec in self:
            rec.unit_consumed = max(0, rec.current_meter_reading - rec.last_meter_reading)
