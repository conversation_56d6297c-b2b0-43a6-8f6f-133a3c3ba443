# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_reports
#
# Translators:
# <PERSON> <brenci<PERSON><PERSON><EMAIL>>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON> <jan.<PERSON><PERSON><PERSON>@centrum.cz>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON>, 2022
# <PERSON>, 2022
# ka<PERSON><PERSON><PERSON>chus<PERSON> <karol<PERSON>.schustero<PERSON>@vdp.sk>, 2023
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON><PERSON><PERSON>, 2023
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-05-16 16:02+0000\n"
"PO-Revision-Date: 2022-09-22 05:45+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2023\n"
"Language-Team: Czech (https://app.transifex.com/odoo/teams/41243/cs/)\n"
"Language: cs\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=4; plural=(n == 1 && n % 1 == 0) ? 0 : (n >= 2 && n <= 4 && n % 1 == 0) ? 1: (n % 1 != 0 ) ? 2 : 3;\n"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid " and %s others"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid " and one other"
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/bank_reconciliation_report/account_report.xml:0
msgid "\" account balance is affected by"
msgstr "\"zůstatek účtu je ovlivněn"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:0
msgid "%s for Transactions(+) imported from your online bank account (dated today) that are not yet reconciled in Odoo (Waiting the final reconciliation allowing finding the right account)"
msgstr "%spro transakce (+) importované z vašeho online bankovního účtu (datováno dnes), které ještě nejsou odsouhlaseny v Odoo (čekání na konečné párování umožňující nalezení správného účtu)"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:0
msgid "%s for Transactions(-) imported from your online bank account (dated today) that are not yet reconciled in Odoo (Waiting the final reconciliation allowing finding the right account)"
msgstr "%s pro transakce (-) importované z vašeho online bankovního účtu (datováno dnes), které ještě nejsou odsouhlaseny v Odoo (čekání na konečné párování umožňující nalezení správného účtu)"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "%s is not a numeric value"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "'Open General Ledger' caret option is only available form report lines targetting accounts."
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "'View Bank Statement' caret option is only available for report lines targeting bank statements."
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "'external' engine does not support groupby, limit nor offset."
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "(%s lines)"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:0
msgid "(+) Outstanding Receipts"
msgstr "(+) Neuhrazené příjmy"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:0
msgid "(-) Outstanding Payments"
msgstr "(-) Neuhrazené platby"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "(1 line)"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "(No Group)"
msgstr "(žádná skupina)"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.journal_report_pdf_export_filters
msgid ", Grouped by month"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.journal_report_pdf_export_filters
msgid ", Sorted by date"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.journal_report_pdf_export_filters
msgid ", Sorted by number"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_move_form_vat_return
msgid "-> Check your tax report"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_move_form_vat_return
msgid "-> Refresh"
msgstr "-> Obnovit"

#. module: account_reports
#: model:account.report.column,name:account_reports.aged_payable_report_period1
#: model:account.report.column,name:account_reports.aged_receivable_report_period1
msgid "1-30"
msgstr ""

#. module: account_reports
#: model:account.report.column,name:account_reports.aged_payable_report_period2
#: model:account.report.column,name:account_reports.aged_receivable_report_period2
msgid "31-60"
msgstr ""

#. module: account_reports
#: model:account.report.column,name:account_reports.aged_payable_report_period3
#: model:account.report.column,name:account_reports.aged_receivable_report_period3
msgid "61-90"
msgstr ""

#. module: account_reports
#: model:account.report.column,name:account_reports.aged_payable_report_period4
#: model:account.report.column,name:account_reports.aged_receivable_report_period4
msgid "91-120"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.pdf_export_filters
msgid "<br/>Companies:"
msgstr "<br/>Společnosti:"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.pdf_export_main
msgid "<span class=\"o_account_reports_footnote_icons\"><i class=\"fa fa-fw fa-trash-o\" role=\"img\" aria-label=\"Delete\" title=\"Delete\"/></span>"
msgstr "<span class=\"o_account_reports_footnote_icons\"><i class=\"fa fa-fw fa-trash-o\" role=\"img\" aria-label=\"Delete\" title=\"Delete\"/></span>"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.partner_view_buttons
msgid "<span class=\"o_stat_text\">Partner Ledger</span>"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.journal_report_pdf_export_line
msgid "=&gt; Audit"
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/journal_report/line/line.xml:0
msgid "=> Audit"
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/journal_report/line_name.xml:0
msgid "=> Journal Items"
msgstr ""

#. module: account_reports
#: model:ir.model.constraint,message:account_reports.constraint_account_report_horizontal_group_name_uniq
msgid "A horizontal group with the same name already exists."
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_tax.py:0
msgid "A tax unit can only be created between companies sharing the same main currency."
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_tax.py:0
msgid "A tax unit must contain a minimum of two companies. You might want to delete the unit."
msgstr ""

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_total_assets0
msgid "ASSETS"
msgstr "MAJETEK"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_journal_report.py:0
#: code:addons/account_reports/wizard/multicurrency_revaluation.py:0
#: model:account.report.column,name:account_reports.aged_payable_report_account_name
#: model:account.report.column,name:account_reports.aged_receivable_report_account_name
#: model:account.report.column,name:account_reports.journal_report_communication
#: model:account.report.column,name:account_reports.partner_ledger_report_account_code
#: model:ir.model,name:account_reports.model_account_account
msgid "Account"
msgstr "Účet"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_chart_template
msgid "Account Chart Template"
msgstr "Šablona schématu účtu"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Account Code"
msgstr "Kód účtu"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_res_company__account_display_representative_field
msgid "Account Display Representative Field"
msgstr ""

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_report_custom_handler
msgid "Account Report Custom Handler"
msgstr ""

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_report_footnote
msgid "Account Report Footnote"
msgstr "Poznámka k poznámce k účtu"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_tax_report_handler
msgid "Account Report Handler for Tax Reports"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_res_partner__account_represented_company_ids
#: model:ir.model.fields,field_description:account_reports.field_res_users__account_represented_company_ids
msgid "Account Represented Company"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_res_company__account_revaluation_journal_id
msgid "Account Revaluation Journal"
msgstr "Deník přecenění účtu"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_account_type.xml:0
msgid "Account:"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_res_company__account_representative_id
msgid "Accounting Firm"
msgstr ""

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_report
msgid "Accounting Report"
msgstr "Účetní sestava"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_report_line
msgid "Accounting Report Line"
msgstr "Řádek účetní sestavy"

#. module: account_reports
#: model:ir.actions.act_window,name:account_reports.action_account_report_tree
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_tree
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_report_search
msgid "Accounting Reports"
msgstr "Reporty Účetnictví"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_analytic.xml:0
#: code:addons/account_reports/static/src/components/account_report/filters/filter_analytic_groupby.xml:0
msgid "Accounts"
msgstr "Účty"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_form
msgid "Accounts Coverage Report"
msgstr ""

#. module: account_reports
#: model:account.report.line,name:account_reports.multicurrency_revaluation_to_adjust
msgid "Accounts To Adjust"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Accounts coverage"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_mail_activity_type__category
msgid "Action"
msgstr "Akce"

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_mail_activity_type__category
msgid "Actions may trigger specific behavior like opening calendar view or automatically mark as done when a document is uploaded"
msgstr "Akce mohou vyvolat určité chování, jako je otevření zobrazení kalendáře, nebo se automaticky označí jako provedené při nahrávání dokumentu"

#. module: account_reports
#: model:ir.model,name:account_reports.model_mail_activity
msgid "Activity"
msgstr "Činnost"

#. module: account_reports
#: model:ir.model,name:account_reports.model_mail_activity_type
msgid "Activity Type"
msgstr "Typ činnosti"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_res_company__totals_below_sections
#: model:ir.model.fields,field_description:account_reports.field_res_config_settings__totals_below_sections
msgid "Add totals below sections"
msgstr "Přidat součty pod sekcemi"

#. module: account_reports
#: model:account.report.column,name:account_reports.multicurrency_revaluation_report_adjustment
msgid "Adjustment"
msgstr "Nastavení"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_multicurrency_revaluation_report.py:0
msgid "Adjustment Entry"
msgstr "Zadání úpravy"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_cash_flow_report.py:0
msgid "Advance Payments received from customers"
msgstr "Zálohy přijaté od partnerů"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_cash_flow_report.py:0
msgid "Advance payments made to suppliers"
msgstr "Zálohy zaplacené dodavatelům"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_form
msgid "Advanced"
msgstr "Pokročilý"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_aged_partner_balance_report_handler
msgid "Aged Partner Balance Custom Handler"
msgstr ""

#. module: account_reports
#: model:account.report,name:account_reports.aged_payable_report
#: model:account.report.line,name:account_reports.aged_payable_line
#: model:ir.actions.client,name:account_reports.action_account_report_ap
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_aged_payable
msgid "Aged Payable"
msgstr "Ve věku splatné"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_aged_payable_report_handler
msgid "Aged Payable Custom Handler"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_reports_journal_dashboard_kanban_view
msgid "Aged Payables"
msgstr "Věkové závazky"

#. module: account_reports
#: model:account.report,name:account_reports.aged_receivable_report
#: model:account.report.line,name:account_reports.aged_receivable_line
#: model:ir.actions.client,name:account_reports.action_account_report_ar
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_aged_receivable
msgid "Aged Receivable"
msgstr "Věková struktura pohledávek"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_aged_receivable_report_handler
msgid "Aged Receivable Custom Handler"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_reports_journal_dashboard_kanban_view
msgid "Aged Receivables"
msgstr "Věková struktura pohledávek"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_fiscal_position.xml:0
#: model_terms:ir.ui.view,arch_db:account_reports.pdf_export_filters
msgid "All"
msgstr "Vše"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "All Journals"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "All Payable"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "All Receivable"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "All receivable/payable"
msgstr ""

#. module: account_reports
#: model:account.report.column,name:account_reports.account_financial_report_ec_sales_amount
#: model:account.report.column,name:account_reports.bank_reconciliation_report_amount
msgid "Amount"
msgstr "Částka"

#. module: account_reports
#: model:account.report.column,name:account_reports.aged_payable_report_amount_currency
#: model:account.report.column,name:account_reports.aged_receivable_report_amount_currency
#: model:account.report.column,name:account_reports.bank_reconciliation_report_amount_currency
#: model:account.report.column,name:account_reports.partner_ledger_report_amount_currency
msgid "Amount Currency"
msgstr "Měna množství"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_journal_report.py:0
msgid "Amount In Currency"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_journal_report.py:0
msgid "Amount in currency: %s"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.pdf_export_filters
msgid "Analytic Accounts:"
msgstr "Analytické účty:"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_analytic.xml:0
msgid "Analytic Filter"
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_analytic_groupby.xml:0
#: model:ir.model.fields,field_description:account_reports.field_account_report__filter_analytic_groupby
msgid "Analytic Group By"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.pdf_export_filters
msgid "Analytic Plans:"
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/footnote/dialog/footnote_dialog.js:0
#: code:addons/account_reports/static/src/components/account_report/line_name/line_name.xml:0
msgid "Annotate"
msgstr "Opatřit poznámkami"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_comparison.xml:0
#: code:addons/account_reports/static/src/components/account_report/filters/filter_date.xml:0
#: code:addons/account_reports/static/src/components/multicurrency_revaluation_report/filters/filter_exchange_rate.xml:0
msgid "Apply"
msgstr "Použít"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "As of %s"
msgstr "K %s"

#. module: account_reports
#: model:account.report.column,name:account_reports.aged_payable_report_period0
#: model:account.report.column,name:account_reports.aged_receivable_report_period0
msgid "At Date"
msgstr "V datum"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_generic_tax_report.py:0
msgid "Audit"
msgstr "Audit"

#. module: account_reports
#: model:ir.ui.menu,name:account_reports.account_reports_audit_reports_menu
msgid "Audit Reports"
msgstr "Auditní Reporty"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_avgcre0
msgid "Average creditors days"
msgstr "Průměrné dny věřitelů"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_avdebt0
msgid "Average debtors days"
msgstr "Průměrné dny dlužníků"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_journal_report.py:0
msgid "B: %s"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_journal_report.py:0
#: model:account.report.column,name:account_reports.balance_sheet_balance
#: model:account.report.column,name:account_reports.cash_flow_report_balance
#: model:account.report.column,name:account_reports.executive_summary_column
#: model:account.report.column,name:account_reports.general_ledger_report_balance
#: model:account.report.column,name:account_reports.partner_ledger_report_balance
#: model:account.report.column,name:account_reports.profit_and_loss_column
msgid "Balance"
msgstr "Zůstatek"

#. module: account_reports
#: model:account.report,name:account_reports.balance_sheet
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_balancesheet0
#: model:ir.actions.client,name:account_reports.action_account_report_bs
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_balance_sheet
msgid "Balance Sheet"
msgstr "Rozvaha"

#. module: account_reports
#: model:account.report.column,name:account_reports.multicurrency_revaluation_report_balance_current
msgid "Balance at Current Rate"
msgstr ""

#. module: account_reports
#: model:account.report.column,name:account_reports.multicurrency_revaluation_report_balance_operation
msgid "Balance at Operation Rate"
msgstr ""

#. module: account_reports
#: model:account.report.column,name:account_reports.multicurrency_revaluation_report_balance_currency
msgid "Balance in Foreign Currency"
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/aged_partner_balance/line_name/line_name.xml:0
msgid "Balance is bad"
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/aged_partner_balance/line_name/line_name.xml:0
msgid "Balance is good"
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/aged_partner_balance/line_name/line_name.xml:0
msgid "Balance is normal"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:0
msgid "Balance of %s"
msgstr "Zůstatek %s"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_generic_tax_report.py:0
msgid "Balance tax advance payment account"
msgstr "Účet zálohové platby zálohy na daň ze zůstatku"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_generic_tax_report.py:0
msgid "Balance tax current account (payable)"
msgstr "Běžný daňový zůstatek (splatný)"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_generic_tax_report.py:0
msgid "Balance tax current account (receivable)"
msgstr "Běžný účet se zůstatkovou daní (pohledávka)"

#. module: account_reports
#: model:ir.actions.client,name:account_reports.action_account_report_bank_reconciliation
msgid "Bank Reconciliation"
msgstr "Bankovní párování"

#. module: account_reports
#: model:account.report,name:account_reports.bank_reconciliation_report
msgid "Bank Reconciliation Report"
msgstr "Zpráva o bankovním párování"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_bank_reconciliation_report_handler
msgid "Bank Reconciliation Report Custom Handler"
msgstr ""

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_bank_view0
msgid "Bank and Cash Accounts"
msgstr "Bankovní a hotovostní účty"

#. module: account_reports
#: model:ir.model,name:account_reports.model_bank_rec_widget
msgid "Bank reconciliation widget for a single statement line"
msgstr ""

#. module: account_reports
#. odoo-python
#. odoo-javascript
#: code:addons/account_reports/models/account_general_ledger.py:0
#: code:addons/account_reports/static/src/components/journal_report/line/line.xml:0
#: model_terms:ir.ui.view,arch_db:account_reports.journal_report_pdf_export_line
msgid "Base Amount"
msgstr "Základní částka"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/footnote/dialog/footnote_dialog.xml:0
#: code:addons/account_reports/static/src/components/aged_partner_balance/dialog/expected_date_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_multicurrency_revaluation_wizard
#: model_terms:ir.ui.view,arch_db:account_reports.view_report_export_wizard
msgid "Cancel"
msgstr "Zrušit"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_generic_tax_report.py:0
msgid "Cannot audit tax from another model than account.tax."
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Cannot generate carryover values for all fiscal positions at once!"
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/line_cell/popover/carryover_popover.xml:0
msgid "Carryover"
msgstr "Přenos"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Carryover adjustment for tax unit"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Carryover can only be generated for a single column group."
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Carryover from %s to %s"
msgstr ""

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_cash0
msgid "Cash"
msgstr "Hotovost"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_cash_flow_report_handler
msgid "Cash Flow Report Custom Handler"
msgstr ""

#. module: account_reports
#: model:account.report,name:account_reports.cash_flow_report
#: model:ir.actions.client,name:account_reports.action_account_report_cs
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_cash_flow
msgid "Cash Flow Statement"
msgstr "Přehled o peněžních tocích"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_cash_flow_report.py:0
msgid "Cash and cash equivalents, beginning of period"
msgstr "Peněžní prostředky a peněžní ekvivalenty, začátek období"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_cash_flow_report.py:0
msgid "Cash and cash equivalents, closing balance"
msgstr "Peněžní prostředky a peněžní ekvivalenty, závěrečný zůstatek"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_cash_flow_report.py:0
msgid "Cash flows from financing activities"
msgstr "Peněžní toky z finančních činností"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_cash_flow_report.py:0
msgid "Cash flows from investing & extraordinary activities"
msgstr "Peněžní toky z investičních a mimořádných činností"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_cash_flow_report.py:0
msgid "Cash flows from operating activities"
msgstr "Peněžní toky z provozní činnosti"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_cash_flow_report.py:0
msgid "Cash flows from unclassified activities"
msgstr "Peněžní toky z neklasifikovaných činností"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_cash_flow_report.py:0
msgid "Cash in"
msgstr "Hotovost"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_cash_flow_report.py:0
msgid "Cash out"
msgstr "Proplatit"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_cash_flow_report.py:0
msgid "Cash paid for operating activities"
msgstr "Hotovost vyplácená za provozní činnosti"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_cash_received0
msgid "Cash received"
msgstr "Hotovost přijata"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_cash_flow_report.py:0
msgid "Cash received from operating activities"
msgstr "Peníze přijaté z provozních činností"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_cash_spent0
msgid "Cash spent"
msgstr "Vynaložené peníze"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_cash_surplus0
msgid "Cash surplus"
msgstr "Hotovostní přebytek"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_change_lock_date
msgid "Change Lock Date"
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/aged_partner_balance/line_name/line_name.js:0
msgid "Change expected date"
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/aged_partner_balance/line_name/line_name.xml:0
msgid "Change expected payment date"
msgstr "Změňte očekávané datum platby"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_generic_tax_report.py:0
msgid "Closing Entry"
msgstr ""

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_closing_bank_balance0
msgid "Closing bank balance"
msgstr "Uzavření bankovního zůstatku"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/sales_report/filters/filter_code.xml:0
msgid "Codes:"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_form
msgid "Columns"
msgstr "Sloupce"

#. module: account_reports
#: model:account.report.column,name:account_reports.general_ledger_report_communication
msgid "Communication"
msgstr "Komunikace"

#. module: account_reports
#: model:ir.model,name:account_reports.model_res_company
#: model:ir.model.fields,field_description:account_reports.field_account_tax_unit__company_ids
msgid "Companies"
msgstr "Společnosti"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard__company_id
msgid "Company"
msgstr "Firma"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_tax.py:0
msgid "Company %s already belongs to a tax unit in %s. A company can at most be part of one tax unit per country."
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.pdf_export_filters
msgid "Company Currency:"
msgstr "Měna společnosti:"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_tax_unit.xml:0
msgid "Company Only"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Company Settings"
msgstr "Nastavení společnosti"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_comparison.xml:0
msgid "Comparison:"
msgstr ""

#. module: account_reports
#: model:ir.model,name:account_reports.model_res_config_settings
msgid "Config Settings"
msgstr "Nastavení konfigurace"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_generic_tax_report.py:0
msgid "Configure your TAX accounts - %s"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.res_config_settings_view_form
msgid "Configure your tax accounts"
msgstr "Nakonfigurovat své daňové účty"

#. module: account_reports
#: model:ir.model,name:account_reports.model_res_partner
msgid "Contact"
msgstr "Kontakt"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_cost_sales0
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_direct_costs0
msgid "Cost of Revenue"
msgstr "Náklady na Příjmy"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Could not expand term %s while evaluating formula %s"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Could not parse account_code formula from token '%s'"
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/journal_report/line/line.xml:0
#: model:ir.model.fields,field_description:account_reports.field_account_tax_unit__country_id
#: model_terms:ir.ui.view,arch_db:account_reports.journal_report_pdf_export_line
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_report_search
msgid "Country"
msgstr "Stát"

#. module: account_reports
#: model:account.report.column,name:account_reports.account_financial_report_ec_sales_country
msgid "Country Code"
msgstr "Kód země"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_multicurrency_revaluation_wizard
msgid "Create Entry"
msgstr "Vytvořit záznam"

#. module: account_reports
#: model:ir.actions.server,name:account_reports.action_create_report_menu
msgid "Create Menu Item"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard__create_uid
#: model:ir.model.fields,field_description:account_reports.field_account_report_footnote__create_uid
#: model:ir.model.fields,field_description:account_reports.field_account_report_horizontal_group__create_uid
#: model:ir.model.fields,field_description:account_reports.field_account_report_horizontal_group_rule__create_uid
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard__create_uid
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard_format__create_uid
#: model:ir.model.fields,field_description:account_reports.field_account_tax_unit__create_uid
msgid "Created by"
msgstr "Vytvořeno od"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard__create_date
#: model:ir.model.fields,field_description:account_reports.field_account_report_footnote__create_date
#: model:ir.model.fields,field_description:account_reports.field_account_report_horizontal_group__create_date
#: model:ir.model.fields,field_description:account_reports.field_account_report_horizontal_group_rule__create_date
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard__create_date
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard_format__create_date
#: model:ir.model.fields,field_description:account_reports.field_account_tax_unit__create_date
msgid "Created on"
msgstr "Vytvořeno"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_journal_report.py:0
#: code:addons/account_reports/wizard/multicurrency_revaluation.py:0
#: model:account.report.column,name:account_reports.general_ledger_report_credit
#: model:account.report.column,name:account_reports.journal_report_debit
#: model:account.report.column,name:account_reports.partner_ledger_report_credit
#: model:account.report.column,name:account_reports.trial_balance_report_credit
msgid "Credit"
msgstr "Dal"

#. module: account_reports
#: model:account.report.column,name:account_reports.aged_payable_report_currency
#: model:account.report.column,name:account_reports.aged_receivable_report_currency
#: model:account.report.column,name:account_reports.bank_reconciliation_report_currency
#: model:account.report.column,name:account_reports.general_ledger_report_amount_currency
msgid "Currency"
msgstr "Měna"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_multicurrency_revaluation_report.py:0
msgid "Currency Rates (%s)"
msgstr "Měnové kurzy (%s)"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_current_assets0
#: model:account.report.line,name:account_reports.account_financial_report_current_assets_view0
msgid "Current Assets"
msgstr "Aktuální majetek"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_current_liabilities0
#: model:account.report.line,name:account_reports.account_financial_report_current_liabilities1
msgid "Current Liabilities"
msgstr "Běžné závazky"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_current_year_earnings_line_2
msgid "Current Year Allocated Earnings"
msgstr "Přidělené zisky běžného roku"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_current_year_earnings_line_1
msgid "Current Year Earnings"
msgstr "Zisk tohoto roku"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_current_year_earnings0
msgid "Current Year Unallocated Earnings"
msgstr "Nerozdělený zisk za běžný rok"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_ca_to_l0
msgid "Current assets to liabilities"
msgstr "Průběžný majetek do pasiv"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_comparison.xml:0
#: code:addons/account_reports/static/src/components/account_report/filters/filter_date.xml:0
msgid "Custom"
msgstr "Vlastní"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report__custom_handler_model_id
msgid "Custom Handler Model"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report__custom_handler_model_name
msgid "Custom Handler Model Name"
msgstr ""

#. module: account_reports
#: model:account.report.column,name:account_reports.bank_reconciliation_report_date
#: model:account.report.column,name:account_reports.general_ledger_report_date
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard__date
msgid "Date"
msgstr "Datum"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_comparison.xml:0
#: code:addons/account_reports/static/src/components/account_report/filters/filter_date.xml:0
msgid "Date :"
msgstr "Datum:"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filters.js:0
msgid "Date cannot be empty"
msgstr "Datum nemůže být prázdné"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_journal_report.py:0
#: code:addons/account_reports/wizard/multicurrency_revaluation.py:0
#: model:account.report.column,name:account_reports.general_ledger_report_debit
#: model:account.report.column,name:account_reports.journal_report_amount_currency
#: model:account.report.column,name:account_reports.partner_ledger_report_debit
#: model:account.report.column,name:account_reports.trial_balance_report_debit
msgid "Debit"
msgstr "Má dáti"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_expression_form
msgid "Definition"
msgstr "Definice"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_res_company__account_tax_periodicity
msgid "Delay units"
msgstr "Jednotky prodlení"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_depreciation0
msgid "Depreciation"
msgstr "Amortizace"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard__display_name
#: model:ir.model.fields,field_description:account_reports.field_account_report_footnote__display_name
#: model:ir.model.fields,field_description:account_reports.field_account_report_horizontal_group__display_name
#: model:ir.model.fields,field_description:account_reports.field_account_report_horizontal_group_rule__display_name
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard__display_name
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard_format__display_name
#: model:ir.model.fields,field_description:account_reports.field_account_tax_unit__display_name
msgid "Display Name"
msgstr "Zobrazované jméno"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard__doc_name
msgid "Documents Name"
msgstr "Název dokumentů"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_horizontal_group_rule__domain
msgid "Domain"
msgstr "Doména"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_fiscal_position.xml:0
msgid "Domestic"
msgstr "Domácí"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.res_config_settings_view_form
msgid "Download the Data Inalterability Check Report"
msgstr "Stáhněte si zprávu o kontrole neměnnosti dat"

#. module: account_reports
#: model:account.report.column,name:account_reports.partner_ledger_report_date_maturity
msgid "Due Date"
msgstr "Termín splnění"

#. module: account_reports
#: model:ir.actions.client,name:account_reports.action_account_report_sales
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_sales
msgid "EC Sales List"
msgstr "Seznam prodejů EC"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_ec_sales_report_handler
msgid "EC Sales Report Custom Handler"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_sales_report.py:0
msgid "EC tax on non EC countries"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_sales_report.py:0
msgid "EC tax on same country"
msgstr ""

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_equity0
msgid "EQUITY"
msgstr "AKCIE"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Editing a manual report line is not allowed in multivat setup when displaying data from all fiscal positions."
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Editing a manual report line is not allowed when multiple companies are selected."
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_trial_balance_report.py:0
msgid "End Balance"
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_comparison.xml:0
#: code:addons/account_reports/static/src/components/account_report/filters/filter_date.xml:0
msgid "End Date :"
msgstr "Datum ukončení :"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_date.xml:0
msgid "End of Last Financial Year"
msgstr "Konec posledního finančního roku"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_date.xml:0
msgid "End of Last Month"
msgstr "Konec posledního měsíce"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_date.xml:0
msgid "End of Last Quarter"
msgstr "Konec posledního čtvrtletí"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_journal_report.py:0
msgid "Ending Balance:"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_sales_report.py:0
msgid "Entries with partners with no VAT"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Error message"
msgstr "Chybové hlášení"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/multicurrency_revaluation_report/filters/filter_exchange_rate.xml:0
msgid "Exchange Rates"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_account__exclude_provision_currency_ids
msgid "Exclude Provision Currency"
msgstr "Vyloučit měnu poskytování"

#. module: account_reports
#: model:account.report.line,name:account_reports.multicurrency_revaluation_excluded
msgid "Excluded Accounts"
msgstr "Vyloučené účty"

#. module: account_reports
#: model:account.report,name:account_reports.executive_summary
#: model:ir.actions.client,name:account_reports.action_account_report_exec_summary
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_exec_summary
msgid "Executive Summary"
msgstr "Sumární přehledy"

#. module: account_reports
#: model:account.report.column,name:account_reports.aged_payable_report_expected_date
#: model:account.report.column,name:account_reports.aged_receivable_report_expected_date
#: model:ir.model.fields,field_description:account_reports.field_account_move_line__expected_pay_date
msgid "Expected Date"
msgstr "Očekávaný datum"

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_move_line__expected_pay_date
msgid "Expected payment date as manually set through the customer statement(e.g: if you had the customer on the phone and want to remember the date he promised he would pay)"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_aged_partner_balance.py:0
msgid "Expected payment date for journal item \"%s\" has been changed from %s to %s on journal entry \"%s\""
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard__expense_provision_account_id
msgid "Expense Account"
msgstr "Účet nákladů"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_res_company__account_revaluation_expense_provision_account_id
msgid "Expense Provision Account"
msgstr "Účet zajišťování výdajů"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/wizard/multicurrency_revaluation.py:0
msgid "Expense Provision for %s"
msgstr ""

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_expenses0
#: model:account.report.line,name:account_reports.account_financial_report_expense0
#: model:account.report.line,name:account_reports.account_financial_report_less_expenses0
msgid "Expenses"
msgstr "Výdaje"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#: model_terms:ir.ui.view,arch_db:account_reports.view_report_export_wizard
msgid "Export"
msgstr "Export"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_reports_export_wizard_format
msgid "Export format for accounting's reports"
msgstr "Formát exportu pro účetní výkazy"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard__export_format_ids
msgid "Export to"
msgstr "Exportovat do"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_reports_export_wizard
msgid "Export wizard for accounting's reports"
msgstr "Průvodce exportem pro účetní výkazy"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_expression_form
msgid "Expression"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_horizontal_group_rule__field_name
msgid "Field"
msgstr "Pole"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Field %s does not exist on account.move.line."
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Field %s of account.move.line is not stored, and hence cannot be used in a groupby expression"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Field 'Custom Handler Model' can only reference records inheriting from [%s]."
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.pdf_export_filters
msgid "Filter:"
msgstr "Filtr:"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_form
msgid "Filters"
msgstr "Filtry"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_fiscal_position.xml:0
msgid "Fiscal Position:"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_tax_unit__fpos_synced
msgid "Fiscal Positions Synchronised"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_tax_unit_form
msgid "Fiscal Positions should apply to all companies of the tax unit. You may want to"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report__footnotes_ids
msgid "Footnotes"
msgstr "Poznámky pod čarou"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/wizard/multicurrency_revaluation.py:0
msgid "Foreign currencies adjustment entry as of %s"
msgstr "Vstup úpravy cizí měny k %s"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid ""
"From %s\n"
"to  %s"
msgstr ""
"Z %s\n"
"na  %s"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard_format__fun_param
msgid "Function Parameter"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard_format__fun_to_call
msgid "Function to Call"
msgstr "Funkce pro volání"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#: code:addons/account_reports/models/account_trial_balance_report.py:0
#: model:account.report,name:account_reports.general_ledger_report
#: model:ir.actions.client,name:account_reports.action_account_report_general_ledger
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_general_ledger
msgid "General Ledger"
msgstr "Obratová předvaha"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_general_ledger_report_handler
msgid "General Ledger Custom Handler"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/wizard/report_export_wizard.py:0
msgid "Generated Documents"
msgstr "Generované dokumenty"

#. module: account_reports
#: model:account.report,name:account_reports.generic_ec_sales_report
msgid "Generic EC Sales List"
msgstr ""

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_generic_tax_report_handler
msgid "Generic Tax Report Custom Handler"
msgstr ""

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_generic_tax_report_handler_account_tax
msgid "Generic Tax Report Custom Handler (Account -> Tax)"
msgstr ""

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_generic_tax_report_handler_tax_account
msgid "Generic Tax Report Custom Handler (Tax -> Account)"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_sales_report.py:0
msgid "Goods"
msgstr "Zboží"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/journal_report/line/line.xml:0
#: model_terms:ir.ui.view,arch_db:account_reports.journal_report_pdf_export_line
msgid "Grid"
msgstr "Mřížka"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_gross_profit0
msgid "Gross Profit"
msgstr "Hrubý zisk"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_gross_profit0
msgid "Gross profit"
msgstr "Hrubý zisk"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_gpmargin0
msgid "Gross profit margin (gross profit / operating income)"
msgstr "Hrubá zisková marže (hrubý zisk / provozní výnosy)"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_report_search
msgid "Group By"
msgstr "Seskupit podle"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/journal_report/filter_extra_options.xml:0
msgid "Group By Months"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_horizontal_group_form
msgid "Group Name"
msgstr "Název Skupiny"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.pdf_export_filters
msgid "Groupby:"
msgstr "Skupina vytvořená:"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_extra_options.xml:0
msgid "Hierarchy and Subtotals"
msgstr "Hierarchie a mezisoučty"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_horizontal_group_rule__horizontal_group_id
msgid "Horizontal Group"
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_horizontal_groups.xml:0
msgid "Horizontal Group:"
msgstr ""

#. module: account_reports
#: model:ir.actions.act_window,name:account_reports.action_account_report_horizontal_groups
#: model:ir.model.fields,field_description:account_reports.field_account_report__horizontal_group_ids
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_horizontal_groups
msgid "Horizontal Groups"
msgstr ""

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_report_horizontal_group
msgid "Horizontal group for reports"
msgstr ""

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_report_horizontal_group_rule
msgid "Horizontal group rule for reports"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.res_config_settings_view_form
msgid "How often tax returns have to be made"
msgstr "Jak často musí být podána daňová přiznání"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard__id
#: model:ir.model.fields,field_description:account_reports.field_account_report_footnote__id
#: model:ir.model.fields,field_description:account_reports.field_account_report_horizontal_group__id
#: model:ir.model.fields,field_description:account_reports.field_account_report_horizontal_group_rule__id
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard__id
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard_format__id
#: model:ir.model.fields,field_description:account_reports.field_account_tax_unit__id
msgid "ID"
msgstr "ID"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/journal_report/line/line.xml:0
#: model_terms:ir.ui.view,arch_db:account_reports.journal_report_pdf_export_line
msgid "Impact On Grid"
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/journal_report/line/line.xml:0
msgid "Impacted Tax Grids"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.journal_report_pdf_export_line
msgid ""
"Impacted Tax Grids\n"
"                                        &amp;nbsp;"
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_extra_options.xml:0
msgid "Include Analytic Simulations"
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_extra_options.xml:0
msgid "Include Draft Entries"
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/journal_report/filter_extra_options.xml:0
msgid "Include Payments"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:0
msgid "Including Unreconciled Bank Statement Payments"
msgstr "Včetně nevyrovnaných plateb z bankovního výpisu"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:0
msgid "Including Unreconciled Bank Statement Receipts"
msgstr "Včetně nevyřízených příjmů z bankovního výpisu"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_income0
#: model:account.report.line,name:account_reports.account_financial_report_totalincome0
msgid "Income"
msgstr "Příjem"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard__income_provision_account_id
msgid "Income Account"
msgstr "Účet příjmů"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_res_company__account_revaluation_income_provision_account_id
msgid "Income Provision Account"
msgstr "Účet poskytování příjmů"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/wizard/multicurrency_revaluation.py:0
msgid "Income Provision for %s"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:0
msgid "Inconsistent Statements"
msgstr "Nekonzistentní prohlášení"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Inconsistent data: more than one external value at the same date for a 'most_recent' external line."
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Inconsistent report_id in options dictionary. Options says %s; report is %s."
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#: code:addons/account_reports/models/account_trial_balance_report.py:0
msgid "Initial Balance"
msgstr "Počáteční zůstatek"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/footnote/dialog/footnote_dialog.xml:0
msgid "Insert foot note here"
msgstr "Sem vložte poznámku"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/sales_report/account_report.xml:0
msgid "Intra-community taxes are applied on"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Invalid token '%s' in account_codes formula '%s'"
msgstr ""

#. module: account_reports
#: model:account.report.column,name:account_reports.aged_payable_report_invoice_date
#: model:account.report.column,name:account_reports.aged_receivable_report_invoice_date
msgid "Invoice Date"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report__is_account_coverage_report_available
msgid "Is Account Coverage Report Available"
msgstr ""

#. module: account_reports
#: model:account.report.column,name:account_reports.partner_ledger_report_journal_code
msgid "JRNL"
msgstr "JRNL"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_journal
#: model:ir.model.fields,field_description:account_reports.field_account_financial_year_op__account_tax_periodicity_journal_id
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard__journal_id
#: model:ir.model.fields,field_description:account_reports.field_res_company__account_tax_periodicity_journal_id
#: model:ir.model.fields,field_description:account_reports.field_res_config_settings__account_tax_periodicity_journal_id
#: model_terms:ir.ui.view,arch_db:account_reports.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:account_reports.setup_financial_year_opening_form
msgid "Journal"
msgstr "Deník"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_move
msgid "Journal Entry"
msgstr "Položka deníku"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Journal Groups"
msgstr "Skupiny účetních deníků"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_move_line
msgid "Journal Item"
msgstr "Položka deníku"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:0
#: code:addons/account_reports/models/account_report.py:0
#: code:addons/account_reports/models/account_trial_balance_report.py:0
msgid "Journal Items"
msgstr "Účetní deník"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#: code:addons/account_reports/models/account_journal_report.py:0
msgid "Journal Items for Tax Audit"
msgstr "Položky účetního deníku pro daňový audit"

#. module: account_reports
#: model:account.report,name:account_reports.journal_report
#: model:ir.actions.client,name:account_reports.action_account_report_ja
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_ja
msgid "Journal Report"
msgstr ""

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_journal_report_handler
msgid "Journal Report Custom Handler"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_generic_tax_report.py:0
msgid "Journal items with archived tax tags"
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_journal.xml:0
#: model_terms:ir.ui.view,arch_db:account_reports.pdf_export_filters
msgid "Journals:"
msgstr "Účetní deníky:"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_liabilities_view0
msgid "LIABILITIES"
msgstr "PASIVA"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_liabilities_and_equity_view0
msgid "LIABILITIES + EQUITY"
msgstr "PASIVA + VLASTNÍ KAPITÁL"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_journal_report.py:0
#: code:addons/account_reports/wizard/multicurrency_revaluation.py:0
#: model:account.report.column,name:account_reports.bank_reconciliation_report_label
#: model:account.report.column,name:account_reports.journal_report_partner_name
msgid "Label"
msgstr "Označení"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_date.xml:0
msgid "Last Financial Year"
msgstr "Poslední finanční rok"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_date.xml:0
msgid "Last Month"
msgstr "Poslední měsíc"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_date.xml:0
msgid "Last Quarter"
msgstr "Poslední čtvrtletí"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_reports_journal_dashboard_kanban_view
msgid "Last Statement balance + Transactions since statement"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard__write_uid
#: model:ir.model.fields,field_description:account_reports.field_account_report_footnote__write_uid
#: model:ir.model.fields,field_description:account_reports.field_account_report_horizontal_group__write_uid
#: model:ir.model.fields,field_description:account_reports.field_account_report_horizontal_group_rule__write_uid
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard__write_uid
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard_format__write_uid
#: model:ir.model.fields,field_description:account_reports.field_account_tax_unit__write_uid
msgid "Last Updated by"
msgstr "Naposledy upraveno od"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard__write_date
#: model:ir.model.fields,field_description:account_reports.field_account_report_footnote__write_date
#: model:ir.model.fields,field_description:account_reports.field_account_report_horizontal_group__write_date
#: model:ir.model.fields,field_description:account_reports.field_account_report_horizontal_group_rule__write_date
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard__write_date
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard_format__write_date
#: model:ir.model.fields,field_description:account_reports.field_account_tax_unit__write_date
msgid "Last Updated on"
msgstr "Naposled upraveno"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_footnote__line_id
msgid "Line"
msgstr "Řádek"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/line/popover/debug_popover.xml:0
msgid "Line without formula"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_form
msgid "Lines"
msgstr "Řádky"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Load more..."
msgstr "Načíst další..."

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_tax_unit__main_company_id
msgid "Main Company"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_tax_unit__main_company_id
msgid "Main company of this unit; the one actually reporting and paying the taxes."
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_multicurrency_revaluation_report.py:0
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_multicurrency_revaluation_wizard
msgid "Make Adjustment Entry"
msgstr "Proveďte zadání úpravy"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Manual value"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Manual values"
msgstr ""

#. module: account_reports
#: model:account.report.column,name:account_reports.partner_ledger_report_matching_number
msgid "Matching Number"
msgstr "Odpovídající číslo"

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_tax_unit__company_ids
msgid "Members of this unit"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Method '%s' must start with the '%s' prefix."
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_horizontal_group_rule__res_model_name
msgid "Model"
msgstr "Model"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_multicurrency_revaluation_report_handler
msgid "Multicurrency Revaluation Report Custom Handler"
msgstr ""

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_multicurrency_revaluation_wizard
msgid "Multicurrency Revaluation Wizard"
msgstr "Průvodce přeceněním více měn"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/res_company.py:0
msgid ""
"Multiple draft tax closing entries exist for fiscal position %s after %s. There should be at most one. \n"
" %s"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/res_company.py:0
msgid ""
"Multiple draft tax closing entries exist for your domestic region after %s. There should be at most one. \n"
" %s"
msgstr ""

#. module: account_reports
#. odoo-python
#. odoo-javascript
#: code:addons/account_reports/models/account_general_ledger.py:0
#: code:addons/account_reports/models/account_journal_report.py:0
#: code:addons/account_reports/static/src/components/journal_report/line/line.xml:0
#: model:ir.model.fields,field_description:account_reports.field_account_report_horizontal_group__name
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard_format__name
#: model:ir.model.fields,field_description:account_reports.field_account_tax_unit__name
#: model_terms:ir.ui.view,arch_db:account_reports.journal_report_pdf_export_line
#: model_terms:ir.ui.view,arch_db:account_reports.view_tax_unit_form
msgid "Name"
msgstr "Jméno"

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_reports_export_wizard__doc_name
msgid "Name to give to the generated documents."
msgstr "Název, který se má generovaným dokumentům dát."

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_profit0
#: model:account.report.line,name:account_reports.account_financial_report_net_profit0
msgid "Net Profit"
msgstr "Čistý zisk"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_net_assets0
msgid "Net assets"
msgstr "Čistý majetek"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_cash_flow_report.py:0
msgid "Net increase in cash and cash equivalents"
msgstr "Čistý nárůst peněžních prostředků a peněžních ekvivalentů"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_npmargin0
msgid "Net profit margin (net profit / income)"
msgstr "Čisté ziskové rozpětí (čistý zisk / zisk)"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/line_cell/popover/edit_popover.xml:0
msgid "No"
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_comparison.xml:0
msgid "No Comparison"
msgstr "Žádné srovnání"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "No VAT number associated with your company. Please define one."
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/wizard/multicurrency_revaluation.py:0
msgid "No adjustment needed"
msgstr "Není nutná žádná úprava"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/wizard/multicurrency_revaluation.py:0
msgid "No provision needed was found."
msgstr "Nebylo nalezeno žádné potřebné opatření."

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Non Trade Partners"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Non Trade Payable"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Non Trade Receivable"
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_horizontal_groups.xml:0
#: model_terms:ir.ui.view,arch_db:account_reports.pdf_export_filters
msgid "None"
msgstr "Nic"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_comparison.xml:0
msgid "Number of periods :"
msgstr "Počet období:"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filters.js:0
msgid "Number of periods cannot be smaller than 1"
msgstr ""

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_off_sheet
msgid "OFF BALANCE SHEET ACCOUNTS"
msgstr "PODROZVAHOVÉ ÚČTY"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filters.js:0
msgid "Odoo Warning"
msgstr "Odoo varování"

#. module: account_reports
#: model:account.report.column,name:account_reports.aged_payable_report_period5
#: model:account.report.column,name:account_reports.aged_receivable_report_period5
msgid "Older"
msgstr "Starší"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/wizard/report_export_wizard.py:0
msgid "One of the formats chosen can not be exported in the DMS"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_move.py:0
msgid "Only Billing Administrators are allowed to change lock dates!"
msgstr "Změnit data uzamčení mohou pouze správci fakturace!"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_extra_options.xml:0
msgid "Only Show Unreconciled Entries"
msgstr "Zobrazit pouze nezúčastněné položky"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/aged_partner_balance/line_name/line_name.xml:0
#: code:addons/account_reports/static/src/components/partner_ledger/line_name.xml:0
msgid "Open"
msgstr "Otevřít"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_financial_year_op
msgid "Opening Balance of Financial Year"
msgstr "Zahajovací zůstatek finančního roku"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_income0
msgid "Operating Income"
msgstr "Provozní zisk"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_expression_form
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_form
msgid "Options"
msgstr "Možnosti"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_extra_options.xml:0
msgid "Options:"
msgstr ""

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_other_income0
msgid "Other Income"
msgstr "Jiný příjem"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:0
#: model_terms:ir.ui.view,arch_db:account_reports.account_reports_journal_dashboard_kanban_view
msgid "Outstanding Payments/Receipts"
msgstr "Neuhrazené platby / příjmy"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "PDF"
msgstr "PDF"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard__report_id
msgid "Parent Report Id"
msgstr "ID nadřazené zprávy"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard_format__export_wizard_id
msgid "Parent Wizard"
msgstr "Nadřazený průvodce"

#. module: account_reports
#: model:account.report.column,name:account_reports.general_ledger_report_partner_name
msgid "Partner"
msgstr "Partner"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.pdf_export_filters
msgid "Partner Categories:"
msgstr "Kategorie partnerů:"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/res_partner.py:0
#: model:account.report,name:account_reports.partner_ledger_report
#: model:ir.actions.client,name:account_reports.action_account_report_partner_ledger
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_partner_ledger
msgid "Partner Ledger"
msgstr "Saldokonto"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_partner_ledger_report_handler
msgid "Partner Ledger Custom Handler"
msgstr ""

#. module: account_reports
#: model:ir.ui.menu,name:account_reports.account_reports_partners_reports_menu
msgid "Partner Reports"
msgstr "Zprávy partnerů"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/partner_ledger/line_name.xml:0
msgid "Partner ledger is bad"
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/partner_ledger/line_name.xml:0
msgid "Partner ledger is good"
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/partner_ledger/line_name.xml:0
msgid "Partner ledger is normal"
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_partner.xml:0
msgid "Partners"
msgstr "Partneři"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.pdf_export_filters
msgid "Partners:"
msgstr "Partneři:"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_aged_partner_balance.py:0
#: code:addons/account_reports/models/account_report.py:0
msgid "Payable"
msgstr "Závazky"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_generic_tax_report.py:0
msgid "Payable tax amount"
msgstr "Splatná částka daně"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_current_liabilities_payable
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_creditors0
msgid "Payables"
msgstr "Závazky"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_performance0
msgid "Performance"
msgstr "Výkon"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_res_config_settings__account_tax_periodicity
#: model:ir.model.fields,help:account_reports.field_account_financial_year_op__account_tax_periodicity
#: model:ir.model.fields,help:account_reports.field_res_company__account_tax_periodicity
#: model:ir.model.fields,help:account_reports.field_res_config_settings__account_tax_periodicity
#: model_terms:ir.ui.view,arch_db:account_reports.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:account_reports.setup_financial_year_opening_form
msgid "Periodicity"
msgstr "Periodicita"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_year_op__account_tax_periodicity
msgid "Periodicity in month"
msgstr "Periodicita v měsíci"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_analytic_groupby.xml:0
msgid "Plans"
msgstr "Plány"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/generic_tax_report/account_report.xml:0
msgid "Please note that the report may include some rounding differences towards the bookings."
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_generic_tax_report.py:0
msgid "Please specify the accounts necessary for the Tax Closing Entry."
msgstr ""

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_fixed_assets_view0
msgid "Plus Fixed Assets"
msgstr "Plus fixný majetek"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_non_current_assets_view0
msgid "Plus Non-current Assets"
msgstr "Plus neaktuálny majetek"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_non_current_liabilities0
msgid "Plus Non-current Liabilities"
msgstr "Plus Dlouhodobé závazky"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_position0
msgid "Position"
msgstr "Pozice"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/line_cell/popover/edit_popover.xml:0
msgid "Post"
msgstr "Zaúčtovat"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.journal_report_pdf_export_filters
msgid "Posted Entries only"
msgstr "Pouze zaúčtované položky"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.journal_report_pdf_export_filters
msgid "Posted and Draft Entries"
msgstr "Odeslané příspěvky a návrhy"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_prepayements0
msgid "Prepayments"
msgstr "Předplacené platby"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard__preview_data
msgid "Preview Data"
msgstr "Náhled dat"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_comparison.xml:0
msgid "Previous Period"
msgstr "Předchozí období"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_previous_year_earnings0
msgid "Previous Years Unallocated Earnings"
msgstr "Nerozdělené příjmy předchozích let"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_multicurrency_revaluation_wizard
msgid "Proceed with caution as there might be an existing adjustment for this period ("
msgstr "Postupujte opatrně, protože pro toto období může existovat úprava ("

#. module: account_reports
#: model:account.report,name:account_reports.profit_and_loss
#: model:ir.actions.client,name:account_reports.action_account_report_pl
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_profit_and_loss
msgid "Profit and Loss"
msgstr "Výnosy a náklady"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_profitability0
msgid "Profitability"
msgstr "Ziskovost"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_move_form_vat_return
msgid "Proposition of tax closing journal entry."
msgstr "Návrh na daňové uzavření deníkového záznamu."

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/wizard/multicurrency_revaluation.py:0
msgid "Provision for %(for_cur)s (1 %(comp_cur)s = %(rate)s %(for_cur)s)"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_aged_partner_balance.py:0
#: code:addons/account_reports/models/account_report.py:0
msgid "Receivable"
msgstr "Pohledávky"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_generic_tax_report.py:0
msgid "Receivable tax amount"
msgstr "Částka splatné daně"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_debtors0
#: model:account.report.line,name:account_reports.account_financial_report_receivable0
msgid "Receivables"
msgstr "Pohledávky"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/aged_partner_balance/line_name/line_name.xml:0
#: code:addons/account_reports/static/src/components/partner_ledger/line_name.xml:0
msgid "Reconcile"
msgstr "Vyrovnat"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_reports_journal_dashboard_kanban_view
msgid "Reconciliation Report"
msgstr "Zpráva o párování"

#. module: account_reports
#: model:account.report.column,name:account_reports.partner_ledger_report_ref
msgid "Ref"
msgstr "Číslo"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_year_op__account_tax_periodicity_reminder_day
#: model:ir.model.fields,field_description:account_reports.field_res_config_settings__account_tax_periodicity_reminder_day
#: model_terms:ir.ui.view,arch_db:account_reports.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:account_reports.setup_financial_year_opening_form
msgid "Reminder"
msgstr "Připomínka"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_footnote__report_id
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_form
msgid "Report"
msgstr "Sestava"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_line_form
msgid "Report Line"
msgstr "Řádek hlášení"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Report lines mentioning the account code"
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_variant.xml:0
msgid "Report:"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.res_config_settings_view_form
msgid "Reporting"
msgstr "Přehledy"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_horizontal_group__report_ids
msgid "Reports"
msgstr "Sestavy"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_retained_earnings0
msgid "Retained Earnings"
msgstr "Nerozdělený zisk"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_return_investment0
msgid "Return on investments (net profit / assets)"
msgstr "Návratnost investic (čistý zisk / majetek)"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard__reversal_date
msgid "Reversal Date"
msgstr "Datum zrušení"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/wizard/multicurrency_revaluation.py:0
msgid "Reversal of: %s"
msgstr "Storno: %s"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_report_search
msgid "Root Report"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_horizontal_group__rule_ids
msgid "Rules"
msgstr "Pravidla"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_reports_journal_dashboard_kanban_view
msgid "Running Balance"
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_comparison.xml:0
msgid "Same Period Last Year"
msgstr "Stejné období minulého roku"

#. module: account_reports
#. odoo-python
#. odoo-javascript
#: code:addons/account_reports/models/account_report.py:0
#: code:addons/account_reports/static/src/components/account_report/footnote/dialog/footnote_dialog.xml:0
#: code:addons/account_reports/static/src/components/aged_partner_balance/dialog/expected_date_dialog.xml:0
msgid "Save"
msgstr "Uložit"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/search_bar/search_bar.xml:0
msgid "Search line"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_sales_report.py:0
msgid "Services"
msgstr "Služby"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_coa
msgid "Setup"
msgstr "Nastavení"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_st_cash_forecast0
msgid "Short term cash forecast"
msgstr "Krátkodobá hotovostní předpověď"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard__show_warning_move_id
msgid "Show Warning Move"
msgstr "Zobrazit varovný pohyb"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_move_form_vat_return
msgid "Some controls failed."
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/journal_report/filter_extra_options.xml:0
msgid "Sort By Date"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_res_company__account_representative_id
msgid "Specify an Accounting Firm that will act as a representative when exporting reports."
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_comparison.xml:0
#: code:addons/account_reports/static/src/components/account_report/filters/filter_date.xml:0
msgid "Start Date :"
msgstr "Počáteční datum :"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_res_company__account_tax_periodicity_reminder_day
msgid "Start from"
msgstr "Začít od"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_journal_report.py:0
msgid "Starting Balance:"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_journal_report.py:0
msgid "T: %s"
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_partner.xml:0
msgid "Tags"
msgstr "Tagy"

#. module: account_reports
#. odoo-python
#. odoo-javascript
#: code:addons/account_reports/models/account_general_ledger.py:0
#: code:addons/account_reports/static/src/components/journal_report/line/line.xml:0
#: model_terms:ir.ui.view,arch_db:account_reports.journal_report_pdf_export_line
msgid "Tax Amount"
msgstr "Výše daně"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_bank_statement_line__tax_closing_end_date
#: model:ir.model.fields,field_description:account_reports.field_account_move__tax_closing_end_date
#: model:ir.model.fields,field_description:account_reports.field_account_payment__tax_closing_end_date
msgid "Tax Closing End Date"
msgstr "Datum ukončení daňové povinnosti"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_general_ledger.py:0
msgid "Tax Declaration"
msgstr "Přiznání k dani"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_journal_report.py:0
msgid "Tax Grids"
msgstr "Daňové tabulky"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_tax_unit__vat
msgid "Tax ID"
msgstr "DIČ"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_generic_tax_report.py:0
msgid "Tax Paid Adjustment"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_generic_tax_report.py:0
msgid "Tax Received Adjustment"
msgstr ""

#. module: account_reports
#: model:ir.actions.client,name:account_reports.action_account_report_gt
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_gt
#: model:mail.activity.type,name:account_reports.tax_closing_activity_type
#: model:mail.activity.type,summary:account_reports.tax_closing_activity_type
#: model_terms:ir.ui.view,arch_db:account_reports.view_move_form_vat_return
msgid "Tax Report"
msgstr "Daňové přiznání"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_bank_statement_line__tax_report_control_error
#: model:ir.model.fields,field_description:account_reports.field_account_move__tax_report_control_error
#: model:ir.model.fields,field_description:account_reports.field_account_payment__tax_report_control_error
msgid "Tax Report Control Error"
msgstr "Chyba kontroly daňové přiznání"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.setup_financial_year_opening_form
msgid "Tax Return"
msgstr "Daňové přiznání"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.res_config_settings_view_form
msgid "Tax Return Periodicity"
msgstr ""

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_tax_unit
#: model_terms:ir.ui.view,arch_db:account_reports.view_tax_unit_form
msgid "Tax Unit"
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_tax_unit.xml:0
msgid "Tax Unit:"
msgstr ""

#. module: account_reports
#: model:ir.actions.act_window,name:account_reports.action_view_tax_units
#: model:ir.model.fields,field_description:account_reports.field_res_company__account_tax_unit_ids
#: model:ir.ui.menu,name:account_reports.menu_view_tax_units
msgid "Tax Units"
msgstr ""

#. module: account_reports
#: model:ir.model.fields.selection,name:account_reports.selection__mail_activity_type__category__tax_report
msgid "Tax report"
msgstr "Daňové přiznání"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/res_company.py:0
msgid "Tax return for %s%s"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/res_company.py:0
msgid "Tax return from %s to %s%s"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_journal_report.py:0
msgid "Taxes"
msgstr "Daně"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/journal_report/line/line.xml:0
msgid "Taxes Applied"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.journal_report_pdf_export_line
msgid ""
"Taxes Applied\n"
"                            &amp;nbsp;"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_tax_unit__fpos_synced
msgid "Technical field indicating whether Fiscal Positions exist for all companies in the unit"
msgstr ""

#. module: account_reports
#: model:ir.model,name:account_reports.model_ir_actions_account_report_download
msgid "Technical model for accounting report downloads"
msgstr "Technický model pro stahování účetních zpráv"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_footnote__text
msgid "Text"
msgstr "Text"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "The Accounts Coverage Report is not available for this report."
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:0
msgid "The Book balance in Odoo dated today"
msgstr "Zůstatek knih v Odoo je datován dnes"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_tax.py:0
msgid "The country detected for this VAT number does not match the one set on this Tax Unit."
msgstr ""

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_tax_unit__country_id
msgid "The country in which this tax unit is used to group your companies' tax reports declaration."
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:0
msgid "The current balance in the General Ledger %s doesn't match the balance of your last bank statement %s leading to an unexplained difference of %s."
msgstr ""

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_tax_unit__vat
msgid "The identifier to be used when submitting a report for this unit."
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_tax.py:0
msgid "The main company of a tax unit has to be part of it."
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_journal_report.py:0
msgid "The selected report line does not target a Journal Entry or a Journal Item."
msgstr ""

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_res_company__account_tax_unit_ids
msgid "The tax units this company belongs to."
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/account_report.xml:0
msgid "There are"
msgstr "Existují"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/bank_reconciliation_report/account_report.xml:0
msgid "There are some"
msgstr "Tam jsou nějací"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_date.xml:0
msgid "This Financial Year"
msgstr "Tento finanční rok"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_date.xml:0
msgid "This Month"
msgstr "Tento měsíc"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_date.xml:0
msgid "This Quarter"
msgstr "Toto čtvrtletí"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "This account exists in the Chart of Accounts but is not mentioned in any line of the report"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "This account is reported in a line of the report but does not exist in the Chart of Accounts"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "This account is reported in multiple lines of the report"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "This account is reported multiple times on the same line of the report"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.res_config_settings_view_form
msgid "This allows you to choose the position of totals in your financial reports."
msgstr "To vám umožní zvolit pozici součtů ve vašich finančních výkazech."

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/generic_tax_report/account_report.xml:0
msgid "This company is part of a tax unit. You're currently not viewing the whole unit; to change that, tick the \"aggregate\" option of \"tax unit\" menu."
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_generic_tax_report.py:0
msgid "This period is already closed for company %s"
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/multicurrency_revaluation_report/account_report.xml:0
msgid "This report only displays the data of the active company."
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_date.xml:0
msgid "Today"
msgstr "Dnes"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_general_ledger.py:0
#: code:addons/account_reports/models/account_partner_ledger.py:0
#: code:addons/account_reports/models/account_sales_report.py:0
#: model:account.report.column,name:account_reports.aged_payable_report_total
#: model:account.report.column,name:account_reports.aged_receivable_report_total
msgid "Total"
msgstr "Celkem"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:0
#: code:addons/account_reports/models/account_report.py:0
msgid "Total %s"
msgstr "Celkový %s"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Trade Partners"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:0
msgid "Transactions that were entered into Odoo, but not yet reconciled (Payments triggered by invoices/bills or manually)"
msgstr "Transakce, které byly zadány do Odoo, ale ještě nebyly odsouhlaseny (platby spuštěné na základě faktur / faktur nebo ručně)"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:0
msgid "Transactions(+) that were entered into Odoo, but not yet reconciled (Payments triggered by invoices/refunds or manually)"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_bank_reconciliation_report.py:0
msgid "Transactions(-) that were entered into Odoo, but not yet reconciled (Payments triggered by bills/credit notes or manually)"
msgstr ""

#. module: account_reports
#: model:account.report,name:account_reports.trial_balance_report
#: model:ir.actions.client,name:account_reports.action_account_report_coa
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_coa
msgid "Trial Balance"
msgstr "Hlavní kniha"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_trial_balance_report_handler
msgid "Trial Balance Custom Handler"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_sales_report.py:0
msgid "Triangular"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Trying to expand a group for a line which was not generated by a report line: %s"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Trying to expand a line without an expansion function."
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Trying to expand groupby results on lines without a groupby value."
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_journal_report.py:0
msgid "Trying to use the journal line expand function on a line that is not linked to a journal."
msgstr ""

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_unaffected_earnings0
msgid "Unallocated Earnings"
msgstr "Nepřiřazené příjmy"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_extra_options.xml:0
msgid "Unfold All"
msgstr "Rozbalit vše"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Unknown"
msgstr "Neznámý"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_partner_ledger.py:0
#: code:addons/account_reports/models/account_sales_report.py:0
msgid "Unknown Partner"
msgstr "Neznámý partner"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Unknown bound criterium: %s"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Unknown date scope: %s"
msgstr ""

#. module: account_reports
#: model:account.report,name:account_reports.multicurrency_revaluation_report
#: model:ir.actions.client,name:account_reports.action_account_report_multicurrency_revaluation
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_multicurrency_revaluation
msgid "Unrealized Currency Gains/Losses"
msgstr "Kurzovní rozdíly"

#. module: account_reports
#: model:account.report.column,name:account_reports.account_financial_report_ec_sales_vat
msgid "VAT Number"
msgstr "DIČ"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_move.py:0
msgid "Vat closing from %s to %s"
msgstr "Uzavření vany od %s na %s"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "View Bank Statement"
msgstr "Zobrazení bankovního výpisu"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/line_cell/popover/carryover_popover.xml:0
msgid "View Carryover Lines"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "View Journal Entry"
msgstr "Zobrazit zápis do deníku"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#: code:addons/account_reports/models/account_sales_report.py:0
msgid "View Partner"
msgstr "Zobrazit partnera"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "View Payment"
msgstr "Zobrazit platbu"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.res_config_settings_view_form
msgid "When ticked, totals and subtotals appear below the sections of the report"
msgstr "Po zaškrtnutí se součty a mezisoučty objeví pod částmi zprávy"

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_res_company__totals_below_sections
#: model:ir.model.fields,help:account_reports.field_res_config_settings__totals_below_sections
msgid "When ticked, totals and subtotals appear below the sections of the report."
msgstr "Pokud je zaškrtnuto, pod částmi přehledu se zobrazí součty a mezisoučty."

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_account__exclude_provision_currency_ids
msgid "Whether or not we have to make provisions for the selected foreign currencies."
msgstr "Bez ohledu na to, zda musíme provést rezervy pro vybrané cizí měny."

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_general_ledger.py:0
msgid "Wrong ID for general ledger line to expand: %s"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_partner_ledger.py:0
msgid "Wrong ID for partner ledger line to expand: %s"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Wrong format for if_other_expr_above/if_other_expr_below formula: %s"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "XLSX"
msgstr "XLSX"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/line_cell/popover/edit_popover.xml:0
msgid "Yes"
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/multicurrency_revaluation_report/account_report.xml:0
msgid "You are using custom exchange rates."
msgstr "Používáte vlastní směnné kurzy."

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_move.py:0
msgid "You cannot reset this closing entry to draft, as it would delete carryover values impacting the tax report of a locked period. To do this, you first need to modify you tax return lock date."
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.pdf_export_main
msgid "[Draft]"
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/sales_report/account_report.xml:0
msgid "addressed to"
msgstr ""

#. module: account_reports
#: model:ir.model.fields.selection,name:account_reports.selection__res_company__account_tax_periodicity__year
msgid "annually"
msgstr "každoročně"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_aged_partner_balance.py:0
msgid "any"
msgstr "jakýkoliv"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:account_reports.setup_financial_year_opening_form
msgid "days after period"
msgstr "dny po období"

#. module: account_reports
#: model:ir.model.fields.selection,name:account_reports.selection__res_company__account_tax_periodicity__2_months
msgid "every 2 months"
msgstr "každé 2 měsíce"

#. module: account_reports
#: model:ir.model.fields.selection,name:account_reports.selection__res_company__account_tax_periodicity__4_months
msgid "every 4 months"
msgstr "každé 4 měsíce"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/bank_reconciliation_report/account_report.xml:0
msgid "having a starting balance different from the previous ending balance."
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/line_cell/popover/carryover_popover.xml:0
msgid "in the next period."
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/sales_report/account_report.xml:0
msgid "invoices"
msgstr "faktury"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/bank_reconciliation_report/account_report.xml:0
msgid "journal items"
msgstr "Účetní deník"

#. module: account_reports
#: model:ir.model.fields.selection,name:account_reports.selection__res_company__account_tax_periodicity__monthly
msgid "monthly"
msgstr "měsíčně"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "n/a"
msgstr "n/a"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/sales_report/account_report.xml:0
msgid "partners"
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/account_report.xml:0
msgid "prior or included in this period."
msgstr ""

#. module: account_reports
#: model:ir.model.fields.selection,name:account_reports.selection__res_company__account_tax_periodicity__trimester
msgid "quarterly"
msgstr "čtvrtletní"

#. module: account_reports
#: model:ir.model.fields.selection,name:account_reports.selection__res_company__account_tax_periodicity__semester
msgid "semi-annually"
msgstr "pololetně"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/bank_reconciliation_report/account_report.xml:0
msgid "statements"
msgstr "prohlášení"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_tax_unit_form
msgid "synchronize fiscal positions"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_tax.py:0
msgid "tax unit [%s]"
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/sales_report/account_report.xml:0
msgid "that are not established abroad."
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/account_report.xml:0
msgid "unposted Journal Entries"
msgstr "neodeslané vnitřní účetní doklady"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/line_cell/popover/carryover_popover.xml:0
msgid "were carried over to this line from previous period."
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/bank_reconciliation_report/account_report.xml:0
msgid "which doesn't result from a bank statement nor payments."
msgstr "který nevyplývá z bankovního výpisu ani z plateb."

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/sales_report/account_report.xml:0
msgid "who are not established in any of the EC countries."
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/line_cell/popover/carryover_popover.xml:0
msgid "will be carried over to"
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/line_cell/popover/carryover_popover.xml:0
msgid "will be carried over to this line in the next period."
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/sales_report/account_report.xml:0
msgid "without a valid intra-community VAT number."
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/aged_partner_balance/line_name/line_name.xml:0
#: code:addons/account_reports/static/src/components/general_ledger/line_name.xml:0
#: code:addons/account_reports/static/src/components/partner_ledger/line_name.xml:0
msgid "⇒ Journal Items"
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/multicurrency_revaluation_report/account_report.xml:0
msgid "⇒ Reset to Odoo’s Rate"
msgstr "⇒ Obnovit rychlost Odoo"
