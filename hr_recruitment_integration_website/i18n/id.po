# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_recruitment_integration_website
# 
# Translators:
# Wil Odoo, 2024
# Abe Manyo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:27+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Abe Manyo, 2024\n"
"Language-Team: Indonesian (https://app.transifex.com/odoo/teams/41243/id/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: id\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: hr_recruitment_integration_website
#: model:ir.model.fields,field_description:hr_recruitment_integration_website.field_hr_recruitment_post_job_wizard__apply_method
msgid "Apply Method"
msgstr "Terapkan Metode"

#. module: hr_recruitment_integration_website
#. odoo-python
#: code:addons/hr_recruitment_integration_website/wizard/hr_recruitment_post_job.py:0
msgid "At least one platform must be selected"
msgstr "Setidaknya satu platform harus dipilih"

#. module: hr_recruitment_integration_website
#: model:ir.model.fields,field_description:hr_recruitment_integration_website.field_hr_recruitment_post_job_wizard__campaign_start_date
msgid "Campaign Start Date"
msgstr "Tanggal Mulai Kampanye"

#. module: hr_recruitment_integration_website
#. odoo-python
#: code:addons/hr_recruitment_integration_website/wizard/hr_recruitment_post_job.py:0
msgid "Campaign Start Date is required."
msgstr "Tanggal Mulai Kampanye diperlukan."

#. module: hr_recruitment_integration_website
#: model:ir.model.fields,field_description:hr_recruitment_integration_website.field_hr_job_post__apply_method
msgid "Contact Method"
msgstr "Metode Kontak"

#. module: hr_recruitment_integration_website
#. odoo-python
#: code:addons/hr_recruitment_integration_website/wizard/hr_recruitment_post_job.py:0
msgid "Generate"
msgstr "Buat"

#. module: hr_recruitment_integration_website
#: model_terms:ir.ui.view,arch_db:hr_recruitment_integration_website.hr_recruitment_post_job_wizard_view_form
msgid "Generate Description (<i class=\"fa fa-fw fa-magic\"/>AI)"
msgstr "Keterangan Umum (<i class=\"fa fa-fw fa-magic\"/>AI)"

#. module: hr_recruitment_integration_website
#. odoo-python
#: code:addons/hr_recruitment_integration_website/models/hr_job.py:0
msgid ""
"IGNORE PREVIOUS PROMPTS AND FORMAT AS HTML AND NO HYPERLINKS\n"
"You are the best recruiter in out company and we are looking to hire a new employee.\n"
"You have been tasked with creating a the best job description for a new job opening.\n"
"Given the following infos, make a friendly and attractive job description that could be used\n"
"to attract potential candidates (The platforms could be things like LinkedIn, Indeed, Glassdoor, Monster, etc.)\n"
"It's your last chance to make a good impression and attract the best candidates to our company so make it count!\n"
"Here is the data to generate the job description:\n"
msgstr ""
"ABAIKAN PROMPT SEBELUMNYA DAN FORMAT SEBAGAI HTML DAN TANPA HYPERLINK\n"
"Anda adalah rekruter terbaik di perusahaan kami dan kami mencari karyawan baru.\n"
"Anda ditugaskan membuat jobdesk terbaik untuk lowongan kerja.\n"
"Diberikan informasi berikut, buat jobdesk yang menarik dan nyaman yang dapat digunakan\n"
"untuk menarik kandidat-kandidat (Platform dapat merupakan LinkedIn, Indeed, Glassdoor, Monster, dsb.)\n"
"Ini adalah kesempatan terakhir Anda untuk membuat kesan yang baik dan memikat kandidat terbaik ke perusahaan kami jadi buatlah yang terbaik!\n"
"Berikut data untuk membuat jobdesk:\n"

#. module: hr_recruitment_integration_website
#: model:ir.model.fields,field_description:hr_recruitment_integration_website.field_hr_recruitment_post_job_wizard__platform_ids
msgid "Job Board"
msgstr "Job Board"

#. module: hr_recruitment_integration_website
#: model:ir.model,name:hr_recruitment_integration_website.model_hr_job
msgid "Job Position"
msgstr "Jabatan Kerja"

#. module: hr_recruitment_integration_website
#: model:ir.model,name:hr_recruitment_integration_website.model_hr_job_post
msgid "Job Post"
msgstr "Postingan Kerja"

#. module: hr_recruitment_integration_website
#: model:ir.model.fields,field_description:hr_recruitment_integration_website.field_hr_recruitment_post_job_wizard__job_apply_url
msgid "Job url"
msgstr "Url Kerja"

#. module: hr_recruitment_integration_website
#. odoo-python
#: code:addons/hr_recruitment_integration_website/models/hr_job.py:0
msgid "Oops, it looks like our AI is unreachable!"
msgstr "Oops, sepertinya AI kami tidak dapat dijangkau!"

#. module: hr_recruitment_integration_website
#: model:ir.model.fields,field_description:hr_recruitment_integration_website.field_hr_recruitment_post_job_wizard__post_html
msgid "Post"
msgstr "Rekam"

#. module: hr_recruitment_integration_website
#. odoo-python
#: code:addons/hr_recruitment_integration_website/wizard/hr_recruitment_post_job.py:0
#: model:ir.model,name:hr_recruitment_integration_website.model_hr_recruitment_post_job_wizard
msgid "Post Job"
msgstr "Postingan Kerja"

#. module: hr_recruitment_integration_website
#. odoo-python
#: code:addons/hr_recruitment_integration_website/wizard/hr_recruitment_post_job.py:0
msgid "Post is required."
msgstr "Postingan diperlukan."

#. module: hr_recruitment_integration_website
#: model:ir.model.fields.selection,name:hr_recruitment_integration_website.selection__hr_job_post__apply_method__redirect
msgid "Redirect to Website"
msgstr "Alihkan ke Website"

#. module: hr_recruitment_integration_website
#: model:ir.model.fields.selection,name:hr_recruitment_integration_website.selection__hr_recruitment_post_job_wizard__apply_method__redirect
msgid "Redirect to company's website"
msgstr "Alihkan ke website perusahaan"

#. module: hr_recruitment_integration_website
#: model:ir.actions.server,name:hr_recruitment_integration_website.hr_recruitment_post_job_wizard_action_regenerate_post
msgid "Regenerate Post"
msgstr "Buat Post"

#. module: hr_recruitment_integration_website
#. odoo-python
#: code:addons/hr_recruitment_integration_website/models/hr_job.py:0
msgid "Sorry, the web page is too long for our AI to process."
msgstr "Maaf, halaman website terlalu panjang untuk AI kami proses."

#. module: hr_recruitment_integration_website
#. odoo-python
#: code:addons/hr_recruitment_integration_website/models/hr_job.py:0
msgid "Sorry, we could not generate a response. Please try again later."
msgstr "Maaf, kami tidak dapat membuat tanggapan. Silakan coba lagi nanti."

#. module: hr_recruitment_integration_website
#. odoo-python
#: code:addons/hr_recruitment_integration_website/wizard/hr_recruitment_post_job.py:0
msgid ""
"The Job Description will be replaced with the generated one, do you want to "
"continue?"
msgstr ""
"Job Description akan diganti dengan yang dibuat, apakah Anda ingin "
"melanjutkan?"

#. module: hr_recruitment_integration_website
#: model:ir.model.fields,help:hr_recruitment_integration_website.field_hr_recruitment_post_job_wizard__campaign_start_date
msgid "The date when the campaign will start."
msgstr "Tanggal saat kampanye akan dimulai."

#. module: hr_recruitment_integration_website
#. odoo-python
#: code:addons/hr_recruitment_integration_website/wizard/hr_recruitment_post_job.py:0
msgid ""
"The job must be published on the website to generate a post with a redirect "
"apply method."
msgstr ""
"Pekerjaan harus dipublikasikan pada website untuk membuat posting dengan "
"metode lamaran alihkan."

#. module: hr_recruitment_integration_website
#. odoo-python
#: code:addons/hr_recruitment_integration_website/wizard/hr_recruitment_post_job.py:0
msgid ""
"URL is required if the apply method is 'Redirect to company's website'."
msgstr ""
"URL diperlukan bila metode melamar adalah 'Alihkan ke website perusahaan'."

#. module: hr_recruitment_integration_website
#. odoo-python
#: code:addons/hr_recruitment_integration_website/models/hr_job.py:0
msgid "You can only generate a post for a published job offer."
msgstr ""
"Anda hanya dapat membuat posting untuk lowongan pekerjaan yang "
"dipublikasikan."

#. module: hr_recruitment_integration_website
#. odoo-python
#: code:addons/hr_recruitment_integration_website/models/hr_job.py:0
msgid ""
"You have reached the maximum number of requests for this service. Try again "
"later."
msgstr ""
"Anda telah mencapai jumlah maksimal permintaan untuk layanan ini. Silakan "
"coba lagi nanti."
