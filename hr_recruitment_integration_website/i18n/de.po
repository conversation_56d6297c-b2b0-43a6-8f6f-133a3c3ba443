# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_recruitment_integration_website
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:27+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: German (https://app.transifex.com/odoo/teams/41243/de/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: de\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: hr_recruitment_integration_website
#: model:ir.model.fields,field_description:hr_recruitment_integration_website.field_hr_recruitment_post_job_wizard__apply_method
msgid "Apply Method"
msgstr "Methode anwenden"

#. module: hr_recruitment_integration_website
#. odoo-python
#: code:addons/hr_recruitment_integration_website/wizard/hr_recruitment_post_job.py:0
msgid "At least one platform must be selected"
msgstr "Es muss mindestens eine Plattform ausgewählt sein."

#. module: hr_recruitment_integration_website
#: model:ir.model.fields,field_description:hr_recruitment_integration_website.field_hr_recruitment_post_job_wizard__campaign_start_date
msgid "Campaign Start Date"
msgstr "Startatum der Kampagne"

#. module: hr_recruitment_integration_website
#. odoo-python
#: code:addons/hr_recruitment_integration_website/wizard/hr_recruitment_post_job.py:0
msgid "Campaign Start Date is required."
msgstr "Das Startdatum der Kampagne ist erforderlich."

#. module: hr_recruitment_integration_website
#: model:ir.model.fields,field_description:hr_recruitment_integration_website.field_hr_job_post__apply_method
msgid "Contact Method"
msgstr "Kontaktform"

#. module: hr_recruitment_integration_website
#. odoo-python
#: code:addons/hr_recruitment_integration_website/wizard/hr_recruitment_post_job.py:0
msgid "Generate"
msgstr "Generieren"

#. module: hr_recruitment_integration_website
#: model_terms:ir.ui.view,arch_db:hr_recruitment_integration_website.hr_recruitment_post_job_wizard_view_form
msgid "Generate Description (<i class=\"fa fa-fw fa-magic\"/>AI)"
msgstr "Beschreibung generieren (<i class=\"fa fa-fw fa-magic\"/>KI)"

#. module: hr_recruitment_integration_website
#. odoo-python
#: code:addons/hr_recruitment_integration_website/models/hr_job.py:0
msgid ""
"IGNORE PREVIOUS PROMPTS AND FORMAT AS HTML AND NO HYPERLINKS\n"
"You are the best recruiter in out company and we are looking to hire a new employee.\n"
"You have been tasked with creating a the best job description for a new job opening.\n"
"Given the following infos, make a friendly and attractive job description that could be used\n"
"to attract potential candidates (The platforms could be things like LinkedIn, Indeed, Glassdoor, Monster, etc.)\n"
"It's your last chance to make a good impression and attract the best candidates to our company so make it count!\n"
"Here is the data to generate the job description:\n"
msgstr ""
"IGNORIEREN SIE DIE VORHERIGEN AUFFORDERUNGEN UND FORMATIEREN SIE ALS HTML UND OHNE HYPERLINKS\n"
"Sie sind der beste Personalvermittler in unserem Unternehmen und wir wollen einen neuen Mitarbeiter einstellen.\n"
"Sie haben den Auftrag, eine optimale Stellenbeschreibung für eine neue Stelle zu erstellen.\n"
"Erstellen Sie anhand der folgenden Informationen eine freundliche und attraktive Stellenbeschreibung, die verwendet werden kann,\n"
"um potenzielle Bewerber anzulocken (als Plattformen kommen z. B. LinkedIn, Indeed, Glassdoor, Monster usw. in Frage).\n"
"Es ist Ihre letzte Chance, einen guten Eindruck zu hinterlassen und die besten Bewerber für unser Unternehmen zu gewinnen, also machen Sie es richtig!\n"
"Hier sind die Daten zur Erstellung der Stellenbeschreibung:\n"

#. module: hr_recruitment_integration_website
#: model:ir.model.fields,field_description:hr_recruitment_integration_website.field_hr_recruitment_post_job_wizard__platform_ids
msgid "Job Board"
msgstr "Stellenportal"

#. module: hr_recruitment_integration_website
#: model:ir.model,name:hr_recruitment_integration_website.model_hr_job
msgid "Job Position"
msgstr "Stelle"

#. module: hr_recruitment_integration_website
#: model:ir.model,name:hr_recruitment_integration_website.model_hr_job_post
msgid "Job Post"
msgstr "Stellenanzeige"

#. module: hr_recruitment_integration_website
#: model:ir.model.fields,field_description:hr_recruitment_integration_website.field_hr_recruitment_post_job_wizard__job_apply_url
msgid "Job url"
msgstr "Job-URL"

#. module: hr_recruitment_integration_website
#. odoo-python
#: code:addons/hr_recruitment_integration_website/models/hr_job.py:0
msgid "Oops, it looks like our AI is unreachable!"
msgstr "Hoppla, unsere KI scheint nicht erreichbar zu sein!"

#. module: hr_recruitment_integration_website
#: model:ir.model.fields,field_description:hr_recruitment_integration_website.field_hr_recruitment_post_job_wizard__post_html
msgid "Post"
msgstr "Beitrag"

#. module: hr_recruitment_integration_website
#. odoo-python
#: code:addons/hr_recruitment_integration_website/wizard/hr_recruitment_post_job.py:0
#: model:ir.model,name:hr_recruitment_integration_website.model_hr_recruitment_post_job_wizard
msgid "Post Job"
msgstr "Stelle veröffentlichen"

#. module: hr_recruitment_integration_website
#. odoo-python
#: code:addons/hr_recruitment_integration_website/wizard/hr_recruitment_post_job.py:0
msgid "Post is required."
msgstr "Stellenanzeige ist erforderlich."

#. module: hr_recruitment_integration_website
#: model:ir.model.fields.selection,name:hr_recruitment_integration_website.selection__hr_job_post__apply_method__redirect
msgid "Redirect to Website"
msgstr "Zur Website weiterleiten"

#. module: hr_recruitment_integration_website
#: model:ir.model.fields.selection,name:hr_recruitment_integration_website.selection__hr_recruitment_post_job_wizard__apply_method__redirect
msgid "Redirect to company's website"
msgstr "Zur Unternehmenswebsite weiterleiten"

#. module: hr_recruitment_integration_website
#: model:ir.actions.server,name:hr_recruitment_integration_website.hr_recruitment_post_job_wizard_action_regenerate_post
msgid "Regenerate Post"
msgstr "Anzeige regenerieren"

#. module: hr_recruitment_integration_website
#. odoo-python
#: code:addons/hr_recruitment_integration_website/models/hr_job.py:0
msgid "Sorry, the web page is too long for our AI to process."
msgstr "Es tut uns leid, aber die Webseite ist zu lang für unsere KI."

#. module: hr_recruitment_integration_website
#. odoo-python
#: code:addons/hr_recruitment_integration_website/models/hr_job.py:0
msgid "Sorry, we could not generate a response. Please try again later."
msgstr ""
"Es tut uns leid, wir konnten keine Antwort generieren. Bitte versuchen Sie "
"es später erneut."

#. module: hr_recruitment_integration_website
#. odoo-python
#: code:addons/hr_recruitment_integration_website/wizard/hr_recruitment_post_job.py:0
msgid ""
"The Job Description will be replaced with the generated one, do you want to "
"continue?"
msgstr ""
"Die Stellenbeschreibung wird durch die generierte Beschreibung ersetzt. "
"Möchten Sie fortfahren?"

#. module: hr_recruitment_integration_website
#: model:ir.model.fields,help:hr_recruitment_integration_website.field_hr_recruitment_post_job_wizard__campaign_start_date
msgid "The date when the campaign will start."
msgstr "Das Datum, an dem die Kampagne beginnt."

#. module: hr_recruitment_integration_website
#. odoo-python
#: code:addons/hr_recruitment_integration_website/wizard/hr_recruitment_post_job.py:0
msgid ""
"The job must be published on the website to generate a post with a redirect "
"apply method."
msgstr ""
"Die Stellenanzeige muss auf der Website veröffentlich sein, um einen Beitrag"
" mit einer umgeleiteten Bewerbung zu generieren."

#. module: hr_recruitment_integration_website
#. odoo-python
#: code:addons/hr_recruitment_integration_website/wizard/hr_recruitment_post_job.py:0
msgid ""
"URL is required if the apply method is 'Redirect to company's website'."
msgstr ""
"Die URL ist erforderlich, wenn die Bewerbungsmethode „Zur "
"Unternehmenswebsite weiterleiten“ ist."

#. module: hr_recruitment_integration_website
#. odoo-python
#: code:addons/hr_recruitment_integration_website/models/hr_job.py:0
msgid "You can only generate a post for a published job offer."
msgstr ""
"Sie können nur eine Anzeige für ein veröffentlichtes Stellenangebot "
"generieren."

#. module: hr_recruitment_integration_website
#. odoo-python
#: code:addons/hr_recruitment_integration_website/models/hr_job.py:0
msgid ""
"You have reached the maximum number of requests for this service. Try again "
"later."
msgstr ""
"Sie haben die maximale Anzahl von Anfragen für diesen Dienst erreicht. "
"Versuchen Sie es später noch einmal."
