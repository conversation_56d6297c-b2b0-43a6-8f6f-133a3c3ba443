# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_recruitment_integration_website
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON>, 2025
# <PERSON><PERSON> <ossi.manty<PERSON><PERSON>@obs-solutions.fi>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:27+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2025\n"
"Language-Team: Finnish (https://app.transifex.com/odoo/teams/41243/fi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fi\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: hr_recruitment_integration_website
#: model:ir.model.fields,field_description:hr_recruitment_integration_website.field_hr_recruitment_post_job_wizard__apply_method
msgid "Apply Method"
msgstr "Käytä menetelmää"

#. module: hr_recruitment_integration_website
#. odoo-python
#: code:addons/hr_recruitment_integration_website/wizard/hr_recruitment_post_job.py:0
msgid "At least one platform must be selected"
msgstr "Ainakin yksi alusta on valittava"

#. module: hr_recruitment_integration_website
#: model:ir.model.fields,field_description:hr_recruitment_integration_website.field_hr_recruitment_post_job_wizard__campaign_start_date
msgid "Campaign Start Date"
msgstr "Kampanjan alkamispäivä"

#. module: hr_recruitment_integration_website
#. odoo-python
#: code:addons/hr_recruitment_integration_website/wizard/hr_recruitment_post_job.py:0
msgid "Campaign Start Date is required."
msgstr "Kampanjan alkamispäivä vaaditaan."

#. module: hr_recruitment_integration_website
#: model:ir.model.fields,field_description:hr_recruitment_integration_website.field_hr_job_post__apply_method
msgid "Contact Method"
msgstr "Yhteydenottotapa"

#. module: hr_recruitment_integration_website
#. odoo-python
#: code:addons/hr_recruitment_integration_website/wizard/hr_recruitment_post_job.py:0
msgid "Generate"
msgstr "Generoi"

#. module: hr_recruitment_integration_website
#: model_terms:ir.ui.view,arch_db:hr_recruitment_integration_website.hr_recruitment_post_job_wizard_view_form
msgid "Generate Description (<i class=\"fa fa-fw fa-magic\"/>AI)"
msgstr "Luo kuvaus (<i class=\"fa fa-fw fa-magic\"/>tekoälyllä)"

#. module: hr_recruitment_integration_website
#. odoo-python
#: code:addons/hr_recruitment_integration_website/models/hr_job.py:0
msgid ""
"IGNORE PREVIOUS PROMPTS AND FORMAT AS HTML AND NO HYPERLINKS\n"
"You are the best recruiter in out company and we are looking to hire a new employee.\n"
"You have been tasked with creating a the best job description for a new job opening.\n"
"Given the following infos, make a friendly and attractive job description that could be used\n"
"to attract potential candidates (The platforms could be things like LinkedIn, Indeed, Glassdoor, Monster, etc.)\n"
"It's your last chance to make a good impression and attract the best candidates to our company so make it count!\n"
"Here is the data to generate the job description:\n"
msgstr ""
"JÄTÄ EDELLISET KEHOTUKSET HUOMIOTTA JA MUOTOILE HTML-MUODOSSA ILMAN HYPERLINKKEJÄ\n"
"Olet yrityksemme paras rekrytoija, ja etsimme uutta työntekijää.\n"
"Sinulle on annettu tehtäväksi luoda paras työnkuvaus uutta avointa työpaikkaa varten.\n"
"Tee seuraavien tietojen perusteella ystävällinen ja houkutteleva työnkuvaus, jota voitaisiin käyttää\n"
"houkuttelemaan mahdollisia ehdokkaita (alustoja voivat olla esimerkiksi LinkedIn, Indeed, Glassdoor, Monster jne.)\n"
"Tämä on viimeinen tilaisuutesi tehdä hyvä vaikutelma ja houkutella parhaat ehdokkaat yritykseemme, joten tee työ hyvin!\n"
"Tässä on tiedot, joiden avulla luodaan työnkuvaus:\n"

#. module: hr_recruitment_integration_website
#: model:ir.model.fields,field_description:hr_recruitment_integration_website.field_hr_recruitment_post_job_wizard__platform_ids
msgid "Job Board"
msgstr "Työnhaku"

#. module: hr_recruitment_integration_website
#: model:ir.model,name:hr_recruitment_integration_website.model_hr_job
msgid "Job Position"
msgstr "Tehtävänimike"

#. module: hr_recruitment_integration_website
#: model:ir.model,name:hr_recruitment_integration_website.model_hr_job_post
msgid "Job Post"
msgstr "Työpaikan postaus"

#. module: hr_recruitment_integration_website
#: model:ir.model.fields,field_description:hr_recruitment_integration_website.field_hr_recruitment_post_job_wizard__job_apply_url
msgid "Job url"
msgstr "Työpaikan URL"

#. module: hr_recruitment_integration_website
#. odoo-python
#: code:addons/hr_recruitment_integration_website/models/hr_job.py:0
msgid "Oops, it looks like our AI is unreachable!"
msgstr "Oho, näyttää siltä, että tekoälymme ei ole tavoitettavissa!"

#. module: hr_recruitment_integration_website
#: model:ir.model.fields,field_description:hr_recruitment_integration_website.field_hr_recruitment_post_job_wizard__post_html
msgid "Post"
msgstr "Kirjaa"

#. module: hr_recruitment_integration_website
#. odoo-python
#: code:addons/hr_recruitment_integration_website/wizard/hr_recruitment_post_job.py:0
#: model:ir.model,name:hr_recruitment_integration_website.model_hr_recruitment_post_job_wizard
msgid "Post Job"
msgstr "Julkaise työpaikkailmoitus"

#. module: hr_recruitment_integration_website
#. odoo-python
#: code:addons/hr_recruitment_integration_website/wizard/hr_recruitment_post_job.py:0
msgid "Post is required."
msgstr "Julkaisu vaaditaan."

#. module: hr_recruitment_integration_website
#: model:ir.model.fields.selection,name:hr_recruitment_integration_website.selection__hr_job_post__apply_method__redirect
msgid "Redirect to Website"
msgstr "Ohjaa verkkosivustolle"

#. module: hr_recruitment_integration_website
#: model:ir.model.fields.selection,name:hr_recruitment_integration_website.selection__hr_recruitment_post_job_wizard__apply_method__redirect
msgid "Redirect to company's website"
msgstr "Uudelleenohjaus yrityksen verkkosivustolle"

#. module: hr_recruitment_integration_website
#: model:ir.actions.server,name:hr_recruitment_integration_website.hr_recruitment_post_job_wizard_action_regenerate_post
msgid "Regenerate Post"
msgstr "Uudista julkaisu"

#. module: hr_recruitment_integration_website
#. odoo-python
#: code:addons/hr_recruitment_integration_website/models/hr_job.py:0
msgid "Sorry, the web page is too long for our AI to process."
msgstr "Pahoittelut. Verkkosivu on liian pitkä tekoälymme käsiteltäväksi."

#. module: hr_recruitment_integration_website
#. odoo-python
#: code:addons/hr_recruitment_integration_website/models/hr_job.py:0
msgid "Sorry, we could not generate a response. Please try again later."
msgstr "Valitettavasti emme saaneet vastausta. Yritä myöhemmin uudelleen."

#. module: hr_recruitment_integration_website
#. odoo-python
#: code:addons/hr_recruitment_integration_website/wizard/hr_recruitment_post_job.py:0
msgid ""
"The Job Description will be replaced with the generated one, do you want to "
"continue?"
msgstr "Työpaikan kuvaus korvataan luodulla tekstillä, haluatko jatkaa?"

#. module: hr_recruitment_integration_website
#: model:ir.model.fields,help:hr_recruitment_integration_website.field_hr_recruitment_post_job_wizard__campaign_start_date
msgid "The date when the campaign will start."
msgstr "Päivämäärä, jolloin kampanja alkaa."

#. module: hr_recruitment_integration_website
#. odoo-python
#: code:addons/hr_recruitment_integration_website/wizard/hr_recruitment_post_job.py:0
msgid ""
"The job must be published on the website to generate a post with a redirect "
"apply method."
msgstr ""
"Työpaikka on julkaistava verkkosivustolla, jotta voidaan luoda julkaisu, "
"jossa on redirect apply -menetelmä."

#. module: hr_recruitment_integration_website
#. odoo-python
#: code:addons/hr_recruitment_integration_website/wizard/hr_recruitment_post_job.py:0
msgid ""
"URL is required if the apply method is 'Redirect to company's website'."
msgstr ""
"URL-osoite vaaditaan, jos hakumenetelmänä on \"Ohjaa yrityksen "
"verkkosivulle\"."

#. module: hr_recruitment_integration_website
#. odoo-python
#: code:addons/hr_recruitment_integration_website/models/hr_job.py:0
msgid "You can only generate a post for a published job offer."
msgstr "Voit luoda julkaisun vain julkaistua työpaikkailmoitusta varten."

#. module: hr_recruitment_integration_website
#. odoo-python
#: code:addons/hr_recruitment_integration_website/models/hr_job.py:0
msgid ""
"You have reached the maximum number of requests for this service. Try again "
"later."
msgstr ""
"Tämän palvelun pyyntöjen enimmäismäärä on saavutettu. Yritä myöhemmin "
"uudelleen."
