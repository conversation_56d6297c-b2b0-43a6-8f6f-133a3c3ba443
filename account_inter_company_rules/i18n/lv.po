# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_inter_company_rules
# 
# Translators:
# <PERSON>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <AUTHOR> <EMAIL>, 2024
# AK, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:26+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: AK, 2025\n"
"Language-Team: Latvian (https://app.transifex.com/odoo/teams/41243/lv/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: lv\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n != 0 ? 1 : 2);\n"

#. module: account_inter_company_rules
#. odoo-python
#: code:addons/account_inter_company_rules/models/account_move.py:0
msgid "%(company)s Invoice: %(entry)s"
msgstr ""

#. module: account_inter_company_rules
#: model:ir.model,name:account_inter_company_rules.model_account_move_send
msgid "Account Move Send"
msgstr "Konta pārvietošana Nosūtīt"

#. module: account_inter_company_rules
#: model:ir.model.fields,field_description:account_inter_company_rules.field_account_bank_statement_line__auto_generated
#: model:ir.model.fields,field_description:account_inter_company_rules.field_account_move__auto_generated
msgid "Auto Generated Document"
msgstr ""

#. module: account_inter_company_rules
#. odoo-python
#: code:addons/account_inter_company_rules/models/account_move.py:0
msgid "Automatically generated from %(origin)s of company %(company)s."
msgstr ""

#. module: account_inter_company_rules
#: model:ir.model.fields,field_description:account_inter_company_rules.field_res_company__intercompany_document_state
#: model:ir.model.fields,field_description:account_inter_company_rules.field_res_config_settings__intercompany_document_state
msgid "Automation"
msgstr "Automācija"

#. module: account_inter_company_rules
#: model:ir.model,name:account_inter_company_rules.model_res_company
msgid "Companies"
msgstr "Uzņēmumi"

#. module: account_inter_company_rules
#: model:ir.model,name:account_inter_company_rules.model_res_config_settings
msgid "Config Settings"
msgstr "Konfigurācijas uzstādījumi"

#. module: account_inter_company_rules
#: model:ir.model.fields.selection,name:account_inter_company_rules.selection__res_company__intercompany_document_state__posted
msgid "Create and validate"
msgstr ""

#. module: account_inter_company_rules
#: model:ir.model.fields,field_description:account_inter_company_rules.field_res_company__intercompany_user_id
#: model:ir.model.fields,field_description:account_inter_company_rules.field_res_config_settings__intercompany_user_id
msgid "Create as"
msgstr ""

#. module: account_inter_company_rules
#: model:ir.model.fields.selection,name:account_inter_company_rules.selection__res_company__intercompany_document_state__draft
msgid "Create in draft"
msgstr ""

#. module: account_inter_company_rules
#: model:ir.model.fields,field_description:account_inter_company_rules.field_res_company__intercompany_generate_bills_refund
#: model:ir.model.fields,field_description:account_inter_company_rules.field_res_config_settings__intercompany_generate_bills_refund
#: model_terms:ir.ui.view,arch_db:account_inter_company_rules.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:account_inter_company_rules.view_company_inter_change_inherit_form
msgid "Generate Bills and Refunds"
msgstr "Ģenerēt rēķinus un atmaksas"

#. module: account_inter_company_rules
#: model_terms:ir.ui.view,arch_db:account_inter_company_rules.view_company_inter_change_inherit_form
msgid "Inter-Company Transactions"
msgstr "Darījumi starp uzņēmumiem"

#. module: account_inter_company_rules
#: model:ir.model,name:account_inter_company_rules.model_account_move
msgid "Journal Entry"
msgstr "Grāmatojums"

#. module: account_inter_company_rules
#: model:ir.model,name:account_inter_company_rules.model_account_move_line
msgid "Journal Item"
msgstr "Žurnāla ieraksts"

#. module: account_inter_company_rules
#: model:ir.model.fields,field_description:account_inter_company_rules.field_res_company__intercompany_purchase_journal_id
#: model:ir.model.fields,field_description:account_inter_company_rules.field_res_config_settings__intercompany_purchase_journal_id
msgid "Purchase Journal"
msgstr "Iepirkumu žurnāls"

#. module: account_inter_company_rules
#: model:ir.model.fields,help:account_inter_company_rules.field_res_company__intercompany_user_id
#: model:ir.model.fields,help:account_inter_company_rules.field_res_config_settings__intercompany_user_id
msgid ""
"Responsible user for creation of documents triggered by intercompany rules."
msgstr ""
"Atbildīgais lietotājs par dokumentu izveidi, ko izraisa starpuzņēmumu "
"noteikumi."

#. module: account_inter_company_rules
#: model:ir.model.fields,field_description:account_inter_company_rules.field_account_bank_statement_line__auto_invoice_id
#: model:ir.model.fields,field_description:account_inter_company_rules.field_account_move__auto_invoice_id
msgid "Source Invoice"
msgstr "Sākotnējais rēķins"
