# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_bank_statement_import_camt
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON> <alina.lisnen<PERSON>@erp.co.ua>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:26+0000\n"
"PO-Revision-Date: 2024-09-25 09:43+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2025\n"
"Language-Team: Ukrainian (https://app.transifex.com/odoo/teams/41243/uk/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: uk\n"
"Plural-Forms: nplurals=4; plural=(n % 1 == 0 && n % 10 == 1 && n % 100 != 11 ? 0 : n % 1 == 0 && n % 10 >= 2 && n % 10 <= 4 && (n % 100 < 12 || n % 100 > 14) ? 1 : n % 1 == 0 && (n % 10 ==0 || (n % 10 >=5 && n % 10 <=9) || (n % 100 >=11 && n % 100 <=14 )) ? 2: 3);\n"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "ACH Concentration"
msgstr "Концентрація ACH"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "ACH Corporate Trade"
msgstr "Корпоративна торгівля ACH"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "ACH Credit"
msgstr "Кредит ACH"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "ACH Debit"
msgstr "Дебет ACH"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "ACH Pre-Authorised"
msgstr "Попередньо авторизований ACH"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "ACH Return"
msgstr "Повернено ACH"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "ACH Reversal"
msgstr "Повернення ACH"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "ACH Settlement"
msgstr "Сплата ACH"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "ACH Transaction"
msgstr "Транзакція ACH"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "ARP Debit"
msgstr "Дебет ARP"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Account Balancing"
msgstr "Сальдо рахунків"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Account Closing"
msgstr "Закриття рахунку"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Account Management"
msgstr "Управління рахунком"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Account Opening"
msgstr "Відкриття рахунку"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Account Transfer"
msgstr "Переміщення рахунку"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/models/account_journal.py:0
msgid "Additional Info: %s"
msgstr "Додаткова інформація: %s"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Additional Miscellaneous Credit Operations"
msgstr "Додаткові різні кредитні операції"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Additional Miscellaneous Debit Operations"
msgstr "Додаткові різні дебетові операції"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/models/account_journal.py:0
msgid ""
"Address:\n"
"%s"
msgstr ""
"Адреса:\n"
"%s"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Adjustments"
msgstr "Корегування"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Automatic Transfer"
msgstr "Автоматичне переміщення"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Back Value"
msgstr "Зворотне значення"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Bank Cheque"
msgstr "Банківський чек"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Bank Fees"
msgstr "Банківська комісія"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Blocked Transactions"
msgstr "Заблоковані транзакції"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Bonus Issue/Capitalisation Issue"
msgstr "Проблема з бонусом/Проблема з капіталізацією"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Borrowing fee"
msgstr "Комісія за позику"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Branch Account Transfer"
msgstr "Переміщення рахунку філії"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Branch Deposit"
msgstr "Депозит філії"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Branch Withdrawal"
msgstr "Вилучення філії"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Brokerage fee"
msgstr "Комісія за посередництво"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Buy Sell Back"
msgstr "Купити Продати назад"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "CSD Blocked Transactions"
msgstr "Заблоковані транзакції CSD"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Call on intermediate securities"
msgstr "Виклик проміжних цінних паперів"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Capital Gains Distribution"
msgstr "Розподіл капітальних прибутків"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Cash Deposit"
msgstr "Грошовий депозит"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Cash Dividend"
msgstr "Грошовий дивіденд"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Cash Letter"
msgstr "Готівковий лист"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Cash Letter Adjustment"
msgstr "Коригування грошових листів"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Cash Management"
msgstr "Управління готівкою"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Cash Pooling"
msgstr "Грошове об'єднання"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Cash Withdrawal"
msgstr "Зняти готівку"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Cash in lieu"
msgstr "Грошові кошти"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Certified Customer Cheque"
msgstr "Сертифікований чек клієнта"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Charge/fees"
msgstr "Сплата/комісії"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Charges"
msgstr "Сплати"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/models/account_journal.py:0
msgid "Check Number: %s"
msgstr "Номер чеку: %s"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Cheque"
msgstr "Чек"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Cheque Deposit"
msgstr "Депозит чеку"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Cheque Reversal"
msgstr "Перевірте сторнування"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Cheque Under Reserve"
msgstr "Чек під резервом"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Circular Cheque"
msgstr "Круговий чек"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Clean Collection"
msgstr "Чиста колекція"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Client Owned Collateral"
msgstr "Застава, що належить клієнту"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Collateral Management"
msgstr "Управління заставою"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Commission"
msgstr "Комісія"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Commission excluding taxes"
msgstr "Комісія без податків"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Commission including taxes"
msgstr "Комісія з податками"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Commodities"
msgstr "Товари"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Compensation/Claims"
msgstr "Компенсація/претензії"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Consumer Loans"
msgstr "Споживчі кредити"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Controlled Disbursement"
msgstr "Контрольована виплата"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Conversion"
msgstr "Зміна"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Corporate Action"
msgstr "Корпоративна дія"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Corporate Own Account Transfer"
msgstr "Корпоративний переказ власних рахунків"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Corporate Rebate"
msgstr "Корпоративна знижка"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Corporate mark broker owned"
msgstr "Корпоративна позначка власника брокера"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Corporate mark client owned"
msgstr "Корпоративна позначка власника клієнта"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/models/account_journal.py:0
msgid "Counter Party: %(partner)s"
msgstr "Протилежна сторона: %(partner)s"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Counter Transactions"
msgstr "Протилежні транзакції"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Credit Adjustment"
msgstr "Коригування кредиту"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Credit Adjustments"
msgstr "Коригування кредиту"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Credit Card Payment"
msgstr "Оплата кредитною карткою"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Credit Transfer with agreed Commercial Information"
msgstr "Кредитний переказ із узгодженою комерційною інформацією"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Cross Trade"
msgstr "Перехресна торгівля"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Cross-Border"
msgstr "Транскордонний"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Cross-Border Cash Withdrawal"
msgstr "Зняття транскордонної готівки"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Cross-Border Credit Card Payment"
msgstr "Оплата транскордонною кредитною карткою"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Cross-Border Credit Transfer"
msgstr "Транскордонний переказ кредиту"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Cross-Border Direct Debit"
msgstr "Прямий транскордонний дебет"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Cross-Border Intra Company Transfer"
msgstr "Транскордонний переказ внутрішньої компанії"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Cross-Border Payroll/Salary Payment"
msgstr "Транскордонна оплата праці"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Cross-Border Standing Order"
msgstr "Постійне транскордонне замовлення"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Crossed Cheque"
msgstr "Перекреслений чек"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Custody"
msgstr "Опіка"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Custody Collection"
msgstr "Колекція опіки"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Customer Card Transactions"
msgstr "Транзакції картки клієнта"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Debit"
msgstr "Дебет"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Debit Adjustments"
msgstr "Коригування дебету"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Decrease in Value"
msgstr "Зниження вартості"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Delivery"
msgstr "Доставка"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Deposit"
msgstr "Попередня оплата"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Deposit/Contribution"
msgstr "Депозит/Накопичення"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Depositary Receipt Issue"
msgstr "Проблема депозитної квитанції"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Derivatives"
msgstr "Похідні"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Direct Debit"
msgstr "Прямий дебет"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Direct Debit Payment"
msgstr "Платіж прямого дебету"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Direct Debit under reserve"
msgstr "Прямий дебет під резервом"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Discounted Draft"
msgstr "Чернетка зі знижкою"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Dishonoured/Unpaid Draft"
msgstr "Пропущена/Неоплачена чернетка"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Dividend Option"
msgstr "Функція дивіденду"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Dividend Reinvestment"
msgstr "Реінвестування дивідендів"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Documentary Collection"
msgstr "Документальна колекція"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Documentary Credit"
msgstr "Документальний кредит"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Domestic Credit Transfer"
msgstr "Внутрішній кредитний переказ"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Draft Maturity Change"
msgstr "Розвинута зміна чернетки"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Drafts/BillOfOrders"
msgstr "Чернетки/Рахунок замовлень"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Drawdown"
msgstr "Зниження"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Drawing"
msgstr "Підтягування"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Dutch Auction"
msgstr "Голландський аукціон"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/models/account_journal.py:0
msgid "End to end ID: %s"
msgstr "ID від кінця до кінця: %s"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/models/account_journal.py:0
msgid "Entry Info: %s"
msgstr "Інформація запису: %s"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Equity Premium Reserve"
msgstr "Резерв власного капіталу"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Equity mark broker owned"
msgstr "Капітальна позначка власника брокера"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Equity mark client owned"
msgstr "Капітальна позначка власника клієнта"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Exchange"
msgstr "Обмін"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Exchange Rate Adjustment"
msgstr "Коригування курсу валют"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Exchange Traded"
msgstr "Обмін валют"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Exchange Traded CCP"
msgstr "Продаж валюти CCP"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Exchange Traded Non-CCP"
msgstr "Продаж валюти Non-CCP"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Extended Domain"
msgstr "Розширений домен"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "External Account Transfer"
msgstr "Зовнішній переказ рахунків"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Factor Update"
msgstr "Оновлення факторів"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Fees"
msgstr "Комісія"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Fees, Commission , Taxes, Charges and Interest"
msgstr "Збори, комісії, податки, збори та відсотки"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Final Maturity"
msgstr "Остаточна зрілість"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Final Payment"
msgstr "Остаточний платіж"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Financial Institution Credit Transfer"
msgstr "Кредитний переказ фінансової установи"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Financial Institution Direct Debit Payment"
msgstr "Оплата прямого дебету фінансової установи"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Financial Institution Own Account Transfer"
msgstr "Переказ власного рахунку фінансової установи"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Fixed Deposit Interest Amount"
msgstr "Сума відсотків за фіксованим депозитом"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Fixed Term Deposits"
msgstr "Фіксовані строкові депозити"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Fixed Term Loans"
msgstr "Фіксовані строкові позики"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Float adjustment"
msgstr "Плаваюче коригування"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Foreign Cheque"
msgstr "Іноземний чек"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Foreign Cheque Under Reserve"
msgstr "Іноземний чек під резервом"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Foreign Currency Deposit"
msgstr "Депозит в іноземній валюті"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Foreign Currency Withdrawal"
msgstr "Зняття іноземної валюти"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Foreign Exchange"
msgstr "Іноземна валюта"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Forwards"
msgstr "Переміщення"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Forwards broker owned collateral"
msgstr "Застава, що належить брокеру"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Forwards client owned collateral"
msgstr "Застава, що належить клієнту"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Freeze of funds"
msgstr "Заморожування коштів"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Full Call / Early Redemption"
msgstr "Повний виклик / достроковий викуп"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Future Variation Margin"
msgstr "Майбутня маржа варіації"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Futures"
msgstr "Термінові контракти"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Futures Commission"
msgstr "Комісія термінових контрактів"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Futures Residual Amount"
msgstr "Залишкова сума термінових контрактів"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Guarantees"
msgstr "Гарантії"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Inspeci/Share Exchange"
msgstr "Inspeci/Share Exchange"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/models/account_journal.py:0
msgid "Instruction ID: %s"
msgstr "ID інструкції: %s"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Interest"
msgstr "Борг"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Interest Payment"
msgstr "Оплата боргу"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Interest Payment with Principle"
msgstr "Виплата боргу з положенням"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Internal Account Transfer"
msgstr "Внутрішній переказ рахунку"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Internal Book Transfer"
msgstr "Внутрішній переказ бухгалтерської книги"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Intra Company Transfer"
msgstr "Внутрішній переказ компанії"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Invoice Accepted with Differed Due Date"
msgstr "Рахунок прийнято з дати відтермінування"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Issued Cash Concentration Transactions"
msgstr "Випущені операції з концентрацією готівки"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Issued Cheques"
msgstr "Видані чеки"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Issued Credit Transfers"
msgstr "Видані кредитні перекази"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Issued Direct Debits"
msgstr "Видані прямі дебети"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Issued Real Time Credit Transfer"
msgstr "Видані кредитні перекази у реальному часі"

#. module: account_bank_statement_import_camt
#: model:ir.model,name:account_bank_statement_import_camt.model_account_journal
msgid "Journal"
msgstr "Журнал"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Lack"
msgstr "Нестача"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Lending Broker Owned Cash Collateral"
msgstr "Кредитний брокер володіє грошовим забезпеченням"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Lending Client Owned Cash Collateral"
msgstr "Кредитний клієнт, що володіє грошовим забезпеченням"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Lending income"
msgstr "Кредитний дохід"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Liquidation Dividend / Liquidation Payment"
msgstr "Ліквідація дивідендів/ліквідація платежів"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Listed Derivatives – Futures"
msgstr "Перелічені похідні - ф'ючерси"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Listed Derivatives – Options"
msgstr "Перелічені похідні - Параметри"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Loans, Deposits & Syndications"
msgstr "Кредити, депозити та синдикації"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Lockbox Transactions"
msgstr "Транзакції Lockbox "

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Management Fees"
msgstr "Комісія за обслуговування"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/models/account_journal.py:0
msgid "Mandate ID: %s"
msgstr "ID мандату: %s"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Margin Payments"
msgstr "Кредитні платежі"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Margin client owned cash collateral"
msgstr "Кредитний клієнт володів грошовими заставами"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Merchant Card Transactions"
msgstr "Операції з торговими картками"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Merger"
msgstr "Злиття"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Miscellaneous Credit Operations"
msgstr "Інші кредитні операції"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Miscellaneous Debit Operations"
msgstr "Інші дебетові операції "

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Miscellaneous Deposit"
msgstr "Інший депозит"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Miscellaneous Securities Operations"
msgstr "Інші операції з цінними паперами"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Mixed Deposit"
msgstr "Змішаний депозит"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Mortgage Loans"
msgstr "Іпотечні кредити"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Netting"
msgstr "Сітка"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid ""
"No exchange rate was found to convert an amount into the currency of the "
"journal"
msgstr "Не знайдено обміну валют для конвертації суми у валюту журналу"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Non Deliverable"
msgstr "Не доставляється"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Non Settled"
msgstr "Не врегульовано"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Non Syndicated"
msgstr "Не синдиковано"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Non Taxable commissions"
msgstr "Неоподатковані комісії"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Non-Presented Circular Cheque"
msgstr "Непредставлений круговий чек"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Not available"
msgstr "Недоступний"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Notice Deposits"
msgstr "Сповіщення депозитів"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Notice Loans"
msgstr "Сповіщення кредитів"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "OTC"
msgstr "OTC"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "OTC CCP"
msgstr "OTC CCP"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "OTC Derivatives – Bonds"
msgstr "OTC Похідні - облігації"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "OTC Derivatives – Credit Derivatives"
msgstr "OTC Похідні - кредитні деривативи"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "OTC Derivatives – Equity"
msgstr "OTC Похідні - власний капітал"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "OTC Derivatives – Interest Rates"
msgstr "OTC Похідні - процентні ставки"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "OTC Derivatives – Structured Exotic Derivatives"
msgstr "OTC Похідні - структуровані екзотичні похідні"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "OTC Derivatives – Swaps"
msgstr "OTC Похідні - обмін"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "OTC Non-CCP"
msgstr "OTC Non-CCP"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Odd Lot Sale/Purchase"
msgstr "Продаж/Купівля додаткових партій"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "One-Off Direct Debit"
msgstr "Разовий прямий дебет"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Open Cheque"
msgstr "Відкритий чек"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Opening & Closing"
msgstr "Відкриття та закриття"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Option broker owned collateral"
msgstr "Опційний брокер володіє заставою"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Option client owned collateral"
msgstr "Опційний клієнт володіє заставою"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Options"
msgstr "Опції"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Order Cheque"
msgstr "Чек замовлення"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Other"
msgstr "Інше"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Overdraft"
msgstr "Кредитний ліміт на ланч"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Overdraft Charge"
msgstr "Стягнення кредитного ліміту"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Pair-Off"
msgstr "Групувати"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Partial Payment"
msgstr "Часткова оплата"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Partial Redemption Without Reduction of Nominal Value"
msgstr "Часткова купівля без зменшення номінальної вартості"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Partial Redemption with reduction of nominal value"
msgstr "Часткова купівля зі зменшенням номінальної вартості"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Payments"
msgstr "Платежі"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Payroll/Salary Payment"
msgstr "Оплата праці"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Placement"
msgstr "Розташування"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/models/account_journal.py:0
msgid ""
"Please check the currency on your bank journal.\n"
"No statements in currency %s were found in this CAMT file."
msgstr ""
"Перевірте валюту на вашому банківському журналі.\n"
"Немає виписок у валюті %s що знайдено у цьому файлі CAMT."

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/models/account_journal.py:0
msgid ""
"Please set the IBAN account on your bank journal.\n"
"\n"
"This CAMT file is targeting several IBAN accounts but none match the current journal."
msgstr ""
"Встановіть рахунок  IBAN у вашому банківському журналі.\n"
"\n"
"Цей файл CAMT націлений на кілька рахунків IBAN, але жоден не відповідає поточному журналу."

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Point-of-Sale (POS) Payment"
msgstr "Платіж точки продажу (POS)"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Point-of-Sale (POS) Payment - Debit Card"
msgstr "Платіж точки продажу (POS) - Дебетова картка"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Portfolio Move"
msgstr "Переміщення портфоліо"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Posting Error"
msgstr "Помилка публікування"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Pre-Authorised Direct Debit"
msgstr "Попередньо авторизований прямий дебет"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Precious Metal"
msgstr "Коштовний метал"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Principal Pay-down/pay-up"
msgstr "Основна оплата/виплата"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Principal Payment"
msgstr "Основний платіж"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Priority Credit Transfer"
msgstr "Пріоритетний переказ кредиту"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Priority Issue"
msgstr "Пріоритетний випуск"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Put Redemption"
msgstr "Покласти викуп"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Received Cash Concentration Transactions"
msgstr "Отримані операції з концентрацією готівки"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Received Cheques"
msgstr "Отримані чеки"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Received Credit Transfers"
msgstr "Отримані кредитні перекази"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Received Direct Debits"
msgstr "Отримані прямі дебети"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Received Real Time Credit Transfer"
msgstr "Отриманий кредитний переказ в режимі реального часу"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Redemption"
msgstr "Викуп"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Redemption Asset Allocation"
msgstr "Розподіл активів викупу"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Redemption Withdrawing Plan"
msgstr "План вилучення викупу"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Reimbursements"
msgstr "Відшкодування"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Renewal"
msgstr "Оновлення"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Repayment"
msgstr "Погашення"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Repo"
msgstr "Сховище"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Repurchase offer/Issuer Bid/Reverse Rights."
msgstr "Пропозиція щодо викупу/ставка емітента/зворотні права."

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Reset Payment"
msgstr "Скинути платіж"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Reversal due to Payment Cancellation Request"
msgstr "Відміна через запит на скасування платежу"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Reversal due to Payment Return/reimbursement of a Credit Transfer"
msgstr ""
"Скасування за рахунок повернення платежу/відшкодування за рахунок кредитного"
" переказу"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Reversal due to Payment Reversal"
msgstr "Скасування за рахунок скасування платежу"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Reversal due to Return/Unpaid Direct Debit"
msgstr "Скасування за рахунок повернення/неоплаченого прямого дебету"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Reversal due to a Payment Cancellation Request"
msgstr "Скасування через запит на скасування платежу"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Reverse Repo"
msgstr "Сховище повернення"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Rights Issue/Subscription Rights/Rights Offer"
msgstr "Права випуску/Права підписки/Права пропозиції"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "SEPA B2B Direct Debit"
msgstr "Прямий дебет SEPA B2B"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "SEPA Core Direct Debit"
msgstr "Прямий дебет SEPA Core"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "SEPA Credit Transfer"
msgstr "Кредитні перекази SEPA"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Same Day Value Credit Transfer"
msgstr "Вартість переказу кредитів у той самий день"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Securities"
msgstr "Цінні папери"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Securities Borrowing"
msgstr "Запозичення цінних паперів"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Securities Lending"
msgstr "Кредитування цінних паперів"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Sell Buy Back"
msgstr "Продаж Купівля назад"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Settlement"
msgstr "Сплата"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Settlement after collection"
msgstr "Сплата після збирання"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Settlement against bank guarantee"
msgstr "Розрахунок проти банківської гарантії"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Settlement at Maturity"
msgstr "Розрахунок у зрілості"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Settlement of Sight Export document"
msgstr "Розрахунок документа на експорт"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Settlement of Sight Import document"
msgstr "Розрахунок документа на імпорт"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Settlement under reserve"
msgstr "Розрахунок під резервом"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Smart-Card Payment"
msgstr "Оплата смарт-карткою"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Spots"
msgstr "Коректування"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Stamp duty"
msgstr "Гербовий збір"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Stand-By Letter Of Credit"
msgstr "Акредитив в режимі очікування"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Standing Order"
msgstr "Постійний порядок"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Subscription"
msgstr "Підписка"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Subscription Asset Allocation"
msgstr "Розподіл активів підписки"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Subscription Savings Plan"
msgstr "План заощаджень підписки"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Swap Payment"
msgstr "Оплата обміну"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Swap broker owned collateral"
msgstr "Брокер обміну володіє заставою"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Swaps"
msgstr "Обміни"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Sweep"
msgstr "Очистити"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Sweeping"
msgstr "Очищення"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Switch"
msgstr "Перемикач"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Syndicated"
msgstr "Синдиковано"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Syndications"
msgstr "Синдикації"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "TBA closing"
msgstr "Закриття TBA"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Tax Reclaim"
msgstr "Податкові вимоги"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Taxes"
msgstr "Податки"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Tender"
msgstr "Тендер"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Topping"
msgstr "Найвищий"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Trade"
msgstr "Торгівля"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Trade Services"
msgstr "Послуги торгівлі"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Trade, Clearing and Settlement"
msgstr "Торгівля, розмитнення та розрахунок"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Transaction Fees"
msgstr "Комісія переказу"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/models/account_journal.py:0
msgid "Transaction ID: %s"
msgstr "ID проведення: %s"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Transfer In"
msgstr "Перемістити в"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Transfer Out"
msgstr "Перемістити з"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Travellers Cheques Deposit"
msgstr "Депозит дорожнього чеку"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Travellers Cheques Withdrawal"
msgstr "Зняття готівки з дорожнього чеку"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Treasury Tax And Loan Service"
msgstr "Податок до казначейства та послуга позики"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Triparty Repo"
msgstr "Тристороннє сховище"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Triparty Reverse Repo"
msgstr "Тристороннє сховище повернення"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Turnaround"
msgstr "Повернути"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Underwriting Commission"
msgstr "Комісія андеррайтингу"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Unpaid Card Transaction"
msgstr "Неоплачена транзакція карткою"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Unpaid Cheque"
msgstr "Неоплачений чек"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Unpaid Foreign Cheque"
msgstr "Неоплачений іноземний чек"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Upfront Payment"
msgstr "Авансовий платіж"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Value Date"
msgstr "Дата значення"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Warrant Exercise/Warrant Conversion"
msgstr "Здійснення гарантій/Перетворення гарантій"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Withdrawal/distribution"
msgstr "Вилучення/розподіл"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Withholding Tax"
msgstr "Утримання податків"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "YTD Adjustment"
msgstr "Коригування YTD"

#. module: account_bank_statement_import_camt
#. odoo-python
#: code:addons/account_bank_statement_import_camt/lib/camt.py:0
msgid "Zero Balancing"
msgstr "Нульовий баланс"
