# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* helpdesk_repair
# 
# Translators:
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <AUTHOR> <EMAIL>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:27+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: abc Def <<EMAIL>>, 2024\n"
"Language-Team: Turkish (https://app.transifex.com/odoo/teams/41243/tr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: tr\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: helpdesk_repair
#: model:ir.actions.act_window,name:helpdesk_repair.action_repair_order_form
msgid "Create a Repair Order"
msgstr "Onarım Siparişi  Oluştur"

#. module: helpdesk_repair
#: model:ir.model,name:helpdesk_repair.model_helpdesk_ticket
msgid "Helpdesk Ticket"
msgstr "Yardım Masası Talebi"

#. module: helpdesk_repair
#: model_terms:ir.ui.view,arch_db:helpdesk_repair.helpdesk_ticket_view_form_inherit_stock_user
msgid "Repair"
msgstr "Onarım"

#. module: helpdesk_repair
#. odoo-python
#: code:addons/helpdesk_repair/models/repair.py:0
msgid "Repair Created"
msgstr "Onarım Oluşturuldu"

#. module: helpdesk_repair
#: model:ir.model,name:helpdesk_repair.model_repair_order
msgid "Repair Order"
msgstr "Onarım Siparişi"

#. module: helpdesk_repair
#. odoo-python
#: code:addons/helpdesk_repair/models/helpdesk_ticket.py:0
#: model_terms:ir.ui.view,arch_db:helpdesk_repair.helpdesk_ticket_view_form_inherit_helpdesk_repair
msgid "Repairs"
msgstr "Onarımlar"

#. module: helpdesk_repair
#: model:ir.model.fields,field_description:helpdesk_repair.field_helpdesk_ticket__repairs_count
msgid "Repairs Count"
msgstr "Onarımların sayısı"
