# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* quality_iot
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-03-26 20:49+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON><PERSON>, 2025\n"
"Language-Team: Italian (https://app.transifex.com/odoo/teams/41243/it/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: it\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: quality_iot
#. odoo-javascript
#: code:addons/quality_iot/static/src/iot_picture/iot_picture.js:0
msgid "Camera configuration error"
msgstr "Errore configurazione fotocamera"

#. module: quality_iot
#. odoo-javascript
#: code:addons/quality_iot/static/src/iot_picture/iot_picture.js:0
msgid "Capturing image..."
msgstr "Acquisizione immagine in corso..."

#. module: quality_iot
#. odoo-javascript
#: code:addons/quality_iot/static/src/iot_measure.js:0
#: code:addons/quality_iot/static/src/iot_picture/iot_picture.js:0
msgid "Connection to device failed"
msgstr "Connessione al dispositivo non riuscita"

#. module: quality_iot
#: model:ir.model.fields,field_description:quality_iot.field_quality_point__device_id
msgid "Device"
msgstr "Dispositivo"

#. module: quality_iot
#: model:ir.model.fields,field_description:quality_iot.field_quality_check__device_name
msgid "Device Name: "
msgstr "Nome dispositivo: "

#. module: quality_iot
#: model:ir.model.fields,field_description:quality_iot.field_quality_check__ip
msgid "Domain Address"
msgstr "Indirizzo dominio"

#. module: quality_iot
#. odoo-javascript
#: code:addons/quality_iot/static/src/iot_measure.xml:0
msgid "Get Measurement"
msgstr "Prendi le misure"

#. module: quality_iot
#. odoo-javascript
#: code:addons/quality_iot/static/src/iot_measure.js:0
msgid "Getting measurement..."
msgstr "Misurazione in corso..."

#. module: quality_iot
#: model:ir.model,name:quality_iot.model_iot_device
msgid "IOT Device"
msgstr "Dispositivo IoT"

#. module: quality_iot
#: model:ir.model.fields,field_description:quality_iot.field_quality_check__identifier
msgid "Identifier"
msgstr "Identificatore"

#. module: quality_iot
#. odoo-javascript
#: code:addons/quality_iot/static/src/iot_measure.js:0
msgid "Measurement device configuration error"
msgstr "Errore configurazione strumento di misura"

#. module: quality_iot
#. odoo-javascript
#: code:addons/quality_iot/static/src/iot_measure.js:0
#: code:addons/quality_iot/static/src/iot_picture/iot_picture.js:0
msgid "Please check if the device is still connected."
msgstr "Controllare che il dispositivo sia ancora connesso."

#. module: quality_iot
#. odoo-javascript
#: code:addons/quality_iot/static/src/iot_picture/iot_picture.js:0
msgid "Please link the corresponding Quality Control Point to the camera."
msgstr "Collega il punto controllo qualità corrispondente alla fotocamera."

#. module: quality_iot
#. odoo-javascript
#: code:addons/quality_iot/static/src/iot_measure.js:0
msgid ""
"Please link the corresponding Quality Control Point to the measurement "
"device."
msgstr ""
"Collega il punto controllo qualità corrispondente allo strumento di misura."

#. module: quality_iot
#: model:ir.model.fields,field_description:quality_iot.field_iot_device__qcp_test_type
msgid "Qcp Test Type"
msgstr "Tipo di prova PCQ"

#. module: quality_iot
#: model:ir.model,name:quality_iot.model_quality_check
msgid "Quality Check"
msgstr "Controllo qualità"

#. module: quality_iot
#: model:ir.model,name:quality_iot.model_quality_point
msgid "Quality Control Point"
msgstr "Punto controllo qualità"

#. module: quality_iot
#: model_terms:ir.ui.view,arch_db:quality_iot.iot_device_view_form_inherit
msgid "Quality Control Points"
msgstr "Punti di controllo qualità"

#. module: quality_iot
#: model:ir.model.fields,field_description:quality_iot.field_iot_device__quality_point_ids
msgid "Quality Point"
msgstr "Punto qualità"
