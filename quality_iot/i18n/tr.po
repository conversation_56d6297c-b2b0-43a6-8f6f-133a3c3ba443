# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* quality_iot
# 
# Translators:
# emre oktem, 2024
# <PERSON>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <AUTHOR> <EMAIL>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# Yedigen, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-03-26 20:49+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Yedigen, 2024\n"
"Language-Team: Turkish (https://app.transifex.com/odoo/teams/41243/tr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: tr\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: quality_iot
#. odoo-javascript
#: code:addons/quality_iot/static/src/iot_picture/iot_picture.js:0
msgid "Camera configuration error"
msgstr ""

#. module: quality_iot
#. odoo-javascript
#: code:addons/quality_iot/static/src/iot_picture/iot_picture.js:0
msgid "Capturing image..."
msgstr ""

#. module: quality_iot
#. odoo-javascript
#: code:addons/quality_iot/static/src/iot_measure.js:0
#: code:addons/quality_iot/static/src/iot_picture/iot_picture.js:0
msgid "Connection to device failed"
msgstr "Cihaza bağlantı başarısız"

#. module: quality_iot
#: model:ir.model.fields,field_description:quality_iot.field_quality_point__device_id
msgid "Device"
msgstr "Makina"

#. module: quality_iot
#: model:ir.model.fields,field_description:quality_iot.field_quality_check__device_name
msgid "Device Name: "
msgstr "Cihaz Adı:"

#. module: quality_iot
#: model:ir.model.fields,field_description:quality_iot.field_quality_check__ip
msgid "Domain Address"
msgstr "Alan Adresi"

#. module: quality_iot
#. odoo-javascript
#: code:addons/quality_iot/static/src/iot_measure.xml:0
msgid "Get Measurement"
msgstr ""

#. module: quality_iot
#. odoo-javascript
#: code:addons/quality_iot/static/src/iot_measure.js:0
msgid "Getting measurement..."
msgstr ""

#. module: quality_iot
#: model:ir.model,name:quality_iot.model_iot_device
msgid "IOT Device"
msgstr "IOT Cihazı"

#. module: quality_iot
#: model:ir.model.fields,field_description:quality_iot.field_quality_check__identifier
msgid "Identifier"
msgstr "Tanımlayıcı"

#. module: quality_iot
#. odoo-javascript
#: code:addons/quality_iot/static/src/iot_measure.js:0
msgid "Measurement device configuration error"
msgstr ""

#. module: quality_iot
#. odoo-javascript
#: code:addons/quality_iot/static/src/iot_measure.js:0
#: code:addons/quality_iot/static/src/iot_picture/iot_picture.js:0
msgid "Please check if the device is still connected."
msgstr "Lütfen cihazın hala bağlı olup olmadığını kontrol edin."

#. module: quality_iot
#. odoo-javascript
#: code:addons/quality_iot/static/src/iot_picture/iot_picture.js:0
msgid "Please link the corresponding Quality Control Point to the camera."
msgstr ""

#. module: quality_iot
#. odoo-javascript
#: code:addons/quality_iot/static/src/iot_measure.js:0
msgid ""
"Please link the corresponding Quality Control Point to the measurement "
"device."
msgstr ""

#. module: quality_iot
#: model:ir.model.fields,field_description:quality_iot.field_iot_device__qcp_test_type
msgid "Qcp Test Type"
msgstr "Qcp Sınama Türü"

#. module: quality_iot
#: model:ir.model,name:quality_iot.model_quality_check
msgid "Quality Check"
msgstr "Kalite Kontrol"

#. module: quality_iot
#: model:ir.model,name:quality_iot.model_quality_point
msgid "Quality Control Point"
msgstr "Kalite Kontrol Noktası"

#. module: quality_iot
#: model_terms:ir.ui.view,arch_db:quality_iot.iot_device_view_form_inherit
msgid "Quality Control Points"
msgstr "Kalite Kontrol Noktaları"

#. module: quality_iot
#: model:ir.model.fields,field_description:quality_iot.field_iot_device__quality_point_ids
msgid "Quality Point"
msgstr "Kalite Noktası"
