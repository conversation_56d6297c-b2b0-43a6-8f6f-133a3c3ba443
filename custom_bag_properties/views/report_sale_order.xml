<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <template id="bag_configuration_saleorder_document_inherit" inherit_id="sale.report_saleorder_document">
<!--        <xpath expr="//<th name="th_description" position="after">-->
        <xpath expr="//th[@name='th_description']" position="after">
            <th name="bag_color" class="text-start">Bag Color</th>
            <th name="size_name" class="text-start">Size</th>
            <th name="thickness_name" class="text-start">Thickness</th>
            <th name="ink_color" class="text-start">Color Of Ink</th>
        </xpath>

        <xpath expr="//td[@name='td_name']" position="after">
            <td name="bag_color_id"><span t-out="line.bag_color_id.name"/></td>
            <td name="size_id"><span t-out="line.size_id.name"/></td>
            <td name="thickness_id"><span t-out="line.thickness_id.name"/></td>
            <td name="ink_color_id"><span t-out="line.ink_color_id.name"/></td>
        </xpath>
    </template>

</odoo>
