# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_account_accountant
# 
# Translators:
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:28+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Chinese (China) (https://app.transifex.com/odoo/teams/41243/zh_CN/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_CN\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: sale_account_accountant
#: model:ir.model,name:sale_account_accountant.model_bank_rec_widget
msgid "Bank reconciliation widget for a single statement line"
msgstr "用于单个对账单行的银行对账工具"

#. module: sale_account_accountant
#: model:ir.model.fields,field_description:sale_account_accountant.field_bank_rec_widget__matched_sale_order_ids
msgid "Matched Sale Order"
msgstr "匹配的销售订单"

#. module: sale_account_accountant
#: model:ir.model,name:sale_account_accountant.model_account_reconcile_model
msgid ""
"Preset to create journal entries during a invoices and payments matching"
msgstr "预设在发票和付款匹配期间创建日记账分录"

#. module: sale_account_accountant
#. odoo-javascript
#: code:addons/sale_account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
msgid "Sale Orders"
msgstr "销售订单"

#. module: sale_account_accountant
#. odoo-javascript
#: code:addons/sale_account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
msgid "There are"
msgstr "这儿"

#. module: sale_account_accountant
#. odoo-javascript
#: code:addons/sale_account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
msgid "matching the communication of the bank statement line."
msgstr "与银行对账单的通讯线路相匹配。"

#. module: sale_account_accountant
#. odoo-javascript
#: code:addons/sale_account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
msgid "uninvoiced sales orders"
msgstr "未开票的销售订单"
