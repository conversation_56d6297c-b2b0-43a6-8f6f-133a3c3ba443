# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* pos_pricer
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:25+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON><PERSON>, 2025\n"
"Language-Team: Spanish (https://app.transifex.com/odoo/teams/41243/es/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: pos_pricer
#: model:ir.model.constraint,message:pos_pricer.constraint_pricer_tag_name_unique
msgid "A Pricer tag with this barcode id already exists"
msgstr "Ya existe una etiqueta Pricer con este ID de código de barras"

#. module: pos_pricer
#: model:ir.model.fields,field_description:pos_pricer.field_pricer_tag__pricer_store_id
msgid "Associated Pricer Store"
msgstr "Tienda Pricer asociada"

#. module: pos_pricer
#: model:ir.model.fields,field_description:pos_pricer.field_pricer_tag__product_id
msgid "Associated Product"
msgstr "Producto asociado"

#. module: pos_pricer
#: model:ir.model.fields,field_description:pos_pricer.field_pricer_store__auth_url
msgid "Auth Url"
msgstr "URL auth"

#. module: pos_pricer
#: model:product.pricelist,name:pos_pricer.pricer_demo_pricelist
msgid "Christmas"
msgstr "Navidad"

#. module: pos_pricer
#: model:ir.model.fields,field_description:pos_pricer.field_pricer_store__create_or_update_products_url
msgid "Create Or Update Products Url"
msgstr "Crear o actualizar URL de productos"

#. module: pos_pricer
#: model:ir.model.fields,field_description:pos_pricer.field_pricer_store__create_uid
#: model:ir.model.fields,field_description:pos_pricer.field_pricer_tag__create_uid
msgid "Created by"
msgstr "Creado por"

#. module: pos_pricer
#: model:ir.model.fields,field_description:pos_pricer.field_pricer_store__create_date
#: model:ir.model.fields,field_description:pos_pricer.field_pricer_tag__create_date
msgid "Created on"
msgstr "Creado el"

#. module: pos_pricer
#: model:ir.model.fields,help:pos_pricer.field_pricer_store__last_update_datetime
msgid "Date and time of the last synchronization with Pricer"
msgstr "Fecha y hora de la última sincronización con Pricer"

#. module: pos_pricer
#: model:ir.model.fields,field_description:pos_pricer.field_pricer_store__display_name
#: model:ir.model.fields,field_description:pos_pricer.field_pricer_tag__display_name
msgid "Display Name"
msgstr "Nombre mostrado"

#. module: pos_pricer
#. odoo-python
#: code:addons/pos_pricer/models/pricer_store.py:0
msgid "Error: %(status_code)s - %(message)s"
msgstr "Error: %(status_code)s - %(message)s"

#. module: pos_pricer
#. odoo-python
#: code:addons/pos_pricer/models/pricer_store.py:0
msgid "Error: check Pricer credentials"
msgstr "Error: revisar credenciales de Pricer"

#. module: pos_pricer
#. odoo-python
#: code:addons/pos_pricer/models/pricer_store.py:0
msgid "Failed to unlink Pricer tag %(pricer_tag)s at API url %(api_url)s"
msgstr ""
"No se pudo desvincular la etiqueta de Pricer %(pricer_tag)s en la URL de la "
"API %(api_url)s"

#. module: pos_pricer
#: model:ir.model.fields,field_description:pos_pricer.field_pricer_store__id
#: model:ir.model.fields,field_description:pos_pricer.field_pricer_tag__id
msgid "ID"
msgstr "ID"

#. module: pos_pricer
#: model:ir.model.fields,help:pos_pricer.field_pricer_store__pricer_store_identifier
msgid "Identifier of the store in the Pricer system"
msgstr "Identificador de la tienda en el sistema Pricer"

#. module: pos_pricer
#. odoo-javascript
#: code:addons/pos_pricer/static/src/views/pricer_quick_pairing_form_view.js:0
msgid ""
"Invalid tag name. Should be 1 letter followed by 16 digits. Example: "
"'N4081315789813275'"
msgstr ""
"Nombre de etiqueta no válido, debería ser 1 letra seguida de 16 dígitos, por"
" ejemplo: 'N4081315789813275'"

#. module: pos_pricer
#: model:ir.model.fields,help:pos_pricer.field_pricer_tag__name
msgid "It is recommended to use a barcode scanner for input"
msgstr "Lo recomendable es usar un lector de código de barras para la entrada"

#. module: pos_pricer
#: model:ir.model.fields,field_description:pos_pricer.field_pricer_store__last_update_datetime
msgid "Last Update"
msgstr "Última actualización"

#. module: pos_pricer
#: model:ir.model.fields,field_description:pos_pricer.field_pricer_store__last_update_status_message
msgid "Last Update Status"
msgstr "Último estado de actualización"

#. module: pos_pricer
#: model:ir.model.fields,field_description:pos_pricer.field_pricer_store__write_uid
#: model:ir.model.fields,field_description:pos_pricer.field_pricer_tag__write_uid
msgid "Last Updated by"
msgstr "Última actualización por"

#. module: pos_pricer
#: model:ir.model.fields,field_description:pos_pricer.field_pricer_store__write_date
#: model:ir.model.fields,field_description:pos_pricer.field_pricer_tag__write_date
msgid "Last Updated on"
msgstr "Última actualización el"

#. module: pos_pricer
#: model:ir.model.fields,field_description:pos_pricer.field_pricer_store__link_tags_url
msgid "Link Tags Url"
msgstr "Vincular URL de las etiquetas"

#. module: pos_pricer
#: model:ir.model.fields,help:pos_pricer.field_pricer_store__pricer_login
msgid "Login of your Pricer account"
msgstr "Datos de inicio de sesión de su cuenta Pricer"

#. module: pos_pricer
#. odoo-javascript
#: code:addons/pos_pricer/static/src/views/pricer_quick_pairing_form_view.js:0
msgid "No product found for barcode '%s'"
msgstr "No se encontró ningún producto para el código de barras '%s'"

#. module: pos_pricer
#: model:ir.model.fields,field_description:pos_pricer.field_product_product__on_sale_price
msgid "On Sale Price"
msgstr "Precio con descuento"

#. module: pos_pricer
#: model:ir.actions.server,name:pos_pricer.pricer_sync_cron_ir_actions_server
msgid "POS Pricer: tags update synchronization "
msgstr "Pricer para TPV: sincronización de la actualización de las etiquetas"

#. module: pos_pricer
#: model:ir.model.fields,help:pos_pricer.field_pricer_store__pricer_password
msgid "Password of your Pricer account"
msgstr "Contraseña de su cuenta de Pricer"

#. module: pos_pricer
#: model:ir.model,name:pos_pricer.model_pos_config
msgid "Point of Sale Configuration"
msgstr "Configuración del TPV"

#. module: pos_pricer
#: model:ir.model.fields,help:pos_pricer.field_product_product__on_sale_price
msgid "Price after setting a Pricer Sales Pricelist"
msgstr "Precio después de configurar una lista de precios venta de Pricer"

#. module: pos_pricer
#: model:ir.ui.menu,name:pos_pricer.pos_menu_pricer_configuration
#: model_terms:ir.ui.view,arch_db:pos_pricer.product_product_form_view_pricers
msgid "Pricer"
msgstr "Pricer"

#. module: pos_pricer
#: model:ir.model.fields,field_description:pos_pricer.field_product_product__pricer_display_price
msgid "Pricer Display Price"
msgstr "Mostrar el precio de Pricer"

#. module: pos_pricer
#: model:ir.model.fields,field_description:pos_pricer.field_pricer_store__pricer_login
msgid "Pricer Login"
msgstr "Datos de inicio de sesión en Pricer"

#. module: pos_pricer
#: model:ir.model.fields,field_description:pos_pricer.field_pricer_store__pricer_password
msgid "Pricer Password"
msgstr "Contraseña de Pricer"

#. module: pos_pricer
#: model:ir.model.fields,field_description:pos_pricer.field_product_product__pricer_product_to_create_or_update
msgid "Pricer Product To Create Or Update"
msgstr "Producto de Pricer para crear o actualizar"

#. module: pos_pricer
#: model:ir.model.fields,field_description:pos_pricer.field_pricer_tag__pricer_product_to_link
msgid "Pricer Product To Link"
msgstr "Producto de Pricer a vincular"

#. module: pos_pricer
#: model:ir.model.fields,field_description:pos_pricer.field_product_product__pricer_sale_pricelist_id
msgid "Pricer Sales Pricelist"
msgstr "Lista de precios de venta de Pricer"

#. module: pos_pricer
#: model:ir.model.fields,field_description:pos_pricer.field_product_product__pricer_store_id
#: model_terms:ir.ui.view,arch_db:pos_pricer.pricer_pricer_store_form_view
#: model_terms:ir.ui.view,arch_db:pos_pricer.pricer_pricer_store_view_list
msgid "Pricer Store"
msgstr "Tienda de Pricer"

#. module: pos_pricer
#: model:ir.model.fields,field_description:pos_pricer.field_pricer_store__pricer_store_identifier
msgid "Pricer Store ID"
msgstr "ID de la tienda Pricer"

#. module: pos_pricer
#. odoo-python
#: code:addons/pos_pricer/models/pricer_store.py:0
msgid ""
"Pricer Store ID must only contain lowercase a-z, 0-9 or '-' and not start "
"with '-'"
msgstr ""
"El ID de la tienda solo puede contener letras en minúsculas de la a a la z, "
"números del 0-9 o '-', pero no puede empezar con '-'"

#. module: pos_pricer
#: model:ir.model.fields,help:pos_pricer.field_pricer_store__name
msgid "Pricer Store name in Odoo database"
msgstr "Nombre de la tienda Pricer en la base de datos de Odoo"

#. module: pos_pricer
#: model:ir.model,name:pos_pricer.model_pricer_store
msgid "Pricer Store regrouping pricer tags"
msgstr "Reagrupamiento de las etiquetas de Pricer en la tienda de Pricer"

#. module: pos_pricer
#: model:ir.actions.act_window,name:pos_pricer.action_open_pricer_stores
#: model:ir.ui.menu,name:pos_pricer.menu_pos_pricer_stores
msgid "Pricer Stores"
msgstr "Tiendas de Pricer"

#. module: pos_pricer
#: model:ir.model.fields,field_description:pos_pricer.field_pricer_tag__name
msgid "Pricer Tag Barcode ID"
msgstr "ID del código de barras de la etiqueta de Pricer"

#. module: pos_pricer
#: model:ir.actions.act_window,name:pos_pricer.action_open_pricer_tags
#: model:ir.model.fields,field_description:pos_pricer.field_pricer_store__pricer_tag_ids
#: model:ir.ui.menu,name:pos_pricer.menu_pos_pricer_tags
msgid "Pricer Tags"
msgstr "Etiquetas de Pricer"

#. module: pos_pricer
#: model:ir.model.fields,field_description:pos_pricer.field_pricer_store__pricer_tenant_name
msgid "Pricer Tenant Name"
msgstr "Nombre del propietario de Pricer"

#. module: pos_pricer
#: model:ir.model,name:pos_pricer.model_pricer_tag
msgid "Pricer electronic tag"
msgstr "Etiqueta electrónica de Pricer"

#. module: pos_pricer
#: model_terms:ir.ui.view,arch_db:pos_pricer.pricer_pricer_tag_view_list
msgid "Pricer tag"
msgstr "Etiqueta de Pricer"

#. module: pos_pricer
#: model:ir.model.fields,field_description:pos_pricer.field_pricer_store__dummy_tag_barcode
msgid "Pricer tag barcode"
msgstr "Código de barras de la etiqueta de Pricer"

#. module: pos_pricer
#: model:ir.model.fields,field_description:pos_pricer.field_product_product__pricer_tag_ids
msgid "Pricer tags ids"
msgstr "IDs de etiquetas de Pricer"

#. module: pos_pricer
#: model:ir.model,name:pos_pricer.model_product_template
msgid "Product"
msgstr "Producto"

#. module: pos_pricer
#. odoo-javascript
#: code:addons/pos_pricer/static/src/views/pricer_quick_pairing_form_view.js:0
msgid "Product '%s' found"
msgstr "Se encontró el producto \"%s\""

#. module: pos_pricer
#: model:ir.model,name:pos_pricer.model_product_product
msgid "Product Variant"
msgstr "Variante de producto"

#. module: pos_pricer
#: model:ir.model.fields,field_description:pos_pricer.field_pricer_store__dummy_prod_barcode
msgid "Product barcode"
msgstr "Código de barras del producto"

#. module: pos_pricer
#: model:ir.model.fields,field_description:pos_pricer.field_pricer_store__product_ids
msgid "Products"
msgstr "Productos"

#. module: pos_pricer
#: model_terms:ir.ui.view,arch_db:pos_pricer.pricer_pricer_store_form_view
msgid "Quick pairing"
msgstr "Emparejamiento rápido"

#. module: pos_pricer
#: model:ir.model.fields,help:pos_pricer.field_pricer_store__dummy_tag_barcode
msgid "Scan the Pricer tag barcode here"
msgstr "Escanee el código de barras de la etiqueta de Pricer aquí"

#. module: pos_pricer
#: model:ir.model.fields,help:pos_pricer.field_pricer_store__dummy_prod_barcode
msgid "Scan the product barcode here"
msgstr "Escanee el código de barras del producto aquí"

#. module: pos_pricer
#: model:ir.model.fields,help:pos_pricer.field_pricer_store__last_update_status_message
msgid "Status message of the last synchronization with Pricer"
msgstr "Mensaje del estado de la última sincronización con Pricer"

#. module: pos_pricer
#: model:ir.model,name:pos_pricer.model_stock_move
msgid "Stock Move"
msgstr "Movimiento de stock"

#. module: pos_pricer
#: model:ir.model.fields,field_description:pos_pricer.field_pricer_store__name
msgid "Store Name"
msgstr "Nombre de la tienda"

#. module: pos_pricer
#. odoo-javascript
#: code:addons/pos_pricer/static/src/views/pricer_quick_pairing_form_view.js:0
msgid "Tag '%s' found"
msgstr "Se encontró la etiqueta \"%s\""

#. module: pos_pricer
#. odoo-javascript
#: code:addons/pos_pricer/static/src/views/pricer_quick_pairing_form_view.js:0
msgid "Tag '%s' not found, creating it"
msgstr "No se encontró la etiqueta \"%s\", crearla"

#. module: pos_pricer
#. odoo-javascript
#: code:addons/pos_pricer/static/src/views/pricer_quick_pairing_form_view.js:0
msgid "Tag '%s' successfully linked with product '%s'"
msgstr "Etiqueta \"%s\" vinculada con éxito con el producto \"%s\""

#. module: pos_pricer
#. odoo-python
#: code:addons/pos_pricer/models/pricer_tag.py:0
msgid ""
"Tag id should be a 17 characters string composed of a letter followed by 16 "
"digits"
msgstr ""
"El ID de la etiqueta debe tener 17 caracteres, una letra seguida de 16 "
"dígitos."

#. module: pos_pricer
#: model:ir.model.fields,help:pos_pricer.field_product_product__pricer_sale_pricelist_id
msgid ""
"This pricelist will be used to set sales on Pricer tags for this product"
msgstr ""
"Esta lista de precios se usará para configurar las ventas en etiquetas de "
"Pricer para este producto"

#. module: pos_pricer
#: model:ir.model.fields,help:pos_pricer.field_pricer_tag__pricer_store_id
#: model:ir.model.fields,help:pos_pricer.field_product_product__pricer_store_id
msgid ""
"This product will be linked to and displayed on the Pricer tags of the store"
" selected here"
msgstr ""
"Este producto se vinculará y se mostrará en las etiquetas Pricer de la "
"tienda que se seleccione aquí"

#. module: pos_pricer
#: model:ir.model.fields,help:pos_pricer.field_product_product__pricer_tag_ids
msgid ""
"This product will be linked to and displayed on the Pricer tags with ids "
"listed here. It is recommended to use a barcode scanner"
msgstr ""
"Este producto se vinculará y se mostrará en las etiquetas Pricer con los IDs"
" enlistados aquí. Le recomendamos usar un lector de código de barras."

#. module: pos_pricer
#: model_terms:ir.ui.view,arch_db:pos_pricer.pricer_pricer_store_view_list
msgid "Update all tags"
msgstr "Actualizar todas las etiquetas"

#. module: pos_pricer
#. odoo-python
#: code:addons/pos_pricer/models/pricer_store.py:0
msgid "Update successfully sent to Pricer"
msgstr "Actualización enviada correctamente a Pricer"

#. module: pos_pricer
#: model_terms:ir.ui.view,arch_db:pos_pricer.pricer_pricer_store_view_list
msgid "Update tags"
msgstr "Actualizar las etiquetas"

#. module: pos_pricer
#: model:ir.model.fields,help:pos_pricer.field_pricer_store__pricer_tenant_name
msgid "Your company identifier at Pricer"
msgstr "El identificador de su empresa en Pricer"
