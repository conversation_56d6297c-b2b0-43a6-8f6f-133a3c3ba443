# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_attendance_gantt
# 
# Translators:
# Wil <PERSON>do<PERSON>, 2024
# <PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:27+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON>, 2025\n"
"Language-Team: Polish (https://app.transifex.com/odoo/teams/41243/pl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: pl\n"
"Plural-Forms: nplurals=4; plural=(n==1 ? 0 : (n%10>=2 && n%10<=4) && (n%100<12 || n%100>14) ? 1 : n!=1 && (n%10>=0 && n%10<=1) || (n%10>=5 && n%10<=9) || (n%100>=12 && n%100<=14) ? 2 : 3);\n"

#. module: hr_attendance_gantt
#: model:ir.model,name:hr_attendance_gantt.model_hr_attendance
msgid "Attendance"
msgstr "Obecność"

#. module: hr_attendance_gantt
#: model:ir.model.fields,field_description:hr_attendance_gantt.field_hr_attendance__color
msgid "Color"
msgstr "Kolor"

#. module: hr_attendance_gantt
#: model:ir.actions.act_window,name:hr_attendance_gantt.action_open_gantt_create_view_form
msgid "Create"
msgstr "Utwórz"

#. module: hr_attendance_gantt
#: model_terms:ir.ui.view,arch_db:hr_attendance_gantt.hr_attendance_gantt_view
msgid "Days"
msgstr "Dni"

#. module: hr_attendance_gantt
#: model_terms:ir.ui.view,arch_db:hr_attendance_gantt.hr_attendance_gantt_create_view_form
msgid "Delete"
msgstr "Usuń"

#. module: hr_attendance_gantt
#: model_terms:ir.ui.view,arch_db:hr_attendance_gantt.hr_attendance_gantt_create_view_form
msgid "Details"
msgstr "Szczegóły"

#. module: hr_attendance_gantt
#: model_terms:ir.ui.view,arch_db:hr_attendance_gantt.hr_attendance_gantt_create_view_form
msgid "Discard"
msgstr "Odrzuć"

#. module: hr_attendance_gantt
#: model:ir.model.fields,field_description:hr_attendance_gantt.field_hr_attendance__overtime_progress
msgid "Overtime Progress"
msgstr "Postęp w nadgodzinach"

#. module: hr_attendance_gantt
#: model_terms:ir.ui.view,arch_db:hr_attendance_gantt.hr_attendance_gantt_create_view_form
msgid "Save & Close"
msgstr "Zapisz i zamknij"
