# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_attendance_gantt
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON>r, 2024
# <AUTHOR> <EMAIL>, 2024
# <PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:27+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON>, 2024\n"
"Language-Team: Hebrew (https://app.transifex.com/odoo/teams/41243/he/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: he\n"
"Plural-Forms: nplurals=3; plural=(n == 1 && n % 1 == 0) ? 0 : (n == 2 && n % 1 == 0) ? 1: 2;\n"

#. module: hr_attendance_gantt
#: model:ir.model,name:hr_attendance_gantt.model_hr_attendance
msgid "Attendance"
msgstr "נוכחות"

#. module: hr_attendance_gantt
#: model:ir.model.fields,field_description:hr_attendance_gantt.field_hr_attendance__color
msgid "Color"
msgstr "צבע"

#. module: hr_attendance_gantt
#: model:ir.actions.act_window,name:hr_attendance_gantt.action_open_gantt_create_view_form
msgid "Create"
msgstr "יצירה"

#. module: hr_attendance_gantt
#: model_terms:ir.ui.view,arch_db:hr_attendance_gantt.hr_attendance_gantt_view
msgid "Days"
msgstr "ימים"

#. module: hr_attendance_gantt
#: model_terms:ir.ui.view,arch_db:hr_attendance_gantt.hr_attendance_gantt_create_view_form
msgid "Delete"
msgstr "מחיקה"

#. module: hr_attendance_gantt
#: model_terms:ir.ui.view,arch_db:hr_attendance_gantt.hr_attendance_gantt_create_view_form
msgid "Details"
msgstr "פרטים"

#. module: hr_attendance_gantt
#: model_terms:ir.ui.view,arch_db:hr_attendance_gantt.hr_attendance_gantt_create_view_form
msgid "Discard"
msgstr "בטל"

#. module: hr_attendance_gantt
#: model:ir.model.fields,field_description:hr_attendance_gantt.field_hr_attendance__overtime_progress
msgid "Overtime Progress"
msgstr ""

#. module: hr_attendance_gantt
#: model_terms:ir.ui.view,arch_db:hr_attendance_gantt.hr_attendance_gantt_create_view_form
msgid "Save & Close"
msgstr "שמור וסגור"
