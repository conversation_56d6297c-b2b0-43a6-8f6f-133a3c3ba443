# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* project_helpdesk
# 
# Translators:
# <AUTHOR> <EMAIL>, 2024
# <AUTHOR> <EMAIL>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:28+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON>, 2024\n"
"Language-Team: Lithuanian (https://app.transifex.com/odoo/teams/41243/lt/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: lt\n"
"Plural-Forms: nplurals=4; plural=(n % 10 == 1 && (n % 100 > 19 || n % 100 < 11) ? 0 : (n % 10 >= 2 && n % 10 <=9) && (n % 100 > 19 || n % 100 < 11) ? 1 : n % 1 != 0 ? 2: 3);\n"

#. module: project_helpdesk
#: model_terms:ir.ui.view,arch_db:project_helpdesk.helpdesk_ticket_convert_to_task_wizard_view_form
#: model_terms:ir.ui.view,arch_db:project_helpdesk.project_task_convert_to_ticket_wizard_view_form
msgid "Convert"
msgstr ""

#. module: project_helpdesk
#: model:ir.model,name:project_helpdesk.model_helpdesk_ticket_convert_wizard
msgid "Convert Helpdesk Tickets to Tasks"
msgstr ""

#. module: project_helpdesk
#: model:ir.model,name:project_helpdesk.model_project_task_convert_wizard
msgid "Convert Project Tasks to Tickets"
msgstr ""

#. module: project_helpdesk
#. odoo-python
#: code:addons/project_helpdesk/models/helpdesk.py:0
#: model:ir.actions.server,name:project_helpdesk.action_ticket_convert_to_task
#: model_terms:ir.ui.view,arch_db:project_helpdesk.helpdesk_ticket_convert_to_task_wizard_view_form
msgid "Convert to Task"
msgstr ""

#. module: project_helpdesk
#. odoo-python
#: code:addons/project_helpdesk/models/project.py:0
#: model:ir.actions.server,name:project_helpdesk.action_task_convert_to_ticket
#: model_terms:ir.ui.view,arch_db:project_helpdesk.project_task_convert_to_ticket_wizard_view_form
msgid "Convert to Ticket"
msgstr ""

#. module: project_helpdesk
#. odoo-python
#: code:addons/project_helpdesk/wizard/helpdesk_ticket_convert_wizard.py:0
msgid "Converted Tasks"
msgstr ""

#. module: project_helpdesk
#. odoo-python
#: code:addons/project_helpdesk/wizard/project_task_convert_wizard.py:0
msgid "Converted Tickets"
msgstr ""

#. module: project_helpdesk
#: model:ir.model.fields,field_description:project_helpdesk.field_helpdesk_ticket_convert_wizard__create_uid
#: model:ir.model.fields,field_description:project_helpdesk.field_project_task_convert_wizard__create_uid
msgid "Created by"
msgstr "Sukūrė"

#. module: project_helpdesk
#: model:ir.model.fields,field_description:project_helpdesk.field_helpdesk_ticket_convert_wizard__create_date
#: model:ir.model.fields,field_description:project_helpdesk.field_project_task_convert_wizard__create_date
msgid "Created on"
msgstr "Sukurta"

#. module: project_helpdesk
#: model_terms:ir.ui.view,arch_db:project_helpdesk.helpdesk_ticket_convert_to_task_wizard_view_form
#: model_terms:ir.ui.view,arch_db:project_helpdesk.project_task_convert_to_ticket_wizard_view_form
msgid "Discard"
msgstr "Atmesti"

#. module: project_helpdesk
#: model:ir.model.fields,field_description:project_helpdesk.field_helpdesk_ticket_convert_wizard__display_name
#: model:ir.model.fields,field_description:project_helpdesk.field_project_task_convert_wizard__display_name
msgid "Display Name"
msgstr "Rodomas pavadinimas"

#. module: project_helpdesk
#: model:ir.model,name:project_helpdesk.model_helpdesk_ticket
msgid "Helpdesk Ticket"
msgstr "Pagalbos tarnybos kreipiniai"

#. module: project_helpdesk
#: model:ir.model.fields,field_description:project_helpdesk.field_helpdesk_ticket_convert_wizard__id
#: model:ir.model.fields,field_description:project_helpdesk.field_project_task_convert_wizard__id
msgid "ID"
msgstr "ID"

#. module: project_helpdesk
#: model:ir.model.fields,field_description:project_helpdesk.field_helpdesk_ticket_convert_wizard__write_uid
#: model:ir.model.fields,field_description:project_helpdesk.field_project_task_convert_wizard__write_uid
msgid "Last Updated by"
msgstr "Paskutinį kartą atnaujino"

#. module: project_helpdesk
#: model:ir.model.fields,field_description:project_helpdesk.field_helpdesk_ticket_convert_wizard__write_date
#: model:ir.model.fields,field_description:project_helpdesk.field_project_task_convert_wizard__write_date
msgid "Last Updated on"
msgstr "Paskutinį kartą atnaujinta"

#. module: project_helpdesk
#: model:ir.model.fields,field_description:project_helpdesk.field_helpdesk_ticket_convert_wizard__project_id
msgid "Project"
msgstr "Projektas"

#. module: project_helpdesk
#. odoo-python
#: code:addons/project_helpdesk/models/project.py:0
msgid "Recurring tasks cannot be converted into tickets."
msgstr ""

#. module: project_helpdesk
#: model:ir.model.fields,field_description:project_helpdesk.field_helpdesk_ticket_convert_wizard__stage_id
#: model:ir.model.fields,field_description:project_helpdesk.field_project_task_convert_wizard__stage_id
msgid "Stage"
msgstr "Etapas"

#. module: project_helpdesk
#: model:ir.model,name:project_helpdesk.model_project_task
msgid "Task"
msgstr "Užduotis"

#. module: project_helpdesk
#. odoo-python
#: code:addons/project_helpdesk/wizard/project_task_convert_wizard.py:0
msgid "Task converted into ticket %s"
msgstr ""

#. module: project_helpdesk
#: model:ir.model.fields,field_description:project_helpdesk.field_project_task_convert_wizard__team_id
msgid "Team"
msgstr "Komanda"

#. module: project_helpdesk
#. odoo-python
#: code:addons/project_helpdesk/wizard/helpdesk_ticket_convert_wizard.py:0
msgid "Ticket converted into task %s"
msgstr ""
