# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* project_helpdesk
# 
# Translators:
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:28+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Polish (https://app.transifex.com/odoo/teams/41243/pl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: pl\n"
"Plural-Forms: nplurals=4; plural=(n==1 ? 0 : (n%10>=2 && n%10<=4) && (n%100<12 || n%100>14) ? 1 : n!=1 && (n%10>=0 && n%10<=1) || (n%10>=5 && n%10<=9) || (n%100>=12 && n%100<=14) ? 2 : 3);\n"

#. module: project_helpdesk
#: model_terms:ir.ui.view,arch_db:project_helpdesk.helpdesk_ticket_convert_to_task_wizard_view_form
#: model_terms:ir.ui.view,arch_db:project_helpdesk.project_task_convert_to_ticket_wizard_view_form
msgid "Convert"
msgstr "Przekształć"

#. module: project_helpdesk
#: model:ir.model,name:project_helpdesk.model_helpdesk_ticket_convert_wizard
msgid "Convert Helpdesk Tickets to Tasks"
msgstr "Konwertuj zgłoszenia helpdesku do zadań"

#. module: project_helpdesk
#: model:ir.model,name:project_helpdesk.model_project_task_convert_wizard
msgid "Convert Project Tasks to Tickets"
msgstr "Przekształć zadania projektu na zgłoszenia"

#. module: project_helpdesk
#. odoo-python
#: code:addons/project_helpdesk/models/helpdesk.py:0
#: model:ir.actions.server,name:project_helpdesk.action_ticket_convert_to_task
#: model_terms:ir.ui.view,arch_db:project_helpdesk.helpdesk_ticket_convert_to_task_wizard_view_form
msgid "Convert to Task"
msgstr "Przekształć na zadanie"

#. module: project_helpdesk
#. odoo-python
#: code:addons/project_helpdesk/models/project.py:0
#: model:ir.actions.server,name:project_helpdesk.action_task_convert_to_ticket
#: model_terms:ir.ui.view,arch_db:project_helpdesk.project_task_convert_to_ticket_wizard_view_form
msgid "Convert to Ticket"
msgstr "Przekształć na zgłoszenie"

#. module: project_helpdesk
#. odoo-python
#: code:addons/project_helpdesk/wizard/helpdesk_ticket_convert_wizard.py:0
msgid "Converted Tasks"
msgstr "Przekształcone zadania"

#. module: project_helpdesk
#. odoo-python
#: code:addons/project_helpdesk/wizard/project_task_convert_wizard.py:0
msgid "Converted Tickets"
msgstr "Przekształcone zgłoszenia"

#. module: project_helpdesk
#: model:ir.model.fields,field_description:project_helpdesk.field_helpdesk_ticket_convert_wizard__create_uid
#: model:ir.model.fields,field_description:project_helpdesk.field_project_task_convert_wizard__create_uid
msgid "Created by"
msgstr "Utworzył(a)"

#. module: project_helpdesk
#: model:ir.model.fields,field_description:project_helpdesk.field_helpdesk_ticket_convert_wizard__create_date
#: model:ir.model.fields,field_description:project_helpdesk.field_project_task_convert_wizard__create_date
msgid "Created on"
msgstr "Data utworzenia"

#. module: project_helpdesk
#: model_terms:ir.ui.view,arch_db:project_helpdesk.helpdesk_ticket_convert_to_task_wizard_view_form
#: model_terms:ir.ui.view,arch_db:project_helpdesk.project_task_convert_to_ticket_wizard_view_form
msgid "Discard"
msgstr "Odrzuć"

#. module: project_helpdesk
#: model:ir.model.fields,field_description:project_helpdesk.field_helpdesk_ticket_convert_wizard__display_name
#: model:ir.model.fields,field_description:project_helpdesk.field_project_task_convert_wizard__display_name
msgid "Display Name"
msgstr "Nazwa wyświetlana"

#. module: project_helpdesk
#: model:ir.model,name:project_helpdesk.model_helpdesk_ticket
msgid "Helpdesk Ticket"
msgstr "Zgłoszenia Punktu Pomocy"

#. module: project_helpdesk
#: model:ir.model.fields,field_description:project_helpdesk.field_helpdesk_ticket_convert_wizard__id
#: model:ir.model.fields,field_description:project_helpdesk.field_project_task_convert_wizard__id
msgid "ID"
msgstr "ID"

#. module: project_helpdesk
#: model:ir.model.fields,field_description:project_helpdesk.field_helpdesk_ticket_convert_wizard__write_uid
#: model:ir.model.fields,field_description:project_helpdesk.field_project_task_convert_wizard__write_uid
msgid "Last Updated by"
msgstr "Ostatnio aktualizowane przez"

#. module: project_helpdesk
#: model:ir.model.fields,field_description:project_helpdesk.field_helpdesk_ticket_convert_wizard__write_date
#: model:ir.model.fields,field_description:project_helpdesk.field_project_task_convert_wizard__write_date
msgid "Last Updated on"
msgstr "Data ostatniej aktualizacji"

#. module: project_helpdesk
#: model:ir.model.fields,field_description:project_helpdesk.field_helpdesk_ticket_convert_wizard__project_id
msgid "Project"
msgstr "Projekt"

#. module: project_helpdesk
#. odoo-python
#: code:addons/project_helpdesk/models/project.py:0
msgid "Recurring tasks cannot be converted into tickets."
msgstr "Zadania powtarzające się nie mogą być przekształcane w zgłoszenia."

#. module: project_helpdesk
#: model:ir.model.fields,field_description:project_helpdesk.field_helpdesk_ticket_convert_wizard__stage_id
#: model:ir.model.fields,field_description:project_helpdesk.field_project_task_convert_wizard__stage_id
msgid "Stage"
msgstr "Etap"

#. module: project_helpdesk
#: model:ir.model,name:project_helpdesk.model_project_task
msgid "Task"
msgstr "Zadanie"

#. module: project_helpdesk
#. odoo-python
#: code:addons/project_helpdesk/wizard/project_task_convert_wizard.py:0
msgid "Task converted into ticket %s"
msgstr "Zadanie zamienione na zgłoszenie %s"

#. module: project_helpdesk
#: model:ir.model.fields,field_description:project_helpdesk.field_project_task_convert_wizard__team_id
msgid "Team"
msgstr "Zespół"

#. module: project_helpdesk
#. odoo-python
#: code:addons/project_helpdesk/wizard/helpdesk_ticket_convert_wizard.py:0
msgid "Ticket converted into task %s"
msgstr "Zgłoszenie zamienione na zadanie %s"
