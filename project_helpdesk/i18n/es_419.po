# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* project_helpdesk
# 
# Translators:
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:28+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Spanish (Latin America) (https://app.transifex.com/odoo/teams/41243/es_419/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es_419\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: project_helpdesk
#: model_terms:ir.ui.view,arch_db:project_helpdesk.helpdesk_ticket_convert_to_task_wizard_view_form
#: model_terms:ir.ui.view,arch_db:project_helpdesk.project_task_convert_to_ticket_wizard_view_form
msgid "Convert"
msgstr "Convertir"

#. module: project_helpdesk
#: model:ir.model,name:project_helpdesk.model_helpdesk_ticket_convert_wizard
msgid "Convert Helpdesk Tickets to Tasks"
msgstr "Convertir tickets de soporte al cliente en tareas"

#. module: project_helpdesk
#: model:ir.model,name:project_helpdesk.model_project_task_convert_wizard
msgid "Convert Project Tasks to Tickets"
msgstr "Convertir las tareas del proyecto en tickets"

#. module: project_helpdesk
#. odoo-python
#: code:addons/project_helpdesk/models/helpdesk.py:0
#: model:ir.actions.server,name:project_helpdesk.action_ticket_convert_to_task
#: model_terms:ir.ui.view,arch_db:project_helpdesk.helpdesk_ticket_convert_to_task_wizard_view_form
msgid "Convert to Task"
msgstr "Convertir a tarea"

#. module: project_helpdesk
#. odoo-python
#: code:addons/project_helpdesk/models/project.py:0
#: model:ir.actions.server,name:project_helpdesk.action_task_convert_to_ticket
#: model_terms:ir.ui.view,arch_db:project_helpdesk.project_task_convert_to_ticket_wizard_view_form
msgid "Convert to Ticket"
msgstr "Convertir a ticket"

#. module: project_helpdesk
#. odoo-python
#: code:addons/project_helpdesk/wizard/helpdesk_ticket_convert_wizard.py:0
msgid "Converted Tasks"
msgstr "Tareas convertidas"

#. module: project_helpdesk
#. odoo-python
#: code:addons/project_helpdesk/wizard/project_task_convert_wizard.py:0
msgid "Converted Tickets"
msgstr "Tickets convertidos"

#. module: project_helpdesk
#: model:ir.model.fields,field_description:project_helpdesk.field_helpdesk_ticket_convert_wizard__create_uid
#: model:ir.model.fields,field_description:project_helpdesk.field_project_task_convert_wizard__create_uid
msgid "Created by"
msgstr "Creado por"

#. module: project_helpdesk
#: model:ir.model.fields,field_description:project_helpdesk.field_helpdesk_ticket_convert_wizard__create_date
#: model:ir.model.fields,field_description:project_helpdesk.field_project_task_convert_wizard__create_date
msgid "Created on"
msgstr "Creado el"

#. module: project_helpdesk
#: model_terms:ir.ui.view,arch_db:project_helpdesk.helpdesk_ticket_convert_to_task_wizard_view_form
#: model_terms:ir.ui.view,arch_db:project_helpdesk.project_task_convert_to_ticket_wizard_view_form
msgid "Discard"
msgstr "Descartar"

#. module: project_helpdesk
#: model:ir.model.fields,field_description:project_helpdesk.field_helpdesk_ticket_convert_wizard__display_name
#: model:ir.model.fields,field_description:project_helpdesk.field_project_task_convert_wizard__display_name
msgid "Display Name"
msgstr "Nombre en pantalla"

#. module: project_helpdesk
#: model:ir.model,name:project_helpdesk.model_helpdesk_ticket
msgid "Helpdesk Ticket"
msgstr "Ticket de soporte al cliente"

#. module: project_helpdesk
#: model:ir.model.fields,field_description:project_helpdesk.field_helpdesk_ticket_convert_wizard__id
#: model:ir.model.fields,field_description:project_helpdesk.field_project_task_convert_wizard__id
msgid "ID"
msgstr "ID"

#. module: project_helpdesk
#: model:ir.model.fields,field_description:project_helpdesk.field_helpdesk_ticket_convert_wizard__write_uid
#: model:ir.model.fields,field_description:project_helpdesk.field_project_task_convert_wizard__write_uid
msgid "Last Updated by"
msgstr "Última actualización por"

#. module: project_helpdesk
#: model:ir.model.fields,field_description:project_helpdesk.field_helpdesk_ticket_convert_wizard__write_date
#: model:ir.model.fields,field_description:project_helpdesk.field_project_task_convert_wizard__write_date
msgid "Last Updated on"
msgstr "Última actualización el"

#. module: project_helpdesk
#: model:ir.model.fields,field_description:project_helpdesk.field_helpdesk_ticket_convert_wizard__project_id
msgid "Project"
msgstr "Proyecto"

#. module: project_helpdesk
#. odoo-python
#: code:addons/project_helpdesk/models/project.py:0
msgid "Recurring tasks cannot be converted into tickets."
msgstr "Las tareas recurrentes no se pueden convertir a tickets."

#. module: project_helpdesk
#: model:ir.model.fields,field_description:project_helpdesk.field_helpdesk_ticket_convert_wizard__stage_id
#: model:ir.model.fields,field_description:project_helpdesk.field_project_task_convert_wizard__stage_id
msgid "Stage"
msgstr "Etapa"

#. module: project_helpdesk
#: model:ir.model,name:project_helpdesk.model_project_task
msgid "Task"
msgstr "Tarea"

#. module: project_helpdesk
#. odoo-python
#: code:addons/project_helpdesk/wizard/project_task_convert_wizard.py:0
msgid "Task converted into ticket %s"
msgstr "La tarea se convirtió en el ticket %s"

#. module: project_helpdesk
#: model:ir.model.fields,field_description:project_helpdesk.field_project_task_convert_wizard__team_id
msgid "Team"
msgstr "Equipo"

#. module: project_helpdesk
#. odoo-python
#: code:addons/project_helpdesk/wizard/helpdesk_ticket_convert_wizard.py:0
msgid "Ticket converted into task %s"
msgstr "El ticket se convirtió en la tarea %s"
