"id","code_type","sequence","code","name","description","tax_rate"
"code_17_AM","Packing Unit","1","AM","Ampoule","Ampoule","0.0"
"code_17_BA","Packing Unit","2","BA","Barrel","Barrel","0.0"
"code_17_BC","Packing Unit","3","BC","Bottlecrate","Bottlecrate","0.0"
"code_17_BE","Packing Unit","4","BE","Bundle","Bundle","0.0"
"code_17_BF","Packing Unit","5","BF","Balloon, non-protected","Balloon, non-protected","0.0"
"code_17_BG","Packing Unit","6","BG","Bag","Bag","0.0"
"code_17_BJ","Packing Unit","7","BJ","Bucket","Bucket","0.0"
"code_17_BK","Packing Unit","8","BK","Basket","Basket","0.0"
"code_17_BL","Packing Unit","9","BL","Bale","Bale","0.0"
"code_17_BQ","Packing Unit","10","BQ","Bottle, protected cylindrical","Bottle, protected cylindrical","0.0"
"code_17_BR","Packing Unit","11","BR","Bar","Bar","0.0"
"code_17_BV","Packing Unit","12","BV","Bottle, bulbous","Bottle, bulbous","0.0"
"code_17_BZ","Packing Unit","13","BZ","Bag","Bag","0.0"
"code_17_CA","Packing Unit","14","CA","Can","Can","0.0"
"code_17_CH","Packing Unit","15","CH","Chest","Chest","0.0"
"code_17_CJ","Packing Unit","16","CJ","Coffin","Coffin","0.0"
"code_17_CL","Packing Unit","17","CL","Coil","Coil","0.0"
"code_17_CR","Packing Unit","18","CR","Wooden Box, Wooden Case","Wooden Box, Wooden Case","0.0"
"code_17_CS","Packing Unit","19","CS","Cassette","Cassette","0.0"
"code_17_CT","Packing Unit","20","CT","Carton","Carton","0.0"
"code_17_CTN","Packing Unit","21","CTN","Container","Container","0.0"
"code_17_CY","Packing Unit","22","CY","Cylinder","Cylinder","0.0"
"code_17_DR","Packing Unit","23","DR","Drum","Drum","0.0"
"code_17_GT","Packing Unit","24","GT","Extra Countable Item","Extra Countable Item","0.0"
"code_17_HH","Packing Unit","25","HH","Hand Baggage","Hand Baggage","0.0"
"code_17_IZ","Packing Unit","26","IZ","Ingots","Ingots","0.0"
"code_17_JR","Packing Unit","27","JR","Jar","Jar","0.0"
"code_17_JU","Packing Unit","28","JU","Jug","Jug","0.0"
"code_17_JY","Packing Unit","29","JY","Jerry CAN Cylindrical","Jerry CAN Cylindrical","0.0"
"code_17_KZ","Packing Unit","30","KZ","Canester","Canester","0.0"
"code_17_LZ","Packing Unit","31","LZ","Logs, in bundle/bunch/truss","Logs, in bundle/bunch/truss","0.0"
"code_17_NT","Packing Unit","32","NT","Net","Net","0.0"
"code_17_OU","Packing Unit","33","OU","Non-Exterior Packaging Unit","Non-Exterior Packaging Unit","0.0"
"code_17_PD","Packing Unit","34","PD","Poddon","Poddon","0.0"
"code_17_PG","Packing Unit","35","PG","Plate","Plate","0.0"
"code_17_PI","Packing Unit","36","PI","Pipe","Pipe","0.0"
"code_17_PO","Packing Unit","37","PO","Pilot","Pilot","0.0"
"code_17_PU","Packing Unit","38","PU","Traypack","Traypack","0.0"
"code_17_RL","Packing Unit","39","RL","Reel","Reel","0.0"
"code_17_RO","Packing Unit","40","RO","Roll","Roll","0.0"
"code_17_RZ","Packing Unit","41","RZ","Rods, in bundle/bunch/truss","Rods, in bundle/bunch/truss","0.0"
"code_17_SK","Packing Unit","42","SK","Skeletoncase","Skeletoncase","0.0"
"code_17_TY","Packing Unit","43","TY","Tank, cylindrical","Tank, cylindrical","0.0"
"code_17_VG","Packing Unit","44","VG","Bulk,gas(at 1031 mbar 15 oC)","Bulk,gas(at 1031 mbar 15 oC)","0.0"
"code_17_VL","Packing Unit","45","VL","Bulk,liquid(at normal temperature/pressure)","Bulk,liquid(at normal temperature/pressure)","0.0"
"code_17_VO","Packing Unit","46","VO","Bulk, solid, large particles(nodules)","Bulk, solid, large particles(""nodules"")","0.0"
"code_17_VQ","Packing Unit","47","VQ","Bulk, gas (liquefied at abnormal temperature/pressure)","Bulk, gas(liquefied at abnormal temperature/pressure)","0.0"
"code_17_VR","Packing Unit","48","VR","Bulk, solid, granular particles(grains)","Bulk, solid, granular particles(""grains"")","0.0"
"code_17_VT","Packing Unit","49","VT","Extra Bulk Item","Extra Bulk Item","0.0"
"code_17_VY","Packing Unit","50","VY","Bulk, fine particles(powder)","Bulk, fine particles(""powder"")","0.0"
"code_17_ML","Packing Unit","51","ML","Mills","cigarette Mills","0.0"
"code_17_TN","Packing Unit","52","TN","TAN","1TAN REFER TO 20BAGS","0.0"
"code_10_4B","Quantity Unit","1","4B","Pair","Pair","0.0"
"code_10_AV","Quantity Unit","2","AV","Cap","Cap","0.0"
"code_10_BA","Quantity Unit","3","BA","Barrel","Barrel","0.0"
"code_10_BE","Quantity Unit","4","BE","bundle","bundle","0.0"
"code_10_BG","Quantity Unit","5","BG","bag","bag","0.0"
"code_10_BL","Quantity Unit","6","BL","block","block","0.0"
"code_10_BLL","Quantity Unit","7","BLL","BLL Barrel (petroleum) (158,987 dm3)","BLL Barrel (petroleum) (158,987 dm3)","0.0"
"code_10_BX","Quantity Unit","8","BX","box","box","0.0"
"code_10_CA","Quantity Unit","9","CA","Can","Can","0.0"
"code_10_CEL","Quantity Unit","10","CEL","Cell","Cell","0.0"
"code_10_CMT","Quantity Unit","11","CMT","centimetre","centimetre","0.0"
"code_10_CR","Quantity Unit","12","CR","CARAT","CARAT","0.0"
"code_10_DR","Quantity Unit","13","DR","Drum","Drum","0.0"
"code_10_DZ","Quantity Unit","14","DZ","Dozen","Dozen","0.0"
"code_10_GLL","Quantity Unit","15","GLL","Gallon","Gallon","0.0"
"code_10_GRM","Quantity Unit","16","GRM","Gram","Gram","0.0"
"code_10_GRO","Quantity Unit","17","GRO","Gross","Gross","0.0"
"code_10_KG","Quantity Unit","18","KG","Kilo-Gramme","Kilo-Gramme","0.0"
"code_10_KTM","Quantity Unit","19","KTM","kilometre","kilometre","0.0"
"code_10_KWT","Quantity Unit","20","KWT","kilowatt","kilowatt","0.0"
"code_10_L","Quantity Unit","21","L","Litre","Litre","0.0"
"code_10_LBR","Quantity Unit","22","LBR","pound","pound","0.0"
"code_10_LK","Quantity Unit","23","LK","link","link","0.0"
"code_10_LTR","Quantity Unit","24","LTR","Litre","Litre","0.0"
"code_10_M","Quantity Unit","25","M","Metre","Metre","0.0"
"code_10_M2","Quantity Unit","26","M2","Square Metre","Square Metre","0.0"
"code_10_M3","Quantity Unit","27","M3","Cubic Metre","Cubic Metre","0.0"
"code_10_MGM","Quantity Unit","28","MGM","milligram","milligram","0.0"
"code_10_MTR","Quantity Unit","29","MTR","metre","metre","0.0"
"code_10_MWT","Quantity Unit","30","MWT","megawatt hour (1000 kW.h)","megawatt hour (1000 kW.h)","0.0"
"code_10_NO","Quantity Unit","31","NO","Number","Number","0.0"
"code_10_NX","Quantity Unit","32","NX","part per thousand","part per thousand","0.0"
"code_10_PA","Quantity Unit","33","PA","packet","packet","0.0"
"code_10_PG","Quantity Unit","34","PG","plate","plate","0.0"
"code_10_PR","Quantity Unit","35","PR","pair","pair","0.0"
"code_10_RL","Quantity Unit","36","RL","reel","reel","0.0"
"code_10_RO","Quantity Unit","37","RO","roll","roll","0.0"
"code_10_SET","Quantity Unit","38","SET","set","set","0.0"
"code_10_ST","Quantity Unit","39","ST","sheet","sheet","0.0"
"code_10_TNE","Quantity Unit","40","TNE","tonne (metric ton)","tonne (metric ton)","0.0"
"code_10_TU","Quantity Unit","41","TU","tube","tube","0.0"
"code_10_U","Quantity Unit","42","U","Pieces/item [Number]","Pieces/item [Number]","0.0"
"code_10_YRD","Quantity Unit","43","YRD","yard","yard","0.0"
"code_04_A","Taxation Type","1","A","A-Exempt","A-Exempt","0.0"
"code_04_B","Taxation Type","2","B","B-Standard Rated","B-Standard Rated","16.0"
"code_04_C","Taxation Type","3","C","C-Zero Rated","C-Zero Rated","0.0"
"code_04_D","Taxation Type","5","D","D-Non VAT","D-Non VAT","0.0"
"code_04_E","Taxation Type","4","E","E-VAT 8%","E-VAT 8%","8.0"
"code_07_01","Payment Type","1","01","CASH","CASH","0.0"
"code_07_02","Payment Type","2","02","CREDIT","CREDIT","0.0"
"code_07_03","Payment Type","3","03","CASH/CREDIT","CASH/CREDIT","0.0"
"code_07_04","Payment Type","4","04","BANK CHECK","BANK CHECK PAYMENT","0.0"
"code_07_05","Payment Type","5","05","DEBIT&CREDIT CARD","PAYMENT USING CARD","0.0"
"code_07_06","Payment Type","6","06","MOBILE MONEY","ANY TRANSACTION USING MOBILE MONEY SYSTEM","0.0"
"code_07_07","Payment Type","7","07","OTHER","OTHER MEANS OF PAYMENT","0.0"
"code_09_01","Branch Status","1","01","Open",,"0.0"
"code_09_02","Branch Status","2","02","Closed",,"0.0"
"code_11_01","Sale Status","1","01","Wait for Approval",,"0.0"
"code_11_02","Sale Status","2","02","Approved",,"0.0"
"code_11_03","Sale Status","3","03","Cancel Requested",,"0.0"
"code_11_04","Sale Status","4","04","Canceled",,"0.0"
"code_11_05","Sale Status","5","05","Refunded",,"0.0"
"code_11_06","Sale Status","6","06","Transferred",,"0.0"
"code_12_01","Stock I/O Type","1","01","Import",,"0.0"
"code_12_02","Stock I/O Type","2","02","Purchase",,"0.0"
"code_12_03","Stock I/O Type","3","03","Return",,"0.0"
"code_12_04","Stock I/O Type","4","04","Stock Movement",,"0.0"
"code_12_05","Stock I/O Type","5","05","Processing",,"0.0"
"code_12_06","Stock I/O Type","6","06","Adjustment",,"0.0"
"code_12_11","Stock I/O Type","11","11","Sale",,"0.0"
"code_12_12","Stock I/O Type","12","12","Return",,"0.0"
"code_12_13","Stock I/O Type","13","13","Stock Movement",,"0.0"
"code_12_14","Stock I/O Type","14","14","Processing",,"0.0"
"code_12_15","Stock I/O Type","15","15","Discarding",,"0.0"
"code_12_16","Stock I/O Type","16","16","Adjustment",,"0.0"
"code_14_C","Transaction Type","1","C","Copy","Copy","0.0"
"code_14_N","Transaction Type","2","N","Normal","Normal","0.0"
"code_14_P","Transaction Type","3","P","Proforma","Proforma","0.0"
"code_14_T","Transaction Type","4","T","Training","Training","0.0"
"code_24_1","Item Type","1","1","Raw Material","Raw Material","0.0"
"code_24_2","Item Type","2","2","Finished Product","Finished Product","0.0"
"code_24_3","Item Type","3","3","Service","Service without stock","0.0"
"code_26_1","Import Item Status","1","1","Unsent",,"0.0"
"code_26_2","Import Item Status","2","2","Waiting",,"0.0"
"code_26_3","Import Item Status","3","3","Approved",,"0.0"
"code_26_4","Import Item Status","4","4","Cancelled",,"0.0"
"code_32_01","Refund Reason","1","01","Missing Quantity",,"0.0"
"code_32_02","Refund Reason","2","02","Missing Waiting",,"0.0"
"code_32_03","Refund Reason","3","03","Damaged",,"0.0"
"code_32_04","Refund Reason","4","04","Wasted",,"0.0"
"code_32_05","Refund Reason","5","05","Raw Material Shortage",,"0.0"
"code_32_06","Refund Reason","6","06","Refund",,"0.0"
"code_32_07","Refund Reason","7","07","Wrong Customer PIN",,"0.0"
"code_32_08","Refund Reason","8","08","Wrong Customer name",,"0.0"
"code_32_09","Refund Reason","9","09","Wrong Amount/price",,"0.0"
"code_32_10","Refund Reason","10","10","Wrong Quantity",,"0.0"
"code_32_11","Refund Reason","11","11","Wrong Item(s)",,"0.0"
"code_32_12","Refund Reason","12","12","Wrong tax type",,"0.0"
"code_32_13","Refund Reason","13","13","Other reason",,"0.0"
"code_34_01","Purchase Status","1","01","Wait for Approval",,"0.0"
"code_34_02","Purchase Status","2","02","Approved",,"0.0"
"code_34_03","Purchase Status","3","03","Cancel Requested",,"0.0"
"code_34_04","Purchase Status","4","04","Canceled",,"0.0"
"code_34_05","Purchase Status","5","05","Refunded",,"0.0"
"code_34_06","Purchase Status","6","06","Transferred",,"0.0"
"code_35_01","Reason of Inventory Adjustment","1","01","Cargo Transit Out",,"0.0"
"code_35_02","Reason of Inventory Adjustment","2","02","Cargo Transit In",,"0.0"
"code_35_03","Reason of Inventory Adjustment","3","03","Adjustment In",,"0.0"
"code_35_04","Reason of Inventory Adjustment","4","04","Adjustment Out",,"0.0"
"code_35_05","Reason of Inventory Adjustment","5","05","Discarding",,"0.0"
"code_35_06","Reason of Inventory Adjustment","6","06","Processing Raw",,"0.0"
"code_35_07","Reason of Inventory Adjustment","7","07","Processing Result",,"0.0"
"code_36_1","Bank","1","1","MAYFAIR BANK LIMITED","MAYFAIR BANK LIMITED","0.0"
"code_36_2","Bank","2","2","M-PAYMENT","M-PAYMENT","0.0"
"code_36_3","Bank","3","3","KENYA POST OFFICE SAVINGS BANK","KENYA POST OFFICE SAVINGS BANK","0.0"
"code_36_4","Bank","4","4","FIRST COMMUNITY BANKS LIMITED","FIRST COMMUNITY BANKS LIMITED","0.0"
"code_36_5","Bank","5","5","GULF AFRICAN BANK LTD","GULF AFRICAN BANK LTD","0.0"
"code_36_6","Bank","6","6","FAMILY BANK LTD","FAMILY BANK LTD","0.0"
"code_36_7","Bank","7","7","EQUITY BANK LIMITED","EQUITY BANK LIMITED","0.0"
"code_36_8","Bank","8","8","SIDIAN BANK LIMITED","SIDIAN BANK LIMITED","0.0"
"code_36_9","Bank","9","9","DIAMOND TRUST BANK LIMITED","DIAMOND TRUST BANK LIMITED","0.0"
"code_36_10","Bank","10","10","HOUSING FINANCE BANK","HOUSING FINANCE BANK","0.0"
"code_36_11","Bank","11","11","SBM BANK (KENYA) LIMITED","SBM BANK (KENYA) LIMITED","0.0"
"code_36_12","Bank","12","12","DEVELOPMENT BANK OF KENYA LIMITED","DEVELOPMENT BANK OF KENYA LIMITED","0.0"
"code_36_13","Bank","13","13","I&M BANK LIMITED","I&M BANK LIMITED","0.0"
"code_36_14","Bank","14","14","GUARDIAN BANK LIMITED","GUARDIAN BANK LIMITED","0.0"
"code_36_15","Bank","15","15","VICTORIA COMMERCIAL BANK LIMITED","VICTORIA COMMERCIAL BANK LIMITED","0.0"
"code_36_16","Bank","16","16","GUARANTY TRUST BANK KENYA LIMITED","GUARANTY TRUST BANK KENYA LIMITED","0.0"
"code_36_17","Bank","17","17","JAMII BORA BANK","JAMII BORA BANK","0.0"
"code_36_18","Bank","18","18","PARAMOUNT UNIVERSAL BANK LIMITED","PARAMOUNT UNIVERSAL BANK LIMITED","0.0"
"code_36_19","Bank","19","19","EQUATORIAL COMMERCIAL HOLDING LIMITED","EQUATORIAL COMMERCIAL HOLDING LIMITED","0.0"
"code_36_20","Bank","20","20","GIRO COMMERCIAL BANK LIMITED","GIRO COMMERCIAL BANK LIMITED","0.0"
"code_36_21","Bank","21","21","NIC BANK LIMITED","NIC BANK LIMITED","0.0"
"code_36_22","Bank","22","22","IMPERIAL BANK LIMITED","IMPERIAL BANK LIMITED","0.0"
"code_36_23","Bank","23","23","CFC STANBIC BANK KENYA LIMITED","CFC STANBIC BANK KENYA LIMITED","0.0"
"code_36_24","Bank","24","24","CHASE BANK LIMITED","CHASE BANK LIMITED","0.0"
"code_36_25","Bank","25","25","ACCESS BANK (KENYA) PLC","ACCESS BANK (KENYA) PLC","0.0"
"code_36_26","Bank","26","26","CREDIT BANK LIMITED","CREDIT BANK LIMITED","0.0"
"code_36_27","Bank","27","27","CONSOLIDATED BANK OF KENYA LIMITED","CONSOLIDATED BANK OF KENYA LIMITED","0.0"
"code_36_28","Bank","28","28","DUBAI BANK KENYA LIMITED","DUBAI BANK KENYA LIMITED","0.0"
"code_36_29","Bank","29","29","BANK OF AFRICA KENYA LIMITED","BANK OF AFRICA KENYA LIMITED","0.0"
"code_36_30","Bank","30","30","MIDDLE EAST BANK KENYA LIMITED","MIDDLE EAST BANK KENYA LIMITED","0.0"
"code_36_31","Bank","31","31","HABIB BANK A.G. ZURICH","HABIB BANK A.G. ZURICH","0.0"
"code_36_32","Bank","32","32","CITIBANK N.A.","CITIBANK N.A.","0.0"
"code_36_33","Bank","33","33","ORIENTAL COMMERCIAL BANK LIMITED","ORIENTAL COMMERCIAL BANK LIMITED","0.0"
"code_36_34","Bank","34","34","NATIONAL BANK OF KENYA LTD","NATIONAL BANK OF KENYA LTD","0.0"
"code_36_35","Bank","35","35","PRIME BANK LIMITED","PRIME BANK LIMITED","0.0"
"code_36_36","Bank","36","36","CENTRAL BANK OF KENYA","CENTRAL BANK OF KENYA","0.0"
"code_36_37","Bank","37","37","HABIB BANK LIMITED","HABIB BANK LIMITED","0.0"
"code_36_38","Bank","38","38","COMMERCIAL BANK OF AFRICA LIMITED","COMMERCIAL BANK OF AFRICA LIMITED","0.0"
"code_36_39","Bank","39","39","BANK OF BARODA KENYA LIMITED","BANK OF BARODA KENYA LIMITED","0.0"
"code_36_40","Bank","40","40","BANK OF INDIA","BANK OF INDIA","0.0"
"code_36_41","Bank","41","41","ABSA BANK KENYA PLC","ABSA BANK KENYA PLC","0.0"
"code_36_42","Bank","42","42","STANDARD CHARTERED BANK KENYA LIMITED","STANDARD CHARTERED BANK KENYA LIMITED","0.0"
"code_36_43","Bank","43","43","KENYA COMMERCIAL BANK LTD","KENYA COMMERCIAL BANK LTD","0.0"
"code_36_44","Bank","44","44","AFRICAN BANKING CORP BANK LTD","AFRICAN BANKING CORP BANK LTD","0.0"
"code_36_45","Bank","45","45","ECO BANK LIMITED","ECO BANK LIMITED","0.0"
"code_36_46","Bank","46","46","UBA KENYA BANK LTD","UBA KENYA BANK LTD","0.0"
"code_36_47","Bank","47","47","CO-OPERATIVE BANK OF KENYA LIMITED","CO-OPERATIVE BANK OF KENYA LIMITED","0.0"
"code_37_S","Sales Receipt Type","10","S","Invoice","Invoice","0.0"
"code_37_R","Sales Receipt Type","11","R","Credit Note","Credit Note","0.0"
"code_38_P","Purchase Receipt Type","1","P","Invoice","Invoice","0.0"
"code_38_R","Purchase Receipt Type","2","R","Credit Note","Credit Note","0.0"
"code_45_52","Tax Office","2","52","Head Quarters",,"0.0"
"code_45_18","Tax Office","4","18","KITUI",,"0.0"
"code_45_37","Tax Office","5","37","SAMBURU",,"0.0"
"code_45_19","Tax Office","6","19","KWALE",,"0.0"
"code_45_25","Tax Office","7","25","MARSABIT",,"0.0"
"code_45_27","Tax Office","8","27","MIGORI",,"0.0"
"code_45_32","Tax Office","10","32","NANDI",,"0.0"
"code_45_33","Tax Office","11","33","NAROK",,"0.0"
"code_45_34","Tax Office","12","34","NYAMIRA",,"0.0"
"code_45_38","Tax Office","13","38","SIAYA",,"0.0"
"code_45_16","Tax Office","15","16","West of Nairobi",,"0.0"
"code_45_60","Tax Office","16","60","LODWAR",,"0.0"
"code_45_14","Tax Office","17","14","Thika",,"0.0"
"code_45_13","Tax Office","19","13","South of Nairobi",,"0.0"
"code_45_45","Tax Office","20","45","VIHIGA",,"0.0"
"code_45_20","Tax Office","21","20","LAIKIPIA",,"0.0"
"code_45_23","Tax Office","22","23","MAKUENI",,"0.0"
"code_45_35","Tax Office","23","35","NYANDARUA",,"0.0"
"code_45_39","Tax Office","24","39","TAITA TAVETA",,"0.0"
"code_45_40","Tax Office","25","40","TANA RIVER",,"0.0"
"code_45_42","Tax Office","26","42","TRANS NZOIA",,"0.0"
"code_45_43","Tax Office","28","43","TURKANA",,"0.0"
"code_45_44","Tax Office","29","44","UASIN GISHU",,"0.0"
"code_45_47","Tax Office","30","47","WEST POKOT",,"0.0"
"code_45_29","Tax Office","31","29","Murang'a",,"0.0"
"code_45_41","Tax Office","32","41","Tharaka-Nithi",,"0.0"
"code_45_12","Tax Office","36","12","North of Nairobi",,"0.0"
"code_45_36","Tax Office","37","36","Nyeri",,"0.0"
"code_45_10","Tax Office","40","10","Mombasa South",,"0.0"
"code_45_9","Tax Office","41","9","Mombasa North",,"0.0"
"code_45_26","Tax Office","42","26","Meru",,"0.0"
"code_45_8","Tax Office","43","8","Malindi",,"0.0"
"code_45_22","Tax Office","44","22","Machakos",,"0.0"
"code_45_17","Tax Office","45","17","Kisumu",,"0.0"
"code_45_7","Tax Office","46","7","Kisii",,"0.0"
"code_45_6","Tax Office","47","6","Kakamega",,"0.0"
"code_45_95","Tax Office","48","95","PREMIER TAX OFFICE",,"0.0"
"code_45_59","Tax Office","49","59","EASTLEIGH",,"0.0"
"code_45_2","Tax Office","50","2","East of Nairobi",,"0.0"
"code_45_3","Tax Office","51","3","Eldoret",,"0.0"
"code_45_4","Tax Office","52","4","Embu",,"0.0"
"code_45_1","Tax Office","54","1","BUNGOMA",,"0.0"
"code_45_5","Tax Office","55","5","GARISSA",,"0.0"
"code_45_24","Tax Office","57","24","MANDERA",,"0.0"
"code_45_49","Tax Office","61","49","LTO",,"0.0"
"code_45_50","Tax Office","62","50","MTO",,"0.0"
"code_45_68","Tax Office","66","68","KRAHP",,"0.0"
"code_45_70","Tax Office","68","70","PUBLIC SECTOR DIVISION",,"0.0"
"code_45_15","Tax Office","69","15","VOI",,"0.0"
"code_45_51","Tax Office","80","51","KERICHO",,"0.0"
"code_45_31","Tax Office","81","31","NAKURU",,"0.0"
"code_45_86","Tax Office","82","86","KERUGOYA",,"0.0"
"code_45_87","Tax Office","83","87","MURANGA",,"0.0"
"code_45_88","Tax Office","84","88","NANYUKI",,"0.0"
"code_45_89","Tax Office","85","89","KIAMBU",,"0.0"
"code_45_83","Tax Office","86","83","KITALE",,"0.0"
"code_45_64","Tax Office","87","64","ISIOLO",,"0.0"
"code_45_90","Tax Office","88","90","KAJIADO",,"0.0"
"code_45_91","Tax Office","89","91","KITUI",,"0.0"
"code_45_92","Tax Office","90","92","MOYALE",,"0.0"
"code_45_46","Tax Office","91","46","WAJIR",,"0.0"
"code_45_85","Tax Office","92","85","MARALAL",,"0.0"
"code_45_61","Tax Office","93","61","NAIVASHA",,"0.0"
"code_45_67","Tax Office","94","67","NAROK",,"0.0"
"code_45_84","Tax Office","95","84","NYAHURURU",,"0.0"
"code_45_93","Tax Office","96","93","HOMABAY",,"0.0"
"code_45_94","Tax Office","97","94","BUSIA",,"0.0"
"code_45_21","Tax Office","98","21","LAMU",,"0.0"
"code_45_99","Tax Office","99","99","DIANI",,"0.0"
"code_48_en_GB","Locale","1","en_GB","English",,"0.0"
"code_48_en_sw","Locale","2","en_sw","Kiswahili",,"0.0"
"code_48_fr_FR","Locale","3","fr_FR","French",,"0.0"
"code_48_ko_KR","Locale","4","ko_KR","???",,"0.0"
"code_49_1","Category Level","1","1","Sagement",,"0.0"
"code_49_2","Category Level","2","2","Family",,"0.0"
"code_49_3","Category Level","3","3","Class",,"0.0"
"code_49_4","Category Level","4","4","Commodity",,"0.0"
