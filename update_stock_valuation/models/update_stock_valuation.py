# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import UserError, ValidationError


class UpdateStockValuation(models.Model):
    _name = 'update.stock.valuation'
    _description = 'Stock Valuation Adjustment'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _order = 'id desc'

    name = fields.Char('Reference', required=True, copy=False, readonly=True, default='New')
    date = fields.Date('Date', required=True, default=fields.Date.context_today)
    location_id = fields.Many2one('stock.location', string='Location')
    company_id = fields.Many2one(
        comodel_name='res.company',
        string='Company',
        required=True,
        readonly=True,
        default=lambda self: self.env.company
    )
    state = fields.Selection(
        selection=[('draft', 'Draft'), ('done', 'Done'), ('cancel', 'Cancelled')],
        string='Status',
        default='draft',
        tracking=True
    )
    line_ids = fields.One2many(
        comodel_name='update.stock.valuation.line',
        inverse_name='update_stock_valuation_id',
        string='Lines'
    )
    journal_id = fields.Many2one(
        comodel_name='account.journal',
        string='Journal',
        required=True,
        domain="[('type', '=', 'general'), ('company_id', '=', company_id)]"
    )
    svl_ids = fields.Many2many(
        'stock.valuation.layer',
        'usv_svl_ref',
        'usv_id',
        'svl_id',
        string='Stock Valuation Layers'
    )
    svl_count = fields.Integer('SVL', compute='_compute_svl_count', store=True)

    @api.depends('svl_ids')
    def _compute_svl_count(self):
        for rec in self:
            rec.svl_count = len(rec.svl_ids)

    @api.model_create_multi
    def create(self, vals_list):
        for vals in vals_list:
            if vals.get('name', 'New') == 'New':
                vals['name'] = self.env['ir.sequence'].next_by_code('update.stock.valuation') or 'New'
        return super().create(vals_list)

    def action_confirm(self):
        if not self.line_ids:
            raise UserError("Please add at least one line.")
        if not self.company_id:
            raise ValidationError(_("Please select a company."))
        self._create_stock_valuation_layers()

    def _get_update_stock_valuation_account(self):
        account = self.env['account.account'].search([
            ('account_type', '=', 'expense'),
            ('company_ids', 'in', [self.company_id.id])
        ], limit=1)
        if not account:
            raise ValidationError(_("Please configure an expense account for the company."))
        return account

    def _create_journal_entry_vals(self, line):
        move_vals = {
            'journal_id': line.journal_id.id,
            'date': line.date,
            'ref': line.name,
            'company_id': line.company_id.id,
            'line_ids': []
        }

        expense_account = self._get_update_stock_valuation_account()
        stock_account = line.product_id.categ_id.property_stock_valuation_account_id

        if not stock_account:
            raise UserError(_(
                f"Stock valuation account is not defined for product category {line.product_id.categ_id.name}"
            ))

        # Create move lines
        move_vals['line_ids'].append((0, 0, {
            'name': f'Stock Valuation Adjustment - {line.product_id.display_name}',
            'account_id': expense_account.id,
            'debit': abs(line.difference) if line.difference < 0 else 0,
            'credit': abs(line.difference) if line.difference > 0 else 0,
            'product_id': line.product_id.id,
        }))
        move_vals['line_ids'].append((0, 0, {
            'name': f'Stock Valuation - {line.product_id.display_name}',
            'account_id': stock_account.id,
            'debit': abs(line.difference) if line.difference > 0 else 0,
            'credit': abs(line.difference) if line.difference < 0 else 0,
            'product_id': line.product_id.id,
        }))

        return move_vals

    def create_svl_vals(self, line):
        svl_vals = {
            'reference': line.name,
            'product_id': line.product_id.id,
            'company_id': line.company_id.id,
            'quantity': 0,
            'unit_cost': 0,
            'value': line.difference,
            'description': f'Stock Valuation Adjustment - {line.name}',
            'stock_move_id': False,
            'account_move_id': False,
        }

        if line.product_id.tracking != 'none' and line.lot_id:
            svl_vals['lot_id'] = line.lot_id.id
            svl_vals['description'] += f' - {line.lot_id.name}'

        return svl_vals

    def _create_stock_valuation_layers(self):
        for line in self.line_ids:
            if line.difference == 0 or line.state == 'done':
                continue

            move_vals = self._create_journal_entry_vals(line)
            if move_vals:
                move = self.env['account.move'].create(move_vals)
                move.sudo().action_post()
                line.account_move_id = move.id

            svl_vals = self.create_svl_vals(line)
            if svl_vals:
                svl_vals['account_move_id'] = line.account_move_id.id
                svl_id = self.env['stock.valuation.layer'].create(svl_vals)
                if svl_id:
                    line.svl_id = svl_id.id
                    line.update_stock_valuation_id.svl_ids = [(4, svl_id.id)]
            if line.svl_id and line.account_move_id:
                line.state = 'done'
        if all(rec == 'done' for rec in self.line_ids.mapped('state')):
            self.state = 'done'

    def action_cancel(self):
        if self.move_id and self.move_id.state == 'posted':
            reverse_move = self.move_id._reverse_moves([{
                'date': fields.Date.context_today(self),
                'ref': f'Reversal of {self.name}'
            }])
            reverse_move.action_post()
        self.state = 'cancel'

    def action_open_svl(self):
        if self.svl_ids:
            return {
                'name': 'Stock Valuation Layers',
                'type': 'ir.actions.act_window',
                'res_model': 'stock.valuation.layer',
                'view_mode': 'list',
                'domain': [('id', 'in', self.svl_ids.ids)],
            }


class UpdateStockValuationLine(models.Model):
    _name = 'update.stock.valuation.line'
    _description = 'Update Stock Valuation Line'

    update_stock_valuation_id = fields.Many2one(
        comodel_name='update.stock.valuation',
        string='Stock Valuation Adjustment',
        required=True,
        ondelete='cascade'
    )
    account_move_id = fields.Many2one(
        comodel_name='account.move',
        string='Journal Entry',
        readonly=True
    )
    product_id = fields.Many2one(
        comodel_name='product.product',
        string='Product',
        required=True,
        domain="[('type', '=', 'consu'), ('company_id', 'in', [company_id, False])]"
    )
    lot_id = fields.Many2one(
        comodel_name='stock.lot',
        string='Lot/Serial Number',
        domain="[('product_id', '=', product_id), ('company_id', 'in', [company_id, False])]"
    )
    date = fields.Date('Date', related='update_stock_valuation_id.date', store=True, readonly=True)
    location_id = fields.Many2one(
        comodel_name='stock.location',
        string='Location',
        related='update_stock_valuation_id.location_id',
        store=True,
        readonly=True
    )
    journal_id = fields.Many2one(
        comodel_name='account.journal',
        string='Journal',
        related='update_stock_valuation_id.journal_id',
        store=True,
        readonly=True
    )
    state = fields.Selection(
        selection=[('draft', 'Draft'), ('done', 'Done'), ('cancel', 'Cancelled')],
        string='Status',
        default='draft',
    )
    name = fields.Char('Name', related='update_stock_valuation_id.name', store=True)
    quantity = fields.Float('Quantity', default=1.0)
    available_quantity = fields.Float('Available Quantity', compute='_compute_available_quantity', store=True)
    current_value = fields.Float('Current Stock Value', compute='_compute_current_value', store=True)
    update_value = fields.Float('Updated Value')
    difference = fields.Float('Difference', compute='_compute_difference', store=True)
    tracking = fields.Selection(related='product_id.tracking', readonly=True)
    company_id = fields.Many2one(related='update_stock_valuation_id.company_id', readonly=True, store=True)
    svl_id = fields.Many2one('stock.valuation.layer', 'Stock Valuation Layer', readonly=True, index=True)

    @api.depends('product_id', 'lot_id', 'company_id', 'location_id')
    def _compute_available_quantity(self):
        print('_compute_available_quantity')
        for line in self:
            if line.product_id:
                domain = [
                    ('product_id', '=', line.product_id.id),
                    ('location_id.usage', '=', 'internal'),
                    ('company_id', '=', line.company_id.id)
                ]
                if line.lot_id:
                    domain.append(('lot_id', '=', line.lot_id.id))
                if line.location_id:
                    domain.append(('location_id', '=', line.location_id.id))
                quants = self.env['stock.quant'].search(domain)
                line.available_quantity = sum(quants.mapped('quantity'))
            else:
                line.available_quantity = 0.0

    @api.depends('product_id', 'lot_id', 'available_quantity')
    def _compute_current_value(self):
        for line in self:
            if line.product_id:
                line.current_value = line.available_quantity * line.product_id.standard_price
            else:
                line.current_value = 0.0

    @api.depends('current_value', 'update_value')
    def _compute_difference(self):
        for line in self:
            line.difference = line.update_value - line.current_value

    @api.constrains('current_value', 'available_quantity', 'product_id', 'lot_id')
    def _check_quantity(self):
        for record in self:
            if record.product_id.tracking == 'none' and record.lot_id:
                raise UserError(_("Product is not tracked by lot/serial number, please remove lot/serial number."))
            if record.product_id.tracking in ['serial', 'lot'] and not record.lot_id:
                raise ValidationError(_("Product is tracked by lot/serial number, please select lot/serial number."))
