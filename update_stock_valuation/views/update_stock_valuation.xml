<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Form view -->
    <record id="update_stock_valuation_form_view" model="ir.ui.view">
        <field name="name">update.stock.valuation.form</field>
        <field name="model">update.stock.valuation</field>
        <field name="arch" type="xml">
            <form string="Stock Valuation Update">
                <header>
                    <button name="action_confirm" invisible="state != 'draft'" string="Confirm" type="object"
                            class="oe_highlight"/>
                    <button name="action_cancel" string="Cancel" type="object"/>
                    <field name="state" widget="statusbar"/>
                </header>
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button name="action_open_svl" invisible="not svl_ids" type="object" class="oe_stat_button"
                                icon="fa-bars">
                            <div class="o_field_widget o_stat_info">
                                <span class="o_stat_value">
                                    <field name="svl_count"/>
                                </span>
                                <span class="o_stat_text">SVL</span>
                            </div>
                        </button>
                    </div>
                    <widget name="web_ribbon" title="Done" bg_color="text-bg-success" invisible="state != 'done'"/>
                    <div class="oe_title">
                        <h1>
                            <field name="name" readonly="1"/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="date"/>
                            <field name="company_id" readonly="1"/>
                            <field name="location_id" required="1" options="{'no_open': True, 'no_create': True}"/>
                        </group>
                        <group>
                            <field name="journal_id" options="{'no_open': True, 'no_create': True}"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Valuation Lines">
                            <field name="line_ids" readonly="state != 'draft'">
                                <list editable="bottom">
                                    <field name="product_id" width="200px" options="{'no_create':True}"/>
                                    <field name="lot_id" width="200px" options="{'no_create':True}"/>
                                    <field name="available_quantity" readonly="1" width="100px"/>
                                    <field name="current_value" width="100px"/>
                                    <field name="location_id" width="100px"/>
                                    <field name="update_value" width="100px"/>
                                    <field name="difference" readonly="1" width="100px"/>
                                </list>
                            </field>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <!-- List view -->
    <record id="update_stock_valuation_list_view" model="ir.ui.view">
        <field name="name">update.stock.valuation.list.view</field>
        <field name="model">update.stock.valuation</field>
        <field name="arch" type="xml">
            <list>
                <field name="name"/>
                <field name="date"/>
                <field name="company_id"/>
                <field name="state" widget="badge" decoration-success="state == 'done'"
                       decoration-info="state == 'draft'" decoration-danger="state == 'cancel'"/>
            </list>
        </field>
    </record>

    <!-- Action -->
    <record id="action_update_stock_valuation" model="ir.actions.act_window">
        <field name="name">Update Stock Valuation</field>
        <field name="res_model">update.stock.valuation</field>
        <field name="view_mode">list,form</field>
    </record>
</odoo>