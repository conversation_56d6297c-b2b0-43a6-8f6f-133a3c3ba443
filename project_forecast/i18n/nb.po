# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* project_forecast
# 
# Translators:
# <PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:28+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON>, 2024\n"
"Language-Team: <PERSON> (https://app.transifex.com/odoo/teams/41243/nb/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: nb\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: project_forecast
#: model_terms:ir.ui.view,arch_db:project_forecast.planning_shift_ics_description_inherit
msgid "<b>Project:</b>"
msgstr ""

#. module: project_forecast
#: model:ir.ui.menu,name:project_forecast.planning_menu_schedule_by_project
msgid "By Project"
msgstr "Etter prosjekt"

#. module: project_forecast
#: model:ir.model.fields,field_description:project_forecast.field_planning_slot_template__company_id
msgid "Company"
msgstr "Firma"

#. module: project_forecast
#: model_terms:ir.actions.act_window,help:project_forecast.project_forecast_action_from_project
msgid "Let's start your planning by adding a new shift."
msgstr ""

#. module: project_forecast
#: model_terms:ir.ui.view,arch_db:project_forecast.planning_slot_template_inherit_view_search
#: model_terms:ir.ui.view,arch_db:project_forecast.planning_slot_view_search
msgid "My Projects"
msgstr "Mine prosjekter"

#. module: project_forecast
#: model_terms:ir.actions.act_window,help:project_forecast.planning_action_schedule_by_project
#: model_terms:ir.actions.act_window,help:project_forecast.planning_action_schedule_project
#: model_terms:ir.actions.act_window,help:project_forecast.project_forecast_action_from_project
#: model_terms:ir.actions.act_window,help:project_forecast.project_forecast_action_schedule_by_employee
msgid "No shifts found. Let's create one!"
msgstr ""

#. module: project_forecast
#. odoo-python
#: code:addons/project_forecast/models/project.py:0
msgid "Planned"
msgstr "Planlagt"

#. module: project_forecast
#: model:ir.actions.act_window,name:project_forecast.planning_action_schedule_project
#: model:ir.actions.act_window,name:project_forecast.project_forecast_action_from_project
#: model:ir.embedded.actions,name:project_forecast.project_embedded_action_planning
#: model:ir.embedded.actions,name:project_forecast.project_embedded_action_planning_dashboard
msgid "Planning"
msgstr "Planlegging"

#. module: project_forecast
#: model:ir.model,name:project_forecast.model_planning_analysis_report
msgid "Planning Analysis Report"
msgstr ""

#. module: project_forecast
#: model:ir.model,name:project_forecast.model_planning_slot
msgid "Planning Shift"
msgstr ""

#. module: project_forecast
#: model:ir.model,name:project_forecast.model_project_project
#: model:ir.model.fields,field_description:project_forecast.field_planning_analysis_report__project_id
#: model:ir.model.fields,field_description:project_forecast.field_planning_slot__project_id
#: model:ir.model.fields,field_description:project_forecast.field_planning_slot_template__project_id
#: model_terms:ir.ui.view,arch_db:project_forecast.open_slots_list_template
#: model_terms:ir.ui.view,arch_db:project_forecast.period_report_template
#: model_terms:ir.ui.view,arch_db:project_forecast.planning_slot_view_search
#: model_terms:ir.ui.view,arch_db:project_forecast.resource_planning_project_forecast
msgid "Project"
msgstr "Prosjekt"

#. module: project_forecast
#: model_terms:ir.ui.view,arch_db:project_forecast.planning_shift_ics_description_inherit
msgid "Project:"
msgstr ""

#. module: project_forecast
#: model:ir.actions.act_window,name:project_forecast.planning_action_schedule_by_project
msgid "Schedule by Project"
msgstr ""

#. module: project_forecast
#: model:ir.actions.act_window,name:project_forecast.project_forecast_action_schedule_by_employee
msgid "Schedule by Resource"
msgstr ""

#. module: project_forecast
#: model_terms:ir.actions.act_window,help:project_forecast.planning_action_schedule_by_project
#: model_terms:ir.actions.act_window,help:project_forecast.planning_action_schedule_project
#: model_terms:ir.actions.act_window,help:project_forecast.project_forecast_action_schedule_by_employee
msgid ""
"Schedule your human and material resources across roles, projects and sales "
"orders."
msgstr ""

#. module: project_forecast
#: model:ir.model,name:project_forecast.model_planning_slot_template
msgid "Shift Template"
msgstr ""

#. module: project_forecast
#: model:planning.role,name:project_forecast.planning_role_tester
msgid "Tester"
msgstr ""

#. module: project_forecast
#. odoo-python
#: code:addons/project_forecast/models/project.py:0
msgid ""
"You cannot update the company for the %(project_name)s project because it’s tied to shifts in another company.\n"
"To change it, first clear the company field for the project. Then move the shifts to the new company, and update the project's company."
msgstr ""
