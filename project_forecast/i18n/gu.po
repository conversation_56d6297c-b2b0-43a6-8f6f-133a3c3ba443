# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * project_forecast
#
# Translators:
# <PERSON>, 2018
# <PERSON><PERSON>, 2018
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2018
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~11.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-05-16 16:03+0000\n"
"PO-Revision-Date: 2018-09-21 14:06+0000\n"
"Last-Translator: <PERSON>har<PERSON>raj Jhala <<EMAIL>>, 2018\n"
"Language-Team: Gujarati (https://www.transifex.com/odoo/teams/41243/gu/)\n"
"Language: gu\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: project_forecast
#: model:ir.ui.menu,name:project_forecast.planning_menu_schedule_by_project
msgid "By Project"
msgstr ""

#. module: project_forecast
#: model_terms:ir.actions.act_window,help:project_forecast.project_forecast_action_from_project
msgid "Let's start your planning by adding a new shift."
msgstr ""

#. module: project_forecast
#: model_terms:ir.ui.view,arch_db:project_forecast.planning_analysis_report_view_search
#: model_terms:ir.ui.view,arch_db:project_forecast.planning_slot_template_inherit_view_search
#: model_terms:ir.ui.view,arch_db:project_forecast.planning_slot_view_search
msgid "My Projects"
msgstr ""

#. module: project_forecast
#: model_terms:ir.actions.act_window,help:project_forecast.planning_action_schedule_by_project
#: model_terms:ir.actions.act_window,help:project_forecast.project_forecast_action_from_project
#: model_terms:ir.actions.act_window,help:project_forecast.project_forecast_action_schedule_by_employee
msgid "No shifts found. Let's create one!"
msgstr ""

#. module: project_forecast
#: model_terms:ir.ui.view,arch_db:project_forecast.project_view_form_inherit_project_forecast
msgid "Plan your resources for this project"
msgstr ""

#. module: project_forecast
#: model_terms:ir.ui.view,arch_db:project_forecast.project_project_view_form_simplified_inherit_forecast
msgid "Plan your resources on project tasks"
msgstr ""

#. module: project_forecast
#. odoo-python
#: code:addons/project_forecast/models/project.py:0
msgid "Planned"
msgstr ""

#. module: project_forecast
#: model:ir.actions.act_window,name:project_forecast.project_forecast_action_from_project
#: model:ir.model.fields,field_description:project_forecast.field_planning_slot__allow_forecast
#: model:ir.model.fields,field_description:project_forecast.field_project_project__allow_forecast
#: model_terms:ir.ui.view,arch_db:project_forecast.project_view_kanban_inherit_project_forecast
msgid "Planning"
msgstr ""

#. module: project_forecast
#: model:ir.model,name:project_forecast.model_planning_analysis_report
msgid "Planning Analysis Report"
msgstr ""

#. module: project_forecast
#: model:ir.model,name:project_forecast.model_planning_slot
msgid "Planning Shift"
msgstr ""

#. module: project_forecast
#: model:ir.model,name:project_forecast.model_project_project
#: model:ir.model.fields,field_description:project_forecast.field_planning_analysis_report__project_id
#: model:ir.model.fields,field_description:project_forecast.field_planning_slot__project_id
#: model:ir.model.fields,field_description:project_forecast.field_planning_slot_template__project_id
#: model_terms:ir.ui.view,arch_db:project_forecast.open_slots_list_template
#: model_terms:ir.ui.view,arch_db:project_forecast.period_report_template
#: model_terms:ir.ui.view,arch_db:project_forecast.planning_analysis_report_view_search
#: model_terms:ir.ui.view,arch_db:project_forecast.planning_slot_view_search
#: model_terms:ir.ui.view,arch_db:project_forecast.resource_planning_project_forecast
msgid "Project"
msgstr ""

#. module: project_forecast
#: model:ir.actions.act_window,name:project_forecast.planning_action_schedule_by_project
msgid "Schedule by Project"
msgstr ""

#. module: project_forecast
#: model:ir.actions.act_window,name:project_forecast.project_forecast_action_schedule_by_employee
msgid "Schedule by Resource"
msgstr ""

#. module: project_forecast
#: model_terms:ir.actions.act_window,help:project_forecast.planning_action_schedule_by_project
#: model_terms:ir.actions.act_window,help:project_forecast.project_forecast_action_schedule_by_employee
msgid "Schedule your human and material resources across roles, projects and sales orders."
msgstr ""

#. module: project_forecast
#. odoo-python
#: code:addons/project_forecast/controllers/main.py:0
msgid "Shift"
msgstr ""

#. module: project_forecast
#: model:ir.model,name:project_forecast.model_planning_slot_template
msgid "Shift Template"
msgstr ""

#. module: project_forecast
#: model:planning.role,name:project_forecast.planning_role_tester
msgid "Tester"
msgstr ""

#. module: project_forecast
#: model:ir.model.fields,field_description:project_forecast.field_project_project__total_forecast_time
msgid "Total Forecast Time"
msgstr ""

#. module: project_forecast
#: model:ir.model.fields,help:project_forecast.field_project_project__total_forecast_time
msgid "Total number of forecast hours in the project rounded to the unit."
msgstr ""
