# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* project_timesheet_forecast_sale
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2025
# <PERSON> <<EMAIL>>, 2025
# Wil Odoo, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-19 09:54+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Wil Odoo, 2025\n"
"Language-Team: Portuguese (https://app.transifex.com/odoo/teams/41243/pt/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: pt\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: project_timesheet_forecast_sale
#. odoo-python
#: code:addons/project_timesheet_forecast_sale/models/project.py:0
msgid "%(name)s's Timesheets and Planning Analysis"
msgstr ""

#. module: project_timesheet_forecast_sale
#: model:ir.model,name:project_timesheet_forecast_sale.model_account_analytic_line
msgid "Analytic Line"
msgstr "Linha Analítica"

#. module: project_timesheet_forecast_sale
#: model:ir.model.fields,field_description:project_timesheet_forecast_sale.field_planning_analysis_report__billable_allocated_hours
msgid "Billable Time Allocated"
msgstr ""

#. module: project_timesheet_forecast_sale
#: model:planning.role,name:project_timesheet_forecast_sale.planning_role_consultant
msgid "Consultant"
msgstr "Consultor"

#. module: project_timesheet_forecast_sale
#: model:ir.model.fields,field_description:project_timesheet_forecast_sale.field_project_timesheet_forecast_report_analysis__effective_billable_hours
msgid "Effective Billable Time"
msgstr ""

#. module: project_timesheet_forecast_sale
#: model:ir.model.fields,field_description:project_timesheet_forecast_sale.field_project_timesheet_forecast_report_analysis__effective_margin
msgid "Effective Margin"
msgstr ""

#. module: project_timesheet_forecast_sale
#: model:ir.model.fields,field_description:project_timesheet_forecast_sale.field_project_timesheet_forecast_report_analysis__effective_non_billable_hours
msgid "Effective Non-Billable Time"
msgstr ""

#. module: project_timesheet_forecast_sale
#: model:ir.model.fields,field_description:project_timesheet_forecast_sale.field_project_timesheet_forecast_report_analysis__effective_revenues
msgid "Effective Revenues"
msgstr ""

#. module: project_timesheet_forecast_sale
#: model:ir.model.fields,field_description:project_timesheet_forecast_sale.field_planning_analysis_report__non_billable_allocated_hours
msgid "Non-billable Time Allocated"
msgstr ""

#. module: project_timesheet_forecast_sale
#: model:ir.model.fields,field_description:project_timesheet_forecast_sale.field_project_timesheet_forecast_report_analysis__planned_billable_hours
msgid "Planned Billable Time"
msgstr ""

#. module: project_timesheet_forecast_sale
#: model:ir.model.fields,field_description:project_timesheet_forecast_sale.field_project_timesheet_forecast_report_analysis__planned_margin
msgid "Planned Margin"
msgstr ""

#. module: project_timesheet_forecast_sale
#: model:ir.model.fields,field_description:project_timesheet_forecast_sale.field_project_timesheet_forecast_report_analysis__planned_non_billable_hours
msgid "Planned Non-Billable Time"
msgstr ""

#. module: project_timesheet_forecast_sale
#: model:ir.model.fields,field_description:project_timesheet_forecast_sale.field_project_timesheet_forecast_report_analysis__planned_revenues
msgid "Planned Revenues"
msgstr ""

#. module: project_timesheet_forecast_sale
#: model:ir.model,name:project_timesheet_forecast_sale.model_planning_analysis_report
msgid "Planning Analysis Report"
msgstr "Relatório de Análise do Planeamento"

#. module: project_timesheet_forecast_sale
#: model:ir.model,name:project_timesheet_forecast_sale.model_planning_slot
msgid "Planning Shift"
msgstr "Planejar Turno"

#. module: project_timesheet_forecast_sale
#: model:ir.model,name:project_timesheet_forecast_sale.model_project_project
msgid "Project"
msgstr "Projeto"

#. module: project_timesheet_forecast_sale
#: model:ir.model,name:project_timesheet_forecast_sale.model_project_update
msgid "Project Update"
msgstr "Atualização de Projeto"

#. module: project_timesheet_forecast_sale
#: model:ir.model,name:project_timesheet_forecast_sale.model_sale_order_line
msgid "Sales Order Line"
msgstr "Linhas da Ordem de Venda"

#. module: project_timesheet_forecast_sale
#: model:planning.role,name:project_timesheet_forecast_sale.planning_role_junior_architect
msgid "Software Junior Architect"
msgstr ""

#. module: project_timesheet_forecast_sale
#: model:planning.role,name:project_timesheet_forecast_sale.planning_role_senior_architect
msgid "Software Senior Architect"
msgstr ""

#. module: project_timesheet_forecast_sale
#: model:ir.model.fields,help:project_timesheet_forecast_sale.field_planning_analysis_report__billable_allocated_hours
msgid "Sum of hours allocated to shifts linked to a SOL."
msgstr ""

#. module: project_timesheet_forecast_sale
#: model:ir.model.fields,help:project_timesheet_forecast_sale.field_planning_analysis_report__non_billable_allocated_hours
msgid "Sum of hours allocated to shifts not linked to a SOL."
msgstr ""

#. module: project_timesheet_forecast_sale
#: model:ir.model,name:project_timesheet_forecast_sale.model_project_timesheet_forecast_report_analysis
msgid "Timesheet & Planning Statistics"
msgstr ""

#. module: project_timesheet_forecast_sale
#. odoo-python
#: code:addons/project_timesheet_forecast_sale/models/project.py:0
msgid "Timesheets and Planning"
msgstr ""

#. module: project_timesheet_forecast_sale
#: model_terms:ir.ui.view,arch_db:project_timesheet_forecast_sale.project_timesheet_forecast_view_kanban_inherit_sale_timesheet
msgid "Timesheets and Planning Analysis"
msgstr ""
