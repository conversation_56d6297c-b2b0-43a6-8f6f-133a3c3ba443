# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_recruitment_integration_base
# 
# Translators:
# Wil Odoo, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:27+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Wil Odoo, 2025\n"
"Language-Team: Ukrainian (https://app.transifex.com/odoo/teams/41243/uk/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: uk\n"
"Plural-Forms: nplurals=4; plural=(n % 1 == 0 && n % 10 == 1 && n % 100 != 11 ? 0 : n % 1 == 0 && n % 10 >= 2 && n % 10 <= 4 && (n % 100 < 12 || n % 100 > 14) ? 1 : n % 1 == 0 && (n % 10 ==0 || (n % 10 >=5 && n % 10 <=9) || (n % 100 >=11 && n % 100 <=14 )) ? 2: 3);\n"

#. module: hr_recruitment_integration_base
#. odoo-python
#: code:addons/hr_recruitment_integration_base/models/hr_job_post.py:0
msgid "%(job)s on %(platform)s"
msgstr "%(job)s на %(platform)s"

#. module: hr_recruitment_integration_base
#: model_terms:ir.ui.view,arch_db:hr_recruitment_integration_base.hr_job_post_view_kanban
msgid "<span class=\"px-2\">-</span>"
msgstr ""

#. module: hr_recruitment_integration_base
#: model_terms:ir.ui.view,arch_db:hr_recruitment_integration_base.hr_job_post_view_kanban
msgid "<span class=\"text-muted\" invisible=\"campaign_end_date\">No limit</span>"
msgstr ""

#. module: hr_recruitment_integration_base
#: model_terms:ir.ui.view,arch_db:hr_recruitment_integration_base.hr_job_post_view_kanban
msgid "<strong class=\"o_kanban_label_width\">From:</strong>"
msgstr ""

#. module: hr_recruitment_integration_base
#: model_terms:ir.ui.view,arch_db:hr_recruitment_integration_base.hr_job_post_view_kanban
msgid "<strong class=\"o_kanban_label_width\">To:</strong>"
msgstr ""

#. module: hr_recruitment_integration_base
#: model_terms:ir.ui.view,arch_db:hr_recruitment_integration_base.hr_job_post_view_form
msgid "<strong class=\"px-2\">to</strong>"
msgstr ""

#. module: hr_recruitment_integration_base
#: model_terms:ir.ui.view,arch_db:hr_recruitment_integration_base.hr_recruitment_post_job_wizard_view_form
msgid "<strong>From</strong>"
msgstr "Від"

#. module: hr_recruitment_integration_base
#: model_terms:ir.ui.view,arch_db:hr_recruitment_integration_base.hr_recruitment_post_job_wizard_view_form
msgid "<strong>to</strong>"
msgstr "<strong>до</strong>"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job_post__api_data
msgid "API Data"
msgstr ""

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job_post__message_needaction
msgid "Action Needed"
msgstr "Необхідна дія"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job_post__activity_ids
msgid "Activities"
msgstr "Дії"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job_post__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Оформлення виключення дії"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job_post__activity_state
msgid "Activity State"
msgstr "Статус дії"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job_post__activity_type_icon
msgid "Activity Type Icon"
msgstr "Іконка типу дії"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_recruitment_post_job_wizard__apply_method
msgid "Apply Method"
msgstr ""

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job_post__message_attachment_count
msgid "Attachment Count"
msgstr "Підрахунок прикріплення"

#. module: hr_recruitment_integration_base
#: model:ir.actions.server,name:hr_recruitment_integration_base.job_board_campaign_manager_start_ir_actions_server
msgid "Automatic Job Posting on job boards"
msgstr ""

#. module: hr_recruitment_integration_base
#: model:ir.actions.server,name:hr_recruitment_integration_base.job_board_campaign_manager_stop_ir_actions_server
msgid "Automatic job posting deleting on job boards"
msgstr ""

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_recruitment_platform__avatar_1920
msgid "Avatar"
msgstr "Аватар"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_recruitment_platform__avatar_1024
msgid "Avatar 1024"
msgstr "Аватар 1024"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_recruitment_platform__avatar_128
msgid "Avatar 128"
msgstr "Аватар 128"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_recruitment_platform__avatar_256
msgid "Avatar 256"
msgstr "Аватар 256"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_recruitment_platform__avatar_512
msgid "Avatar 512"
msgstr "Аватар 512"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields.selection,name:hr_recruitment_integration_base.selection__hr_job__payment_interval__biweekly
msgid "Bi-Week"
msgstr ""

#. module: hr_recruitment_integration_base
#: model_terms:ir.ui.view,arch_db:hr_recruitment_integration_base.hr_job_post_view_form
msgid "Campaign Duration"
msgstr ""

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job_post__campaign_end_date
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_recruitment_post_job_wizard__campaign_end_date
msgid "Campaign End Date"
msgstr ""

#. module: hr_recruitment_integration_base
#. odoo-python
#: code:addons/hr_recruitment_integration_base/models/hr_job_post.py:0
msgid "Campaign End Date:"
msgstr ""

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job_post__campaign_start_date
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_recruitment_post_job_wizard__campaign_start_date
msgid "Campaign Start Date"
msgstr ""

#. module: hr_recruitment_integration_base
#. odoo-python
#: code:addons/hr_recruitment_integration_base/models/hr_job_post.py:0
msgid "Campaign Start Date:"
msgstr ""

#. module: hr_recruitment_integration_base
#. odoo-python
#: code:addons/hr_recruitment_integration_base/wizard/hr_recruitment_post.py:0
msgid "Campaign start date can't be after campaign end date"
msgstr ""

#. module: hr_recruitment_integration_base
#. odoo-python
#: code:addons/hr_recruitment_integration_base/wizard/hr_recruitment_post.py:0
msgid "Campaign will start on %(start_date)s"
msgstr ""

#. module: hr_recruitment_integration_base
#. odoo-python
#: code:addons/hr_recruitment_integration_base/wizard/hr_recruitment_post.py:0
msgid "Can't postpone posts that are already posted"
msgstr ""

#. module: hr_recruitment_integration_base
#: model_terms:ir.ui.view,arch_db:hr_recruitment_integration_base.hr_recruitment_post_job_wizard_view_form
msgid "Cancel"
msgstr "Скасувати"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job_post__company_id
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_recruitment_post_job_wizard__company_id
msgid "Company"
msgstr "Компанія"

#. module: hr_recruitment_integration_base
#. odoo-python
#: code:addons/hr_recruitment_integration_base/models/hr_job_post.py:0
msgid "Company:"
msgstr "Компанія:"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job_post__apply_method
#: model_terms:ir.ui.view,arch_db:hr_recruitment_integration_base.hr_job_post_view_kanban_search
msgid "Contact Method"
msgstr ""

#. module: hr_recruitment_integration_base
#. odoo-python
#: code:addons/hr_recruitment_integration_base/models/hr_job_post.py:0
msgid "Contact Method:"
msgstr ""

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job_post__apply_vector
msgid "Contact Point"
msgstr ""

#. module: hr_recruitment_integration_base
#. odoo-python
#: code:addons/hr_recruitment_integration_base/models/hr_job_post.py:0
msgid "Contact Point:"
msgstr ""

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job_post__create_uid
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_recruitment_platform__create_uid
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_recruitment_post_job_wizard__create_uid
msgid "Created by"
msgstr "Створив"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job_post__create_date
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_recruitment_platform__create_date
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_recruitment_post_job_wizard__create_date
msgid "Created on"
msgstr "Створено"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job__currency_id
msgid "Currency"
msgstr "Валюта"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_recruitment_post_job_wizard__api_data
msgid "Data"
msgstr "Дані"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields.selection,name:hr_recruitment_integration_base.selection__hr_job__payment_interval__daily
msgid "Day"
msgstr "День"

#. module: hr_recruitment_integration_base
#. odoo-javascript
#: code:addons/hr_recruitment_integration_base/static/src/views/form/job_post_no_save_controller.js:0
msgid "Delete"
msgstr "Видалити"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields.selection,name:hr_recruitment_integration_base.selection__hr_job_post__status__deleted
msgid "Deleted"
msgstr "Видалено"

#. module: hr_recruitment_integration_base
#: model_terms:ir.ui.view,arch_db:hr_recruitment_integration_base.hr_job_post_view_form
#: model_terms:ir.ui.view,arch_db:hr_recruitment_integration_base.hr_recruitment_post_job_wizard_view_form
msgid "Description"
msgstr "Опис"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job_post__display_name
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_recruitment_platform__display_name
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_recruitment_post_job_wizard__display_name
msgid "Display Name"
msgstr "Назва для відображення"

#. module: hr_recruitment_integration_base
#: model:ir.actions.server,name:hr_recruitment_integration_base.hr_job_post_double_check_action
msgid "Double Check Job Post"
msgstr ""

#. module: hr_recruitment_integration_base
#. odoo-javascript
#: code:addons/hr_recruitment_integration_base/static/src/views/form/job_post_no_save_controller.js:0
msgid "Duplicate"
msgstr "Дублювати"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_recruitment_post_job_wizard__job_apply_mail
#: model:ir.model.fields.selection,name:hr_recruitment_integration_base.selection__hr_job_post__apply_method__email
msgid "Email"
msgstr "Ел. пошта"

#. module: hr_recruitment_integration_base
#: model_terms:ir.ui.view,arch_db:hr_recruitment_integration_base.hr_job_post_view_kanban_search
msgid "Expiration date"
msgstr "Термін дії"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields.selection,name:hr_recruitment_integration_base.selection__hr_job_post__status__expired
#: model_terms:ir.ui.view,arch_db:hr_recruitment_integration_base.hr_job_post_view_kanban_search
msgid "Expired"
msgstr "Протерміновано"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields.selection,name:hr_recruitment_integration_base.selection__hr_job_post__status__failure
msgid "Failure"
msgstr "Невдача"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job_post__message_follower_ids
msgid "Followers"
msgstr "Підписники"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job_post__message_partner_ids
msgid "Followers (Partners)"
msgstr "Підписники (Партнери)"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,help:hr_recruitment_integration_base.field_hr_job_post__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Іконка з чудовим шрифтом, напр. fa-tasks"

#. module: hr_recruitment_integration_base
#: model_terms:ir.ui.view,arch_db:hr_recruitment_integration_base.hr_job_post_view_kanban_search
msgid "Group By"
msgstr "Групувати за"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job_post__has_message
msgid "Has Message"
msgstr "Є повідомлення"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields.selection,name:hr_recruitment_integration_base.selection__hr_job__payment_interval__hourly
msgid "Hour"
msgstr "Година"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job_post__id
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_recruitment_platform__id
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_recruitment_post_job_wizard__id
msgid "ID"
msgstr "ID"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job_post__activity_exception_icon
msgid "Icon"
msgstr "Значок"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,help:hr_recruitment_integration_base.field_hr_job_post__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Іконка для визначення виключення дії."

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,help:hr_recruitment_integration_base.field_hr_job_post__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Якщо позначено, то нові повідомлення будуть потребувати вашої уваги."

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,help:hr_recruitment_integration_base.field_hr_job_post__message_has_error
#: model:ir.model.fields,help:hr_recruitment_integration_base.field_hr_job_post__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Якщо позначено, деякі повідомлення мають помилку доставки."

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_recruitment_platform__image_1920
msgid "Image"
msgstr "Зображення"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_recruitment_platform__image_1024
msgid "Image 1024"
msgstr "Зображення 1024"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_recruitment_platform__image_128
msgid "Image 128"
msgstr "Зображення 128"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_recruitment_platform__image_256
msgid "Image 256"
msgstr "Зображення 256"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_recruitment_platform__image_512
msgid "Image 512"
msgstr "Зображення 512"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job_post__message_is_follower
msgid "Is Follower"
msgstr "Стежить"

#. module: hr_recruitment_integration_base
#: model_terms:ir.ui.view,arch_db:hr_recruitment_integration_base.hr_job_post_view_kanban_search
msgid "Issue"
msgstr "Проблема"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job_post__job_id
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_recruitment_post_job_wizard__job_id
#: model_terms:ir.ui.view,arch_db:hr_recruitment_integration_base.hr_job_post_view_kanban_search
msgid "Job"
msgstr "Вакансія"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_recruitment_post_job_wizard__platform_ids
#: model_terms:ir.ui.view,arch_db:hr_recruitment_integration_base.view_hr_job_form
msgid "Job Board"
msgstr ""

#. module: hr_recruitment_integration_base
#: model_terms:ir.ui.view,arch_db:hr_recruitment_integration_base.view_hr_job_kanban
msgid "Job Board Posts"
msgstr ""

#. module: hr_recruitment_integration_base
#: model_terms:ir.ui.view,arch_db:hr_recruitment_integration_base.view_hr_job_form
msgid "Job Boards"
msgstr ""

#. module: hr_recruitment_integration_base
#: model:ir.actions.act_window,name:hr_recruitment_integration_base.action_open_hr_job_post
#: model:ir.ui.menu,name:hr_recruitment_integration_base.menu_hr_job_boards
msgid "Job Boards Posts"
msgstr ""

#. module: hr_recruitment_integration_base
#: model:ir.model,name:hr_recruitment_integration_base.model_hr_job
msgid "Job Position"
msgstr "Посада"

#. module: hr_recruitment_integration_base
#: model:ir.model,name:hr_recruitment_integration_base.model_hr_job_post
msgid "Job Post"
msgstr ""

#. module: hr_recruitment_integration_base
#. odoo-python
#: code:addons/hr_recruitment_integration_base/models/hr_job_post.py:0
msgid "Job Post on %(platform)s has been %(mode)s"
msgstr ""

#. module: hr_recruitment_integration_base
#. odoo-python
#: code:addons/hr_recruitment_integration_base/models/hr_job_post.py:0
msgid "Job Post on %(platform)s has been modified"
msgstr ""

#. module: hr_recruitment_integration_base
#. odoo-python
#: code:addons/hr_recruitment_integration_base/wizard/hr_recruitment_post.py:0
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job__job_post_ids
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_recruitment_post_job_wizard__post_ids
msgid "Job Posts"
msgstr ""

#. module: hr_recruitment_integration_base
#. odoo-python
#: code:addons/hr_recruitment_integration_base/models/hr_job_post.py:0
msgid "Job:"
msgstr ""

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job_post__write_uid
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_recruitment_platform__write_uid
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_recruitment_post_job_wizard__write_uid
msgid "Last Updated by"
msgstr "Востаннє оновив"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job_post__write_date
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_recruitment_platform__write_date
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_recruitment_post_job_wizard__write_date
msgid "Last Updated on"
msgstr "Останнє оновлення"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job__salary_max
msgid "Maximum Salary"
msgstr ""

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job_post__message_has_error
msgid "Message Delivery error"
msgstr "Помилка доставлення повідомлення"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job_post__message_ids
msgid "Messages"
msgstr "Повідомлення"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job__salary_min
msgid "Minimum Salary"
msgstr ""

#. module: hr_recruitment_integration_base
#: model:ir.model.fields.selection,name:hr_recruitment_integration_base.selection__hr_job__payment_interval__monthly
msgid "Month"
msgstr "Місяць"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job_post__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Дедлайн моєї дії"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_recruitment_platform__name
msgid "Name"
msgstr "Назва"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job_post__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Наступна подія календаря дій"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job_post__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Дедлайн наступної дії"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job_post__activity_summary
msgid "Next Activity Summary"
msgstr "Підсумок наступної дії"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job_post__activity_type_id
msgid "Next Activity Type"
msgstr "Тип наступної дії"

#. module: hr_recruitment_integration_base
#. odoo-python
#: code:addons/hr_recruitment_integration_base/models/hr_recruitment_platform.py:0
msgid "No API call defined for this platform please contact the administrator"
msgstr ""

#. module: hr_recruitment_integration_base
#. odoo-python
#: code:addons/hr_recruitment_integration_base/models/hr_job_post.py:0
#: model_terms:ir.ui.view,arch_db:hr_recruitment_integration_base.hr_job_post_view_form
#: model_terms:ir.ui.view,arch_db:hr_recruitment_integration_base.hr_recruitment_post_job_wizard_view_form
msgid "No Limit"
msgstr "Необмежено"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job_post__message_needaction_counter
msgid "Number of Actions"
msgstr "Кількість дій"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job__job_post_count
msgid "Number of Job Posts"
msgstr ""

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job_post__message_has_error_counter
msgid "Number of errors"
msgstr "Кількість помилок"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,help:hr_recruitment_integration_base.field_hr_job_post__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Кількість повідомлень, які вимагають дії"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,help:hr_recruitment_integration_base.field_hr_job_post__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Кількість повідомлень з помилковою дставкою"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields.selection,name:hr_recruitment_integration_base.selection__hr_job_post__status__pending
msgid "Pending"
msgstr "В очікуванні"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job_post__platform_id
#: model_terms:ir.ui.view,arch_db:hr_recruitment_integration_base.hr_job_post_view_kanban_search
msgid "Platform"
msgstr ""

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job_post__post_html
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_recruitment_post_job_wizard__post_html
#: model_terms:ir.ui.view,arch_db:hr_recruitment_integration_base.hr_recruitment_post_job_wizard_view_form
msgid "Post"
msgstr "Опублікувати"

#. module: hr_recruitment_integration_base
#: model:ir.model,name:hr_recruitment_integration_base.model_hr_recruitment_post_job_wizard
#: model_terms:ir.ui.view,arch_db:hr_recruitment_integration_base.hr_recruitment_post_job_wizard_view_form
msgid "Post Job"
msgstr ""

#. module: hr_recruitment_integration_base
#: model_terms:ir.ui.view,arch_db:hr_recruitment_integration_base.hr_job_post_view_form
#: model_terms:ir.ui.view,arch_db:hr_recruitment_integration_base.hr_job_post_view_list
msgid "Post Now"
msgstr "Опублікувати зараз"

#. module: hr_recruitment_integration_base
#: model_terms:ir.ui.view,arch_db:hr_recruitment_integration_base.view_hr_job_form
msgid "Posts"
msgstr "Публікації"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_recruitment_platform__price_to_delete
msgid "Price to Delete"
msgstr ""

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_recruitment_platform__price_to_get
msgid "Price to Get"
msgstr ""

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_recruitment_platform__price_to_publish
msgid "Price to Publish"
msgstr ""

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_recruitment_platform__price_to_update
msgid "Price to Update"
msgstr ""

#. module: hr_recruitment_integration_base
#: model_terms:ir.ui.view,arch_db:hr_recruitment_integration_base.view_hr_job_form
msgid "Publish on Job Board"
msgstr ""

#. module: hr_recruitment_integration_base
#. odoo-python
#: code:addons/hr_recruitment_integration_base/models/hr_job.py:0
msgid "Publish on a Job Board"
msgstr ""

#. module: hr_recruitment_integration_base
#: model_terms:ir.ui.view,arch_db:hr_recruitment_integration_base.hr_job_post_view_kanban_search
msgid "Published"
msgstr "Опубліковано"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job_post__rating_ids
msgid "Ratings"
msgstr "Оцінювання"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job_post__recruiter_id
msgid "Recruiter"
msgstr "Рекрутер"

#. module: hr_recruitment_integration_base
#: model:ir.model,name:hr_recruitment_integration_base.model_hr_recruitment_platform
msgid "Recruitment Platform"
msgstr ""

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job_post__activity_user_id
msgid "Responsible User"
msgstr "Відповідальний користувач"

#. module: hr_recruitment_integration_base
#. odoo-python
#: code:addons/hr_recruitment_integration_base/models/hr_job_post.py:0
msgid "Reuse Job Post"
msgstr ""

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job_post__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Помилка доставки SMS"

#. module: hr_recruitment_integration_base
#: model_terms:ir.ui.view,arch_db:hr_recruitment_integration_base.view_hr_job_form
msgid "Salary Range"
msgstr ""

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job__payment_interval
msgid "Salary Time Unit"
msgstr ""

#. module: hr_recruitment_integration_base
#: model:ir.model.fields.selection,name:hr_recruitment_integration_base.selection__hr_recruitment_post_job_wizard__apply_method__email
msgid "Send an Email"
msgstr ""

#. module: hr_recruitment_integration_base
#: model_terms:ir.ui.view,arch_db:hr_recruitment_integration_base.hr_job_post_view_list
msgid "Start Date"
msgstr "Початкова дата"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job_post__status
#: model_terms:ir.ui.view,arch_db:hr_recruitment_integration_base.hr_job_post_view_kanban_search
msgid "Status"
msgstr "Статус"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job_post__status_message
msgid "Status Message"
msgstr "Повідомлення про Статус"

#. module: hr_recruitment_integration_base
#. odoo-python
#: code:addons/hr_recruitment_integration_base/models/hr_job_post.py:0
msgid "Status Message:"
msgstr ""

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,help:hr_recruitment_integration_base.field_hr_job_post__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Етап заснований на діях\n"
"Протерміновано: термін виконання вже минув\n"
"Сьогодні: дата дії сьогодні\n"
"Заплановано: майбутні дії."

#. module: hr_recruitment_integration_base
#. odoo-python
#: code:addons/hr_recruitment_integration_base/models/hr_job_post.py:0
msgid "Status:"
msgstr "Статус:"

#. module: hr_recruitment_integration_base
#: model_terms:ir.ui.view,arch_db:hr_recruitment_integration_base.hr_job_post_view_list
msgid "Stop Campaigns"
msgstr ""

#. module: hr_recruitment_integration_base
#: model_terms:ir.ui.view,arch_db:hr_recruitment_integration_base.hr_job_post_view_list
msgid "Stop Date"
msgstr "Дата зупинки"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields.selection,name:hr_recruitment_integration_base.selection__hr_job_post__status__success
msgid "Success"
msgstr "Успіх"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,help:hr_recruitment_integration_base.field_hr_job_post__recruiter_id
msgid ""
"The Recruiter will be the default value for all Applicants in this job"
"             position. The Recruiter is automatically added to all meetings "
"with the Applicant."
msgstr ""

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,help:hr_recruitment_integration_base.field_hr_job_post__campaign_end_date
#: model:ir.model.fields,help:hr_recruitment_integration_base.field_hr_recruitment_post_job_wizard__campaign_end_date
msgid ""
"The date when the campaign will end. If not set, the campaign will run "
"indefinitely or to the maximum allowed by a platform."
msgstr ""

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,help:hr_recruitment_integration_base.field_hr_job_post__campaign_start_date
#: model:ir.model.fields,help:hr_recruitment_integration_base.field_hr_recruitment_post_job_wizard__campaign_start_date
msgid "The date when the campaign will start."
msgstr ""

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,help:hr_recruitment_integration_base.field_hr_job_post__apply_vector
msgid "The email address, phone number, url to send applications to."
msgstr ""

#. module: hr_recruitment_integration_base
#. odoo-python
#: code:addons/hr_recruitment_integration_base/models/hr_job_post.py:0
msgid ""
"This action will update the job post on the platform. This action will cost "
"%(price)s credits. Do you want to continue?"
msgstr ""

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,help:hr_recruitment_integration_base.field_hr_job_post__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Тип дії виключення на записі."

#. module: hr_recruitment_integration_base
#. odoo-python
#: code:addons/hr_recruitment_integration_base/models/hr_job_post.py:0
#: model_terms:ir.ui.view,arch_db:hr_recruitment_integration_base.hr_job_post_view_form
msgid "Update"
msgstr "Оновити"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields.selection,name:hr_recruitment_integration_base.selection__hr_job_post__status__warning
msgid "Warning"
msgstr "Попередження"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_recruitment_platform__website
msgid "Website"
msgstr "Веб-сайт"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job_post__website_message_ids
msgid "Website Messages"
msgstr "Повідомлення з веб-сайту"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,help:hr_recruitment_integration_base.field_hr_job_post__website_message_ids
msgid "Website communication history"
msgstr "Історія бесіди на сайті"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields.selection,name:hr_recruitment_integration_base.selection__hr_job__payment_interval__weekly
msgid "Week"
msgstr "Тиждень"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job__schedule_id
msgid "Working Schedule"
msgstr "Графік робочого часу"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields.selection,name:hr_recruitment_integration_base.selection__hr_job__payment_interval__yearly
msgid "Year"
msgstr "Рік"

#. module: hr_recruitment_integration_base
#. odoo-python
#: code:addons/hr_recruitment_integration_base/models/hr_job_post.py:0
msgid "created"
msgstr ""

#. module: hr_recruitment_integration_base
#. odoo-python
#: code:addons/hr_recruitment_integration_base/models/hr_recruitment_platform.py:0
msgid "failure"
msgstr ""

#. module: hr_recruitment_integration_base
#: model_terms:ir.ui.view,arch_db:hr_recruitment_integration_base.view_hr_job_form
msgid "per"
msgstr "на"

#. module: hr_recruitment_integration_base
#: model_terms:ir.ui.view,arch_db:hr_recruitment_integration_base.hr_job_post_view_kanban
msgid "plateform icon"
msgstr ""

#. module: hr_recruitment_integration_base
#. odoo-python
#: code:addons/hr_recruitment_integration_base/wizard/hr_recruitment_post.py:0
msgid "updated"
msgstr ""
