# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_recruitment_integration_base
# 
# Translators:
# <PERSON><PERSON> (Quartile) <<EMAIL>>, 2024
# Wil Odoo, 2024
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:27+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON><PERSON>, 2025\n"
"Language-Team: Japanese (https://app.transifex.com/odoo/teams/41243/ja/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ja\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: hr_recruitment_integration_base
#. odoo-python
#: code:addons/hr_recruitment_integration_base/models/hr_job_post.py:0
msgid "%(job)s on %(platform)s"
msgstr "%(job)s , %(platform)s"

#. module: hr_recruitment_integration_base
#: model_terms:ir.ui.view,arch_db:hr_recruitment_integration_base.hr_job_post_view_kanban
msgid "<span class=\"px-2\">-</span>"
msgstr "<span class=\"px-2\">-</span>"

#. module: hr_recruitment_integration_base
#: model_terms:ir.ui.view,arch_db:hr_recruitment_integration_base.hr_job_post_view_kanban
msgid "<span class=\"text-muted\" invisible=\"campaign_end_date\">No limit</span>"
msgstr "<span class=\"text-muted\" invisible=\"campaign_end_date\">制限なし</span>"

#. module: hr_recruitment_integration_base
#: model_terms:ir.ui.view,arch_db:hr_recruitment_integration_base.hr_job_post_view_kanban
msgid "<strong class=\"o_kanban_label_width\">From:</strong>"
msgstr "<strong class=\"o_kanban_label_width\">開始:</strong>"

#. module: hr_recruitment_integration_base
#: model_terms:ir.ui.view,arch_db:hr_recruitment_integration_base.hr_job_post_view_kanban
msgid "<strong class=\"o_kanban_label_width\">To:</strong>"
msgstr "<strong class=\"o_kanban_label_width\">終了:</strong>"

#. module: hr_recruitment_integration_base
#: model_terms:ir.ui.view,arch_db:hr_recruitment_integration_base.hr_job_post_view_form
msgid "<strong class=\"px-2\">to</strong>"
msgstr "<strong class=\"px-2\">終了</strong>"

#. module: hr_recruitment_integration_base
#: model_terms:ir.ui.view,arch_db:hr_recruitment_integration_base.hr_recruitment_post_job_wizard_view_form
msgid "<strong>From</strong>"
msgstr "<strong>移動元</strong>"

#. module: hr_recruitment_integration_base
#: model_terms:ir.ui.view,arch_db:hr_recruitment_integration_base.hr_recruitment_post_job_wizard_view_form
msgid "<strong>to</strong>"
msgstr "<strong>から以下へ：</strong>"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job_post__api_data
msgid "API Data"
msgstr "APIデータ"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job_post__message_needaction
msgid "Action Needed"
msgstr "要アクション"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job_post__activity_ids
msgid "Activities"
msgstr "活動"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job_post__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "例外活動文字装飾"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job_post__activity_state
msgid "Activity State"
msgstr "活動状態"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job_post__activity_type_icon
msgid "Activity Type Icon"
msgstr "活動種別アイコン"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_recruitment_post_job_wizard__apply_method
msgid "Apply Method"
msgstr "応募方法"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job_post__message_attachment_count
msgid "Attachment Count"
msgstr "添付数"

#. module: hr_recruitment_integration_base
#: model:ir.actions.server,name:hr_recruitment_integration_base.job_board_campaign_manager_start_ir_actions_server
msgid "Automatic Job Posting on job boards"
msgstr "求人情報サイトへの求人自動掲載"

#. module: hr_recruitment_integration_base
#: model:ir.actions.server,name:hr_recruitment_integration_base.job_board_campaign_manager_stop_ir_actions_server
msgid "Automatic job posting deleting on job boards"
msgstr "求人情報サイトへの求人自動削除"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_recruitment_platform__avatar_1920
msgid "Avatar"
msgstr "アバター"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_recruitment_platform__avatar_1024
msgid "Avatar 1024"
msgstr "アバター 1024"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_recruitment_platform__avatar_128
msgid "Avatar 128"
msgstr "アバター 128"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_recruitment_platform__avatar_256
msgid "Avatar 256"
msgstr "アバター 256"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_recruitment_platform__avatar_512
msgid "Avatar 512"
msgstr "アバター 512"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields.selection,name:hr_recruitment_integration_base.selection__hr_job__payment_interval__biweekly
msgid "Bi-Week"
msgstr "隔週"

#. module: hr_recruitment_integration_base
#: model_terms:ir.ui.view,arch_db:hr_recruitment_integration_base.hr_job_post_view_form
msgid "Campaign Duration"
msgstr "キャンペーン期間"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job_post__campaign_end_date
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_recruitment_post_job_wizard__campaign_end_date
msgid "Campaign End Date"
msgstr "キャンペーン終了日"

#. module: hr_recruitment_integration_base
#. odoo-python
#: code:addons/hr_recruitment_integration_base/models/hr_job_post.py:0
msgid "Campaign End Date:"
msgstr "キャンペーン終了日:"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job_post__campaign_start_date
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_recruitment_post_job_wizard__campaign_start_date
msgid "Campaign Start Date"
msgstr "キャンペーン開始日"

#. module: hr_recruitment_integration_base
#. odoo-python
#: code:addons/hr_recruitment_integration_base/models/hr_job_post.py:0
msgid "Campaign Start Date:"
msgstr "キャンペーン開始日:"

#. module: hr_recruitment_integration_base
#. odoo-python
#: code:addons/hr_recruitment_integration_base/wizard/hr_recruitment_post.py:0
msgid "Campaign start date can't be after campaign end date"
msgstr "キャンペーン開始日は終了日の前にして下さい。"

#. module: hr_recruitment_integration_base
#. odoo-python
#: code:addons/hr_recruitment_integration_base/wizard/hr_recruitment_post.py:0
msgid "Campaign will start on %(start_date)s"
msgstr " %(start_date)sにキャンペーンが始まります"

#. module: hr_recruitment_integration_base
#. odoo-python
#: code:addons/hr_recruitment_integration_base/wizard/hr_recruitment_post.py:0
msgid "Can't postpone posts that are already posted"
msgstr "既に投稿済の投稿を延期することはできません"

#. module: hr_recruitment_integration_base
#: model_terms:ir.ui.view,arch_db:hr_recruitment_integration_base.hr_recruitment_post_job_wizard_view_form
msgid "Cancel"
msgstr "キャンセル"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job_post__company_id
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_recruitment_post_job_wizard__company_id
msgid "Company"
msgstr "会社"

#. module: hr_recruitment_integration_base
#. odoo-python
#: code:addons/hr_recruitment_integration_base/models/hr_job_post.py:0
msgid "Company:"
msgstr "会社:"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job_post__apply_method
#: model_terms:ir.ui.view,arch_db:hr_recruitment_integration_base.hr_job_post_view_kanban_search
msgid "Contact Method"
msgstr "連絡方法"

#. module: hr_recruitment_integration_base
#. odoo-python
#: code:addons/hr_recruitment_integration_base/models/hr_job_post.py:0
msgid "Contact Method:"
msgstr "連絡方法:"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job_post__apply_vector
msgid "Contact Point"
msgstr "連絡先"

#. module: hr_recruitment_integration_base
#. odoo-python
#: code:addons/hr_recruitment_integration_base/models/hr_job_post.py:0
msgid "Contact Point:"
msgstr "連絡先:"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job_post__create_uid
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_recruitment_platform__create_uid
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_recruitment_post_job_wizard__create_uid
msgid "Created by"
msgstr "作成者"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job_post__create_date
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_recruitment_platform__create_date
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_recruitment_post_job_wizard__create_date
msgid "Created on"
msgstr "作成日"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job__currency_id
msgid "Currency"
msgstr "通貨"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_recruitment_post_job_wizard__api_data
msgid "Data"
msgstr "データ"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields.selection,name:hr_recruitment_integration_base.selection__hr_job__payment_interval__daily
msgid "Day"
msgstr "日"

#. module: hr_recruitment_integration_base
#. odoo-javascript
#: code:addons/hr_recruitment_integration_base/static/src/views/form/job_post_no_save_controller.js:0
msgid "Delete"
msgstr "削除"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields.selection,name:hr_recruitment_integration_base.selection__hr_job_post__status__deleted
msgid "Deleted"
msgstr "削除済"

#. module: hr_recruitment_integration_base
#: model_terms:ir.ui.view,arch_db:hr_recruitment_integration_base.hr_job_post_view_form
#: model_terms:ir.ui.view,arch_db:hr_recruitment_integration_base.hr_recruitment_post_job_wizard_view_form
msgid "Description"
msgstr "説明"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job_post__display_name
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_recruitment_platform__display_name
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_recruitment_post_job_wizard__display_name
msgid "Display Name"
msgstr "表示名"

#. module: hr_recruitment_integration_base
#: model:ir.actions.server,name:hr_recruitment_integration_base.hr_job_post_double_check_action
msgid "Double Check Job Post"
msgstr "求人情報のダブルチェック"

#. module: hr_recruitment_integration_base
#. odoo-javascript
#: code:addons/hr_recruitment_integration_base/static/src/views/form/job_post_no_save_controller.js:0
msgid "Duplicate"
msgstr "複製"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_recruitment_post_job_wizard__job_apply_mail
#: model:ir.model.fields.selection,name:hr_recruitment_integration_base.selection__hr_job_post__apply_method__email
msgid "Email"
msgstr "メール"

#. module: hr_recruitment_integration_base
#: model_terms:ir.ui.view,arch_db:hr_recruitment_integration_base.hr_job_post_view_kanban_search
msgid "Expiration date"
msgstr "有効期限日"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields.selection,name:hr_recruitment_integration_base.selection__hr_job_post__status__expired
#: model_terms:ir.ui.view,arch_db:hr_recruitment_integration_base.hr_job_post_view_kanban_search
msgid "Expired"
msgstr "契約期間終了済"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields.selection,name:hr_recruitment_integration_base.selection__hr_job_post__status__failure
msgid "Failure"
msgstr "失敗"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job_post__message_follower_ids
msgid "Followers"
msgstr "フォロワー"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job_post__message_partner_ids
msgid "Followers (Partners)"
msgstr "フォロワー (取引先)"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,help:hr_recruitment_integration_base.field_hr_job_post__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Font awesomeのアイコン 例. fa-tasks"

#. module: hr_recruitment_integration_base
#: model_terms:ir.ui.view,arch_db:hr_recruitment_integration_base.hr_job_post_view_kanban_search
msgid "Group By"
msgstr "グループ化"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job_post__has_message
msgid "Has Message"
msgstr "メッセージあり"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields.selection,name:hr_recruitment_integration_base.selection__hr_job__payment_interval__hourly
msgid "Hour"
msgstr "時間"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job_post__id
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_recruitment_platform__id
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_recruitment_post_job_wizard__id
msgid "ID"
msgstr "ID"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job_post__activity_exception_icon
msgid "Icon"
msgstr "アイコン"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,help:hr_recruitment_integration_base.field_hr_job_post__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "例外活動を示すアイコン"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,help:hr_recruitment_integration_base.field_hr_job_post__message_needaction
msgid "If checked, new messages require your attention."
msgstr "チェックした場合は、新しいメッセージに注意が必要です。"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,help:hr_recruitment_integration_base.field_hr_job_post__message_has_error
#: model:ir.model.fields,help:hr_recruitment_integration_base.field_hr_job_post__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "チェックした場合は、一部のメッセージに配信エラーが発生されました。"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_recruitment_platform__image_1920
msgid "Image"
msgstr "画像"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_recruitment_platform__image_1024
msgid "Image 1024"
msgstr "画像1024"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_recruitment_platform__image_128
msgid "Image 128"
msgstr "画像128"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_recruitment_platform__image_256
msgid "Image 256"
msgstr "画像256"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_recruitment_platform__image_512
msgid "Image 512"
msgstr "画像512"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job_post__message_is_follower
msgid "Is Follower"
msgstr "フォロー中　"

#. module: hr_recruitment_integration_base
#: model_terms:ir.ui.view,arch_db:hr_recruitment_integration_base.hr_job_post_view_kanban_search
msgid "Issue"
msgstr "課題"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job_post__job_id
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_recruitment_post_job_wizard__job_id
#: model_terms:ir.ui.view,arch_db:hr_recruitment_integration_base.hr_job_post_view_kanban_search
msgid "Job"
msgstr "職務"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_recruitment_post_job_wizard__platform_ids
#: model_terms:ir.ui.view,arch_db:hr_recruitment_integration_base.view_hr_job_form
msgid "Job Board"
msgstr "求人ボード"

#. module: hr_recruitment_integration_base
#: model_terms:ir.ui.view,arch_db:hr_recruitment_integration_base.view_hr_job_kanban
msgid "Job Board Posts"
msgstr "求人情報サイト投稿"

#. module: hr_recruitment_integration_base
#: model_terms:ir.ui.view,arch_db:hr_recruitment_integration_base.view_hr_job_form
msgid "Job Boards"
msgstr "求人ボード"

#. module: hr_recruitment_integration_base
#: model:ir.actions.act_window,name:hr_recruitment_integration_base.action_open_hr_job_post
#: model:ir.ui.menu,name:hr_recruitment_integration_base.menu_hr_job_boards
msgid "Job Boards Posts"
msgstr "求人情報サイト投稿"

#. module: hr_recruitment_integration_base
#: model:ir.model,name:hr_recruitment_integration_base.model_hr_job
msgid "Job Position"
msgstr "職位"

#. module: hr_recruitment_integration_base
#: model:ir.model,name:hr_recruitment_integration_base.model_hr_job_post
msgid "Job Post"
msgstr "役職"

#. module: hr_recruitment_integration_base
#. odoo-python
#: code:addons/hr_recruitment_integration_base/models/hr_job_post.py:0
msgid "Job Post on %(platform)s has been %(mode)s"
msgstr "%(platform)s の求人情報が %(mode)sに変更されました"

#. module: hr_recruitment_integration_base
#. odoo-python
#: code:addons/hr_recruitment_integration_base/models/hr_job_post.py:0
msgid "Job Post on %(platform)s has been modified"
msgstr " %(platform)s の求人情報が変更されました"

#. module: hr_recruitment_integration_base
#. odoo-python
#: code:addons/hr_recruitment_integration_base/wizard/hr_recruitment_post.py:0
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job__job_post_ids
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_recruitment_post_job_wizard__post_ids
msgid "Job Posts"
msgstr "求人情報"

#. module: hr_recruitment_integration_base
#. odoo-python
#: code:addons/hr_recruitment_integration_base/models/hr_job_post.py:0
msgid "Job:"
msgstr "求人情報"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job_post__write_uid
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_recruitment_platform__write_uid
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_recruitment_post_job_wizard__write_uid
msgid "Last Updated by"
msgstr "最終更新者"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job_post__write_date
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_recruitment_platform__write_date
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_recruitment_post_job_wizard__write_date
msgid "Last Updated on"
msgstr "最終更新日"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job__salary_max
msgid "Maximum Salary"
msgstr "最高給与"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job_post__message_has_error
msgid "Message Delivery error"
msgstr "メッセージ配信エラー"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job_post__message_ids
msgid "Messages"
msgstr "メッセージ"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job__salary_min
msgid "Minimum Salary"
msgstr "最小給与"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields.selection,name:hr_recruitment_integration_base.selection__hr_job__payment_interval__monthly
msgid "Month"
msgstr "月"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job_post__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "活動期限"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_recruitment_platform__name
msgid "Name"
msgstr "名称"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job_post__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "次の活動カレンダーイベント"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job_post__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "次の活動期限"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job_post__activity_summary
msgid "Next Activity Summary"
msgstr "次の活動概要"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job_post__activity_type_id
msgid "Next Activity Type"
msgstr "次の活動タイプ"

#. module: hr_recruitment_integration_base
#. odoo-python
#: code:addons/hr_recruitment_integration_base/models/hr_recruitment_platform.py:0
msgid "No API call defined for this platform please contact the administrator"
msgstr "このプラットフォームにはAPIコールが定義されていません。管理者までご連絡下さい。"

#. module: hr_recruitment_integration_base
#. odoo-python
#: code:addons/hr_recruitment_integration_base/models/hr_job_post.py:0
#: model_terms:ir.ui.view,arch_db:hr_recruitment_integration_base.hr_job_post_view_form
#: model_terms:ir.ui.view,arch_db:hr_recruitment_integration_base.hr_recruitment_post_job_wizard_view_form
msgid "No Limit"
msgstr "制限なし"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job_post__message_needaction_counter
msgid "Number of Actions"
msgstr "アクション数"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job__job_post_count
msgid "Number of Job Posts"
msgstr "求人情報の数"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job_post__message_has_error_counter
msgid "Number of errors"
msgstr "エラー数"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,help:hr_recruitment_integration_base.field_hr_job_post__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "アクションを必要とするメッセージの数"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,help:hr_recruitment_integration_base.field_hr_job_post__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "配信エラーが発生されたメッセージ数"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields.selection,name:hr_recruitment_integration_base.selection__hr_job_post__status__pending
msgid "Pending"
msgstr "保留"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job_post__platform_id
#: model_terms:ir.ui.view,arch_db:hr_recruitment_integration_base.hr_job_post_view_kanban_search
msgid "Platform"
msgstr "プラットフォーム"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job_post__post_html
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_recruitment_post_job_wizard__post_html
#: model_terms:ir.ui.view,arch_db:hr_recruitment_integration_base.hr_recruitment_post_job_wizard_view_form
msgid "Post"
msgstr "記帳"

#. module: hr_recruitment_integration_base
#: model:ir.model,name:hr_recruitment_integration_base.model_hr_recruitment_post_job_wizard
#: model_terms:ir.ui.view,arch_db:hr_recruitment_integration_base.hr_recruitment_post_job_wizard_view_form
msgid "Post Job"
msgstr "求人を投稿"

#. module: hr_recruitment_integration_base
#: model_terms:ir.ui.view,arch_db:hr_recruitment_integration_base.hr_job_post_view_form
#: model_terms:ir.ui.view,arch_db:hr_recruitment_integration_base.hr_job_post_view_list
msgid "Post Now"
msgstr "今すぐ投稿"

#. module: hr_recruitment_integration_base
#: model_terms:ir.ui.view,arch_db:hr_recruitment_integration_base.view_hr_job_form
msgid "Posts"
msgstr "投稿"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_recruitment_platform__price_to_delete
msgid "Price to Delete"
msgstr "削除価格"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_recruitment_platform__price_to_get
msgid "Price to Get"
msgstr "取得価格"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_recruitment_platform__price_to_publish
msgid "Price to Publish"
msgstr "公開価格"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_recruitment_platform__price_to_update
msgid "Price to Update"
msgstr "更新価格"

#. module: hr_recruitment_integration_base
#: model_terms:ir.ui.view,arch_db:hr_recruitment_integration_base.view_hr_job_form
msgid "Publish on Job Board"
msgstr "求人ボードに公開"

#. module: hr_recruitment_integration_base
#. odoo-python
#: code:addons/hr_recruitment_integration_base/models/hr_job.py:0
msgid "Publish on a Job Board"
msgstr "求人ボードに公開"

#. module: hr_recruitment_integration_base
#: model_terms:ir.ui.view,arch_db:hr_recruitment_integration_base.hr_job_post_view_kanban_search
msgid "Published"
msgstr "公開済"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job_post__rating_ids
msgid "Ratings"
msgstr "評価"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job_post__recruiter_id
msgid "Recruiter"
msgstr "リクルーター"

#. module: hr_recruitment_integration_base
#: model:ir.model,name:hr_recruitment_integration_base.model_hr_recruitment_platform
msgid "Recruitment Platform"
msgstr "採用プラットフォーム"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job_post__activity_user_id
msgid "Responsible User"
msgstr "担当ユーザ"

#. module: hr_recruitment_integration_base
#. odoo-python
#: code:addons/hr_recruitment_integration_base/models/hr_job_post.py:0
msgid "Reuse Job Post"
msgstr "求人情報を再利用"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job_post__message_has_sms_error
msgid "SMS Delivery error"
msgstr "SMS配信エラー"

#. module: hr_recruitment_integration_base
#: model_terms:ir.ui.view,arch_db:hr_recruitment_integration_base.view_hr_job_form
msgid "Salary Range"
msgstr "給与幅"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job__payment_interval
msgid "Salary Time Unit"
msgstr "給与時間単位"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields.selection,name:hr_recruitment_integration_base.selection__hr_recruitment_post_job_wizard__apply_method__email
msgid "Send an Email"
msgstr "Eメール送付"

#. module: hr_recruitment_integration_base
#: model_terms:ir.ui.view,arch_db:hr_recruitment_integration_base.hr_job_post_view_list
msgid "Start Date"
msgstr "開始日"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job_post__status
#: model_terms:ir.ui.view,arch_db:hr_recruitment_integration_base.hr_job_post_view_kanban_search
msgid "Status"
msgstr "状態"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job_post__status_message
msgid "Status Message"
msgstr "ステータスメッセージ"

#. module: hr_recruitment_integration_base
#. odoo-python
#: code:addons/hr_recruitment_integration_base/models/hr_job_post.py:0
msgid "Status Message:"
msgstr "ステータスメッセージ:"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,help:hr_recruitment_integration_base.field_hr_job_post__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"活動に基づくステータス\n"
"遅延: 期限が既に過ぎています\n"
"今日: 活動日は今日です\n"
"予定: 将来の活動。"

#. module: hr_recruitment_integration_base
#. odoo-python
#: code:addons/hr_recruitment_integration_base/models/hr_job_post.py:0
msgid "Status:"
msgstr "ステータス:"

#. module: hr_recruitment_integration_base
#: model_terms:ir.ui.view,arch_db:hr_recruitment_integration_base.hr_job_post_view_list
msgid "Stop Campaigns"
msgstr "キャンペーンを中止する"

#. module: hr_recruitment_integration_base
#: model_terms:ir.ui.view,arch_db:hr_recruitment_integration_base.hr_job_post_view_list
msgid "Stop Date"
msgstr "停止日"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields.selection,name:hr_recruitment_integration_base.selection__hr_job_post__status__success
msgid "Success"
msgstr "成功"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,help:hr_recruitment_integration_base.field_hr_job_post__recruiter_id
msgid ""
"The Recruiter will be the default value for all Applicants in this job"
"             position. The Recruiter is automatically added to all meetings "
"with the Applicant."
msgstr ""
"採用担当者は、この求人ポジションの全ての応募者の採用担当者フィールドのデフォルト値となります。採用担当者は、応募者との全てのミーティングに自動的に追加されます。"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,help:hr_recruitment_integration_base.field_hr_job_post__campaign_end_date
#: model:ir.model.fields,help:hr_recruitment_integration_base.field_hr_recruitment_post_job_wizard__campaign_end_date
msgid ""
"The date when the campaign will end. If not set, the campaign will run "
"indefinitely or to the maximum allowed by a platform."
msgstr "キャンペーンが終了する日付です。設定されていない場合、キャンペーンは無期限、またはプラットフォームが許可する最大期間まで継続されます。"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,help:hr_recruitment_integration_base.field_hr_job_post__campaign_start_date
#: model:ir.model.fields,help:hr_recruitment_integration_base.field_hr_recruitment_post_job_wizard__campaign_start_date
msgid "The date when the campaign will start."
msgstr "キャンペーン開始の日付。"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,help:hr_recruitment_integration_base.field_hr_job_post__apply_vector
msgid "The email address, phone number, url to send applications to."
msgstr "メールアドレス、電話番号、応募書類を送信するURL。"

#. module: hr_recruitment_integration_base
#. odoo-python
#: code:addons/hr_recruitment_integration_base/models/hr_job_post.py:0
msgid ""
"This action will update the job post on the platform. This action will cost "
"%(price)s credits. Do you want to continue?"
msgstr ""
"このアクションにより、プラットフォーム上の求人情報が更新されます。 このアクションには %(price)s クレジットがかかります。続けますか?"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,help:hr_recruitment_integration_base.field_hr_job_post__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "記録上の例外活動の種類。"

#. module: hr_recruitment_integration_base
#. odoo-python
#: code:addons/hr_recruitment_integration_base/models/hr_job_post.py:0
#: model_terms:ir.ui.view,arch_db:hr_recruitment_integration_base.hr_job_post_view_form
msgid "Update"
msgstr "更新"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields.selection,name:hr_recruitment_integration_base.selection__hr_job_post__status__warning
msgid "Warning"
msgstr "警告"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_recruitment_platform__website
msgid "Website"
msgstr "ウェブサイト"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job_post__website_message_ids
msgid "Website Messages"
msgstr "ウェブサイトメッセージ"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,help:hr_recruitment_integration_base.field_hr_job_post__website_message_ids
msgid "Website communication history"
msgstr "ウェブサイト通信履歴"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields.selection,name:hr_recruitment_integration_base.selection__hr_job__payment_interval__weekly
msgid "Week"
msgstr "週"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job__schedule_id
msgid "Working Schedule"
msgstr "勤務スケジュール"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields.selection,name:hr_recruitment_integration_base.selection__hr_job__payment_interval__yearly
msgid "Year"
msgstr "年"

#. module: hr_recruitment_integration_base
#. odoo-python
#: code:addons/hr_recruitment_integration_base/models/hr_job_post.py:0
msgid "created"
msgstr "作成済"

#. module: hr_recruitment_integration_base
#. odoo-python
#: code:addons/hr_recruitment_integration_base/models/hr_recruitment_platform.py:0
msgid "failure"
msgstr "失敗"

#. module: hr_recruitment_integration_base
#: model_terms:ir.ui.view,arch_db:hr_recruitment_integration_base.view_hr_job_form
msgid "per"
msgstr "／"

#. module: hr_recruitment_integration_base
#: model_terms:ir.ui.view,arch_db:hr_recruitment_integration_base.hr_job_post_view_kanban
msgid "plateform icon"
msgstr "プラットフォームアイコン"

#. module: hr_recruitment_integration_base
#. odoo-python
#: code:addons/hr_recruitment_integration_base/wizard/hr_recruitment_post.py:0
msgid "updated"
msgstr "更新済"
