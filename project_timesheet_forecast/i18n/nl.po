# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* project_timesheet_forecast
# 
# Translators:
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:25+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Dutch (https://app.transifex.com/odoo/teams/41243/nl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: nl\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: project_timesheet_forecast
#: model_terms:ir.ui.view,arch_db:project_timesheet_forecast.project_forecast_view_form_inherit_project_timesheet_forecast
msgid "<span class=\"o_stat_text\">Recorded</span>"
msgstr "<span class=\"o_stat_text\">Geboekt</span>"

#. module: project_timesheet_forecast
#: model_terms:ir.ui.view,arch_db:project_timesheet_forecast.project_forecast_view_form_inherit_project_timesheet_forecast
msgid ""
"<span invisible=\"encode_uom_in_days\"> Hours</span>\n"
"                            <span invisible=\"not encode_uom_in_days\"> Days</span>"
msgstr ""
"<span invisible=\"encode_uom_in_days\"> Uren</span>\n"
"                            <span invisible=\"not encode_uom_in_days\"> Dagen</span>"

#. module: project_timesheet_forecast
#: model:ir.model.fields,field_description:project_timesheet_forecast.field_planning_analysis_report__allocated_hours_cost
msgid "Allocated Time Cost"
msgstr "Toegewezen tijdkosten"

#. module: project_timesheet_forecast
#: model:ir.model.fields,help:project_timesheet_forecast.field_planning_analysis_report__remaining_hours
msgid "Allocated time minus the effective time."
msgstr "Toegewezen uren min de werkelijke uren."

#. module: project_timesheet_forecast
#: model:ir.model.fields,field_description:project_timesheet_forecast.field_planning_slot__allow_timesheets
msgid "Allow timesheets"
msgstr "Urenstaten toestaan"

#. module: project_timesheet_forecast
#: model:ir.model,name:project_timesheet_forecast.model_account_analytic_line
msgid "Analytic Line"
msgstr "Analytische boeking"

#. module: project_timesheet_forecast
#: model:ir.model.fields,field_description:project_timesheet_forecast.field_project_timesheet_forecast_report_analysis__user_id
msgid "Assigned to"
msgstr "Toegewezen aan"

#. module: project_timesheet_forecast
#: model:ir.model.fields,field_description:project_timesheet_forecast.field_project_timesheet_forecast_report_analysis__company_id
msgid "Company"
msgstr "Bedrijf"

#. module: project_timesheet_forecast
#: model:ir.model.fields,field_description:project_timesheet_forecast.field_project_timesheet_forecast_report_analysis__entry_date
#: model_terms:ir.ui.view,arch_db:project_timesheet_forecast.project_timesheet_forecast_report_view_search
msgid "Date"
msgstr "Datum"

#. module: project_timesheet_forecast
#: model:ir.model.fields,field_description:project_timesheet_forecast.field_project_timesheet_forecast_report_analysis__display_name
msgid "Display Name"
msgstr "Schermnaam"

#. module: project_timesheet_forecast
#: model:ir.model.fields,field_description:project_timesheet_forecast.field_project_timesheet_forecast_report_analysis__effective_costs
msgid "Effective Costs"
msgstr "Werkelijke kosten"

#. module: project_timesheet_forecast
#: model_terms:ir.ui.view,arch_db:project_timesheet_forecast.project_forecast_view_tree_inherit_project_timesheet_forecast
msgid "Effective Hours"
msgstr "Werkelijke uren"

#. module: project_timesheet_forecast
#: model:ir.model.fields,field_description:project_timesheet_forecast.field_planning_analysis_report__effective_hours
#: model:ir.model.fields,field_description:project_timesheet_forecast.field_planning_slot__effective_hours
#: model:ir.model.fields,field_description:project_timesheet_forecast.field_project_timesheet_forecast_report_analysis__effective_hours
msgid "Effective Time"
msgstr "Werkelijke uren"

#. module: project_timesheet_forecast
#: model:ir.model.fields,field_description:project_timesheet_forecast.field_planning_analysis_report__effective_hours_cost
msgid "Effective Time Cost"
msgstr "Werkelijke tijdkosten"

#. module: project_timesheet_forecast
#: model:ir.model.fields,field_description:project_timesheet_forecast.field_project_timesheet_forecast_report_analysis__employee_id
#: model_terms:ir.ui.view,arch_db:project_timesheet_forecast.project_timesheet_forecast_report_view_search
msgid "Employee"
msgstr "Werknemer"

#. module: project_timesheet_forecast
#: model_terms:ir.ui.view,arch_db:project_timesheet_forecast.project_timesheet_forecast_report_view_search
msgid "Group By"
msgstr "Groeperen op"

#. module: project_timesheet_forecast
#: model:ir.model.fields,field_description:project_timesheet_forecast.field_project_timesheet_forecast_report_analysis__id
msgid "ID"
msgstr "ID"

#. module: project_timesheet_forecast
#: model:ir.model.fields,field_description:project_timesheet_forecast.field_project_timesheet_forecast_report_analysis__is_published
msgid "Is Published"
msgstr "Is gepubliceerd"

#. module: project_timesheet_forecast
#: model_terms:ir.ui.view,arch_db:project_timesheet_forecast.project_timesheet_forecast_report_view_search
msgid "Last Week"
msgstr "Vorige week"

#. module: project_timesheet_forecast
#: model:ir.model,name:project_timesheet_forecast.model_ir_ui_menu
msgid "Menu"
msgstr "Menu"

#. module: project_timesheet_forecast
#: model_terms:ir.ui.view,arch_db:project_timesheet_forecast.project_timesheet_forecast_report_view_search
msgid "My Department"
msgstr "Mijn afdeling"

#. module: project_timesheet_forecast
#: model_terms:ir.ui.view,arch_db:project_timesheet_forecast.project_timesheet_forecast_report_view_search
msgid "My Projects"
msgstr "Mijn projecten"

#. module: project_timesheet_forecast
#: model_terms:ir.ui.view,arch_db:project_timesheet_forecast.project_timesheet_forecast_report_view_search
msgid "My Team"
msgstr "Mijn team"

#. module: project_timesheet_forecast
#: model_terms:ir.actions.act_window,help:project_timesheet_forecast.project_timesheet_forecast_report_action
msgid "No data yet!"
msgstr "Nog geen gegevens!"

#. module: project_timesheet_forecast
#: model_terms:ir.actions.act_window,help:project_timesheet_forecast.project_timesheet_action_schedule_by_role
msgid "No shifts found. Let's create one!"
msgstr "Geen diensten gevonden. Laten we er één maken!"

#. module: project_timesheet_forecast
#: model:ir.model.fields,help:project_timesheet_forecast.field_planning_analysis_report__effective_hours
#: model:ir.model.fields,help:project_timesheet_forecast.field_planning_slot__effective_hours
msgid ""
"Number of time recorded on the employee's Timesheets for this task (and its "
"sub-tasks) during the timeframe of the shift."
msgstr ""
"Aantal geregistreerde tijd op de urenstaten van de werknemer voor deze taak "
"(en zijn subtaken) gedurende het tijdsbestek van de dienst."

#. module: project_timesheet_forecast
#: model:ir.model.fields,field_description:project_timesheet_forecast.field_project_timesheet_forecast_report_analysis__planned_costs
msgid "Planned Costs"
msgstr "Geplande kosten"

#. module: project_timesheet_forecast
#: model:ir.model.fields,field_description:project_timesheet_forecast.field_project_timesheet_forecast_report_analysis__planned_hours
msgid "Planned Time"
msgstr "Geplande tijd"

#. module: project_timesheet_forecast
#: model:ir.model.fields.selection,name:project_timesheet_forecast.selection__project_timesheet_forecast_report_analysis__line_type__forecast
msgid "Planning"
msgstr "Planning"

#. module: project_timesheet_forecast
#: model:ir.model,name:project_timesheet_forecast.model_planning_analysis_report
msgid "Planning Analysis Report"
msgstr "Planningsanalyserapport"

#. module: project_timesheet_forecast
#: model:ir.model,name:project_timesheet_forecast.model_planning_slot
#: model:ir.model.fields,field_description:project_timesheet_forecast.field_account_analytic_line__slot_id
msgid "Planning Shift"
msgstr "Planning dienst"

#. module: project_timesheet_forecast
#: model:ir.model.fields,field_description:project_timesheet_forecast.field_planning_slot__percentage_hours
msgid "Progress"
msgstr "Voortgang"

#. module: project_timesheet_forecast
#: model:ir.model.fields,field_description:project_timesheet_forecast.field_planning_analysis_report__percentage_hours
msgid "Progress (%)"
msgstr "Voortgang (%)"

#. module: project_timesheet_forecast
#: model:ir.model,name:project_timesheet_forecast.model_project_project
#: model:ir.model.fields,field_description:project_timesheet_forecast.field_project_timesheet_forecast_report_analysis__project_id
#: model_terms:ir.ui.view,arch_db:project_timesheet_forecast.project_timesheet_forecast_report_view_search
msgid "Project"
msgstr "Project"

#. module: project_timesheet_forecast
#: model:ir.actions.act_window,name:project_timesheet_forecast.project_timesheet_action_schedule_by_role
msgid "Schedule by Role"
msgstr "Planning per rol"

#. module: project_timesheet_forecast
#: model_terms:ir.actions.act_window,help:project_timesheet_forecast.project_timesheet_action_schedule_by_role
msgid ""
"Schedule your human and material resources across roles, projects and sales "
"orders."
msgstr ""
"Plan je menselijke en materiële middelen over rollen, projecten en "
"verkooporders."

#. module: project_timesheet_forecast
#: model_terms:ir.ui.view,arch_db:project_timesheet_forecast.project_timesheet_forecast_report_analysis_view_tree
msgid "Sum of Difference"
msgstr "Som van verschil"

#. module: project_timesheet_forecast
#: model_terms:ir.ui.view,arch_db:project_timesheet_forecast.project_timesheet_forecast_report_analysis_view_tree
msgid "Sum of Effective Hours"
msgstr "Som van effectieve uren"

#. module: project_timesheet_forecast
#: model_terms:ir.ui.view,arch_db:project_timesheet_forecast.project_timesheet_forecast_report_analysis_view_tree
msgid "Sum of Planned Hours"
msgstr "Som van geplande uren"

#. module: project_timesheet_forecast
#: model_terms:ir.ui.view,arch_db:project_timesheet_forecast.project_timesheet_forecast_report_view_search
msgid "This Week"
msgstr "Deze week"

#. module: project_timesheet_forecast
#. odoo-python
#: code:addons/project_timesheet_forecast/models/project_forecast.py:0
msgid "This project isn't expected to have slot during this period."
msgstr ""
"Dit project zal naar verwachting geen tijdslots hebben in deze periode."

#. module: project_timesheet_forecast
#: model:ir.model.fields,field_description:project_timesheet_forecast.field_planning_analysis_report__remaining_hours
#: model:ir.model.fields,field_description:project_timesheet_forecast.field_project_timesheet_forecast_report_analysis__difference
msgid "Time Remaining"
msgstr "Resterende tijd"

#. module: project_timesheet_forecast
#: model:ir.model.fields.selection,name:project_timesheet_forecast.selection__project_timesheet_forecast_report_analysis__line_type__timesheet
msgid "Timesheet"
msgstr "Urenstaat"

#. module: project_timesheet_forecast
#: model:ir.model,name:project_timesheet_forecast.model_project_timesheet_forecast_report_analysis
msgid "Timesheet & Planning Statistics"
msgstr "Urenstaat & Planningstatistieken"

#. module: project_timesheet_forecast
#: model_terms:ir.ui.view,arch_db:project_timesheet_forecast.project_timesheet_forecast_report_view_graph
#: model_terms:ir.ui.view,arch_db:project_timesheet_forecast.project_timesheet_forecast_report_view_pivot
msgid "Timesheet and Planning Analysis"
msgstr "Urenstaat en planninganalyse"

#. module: project_timesheet_forecast
#. odoo-python
#: code:addons/project_timesheet_forecast/models/project_forecast.py:0
msgid "Timesheets"
msgstr "Urenstaten"

#. module: project_timesheet_forecast
#: model:ir.actions.act_window,name:project_timesheet_forecast.project_timesheet_forecast_report_action
#: model:ir.ui.menu,name:project_timesheet_forecast.menu_project_timesheet_forecast_report_analysis
msgid "Timesheets / Planning Analysis"
msgstr "Analyse Urenstaten / Planning"

#. module: project_timesheet_forecast
#: model_terms:ir.ui.view,arch_db:project_timesheet_forecast.project_timesheet_forecast_report_analysis_view_tree
#: model_terms:ir.ui.view,arch_db:project_timesheet_forecast.project_timesheet_forecast_report_view_search
msgid "Timesheets and Planning Analysis"
msgstr "Urenstaten en planningsanalyse"

#. module: project_timesheet_forecast
#: model:ir.model.fields,help:project_timesheet_forecast.field_planning_slot__allow_timesheets
msgid "Timesheets can be logged on this slot."
msgstr "Urenstaten kunnen op dit slot worden gelogd."

#. module: project_timesheet_forecast
#: model_terms:ir.ui.view,arch_db:project_timesheet_forecast.project_timesheet_forecast_report_view_search
msgid "Today"
msgstr "Vandaag"

#. module: project_timesheet_forecast
#: model_terms:ir.actions.act_window,help:project_timesheet_forecast.project_timesheet_forecast_report_action
msgid ""
"Track the progress of your projects by comparing the hours initially planned"
" with the effective hours recorded"
msgstr ""
"Volg de voortgang van je projecten door de aanvankelijke geplande duren te "
"vergelijken met de werkelijke geregistreerde uren."

#. module: project_timesheet_forecast
#: model:ir.model.fields,field_description:project_timesheet_forecast.field_project_timesheet_forecast_report_analysis__line_type
msgid "Type"
msgstr "Soort"
