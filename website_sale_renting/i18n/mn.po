# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_sale_renting
# 
# Translators:
# <AUTHOR> <EMAIL>, 2024
# Bayark<PERSON><PERSON> Bataa, 2024
# <PERSON><PERSON><PERSON><PERSON> Gan<PERSON> <<EMAIL>>, 2024
# hish, 2024
# <AUTHOR> <EMAIL>, 2024
# <PERSON>, 2024
# Baskhu<PERSON>hu<PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:29+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Baskhu<PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Mongolian (https://app.transifex.com/odoo/teams/41243/mn/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: mn\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: website_sale_renting
#. odoo-python
#: code:addons/website_sale_renting/models/sale_order.py:0
msgid ""
"\n"
"                The new period is not valid for some products of your cart.\n"
"                Your changes on the rental period are not taken into account.\n"
"            "
msgstr ""

#. module: website_sale_renting
#. odoo-python
#: code:addons/website_sale_renting/models/sale_order.py:0
msgid ""
"\n"
"                Your rental duration was too short. Unfortunately, we do not process\n"
"                rentals that last less than %(duration)s %(unit)s.\n"
"            "
msgstr ""

#. module: website_sale_renting
#. odoo-python
#: code:addons/website_sale_renting/models/sale_order.py:0
msgid ""
"\n"
"                Your rental product had invalid date of pickup (%(start_date)s).\n"
"                Unfortunately, we do not process pickups on that weekday.\n"
"            "
msgstr ""

#. module: website_sale_renting
#. odoo-python
#: code:addons/website_sale_renting/models/sale_order.py:0
msgid ""
"\n"
"                Your rental product had invalid date of return (%(end_date)s).\n"
"                Unfortunately, we do not process returns on that weekday.\n"
"            "
msgstr ""

#. module: website_sale_renting
#. odoo-python
#: code:addons/website_sale_renting/models/sale_order.py:0
msgid ""
"\n"
"                Your rental product had invalid dates of pickup (%(start_date)s) and\n"
"                return (%(end_date)s). Unfortunately, we do not process pickups nor\n"
"                returns on those weekdays.\n"
"            "
msgstr ""

#. module: website_sale_renting
#. odoo-python
#: code:addons/website_sale_renting/models/sale_order.py:0
msgid ""
"\n"
"            Some of your rental products (%(product)s) cannot be rented during the\n"
"            selected period and your cart must be updated. We're sorry for the\n"
"            inconvenience.\n"
"        "
msgstr ""

#. module: website_sale_renting
#. odoo-javascript
#: code:addons/website_sale_renting/static/src/js/renting_mixin.js:0
msgid "(%s days)."
msgstr ""

#. module: website_sale_renting
#. odoo-javascript
#: code:addons/website_sale_renting/static/src/js/renting_mixin.js:0
msgid "(%s hours)."
msgstr ""

#. module: website_sale_renting
#. odoo-javascript
#: code:addons/website_sale_renting/static/src/js/renting_mixin.js:0
msgid "(%s months)."
msgstr ""

#. module: website_sale_renting
#. odoo-javascript
#: code:addons/website_sale_renting/static/src/js/renting_mixin.js:0
msgid "(%s weeks)."
msgstr ""

#. module: website_sale_renting
#: model_terms:ir.ui.view,arch_db:website_sale_renting.s_rental_search_options
msgid ", .s_rental_search"
msgstr ""

#. module: website_sale_renting
#: model_terms:ir.ui.view,arch_db:website_sale_renting.o_wsale_offcanvas
#: model_terms:ir.ui.view,arch_db:website_sale_renting.products
#: model_terms:ir.ui.view,arch_db:website_sale_renting.products_collapsible
msgid "<b>Rental Period</b>"
msgstr ""

#. module: website_sale_renting
#: model_terms:ir.ui.view,arch_db:website_sale_renting.rental_product
msgid ""
"<i class=\"fa fa-warning\" role=\"img\" aria-label=\"Warning\"/>\n"
"                            <span name=\"renting_warning_message\"/>"
msgstr ""

#. module: website_sale_renting
#: model_terms:ir.ui.view,arch_db:website_sale_renting.cart
msgid ""
"<i class=\"fa fa-warning\" role=\"img\" aria-label=\"Warning\"/>\n"
"                        <span name=\"renting_warning_message\"/>"
msgstr ""

#. module: website_sale_renting
#: model_terms:ir.ui.view,arch_db:website_sale_renting.s_rental_search
msgid ""
"<i class=\"fa fa-warning\" role=\"img\" aria-label=\"Warning\"/>\n"
"                    <span name=\"renting_warning_message\"/>"
msgstr ""

#. module: website_sale_renting
#: model_terms:ir.ui.view,arch_db:website_sale_renting.datepicker
msgid ""
"<i class=\"fa fa-warning\" role=\"img\" aria-label=\"Warning\"/>\n"
"                <span name=\"renting_warning_message\"/>"
msgstr ""

#. module: website_sale_renting
#: model_terms:ir.ui.view,arch_db:website_sale_renting.o_wsale_offcanvas
#: model_terms:ir.ui.view,arch_db:website_sale_renting.products
msgid ""
"<small class=\"mx-auto\"><b>Clear Rental Period</b></small>\n"
"                                <i class=\"oi oi-close\" role=\"img\"/>"
msgstr ""

#. module: website_sale_renting
#: model_terms:ir.ui.view,arch_db:website_sale_renting.rental_search_result_price
msgid "<small>from</small>"
msgstr ""

#. module: website_sale_renting
#: model_terms:ir.ui.view,arch_db:website_sale_renting.rental_search_result_price
msgid "<small>per</small>"
msgstr ""

#. module: website_sale_renting
#: model_terms:ir.ui.view,arch_db:website_sale_renting.s_rental_search
msgid ""
"<span class=\"d-md-none me-2\">Search period</span>\n"
"                                <i class=\"fa fa-search\" role=\"img\"/>"
msgstr ""

#. module: website_sale_renting
#: model_terms:ir.ui.view,arch_db:website_sale_renting.price_dynamic_filter_template_product_product
#: model_terms:ir.ui.view,arch_db:website_sale_renting.rental_products_price
msgid "<span>/</span>"
msgstr ""

#. module: website_sale_renting
#: model_terms:ir.ui.view,arch_db:website_sale_renting.rental_product
msgid "<span>Rental Period</span>"
msgstr ""

#. module: website_sale_renting
#: model_terms:ir.ui.view,arch_db:website_sale_renting.products_item
#: model_terms:ir.ui.view,arch_db:website_sale_renting.rental_products_price
#: model_terms:ir.ui.view,arch_db:website_sale_renting.suggested_products_list
msgid "<span>per</span>"
msgstr ""

#. module: website_sale_renting
#: model_terms:ir.ui.view,arch_db:website_sale_renting.rental_product
msgid "<strong class=\"attribute_name\">Pricing</strong>"
msgstr ""

#. module: website_sale_renting
#. odoo-javascript
#: code:addons/website_sale_renting/static/src/snippets/s_rental_search/options.js:0
msgid "All"
msgstr "Бүх"

#. module: website_sale_renting
#: model_terms:ir.ui.view,arch_db:website_sale_renting.cart
msgid "Applies to all rented items"
msgstr ""

#. module: website_sale_renting
#: model_terms:ir.ui.view,arch_db:website_sale_renting.s_rental_search_options
msgid "Attribute Filter"
msgstr ""

#. module: website_sale_renting
#: model:ir.model,name:website_sale_renting.model_res_company
msgid "Companies"
msgstr "Компаниуд"

#. module: website_sale_renting
#: model:ir.model,name:website_sale_renting.model_res_config_settings
msgid "Config Settings"
msgstr "Тохиргооны тохируулга"

#. module: website_sale_renting
#: model_terms:ir.ui.view,arch_db:website_sale_renting.snippet_options
msgid "Datepicker"
msgstr ""

#. module: website_sale_renting
#: model_terms:ir.ui.view,arch_db:website_sale_renting.s_rental_search_options
msgid "Day"
msgstr "Хоног"

#. module: website_sale_renting
#: model:ir.model.fields.selection,name:website_sale_renting.selection__res_company__renting_minimal_time_unit__day
msgid "Days"
msgstr "Өдөр"

#. module: website_sale_renting
#: model_terms:ir.ui.view,arch_db:website_sale_renting.res_config_settings_view_form
msgid "Days during which pickup and return are not possible."
msgstr ""

#. module: website_sale_renting
#: model_terms:ir.ui.view,arch_db:website_sale_renting.cart
#: model_terms:ir.ui.view,arch_db:website_sale_renting.datepicker
#: model_terms:ir.ui.view,arch_db:website_sale_renting.rental_product
#: model_terms:ir.ui.view,arch_db:website_sale_renting.s_rental_search
msgid "End date"
msgstr "Дуусах огноо"

#. module: website_sale_renting
#: model:ir.model.fields,field_description:website_sale_renting.field_res_company__renting_forbidden_fri
#: model:ir.model.fields,field_description:website_sale_renting.field_res_config_settings__renting_forbidden_fri
msgid "Friday"
msgstr "Баасан"

#. module: website_sale_renting
#: model_terms:ir.ui.view,arch_db:website_sale_renting.price_dynamic_filter_template_product_product
msgid "From"
msgstr "Эхлэх"

#. module: website_sale_renting
#: model_terms:ir.ui.view,arch_db:website_sale_renting.o_wsale_offcanvas
msgid "Go to your cart"
msgstr ""

#. module: website_sale_renting
#: model_terms:ir.ui.view,arch_db:website_sale_renting.s_rental_search_options
msgid "Hour"
msgstr ""

#. module: website_sale_renting
#: model:ir.model.fields.selection,name:website_sale_renting.selection__res_company__renting_minimal_time_unit__hour
msgid "Hours"
msgstr "Цаг"

#. module: website_sale_renting
#: model:ir.model.fields,field_description:website_sale_renting.field_res_company__renting_minimal_time_duration
#: model:ir.model.fields,field_description:website_sale_renting.field_res_config_settings__renting_minimal_time_duration
msgid "Minimal Rental Duration"
msgstr ""

#. module: website_sale_renting
#: model:ir.model.fields,field_description:website_sale_renting.field_res_company__renting_minimal_time_unit
#: model:ir.model.fields,field_description:website_sale_renting.field_res_config_settings__renting_minimal_time_unit
msgid "Minimal Rental Duration Unit"
msgstr ""

#. module: website_sale_renting
#: model_terms:ir.ui.view,arch_db:website_sale_renting.res_config_settings_view_form
msgid "Minimal duration between pickup and return."
msgstr ""

#. module: website_sale_renting
#: model_terms:ir.ui.view,arch_db:website_sale_renting.res_config_settings_view_form
msgid "Minimal time of rental"
msgstr ""

#. module: website_sale_renting
#: model:ir.model.fields,field_description:website_sale_renting.field_res_company__renting_forbidden_mon
#: model:ir.model.fields,field_description:website_sale_renting.field_res_config_settings__renting_forbidden_mon
msgid "Monday"
msgstr "Даваа"

#. module: website_sale_renting
#: model:ir.model.fields.selection,name:website_sale_renting.selection__res_company__renting_minimal_time_unit__month
msgid "Months"
msgstr "Сар"

#. module: website_sale_renting
#. odoo-python
#: code:addons/website_sale_renting/models/product_template.py:0
msgid "Please choose a return date that is after the pickup date."
msgstr ""

#. module: website_sale_renting
#. odoo-javascript
#: code:addons/website_sale_renting/static/src/js/renting_mixin.js:0
msgid "Please select a rental period."
msgstr ""

#. module: website_sale_renting
#: model:ir.model,name:website_sale_renting.model_product_pricelist
msgid "Pricelist"
msgstr "Үнийн хүснэгт"

#. module: website_sale_renting
#: model:ir.model,name:website_sale_renting.model_product_template
msgid "Product"
msgstr "Бараа"

#. module: website_sale_renting
#: model:ir.model,name:website_sale_renting.model_product_product
msgid "Product Variant"
msgstr "Барааны хувилбар"

#. module: website_sale_renting
#: model_terms:ir.ui.view,arch_db:website_sale_renting.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:website_sale_renting.res_config_settings_view_form_inherit_website
msgid "Rent Online"
msgstr ""

#. module: website_sale_renting
#: model_terms:ir.ui.view,arch_db:website_sale_renting.cart_summary
msgid "Rental Period"
msgstr ""

#. module: website_sale_renting
#: model_terms:ir.ui.view,arch_db:website_sale_renting.accordion_rental_item
msgid "Rental Pricing"
msgstr ""

#. module: website_sale_renting
#: model_terms:ir.ui.view,arch_db:website_sale_renting.snippets
msgid "Rental Search"
msgstr ""

#. module: website_sale_renting
#: model_terms:ir.ui.view,arch_db:website_sale_renting.cart
msgid "Rental period"
msgstr ""

#. module: website_sale_renting
#: model:ir.model,name:website_sale_renting.model_sale_order
msgid "Sales Order"
msgstr "Борлуулалтын захиалга"

#. module: website_sale_renting
#: model:ir.model,name:website_sale_renting.model_sale_order_line
msgid "Sales Order Line"
msgstr "Борлуулалтын захиалгын мөр"

#. module: website_sale_renting
#: model:ir.model.fields,field_description:website_sale_renting.field_res_company__renting_forbidden_sat
#: model:ir.model.fields,field_description:website_sale_renting.field_res_config_settings__renting_forbidden_sat
msgid "Saturday"
msgstr "Бямба"

#. module: website_sale_renting
#: model:ir.model.fields,help:website_sale_renting.field_res_config_settings__tz
#: model:ir.model.fields,help:website_sale_renting.field_website__tz
msgid "Select your website timezone here."
msgstr ""

#. module: website_sale_renting
#. odoo-python
#: code:addons/website_sale_renting/models/sale_order.py:0
msgid ""
"Some of your rental products cannot be rented during the selected period and"
" your cart must be updated. We're sorry for the inconvenience."
msgstr ""

#. module: website_sale_renting
#: model_terms:ir.ui.view,arch_db:website_sale_renting.cart
#: model_terms:ir.ui.view,arch_db:website_sale_renting.datepicker
#: model_terms:ir.ui.view,arch_db:website_sale_renting.rental_product
#: model_terms:ir.ui.view,arch_db:website_sale_renting.s_rental_search
msgid "Start date"
msgstr "Эхлэх огноо"

#. module: website_sale_renting
#: model:ir.model.fields,field_description:website_sale_renting.field_res_company__renting_forbidden_sun
#: model:ir.model.fields,field_description:website_sale_renting.field_res_config_settings__renting_forbidden_sun
msgid "Sunday"
msgstr "Ням"

#. module: website_sale_renting
#: model_terms:ir.ui.view,arch_db:website_sale_renting.o_wsale_offcanvas
msgid "The period must be the same for all the products in your cart."
msgstr ""

#. module: website_sale_renting
#: model_terms:ir.ui.view,arch_db:website_sale_renting.products
#: model_terms:ir.ui.view,arch_db:website_sale_renting.rental_product
msgid ""
"The period must be the same for all the products in your cart. Go to your "
"cart to change it or create different orders."
msgstr ""

#. module: website_sale_renting
#. odoo-javascript
#: code:addons/website_sale_renting/static/src/js/renting_mixin.js:0
msgid "The pickup date cannot be in the past."
msgstr ""

#. module: website_sale_renting
#. odoo-javascript
#: code:addons/website_sale_renting/static/src/js/renting_mixin.js:0
msgid "The rental lasts less than the minimal rental duration %s"
msgstr ""

#. module: website_sale_renting
#. odoo-javascript
#: code:addons/website_sale_renting/static/src/js/renting_mixin.js:0
msgid "The return date should be after the pickup date."
msgstr ""

#. module: website_sale_renting
#: model:ir.model.fields,field_description:website_sale_renting.field_res_company__renting_forbidden_thu
#: model:ir.model.fields,field_description:website_sale_renting.field_res_config_settings__renting_forbidden_thu
msgid "Thursday"
msgstr "Пүрэв"

#. module: website_sale_renting
#: model:ir.model.fields,field_description:website_sale_renting.field_res_config_settings__tz
#: model:ir.model.fields,field_description:website_sale_renting.field_website__tz
#: model_terms:ir.ui.view,arch_db:website_sale_renting.res_config_settings_view_form_inherit_website
msgid "Timezone"
msgstr "Цагийн бүс"

#. module: website_sale_renting
#: model_terms:ir.ui.view,arch_db:website_sale_renting.s_rental_search_options
msgid "Timing"
msgstr ""

#. module: website_sale_renting
#: model:ir.model.fields,field_description:website_sale_renting.field_res_company__renting_forbidden_tue
#: model:ir.model.fields,field_description:website_sale_renting.field_res_config_settings__renting_forbidden_tue
msgid "Tuesday"
msgstr "Мягмар"

#. module: website_sale_renting
#: model_terms:ir.ui.view,arch_db:website_sale_renting.res_config_settings_view_form
msgid "Unavailability days"
msgstr ""

#. module: website_sale_renting
#: model:ir.model,name:website_sale_renting.model_website
msgid "Website"
msgstr "Вэбсайт"

#. module: website_sale_renting
#: model:ir.model.fields,field_description:website_sale_renting.field_res_company__renting_forbidden_wed
#: model:ir.model.fields,field_description:website_sale_renting.field_res_config_settings__renting_forbidden_wed
msgid "Wednesday"
msgstr "Лхагва"

#. module: website_sale_renting
#: model:ir.model.fields.selection,name:website_sale_renting.selection__res_company__renting_minimal_time_unit__week
msgid "Weeks"
msgstr "7 хоног"

#. module: website_sale_renting
#. odoo-javascript
#: code:addons/website_sale_renting/static/src/js/renting_mixin.js:0
msgid "You cannot pick up your rental on that day of the week."
msgstr ""

#. module: website_sale_renting
#. odoo-javascript
#: code:addons/website_sale_renting/static/src/js/renting_mixin.js:0
msgid "You cannot return your rental on that day of the week."
msgstr ""

#. module: website_sale_renting
#. odoo-python
#: code:addons/website_sale_renting/models/sale_order.py:0
msgid "Your rental product cannot be pickedup in the past."
msgstr ""

#. module: website_sale_renting
#: model_terms:ir.ui.view,arch_db:website_sale_renting.products_item
#: model_terms:ir.ui.view,arch_db:website_sale_renting.suggested_products_list
msgid "from"
msgstr "хаанаас"

#. module: website_sale_renting
#: model_terms:ir.ui.view,arch_db:website_sale_renting.o_wsale_offcanvas
msgid "to change it or create different orders."
msgstr ""
