# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* currency_rate_live
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-19 09:53+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Language-Team: Hindi (https://app.transifex.com/odoo/teams/41243/hi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: hi\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: currency_rate_live
#: model_terms:ir.ui.view,arch_db:currency_rate_live.res_config_settings_view_form
msgid ""
"<i title=\"Update now\" role=\"img\" aria-label=\"Update now\" class=\"fa "
"fa-fw fa-refresh\"/>"
msgstr ""

#. module: currency_rate_live
#: model:ir.model,name:currency_rate_live.model_res_company
msgid "Companies"
msgstr ""

#. module: currency_rate_live
#: model:ir.model,name:currency_rate_live.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: currency_rate_live
#: model:ir.actions.server,name:currency_rate_live.ir_cron_currency_update_ir_actions_server
msgid "Currency: rate update"
msgstr ""

#. module: currency_rate_live
#: model:ir.model.fields.selection,name:currency_rate_live.selection__res_company__currency_interval_unit__daily
msgid "Daily"
msgstr ""

#. module: currency_rate_live
#. odoo-python
#: code:addons/currency_rate_live/models/res_config_settings.py:0
msgid "Error updating the currency rates from the BCU: %s."
msgstr ""

#. module: currency_rate_live
#: model:ir.model.fields.selection,name:currency_rate_live.selection__res_company__currency_provider__ecb
msgid "European Central Bank"
msgstr ""

#. module: currency_rate_live
#: model_terms:ir.ui.view,arch_db:currency_rate_live.res_config_settings_view_form
msgid "Interval"
msgstr ""

#. module: currency_rate_live
#: model:ir.model.fields,field_description:currency_rate_live.field_res_company__currency_interval_unit
#: model:ir.model.fields,field_description:currency_rate_live.field_res_config_settings__currency_interval_unit
msgid "Interval Unit"
msgstr ""

#. module: currency_rate_live
#: model:ir.model.fields.selection,name:currency_rate_live.selection__res_company__currency_interval_unit__manually
msgid "Manually"
msgstr ""

#. module: currency_rate_live
#: model:ir.model.fields.selection,name:currency_rate_live.selection__res_company__currency_interval_unit__monthly
msgid "Monthly"
msgstr ""

#. module: currency_rate_live
#: model:ir.model.fields,field_description:currency_rate_live.field_res_company__currency_next_execution_date
#: model:ir.model.fields,field_description:currency_rate_live.field_res_config_settings__currency_next_execution_date
msgid "Next Execution Date"
msgstr ""

#. module: currency_rate_live
#: model_terms:ir.ui.view,arch_db:currency_rate_live.res_config_settings_view_form
msgid "Next Run"
msgstr ""

#. module: currency_rate_live
#. odoo-python
#: code:addons/currency_rate_live/models/res_config_settings.py:0
msgid "No available currency rate could be updated from the BCU."
msgstr ""

#. module: currency_rate_live
#: model_terms:ir.ui.view,arch_db:currency_rate_live.res_config_settings_view_form
msgid "Service"
msgstr ""

#. module: currency_rate_live
#: model:ir.model.fields,field_description:currency_rate_live.field_res_company__currency_provider
#: model:ir.model.fields,field_description:currency_rate_live.field_res_config_settings__currency_provider
msgid "Service Provider"
msgstr ""

#. module: currency_rate_live
#. odoo-python
#: code:addons/currency_rate_live/models/res_config_settings.py:0
msgid ""
"The selected exchange rate provider requires the GTQ and USD currencies to "
"be active."
msgstr ""

#. module: currency_rate_live
#. odoo-python
#: code:addons/currency_rate_live/models/res_config_settings.py:0
msgid ""
"Unable to connect to the online exchange rate platform %s. The web service "
"may be temporarily down. Please try again in a moment."
msgstr ""

#. module: currency_rate_live
#: model:ir.model.fields.selection,name:currency_rate_live.selection__res_company__currency_interval_unit__weekly
msgid "Weekly"
msgstr ""

#. module: currency_rate_live
#. odoo-python
#: code:addons/currency_rate_live/models/res_config_settings.py:0
msgid ""
"Your main currency (%s) is not supported by this exchange rate provider. "
"Please choose another one."
msgstr ""

#. module: currency_rate_live
#: model:ir.model.fields.selection,name:currency_rate_live.selection__res_company__currency_provider__cbuae
msgid "[AE] Central Bank of the UAE"
msgstr ""

#. module: currency_rate_live
#: model:ir.model.fields.selection,name:currency_rate_live.selection__res_company__currency_provider__bnb
msgid "[BG] Bulgaria National Bank"
msgstr ""

#. module: currency_rate_live
#: model:ir.model.fields.selection,name:currency_rate_live.selection__res_company__currency_provider__bbr
msgid "[BR] Central Bank of Brazil"
msgstr ""

#. module: currency_rate_live
#: model:ir.model.fields.selection,name:currency_rate_live.selection__res_company__currency_provider__boc
msgid "[CA] Bank of Canada"
msgstr ""

#. module: currency_rate_live
#: model:ir.model.fields.selection,name:currency_rate_live.selection__res_company__currency_provider__fta
msgid "[CH] Federal Tax Administration of Switzerland"
msgstr ""

#. module: currency_rate_live
#: model:ir.model.fields.selection,name:currency_rate_live.selection__res_company__currency_provider__mindicador
msgid "[CL] Central Bank of Chile via mindicador.cl"
msgstr ""

#. module: currency_rate_live
#: model:ir.model.fields.selection,name:currency_rate_live.selection__res_company__currency_provider__banrepco
msgid "[CO] Bank of the Republic"
msgstr ""

#. module: currency_rate_live
#: model:ir.model.fields.selection,name:currency_rate_live.selection__res_company__currency_provider__cnb
msgid "[CZ] Czech National Bank"
msgstr ""

#. module: currency_rate_live
#: model:ir.model.fields.selection,name:currency_rate_live.selection__res_company__currency_provider__cbegy
msgid "[EG] Central Bank of Egypt"
msgstr ""

#. module: currency_rate_live
#: model:ir.model.fields.selection,name:currency_rate_live.selection__res_company__currency_provider__banguat
msgid "[GT] Bank of Guatemala"
msgstr ""

#. module: currency_rate_live
#: model:ir.model.fields.selection,name:currency_rate_live.selection__res_company__currency_provider__mnb
msgid "[HU] Magyar Nemzeti Bank"
msgstr ""

#. module: currency_rate_live
#: model:ir.model.fields.selection,name:currency_rate_live.selection__res_company__currency_provider__bi
msgid "[ID] Bank Indonesia"
msgstr ""

#. module: currency_rate_live
#: model:ir.model.fields.selection,name:currency_rate_live.selection__res_company__currency_provider__boi
msgid "[IT] Bank of Italy"
msgstr ""

#. module: currency_rate_live
#: model:ir.model.fields.selection,name:currency_rate_live.selection__res_company__currency_provider__banxico
msgid "[MX] Bank of Mexico"
msgstr ""

#. module: currency_rate_live
#: model:ir.model.fields.selection,name:currency_rate_live.selection__res_company__currency_provider__bnm
msgid "[MY] Bank Negara Malaysia"
msgstr ""

#. module: currency_rate_live
#: model:ir.model.fields.selection,name:currency_rate_live.selection__res_company__currency_provider__bcrp
msgid "[PE] SUNAT (replaces Bank of Peru)"
msgstr ""

#. module: currency_rate_live
#: model:ir.model.fields.selection,name:currency_rate_live.selection__res_company__currency_provider__nbp
msgid "[PL] National Bank of Poland"
msgstr ""

#. module: currency_rate_live
#: model:ir.model.fields.selection,name:currency_rate_live.selection__res_company__currency_provider__bnr
msgid "[RO] National Bank of Romania"
msgstr ""

#. module: currency_rate_live
#: model:ir.model.fields.selection,name:currency_rate_live.selection__res_company__currency_provider__srb
msgid "[SE] Sveriges Riksbank"
msgstr ""

#. module: currency_rate_live
#: model:ir.model.fields.selection,name:currency_rate_live.selection__res_company__currency_provider__bot
msgid "[TH] Bank of Thailand"
msgstr ""

#. module: currency_rate_live
#: model:ir.model.fields.selection,name:currency_rate_live.selection__res_company__currency_provider__tcmb
msgid "[TR] Central Bank of the Republic of Türkiye"
msgstr ""

#. module: currency_rate_live
#: model:ir.model.fields.selection,name:currency_rate_live.selection__res_company__currency_provider__hmrc
msgid "[UK] HM Revenue & Customs"
msgstr ""

#. module: currency_rate_live
#: model:ir.model.fields.selection,name:currency_rate_live.selection__res_company__currency_provider__bcu
msgid "[UY] Uruguayan Central Bank"
msgstr ""

#. module: currency_rate_live
#: model:ir.model.fields.selection,name:currency_rate_live.selection__res_company__currency_provider__xe_com
msgid "xe.com"
msgstr ""
