# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_avatax
# 
# Translators:
# <PERSON><PERSON><PERSON>, 2024
# Larisa Pop, 2024
# Wil Odoo, 2024
# <PERSON><PERSON><PERSON>_nexterp, 2025
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:26+0000\n"
"PO-Revision-Date: 2024-09-25 09:43+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2025\n"
"Language-Team: Romanian (https://app.transifex.com/odoo/teams/41243/ro/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ro\n"
"Plural-Forms: nplurals=3; plural=(n==1?0:(((n%100>19)||((n%100==0)&&(n!=0)))?2:1));\n"

#. module: account_avatax
#. odoo-python
#: code:addons/account_avatax/models/account_external_tax_mixin.py:0
msgid "- %(partner_name)s (ID: %(partner_id)s) on %(record_list)s"
msgstr "- %(partner_name)s (ID: %(partner_id)s) pe %(record_list)s"

#. module: account_avatax
#: model_terms:ir.ui.view,arch_db:account_avatax.res_config_settings_view_form
msgid ""
"<i class=\"oi oi-fw oi-arrow-right\"/>\n"
"                                How to Get Credentials"
msgstr ""
"<i class=\"oi oi-fw oi-arrow-right\"/>\n"
"                                Cum să obțineți acreditările"

#. module: account_avatax
#: model_terms:ir.ui.view,arch_db:account_avatax.res_config_settings_view_form
msgid ""
"<i title=\"Go to Avatax portal\" role=\"img\" aria-label=\"Go to Avatax portal\" class=\"fa fa-external-link-square fa-fw\"/>\n"
"                                Avatax portal"
msgstr ""
"<i title=\"Go to Avatax portal\" role=\"img\" aria-label=\"Go to Avatax portal\" class=\"fa fa-external-link-square fa-fw\"/>\n"
"                                Portalul Avatax"

#. module: account_avatax
#: model_terms:ir.ui.view,arch_db:account_avatax.res_config_settings_view_form
msgid ""
"<i title=\"Show logs\" role=\"img\" aria-label=\"Show logs\" class=\"fa fa-file-text-o\"/>\n"
"                                Show logs"
msgstr ""
"<i title=\"Show logs\" role=\"img\" aria-label=\"Show logs\" class=\"fa fa-file-text-o\"/>\n"
"                               Afișați jurnalele"

#. module: account_avatax
#: model_terms:ir.ui.view,arch_db:account_avatax.res_config_settings_view_form
msgid ""
"<i title=\"Start logging for 30 minutes\" role=\"img\" aria-label=\"Start logging for 30 minutes\" class=\"fa fa-file-text-o\"/>\n"
"                                Start logging for 30 minutes"
msgstr ""
"<i title=\"Start logging for 30 minutes\" role=\"img\" aria-label=\"Start logging for 30 minutes\" class=\"fa fa-file-text-o\"/>\n"
"                                Începeți să vă conectați timp de 30 de minute"

#. module: account_avatax
#: model_terms:ir.ui.view,arch_db:account_avatax.res_config_settings_view_form
msgid ""
"<i title=\"Sync Parameters\" role=\"img\" aria-label=\"Sync Parameters\" class=\"fa fa-refresh\"/>\n"
"                                Sync Parameters"
msgstr ""
"<i title=\"Sync Parameters\" role=\"img\" aria-label=\"Sync Parameters\" class=\"fa fa-refresh\"/>\n"
"                                Parametrii de sincronizare"

#. module: account_avatax
#: model_terms:ir.ui.view,arch_db:account_avatax.res_config_settings_view_form
msgid ""
"<i title=\"Test connection\" role=\"img\" aria-label=\"Test connection\" class=\"fa fa-plug fa-fw\"/>\n"
"                                Test connection"
msgstr ""
"<i title=\"Test connection\" role=\"img\" aria-label=\"Test connection\" class=\"fa fa-plug fa-fw\"/>\n"
"                                Test de conexiune"

#. module: account_avatax
#: model_terms:ir.ui.view,arch_db:account_avatax.res_config_settings_view_form
msgid "API ID"
msgstr "API ID"

#. module: account_avatax
#: model_terms:ir.ui.view,arch_db:account_avatax.res_config_settings_view_form
msgid "API KEY"
msgstr "API KEY"

#. module: account_avatax
#: model:ir.model,name:account_avatax.model_account_chart_template
msgid "Account Chart Template"
msgstr "Plan de Conturi"

#. module: account_avatax
#: model:ir.model.fields,help:account_avatax.field_account_fiscal_position__avatax_invoice_account_id
msgid "Account that will be used by Avatax taxes for invoices."
msgstr "Contul care va fi utilizat de taxele Avatax pentru facturi."

#. module: account_avatax
#: model:ir.model.fields,help:account_avatax.field_account_fiscal_position__avatax_refund_account_id
msgid "Account that will be used by Avatax taxes for refunds."
msgstr "Contul care va fi utilizat de taxele Avatax pentru rambursări."

#. module: account_avatax
#: model_terms:ir.ui.view,arch_db:account_avatax.res_config_settings_view_form
msgid "Address Validation"
msgstr "Validarea adresei"

#. module: account_avatax
#. odoo-python
#: code:addons/account_avatax/wizard/avatax_validate_address.py:0
msgid "Address validation is only supported for North American addresses."
msgstr ""
"Validarea adresei este acceptată numai pentru adresele din America de Nord."

#. module: account_avatax
#. odoo-python
#: code:addons/account_avatax/models/res_company.py:0
msgid "Authentication failed."
msgstr "Autentificare eșuată."

#. module: account_avatax
#. odoo-python
#: code:addons/account_avatax/models/res_company.py:0
msgid "Authentication success."
msgstr "Autentificare reușită."

#. module: account_avatax
#: model:account.fiscal.position,name:account_avatax.account_fiscal_position_avatax_us
msgid "Automatic Tax Mapping (AvaTax)"
msgstr "Maparea automată a taxelor (AvaTax)"

#. module: account_avatax
#: model_terms:ir.ui.view,arch_db:account_avatax.res_config_settings_view_form
msgid "Automatically compute tax rates in the US and Canada."
msgstr ""

#. module: account_avatax
#: model_terms:ir.ui.view,arch_db:account_avatax.res_config_settings_view_form
msgid "AvaTax"
msgstr "AvaTax"

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_res_company__avalara_api_id
#: model:ir.model.fields,field_description:account_avatax.field_res_config_settings__avalara_api_id
msgid "Avalara API ID"
msgstr "Avalara API ID"

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_res_company__avalara_api_key
#: model:ir.model.fields,field_description:account_avatax.field_res_config_settings__avalara_api_key
msgid "Avalara API KEY"
msgstr "Avalara API KEY"

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_res_company__avalara_address_validation
#: model:ir.model.fields,field_description:account_avatax.field_res_config_settings__avalara_address_validation
msgid "Avalara Address Validation"
msgstr "Validarea adresei Avalara"

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_account_avatax_unique_code__avatax_unique_code
#: model:ir.model.fields,field_description:account_avatax.field_account_bank_statement_line__avatax_unique_code
#: model:ir.model.fields,field_description:account_avatax.field_account_move__avatax_unique_code
#: model:ir.model.fields,field_description:account_avatax.field_res_partner__avatax_unique_code
#: model:ir.model.fields,field_description:account_avatax.field_res_users__avatax_unique_code
msgid "Avalara Code"
msgstr "Codul Avalara"

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_res_config_settings__avalara_partner_code
msgid "Avalara Company Code"
msgstr "Codul companiei Avalara"

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_res_company__avalara_environment
#: model:ir.model.fields,field_description:account_avatax.field_res_config_settings__avalara_environment
msgid "Avalara Environment"
msgstr "Mediul Avalara"

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_res_partner__avalara_exemption_id
#: model:ir.model.fields,field_description:account_avatax.field_res_users__avalara_exemption_id
msgid "Avalara Exemption"
msgstr "Excepția Avalara"

#. module: account_avatax
#: model:ir.actions.act_window,name:account_avatax.ir_logging_avalara_action
msgid "Avalara Logging"
msgstr "Jurnalul Avalara"

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_res_partner__avalara_partner_code
#: model:ir.model.fields,field_description:account_avatax.field_res_users__avalara_partner_code
msgid "Avalara Partner Code"
msgstr "Codul partenerului Avalara"

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_res_partner__avalara_show_address_validation
#: model:ir.model.fields,field_description:account_avatax.field_res_users__avalara_show_address_validation
msgid "Avalara Show Address Validation"
msgstr "Avalara arată validarea adresei"

#. module: account_avatax
#: model_terms:ir.ui.view,arch_db:account_avatax.account_fiscal_position_form_inherit
msgid "Avatax"
msgstr "Avatax"

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_product_category__avatax_category_id
#: model:ir.model.fields,field_description:account_avatax.field_product_product__avatax_category_id
#: model:ir.model.fields,field_description:account_avatax.field_product_template__avatax_category_id
msgid "Avatax Category"
msgstr "Categoria Avatax"

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_account_bank_statement_line__avatax_tax_date
#: model:ir.model.fields,field_description:account_avatax.field_account_move__avatax_tax_date
msgid "Avatax Date"
msgstr "Data Avatax"

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_account_fiscal_position__avatax_invoice_account_id
msgid "Avatax Invoice Account"
msgstr "Contul de facturare Avatax"

#. module: account_avatax
#: model:ir.model,name:account_avatax.model_avatax_exemption
msgid "Avatax Partner Exemption Codes"
msgstr "Codurile de excepție a partenerilor Avatax"

#. module: account_avatax
#: model:ir.model,name:account_avatax.model_product_avatax_category
msgid "Avatax Product Category"
msgstr "Categoria produsului Avatax"

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_account_fiscal_position__avatax_refund_account_id
msgid "Avatax Refund Account"
msgstr "Contul de rambursare Avatax"

#. module: account_avatax
#: model:ir.model.fields,help:account_avatax.field_account_bank_statement_line__avatax_tax_date
#: model:ir.model.fields,help:account_avatax.field_account_move__avatax_tax_date
msgid ""
"Avatax will use this date to calculate the tax on this invoice. If not "
"specified it will use the Invoice Date."
msgstr ""
"Avatax va utiliza această dată pentru a calcula taxa pe această factură. "
"Dacă nu este specificată, va utiliza data facturii."

#. module: account_avatax
#: model_terms:ir.ui.view,arch_db:account_avatax.avatax_validate_address_view_form
msgid "Cancel"
msgstr "Anulează"

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_avatax_validate_address__city
#: model_terms:ir.ui.view,arch_db:account_avatax.avatax_validate_address_view_form
msgid "City"
msgstr "Localitate"

#. module: account_avatax
#: model_terms:ir.ui.view,arch_db:account_avatax.avatax_connection_test_result_view_form
msgid "Close"
msgstr "Închide"

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_avatax_exemption__code
#: model:ir.model.fields,field_description:account_avatax.field_product_avatax_category__code
msgid "Code"
msgstr "Cod"

#. module: account_avatax
#: model_terms:ir.ui.view,arch_db:account_avatax.res_config_settings_view_form
msgid "Commit Transactions"
msgstr "Trimite tranzacțiile"

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_res_company__avalara_commit
#: model:ir.model.fields,field_description:account_avatax.field_res_config_settings__avalara_commit
msgid "Commit in Avatax"
msgstr "Trimite în Avatax"

#. module: account_avatax
#: model:ir.model,name:account_avatax.model_res_company
msgid "Companies"
msgstr "Companii"

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_avatax_exemption__company_id
msgid "Company"
msgstr "Companie"

#. module: account_avatax
#: model_terms:ir.ui.view,arch_db:account_avatax.res_config_settings_view_form
msgid "Company Code"
msgstr "Codul companiei"

#. module: account_avatax
#: model:ir.model,name:account_avatax.model_res_config_settings
msgid "Config Settings"
msgstr "Setări de configurare"

#. module: account_avatax
#: model:ir.model,name:account_avatax.model_res_partner
msgid "Contact"
msgstr "Contactați"

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_avatax_validate_address__country_id
#: model_terms:ir.ui.view,arch_db:account_avatax.avatax_validate_address_view_form
msgid "Country"
msgstr "Țară"

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_avatax_connection_test_result__create_uid
#: model:ir.model.fields,field_description:account_avatax.field_avatax_exemption__create_uid
#: model:ir.model.fields,field_description:account_avatax.field_avatax_validate_address__create_uid
#: model:ir.model.fields,field_description:account_avatax.field_product_avatax_category__create_uid
msgid "Created by"
msgstr "Creat de"

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_avatax_connection_test_result__create_date
#: model:ir.model.fields,field_description:account_avatax.field_avatax_exemption__create_date
#: model:ir.model.fields,field_description:account_avatax.field_avatax_validate_address__create_date
#: model:ir.model.fields,field_description:account_avatax.field_product_avatax_category__create_date
msgid "Created on"
msgstr "Creat pe"

#. module: account_avatax
#: model:ir.model.fields,help:account_avatax.field_res_partner__avalara_partner_code
#: model:ir.model.fields,help:account_avatax.field_res_users__avalara_partner_code
msgid "Customer Code set in Avalara for this partner."
msgstr "Codul clientului setat în Avalara pentru acest partener."

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_avatax_exemption__description
#: model:ir.model.fields,field_description:account_avatax.field_product_avatax_category__description
msgid "Description"
msgstr "Descriere"

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_avatax_connection_test_result__display_name
#: model:ir.model.fields,field_description:account_avatax.field_avatax_exemption__display_name
#: model:ir.model.fields,field_description:account_avatax.field_avatax_validate_address__display_name
#: model:ir.model.fields,field_description:account_avatax.field_product_avatax_category__display_name
msgid "Display Name"
msgstr "Nume afișat"

#. module: account_avatax
#: model_terms:ir.ui.view,arch_db:account_avatax.res_config_settings_view_form
msgid "Environment"
msgstr "Mediu"

#. module: account_avatax
#: model:ir.model,name:account_avatax.model_account_fiscal_position
msgid "Fiscal Position"
msgstr "Poziție fiscală"

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_avatax_validate_address__validated_latitude
msgid "Geo Latitude"
msgstr "Geo Latitudine"

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_avatax_validate_address__validated_longitude
msgid "Geo Longitude"
msgstr "Geo Longitudine"

#. module: account_avatax
#. odoo-python
#: code:addons/account_avatax/models/account_external_tax_mixin.py:0
msgid "Go to the configuration panel"
msgstr "Du-te la panoul de configurare"

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_avatax_connection_test_result__id
#: model:ir.model.fields,field_description:account_avatax.field_avatax_exemption__id
#: model:ir.model.fields,field_description:account_avatax.field_avatax_validate_address__id
#: model:ir.model.fields,field_description:account_avatax.field_product_avatax_category__id
msgid "ID"
msgstr "ID"

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_avatax_validate_address__is_already_valid
msgid "Is Already Valid"
msgstr "Este deja valid"

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_account_bank_statement_line__is_avatax
#: model:ir.model.fields,field_description:account_avatax.field_account_external_tax_mixin__is_avatax
#: model:ir.model.fields,field_description:account_avatax.field_account_move__is_avatax
msgid "Is Avatax"
msgstr "Este Avatax"

#. module: account_avatax
#: model:ir.model,name:account_avatax.model_account_move
msgid "Journal Entry"
msgstr "Înregistrare de jurnal "

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_avatax_connection_test_result__write_uid
#: model:ir.model.fields,field_description:account_avatax.field_avatax_exemption__write_uid
#: model:ir.model.fields,field_description:account_avatax.field_avatax_validate_address__write_uid
#: model:ir.model.fields,field_description:account_avatax.field_product_avatax_category__write_uid
msgid "Last Updated by"
msgstr "Ultima actualizare făcută de"

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_avatax_connection_test_result__write_date
#: model:ir.model.fields,field_description:account_avatax.field_avatax_exemption__write_date
#: model:ir.model.fields,field_description:account_avatax.field_avatax_validate_address__write_date
#: model:ir.model.fields,field_description:account_avatax.field_product_avatax_category__write_date
msgid "Last Updated on"
msgstr "Ultima actualizare pe"

#. module: account_avatax
#: model_terms:ir.ui.view,arch_db:account_avatax.avatax_validate_address_view_form
msgid "Latitude"
msgstr "Latitudine"

#. module: account_avatax
#: model_terms:ir.ui.view,arch_db:account_avatax.avatax_validate_address_view_form
msgid "Longitude"
msgstr "Longitudine"

#. module: account_avatax
#: model:ir.model,name:account_avatax.model_account_avatax_unique_code
msgid "Mixin to generate unique ids for Avatax"
msgstr "Mixin pentru a genera id-uri unice pentru Avatax"

#. module: account_avatax
#: model:ir.model,name:account_avatax.model_account_external_tax_mixin
msgid "Mixin to manage common parts of external tax calculation"
msgstr "Mixin pentru a gestiona părțile comune ale calculului fiscal extern"

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_avatax_exemption__name
msgid "Name"
msgstr "Nume"

#. module: account_avatax
#. odoo-python
#: code:addons/account_avatax/models/account_external_tax_mixin.py:0
msgid ""
"Odoo could not change the state of the transaction related to %(document)s in AvaTax\n"
"Please check the status of `%(technical)s` in the AvaTax portal."
msgstr ""
"Odoo nu a putut schimba starea tranzacției asociate cu %(document)s în AvaTax\n"
"Vă rugăm să verificați starea `%(technical)s` în portalul AvaTax."

#. module: account_avatax
#. odoo-python
#: code:addons/account_avatax/models/res_company.py:0
msgid "Odoo could not fetch the exemption codes of %(company)s"
msgstr "Odoo nu a putut prelua codurile de excepție ale %(company)s"

#. module: account_avatax
#. odoo-python
#: code:addons/account_avatax/models/account_external_tax_mixin.py:0
msgid ""
"Odoo could not fetch the taxes related to %(document)s.\n"
"Please check the status of `%(technical)s` in the AvaTax portal."
msgstr ""
"Odoo nu a putut prelua taxele asociate cu %(document)s.\n"
"Vă rugăm să verificați starea `%(technical)s` în portalul AvaTax."

#. module: account_avatax
#. odoo-python
#: code:addons/account_avatax/wizard/avatax_validate_address.py:0
msgid "Odoo could not validate the address of %(partner)s with Avalara."
msgstr "Odoo nu a putut valida adresa %(partner)s cu Avalara."

#. module: account_avatax
#. odoo-python
#: code:addons/account_avatax/models/account_external_tax_mixin.py:0
msgid ""
"Odoo could not void the transaction related to %(document)s in AvaTax\n"
"Please check the status of `%(technical)s` in the AvaTax portal."
msgstr ""
"Odoo nu a putut anula tranzacția asociată cu %(document)s în AvaTax\n"
"Vă rugăm să verificați starea `%(technical)s` în portalul AvaTax."

#. module: account_avatax
#: model_terms:ir.ui.view,arch_db:account_avatax.avatax_validate_address_view_form
msgid "Original Address"
msgstr "Adresa originală"

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_avatax_validate_address__partner_id
msgid "Partner"
msgstr "Partener"

#. module: account_avatax
#. odoo-python
#: code:addons/account_avatax/models/account_external_tax_mixin.py:0
msgid "Please add your AvaTax credentials"
msgstr "Vă rugăm să adăugați credențialele dvs. AvaTax"

#. module: account_avatax
#: model:ir.model,name:account_avatax.model_product_template
msgid "Product"
msgstr "Produs"

#. module: account_avatax
#: model:ir.model,name:account_avatax.model_product_category
msgid "Product Category"
msgstr "Categorie produs"

#. module: account_avatax
#: model:ir.model,name:account_avatax.model_product_product
msgid "Product Variant"
msgstr "Variantă de produs"

#. module: account_avatax
#: model:ir.model.fields.selection,name:account_avatax.selection__res_company__avalara_environment__production
msgid "Production"
msgstr "Producție"

#. module: account_avatax
#: model:ir.model.fields.selection,name:account_avatax.selection__res_company__avalara_environment__sandbox
msgid "Sandbox"
msgstr "Sandbox"

#. module: account_avatax
#: model_terms:ir.ui.view,arch_db:account_avatax.avatax_validate_address_view_form
msgid "Save Validated"
msgstr "Salvare validată"

#. module: account_avatax
#. odoo-python
#: code:addons/account_avatax/models/account_avatax_unique_code.py:0
msgid "Search operation not supported"
msgstr "Operația de căutare nu este acceptată"

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_avatax_connection_test_result__server_response
msgid "Server Response"
msgstr "Răspunsul serverului"

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_avatax_validate_address__state_id
#: model_terms:ir.ui.view,arch_db:account_avatax.avatax_validate_address_view_form
msgid "State"
msgstr "Județ"

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_avatax_validate_address__street
#: model_terms:ir.ui.view,arch_db:account_avatax.avatax_validate_address_view_form
msgid "Street"
msgstr "Stradă"

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_avatax_validate_address__street2
msgid "Street2"
msgstr "Strada2"

#. module: account_avatax
#: model:ir.model,name:account_avatax.model_avatax_validate_address
msgid "Suggests validated addresses from Avatax"
msgstr "Sugerează adrese valide de la Avatax"

#. module: account_avatax
#: model_terms:ir.ui.view,arch_db:account_avatax.res_config_settings_view_form
msgid "Synchronize the exemption codes from Avatax"
msgstr "Sincronizați codurile de excepție de la Avatax"

#. module: account_avatax
#. odoo-python
#: code:addons/account_avatax/models/res_company.py:0
msgid "Test Result"
msgstr "Rezultatul testului"

#. module: account_avatax
#: model:ir.model,name:account_avatax.model_avatax_connection_test_result
msgid "Test connection with avatax"
msgstr "Testați conexiunea cu avatax"

#. module: account_avatax
#: model:ir.model.fields,help:account_avatax.field_res_config_settings__avalara_partner_code
msgid ""
"The Avalara Company Code for this company. Avalara will interpret as DEFAULT"
" if it is not set."
msgstr ""
"Codul companiei Avalara pentru această companie. Avalara va interpreta ca "
"DEFAULT dacă nu este setat."

#. module: account_avatax
#. odoo-python
#: code:addons/account_avatax/models/account_external_tax_mixin.py:0
msgid ""
"The Avalara Tax Code is required for %(name)s (#%(id)s)\n"
"See https://taxcode.avatax.avalara.com/"
msgstr ""
"Codul fiscal Avalara este necesar pentru %(name)s (#%(id)s)\n"
"Vedeți https://taxcode.avatax.avalara.com/"

#. module: account_avatax
#. odoo-python
#: code:addons/account_avatax/models/account_external_tax_mixin.py:0
msgid ""
"The following customer(s) need to have a zip, state and country when using "
"Avatax:"
msgstr ""
"Următorii clienți trebuie să aibă un zip, un stat și o țară atunci când "
"folosesc Avatax:"

#. module: account_avatax
#: model:ir.model.fields,help:account_avatax.field_res_config_settings__avalara_commit
msgid "The transactions will be committed for reporting in Avatax."
msgstr "Tranzacțiile vor fi comise pentru raportare în Avatax."

#. module: account_avatax
#: model_terms:ir.ui.view,arch_db:account_avatax.avatax_validate_address_view_form
msgid "This is already a valid address."
msgstr "Aceasta este deja o adresă validă."

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_res_company__setting_account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_res_config_settings__setting_account_avatax
msgid "Use AvaTax"
msgstr "Utilizați AvaTax"

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_account_fiscal_position__is_avatax
msgid "Use AvaTax API"
msgstr "Utilizați API-ul AvaTax"

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_res_company__avalara_use_upc
#: model:ir.model.fields,field_description:account_avatax.field_res_config_settings__avalara_use_upc
#: model_terms:ir.ui.view,arch_db:account_avatax.res_config_settings_view_form
msgid "Use UPC"
msgstr "Utilizați UPC"

#. module: account_avatax
#: model:ir.model.fields,help:account_avatax.field_res_config_settings__avalara_use_upc
msgid "Use Universal Product Code instead of custom defined codes in Avalara."
msgstr ""
"Utilizați codul universal de produs în loc de coduri definite personalizat "
"în Avalara."

#. module: account_avatax
#: model:ir.model.fields,help:account_avatax.field_account_avatax_unique_code__avatax_unique_code
#: model:ir.model.fields,help:account_avatax.field_account_bank_statement_line__avatax_unique_code
#: model:ir.model.fields,help:account_avatax.field_account_move__avatax_unique_code
#: model:ir.model.fields,help:account_avatax.field_res_partner__avatax_unique_code
#: model:ir.model.fields,help:account_avatax.field_res_users__avatax_unique_code
msgid "Use this code to cross-reference in the Avalara portal."
msgstr "Utilizați acest cod pentru a face o referință în portalul Avalara."

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_avatax_exemption__valid_country_ids
msgid "Valid Country"
msgstr "Țară validă"

#. module: account_avatax
#: model_terms:ir.ui.view,arch_db:account_avatax.res_partner_form_inherit
msgid "Validate"
msgstr "Validează"

#. module: account_avatax
#. odoo-python
#: code:addons/account_avatax/models/res_partner.py:0
msgid "Validate address of %s"
msgstr "Validează adresa de %s"

#. module: account_avatax
#: model:ir.model.fields,help:account_avatax.field_res_config_settings__avalara_address_validation
msgid ""
"Validate and correct the addresses of partners in North America with "
"Avalara."
msgstr ""
"Validează și corectează adresele partenerilor din America de Nord cu "
"Avalara."

#. module: account_avatax
#: model_terms:ir.ui.view,arch_db:account_avatax.avatax_validate_address_view_form
msgid "Validated Address"
msgstr "Adresă validată"

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_avatax_validate_address__validated_city
msgid "Validated City"
msgstr "Oraș validat"

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_avatax_validate_address__validated_country_id
msgid "Validated Country"
msgstr "Țară validată"

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_avatax_validate_address__validated_state_id
msgid "Validated State"
msgstr "Județ validat"

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_avatax_validate_address__validated_street
msgid "Validated Street"
msgstr "Stradă validată"

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_avatax_validate_address__validated_street2
msgid "Validated Street2"
msgstr "Stradă2 validată"

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_avatax_validate_address__validated_zip
msgid "Validated Zip Code"
msgstr "Cod poștal validat"

#. module: account_avatax
#: model:ir.model.fields,field_description:account_avatax.field_avatax_validate_address__zip
#: model_terms:ir.ui.view,arch_db:account_avatax.avatax_validate_address_view_form
msgid "Zip Code"
msgstr "Cod poștal"

#. module: account_avatax
#. odoo-python
#: code:addons/account_avatax/models/product.py:0
msgid "[%(code)s] %(description)s"
msgstr "[%(code)s]%(description)s"

#. module: account_avatax
#: model:ir.model.fields,help:account_avatax.field_product_category__avatax_category_id
#: model:ir.model.fields,help:account_avatax.field_product_product__avatax_category_id
#: model:ir.model.fields,help:account_avatax.field_product_template__avatax_category_id
msgid "https://taxcode.avatax.avalara.com/"
msgstr "https://taxcode.avatax.avalara.com/"
