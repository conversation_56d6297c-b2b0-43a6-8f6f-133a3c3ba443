# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_stock_renting
# 
# Translators:
# Wil Odoo, 2024
# <PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-25 09:15+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2025\n"
"Language-Team: Spanish (Latin America) (https://app.transifex.com/odoo/teams/41243/es_419/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es_419\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: sale_stock_renting
#. odoo-python
#: code:addons/sale_stock_renting/models/product_product.py:0
msgid "%(product)s (%(qty)s item)"
msgstr "%(product)s (%(qty)s producto)"

#. module: sale_stock_renting
#. odoo-python
#: code:addons/sale_stock_renting/models/product_product.py:0
msgid "%(product)s (%(qty)s items)"
msgstr "%(product)s (%(qty)s productos)"

#. module: sale_stock_renting
#: model_terms:ir.ui.view,arch_db:sale_stock_renting.rental_order_wizard_view_form
msgid ""
"<i class=\"fa fa-warning\"/>\n"
"                        <span>\n"
"                            Some products don't have the requested qty available for pickup\n"
"                        </span>"
msgstr ""
"<i class=\"fa fa-warning\"/>\n"
"                        <span>\n"
"                            Algunos productos no tienen la cantidad solicitada disponible para su recolección\n"
"                        </span>"

#. module: sale_stock_renting
#: model_terms:ir.ui.view,arch_db:sale_stock_renting.res_config_settings_inherit_view_form
msgid "Activate to use stock deliveries and receipts for rental orders"
msgstr ""
"Active para usar entregas de inventario y recepciones para órdenes de "
"alquiler"

#. module: sale_stock_renting
#: model:ir.model.fields,help:sale_stock_renting.field_res_company__padding_time
msgid ""
"Amount of time (in hours) during which a product is considered unavailable "
"prior to renting (preparation time)."
msgstr ""
"Tiempo (en horas) durante el cual se considera que un producto no está "
"disponible antes de volver a alquilarlo (tiempo de preparación)."

#. module: sale_stock_renting
#: model:ir.model.fields,help:sale_stock_renting.field_res_config_settings__padding_time
msgid ""
"Amount of time (in hours) during which a product is considered "
"unavailableprior to renting (preparation time)."
msgstr ""
"Tiempo (en horas) durante el cual se considera que un producto no está "
"disponible antes de volver a alquilarlo (tiempo de preparación)."

#. module: sale_stock_renting
#. odoo-javascript
#: code:addons/sale_stock_renting/static/src/widgets/qty_at_date_widget.xml:0
msgid "Availability"
msgstr "Disponibilidad"

#. module: sale_stock_renting
#: model:ir.model.fields,field_description:sale_stock_renting.field_rental_order_wizard_line__qty_available
msgid "Available"
msgstr "Disponible"

#. module: sale_stock_renting
#: model:ir.model.fields,field_description:sale_stock_renting.field_sale_order_line__available_reserved_lots
msgid "Available Reserved Lots"
msgstr "Terrenos disponibles reservados"

#. module: sale_stock_renting
#. odoo-javascript
#: code:addons/sale_stock_renting/static/src/widgets/qty_at_date_widget.xml:0
msgid "Available for Rent"
msgstr "Disponible para alquilar"

#. module: sale_stock_renting
#: model:ir.model,name:sale_stock_renting.model_res_company
msgid "Companies"
msgstr "Empresas"

#. module: sale_stock_renting
#: model:ir.model,name:sale_stock_renting.model_res_config_settings
msgid "Config Settings"
msgstr "Ajustes de configuración"

#. module: sale_stock_renting
#: model_terms:ir.ui.view,arch_db:sale_stock_renting.res_config_settings_inherit_view_form
msgid "Default Padding Time"
msgstr "Tiempo predeterminado de espera "

#. module: sale_stock_renting
#: model:ir.model.fields,help:sale_stock_renting.field_rental_order_wizard_line__tracking
#: model:ir.model.fields,help:sale_stock_renting.field_sale_order_line__tracking
msgid "Ensure the traceability of a storable product in your warehouse."
msgstr "Asegure la trazabilidad de los productos almacenables en su almacén."

#. module: sale_stock_renting
#: model:ir.model.fields,field_description:sale_stock_renting.field_rental_order_wizard__has_lines_missing_stock
msgid "Has lines whose products have insufficient stock"
msgstr "Tiene líneas que incluyen existencias insuficientes de productos"

#. module: sale_stock_renting
#: model:ir.model.fields,field_description:sale_stock_renting.field_rental_order_wizard__has_tracked_lines
msgid "Has lines with tracked products"
msgstr "Tiene líneas que incluyen productos con seguimiento"

#. module: sale_stock_renting
#: model:ir.model.fields,field_description:sale_stock_renting.field_sale_rental_schedule__is_available
msgid "Is Available"
msgstr "Disponible"

#. module: sale_stock_renting
#: model:ir.model.fields,field_description:sale_stock_renting.field_rental_order_wizard_line__is_product_storable
msgid "Is Product Storable"
msgstr "Es un producto almacenable"

#. module: sale_stock_renting
#: model:ir.model,name:sale_stock_renting.model_account_move
msgid "Journal Entry"
msgstr "Asiento contable"

#. module: sale_stock_renting
#: model:ir.model,name:sale_stock_renting.model_stock_lot
msgid "Lot/Serial"
msgstr "Número de serie/lote"

#. module: sale_stock_renting
#: model:ir.model,name:sale_stock_renting.model_stock_warehouse_orderpoint
msgid "Minimum Inventory Rule"
msgstr "Regla de inventario mínimo"

#. module: sale_stock_renting
#: model_terms:ir.ui.view,arch_db:sale_stock_renting.res_config_settings_inherit_view_form
msgid "Minimum amount of time between two rentals"
msgstr "Tiempo mínimo entre dos alquileres"

#. module: sale_stock_renting
#. odoo-python
#: code:addons/sale_stock_renting/models/sale_order_line.py:0
msgid ""
"No valid quant has been found in location %(location)s for serial number "
"%(serial_number)s!"
msgstr ""
"No se ha encontró ninguna cantidad válida en la ubicación %(location)s para "
"el número de serie %(serial_number)s"

#. module: sale_stock_renting
#: model:ir.model.fields,field_description:sale_stock_renting.field_res_config_settings__padding_time
msgid "Padding"
msgstr "Espera"

#. module: sale_stock_renting
#: model:ir.model.fields,field_description:sale_stock_renting.field_res_company__padding_time
msgid "Padding Time"
msgstr "Tiempo de espera"

#. module: sale_stock_renting
#: model:ir.model,name:sale_stock_renting.model_rental_order_wizard
msgid "Pick-up/Return products"
msgstr "Recolección/Devolución de productos"

#. module: sale_stock_renting
#: model:ir.model.fields,field_description:sale_stock_renting.field_rental_order_wizard_line__pickeable_lot_ids
msgid "Pickeable Lot"
msgstr "Lote recolectable"

#. module: sale_stock_renting
#: model:ir.model.fields,field_description:sale_stock_renting.field_rental_order_wizard_line__pickedup_lot_ids
#: model:ir.model.fields,field_description:sale_stock_renting.field_sale_order_line__pickedup_lot_ids
msgid "Pickedup Lot"
msgstr "Lote recolectado"

#. module: sale_stock_renting
#. odoo-python
#: code:addons/sale_stock_renting/wizard/rental_processing.py:0
msgid "Please specify the serial numbers picked up for the tracked products."
msgstr ""
"Especifique los números de serie de los productos recolectados para su "
"seguimiento."

#. module: sale_stock_renting
#. odoo-python
#: code:addons/sale_stock_renting/wizard/rental_processing.py:0
msgid "Please specify the serial numbers returned for the tracked products."
msgstr ""
"Especifique los números de serie de los productos devueltos para su "
"seguimiento."

#. module: sale_stock_renting
#: model:ir.model,name:sale_stock_renting.model_product_template
msgid "Product"
msgstr "Producto"

#. module: sale_stock_renting
#: model:ir.model,name:sale_stock_renting.model_product_product
msgid "Product Variant"
msgstr "Variante del producto"

#. module: sale_stock_renting
#: model:stock.route,name:sale_stock_renting.route_rental
msgid "Rental"
msgstr "Alquiler"

#. module: sale_stock_renting
#: model:ir.model,name:sale_stock_renting.model_sale_rental_report
msgid "Rental Analysis Report"
msgstr "Reporte analítico de alquiler"

#. module: sale_stock_renting
#: model:ir.model.fields,field_description:sale_stock_renting.field_res_company__rental_loc_id
msgid "Rental Location"
msgstr "Ubicación de alquiler"

#. module: sale_stock_renting
#: model:ir.model,name:sale_stock_renting.model_sale_rental_schedule
msgid "Rental Schedule"
msgstr "Horario de alquiler"

#. module: sale_stock_renting
#: model_terms:ir.ui.view,arch_db:sale_stock_renting.res_config_settings_inherit_view_form
msgid "Rental Transfers"
msgstr "Transferencias de alquiler"

#. module: sale_stock_renting
#. odoo-python
#: code:addons/sale_stock_renting/models/sale_order_line.py:0
msgid "Rental move: %(order)s"
msgstr "Movimiento de alquiler: %(order)s"

#. module: sale_stock_renting
#: model:ir.model.fields,field_description:sale_stock_renting.field_res_config_settings__group_rental_stock_picking
msgid "Rental pickings"
msgstr "Recolecciones de alquier"

#. module: sale_stock_renting
#: model:ir.model,name:sale_stock_renting.model_rental_order_wizard_line
msgid "RentalOrderLine transient representation"
msgstr "Representación transitoria de RentalOrderLine"

#. module: sale_stock_renting
#: model:ir.model.fields,field_description:sale_stock_renting.field_sale_order_line__reserved_lot_ids
msgid "Reserved Lot"
msgstr "Lote reservado"

#. module: sale_stock_renting
#. odoo-javascript
#: code:addons/sale_stock_renting/static/src/widgets/qty_at_date_widget.xml:0
msgid "Reserved lots unavailable"
msgstr "Lotes reservados sin disponibilidad"

#. module: sale_stock_renting
#: model:ir.model,name:sale_stock_renting.model_stock_return_picking
msgid "Return Picking"
msgstr "Recolección de devolución"

#. module: sale_stock_renting
#: model:ir.model.fields,field_description:sale_stock_renting.field_rental_order_wizard_line__returnable_lot_ids
msgid "Returnable Lot"
msgstr "Lote de devolución"

#. module: sale_stock_renting
#: model:ir.model.fields,field_description:sale_stock_renting.field_rental_order_wizard_line__returned_lot_ids
#: model:ir.model.fields,field_description:sale_stock_renting.field_sale_order_line__returned_lot_ids
msgid "Returned Lot"
msgstr "Lote devuelto"

#. module: sale_stock_renting
#: model:ir.model,name:sale_stock_renting.model_sale_order
msgid "Sales Order"
msgstr "Orden de venta"

#. module: sale_stock_renting
#: model:ir.model,name:sale_stock_renting.model_sale_order_line
msgid "Sales Order Line"
msgstr "Línea de la orden de venta"

#. module: sale_stock_renting
#: model:ir.model.fields,field_description:sale_stock_renting.field_product_product__preparation_time
#: model:ir.model.fields,field_description:sale_stock_renting.field_product_template__preparation_time
msgid "Security Time"
msgstr "Tiempo de seguridad"

#. module: sale_stock_renting
#: model:ir.model.fields,field_description:sale_stock_renting.field_sale_rental_report__lot_id
#: model:ir.model.fields,field_description:sale_stock_renting.field_sale_rental_schedule__lot_id
#: model_terms:ir.ui.view,arch_db:sale_stock_renting.rental_report_search_view_inherit_lots
#: model_terms:ir.ui.view,arch_db:sale_stock_renting.rental_schedule_view_search_inherit_lots
msgid "Serial Number"
msgstr "Nº de serie"

#. module: sale_stock_renting
#: model_terms:ir.ui.view,arch_db:sale_stock_renting.rental_order_wizard_view_form
#: model_terms:ir.ui.view,arch_db:sale_stock_renting.report_rental_order_document
msgid "Serial Numbers"
msgstr "Números de serie"

#. module: sale_stock_renting
#: model:ir.model,name:sale_stock_renting.model_stock_move
msgid "Stock Move"
msgstr "Movimiento de stock"

#. module: sale_stock_renting
#: model:ir.model.fields,help:sale_stock_renting.field_product_product__preparation_time
#: model:ir.model.fields,help:sale_stock_renting.field_product_template__preparation_time
msgid "Temporarily make this product unavailable before pickup."
msgstr ""
"El producto no estará disponible de forma temporal antes de su recolección."

#. module: sale_stock_renting
#: model_terms:ir.ui.view,arch_db:sale_stock_renting.rental_schedule_view_gantt_inherit_stock
msgid "The product is not available during this period."
msgstr "El producto no está disponible durante este periodo. "

#. module: sale_stock_renting
#: model:ir.model.fields,help:sale_stock_renting.field_res_company__rental_loc_id
msgid ""
"This technical location serves as stock for products currently in rentalThis"
" location is internal because products in rentalare still considered as "
"company assets."
msgstr ""
"Esta ubicación técnica sirve como almacén para los productos que están en "
"alquiler. Esta ubicación es interna porque los productos en alquiler siguen "
"considerándose activos de la empresa."

#. module: sale_stock_renting
#: model:ir.model.fields,field_description:sale_stock_renting.field_rental_order_wizard_line__tracking
#: model:ir.model.fields,field_description:sale_stock_renting.field_sale_order_line__tracking
msgid "Tracking"
msgstr "Seguimiento"

#. module: sale_stock_renting
#. odoo-python
#: code:addons/sale_stock_renting/models/product_template.py:0
msgid ""
"Tracking by lots isn't supported for rental products.\n"
"You should rather change the tracking mode to unique serial numbers."
msgstr ""
"El seguimiento por lotes no es compatible con los productos de alquiler.\n"
"Es preferible cambiar el modo de seguimiento a números de serie únicos."

#. module: sale_stock_renting
#: model:ir.model.fields,field_description:sale_stock_renting.field_sale_order_line__unavailable_lot_ids
msgid "Unavailable Lot"
msgstr "Lote no disponible"

#. module: sale_stock_renting
#: model:res.groups,name:sale_stock_renting.group_rental_stock_picking
msgid "Use pickings for rental orders"
msgstr "Utilice las recolecciones para las órdenes de alquiler"

#. module: sale_stock_renting
#. odoo-javascript
#: code:addons/sale_stock_renting/static/src/widgets/qty_at_date_widget.xml:0
msgid "View Rentals"
msgstr "Ver alquileres"

#. module: sale_stock_renting
#: model:ir.model,name:sale_stock_renting.model_stock_warehouse
#: model:ir.model.fields,field_description:sale_stock_renting.field_sale_rental_schedule__warehouse_id
msgid "Warehouse"
msgstr "Almacén"

#. module: sale_stock_renting
#. odoo-python
#: code:addons/sale_stock_renting/models/sale_order_line.py:0
msgid "You cannot change the product of lines linked to stock moves."
msgstr ""
"No puede cambiar el producto de las líneas vinculadas a los movimientos de "
"existencias."

#. module: sale_stock_renting
#: model_terms:ir.ui.view,arch_db:sale_stock_renting.res_config_settings_inherit_view_form
msgid "hours"
msgstr "horas"

#. module: sale_stock_renting
#: model_terms:ir.ui.view,arch_db:sale_stock_renting.product_template_inherit_view_form_stock_rental
msgid "hours before orders"
msgstr "horas antes de las órdenes"

#. module: sale_stock_renting
#. odoo-javascript
#: code:addons/sale_stock_renting/static/src/widgets/qty_at_date_widget.xml:0
msgid "to"
msgstr "para"
