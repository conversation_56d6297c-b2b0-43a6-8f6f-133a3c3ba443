# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_accountant_batch_payment
# 
# Translators:
# <PERSON> <<EMAIL>>, 2024
# Wil Odoo, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-01-27 13:54+0000\n"
"PO-Revision-Date: 2024-09-25 09:43+0000\n"
"Last-Translator: Wil Odoo, 2025\n"
"Language-Team: Dutch (https://app.transifex.com/odoo/teams/41243/nl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: nl\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: account_accountant_batch_payment
#: model_terms:ir.ui.view,arch_db:account_accountant_batch_payment.view_account_batch_payment_rejection_form
msgid ""
"<br/>\n"
"                        <span>Do you want to cancel payments to retry them later or keep the batch open with unprocess payments, if you expect them later.</span>"
msgstr ""
"<br/>\n"
"                        <span>Wil je betalingen annuleren om deze later opnieuw te proberen, of wil je de batch openhouden met onverwerkte betalingen, als je ze later verwacht?</span>"

#. module: account_accountant_batch_payment
#: model_terms:ir.ui.view,arch_db:account_accountant_batch_payment.view_account_batch_payment_list_bank_rec_widget
msgid "Amount Due"
msgstr "Verschuldigd bedrag"

#. module: account_accountant_batch_payment
#: model_terms:ir.ui.view,arch_db:account_accountant_batch_payment.view_account_batch_payment_list_bank_rec_widget
msgid "Amount Due (in currency)"
msgstr "Verschuldigd bedrag (in valuta)"

#. module: account_accountant_batch_payment
#: model:ir.model,name:account_accountant_batch_payment.model_bank_rec_widget
msgid "Bank reconciliation widget for a single statement line"
msgstr "Bankafletterswidget voor een enkele afschriftregel"

#. module: account_accountant_batch_payment
#. odoo-python
#: code:addons/account_accountant_batch_payment/models/account_batch_payment.py:0
#: model:ir.model,name:account_accountant_batch_payment.model_account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_accountant_batch_payment.view_account_batch_payment_rejection_form
#: model_terms:ir.ui.view,arch_db:account_accountant_batch_payment.view_account_batch_payment_search_bank_rec_widget
msgid "Batch Payment"
msgstr "Batchbetaling"

#. module: account_accountant_batch_payment
#. odoo-javascript
#: code:addons/account_accountant_batch_payment/static/src/components/bank_reconciliation/bank_rec_form.xml:0
msgid "Batch Payments"
msgstr "Batchbetalingen"

#. module: account_accountant_batch_payment
#: model_terms:ir.ui.view,arch_db:account_accountant_batch_payment.view_account_batch_payment_rejection_form
msgid "Cancel"
msgstr "Annuleren"

#. module: account_accountant_batch_payment
#: model_terms:ir.ui.view,arch_db:account_accountant_batch_payment.view_account_batch_payment_rejection_form
msgid "Cancel Payments"
msgstr "Betalingen annuleren"

#. module: account_accountant_batch_payment
#: model:ir.model.fields,field_description:account_accountant_batch_payment.field_account_batch_payment_rejection__create_uid
msgid "Created by"
msgstr "Aangemaakt door"

#. module: account_accountant_batch_payment
#: model:ir.model.fields,field_description:account_accountant_batch_payment.field_account_batch_payment_rejection__create_date
msgid "Created on"
msgstr "Aangemaakt op"

#. module: account_accountant_batch_payment
#: model_terms:ir.ui.view,arch_db:account_accountant_batch_payment.view_account_batch_payment_search_bank_rec_widget
msgid "Date"
msgstr "Datum"

#. module: account_accountant_batch_payment
#: model:ir.model.fields,field_description:account_accountant_batch_payment.field_account_batch_payment_rejection__display_name
msgid "Display Name"
msgstr "Schermnaam"

#. module: account_accountant_batch_payment
#. odoo-python
#: code:addons/account_accountant_batch_payment/models/bank_rec_widget.py:0
msgid "Exchange Difference: %(batch_name)s - %(currency)s"
msgstr "Wisselkoersverschil: %(batch_name)s - %(currency)s"

#. module: account_accountant_batch_payment
#: model_terms:ir.ui.view,arch_db:account_accountant_batch_payment.view_account_batch_payment_rejection_form
msgid "Expect Payments Later"
msgstr "Verwacht betalingen later"

#. module: account_accountant_batch_payment
#: model:ir.model.fields,field_description:account_accountant_batch_payment.field_bank_rec_widget_line__flag
msgid "Flag"
msgstr "Vlag"

#. module: account_accountant_batch_payment
#: model:ir.model.fields,field_description:account_accountant_batch_payment.field_account_batch_payment_rejection__id
msgid "ID"
msgstr "ID"

#. module: account_accountant_batch_payment
#: model:ir.model.fields,field_description:account_accountant_batch_payment.field_account_batch_payment_rejection__in_reconcile_payment_ids
msgid "In Reconcile Payment"
msgstr "Betaling af te letteren"

#. module: account_accountant_batch_payment
#. odoo-python
#: code:addons/account_accountant_batch_payment/models/bank_rec_widget.py:0
msgid "Includes %(count)s payment(s)"
msgstr "Inclusief %(count)s betaling(en)"

#. module: account_accountant_batch_payment
#: model:ir.model.fields,field_description:account_accountant_batch_payment.field_account_batch_payment_rejection__write_uid
msgid "Last Updated by"
msgstr "Laatst bijgewerkt door"

#. module: account_accountant_batch_payment
#: model:ir.model.fields,field_description:account_accountant_batch_payment.field_account_batch_payment_rejection__write_date
msgid "Last Updated on"
msgstr "Laatst bijgewerkt op"

#. module: account_accountant_batch_payment
#: model:ir.model,name:account_accountant_batch_payment.model_bank_rec_widget_line
msgid "Line of the bank reconciliation widget"
msgstr "Regel van de bankafletterwidget"

#. module: account_accountant_batch_payment
#: model:ir.model,name:account_accountant_batch_payment.model_account_batch_payment_rejection
msgid "Manage the payment rejection from batch payments"
msgstr "De afwijzing van batchbetalingen beheren"

#. module: account_accountant_batch_payment
#: model:ir.model.fields,field_description:account_accountant_batch_payment.field_account_batch_payment_rejection__nb_batch_payment_ids
msgid "Nb Batch Payment"
msgstr "# Batchbetaling"

#. module: account_accountant_batch_payment
#: model:ir.model.fields,field_description:account_accountant_batch_payment.field_account_batch_payment_rejection__nb_rejected_payment_ids
msgid "Nb Rejected Payment"
msgstr "# Geweigerde betaling"

#. module: account_accountant_batch_payment
#: model_terms:ir.ui.view,arch_db:account_accountant_batch_payment.view_account_batch_payment_search_bank_rec_widget
msgid "Paid"
msgstr "Betaald"

#. module: account_accountant_batch_payment
#: model:ir.model,name:account_accountant_batch_payment.model_account_reconcile_model
msgid ""
"Preset to create journal entries during a invoices and payments matching"
msgstr ""
"Voorinstelling om boekingen aan te maken bij het afletteren van facturen met"
" betalingen"

#. module: account_accountant_batch_payment
#: model_terms:ir.ui.view,arch_db:account_accountant_batch_payment.view_account_batch_payment_search_bank_rec_widget
msgid "Received"
msgstr "Ontvangen"

#. module: account_accountant_batch_payment
#: model:ir.model.fields,field_description:account_accountant_batch_payment.field_account_batch_payment_rejection__rejected_payment_ids
msgid "Rejected Payment"
msgstr "Geweigerde betaling"

#. module: account_accountant_batch_payment
#: model:ir.model.fields,field_description:account_accountant_batch_payment.field_bank_rec_widget__selected_batch_payment_ids
msgid "Selected Batch Payment"
msgstr "Geselecteerde batchbetaling"

#. module: account_accountant_batch_payment
#: model:ir.model.fields,field_description:account_accountant_batch_payment.field_bank_rec_widget_line__source_batch_payment_id
msgid "Source Batch Payment"
msgstr "Bron batchbetaling"

#. module: account_accountant_batch_payment
#: model:ir.model.fields,field_description:account_accountant_batch_payment.field_bank_rec_widget_line__source_batch_payment_name
msgid "Source Batch Payment Name"
msgstr "Bron batchbetalingsnaam"

#. module: account_accountant_batch_payment
#: model_terms:ir.ui.view,arch_db:account_accountant_batch_payment.view_account_batch_payment_list_bank_rec_widget
msgid "Suggestions"
msgstr "Suggesties"

#. module: account_accountant_batch_payment
#: model_terms:ir.ui.view,arch_db:account_accountant_batch_payment.view_account_batch_payment_search_bank_rec_widget
msgid "Unreconciled"
msgstr "Onafgeletterd"

#. module: account_accountant_batch_payment
#: model_terms:ir.ui.view,arch_db:account_accountant_batch_payment.view_account_batch_payment_list_bank_rec_widget
msgid "View"
msgstr "Bekijk"

#. module: account_accountant_batch_payment
#: model_terms:ir.ui.view,arch_db:account_accountant_batch_payment.view_account_batch_payment_rejection_form
msgid "batches have been removed."
msgstr "batches zijn verwijderd."

#. module: account_accountant_batch_payment
#: model:ir.model.fields.selection,name:account_accountant_batch_payment.selection__bank_rec_widget_line__flag__new_batch
msgid "new_batch"
msgstr "nieuwe_partij"

#. module: account_accountant_batch_payment
#: model_terms:ir.ui.view,arch_db:account_accountant_batch_payment.view_account_batch_payment_rejection_form
msgid "payments from"
msgstr "betalingen van"

#. module: account_accountant_batch_payment
#: model_terms:ir.ui.view,arch_db:account_accountant_batch_payment.view_account_batch_payment_rejection_form
msgid "payments from the batch have been removed."
msgstr "betalingen van de batch zijn verwijderd."
