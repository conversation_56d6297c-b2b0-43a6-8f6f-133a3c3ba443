# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_accountant_batch_payment
# 
# Translators:
# jabiri7, 2024
# <PERSON><PERSON><PERSON>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON>, 2024
# <AUTHOR> <EMAIL>, 2024
# <PERSON>, 2024
# <AUTHOR> <EMAIL>, 2024
# <PERSON><PERSON>, 2024
# <PERSON>, 2024
# Wil Odoo, 2025
# Noemi <PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-01-27 13:54+0000\n"
"PO-Revision-Date: 2024-09-25 09:43+0000\n"
"Last-Translator: Noemi <PERSON>, 2025\n"
"Language-Team: Catalan (https://app.transifex.com/odoo/teams/41243/ca/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ca\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: account_accountant_batch_payment
#: model_terms:ir.ui.view,arch_db:account_accountant_batch_payment.view_account_batch_payment_rejection_form
msgid ""
"<br/>\n"
"                        <span>Do you want to cancel payments to retry them later or keep the batch open with unprocess payments, if you expect them later.</span>"
msgstr ""
"<br/>\n"
"                        <span>Voleu cancel·lar els pagaments per tornar-los a provar més tard o mantenir el lot obert amb els pagaments sense processar, si els espereu més tard.</span>"

#. module: account_accountant_batch_payment
#: model_terms:ir.ui.view,arch_db:account_accountant_batch_payment.view_account_batch_payment_list_bank_rec_widget
msgid "Amount Due"
msgstr "Import del deute"

#. module: account_accountant_batch_payment
#: model_terms:ir.ui.view,arch_db:account_accountant_batch_payment.view_account_batch_payment_list_bank_rec_widget
msgid "Amount Due (in currency)"
msgstr "Import pendent (en moneda)"

#. module: account_accountant_batch_payment
#: model:ir.model,name:account_accountant_batch_payment.model_bank_rec_widget
msgid "Bank reconciliation widget for a single statement line"
msgstr "Giny de conciliació bancària per a una única línia d'extractes"

#. module: account_accountant_batch_payment
#. odoo-python
#: code:addons/account_accountant_batch_payment/models/account_batch_payment.py:0
#: model:ir.model,name:account_accountant_batch_payment.model_account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_accountant_batch_payment.view_account_batch_payment_rejection_form
#: model_terms:ir.ui.view,arch_db:account_accountant_batch_payment.view_account_batch_payment_search_bank_rec_widget
msgid "Batch Payment"
msgstr "Remesa de pagaments"

#. module: account_accountant_batch_payment
#. odoo-javascript
#: code:addons/account_accountant_batch_payment/static/src/components/bank_reconciliation/bank_rec_form.xml:0
msgid "Batch Payments"
msgstr "Pagaments per lots"

#. module: account_accountant_batch_payment
#: model_terms:ir.ui.view,arch_db:account_accountant_batch_payment.view_account_batch_payment_rejection_form
msgid "Cancel"
msgstr "Cancel·la"

#. module: account_accountant_batch_payment
#: model_terms:ir.ui.view,arch_db:account_accountant_batch_payment.view_account_batch_payment_rejection_form
msgid "Cancel Payments"
msgstr "Cancel·la els pagaments"

#. module: account_accountant_batch_payment
#: model:ir.model.fields,field_description:account_accountant_batch_payment.field_account_batch_payment_rejection__create_uid
msgid "Created by"
msgstr "Creat per"

#. module: account_accountant_batch_payment
#: model:ir.model.fields,field_description:account_accountant_batch_payment.field_account_batch_payment_rejection__create_date
msgid "Created on"
msgstr "Creat el"

#. module: account_accountant_batch_payment
#: model_terms:ir.ui.view,arch_db:account_accountant_batch_payment.view_account_batch_payment_search_bank_rec_widget
msgid "Date"
msgstr "Data"

#. module: account_accountant_batch_payment
#: model:ir.model.fields,field_description:account_accountant_batch_payment.field_account_batch_payment_rejection__display_name
msgid "Display Name"
msgstr "Nom mostrat"

#. module: account_accountant_batch_payment
#. odoo-python
#: code:addons/account_accountant_batch_payment/models/bank_rec_widget.py:0
msgid "Exchange Difference: %(batch_name)s - %(currency)s"
msgstr "Diferència de canvi: %(batch_name)s - %(currency)s"

#. module: account_accountant_batch_payment
#: model_terms:ir.ui.view,arch_db:account_accountant_batch_payment.view_account_batch_payment_rejection_form
msgid "Expect Payments Later"
msgstr "Espera pagaments més tard"

#. module: account_accountant_batch_payment
#: model:ir.model.fields,field_description:account_accountant_batch_payment.field_bank_rec_widget_line__flag
msgid "Flag"
msgstr "Marcar"

#. module: account_accountant_batch_payment
#: model:ir.model.fields,field_description:account_accountant_batch_payment.field_account_batch_payment_rejection__id
msgid "ID"
msgstr "ID"

#. module: account_accountant_batch_payment
#: model:ir.model.fields,field_description:account_accountant_batch_payment.field_account_batch_payment_rejection__in_reconcile_payment_ids
msgid "In Reconcile Payment"
msgstr "En el pagament de conciliació"

#. module: account_accountant_batch_payment
#. odoo-python
#: code:addons/account_accountant_batch_payment/models/bank_rec_widget.py:0
msgid "Includes %(count)s payment(s)"
msgstr "Inclou %(count)s pagament(s)"

#. module: account_accountant_batch_payment
#: model:ir.model.fields,field_description:account_accountant_batch_payment.field_account_batch_payment_rejection__write_uid
msgid "Last Updated by"
msgstr "Última actualització per"

#. module: account_accountant_batch_payment
#: model:ir.model.fields,field_description:account_accountant_batch_payment.field_account_batch_payment_rejection__write_date
msgid "Last Updated on"
msgstr "Última actualització el"

#. module: account_accountant_batch_payment
#: model:ir.model,name:account_accountant_batch_payment.model_bank_rec_widget_line
msgid "Line of the bank reconciliation widget"
msgstr "Giny de línia de conciliació bancària"

#. module: account_accountant_batch_payment
#: model:ir.model,name:account_accountant_batch_payment.model_account_batch_payment_rejection
msgid "Manage the payment rejection from batch payments"
msgstr "Gestiona el rebuig de pagament dels pagaments per lots"

#. module: account_accountant_batch_payment
#: model:ir.model.fields,field_description:account_accountant_batch_payment.field_account_batch_payment_rejection__nb_batch_payment_ids
msgid "Nb Batch Payment"
msgstr "Pagament per lots Nb"

#. module: account_accountant_batch_payment
#: model:ir.model.fields,field_description:account_accountant_batch_payment.field_account_batch_payment_rejection__nb_rejected_payment_ids
msgid "Nb Rejected Payment"
msgstr "Nb Pagament rebutjat"

#. module: account_accountant_batch_payment
#: model_terms:ir.ui.view,arch_db:account_accountant_batch_payment.view_account_batch_payment_search_bank_rec_widget
msgid "Paid"
msgstr "Pagat"

#. module: account_accountant_batch_payment
#: model:ir.model,name:account_accountant_batch_payment.model_account_reconcile_model
msgid ""
"Preset to create journal entries during a invoices and payments matching"
msgstr ""
"Preajust per crear assentaments durant la conciliació de factures i "
"pagaments"

#. module: account_accountant_batch_payment
#: model_terms:ir.ui.view,arch_db:account_accountant_batch_payment.view_account_batch_payment_search_bank_rec_widget
msgid "Received"
msgstr "Rebuda"

#. module: account_accountant_batch_payment
#: model:ir.model.fields,field_description:account_accountant_batch_payment.field_account_batch_payment_rejection__rejected_payment_ids
msgid "Rejected Payment"
msgstr "Pagament rebutjat"

#. module: account_accountant_batch_payment
#: model:ir.model.fields,field_description:account_accountant_batch_payment.field_bank_rec_widget__selected_batch_payment_ids
msgid "Selected Batch Payment"
msgstr "Pagament per lots seleccionat"

#. module: account_accountant_batch_payment
#: model:ir.model.fields,field_description:account_accountant_batch_payment.field_bank_rec_widget_line__source_batch_payment_id
msgid "Source Batch Payment"
msgstr "Pagament per lots font"

#. module: account_accountant_batch_payment
#: model:ir.model.fields,field_description:account_accountant_batch_payment.field_bank_rec_widget_line__source_batch_payment_name
msgid "Source Batch Payment Name"
msgstr "Nom de la font del pagament per lots"

#. module: account_accountant_batch_payment
#: model_terms:ir.ui.view,arch_db:account_accountant_batch_payment.view_account_batch_payment_list_bank_rec_widget
msgid "Suggestions"
msgstr "Suggeriments"

#. module: account_accountant_batch_payment
#: model_terms:ir.ui.view,arch_db:account_accountant_batch_payment.view_account_batch_payment_search_bank_rec_widget
msgid "Unreconciled"
msgstr "No conciliat"

#. module: account_accountant_batch_payment
#: model_terms:ir.ui.view,arch_db:account_accountant_batch_payment.view_account_batch_payment_list_bank_rec_widget
msgid "View"
msgstr "Vista"

#. module: account_accountant_batch_payment
#: model_terms:ir.ui.view,arch_db:account_accountant_batch_payment.view_account_batch_payment_rejection_form
msgid "batches have been removed."
msgstr "S'han eliminat els lots."

#. module: account_accountant_batch_payment
#: model:ir.model.fields.selection,name:account_accountant_batch_payment.selection__bank_rec_widget_line__flag__new_batch
msgid "new_batch"
msgstr "new_batch"

#. module: account_accountant_batch_payment
#: model_terms:ir.ui.view,arch_db:account_accountant_batch_payment.view_account_batch_payment_rejection_form
msgid "payments from"
msgstr "pagaments de"

#. module: account_accountant_batch_payment
#: model_terms:ir.ui.view,arch_db:account_accountant_batch_payment.view_account_batch_payment_rejection_form
msgid "payments from the batch have been removed."
msgstr "S'han eliminat els pagaments per lots."
