<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!--<=================form view================>-->
        <record id="view_property_reservation_form" model="ir.ui.view">
            <field name="name">property.reservation.form</field>
            <field name="model">property.reservation</field>
            <field name="arch" type="xml">
                <form>
                    <header>
                        <button string="Confirm" type="object" name="action_confirm" class="oe_highlight"
                                invisible="state != 'new'"/>
                        <button string="Start Contract" type="object" class="oe_highlight" name="action_start_contract"
                                invisible="state != 'confirmed'"/>
                        <field name="state" widget="statusbar" nolabel="1"
                               statusbar_visible="new,confirmed,contract_started"/>
                    </header>
                    <sheet>
                        <div class="oe_button_box" name="button_box">
                            <button string="View Contract" type="object" class="oe_stat_button"
                                    icon="fa-bars" name="action_open_contract"
                                    invisible="contract_id == False">
                            </button>
                        </div>
                        <label for="name" string="Reservation Reference"/>
                        <div class="oe_title">
                            <h2>
                                <field name="name" class="oe_inline" readonly="1"/>
                            </h2>
                        </div>
                        <group>
                            <group string="Tenant">
                                <field name="contract_id" invisible="1"/>
                                <field name="tenant_id"
                                       readonly="state in ['confirmed' , 'contract_started']"/>
                                <label for="street" string="Address"/>
                                <div class="o_address_format">
                                    <field name="street" placeholder="Street..." class="o_address_street"/>
                                    <field name="street2" placeholder="Street 2..." class="o_address_street"/>
                                    <field name="city" placeholder="City" class="o_address_city"/>
                                    <field name="country_id" placeholder="Country" class="o_address_country"
                                           options='{"no_open": True, "no_create": True}'/>
                                </div>
                                <field name="mobile"/>
                                <field name="email"/>
                                <field name="company_id" readonly="1"/>
                            </group>
                            <group string="Date and Property">
                                <field name="building_id"
                                       options="{'no_create': True, 'no_create_edit':True}"
                                       readonly="state in ['contract_started']"/>
                                <field name="res_floor_id" widget="many2many_tags"
                                       options="{'no_create': True, 'no_create_edit':True}"
                                       readonly="state in ['contract_started']"/>
                                <field name="res_property_id" widget="many2many_tags"
                                       options="{'no_create': True, 'no_create_edit':True}"
                                       readonly="state in ['contract_started']"/>
                                <field name="amenity_ids" widget="many2many_tags"
                                       readonly="state in ['contract_started']"/>
                                <field name="start_date"
                                       readonly="state in ['confirmed' , 'contract_started']"/>
                                <field name="end_date"
                                       readonly="state in ['confirmed' , 'contract_started']"/>
                                <field name="total_months"/>
                                <field name="payment_frequency"
                                       readonly="state in ['contract_started']"/>
                            </group>
                        </group>
                        <group>
                            <group string="Payment Plan">
                                <field name="rental_amount"
                                       readonly="state in ['contract_started']" invisible="1"/>
                                <field name="other_charges_total"
                                       invisible="1"/>
                                <field name="discount_mode" readonly="1"
                                       groups="base_property.group_base_rental_discount_user"
                                       invisible="1"/>
                                <field name="discount" readonly="state in ['contract_started']"
                                       groups="base_property.group_base_rental_discount_user"/>
                                <field name="net_rent"/>
                            </group>
                            <group string="Total Values and Bills">
                                <field name="balance"
                                       readonly="state in ['confirmed' , 'contract_started']"/>
                                <field name="total_contract_amount"
                                       readonly="state in ['confirmed' , 'contract_started']"/>
                                <field name="electricity_bill" readonly="state != 'new'"/>
                                <field name="electricity_last_reading"
                                       readonly="state != 'new'"
                                       invisible="electricity_bill != True"/>
                                <field name="electricity_current_reading"
                                       invisible="electricity_bill != True" readonly="0"/>
                                <field name="water_bill" readonly="state != 'new'"/>
                                <field name="water_last_reading"
                                       invisible="water_bill != True"
                                       readonly="state != 'new'"/>
                                <field name="water_current_reading"
                                       invisible="water_bill != True" readonly="0"/>
                                <field name="user_id" invisible="1"/>
                            </group>
                        </group>
                        <notebook>
                            <page string="Other Information" name="other_information">
                                <sheet>
                                    <field name="other_info" string="Other Info"
                                           readonly="state == 'contract_started'"/>
                                </sheet>
                            </page>
                        </notebook>
                        <group>
                            <field name="term_and_condition" readonly="state == 'contract_started'"/>
                        </group>
                    </sheet>
                    <chatter/>
                </form>
            </field>
        </record>

        <!--<=================tree view================>-->
        <record id="view_property_reservation_tree" model="ir.ui.view">
            <field name="name">property.reservation.tree</field>
            <field name="model">property.reservation</field>
            <field name="arch" type="xml">
                <list>
                    <field name="name"/>
                    <field name="tenant_id"/>
                    <field name="mobile"/>
                    <field name="email"/>
                    <field name="start_date"/>
                    <field name="end_date"/>
                    <field name="rental_amount"/>
                    <field name="other_charges_total" invisible="1"/>
                    <field name="discount"/>
                    <field name="net_rent"/>
                    <field name="state"/>
                    <field name="id" invisible="1"/>
                </list>
            </field>
        </record>

        <!--<=================Search View================>-->
        <record id="search_contract_reservation_view" model="ir.ui.view">
            <field name="name">property.reservation.search</field>
            <field name="model">property.reservation</field>
            <field name="arch" type="xml">
                <search>
                    <field name="name"/>
                    <field name="tenant_id"/>
                    <field name="mobile"/>
                    <field name="email"/>
                    <field name="building_id"/>
                    <field name="res_floor_id"/>
                    <field name="res_property_id"/>
                    <field name="start_date"/>
                    <field name="end_date"/>
                    <field name="electricity_bill"/>
                    <field name="water_bill"/>
                    <field name="state"/>
                    <group expand="0" string="Group By">
                        <filter string="State" name="group_by_state"
                                context="{'group_by': 'state'}"/>
                        <filter string="Tenant" name="group_by_tenant_id"
                                context="{'group_by': 'tenant_id'}"/>
                        <filter string="Building" name="group_by_building_id"
                                context="{'group_by': 'building_id'}"/>
                        <filter string="Floor" name="group_by_res_floor_id"
                                context="{'group_by': 'res_floor_id'}"/>
                        <filter string="Property" name="group_by_res_property_id"
                                context="{'group_by': 'res_property_id'}"/>
                        <filter string="Electricity Bill" name="group_by_electricity_bill"
                                context="{'group_by': 'electricity_bill'}"/>
                        <filter string="Water Bill" name="group_by_water_bill"
                                context="{'group_by': 'water_bill'}"/>
                        <filter string="Rental Amount" name="group_by_rental_amount"
                                context="{'group_by': 'rental_amount'}"/>
                        <filter string="Discount" name="group_by_discount"
                                context="{'group_by': 'discount'}"/>
                        <filter string="Net Rent" name="group_by_net_rent"
                                context="{'group_by': 'net_rent'}"/>
                    </group>
                </search>
            </field>
        </record>

        <!--<=================Action view================>-->
        <record id="action_property_reservation" model="ir.actions.act_window">
            <field name="name">Property Reservations</field>
            <field name="res_model">property.reservation</field>
            <field name="view_mode">list,form,search</field>
            <field name="help" type="html">
                <p class="oe_view_nocontent_create">
                    Click to create a new property reservation.
                </p>
            </field>
        </record>

    </data>
</odoo>
