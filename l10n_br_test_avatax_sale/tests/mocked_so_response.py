# Part of Odoo. See LICENSE file for full copyright and licensing details.
def generate_response(sale_order_line_ids):
    assert len(sale_order_line_ids) == 4, "the mocked response is for 4 lines"
    # negative lines are distributed to positive lines and not sent to avatax
    for i, line in enumerate(RESPONSE['lines']):
        line['lineCode'] = sale_order_line_ids[i].id
    return RESPONSE


# To reduce the size of this mocked response it was trimmed as follows:
# del response['header']['additionalInfo']
# for line in response['input']['lines'] + response['lines']:
#     del line['warnings']
#     for details in line.get('taxDetails', []):
#         details.pop('citation', None)
#         details.pop('citationId', None)
RESPONSE = {'header': {'accountId': 'c5235ce3-f699-4eaf-a203-e5ce22ecf1e6',
            'amountCalcType': 'gross',
            'companyLocation': '**************',
            'documentCode': 'sale.order_65',
            'eDocCreatorPerspective': True,
            'eDocCreatorType': 'self',
            'goods': {'class': 'VENDA PRODUTO À NAO CONTRIBUINTE DE ICMS',
                      'tpImp': '1'},
            'locations': {'entity': {'activitySector': {'code': 'false'},
                                     'address': {'cityCode': 4318705,
                                                 'cityName': 'São Leopoldo',
                                                 'country': 'BRA',
                                                 'countryCode': '0',
                                                 'state': 'RS',
                                                 'zipcode': '93022-718'},
                                     'federalTaxId': '',
                                     'suframa': '',
                                     'taxRegime': 'individual',
                                     'taxesSettings': {'icmsTaxPayer': False},
                                     'type': 'individual'},
                          'establishment': {'activitySector': {'code': 'false'},
                                            'address': {'cityCode': 4106902,
                                                        'cityName': 'Curitiba',
                                                        'country': 'BRA',
                                                        'countryCode': '0',
                                                        'state': 'PR',
                                                        'zipcode': '80010-010'},
                                            'federalTaxId': 'false',
                                            'suframa': '',
                                            'taxRegime': 'individual',
                                            'taxesSettings': {'icmsTaxPayer': False,
                                                              'pisCofinsIcmsTaxCreditReliefMode': {'icms': True,
                                                                                                   'icmsFcp': True},
                                                              'pisCofinsIcmsTaxReliefMode': {'icms': True,
                                                                                             'icmsFcp': True}},
                                            'type': 'business'}},
                       'messageType': 'goods',
                       'subscriptionId': '4655acb1-d6a3-4d0f-bcc0-e7dd3074447d',
                       'transactionDate': '2021-01-01T00:00:00'},
            'input': {'header': {'accountId': 'c5235ce3-f699-4eaf-a203-e5ce22ecf1e6',
                      'amountCalcType': 'gross',
                      'companyLocation': '**************',
                      'documentCode': 'sale.order_65',
                      'eDocCreatorPerspective': True,
                      'eDocCreatorType': 'self',
                      'goods': {},
                      'locations': {'entity': {'activitySector': {'code': 'false'},
                                               'address': {'cityCode': 4318705,
                                                           'cityName': 'São '
                                                                       'Leopoldo',
                                                           'country': 'BRA',
                                                           'countryCode': '0',
                                                           'state': 'RS',
                                                           'zipcode': '93022-718'},
                                               'federalTaxId': '',
                                               'suframa': '',
                                               'taxRegime': 'individual',
                                               'taxesSettings': {'icmsTaxPayer': False},
                                               'type': 'individual'},
                                    'establishment': {'activitySector': {'code': 'false'},
                                                      'address': {'cityCode': 4106902,
                                                                  'cityName': 'Curitiba',
                                                                  'country': 'BRA',
                                                                  'countryCode': '0',
                                                                  'state': 'PR',
                                                                  'zipcode': '80010-010'},
                                                      'federalTaxId': 'false',
                                                      'suframa': '',
                                                      'taxRegime': 'individual',
                                                      'taxesSettings': {'icmsTaxPayer': False,
                                                                        'pisCofinsIcmsTaxCreditReliefMode': {'icms': True,
                                                                                                             'icmsFcp': True},
                                                                        'pisCofinsIcmsTaxReliefMode': {'icms': True,
                                                                                                       'icmsFcp': True}},
                                                      'type': 'business'}},
                      'messageType': 'goods',
                      'subscriptionId': '4655acb1-d6a3-4d0f-bcc0-e7dd3074447d',
                      'transactionDate': '2021-01-01T00:00:00'},
           'lines': [{'freightAmount': 0,
                      'goods': {},
                      'insuranceAmount': 0,
                      'itemDescriptor': {'cest': '',
                                         'hsCode': '4901.10.00',
                                         'productType': 'FOR PRODUCT',
                                         'source': '0'},
                      'lineAmount': 35,
                      'lineCode': 265,
                      'lineTaxedDiscount': 0,
                      'numberOfItems': 0,
                      'operationType': 'standardSales',
                      'otherCostAmount': 0,
                      'overwrite': 'no',
                      'useType': 'use or consumption'},
                     {'freightAmount': 0,
                      'goods': {},
                      'insuranceAmount': 0,
                      'itemDescriptor': {'cest': '',
                                         'hsCode': '4901.10.00',
                                         'productType': 'FOR PRODUCT',
                                         'source': '0'},
                      'lineAmount': 30,
                      'lineCode': 266,
                      'lineTaxedDiscount': 0,
                      'numberOfItems': 0,
                      'operationType': 'standardSales',
                      'otherCostAmount': 0,
                      'overwrite': 'no',
                      'useType': 'use or consumption'},
                     {'freightAmount': 0,
                      'goods': {},
                      'insuranceAmount': 0,
                      'itemDescriptor': {'cest': '',
                                         'hsCode': '4901.10.00',
                                         'productType': 'FOR PRODUCT',
                                         'source': '0'},
                      'lineAmount': 15,
                      'lineCode': 267,
                      'lineTaxedDiscount': 0,
                      'numberOfItems': 0,
                      'operationType': 'standardSales',
                      'otherCostAmount': 0,
                      'overwrite': 'no',
                      'useType': 'use or consumption'},
                     {'freightAmount': 0,
                      'goods': {},
                      'insuranceAmount': 0,
                      'itemDescriptor': {'cest': '',
                                         'hsCode': '4901.10.00',
                                         'productType': 'FOR PRODUCT',
                                         'source': '0'},
                      'lineAmount': 15,
                      'lineCode': 268,
                      'lineTaxedDiscount': 0,
                      'numberOfItems': 0,
                      'operationType': 'standardSales',
                      'otherCostAmount': 0,
                      'overwrite': 'no',
                      'useType': 'use or consumption'}],
           'version': '3'},
            'lines': [{'cfop': 6107,
            'freightAmount': 0,
            'goods': {'entityIcmsStSubstitute': 'default',
                      'subjectToIPIonInbound': False},
            'insuranceAmount': 0,
            'itemDescriptor': {'cest': '',
                               'hsCode': '4901.10.00',
                               'productType': 'FOR PRODUCT',
                               'source': '0'},
            'lineAdditionalInfo': '',
            'lineAmount': 35,
            'lineCode': 265,
            'lineNetFigure': 28.88,
            'lineTaxedDiscount': 0,
            'lineUnitPrice': 35,
            'numberOfItems': 1,
            'operationType': 'standardSales',
            'otherCostAmount': 0,
            'overwrite': 'no',
            'taxDetails': [{'exemptionCode': '',
                            'isCustomCitation': False,
                            'jurisdictionName': 'Brazil',
                            'jurisdictionType': 'Country',
                            'rate': 13.45,
                            'subtotalTaxable': 35,
                            'tax': 4.71,
                            'taxImpact': {'accounting': 'none',
                                          'impactOnFinalPrice': 'Informative',
                                          'impactOnNetAmount': 'Informative'},
                            'taxType': 'aproxtribFed'},
                           {'exemptionCode': '',
                            'jurisdictionName': 'Brazil',
                            'jurisdictionType': 'Country',
                            'rate': 19,
                            'subtotalTaxable': 35,
                            'tax': 6.65,
                            'taxImpact': {'accounting': 'none',
                                          'impactOnFinalPrice': 'Informative',
                                          'impactOnNetAmount': 'Informative'},
                            'taxType': 'aproxtribState'},
                           {'calcMode': 'rate',
                            'cst': '06',
                            'exemptionCode': '',
                            'isCustomCitation': False,
                            'jurisdictionName': 'Brazil',
                            'jurisdictionType': 'Country',
                            'rate': 0,
                            'subtotalTaxable': 35,
                            'tax': 0,
                            'taxImpact': {'accounting': 'none',
                                          'impactOnFinalPrice': 'Included',
                                          'impactOnNetAmount': 'Included'},
                            'taxType': 'cofins'},
                           {'cst': '00',
                            'exemptionCode': '',
                            'isCustomCitation': False,
                            'jurisdictionName': 'Paraná',
                            'jurisdictionType': 'State',
                            'modBC': '3',
                            'rate': 12,
                            'source': '0',
                            'subtotalTaxable': 35,
                            'tax': 4.2,
                            'taxImpact': {'accounting': 'liability',
                                          'impactOnFinalPrice': 'Included',
                                          'impactOnNetAmount': 'Included'},
                            'taxType': 'icms'},
                           {'exemptionCode': '',
                            'icmsInterPartRate': 100,
                            'isCustomCitation': False,
                            'jurisdictionName': 'Rio Grande do Sul',
                            'jurisdictionType': 'State',
                            'rate': 17.5,
                            'subtotalTaxable': 35,
                            'tax': 1.92,
                            'taxImpact': {'accounting': 'liability',
                                          'impactOnFinalPrice': 'Included',
                                          'impactOnNetAmount': 'Included'},
                            'taxType': 'icmsDifaDest'},
                           {'exemptionCode': '',
                            'isCustomCitation': False,
                            'jurisdictionName': 'Paraná',
                            'jurisdictionType': 'State',
                            'rate': 12,
                            'subtotalTaxable': 35,
                            'tax': 0,
                            'taxImpact': {'accounting': 'liability',
                                          'impactOnFinalPrice': 'Included',
                                          'impactOnNetAmount': 'Included'},
                            'taxType': 'icmsDifaRemet'},
                           {'calcMode': 'rate',
                            'cst': '53',
                            'exemptionCode': '',
                            'isCustomCitation': False,
                            'jurisdictionName': 'Brazil',
                            'jurisdictionType': 'Country',
                            'legalTaxClass': 999,
                            'rate': 0,
                            'subtotalTaxable': 35,
                            'tax': 0,
                            'taxImpact': {'accounting': 'none',
                                          'impactOnFinalPrice': 'Informative',
                                          'impactOnNetAmount': 'Informative'},
                            'taxType': 'ipi',
                            'traceCode': 'C005'},
                           {'calcMode': 'rate',
                            'cst': '06',
                            'exemptionCode': '',
                            'isCustomCitation': False,
                            'jurisdictionName': 'Brazil',
                            'jurisdictionType': 'Country',
                            'rate': 0,
                            'subtotalTaxable': 35,
                            'tax': 0,
                            'taxImpact': {'accounting': 'none',
                                          'impactOnFinalPrice': 'Included',
                                          'impactOnNetAmount': 'Included'},
                            'taxType': 'pis'}],
            'useType': 'use or consumption'},
           {'cfop': 6107,
            'freightAmount': 0,
            'goods': {'entityIcmsStSubstitute': 'default',
                      'subjectToIPIonInbound': False},
            'insuranceAmount': 0,
            'itemDescriptor': {'cest': '',
                               'hsCode': '4901.10.00',
                               'productType': 'FOR PRODUCT',
                               'source': '0'},
            'lineAdditionalInfo': '',
            'lineAmount': 30,
            'lineCode': 266,
            'lineNetFigure': 24.75,
            'lineTaxedDiscount': 0,
            'lineUnitPrice': 30,
            'numberOfItems': 1,
            'operationType': 'standardSales',
            'otherCostAmount': 0,
            'overwrite': 'no',
            'taxDetails': [{'exemptionCode': '',
                            'isCustomCitation': False,
                            'jurisdictionName': 'Brazil',
                            'jurisdictionType': 'Country',
                            'rate': 13.45,
                            'subtotalTaxable': 30,
                            'tax': 4.03,
                            'taxImpact': {'accounting': 'none',
                                          'impactOnFinalPrice': 'Informative',
                                          'impactOnNetAmount': 'Informative'},
                            'taxType': 'aproxtribFed'},
                           {'exemptionCode': '',
                            'jurisdictionName': 'Brazil',
                            'jurisdictionType': 'Country',
                            'rate': 19,
                            'subtotalTaxable': 30,
                            'tax': 5.7,
                            'taxImpact': {'accounting': 'none',
                                          'impactOnFinalPrice': 'Informative',
                                          'impactOnNetAmount': 'Informative'},
                            'taxType': 'aproxtribState'},
                           {'calcMode': 'rate',
                            'cst': '06',
                            'exemptionCode': '',
                            'isCustomCitation': False,
                            'jurisdictionName': 'Brazil',
                            'jurisdictionType': 'Country',
                            'rate': 0,
                            'subtotalTaxable': 30,
                            'tax': 0,
                            'taxImpact': {'accounting': 'none',
                                          'impactOnFinalPrice': 'Included',
                                          'impactOnNetAmount': 'Included'},
                            'taxType': 'cofins'},
                           {'cst': '00',
                            'exemptionCode': '',
                            'isCustomCitation': False,
                            'jurisdictionName': 'Paraná',
                            'jurisdictionType': 'State',
                            'modBC': '3',
                            'rate': 12,
                            'source': '0',
                            'subtotalTaxable': 30,
                            'tax': 3.6,
                            'taxImpact': {'accounting': 'liability',
                                          'impactOnFinalPrice': 'Included',
                                          'impactOnNetAmount': 'Included'},
                            'taxType': 'icms'},
                           {'exemptionCode': '',
                            'icmsInterPartRate': 100,
                            'isCustomCitation': False,
                            'jurisdictionName': 'Rio Grande do Sul',
                            'jurisdictionType': 'State',
                            'rate': 17.5,
                            'subtotalTaxable': 30,
                            'tax': 1.65,
                            'taxImpact': {'accounting': 'liability',
                                          'impactOnFinalPrice': 'Included',
                                          'impactOnNetAmount': 'Included'},
                            'taxType': 'icmsDifaDest'},
                           {'exemptionCode': '',
                            'isCustomCitation': False,
                            'jurisdictionName': 'Paraná',
                            'jurisdictionType': 'State',
                            'rate': 12,
                            'subtotalTaxable': 30,
                            'tax': 0,
                            'taxImpact': {'accounting': 'liability',
                                          'impactOnFinalPrice': 'Included',
                                          'impactOnNetAmount': 'Included'},
                            'taxType': 'icmsDifaRemet'},
                           {'calcMode': 'rate',
                            'cst': '53',
                            'exemptionCode': '',
                            'isCustomCitation': False,
                            'jurisdictionName': 'Brazil',
                            'jurisdictionType': 'Country',
                            'legalTaxClass': 999,
                            'rate': 0,
                            'subtotalTaxable': 30,
                            'tax': 0,
                            'taxImpact': {'accounting': 'none',
                                          'impactOnFinalPrice': 'Informative',
                                          'impactOnNetAmount': 'Informative'},
                            'taxType': 'ipi',
                            'traceCode': 'C005'},
                           {'calcMode': 'rate',
                            'cst': '06',
                            'exemptionCode': '',
                            'isCustomCitation': False,
                            'jurisdictionName': 'Brazil',
                            'jurisdictionType': 'Country',
                            'rate': 0,
                            'subtotalTaxable': 30,
                            'tax': 0,
                            'taxImpact': {'accounting': 'none',
                                          'impactOnFinalPrice': 'Included',
                                          'impactOnNetAmount': 'Included'},
                            'taxType': 'pis'}],
            'useType': 'use or consumption'},
           {'cfop': 6107,
            'freightAmount': 0,
            'goods': {'entityIcmsStSubstitute': 'default',
                      'subjectToIPIonInbound': False},
            'insuranceAmount': 0,
            'itemDescriptor': {'cest': '',
                               'hsCode': '4901.10.00',
                               'productType': 'FOR PRODUCT',
                               'source': '0'},
            'lineAdditionalInfo': '',
            'lineAmount': 15,
            'lineCode': 267,
            'lineNetFigure': 12.38,
            'lineTaxedDiscount': 0,
            'lineUnitPrice': 15,
            'numberOfItems': 1,
            'operationType': 'standardSales',
            'otherCostAmount': 0,
            'overwrite': 'no',
            'taxDetails': [{'exemptionCode': '',
                            'isCustomCitation': False,
                            'jurisdictionName': 'Brazil',
                            'jurisdictionType': 'Country',
                            'rate': 13.45,
                            'subtotalTaxable': 15,
                            'tax': 2.02,
                            'taxImpact': {'accounting': 'none',
                                          'impactOnFinalPrice': 'Informative',
                                          'impactOnNetAmount': 'Informative'},
                            'taxType': 'aproxtribFed'},
                           {'exemptionCode': '',
                            'jurisdictionName': 'Brazil',
                            'jurisdictionType': 'Country',
                            'rate': 19,
                            'subtotalTaxable': 15,
                            'tax': 2.85,
                            'taxImpact': {'accounting': 'none',
                                          'impactOnFinalPrice': 'Informative',
                                          'impactOnNetAmount': 'Informative'},
                            'taxType': 'aproxtribState'},
                           {'calcMode': 'rate',
                            'cst': '06',
                            'exemptionCode': '',
                            'isCustomCitation': False,
                            'jurisdictionName': 'Brazil',
                            'jurisdictionType': 'Country',
                            'rate': 0,
                            'subtotalTaxable': 15,
                            'tax': 0,
                            'taxImpact': {'accounting': 'none',
                                          'impactOnFinalPrice': 'Included',
                                          'impactOnNetAmount': 'Included'},
                            'taxType': 'cofins'},
                           {'cst': '00',
                            'exemptionCode': '',
                            'isCustomCitation': False,
                            'jurisdictionName': 'Paraná',
                            'jurisdictionType': 'State',
                            'modBC': '3',
                            'rate': 12,
                            'source': '0',
                            'subtotalTaxable': 15,
                            'tax': 1.8,
                            'taxImpact': {'accounting': 'liability',
                                          'impactOnFinalPrice': 'Included',
                                          'impactOnNetAmount': 'Included'},
                            'taxType': 'icms'},
                           {'exemptionCode': '',
                            'icmsInterPartRate': 100,
                            'isCustomCitation': False,
                            'jurisdictionName': 'Rio Grande do Sul',
                            'jurisdictionType': 'State',
                            'rate': 17.5,
                            'subtotalTaxable': 15,
                            'tax': 0.82,
                            'taxImpact': {'accounting': 'liability',
                                          'impactOnFinalPrice': 'Included',
                                          'impactOnNetAmount': 'Included'},
                            'taxType': 'icmsDifaDest'},
                           {'exemptionCode': '',
                            'isCustomCitation': False,
                            'jurisdictionName': 'Paraná',
                            'jurisdictionType': 'State',
                            'rate': 12,
                            'subtotalTaxable': 15,
                            'tax': 0,
                            'taxImpact': {'accounting': 'liability',
                                          'impactOnFinalPrice': 'Included',
                                          'impactOnNetAmount': 'Included'},
                            'taxType': 'icmsDifaRemet'},
                           {'calcMode': 'rate',
                            'cst': '53',
                            'exemptionCode': '',
                            'isCustomCitation': False,
                            'jurisdictionName': 'Brazil',
                            'jurisdictionType': 'Country',
                            'legalTaxClass': 999,
                            'rate': 0,
                            'subtotalTaxable': 15,
                            'tax': 0,
                            'taxImpact': {'accounting': 'none',
                                          'impactOnFinalPrice': 'Informative',
                                          'impactOnNetAmount': 'Informative'},
                            'taxType': 'ipi',
                            'traceCode': 'C005'},
                           {'calcMode': 'rate',
                            'cst': '06',
                            'exemptionCode': '',
                            'isCustomCitation': False,
                            'jurisdictionName': 'Brazil',
                            'jurisdictionType': 'Country',
                            'rate': 0,
                            'subtotalTaxable': 15,
                            'tax': 0,
                            'taxImpact': {'accounting': 'none',
                                          'impactOnFinalPrice': 'Included',
                                          'impactOnNetAmount': 'Included'},
                            'taxType': 'pis'}],
            'useType': 'use or consumption'},
           {'cfop': 6107,
            'freightAmount': 0,
            'goods': {'entityIcmsStSubstitute': 'default',
                      'subjectToIPIonInbound': False},
            'insuranceAmount': 0,
            'itemDescriptor': {'cest': '',
                               'hsCode': '4901.10.00',
                               'productType': 'FOR PRODUCT',
                               'source': '0'},
            'lineAdditionalInfo': '',
            'lineAmount': 15,
            'lineCode': 268,
            'lineNetFigure': 12.38,
            'lineTaxedDiscount': 0,
            'lineUnitPrice': 15,
            'numberOfItems': 1,
            'operationType': 'standardSales',
            'otherCostAmount': 0,
            'overwrite': 'no',
            'taxDetails': [{'exemptionCode': '',
                            'isCustomCitation': False,
                            'jurisdictionName': 'Brazil',
                            'jurisdictionType': 'Country',
                            'rate': 13.45,
                            'subtotalTaxable': 15,
                            'tax': 2.02,
                            'taxImpact': {'accounting': 'none',
                                          'impactOnFinalPrice': 'Informative',
                                          'impactOnNetAmount': 'Informative'},
                            'taxType': 'aproxtribFed'},
                           {'exemptionCode': '',
                            'jurisdictionName': 'Brazil',
                            'jurisdictionType': 'Country',
                            'rate': 19,
                            'subtotalTaxable': 15,
                            'tax': 2.85,
                            'taxImpact': {'accounting': 'none',
                                          'impactOnFinalPrice': 'Informative',
                                          'impactOnNetAmount': 'Informative'},
                            'taxType': 'aproxtribState'},
                           {'calcMode': 'rate',
                            'cst': '06',
                            'exemptionCode': '',
                            'isCustomCitation': False,
                            'jurisdictionName': 'Brazil',
                            'jurisdictionType': 'Country',
                            'rate': 0,
                            'subtotalTaxable': 15,
                            'tax': 0,
                            'taxImpact': {'accounting': 'none',
                                          'impactOnFinalPrice': 'Included',
                                          'impactOnNetAmount': 'Included'},
                            'taxType': 'cofins'},
                           {'cst': '00',
                            'exemptionCode': '',
                            'isCustomCitation': False,
                            'jurisdictionName': 'Paraná',
                            'jurisdictionType': 'State',
                            'modBC': '3',
                            'rate': 12,
                            'source': '0',
                            'subtotalTaxable': 15,
                            'tax': 1.8,
                            'taxImpact': {'accounting': 'liability',
                                          'impactOnFinalPrice': 'Included',
                                          'impactOnNetAmount': 'Included'},
                            'taxType': 'icms'},
                           {'exemptionCode': '',
                            'icmsInterPartRate': 100,
                            'isCustomCitation': False,
                            'jurisdictionName': 'Rio Grande do Sul',
                            'jurisdictionType': 'State',
                            'rate': 17.5,
                            'subtotalTaxable': 15,
                            'tax': 0.82,
                            'taxImpact': {'accounting': 'liability',
                                          'impactOnFinalPrice': 'Included',
                                          'impactOnNetAmount': 'Included'},
                            'taxType': 'icmsDifaDest'},
                           {'exemptionCode': '',
                            'isCustomCitation': False,
                            'jurisdictionName': 'Paraná',
                            'jurisdictionType': 'State',
                            'rate': 12,
                            'subtotalTaxable': 15,
                            'tax': 0,
                            'taxImpact': {'accounting': 'liability',
                                          'impactOnFinalPrice': 'Included',
                                          'impactOnNetAmount': 'Included'},
                            'taxType': 'icmsDifaRemet'},
                           {'calcMode': 'rate',
                            'cst': '53',
                            'exemptionCode': '',
                            'isCustomCitation': False,
                            'jurisdictionName': 'Brazil',
                            'jurisdictionType': 'Country',
                            'legalTaxClass': 999,
                            'rate': 0,
                            'subtotalTaxable': 15,
                            'tax': 0,
                            'taxImpact': {'accounting': 'none',
                                          'impactOnFinalPrice': 'Informative',
                                          'impactOnNetAmount': 'Informative'},
                            'taxType': 'ipi',
                            'traceCode': 'C005'},
                           {'calcMode': 'rate',
                            'cst': '06',
                            'exemptionCode': '',
                            'isCustomCitation': False,
                            'jurisdictionName': 'Brazil',
                            'jurisdictionType': 'Country',
                            'rate': 0,
                            'subtotalTaxable': 15,
                            'tax': 0,
                            'taxImpact': {'accounting': 'none',
                                          'impactOnFinalPrice': 'Included',
                                          'impactOnNetAmount': 'Included'},
                            'taxType': 'pis'}],
            'useType': 'use or consumption'}],
            'processingInfo': {'authMS': 4.157,
                    'calculationMS': 13.2907,
                    'duration': '13.291',
                    'env': 'sbx',
                    'invoiceSubmitMS': 0.0079,
                    'retrieveDataMS': 209.5932,
                    'taxDiscoveryMS': 179.2088,
                    'totalMS': 232.281,
                    'versionId': '23.7.2'},
            'summary': {'numberOfLines': 4,
             'taxByType': {'aproxtribFed': {'jurisdictions': [{'jurisdictionName': 'Brazil',
                                                               'jurisdictionType': 'Country',
                                                               'tax': 12.78}],
                                            'subtotalTaxable': 95,
                                            'tax': 12.78},
                           'aproxtribState': {'jurisdictions': [{'jurisdictionName': 'Brazil',
                                                                 'jurisdictionType': 'Country',
                                                                 'tax': 18.05}],
                                              'subtotalTaxable': 95,
                                              'tax': 18.05},
                           'cofins': {'jurisdictions': [{'jurisdictionName': 'Brazil',
                                                         'jurisdictionType': 'Country',
                                                         'tax': 0}],
                                      'subtotalTaxable': 95,
                                      'tax': 0},
                           'icms': {'jurisdictions': [{'jurisdictionName': 'Paraná',
                                                       'jurisdictionType': 'State',
                                                       'tax': 11.400000000000002}],
                                    'subtotalTaxable': 95,
                                    'tax': 11.4},
                           'icmsDifaDest': {'jurisdictions': [{'jurisdictionName': 'Rio '
                                                                                   'Grande '
                                                                                   'do '
                                                                                   'Sul',
                                                               'jurisdictionType': 'State',
                                                               'tax': 5.21}],
                                            'subtotalTaxable': 95,
                                            'tax': 5.21},
                           'icmsDifaRemet': {'jurisdictions': [{'jurisdictionName': 'Paraná',
                                                                'jurisdictionType': 'State',
                                                                'tax': 0}],
                                             'subtotalTaxable': 95,
                                             'tax': 0},
                           'ipi': {'jurisdictions': [{'jurisdictionName': 'Brazil',
                                                      'jurisdictionType': 'Country',
                                                      'tax': 0}],
                                   'subtotalTaxable': 95,
                                   'tax': 0},
                           'pis': {'jurisdictions': [{'jurisdictionName': 'Brazil',
                                                      'jurisdictionType': 'Country',
                                                      'tax': 0}],
                                   'subtotalTaxable': 95,
                                   'tax': 0}},
             'taxImpactHighlights': {'added': [],
                                     'included': [{'subtotalTaxable': 95,
                                                   'tax': 0,
                                                   'taxType': 'cofins'},
                                                  {'subtotalTaxable': 95,
                                                   'tax': 11.4,
                                                   'taxType': 'icms'},
                                                  {'subtotalTaxable': 95,
                                                   'tax': 5.21,
                                                   'taxType': 'icmsDifaDest'},
                                                  {'subtotalTaxable': 95,
                                                   'tax': 0,
                                                   'taxType': 'icmsDifaRemet'},
                                                  {'subtotalTaxable': 95,
                                                   'tax': 0,
                                                   'taxType': 'pis'}],
                                     'informative': [{'subtotalTaxable': 95,
                                                      'tax': 12.78,
                                                      'taxType': 'aproxtribFed'},
                                                     {'subtotalTaxable': 95,
                                                      'tax': 18.05,
                                                      'taxType': 'aproxtribState'},
                                                     {'subtotalTaxable': 95,
                                                      'tax': 0,
                                                      'taxType': 'ipi'}],
                                     'subtracted': [],
                                     'withheld': []},
             'totalFreights': 0,
             'totalInsurances': 0,
             'totalInvoice': 95,
             'totalLineAmounts': 95,
             'totalOtherCosts': 0,
             'totalTaxedDiscounts': 0,
             'totalUnTaxedOtherCosts': 0,
             'totalUntaxedDiscounts': 0},
            'version': '3'}
