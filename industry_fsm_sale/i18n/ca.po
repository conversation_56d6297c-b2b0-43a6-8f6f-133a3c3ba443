# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* industry_fsm_sale
# 
# Translators:
# <PERSON><PERSON> <car<PERSON><PERSON><PERSON>@hotmail.com>, 2024
# <PERSON><PERSON><PERSON>, 2024
# <PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON>, 2024
# <PERSON> Bochaca <<EMAIL>>, 2024
# jabiri7, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON>, 2024
# <PERSON><PERSON>, 2024
# <PERSON><PERSON><PERSON>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <AUTHOR> <EMAIL>, 2024
# <PERSON>, 2024
# Xavier, 2024
# Wil Odoo, 2025
# <AUTHOR> <EMAIL>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-25 09:14+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: EstudiTIC - estuditic.com <<EMAIL>>, 2025\n"
"Language-Team: Catalan (https://app.transifex.com/odoo/teams/41243/ca/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ca\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: industry_fsm_sale
#. odoo-python
#: code:addons/industry_fsm_sale/models/project_task.py:0
msgid "%(project_name)s - %(task_name)s"
msgstr "%(project_name)s - %(task_name)s"

#. module: industry_fsm_sale
#. odoo-javascript
#: code:addons/industry_fsm_sale/static/src/js/tours/industry_fsm_sale_tour.js:0
msgid "<b>Invoice your time and material</b> to your customer."
msgstr "<b>Factura el vostre temps i material</b> al vostre client."

#. module: industry_fsm_sale
#. odoo-javascript
#: code:addons/industry_fsm_sale/static/src/js/tours/industry_fsm_sale_tour.js:0
msgid "<b>Review and sign</b> the <b>task report</b> with your customer."
msgstr ""
"<b>Revisa i signa</b> l'informe <b>de tasques</b> amb el vostre client."

#. module: industry_fsm_sale
#. odoo-javascript
#: code:addons/industry_fsm_sale/static/src/js/tours/industry_fsm_sale_tour.js:0
msgid "<b>Send your task report</b> to your customer."
msgstr "<b>Envieu l'informe de tasques</b> al vostre client."

#. module: industry_fsm_sale
#. odoo-python
#: code:addons/industry_fsm_sale/models/project_task.py:0
msgid ""
"<p class=\"o_view_nocontent_smiling_face\">\n"
"                            No products found. Let's create one!\n"
"                        </p><p>\n"
"                            Keep track of the products you are using to complete your tasks, and invoice your customers for the goods.\n"
"                            Tip: using kits, you can add multiple products at once.\n"
"                        </p><p>\n"
"                            When your task is marked as done, your stock will be updated automatically. Simply choose a warehouse\n"
"                            in your profile from where to draw stock.\n"
"                        </p>"
msgstr ""
"<p class=\"o_view_nocontent_smiling_face\">\n"
"                            No s'ha trobat cap producte. Creem-ne un!\n"
"                        </p><p>\n"
"                            Feu un seguiment dels productes que esteu utilitzant per a completar les vostres tasques i facturar els vostres clients pels productes.\n"
"                            Consell: utilitzant kits, podeu afegir diversos productes alhora.\n"
"                        </p><p>\n"
"                            Quan la vostra tasca es marca com a feta, l'estoc s'actualitzarà automàticament. Simplement trieu un magatzem\n"
"                            al vostre perfil des d'on dibuixar l'estoc.\n"
"                        </p>"

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.project_view_form_inherit
msgid ""
"<span invisible=\"not is_fsm\">\n"
"                    Define the service and the rate at which an employee's time is billed based on their expertise, skills or experience.\n"
"                </span>"
msgstr ""

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.worksheet_time_and_material
msgid "<span>Disc.%</span>"
msgstr "<span>Desc.%</span>"

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.project_sharing_project_task_inherit_view_form
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.view_task_form2_inherit
msgid "<span>Products</span>"
msgstr "<span>Productes</span>"

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.worksheet_time_and_material
msgid "<strong class=\"mr16\">Subtotal</strong>"
msgstr "<strong class=\"mr16\">Subtotal</strong>"

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.worksheet_time_and_material
msgid "<strong>Amount Due: </strong>"
msgstr "<strong>Import vençut:</strong>"

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.worksheet_time_and_material
msgid "<strong>Taxes</strong>"
msgstr "<strong>Impostos</strong>"

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.worksheet_time_and_material
msgid "<strong>Total</strong>"
msgstr "<strong>Total</strong>"

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.worksheet_time_and_material
msgid "<strong>Untaxed amount</strong>"
msgstr "<strong>Quantitat no gravada</strong>"

#. module: industry_fsm_sale
#. odoo-python
#: code:addons/industry_fsm_sale/models/project_task.py:0
msgid "A customer should be set on the task to generate a worksheet."
msgstr "S'ha d'establir un client a la tasca de generar un full de treball."

#. module: industry_fsm_sale
#. odoo-python
#: code:addons/industry_fsm_sale/models/project_task.py:0
msgid "Add Products"
msgstr ""
"Afegeix un producte\n"
" "

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.industry_fsm_sale_product_catalog_inherit_search_view
msgid "Added Products"
msgstr "Productes afegits"

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.worksheet_time_and_material
msgid "Amount"
msgstr "Import"

#. module: industry_fsm_sale
#: model:ir.model.constraint,message:industry_fsm_sale.constraint_project_project_fsm_imply_task_rate
msgid "An FSM project must be billed at task rate or employee rate."
msgstr ""
"Un projecte FSM s'ha de facturar a tipus de tasca o a tipus d'empleat."

#. module: industry_fsm_sale
#. odoo-python
#: code:addons/industry_fsm_sale/wizard/sale_make_invoice_advance.py:0
msgid "An invoice has been created: %s"
msgstr "S'ha creat una factura: %s"

#. module: industry_fsm_sale
#: model:ir.model,name:industry_fsm_sale.model_account_analytic_line
msgid "Analytic Line"
msgstr "Línia analítica"

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_project__allow_billable
msgid "Billable"
msgstr "Facturable"

#. module: industry_fsm_sale
#. odoo-python
#: code:addons/industry_fsm_sale/models/project_task.py:0
msgid ""
"By saving this change, all timesheet entries will be linked to the selected "
"Sales Order Item without distinction."
msgstr ""
"En desar aquest canvi, totes les entrades del full de temps s'enllaçaran a "
"l'element de comanda de vendes seleccionat sense distinció."

#. module: industry_fsm_sale
#. odoo-javascript
#: code:addons/industry_fsm_sale/static/src/js/tours/industry_fsm_sale_tour.js:0
msgid ""
"Choose a <b>name</b> for your product <i>(e.g. Bolts, Screws, Boiler, "
"etc.).</i>"
msgstr ""
"Trieu un nom <b></b> per al vostre producte <i>(p. ex. Bolts, Screws, "
"Boiler, etc.).</i>"

#. module: industry_fsm_sale
#. odoo-javascript
#: code:addons/industry_fsm_sale/static/src/js/tours/industry_fsm_sale_tour.js:0
msgid ""
"Click on a product to add it to your <b>list of materials</b>. <i>Tip: for "
"large quantities, click on the number to edit it directly.</i>"
msgstr ""

#. module: industry_fsm_sale
#: model:ir.model,name:industry_fsm_sale.model_res_config_settings
msgid "Config Settings"
msgstr "Paràmetres de configuració"

#. module: industry_fsm_sale
#. odoo-javascript
#: code:addons/industry_fsm_sale/static/src/js/tours/industry_fsm_sale_tour.js:0
msgid "Confirm the creation of your <b>invoice</b>."
msgstr "Confirmeu la creació de la vostra <b>factura</b>."

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.project_task_view_list_fsm_sale_inherit
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.view_task_form2_inherit
msgid "Create Invoice"
msgstr "Crea factura"

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.project_view_form_inherit
msgid "Create new quotations directly from tasks"
msgstr "Crear pressupostos directament des de tasques"

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_task__currency_id
msgid "Currency"
msgstr "Divisa"

#. module: industry_fsm_sale
#. odoo-javascript
#: code:addons/industry_fsm_sale/static/src/js/tours/industry_fsm_sale_tour.js:0
msgid "Customize your <b>layout</b>."
msgstr ""

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.project_view_form_inherit
msgid "Default Service"
msgstr "Servei per defecte"

#. module: industry_fsm_sale
#. odoo-python
#: code:addons/industry_fsm_sale/models/sale_order.py:0
msgid "Extra Quotation Created: %s"
msgstr ""

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_project__allow_quotations
msgid "Extra Quotations"
msgstr "Cites addicionals"

#. module: industry_fsm_sale
#: model:ir.model,name:industry_fsm_sale.model_report_project_task_user_fsm
msgid "FSM Tasks Analysis"
msgstr "Anàlisi de tasques FSM"

#. module: industry_fsm_sale
#: model:product.template,name:industry_fsm_sale.field_service_product_product_template
msgid "Field Service"
msgstr "Servei de camp"

#. module: industry_fsm_sale
#: model:ir.model.fields.selection,name:industry_fsm_sale.selection__report_project_task_user_fsm__invoice_status__invoiced
msgid "Fully Invoiced"
msgstr "Totalment facturat "

#. module: industry_fsm_sale
#. odoo-javascript
#: code:addons/industry_fsm_sale/static/src/js/tours/industry_fsm_sale_tour.js:0
msgid "Go back to your Field Service <b>task</b>."
msgstr "Torna al vostre servei de camp <b>tasca</b>."

#. module: industry_fsm_sale
#: model:ir.model.fields,help:industry_fsm_sale.field_project_task__under_warranty
msgid ""
"If ticked, the time and materials used for this task will not be billed to "
"the customer. However, the inventory of consumed materials will still be "
"updated."
msgstr ""

#. module: industry_fsm_sale
#. odoo-python
#: code:addons/industry_fsm_sale/models/product_product.py:0
msgid "Invalid operator: %s"
msgstr "Operador no vàlid: %s"

#. module: industry_fsm_sale
#. odoo-python
#: code:addons/industry_fsm_sale/models/product_product.py:0
msgid "Invalid value: %s"
msgstr "Valor no vàlid: %s"

#. module: industry_fsm_sale
#. odoo-javascript
#: code:addons/industry_fsm_sale/static/src/js/tours/industry_fsm_sale_tour.js:0
msgid "Invite your customer to <b>validate and sign your task report</b>."
msgstr ""
"Convideu al vostre client a validar <b>i signeu l'informe de tasques</b>."

#. module: industry_fsm_sale
#. odoo-python
#: code:addons/industry_fsm_sale/models/project_task.py:0
msgid "Invoice"
msgstr "Factura"

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_task__portal_invoice_count
msgid "Invoice Count"
msgstr "Comptador de factures"

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_report_project_task_user_fsm__invoice_status
msgid "Invoice Status"
msgstr "Estat factura"

#. module: industry_fsm_sale
#: model_terms:ir.actions.act_window,help:industry_fsm_sale.project_task_action_to_invoice_fsm
#: model_terms:ir.actions.act_window,help:industry_fsm_sale.project_task_action_to_invoice_fsm2
msgid ""
"Invoice your time and material to your customers once your tasks are done."
msgstr ""
"Factura el teu temps i material als teus clients una vegada que les teves "
"tasques estiguin acabades."

#. module: industry_fsm_sale
#. odoo-python
#: code:addons/industry_fsm_sale/models/project_task.py:0
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.project_sharing_project_task_inherit_view_form
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.view_task_form2_inherit
msgid "Invoices"
msgstr "Factures"

#. module: industry_fsm_sale
#. odoo-javascript
#: code:addons/industry_fsm_sale/static/src/js/tours/industry_fsm_sale_tour.js:0
msgid "Let's <b>track the material</b> you use for your task."
msgstr ""
"Fem un seguiment de <b>del material</b> que utilitzeu per a la vostra tasca."

#. module: industry_fsm_sale
#. odoo-javascript
#: code:addons/industry_fsm_sale/static/src/js/tours/industry_fsm_sale_tour.js:0
msgid "Let's create a new <b>product</b>."
msgstr "Creem un nou producte <b></b>."

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_task__material_line_total_price
msgid "Material Line Total Price"
msgstr "Preu total de la línia de material"

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_product_product__fsm_quantity
msgid "Material Quantity"
msgstr "Quantitat del material"

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.view_task_form2_inherit
msgid "New Quotation"
msgstr "Nou pressupost"

#. module: industry_fsm_sale
#: model_terms:ir.actions.act_window,help:industry_fsm_sale.product_action_fsm
msgid "No products found. Let's create one!"
msgstr "No s'ha trobat cap producte. Creem-ne un!"

#. module: industry_fsm_sale
#: model_terms:ir.actions.act_window,help:industry_fsm_sale.project_task_action_to_invoice_fsm
#: model_terms:ir.actions.act_window,help:industry_fsm_sale.project_task_action_to_invoice_fsm2
msgid "No tasks found. Let's create one!"
msgstr "No s'ha trobat cap tasca. ¡Creem-ne una!"

#. module: industry_fsm_sale
#: model:ir.model.fields.selection,name:industry_fsm_sale.selection__report_project_task_user_fsm__invoice_status__no
msgid "Nothing to Invoice"
msgstr "Res a facturar"

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_task__invoice_count
msgid "Number of invoices"
msgstr "Nombre de factures"

#. module: industry_fsm_sale
#: model:product.template,name:industry_fsm_sale.field_service_product_zero_cost_1_product_template
msgid "Plumbing Valves"
msgstr ""

#. module: industry_fsm_sale
#: model:product.template,name:industry_fsm_sale.field_service_product_zero_cost_4_product_template
msgid "Plumbing Washers"
msgstr ""

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_task__portal_quotation_count
msgid "Portal Quotation Count"
msgstr "Portal de comptador de pressupostos"

#. module: industry_fsm_sale
#: model:ir.model,name:industry_fsm_sale.model_product_template
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.worksheet_time_and_material
msgid "Product"
msgstr "Producte"

#. module: industry_fsm_sale
#: model:ir.model,name:industry_fsm_sale.model_product_product
msgid "Product Variant"
msgstr "Variant de producte"

#. module: industry_fsm_sale
#: model:ir.actions.act_window,name:industry_fsm_sale.product_action_fsm
#: model:ir.ui.menu,name:industry_fsm_sale.fsm_menu_settings_product
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.view_task_form2_inherit
msgid "Products"
msgstr "Productes"

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_project__allow_material
msgid "Products on Tasks"
msgstr "Productes en tasques"

#. module: industry_fsm_sale
#: model:ir.model,name:industry_fsm_sale.model_project_project
#: model:ir.model.fields,field_description:industry_fsm_sale.field_product_product__project_id
#: model:ir.model.fields,field_description:industry_fsm_sale.field_product_template__project_id
msgid "Project"
msgstr "Projecte"

#. module: industry_fsm_sale
#: model:ir.model,name:industry_fsm_sale.model_project_sale_line_employee_map
msgid "Project Sales line, employee mapping"
msgstr "Línia de vendes del projecte, mapatge dels empleats"

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.worksheet_time_and_material
msgid "Quantity"
msgstr "Quantitat"

#. module: industry_fsm_sale
#. odoo-python
#: code:addons/industry_fsm_sale/controllers/portal.py:0
msgid "Quotation"
msgstr "Pressupost"

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_task__quotation_count
msgid "Quotation Count"
msgstr "Compte de pressupostos"

#. module: industry_fsm_sale
#. odoo-python
#: code:addons/industry_fsm_sale/controllers/portal.py:0
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.project_sharing_project_task_inherit_view_form
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.view_task_form2_inherit
msgid "Quotations"
msgstr "Pressupostos"

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.view_order_form_inherit_sale_project
msgid "Related Task"
msgstr "Tasca relacionada"

#. module: industry_fsm_sale
#: model:product.template,name:industry_fsm_sale.field_service_product_zero_cost_2_product_template
msgid "Safety Gloves"
msgstr ""

#. module: industry_fsm_sale
#: model:ir.model,name:industry_fsm_sale.model_sale_advance_payment_inv
msgid "Sales Advance Payment Invoice"
msgstr "Vendes. Bestreta pagament factura"

#. module: industry_fsm_sale
#: model:ir.model,name:industry_fsm_sale.model_sale_order
msgid "Sales Order"
msgstr "Comanda"

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_project__sale_line_id
msgid "Sales Order Item"
msgstr "Element de la comanda de venda"

#. module: industry_fsm_sale
#: model:ir.model,name:industry_fsm_sale.model_sale_order_line
msgid "Sales Order Line"
msgstr "Línia comanda de venda"

#. module: industry_fsm_sale
#: model:ir.model.fields,help:industry_fsm_sale.field_project_project__sale_line_id
msgid ""
"Sales order item that will be selected by default on the tasks and timesheets of this project, except if the employee set on the timesheets is explicitely linked to another sales order item on the project.\n"
"It can be modified on each task and timesheet entry individually if necessary."
msgstr ""
"L'element de comanda de vendes que se seleccionarà per defecte a les tasques i fulls d'hores d'aquest projecte, excepte si el treballador establert en els fulls d'hores està explícitament enllaçat a un altre element de comanda de vendes del projecte.\n"
"Es pot modificar a cada entrada de la tasca i del full de temps individualment si cal."

#. module: industry_fsm_sale
#. odoo-javascript
#: code:addons/industry_fsm_sale/static/src/js/tours/industry_fsm_sale_tour.js:0
msgid "Save time by automatically generating a <b>signature</b>."
msgstr "Estalvieu el temps generant automàticament una signatura <b></b>."

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_sale_line_employee_map__timesheet_product_id
msgid "Service"
msgstr "Servei"

#. module: industry_fsm_sale
#: model:ir.model,name:industry_fsm_sale.model_project_task
#: model:ir.model.fields,field_description:industry_fsm_sale.field_sale_order__task_id
msgid "Task"
msgstr "Tasca"

#. module: industry_fsm_sale
#: model:ir.model,name:industry_fsm_sale.model_project_task_recurrence
msgid "Task Recurrence"
msgstr "Recurrència de la tasca"

#. module: industry_fsm_sale
#: model:ir.model.fields,help:industry_fsm_sale.field_sale_order__task_id
msgid "Task from which this quotation have been created"
msgstr "Tasca des de la qual s'ha creat aquest pressupost"

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.worksheet_time_and_material
msgid "Taxes"
msgstr "Impostos"

#. module: industry_fsm_sale
#. odoo-python
#: code:addons/industry_fsm_sale/models/product_template.py:0
msgid ""
"The following products are currently associated with a Field Service "
"project, you cannot change their Invoicing Policy or Type:%s"
msgstr ""

#. module: industry_fsm_sale
#: model:ir.model.constraint,message:industry_fsm_sale.constraint_project_project_material_imply_billable
msgid "The material can be allowed only when the task can be billed."
msgstr "El material només es pot permetre quan la tasca es pot facturar."

#. module: industry_fsm_sale
#: model:ir.model.constraint,message:industry_fsm_sale.constraint_project_project_timesheet_product_required_if_billable_and_time
msgid ""
"The timesheet product is required when the fsm project can be billed and "
"timesheets are allowed."
msgstr ""
"El producte del full de temps és necessari quan el projecte fsm es pot "
"facturar i es permeten fulls d'hores."

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.portal_my_task
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.worksheet_custom_page
msgid "Time &amp; Material"
msgstr "Temps &amp; Material"

#. module: industry_fsm_sale
#: model:ir.actions.act_window,name:industry_fsm_sale.project_task_action_to_invoice_fsm
#: model:ir.actions.act_window,name:industry_fsm_sale.project_task_action_to_invoice_fsm2
#: model:ir.model.fields.selection,name:industry_fsm_sale.selection__report_project_task_user_fsm__invoice_status__to_invoice
#: model:ir.ui.menu,name:industry_fsm_sale.fsm_menu_all_tasks_invoice
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.project_task_view_search_fsm_inherit_sale
msgid "To Invoice"
msgstr "Per facturar"

#. module: industry_fsm_sale
#: model:ir.actions.server,name:industry_fsm_sale.project_task_to_invoice_fsm_server_action
msgid "To Invoice Server Action"
msgstr ""

#. module: industry_fsm_sale
#: model_terms:ir.actions.act_window,help:industry_fsm_sale.product_action_fsm
msgid "Track and bill the material used to complete your tasks."
msgstr ""
"Seguiu i reviseu el material utilitzat per a completar les vostres tasques."

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.project_view_form_inherit
msgid "Track the material used to complete tasks"
msgstr "Segueix el material utilitzat per completar les tasques"

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_task__under_warranty
msgid "Under Warranty"
msgstr "Sota Garantia"

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_sale_line_employee_map__price_unit
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.worksheet_time_and_material
msgid "Unit Price"
msgstr "Preu un."

#. module: industry_fsm_sale
#: model:ir.model.fields.selection,name:industry_fsm_sale.selection__report_project_task_user_fsm__invoice_status__upselling
msgid "Upselling Opportunity"
msgstr "Oportunitat de venda addicional"

#. module: industry_fsm_sale
#. odoo-javascript
#: code:addons/industry_fsm_sale/static/src/js/tours/industry_fsm_sale_tour.js:0
msgid "Use the breadcrumbs to navigate to your <b>list of products</b>."
msgstr ""
"Utilitzeu les molles de pa per navegar a la vostra <b>llista de "
"productes</b>."

#. module: industry_fsm_sale
#. odoo-javascript
#: code:addons/industry_fsm_sale/static/src/js/tours/industry_fsm_sale_tour.js:0
msgid "Use the breadcrumbs to return to your <b>task</b>."
msgstr "Utilitzeu la ruta de navegació per tornar a la vostra tasca <b></b>."

#. module: industry_fsm_sale
#. odoo-javascript
#: code:addons/industry_fsm_sale/static/src/js/tours/industry_fsm_sale_tour.js:0
msgid "Validate the <b>signature</b>."
msgstr "Validar la <b>signatura</b>."

#. module: industry_fsm_sale
#. odoo-javascript
#: code:addons/industry_fsm_sale/static/src/js/tours/industry_fsm_sale_tour.js:0
msgid "Wait for the invoice to show up"
msgstr "Espera que aparegui la factura"

#. module: industry_fsm_sale
#: model:product.template,name:industry_fsm_sale.field_service_product_zero_cost_3_product_template
msgid "Water Testing Kits"
msgstr ""
