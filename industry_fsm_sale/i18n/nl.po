# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* industry_fsm_sale
# 
# Translators:
# Wil <PERSON>do<PERSON>, 2024
# <PERSON> <<EMAIL>>, 2025
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-25 09:14+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON><PERSON>, 2025\n"
"Language-Team: Dutch (https://app.transifex.com/odoo/teams/41243/nl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: nl\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: industry_fsm_sale
#. odoo-python
#: code:addons/industry_fsm_sale/models/project_task.py:0
msgid "%(project_name)s - %(task_name)s"
msgstr "%(project_name)s - %(task_name)s"

#. module: industry_fsm_sale
#. odoo-javascript
#: code:addons/industry_fsm_sale/static/src/js/tours/industry_fsm_sale_tour.js:0
msgid "<b>Invoice your time and material</b> to your customer."
msgstr "<b>Factureer je tijd en materiaal</b> aan je klant."

#. module: industry_fsm_sale
#. odoo-javascript
#: code:addons/industry_fsm_sale/static/src/js/tours/industry_fsm_sale_tour.js:0
msgid "<b>Review and sign</b> the <b>task report</b> with your customer."
msgstr "<b>Bekijk en onderteken</b> het <b>taakrapport</b> met de klant."

#. module: industry_fsm_sale
#. odoo-javascript
#: code:addons/industry_fsm_sale/static/src/js/tours/industry_fsm_sale_tour.js:0
msgid "<b>Send your task report</b> to your customer."
msgstr "<b>Stuur je taakrapport</b> naar de klant."

#. module: industry_fsm_sale
#. odoo-python
#: code:addons/industry_fsm_sale/models/project_task.py:0
msgid ""
"<p class=\"o_view_nocontent_smiling_face\">\n"
"                            No products found. Let's create one!\n"
"                        </p><p>\n"
"                            Keep track of the products you are using to complete your tasks, and invoice your customers for the goods.\n"
"                            Tip: using kits, you can add multiple products at once.\n"
"                        </p><p>\n"
"                            When your task is marked as done, your stock will be updated automatically. Simply choose a warehouse\n"
"                            in your profile from where to draw stock.\n"
"                        </p>"
msgstr ""
"<p class=\"o_view_nocontent_smiling_face\">\n"
"Geen producten gevonden. Laten we er een maken!\n"
"</p><p>\n"
"Houd bij welke producten je gebruikt om je taken uit te voeren en factureer je klanten voor de goederen.\n"
"Tip: met kits kun je meerdere producten tegelijk toevoegen.\n"
"</p><p>\n"
"Wanneer je taak als voltooid is gemarkeerd, wordt de voorraad automatisch bijgewerkt. Kies eenvoudig een magazijn\n"
"in je profiel van waaruit je voorraad kunt afboeken.</p>"

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.project_view_form_inherit
msgid ""
"<span invisible=\"not is_fsm\">\n"
"                    Define the service and the rate at which an employee's time is billed based on their expertise, skills or experience.\n"
"                </span>"
msgstr ""
"<span invisible=\"not is_fsm\">\n"
"                    Bepaal de dienst en het tarief waartegen de tijd van een werknemer wordt gefactureerd op basis van zijn expertise, vaardigheden of ervaring.\n"
"                </span>"

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.worksheet_time_and_material
msgid "<span>Disc.%</span>"
msgstr "<span>Kort.%</span>"

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.project_sharing_project_task_inherit_view_form
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.view_task_form2_inherit
msgid "<span>Products</span>"
msgstr "<span>Producten</span>"

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.worksheet_time_and_material
msgid "<strong class=\"mr16\">Subtotal</strong>"
msgstr "<strong class=\"mr16\">Subtotaal</strong>"

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.worksheet_time_and_material
msgid "<strong>Amount Due: </strong>"
msgstr "<strong>Verschuldigd bedrag: </strong>"

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.worksheet_time_and_material
msgid "<strong>Taxes</strong>"
msgstr "<strong>Btw</strong>"

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.worksheet_time_and_material
msgid "<strong>Total</strong>"
msgstr "<strong>Totaal</strong>"

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.worksheet_time_and_material
msgid "<strong>Untaxed amount</strong>"
msgstr "<strong>Onbelast bedrag</strong>"

#. module: industry_fsm_sale
#. odoo-python
#: code:addons/industry_fsm_sale/models/project_task.py:0
msgid "A customer should be set on the task to generate a worksheet."
msgstr ""
"Een klant moet gekoppeld worden aan een taak om een werkbon te genereren."

#. module: industry_fsm_sale
#. odoo-python
#: code:addons/industry_fsm_sale/models/project_task.py:0
msgid "Add Products"
msgstr "Producten toevoegen"

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.industry_fsm_sale_product_catalog_inherit_search_view
msgid "Added Products"
msgstr "Toegevoegde producten"

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.worksheet_time_and_material
msgid "Amount"
msgstr "Bedrag"

#. module: industry_fsm_sale
#: model:ir.model.constraint,message:industry_fsm_sale.constraint_project_project_fsm_imply_task_rate
msgid "An FSM project must be billed at task rate or employee rate."
msgstr ""
"Een buitendienst-project moet worden gefactureerd tegen taaktarief of "
"personeelstarief."

#. module: industry_fsm_sale
#. odoo-python
#: code:addons/industry_fsm_sale/wizard/sale_make_invoice_advance.py:0
msgid "An invoice has been created: %s"
msgstr "Er is een factuur aangemaakt: %s"

#. module: industry_fsm_sale
#: model:ir.model,name:industry_fsm_sale.model_account_analytic_line
msgid "Analytic Line"
msgstr "Analytische boeking"

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_project__allow_billable
msgid "Billable"
msgstr "Factureerbaar"

#. module: industry_fsm_sale
#. odoo-python
#: code:addons/industry_fsm_sale/models/project_task.py:0
msgid ""
"By saving this change, all timesheet entries will be linked to the selected "
"Sales Order Item without distinction."
msgstr ""
"Door deze wijziging op te slaan, worden alle urenregistratieposten zonder "
"onderscheid gekoppeld aan het geselecteerde klantorderitem."

#. module: industry_fsm_sale
#. odoo-javascript
#: code:addons/industry_fsm_sale/static/src/js/tours/industry_fsm_sale_tour.js:0
msgid ""
"Choose a <b>name</b> for your product <i>(e.g. Bolts, Screws, Boiler, "
"etc.).</i>"
msgstr ""
"Kies een <b>naam</b> voor je product <i>(bijv. bouten, schroeven, ketel, "
"enz.).</i>"

#. module: industry_fsm_sale
#. odoo-javascript
#: code:addons/industry_fsm_sale/static/src/js/tours/industry_fsm_sale_tour.js:0
msgid ""
"Click on a product to add it to your <b>list of materials</b>. <i>Tip: for "
"large quantities, click on the number to edit it directly.</i>"
msgstr ""
"Klik op een product om het toe te voegen aan je <b>materialenlijst</b>. "
"<i>Tip: klik voor grote hoeveelheden op het aantal om het onmiddellijk aan "
"te passen.</i>"

#. module: industry_fsm_sale
#: model:ir.model,name:industry_fsm_sale.model_res_config_settings
msgid "Config Settings"
msgstr "Configuratie-instellingen"

#. module: industry_fsm_sale
#. odoo-javascript
#: code:addons/industry_fsm_sale/static/src/js/tours/industry_fsm_sale_tour.js:0
msgid "Confirm the creation of your <b>invoice</b>."
msgstr "Bevestig de aanmaak van de <b>factuur</b>."

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.project_task_view_list_fsm_sale_inherit
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.view_task_form2_inherit
msgid "Create Invoice"
msgstr "Factuur maken"

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.project_view_form_inherit
msgid "Create new quotations directly from tasks"
msgstr "Maak direct nieuwe offertes vanuit taken"

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_task__currency_id
msgid "Currency"
msgstr "Valuta"

#. module: industry_fsm_sale
#. odoo-javascript
#: code:addons/industry_fsm_sale/static/src/js/tours/industry_fsm_sale_tour.js:0
msgid "Customize your <b>layout</b>."
msgstr "Pas je<b>lay-out</b>aan."

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.project_view_form_inherit
msgid "Default Service"
msgstr "Standaard dienst"

#. module: industry_fsm_sale
#. odoo-python
#: code:addons/industry_fsm_sale/models/sale_order.py:0
msgid "Extra Quotation Created: %s"
msgstr "Extra offerte aangemaakt: %s"

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_project__allow_quotations
msgid "Extra Quotations"
msgstr "Extra offerte"

#. module: industry_fsm_sale
#: model:ir.model,name:industry_fsm_sale.model_report_project_task_user_fsm
msgid "FSM Tasks Analysis"
msgstr "Buitendienst taakanalyse"

#. module: industry_fsm_sale
#: model:product.template,name:industry_fsm_sale.field_service_product_product_template
msgid "Field Service"
msgstr "Buitendienst"

#. module: industry_fsm_sale
#: model:ir.model.fields.selection,name:industry_fsm_sale.selection__report_project_task_user_fsm__invoice_status__invoiced
msgid "Fully Invoiced"
msgstr "Volledig gefactureerd"

#. module: industry_fsm_sale
#. odoo-javascript
#: code:addons/industry_fsm_sale/static/src/js/tours/industry_fsm_sale_tour.js:0
msgid "Go back to your Field Service <b>task</b>."
msgstr "Ga terug naar je buitendienst <b>taak</b>."

#. module: industry_fsm_sale
#: model:ir.model.fields,help:industry_fsm_sale.field_project_task__under_warranty
msgid ""
"If ticked, the time and materials used for this task will not be billed to "
"the customer. However, the inventory of consumed materials will still be "
"updated."
msgstr ""
"Vink dit vakje aan om de tijd en de gebruikte materialen van deze taak niet "
"aan de klant te factureren. De voorraad van verbruikte materialen wordt "
"echter wel bijgewerkt."

#. module: industry_fsm_sale
#. odoo-python
#: code:addons/industry_fsm_sale/models/product_product.py:0
msgid "Invalid operator: %s"
msgstr "Ongeldige operator: %s"

#. module: industry_fsm_sale
#. odoo-python
#: code:addons/industry_fsm_sale/models/product_product.py:0
msgid "Invalid value: %s"
msgstr "Ongeldige waarde: %s"

#. module: industry_fsm_sale
#. odoo-javascript
#: code:addons/industry_fsm_sale/static/src/js/tours/industry_fsm_sale_tour.js:0
msgid "Invite your customer to <b>validate and sign your task report</b>."
msgstr ""
"Nodig je klant uit om <b>je taakrapport te bevestigen en te "
"ondertekenen</b>."

#. module: industry_fsm_sale
#. odoo-python
#: code:addons/industry_fsm_sale/models/project_task.py:0
msgid "Invoice"
msgstr "Factuur"

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_task__portal_invoice_count
msgid "Invoice Count"
msgstr "Aantal facturen"

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_report_project_task_user_fsm__invoice_status
msgid "Invoice Status"
msgstr "Factuurstatus"

#. module: industry_fsm_sale
#: model_terms:ir.actions.act_window,help:industry_fsm_sale.project_task_action_to_invoice_fsm
#: model_terms:ir.actions.act_window,help:industry_fsm_sale.project_task_action_to_invoice_fsm2
msgid ""
"Invoice your time and material to your customers once your tasks are done."
msgstr ""
"Factureer je tijd en materiaal aan je klanten zodra je taken zijn "
"uitgevoerd."

#. module: industry_fsm_sale
#. odoo-python
#: code:addons/industry_fsm_sale/models/project_task.py:0
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.project_sharing_project_task_inherit_view_form
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.view_task_form2_inherit
msgid "Invoices"
msgstr "Facturen"

#. module: industry_fsm_sale
#. odoo-javascript
#: code:addons/industry_fsm_sale/static/src/js/tours/industry_fsm_sale_tour.js:0
msgid "Let's <b>track the material</b> you use for your task."
msgstr "Laten we <b>het materiaal bijhouden</b> dat je voor je taak gebruikt."

#. module: industry_fsm_sale
#. odoo-javascript
#: code:addons/industry_fsm_sale/static/src/js/tours/industry_fsm_sale_tour.js:0
msgid "Let's create a new <b>product</b>."
msgstr "Laten we een nieuw <b>product</b> maken."

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_task__material_line_total_price
msgid "Material Line Total Price"
msgstr "Totaalprijs materiaalregel"

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_product_product__fsm_quantity
msgid "Material Quantity"
msgstr "Materiaalaantal"

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.view_task_form2_inherit
msgid "New Quotation"
msgstr "Nieuwe offerte"

#. module: industry_fsm_sale
#: model_terms:ir.actions.act_window,help:industry_fsm_sale.product_action_fsm
msgid "No products found. Let's create one!"
msgstr "Geen producten gevonden. Laten we er één maken!"

#. module: industry_fsm_sale
#: model_terms:ir.actions.act_window,help:industry_fsm_sale.project_task_action_to_invoice_fsm
#: model_terms:ir.actions.act_window,help:industry_fsm_sale.project_task_action_to_invoice_fsm2
msgid "No tasks found. Let's create one!"
msgstr "Geen taken gevonden. Maak er één aan!"

#. module: industry_fsm_sale
#: model:ir.model.fields.selection,name:industry_fsm_sale.selection__report_project_task_user_fsm__invoice_status__no
msgid "Nothing to Invoice"
msgstr "Niets te factureren"

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_task__invoice_count
msgid "Number of invoices"
msgstr "Aantal facturen"

#. module: industry_fsm_sale
#: model:product.template,name:industry_fsm_sale.field_service_product_zero_cost_1_product_template
msgid "Plumbing Valves"
msgstr "Leidingafsluiters"

#. module: industry_fsm_sale
#: model:product.template,name:industry_fsm_sale.field_service_product_zero_cost_4_product_template
msgid "Plumbing Washers"
msgstr "Leidingaansluitingen"

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_task__portal_quotation_count
msgid "Portal Quotation Count"
msgstr "Aantal portaloffertes"

#. module: industry_fsm_sale
#: model:ir.model,name:industry_fsm_sale.model_product_template
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.worksheet_time_and_material
msgid "Product"
msgstr "Product"

#. module: industry_fsm_sale
#: model:ir.model,name:industry_fsm_sale.model_product_product
msgid "Product Variant"
msgstr "Productvariant"

#. module: industry_fsm_sale
#: model:ir.actions.act_window,name:industry_fsm_sale.product_action_fsm
#: model:ir.ui.menu,name:industry_fsm_sale.fsm_menu_settings_product
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.view_task_form2_inherit
msgid "Products"
msgstr "Producten"

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_project__allow_material
msgid "Products on Tasks"
msgstr "Producten op taken"

#. module: industry_fsm_sale
#: model:ir.model,name:industry_fsm_sale.model_project_project
#: model:ir.model.fields,field_description:industry_fsm_sale.field_product_product__project_id
#: model:ir.model.fields,field_description:industry_fsm_sale.field_product_template__project_id
msgid "Project"
msgstr "Project"

#. module: industry_fsm_sale
#: model:ir.model,name:industry_fsm_sale.model_project_sale_line_employee_map
msgid "Project Sales line, employee mapping"
msgstr "Project verkoopregel/Werknemer mapping"

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.worksheet_time_and_material
msgid "Quantity"
msgstr "Aantal"

#. module: industry_fsm_sale
#. odoo-python
#: code:addons/industry_fsm_sale/controllers/portal.py:0
msgid "Quotation"
msgstr "Offerte"

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_task__quotation_count
msgid "Quotation Count"
msgstr "Aantal offertes"

#. module: industry_fsm_sale
#. odoo-python
#: code:addons/industry_fsm_sale/controllers/portal.py:0
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.project_sharing_project_task_inherit_view_form
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.view_task_form2_inherit
msgid "Quotations"
msgstr "Offertes"

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.view_order_form_inherit_sale_project
msgid "Related Task"
msgstr "Gerelateerde taak"

#. module: industry_fsm_sale
#: model:product.template,name:industry_fsm_sale.field_service_product_zero_cost_2_product_template
msgid "Safety Gloves"
msgstr "Veiligheidshandschoenen"

#. module: industry_fsm_sale
#: model:ir.model,name:industry_fsm_sale.model_sale_advance_payment_inv
msgid "Sales Advance Payment Invoice"
msgstr "Verkoop vooruibetaling factuur"

#. module: industry_fsm_sale
#: model:ir.model,name:industry_fsm_sale.model_sale_order
msgid "Sales Order"
msgstr "Verkooporder"

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_project__sale_line_id
msgid "Sales Order Item"
msgstr "Verkooporderregel"

#. module: industry_fsm_sale
#: model:ir.model,name:industry_fsm_sale.model_sale_order_line
msgid "Sales Order Line"
msgstr "Verkooporderregel"

#. module: industry_fsm_sale
#: model:ir.model.fields,help:industry_fsm_sale.field_project_project__sale_line_id
msgid ""
"Sales order item that will be selected by default on the tasks and timesheets of this project, except if the employee set on the timesheets is explicitely linked to another sales order item on the project.\n"
"It can be modified on each task and timesheet entry individually if necessary."
msgstr ""
"Verkooporderregel dat standaard zal worden gekozen op de taken en urenstaten van dit project, behalve indien de op de urenstaten vastgelegde werknemer uitdrukkelijk gerelateerd is aan een ander verkooporderregel op het project. \n"
"Dit kan individueel worden gewijzigd op elke taak en urenstaat indien nodig."

#. module: industry_fsm_sale
#. odoo-javascript
#: code:addons/industry_fsm_sale/static/src/js/tours/industry_fsm_sale_tour.js:0
msgid "Save time by automatically generating a <b>signature</b>."
msgstr "Bespaar tijd door automatisch een <b>handtekening</b> te genereren."

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_sale_line_employee_map__timesheet_product_id
msgid "Service"
msgstr "Dienst"

#. module: industry_fsm_sale
#: model:ir.model,name:industry_fsm_sale.model_project_task
#: model:ir.model.fields,field_description:industry_fsm_sale.field_sale_order__task_id
msgid "Task"
msgstr "Taak"

#. module: industry_fsm_sale
#: model:ir.model,name:industry_fsm_sale.model_project_task_recurrence
msgid "Task Recurrence"
msgstr "Taak herhaling"

#. module: industry_fsm_sale
#: model:ir.model.fields,help:industry_fsm_sale.field_sale_order__task_id
msgid "Task from which this quotation have been created"
msgstr "Taak waaruit deze offerte is gemaakt"

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.worksheet_time_and_material
msgid "Taxes"
msgstr "Btw"

#. module: industry_fsm_sale
#. odoo-python
#: code:addons/industry_fsm_sale/models/product_template.py:0
msgid ""
"The following products are currently associated with a Field Service "
"project, you cannot change their Invoicing Policy or Type:%s"
msgstr ""
"De volgende producten zijn momenteel gekoppeld aan een buitensdienst-"
"project. Je kunt hun factureringsbeleid of -type niet wijzigen: %s"

#. module: industry_fsm_sale
#: model:ir.model.constraint,message:industry_fsm_sale.constraint_project_project_material_imply_billable
msgid "The material can be allowed only when the task can be billed."
msgstr ""
"Het materiaal kan alleen toegestaan worden als de taak gefactureerd kan "
"worden."

#. module: industry_fsm_sale
#: model:ir.model.constraint,message:industry_fsm_sale.constraint_project_project_timesheet_product_required_if_billable_and_time
msgid ""
"The timesheet product is required when the fsm project can be billed and "
"timesheets are allowed."
msgstr ""
"Het urenstaat-product is vereist wanneer het buitendienst-project kan worden"
" gefactureerd en urenstaten zijn toegestaan."

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.portal_my_task
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.worksheet_custom_page
msgid "Time &amp; Material"
msgstr "Tijd & Materiaal"

#. module: industry_fsm_sale
#: model:ir.actions.act_window,name:industry_fsm_sale.project_task_action_to_invoice_fsm
#: model:ir.actions.act_window,name:industry_fsm_sale.project_task_action_to_invoice_fsm2
#: model:ir.model.fields.selection,name:industry_fsm_sale.selection__report_project_task_user_fsm__invoice_status__to_invoice
#: model:ir.ui.menu,name:industry_fsm_sale.fsm_menu_all_tasks_invoice
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.project_task_view_search_fsm_inherit_sale
msgid "To Invoice"
msgstr "Te factureren"

#. module: industry_fsm_sale
#: model:ir.actions.server,name:industry_fsm_sale.project_task_to_invoice_fsm_server_action
msgid "To Invoice Server Action"
msgstr "Te factureren serveractie"

#. module: industry_fsm_sale
#: model_terms:ir.actions.act_window,help:industry_fsm_sale.product_action_fsm
msgid "Track and bill the material used to complete your tasks."
msgstr ""
"Volg en factureer het materiaal dat is gebruikt om je taken te voltooien."

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.project_view_form_inherit
msgid "Track the material used to complete tasks"
msgstr "Traceer het materiaal gebruikt om deze taken te voltooien"

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_task__under_warranty
msgid "Under Warranty"
msgstr "Onder garantie"

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_sale_line_employee_map__price_unit
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.worksheet_time_and_material
msgid "Unit Price"
msgstr "Prijs"

#. module: industry_fsm_sale
#: model:ir.model.fields.selection,name:industry_fsm_sale.selection__report_project_task_user_fsm__invoice_status__upselling
msgid "Upselling Opportunity"
msgstr "Upselling verkoopkans"

#. module: industry_fsm_sale
#. odoo-javascript
#: code:addons/industry_fsm_sale/static/src/js/tours/industry_fsm_sale_tour.js:0
msgid "Use the breadcrumbs to navigate to your <b>list of products</b>."
msgstr ""
"Gebruik de broodkruimels om naar de <b>lijst met producten</b>te navigeren."

#. module: industry_fsm_sale
#. odoo-javascript
#: code:addons/industry_fsm_sale/static/src/js/tours/industry_fsm_sale_tour.js:0
msgid "Use the breadcrumbs to return to your <b>task</b>."
msgstr "Gebruik de broodkruimels om terug te keren naar je <b>taak</b>."

#. module: industry_fsm_sale
#. odoo-javascript
#: code:addons/industry_fsm_sale/static/src/js/tours/industry_fsm_sale_tour.js:0
msgid "Validate the <b>signature</b>."
msgstr "Valideer de <b>handtekening</b>."

#. module: industry_fsm_sale
#. odoo-javascript
#: code:addons/industry_fsm_sale/static/src/js/tours/industry_fsm_sale_tour.js:0
msgid "Wait for the invoice to show up"
msgstr "Wacht tot de factuur verschijnt"

#. module: industry_fsm_sale
#: model:product.template,name:industry_fsm_sale.field_service_product_zero_cost_3_product_template
msgid "Water Testing Kits"
msgstr "Water-testkits"
