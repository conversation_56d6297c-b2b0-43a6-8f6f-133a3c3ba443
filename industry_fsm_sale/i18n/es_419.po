# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* industry_fsm_sale
# 
# Translators:
# Wil Odoo, 2024
# <PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-25 09:14+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2024\n"
"Language-Team: Spanish (Latin America) (https://app.transifex.com/odoo/teams/41243/es_419/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es_419\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: industry_fsm_sale
#. odoo-python
#: code:addons/industry_fsm_sale/models/project_task.py:0
msgid "%(project_name)s - %(task_name)s"
msgstr "%(project_name)s - %(task_name)s"

#. module: industry_fsm_sale
#. odoo-javascript
#: code:addons/industry_fsm_sale/static/src/js/tours/industry_fsm_sale_tour.js:0
msgid "<b>Invoice your time and material</b> to your customer."
msgstr "<b>Facture su tiempo y materiales</b> a sus clientes."

#. module: industry_fsm_sale
#. odoo-javascript
#: code:addons/industry_fsm_sale/static/src/js/tours/industry_fsm_sale_tour.js:0
msgid "<b>Review and sign</b> the <b>task report</b> with your customer."
msgstr "<b>Revise y firme</b> el <b>reporte de tarea</b> con su cliente."

#. module: industry_fsm_sale
#. odoo-javascript
#: code:addons/industry_fsm_sale/static/src/js/tours/industry_fsm_sale_tour.js:0
msgid "<b>Send your task report</b> to your customer."
msgstr "<b>Envíe su reporte de tarea</b> a su cliente."

#. module: industry_fsm_sale
#. odoo-python
#: code:addons/industry_fsm_sale/models/project_task.py:0
msgid ""
"<p class=\"o_view_nocontent_smiling_face\">\n"
"                            No products found. Let's create one!\n"
"                        </p><p>\n"
"                            Keep track of the products you are using to complete your tasks, and invoice your customers for the goods.\n"
"                            Tip: using kits, you can add multiple products at once.\n"
"                        </p><p>\n"
"                            When your task is marked as done, your stock will be updated automatically. Simply choose a warehouse\n"
"                            in your profile from where to draw stock.\n"
"                        </p>"
msgstr ""
"<p class=\"o_view_nocontent_smiling_face\">\n"
"                            No se encontraron productos. ¡Creemos uno!\n"
"                        </p><p>\n"
"                            Lleve el seguimiento de los productos que utiliza para completar sus tareas, y facture la mercancía a sus clientes.\n"
"                            Consejo: puede agregar varios productos a la vez al usar kits.\n"
"                        </p><p>\n"
"                            Cuando su tarea se marca como hecha, sus existencias se actualizarán de forma automática. Solo elija en su perfil\n"
"                            un almacén de dónde obtener las existencias.\n"
"                        </p>"

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.project_view_form_inherit
msgid ""
"<span invisible=\"not is_fsm\">\n"
"                    Define the service and the rate at which an employee's time is billed based on their expertise, skills or experience.\n"
"                </span>"
msgstr ""
"<span invisible=\"not is_fsm\">\n"
"                    Defina el servicio y la tasa con la que el tiempo del empleado se facturará según su experiencia o habilidades.\n"
"                </span>"

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.worksheet_time_and_material
msgid "<span>Disc.%</span>"
msgstr "<span>% de desc.</span>"

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.project_sharing_project_task_inherit_view_form
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.view_task_form2_inherit
msgid "<span>Products</span>"
msgstr "<span>Productos</span>"

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.worksheet_time_and_material
msgid "<strong class=\"mr16\">Subtotal</strong>"
msgstr "<strong class=\"mr16\">Subtotal</strong>"

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.worksheet_time_and_material
msgid "<strong>Amount Due: </strong>"
msgstr "<strong>Importe adeudado: </strong>"

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.worksheet_time_and_material
msgid "<strong>Taxes</strong>"
msgstr "<strong>Impuestos</strong>"

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.worksheet_time_and_material
msgid "<strong>Total</strong>"
msgstr "<strong>Total</strong>"

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.worksheet_time_and_material
msgid "<strong>Untaxed amount</strong>"
msgstr "<strong>Importe sin impuestos</strong>"

#. module: industry_fsm_sale
#. odoo-python
#: code:addons/industry_fsm_sale/models/project_task.py:0
msgid "A customer should be set on the task to generate a worksheet."
msgstr "Asigne un cliente a la tarea para generar una hoja de trabajo."

#. module: industry_fsm_sale
#. odoo-python
#: code:addons/industry_fsm_sale/models/project_task.py:0
msgid "Add Products"
msgstr "Agregar productos"

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.industry_fsm_sale_product_catalog_inherit_search_view
msgid "Added Products"
msgstr "Productos agregados"

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.worksheet_time_and_material
msgid "Amount"
msgstr "Importe"

#. module: industry_fsm_sale
#: model:ir.model.constraint,message:industry_fsm_sale.constraint_project_project_fsm_imply_task_rate
msgid "An FSM project must be billed at task rate or employee rate."
msgstr ""
"Un proyecto de Servicio externo debe facturarse según la tasa de la tarea o "
"del empleado."

#. module: industry_fsm_sale
#. odoo-python
#: code:addons/industry_fsm_sale/wizard/sale_make_invoice_advance.py:0
msgid "An invoice has been created: %s"
msgstr "Se creó una factura: %s"

#. module: industry_fsm_sale
#: model:ir.model,name:industry_fsm_sale.model_account_analytic_line
msgid "Analytic Line"
msgstr "Línea analítica"

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_project__allow_billable
msgid "Billable"
msgstr "Facturable"

#. module: industry_fsm_sale
#. odoo-python
#: code:addons/industry_fsm_sale/models/project_task.py:0
msgid ""
"By saving this change, all timesheet entries will be linked to the selected "
"Sales Order Item without distinction."
msgstr ""
"Al guardar este cambio, todas las entradas de hojas de horas se vincularán a"
" los artículos seleccionados de la orden de venta sin distinción."

#. module: industry_fsm_sale
#. odoo-javascript
#: code:addons/industry_fsm_sale/static/src/js/tours/industry_fsm_sale_tour.js:0
msgid ""
"Choose a <b>name</b> for your product <i>(e.g. Bolts, Screws, Boiler, "
"etc.).</i>"
msgstr ""
"Elija un <b>nombre</b> para su producto <i>(por ejemplo, pernos, tornillos, "
"calentador, etc.).</i>"

#. module: industry_fsm_sale
#. odoo-javascript
#: code:addons/industry_fsm_sale/static/src/js/tours/industry_fsm_sale_tour.js:0
msgid ""
"Click on a product to add it to your <b>list of materials</b>. <i>Tip: for "
"large quantities, click on the number to edit it directly.</i>"
msgstr ""
"Haga clic en un producto para agregarlo a su <b>lista de materiales</b>. "
"<i>Consejo: para grandes cantidades, haga clic en el número para editarlo "
"directamente.</i>"

#. module: industry_fsm_sale
#: model:ir.model,name:industry_fsm_sale.model_res_config_settings
msgid "Config Settings"
msgstr "Ajustes de configuración"

#. module: industry_fsm_sale
#. odoo-javascript
#: code:addons/industry_fsm_sale/static/src/js/tours/industry_fsm_sale_tour.js:0
msgid "Confirm the creation of your <b>invoice</b>."
msgstr "Confirme la creación de su <b>factura</b>."

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.project_task_view_list_fsm_sale_inherit
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.view_task_form2_inherit
msgid "Create Invoice"
msgstr "Crear factura"

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.project_view_form_inherit
msgid "Create new quotations directly from tasks"
msgstr "Cree nuevas cotizaciones directamente desde las tareas"

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_task__currency_id
msgid "Currency"
msgstr "Moneda"

#. module: industry_fsm_sale
#. odoo-javascript
#: code:addons/industry_fsm_sale/static/src/js/tours/industry_fsm_sale_tour.js:0
msgid "Customize your <b>layout</b>."
msgstr "Personalice su <b>diseño</b>."

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.project_view_form_inherit
msgid "Default Service"
msgstr "Servicio predeterminado"

#. module: industry_fsm_sale
#. odoo-python
#: code:addons/industry_fsm_sale/models/sale_order.py:0
msgid "Extra Quotation Created: %s"
msgstr "Cotización adicional creada: %s"

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_project__allow_quotations
msgid "Extra Quotations"
msgstr "Cotizaciones adicionales"

#. module: industry_fsm_sale
#: model:ir.model,name:industry_fsm_sale.model_report_project_task_user_fsm
msgid "FSM Tasks Analysis"
msgstr "Análisis de las tareas del Servicio externo "

#. module: industry_fsm_sale
#: model:product.template,name:industry_fsm_sale.field_service_product_product_template
msgid "Field Service"
msgstr "Servicio externo"

#. module: industry_fsm_sale
#: model:ir.model.fields.selection,name:industry_fsm_sale.selection__report_project_task_user_fsm__invoice_status__invoiced
msgid "Fully Invoiced"
msgstr "Facturado por completo"

#. module: industry_fsm_sale
#. odoo-javascript
#: code:addons/industry_fsm_sale/static/src/js/tours/industry_fsm_sale_tour.js:0
msgid "Go back to your Field Service <b>task</b>."
msgstr "Volver a su<b>tarea</b> de servicio externo."

#. module: industry_fsm_sale
#: model:ir.model.fields,help:industry_fsm_sale.field_project_task__under_warranty
msgid ""
"If ticked, the time and materials used for this task will not be billed to "
"the customer. However, the inventory of consumed materials will still be "
"updated."
msgstr ""
"Si esta opción está seleccionada, el tiempo y los materiales utilizados para"
" esta tarea no se le facturarán al cliente, pero se actualizará el "
"inventario de los materiales consumidos."

#. module: industry_fsm_sale
#. odoo-python
#: code:addons/industry_fsm_sale/models/product_product.py:0
msgid "Invalid operator: %s"
msgstr "Operador no válido: %s"

#. module: industry_fsm_sale
#. odoo-python
#: code:addons/industry_fsm_sale/models/product_product.py:0
msgid "Invalid value: %s"
msgstr "Valor no válido: %s"

#. module: industry_fsm_sale
#. odoo-javascript
#: code:addons/industry_fsm_sale/static/src/js/tours/industry_fsm_sale_tour.js:0
msgid "Invite your customer to <b>validate and sign your task report</b>."
msgstr "Invite a su cliente a que <b>valide y firme su reporte de tarea.</b>."

#. module: industry_fsm_sale
#. odoo-python
#: code:addons/industry_fsm_sale/models/project_task.py:0
msgid "Invoice"
msgstr "Factura"

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_task__portal_invoice_count
msgid "Invoice Count"
msgstr "Número de facturas"

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_report_project_task_user_fsm__invoice_status
msgid "Invoice Status"
msgstr "Estado de la factura"

#. module: industry_fsm_sale
#: model_terms:ir.actions.act_window,help:industry_fsm_sale.project_task_action_to_invoice_fsm
#: model_terms:ir.actions.act_window,help:industry_fsm_sale.project_task_action_to_invoice_fsm2
msgid ""
"Invoice your time and material to your customers once your tasks are done."
msgstr ""
"Facture su tiempo y materiales a sus clientes una vez que haya terminado sus"
" tareas."

#. module: industry_fsm_sale
#. odoo-python
#: code:addons/industry_fsm_sale/models/project_task.py:0
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.project_sharing_project_task_inherit_view_form
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.view_task_form2_inherit
msgid "Invoices"
msgstr "Facturas"

#. module: industry_fsm_sale
#. odoo-javascript
#: code:addons/industry_fsm_sale/static/src/js/tours/industry_fsm_sale_tour.js:0
msgid "Let's <b>track the material</b> you use for your task."
msgstr ""
"Llevemos un <b>seguimiento de los materiales</b>que utiliza en su tarea."

#. module: industry_fsm_sale
#. odoo-javascript
#: code:addons/industry_fsm_sale/static/src/js/tours/industry_fsm_sale_tour.js:0
msgid "Let's create a new <b>product</b>."
msgstr "Creemos un <b>producto</b> nuevo."

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_task__material_line_total_price
msgid "Material Line Total Price"
msgstr "Precio total de línea de material"

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_product_product__fsm_quantity
msgid "Material Quantity"
msgstr "Cantidad de material"

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.view_task_form2_inherit
msgid "New Quotation"
msgstr "Nueva cotización"

#. module: industry_fsm_sale
#: model_terms:ir.actions.act_window,help:industry_fsm_sale.product_action_fsm
msgid "No products found. Let's create one!"
msgstr "No se ha encontrado ningún producto. Creemos uno."

#. module: industry_fsm_sale
#: model_terms:ir.actions.act_window,help:industry_fsm_sale.project_task_action_to_invoice_fsm
#: model_terms:ir.actions.act_window,help:industry_fsm_sale.project_task_action_to_invoice_fsm2
msgid "No tasks found. Let's create one!"
msgstr "No se encontraron tareas. ¡Creemos una!"

#. module: industry_fsm_sale
#: model:ir.model.fields.selection,name:industry_fsm_sale.selection__report_project_task_user_fsm__invoice_status__no
msgid "Nothing to Invoice"
msgstr "Nada que facturar"

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_task__invoice_count
msgid "Number of invoices"
msgstr "Número de facturas"

#. module: industry_fsm_sale
#: model:product.template,name:industry_fsm_sale.field_service_product_zero_cost_1_product_template
msgid "Plumbing Valves"
msgstr "Válvulas de plomería"

#. module: industry_fsm_sale
#: model:product.template,name:industry_fsm_sale.field_service_product_zero_cost_4_product_template
msgid "Plumbing Washers"
msgstr "Arandelas para plomería"

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_task__portal_quotation_count
msgid "Portal Quotation Count"
msgstr "Número de cotizaciones del portal"

#. module: industry_fsm_sale
#: model:ir.model,name:industry_fsm_sale.model_product_template
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.worksheet_time_and_material
msgid "Product"
msgstr "Producto"

#. module: industry_fsm_sale
#: model:ir.model,name:industry_fsm_sale.model_product_product
msgid "Product Variant"
msgstr "Variante del producto"

#. module: industry_fsm_sale
#: model:ir.actions.act_window,name:industry_fsm_sale.product_action_fsm
#: model:ir.ui.menu,name:industry_fsm_sale.fsm_menu_settings_product
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.view_task_form2_inherit
msgid "Products"
msgstr "Productos"

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_project__allow_material
msgid "Products on Tasks"
msgstr "Productos en tareas"

#. module: industry_fsm_sale
#: model:ir.model,name:industry_fsm_sale.model_project_project
#: model:ir.model.fields,field_description:industry_fsm_sale.field_product_product__project_id
#: model:ir.model.fields,field_description:industry_fsm_sale.field_product_template__project_id
msgid "Project"
msgstr "Proyecto"

#. module: industry_fsm_sale
#: model:ir.model,name:industry_fsm_sale.model_project_sale_line_employee_map
msgid "Project Sales line, employee mapping"
msgstr "Línea de venta de proyecto, mapeo de empleados"

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.worksheet_time_and_material
msgid "Quantity"
msgstr "Cantidad"

#. module: industry_fsm_sale
#. odoo-python
#: code:addons/industry_fsm_sale/controllers/portal.py:0
msgid "Quotation"
msgstr "Cotización"

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_task__quotation_count
msgid "Quotation Count"
msgstr "Número de cotizaciones"

#. module: industry_fsm_sale
#. odoo-python
#: code:addons/industry_fsm_sale/controllers/portal.py:0
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.project_sharing_project_task_inherit_view_form
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.view_task_form2_inherit
msgid "Quotations"
msgstr "Cotizaciones"

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.view_order_form_inherit_sale_project
msgid "Related Task"
msgstr "Tarea relacionada"

#. module: industry_fsm_sale
#: model:product.template,name:industry_fsm_sale.field_service_product_zero_cost_2_product_template
msgid "Safety Gloves"
msgstr "Guantes de seguridad"

#. module: industry_fsm_sale
#: model:ir.model,name:industry_fsm_sale.model_sale_advance_payment_inv
msgid "Sales Advance Payment Invoice"
msgstr "Factura de pago anticipado de ventas"

#. module: industry_fsm_sale
#: model:ir.model,name:industry_fsm_sale.model_sale_order
msgid "Sales Order"
msgstr "Orden de venta"

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_project__sale_line_id
msgid "Sales Order Item"
msgstr "Artículo de la orden de venta"

#. module: industry_fsm_sale
#: model:ir.model,name:industry_fsm_sale.model_sale_order_line
msgid "Sales Order Line"
msgstr "Línea de la orden de venta"

#. module: industry_fsm_sale
#: model:ir.model.fields,help:industry_fsm_sale.field_project_project__sale_line_id
msgid ""
"Sales order item that will be selected by default on the tasks and timesheets of this project, except if the employee set on the timesheets is explicitely linked to another sales order item on the project.\n"
"It can be modified on each task and timesheet entry individually if necessary."
msgstr ""
"Artículo de orden de venta que se seleccionará de forma predeterminada en las tareas y hojas de horas de este proyecto, a no ser que el empleado establecido en las hojas de horas esté vinculado a otro artículo de orden de venta del proyecto.\n"
"Esto se puede modificar en cada tarea y entrada de hoja de horas si es necesario."

#. module: industry_fsm_sale
#. odoo-javascript
#: code:addons/industry_fsm_sale/static/src/js/tours/industry_fsm_sale_tour.js:0
msgid "Save time by automatically generating a <b>signature</b>."
msgstr "Ahorre tiempo generando una<b>firma</b>."

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_sale_line_employee_map__timesheet_product_id
msgid "Service"
msgstr "Servicio"

#. module: industry_fsm_sale
#: model:ir.model,name:industry_fsm_sale.model_project_task
#: model:ir.model.fields,field_description:industry_fsm_sale.field_sale_order__task_id
msgid "Task"
msgstr "Tarea"

#. module: industry_fsm_sale
#: model:ir.model,name:industry_fsm_sale.model_project_task_recurrence
msgid "Task Recurrence"
msgstr "Recurrencia de la tarea"

#. module: industry_fsm_sale
#: model:ir.model.fields,help:industry_fsm_sale.field_sale_order__task_id
msgid "Task from which this quotation have been created"
msgstr "Tareas creadas a partir de esta cotización"

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.worksheet_time_and_material
msgid "Taxes"
msgstr "Impuestos"

#. module: industry_fsm_sale
#. odoo-python
#: code:addons/industry_fsm_sale/models/product_template.py:0
msgid ""
"The following products are currently associated with a Field Service "
"project, you cannot change their Invoicing Policy or Type:%s"
msgstr ""
"Los siguientes productos están asociados a un proyecto de Servicio externo y"
" no puede cambiar su política de facturación o tipo: %s"

#. module: industry_fsm_sale
#: model:ir.model.constraint,message:industry_fsm_sale.constraint_project_project_material_imply_billable
msgid "The material can be allowed only when the task can be billed."
msgstr "Solo se permite el material cuando se puede facturar la tarea."

#. module: industry_fsm_sale
#: model:ir.model.constraint,message:industry_fsm_sale.constraint_project_project_timesheet_product_required_if_billable_and_time
msgid ""
"The timesheet product is required when the fsm project can be billed and "
"timesheets are allowed."
msgstr ""
"El producto de la hoja de horas es necesario cuando se puede facturar el "
"proyecto de Servicio externo y se permiten las hojas de horas."

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.portal_my_task
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.worksheet_custom_page
msgid "Time &amp; Material"
msgstr "Tiempo y materiales"

#. module: industry_fsm_sale
#: model:ir.actions.act_window,name:industry_fsm_sale.project_task_action_to_invoice_fsm
#: model:ir.actions.act_window,name:industry_fsm_sale.project_task_action_to_invoice_fsm2
#: model:ir.model.fields.selection,name:industry_fsm_sale.selection__report_project_task_user_fsm__invoice_status__to_invoice
#: model:ir.ui.menu,name:industry_fsm_sale.fsm_menu_all_tasks_invoice
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.project_task_view_search_fsm_inherit_sale
msgid "To Invoice"
msgstr "Por facturar"

#. module: industry_fsm_sale
#: model:ir.actions.server,name:industry_fsm_sale.project_task_to_invoice_fsm_server_action
msgid "To Invoice Server Action"
msgstr "Acción del servidor por facturar"

#. module: industry_fsm_sale
#: model_terms:ir.actions.act_window,help:industry_fsm_sale.product_action_fsm
msgid "Track and bill the material used to complete your tasks."
msgstr ""
"Registre y facture los materiales que utiliza para completar sus tareas."

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.project_view_form_inherit
msgid "Track the material used to complete tasks"
msgstr "Registre los materiales que utiliza para completar las tareas."

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_task__under_warranty
msgid "Under Warranty"
msgstr "Bajo garantía"

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_sale_line_employee_map__price_unit
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.worksheet_time_and_material
msgid "Unit Price"
msgstr "Precio unitario"

#. module: industry_fsm_sale
#: model:ir.model.fields.selection,name:industry_fsm_sale.selection__report_project_task_user_fsm__invoice_status__upselling
msgid "Upselling Opportunity"
msgstr "Oportunidad de venta adicional"

#. module: industry_fsm_sale
#. odoo-javascript
#: code:addons/industry_fsm_sale/static/src/js/tours/industry_fsm_sale_tour.js:0
msgid "Use the breadcrumbs to navigate to your <b>list of products</b>."
msgstr "Utilice las migas de pan para ir a su <b>lista de productos</b>."

#. module: industry_fsm_sale
#. odoo-javascript
#: code:addons/industry_fsm_sale/static/src/js/tours/industry_fsm_sale_tour.js:0
msgid "Use the breadcrumbs to return to your <b>task</b>."
msgstr "Utilice las migas de pan para volver a su <b>tarea</b>."

#. module: industry_fsm_sale
#. odoo-javascript
#: code:addons/industry_fsm_sale/static/src/js/tours/industry_fsm_sale_tour.js:0
msgid "Validate the <b>signature</b>."
msgstr "Valide la <b>firma</b>."

#. module: industry_fsm_sale
#. odoo-javascript
#: code:addons/industry_fsm_sale/static/src/js/tours/industry_fsm_sale_tour.js:0
msgid "Wait for the invoice to show up"
msgstr "Espere a que aparezca la factura"

#. module: industry_fsm_sale
#: model:product.template,name:industry_fsm_sale.field_service_product_zero_cost_3_product_template
msgid "Water Testing Kits"
msgstr "Kits de análisis de agua"
