# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* industry_fsm_sale
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-25 09:14+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON><PERSON>, 2025\n"
"Language-Team: French (https://app.transifex.com/odoo/teams/41243/fr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fr\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: industry_fsm_sale
#. odoo-python
#: code:addons/industry_fsm_sale/models/project_task.py:0
msgid "%(project_name)s - %(task_name)s"
msgstr "%(project_name)s - %(task_name)s"

#. module: industry_fsm_sale
#. odoo-javascript
#: code:addons/industry_fsm_sale/static/src/js/tours/industry_fsm_sale_tour.js:0
msgid "<b>Invoice your time and material</b> to your customer."
msgstr "<b>Facturez votre temps et votre matériel</b> à votre client."

#. module: industry_fsm_sale
#. odoo-javascript
#: code:addons/industry_fsm_sale/static/src/js/tours/industry_fsm_sale_tour.js:0
msgid "<b>Review and sign</b> the <b>task report</b> with your customer."
msgstr ""
"<b>Passez en revue et signez</b> le <b>rapport de tâche</b> avec votre "
"client."

#. module: industry_fsm_sale
#. odoo-javascript
#: code:addons/industry_fsm_sale/static/src/js/tours/industry_fsm_sale_tour.js:0
msgid "<b>Send your task report</b> to your customer."
msgstr "<b>Envoyez votre rapport de tâche</b> à votre client."

#. module: industry_fsm_sale
#. odoo-python
#: code:addons/industry_fsm_sale/models/project_task.py:0
msgid ""
"<p class=\"o_view_nocontent_smiling_face\">\n"
"                            No products found. Let's create one!\n"
"                        </p><p>\n"
"                            Keep track of the products you are using to complete your tasks, and invoice your customers for the goods.\n"
"                            Tip: using kits, you can add multiple products at once.\n"
"                        </p><p>\n"
"                            When your task is marked as done, your stock will be updated automatically. Simply choose a warehouse\n"
"                            in your profile from where to draw stock.\n"
"                        </p>"
msgstr ""
"<p class=\"o_view_nocontent_smiling_face\">Aucun produit trouvé. Créons-en un !\n"
"</p><p>\n"
"Gardez une trace des produits que vous utilisez pour accomplir vos tâches et facturez vos clients pour les marchandises.\n"
"Astuce : grâce aux kits, vous pouvez ajouter plusieurs produits à la fois.\n"
"</p><p>\n"
"Lorsque votre tâche est marquée comme terminée, votre stock sera mis à jour automatiquement. Choisissez simplement un entrepôt \n"
"dans votre profil d'où tirer le stock.\n"
"</p>"

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.project_view_form_inherit
msgid ""
"<span invisible=\"not is_fsm\">\n"
"                    Define the service and the rate at which an employee's time is billed based on their expertise, skills or experience.\n"
"                </span>"
msgstr ""
"<span invisible=\"not is_fsm\">\n"
"                    Définissez le service et le taux auquel le temps d'un employé est facturé en fonction de son expertise, de ses compétences ou de son expérience.\n"
"                </span>"

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.worksheet_time_and_material
msgid "<span>Disc.%</span>"
msgstr "<span>Rem.%</span>"

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.project_sharing_project_task_inherit_view_form
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.view_task_form2_inherit
msgid "<span>Products</span>"
msgstr "<span>Produits</span>"

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.worksheet_time_and_material
msgid "<strong class=\"mr16\">Subtotal</strong>"
msgstr "<strong class=\"mr16\">Sous-total</strong>"

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.worksheet_time_and_material
msgid "<strong>Amount Due: </strong>"
msgstr "<strong>Montant dû : </strong>"

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.worksheet_time_and_material
msgid "<strong>Taxes</strong>"
msgstr "<strong>Taxes</strong>"

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.worksheet_time_and_material
msgid "<strong>Total</strong>"
msgstr "<strong>Total</strong>"

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.worksheet_time_and_material
msgid "<strong>Untaxed amount</strong>"
msgstr "<strong>Montant hors taxes</strong>"

#. module: industry_fsm_sale
#. odoo-python
#: code:addons/industry_fsm_sale/models/project_task.py:0
msgid "A customer should be set on the task to generate a worksheet."
msgstr ""
"Un client doit être défini sur la tâche afin de générer une feuille de "
"travail."

#. module: industry_fsm_sale
#. odoo-python
#: code:addons/industry_fsm_sale/models/project_task.py:0
msgid "Add Products"
msgstr "Ajouter des produits"

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.industry_fsm_sale_product_catalog_inherit_search_view
msgid "Added Products"
msgstr "Produits ajoutés"

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.worksheet_time_and_material
msgid "Amount"
msgstr "Montant"

#. module: industry_fsm_sale
#: model:ir.model.constraint,message:industry_fsm_sale.constraint_project_project_fsm_imply_task_rate
msgid "An FSM project must be billed at task rate or employee rate."
msgstr ""
"Un projet de services sur site doit être facturé à un taux par tâche ou à un"
" taux par employé."

#. module: industry_fsm_sale
#. odoo-python
#: code:addons/industry_fsm_sale/wizard/sale_make_invoice_advance.py:0
msgid "An invoice has been created: %s"
msgstr "Une facture a été créée : %s"

#. module: industry_fsm_sale
#: model:ir.model,name:industry_fsm_sale.model_account_analytic_line
msgid "Analytic Line"
msgstr "Ligne analytique"

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_project__allow_billable
msgid "Billable"
msgstr "Facturable"

#. module: industry_fsm_sale
#. odoo-python
#: code:addons/industry_fsm_sale/models/project_task.py:0
msgid ""
"By saving this change, all timesheet entries will be linked to the selected "
"Sales Order Item without distinction."
msgstr ""
"En sauvegardant ce changement, toutes les entrées de feuilles de temps "
"seront liées à l'article de la commande client sélectionnée sans "
"distinction."

#. module: industry_fsm_sale
#. odoo-javascript
#: code:addons/industry_fsm_sale/static/src/js/tours/industry_fsm_sale_tour.js:0
msgid ""
"Choose a <b>name</b> for your product <i>(e.g. Bolts, Screws, Boiler, "
"etc.).</i>"
msgstr ""
"Choisissez un <b>nom</b> pour votre produit <i>(par exemple : boulons, vis, "
"chaudière, etc.).</i>"

#. module: industry_fsm_sale
#. odoo-javascript
#: code:addons/industry_fsm_sale/static/src/js/tours/industry_fsm_sale_tour.js:0
msgid ""
"Click on a product to add it to your <b>list of materials</b>. <i>Tip: for "
"large quantities, click on the number to edit it directly.</i>"
msgstr ""
"Cliquez sur un produit pour l'ajouter à votre <b>liste de matériaux</b>. "
"<i>Astuce : pour les grandes quantités, cliquez sur le numéro pour le "
"modifier directement.</i>"

#. module: industry_fsm_sale
#: model:ir.model,name:industry_fsm_sale.model_res_config_settings
msgid "Config Settings"
msgstr "Paramètres de configuration"

#. module: industry_fsm_sale
#. odoo-javascript
#: code:addons/industry_fsm_sale/static/src/js/tours/industry_fsm_sale_tour.js:0
msgid "Confirm the creation of your <b>invoice</b>."
msgstr "Confirmez la création de votre <b>facture</b>."

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.project_task_view_list_fsm_sale_inherit
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.view_task_form2_inherit
msgid "Create Invoice"
msgstr "Créer une facture"

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.project_view_form_inherit
msgid "Create new quotations directly from tasks"
msgstr "Créez de nouveaux devis directement depuis les tâches"

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_task__currency_id
msgid "Currency"
msgstr "Devise"

#. module: industry_fsm_sale
#. odoo-javascript
#: code:addons/industry_fsm_sale/static/src/js/tours/industry_fsm_sale_tour.js:0
msgid "Customize your <b>layout</b>."
msgstr "Personnalisez votre <b>mise en page</b>."

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.project_view_form_inherit
msgid "Default Service"
msgstr "Service par défaut"

#. module: industry_fsm_sale
#. odoo-python
#: code:addons/industry_fsm_sale/models/sale_order.py:0
msgid "Extra Quotation Created: %s"
msgstr "Devis supplémentaire créé : %s"

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_project__allow_quotations
msgid "Extra Quotations"
msgstr "Devis supplémentaires"

#. module: industry_fsm_sale
#: model:ir.model,name:industry_fsm_sale.model_report_project_task_user_fsm
msgid "FSM Tasks Analysis"
msgstr "Analyse des tâches de Services sur site"

#. module: industry_fsm_sale
#: model:product.template,name:industry_fsm_sale.field_service_product_product_template
msgid "Field Service"
msgstr "Service sur site"

#. module: industry_fsm_sale
#: model:ir.model.fields.selection,name:industry_fsm_sale.selection__report_project_task_user_fsm__invoice_status__invoiced
msgid "Fully Invoiced"
msgstr "Entièrement facturé"

#. module: industry_fsm_sale
#. odoo-javascript
#: code:addons/industry_fsm_sale/static/src/js/tours/industry_fsm_sale_tour.js:0
msgid "Go back to your Field Service <b>task</b>."
msgstr "Revenez à votre <b>tâche</b> de services sur site."

#. module: industry_fsm_sale
#: model:ir.model.fields,help:industry_fsm_sale.field_project_task__under_warranty
msgid ""
"If ticked, the time and materials used for this task will not be billed to "
"the customer. However, the inventory of consumed materials will still be "
"updated."
msgstr ""
"Si cette case est cochée, le temps et les matériaux utilisés pour cette "
"tâche ne seront pas facturés au client. Le stock des matériaux utilisés sera"
" toutefois mis à jour."

#. module: industry_fsm_sale
#. odoo-python
#: code:addons/industry_fsm_sale/models/product_product.py:0
msgid "Invalid operator: %s"
msgstr "Opérateur invalide : %s"

#. module: industry_fsm_sale
#. odoo-python
#: code:addons/industry_fsm_sale/models/product_product.py:0
msgid "Invalid value: %s"
msgstr "Valeur invalide : %s"

#. module: industry_fsm_sale
#. odoo-javascript
#: code:addons/industry_fsm_sale/static/src/js/tours/industry_fsm_sale_tour.js:0
msgid "Invite your customer to <b>validate and sign your task report</b>."
msgstr ""
"Invitez votre client à <b>valider et signer votre rapport de tâche</b>."

#. module: industry_fsm_sale
#. odoo-python
#: code:addons/industry_fsm_sale/models/project_task.py:0
msgid "Invoice"
msgstr "Facture"

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_task__portal_invoice_count
msgid "Invoice Count"
msgstr "Nombre de factures"

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_report_project_task_user_fsm__invoice_status
msgid "Invoice Status"
msgstr "Statut de la facture"

#. module: industry_fsm_sale
#: model_terms:ir.actions.act_window,help:industry_fsm_sale.project_task_action_to_invoice_fsm
#: model_terms:ir.actions.act_window,help:industry_fsm_sale.project_task_action_to_invoice_fsm2
msgid ""
"Invoice your time and material to your customers once your tasks are done."
msgstr ""
"Facturez votre temps et votre matériel à vos clients une fois vos tâches "
"terminées."

#. module: industry_fsm_sale
#. odoo-python
#: code:addons/industry_fsm_sale/models/project_task.py:0
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.project_sharing_project_task_inherit_view_form
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.view_task_form2_inherit
msgid "Invoices"
msgstr "Factures clients"

#. module: industry_fsm_sale
#. odoo-javascript
#: code:addons/industry_fsm_sale/static/src/js/tours/industry_fsm_sale_tour.js:0
msgid "Let's <b>track the material</b> you use for your task."
msgstr "<b>Suivons le matériel</b> que vous utilisez pour votre tâche."

#. module: industry_fsm_sale
#. odoo-javascript
#: code:addons/industry_fsm_sale/static/src/js/tours/industry_fsm_sale_tour.js:0
msgid "Let's create a new <b>product</b>."
msgstr "Créons un nouveau <b>produit</b>."

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_task__material_line_total_price
msgid "Material Line Total Price"
msgstr "Prix total de la ligne de matériel"

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_product_product__fsm_quantity
msgid "Material Quantity"
msgstr "Quantité de matériel"

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.view_task_form2_inherit
msgid "New Quotation"
msgstr "Nouveau devis"

#. module: industry_fsm_sale
#: model_terms:ir.actions.act_window,help:industry_fsm_sale.product_action_fsm
msgid "No products found. Let's create one!"
msgstr "Aucun produit trouvé. Créons-en un!"

#. module: industry_fsm_sale
#: model_terms:ir.actions.act_window,help:industry_fsm_sale.project_task_action_to_invoice_fsm
#: model_terms:ir.actions.act_window,help:industry_fsm_sale.project_task_action_to_invoice_fsm2
msgid "No tasks found. Let's create one!"
msgstr "Aucune tâche trouvée. Créons-en une !"

#. module: industry_fsm_sale
#: model:ir.model.fields.selection,name:industry_fsm_sale.selection__report_project_task_user_fsm__invoice_status__no
msgid "Nothing to Invoice"
msgstr "Rien à facturer"

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_task__invoice_count
msgid "Number of invoices"
msgstr "Nombre de factures"

#. module: industry_fsm_sale
#: model:product.template,name:industry_fsm_sale.field_service_product_zero_cost_1_product_template
msgid "Plumbing Valves"
msgstr "Vannes de plomberie"

#. module: industry_fsm_sale
#: model:product.template,name:industry_fsm_sale.field_service_product_zero_cost_4_product_template
msgid "Plumbing Washers"
msgstr "Joints de plomberie"

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_task__portal_quotation_count
msgid "Portal Quotation Count"
msgstr "Nombre de devis du portail"

#. module: industry_fsm_sale
#: model:ir.model,name:industry_fsm_sale.model_product_template
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.worksheet_time_and_material
msgid "Product"
msgstr "Produit"

#. module: industry_fsm_sale
#: model:ir.model,name:industry_fsm_sale.model_product_product
msgid "Product Variant"
msgstr "Variante de produit"

#. module: industry_fsm_sale
#: model:ir.actions.act_window,name:industry_fsm_sale.product_action_fsm
#: model:ir.ui.menu,name:industry_fsm_sale.fsm_menu_settings_product
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.view_task_form2_inherit
msgid "Products"
msgstr "Produits"

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_project__allow_material
msgid "Products on Tasks"
msgstr "Produits sur les tâches"

#. module: industry_fsm_sale
#: model:ir.model,name:industry_fsm_sale.model_project_project
#: model:ir.model.fields,field_description:industry_fsm_sale.field_product_product__project_id
#: model:ir.model.fields,field_description:industry_fsm_sale.field_product_template__project_id
msgid "Project"
msgstr "Projet"

#. module: industry_fsm_sale
#: model:ir.model,name:industry_fsm_sale.model_project_sale_line_employee_map
msgid "Project Sales line, employee mapping"
msgstr "Ligne de vente du projet, mapping de l'employé"

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.worksheet_time_and_material
msgid "Quantity"
msgstr "Quantité"

#. module: industry_fsm_sale
#. odoo-python
#: code:addons/industry_fsm_sale/controllers/portal.py:0
msgid "Quotation"
msgstr "Devis"

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_task__quotation_count
msgid "Quotation Count"
msgstr "Nombre de devis"

#. module: industry_fsm_sale
#. odoo-python
#: code:addons/industry_fsm_sale/controllers/portal.py:0
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.project_sharing_project_task_inherit_view_form
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.view_task_form2_inherit
msgid "Quotations"
msgstr "Devis"

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.view_order_form_inherit_sale_project
msgid "Related Task"
msgstr "Tâche liée"

#. module: industry_fsm_sale
#: model:product.template,name:industry_fsm_sale.field_service_product_zero_cost_2_product_template
msgid "Safety Gloves"
msgstr "Gants de sécurité"

#. module: industry_fsm_sale
#: model:ir.model,name:industry_fsm_sale.model_sale_advance_payment_inv
msgid "Sales Advance Payment Invoice"
msgstr "Facture de paiement d'avance"

#. module: industry_fsm_sale
#: model:ir.model,name:industry_fsm_sale.model_sale_order
msgid "Sales Order"
msgstr "Commande client"

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_project__sale_line_id
msgid "Sales Order Item"
msgstr "Article de la commande client"

#. module: industry_fsm_sale
#: model:ir.model,name:industry_fsm_sale.model_sale_order_line
msgid "Sales Order Line"
msgstr "Ligne de commande"

#. module: industry_fsm_sale
#: model:ir.model.fields,help:industry_fsm_sale.field_project_project__sale_line_id
msgid ""
"Sales order item that will be selected by default on the tasks and timesheets of this project, except if the employee set on the timesheets is explicitely linked to another sales order item on the project.\n"
"It can be modified on each task and timesheet entry individually if necessary."
msgstr ""
"Article de la commande client qui sera sélectionné par défaut sur les tâches et feuilles de temps de ce projet, sauf si l'employé renseigné sur les feuilles de temps est explicitement lié à un article de la commande client sur le projet.\n"
"Il peut être modifié sur chaque tâche et entrée de feuille de temps individuellement si nécessaire."

#. module: industry_fsm_sale
#. odoo-javascript
#: code:addons/industry_fsm_sale/static/src/js/tours/industry_fsm_sale_tour.js:0
msgid "Save time by automatically generating a <b>signature</b>."
msgstr "Gagnez du temps en générant une <b>signature</b> automatiquement."

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_sale_line_employee_map__timesheet_product_id
msgid "Service"
msgstr "Service"

#. module: industry_fsm_sale
#: model:ir.model,name:industry_fsm_sale.model_project_task
#: model:ir.model.fields,field_description:industry_fsm_sale.field_sale_order__task_id
msgid "Task"
msgstr "Tâche"

#. module: industry_fsm_sale
#: model:ir.model,name:industry_fsm_sale.model_project_task_recurrence
msgid "Task Recurrence"
msgstr "Récurrence de tâche"

#. module: industry_fsm_sale
#: model:ir.model.fields,help:industry_fsm_sale.field_sale_order__task_id
msgid "Task from which this quotation have been created"
msgstr "Tâche à partir de laquelle ce devis a été créé"

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.worksheet_time_and_material
msgid "Taxes"
msgstr "Taxes"

#. module: industry_fsm_sale
#. odoo-python
#: code:addons/industry_fsm_sale/models/product_template.py:0
msgid ""
"The following products are currently associated with a Field Service "
"project, you cannot change their Invoicing Policy or Type:%s"
msgstr ""
"Les produits suivants sont actuellement associés à un projet de service sur "
"site, vous ne pouvez pas modifier leur politique ou leur type de facturation"
" :%s"

#. module: industry_fsm_sale
#: model:ir.model.constraint,message:industry_fsm_sale.constraint_project_project_material_imply_billable
msgid "The material can be allowed only when the task can be billed."
msgstr ""
"Le matériel peut seulement être autorisé lorsque la tâche peut être "
"facturée."

#. module: industry_fsm_sale
#: model:ir.model.constraint,message:industry_fsm_sale.constraint_project_project_timesheet_product_required_if_billable_and_time
msgid ""
"The timesheet product is required when the fsm project can be billed and "
"timesheets are allowed."
msgstr ""
"Le produit feuille de temps est requis lorsque le projet service sur site "
"peut être facturé et que les feuilles de temps sont autorisées."

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.portal_my_task
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.worksheet_custom_page
msgid "Time &amp; Material"
msgstr "Temps &amp; Matériel"

#. module: industry_fsm_sale
#: model:ir.actions.act_window,name:industry_fsm_sale.project_task_action_to_invoice_fsm
#: model:ir.actions.act_window,name:industry_fsm_sale.project_task_action_to_invoice_fsm2
#: model:ir.model.fields.selection,name:industry_fsm_sale.selection__report_project_task_user_fsm__invoice_status__to_invoice
#: model:ir.ui.menu,name:industry_fsm_sale.fsm_menu_all_tasks_invoice
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.project_task_view_search_fsm_inherit_sale
msgid "To Invoice"
msgstr "À facturer"

#. module: industry_fsm_sale
#: model:ir.actions.server,name:industry_fsm_sale.project_task_to_invoice_fsm_server_action
msgid "To Invoice Server Action"
msgstr "Action serveur pour facturer"

#. module: industry_fsm_sale
#: model_terms:ir.actions.act_window,help:industry_fsm_sale.product_action_fsm
msgid "Track and bill the material used to complete your tasks."
msgstr "Suivez et facturez le matériel utilisé pour accomplir vos tâches."

#. module: industry_fsm_sale
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.project_view_form_inherit
msgid "Track the material used to complete tasks"
msgstr "Suivez le matériel utilisé pour accomplir les tâches"

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_task__under_warranty
msgid "Under Warranty"
msgstr "Sous garantie"

#. module: industry_fsm_sale
#: model:ir.model.fields,field_description:industry_fsm_sale.field_project_sale_line_employee_map__price_unit
#: model_terms:ir.ui.view,arch_db:industry_fsm_sale.worksheet_time_and_material
msgid "Unit Price"
msgstr "Prix unitaire"

#. module: industry_fsm_sale
#: model:ir.model.fields.selection,name:industry_fsm_sale.selection__report_project_task_user_fsm__invoice_status__upselling
msgid "Upselling Opportunity"
msgstr "Opportunité de vente incitative"

#. module: industry_fsm_sale
#. odoo-javascript
#: code:addons/industry_fsm_sale/static/src/js/tours/industry_fsm_sale_tour.js:0
msgid "Use the breadcrumbs to navigate to your <b>list of products</b>."
msgstr ""
"Utilisez le fil d'Ariane pour retourner à votre <b>liste de produits</b>."

#. module: industry_fsm_sale
#. odoo-javascript
#: code:addons/industry_fsm_sale/static/src/js/tours/industry_fsm_sale_tour.js:0
msgid "Use the breadcrumbs to return to your <b>task</b>."
msgstr "Utilisez le fil d'Ariane pour revenir à votre <b>tâche</b>."

#. module: industry_fsm_sale
#. odoo-javascript
#: code:addons/industry_fsm_sale/static/src/js/tours/industry_fsm_sale_tour.js:0
msgid "Validate the <b>signature</b>."
msgstr "Validez la <b>signature</b>."

#. module: industry_fsm_sale
#. odoo-javascript
#: code:addons/industry_fsm_sale/static/src/js/tours/industry_fsm_sale_tour.js:0
msgid "Wait for the invoice to show up"
msgstr "Attendez que la facture s'affiche"

#. module: industry_fsm_sale
#: model:product.template,name:industry_fsm_sale.field_service_product_zero_cost_3_product_template
msgid "Water Testing Kits"
msgstr "Kits d'analyse de l'eau"
