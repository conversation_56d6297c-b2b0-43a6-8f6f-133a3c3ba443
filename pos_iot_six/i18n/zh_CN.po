# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* pos_iot_six
# 
# Translators:
# Wil <PERSON><PERSON>, 2024
# <PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-19 09:53+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON>, 2025\n"
"Language-Team: Chinese (China) (https://app.transifex.com/odoo/teams/41243/zh_CN/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_CN\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: pos_iot_six
#: model_terms:ir.ui.view,arch_db:pos_iot_six.view_add_six_terminal
msgid "Add Terminal"
msgstr "添加终端"

#. module: pos_iot_six
#: model_terms:ir.ui.view,arch_db:pos_iot_six.view_add_six_terminal
msgid "Cancel"
msgstr "取消"

#. module: pos_iot_six
#: model:ir.model,name:pos_iot_six.model_pos_iot_six_add_six_terminal
msgid "Connect a Six Payment Terminal"
msgstr "连接  Six 支付终端"

#. module: pos_iot_six
#: model:ir.model.fields,field_description:pos_iot_six.field_pos_iot_six_add_six_terminal__create_uid
msgid "Created by"
msgstr "创建人"

#. module: pos_iot_six
#: model:ir.model.fields,field_description:pos_iot_six.field_pos_iot_six_add_six_terminal__create_date
msgid "Created on"
msgstr "创建日期"

#. module: pos_iot_six
#: model:ir.model.fields,field_description:pos_iot_six.field_pos_iot_six_add_six_terminal__display_name
msgid "Display Name"
msgstr "显示名称"

#. module: pos_iot_six
#. odoo-javascript
#: code:addons/pos_iot_six/static/src/js/six_terminal_id_field.xml:0
msgid "Failed to save Terminal ID to IoT Box"
msgstr "无法将终端 ID 保存到 IoT 盒子中"

#. module: pos_iot_six
#: model:ir.model.fields,field_description:pos_iot_six.field_pos_iot_six_add_six_terminal__id
msgid "ID"
msgstr "ID"

#. module: pos_iot_six
#: model:ir.model,name:pos_iot_six.model_iot_box
#: model:ir.model.fields,field_description:pos_iot_six.field_pos_iot_six_add_six_terminal__iot_box_id
msgid "IoT Box"
msgstr "物联网盒子"

#. module: pos_iot_six
#: model:ir.model.fields,field_description:pos_iot_six.field_pos_iot_six_add_six_terminal__iot_box_url
msgid "IoT Box Home Page"
msgstr "IOT 盒子首页"

#. module: pos_iot_six
#: model:ir.model.fields,field_description:pos_iot_six.field_pos_iot_six_add_six_terminal__write_uid
msgid "Last Updated by"
msgstr "最后更新人"

#. module: pos_iot_six
#: model:ir.model.fields,field_description:pos_iot_six.field_pos_iot_six_add_six_terminal__write_date
msgid "Last Updated on"
msgstr "上次更新日期"

#. module: pos_iot_six
#. odoo-javascript
#: code:addons/pos_iot_six/static/src/js/six_terminal_id_field.xml:0
msgid "Must only contain digits"
msgstr "必须只包含数字"

#. module: pos_iot_six
#: model:ir.model,name:pos_iot_six.model_pos_payment_method
msgid "Point of Sale Payment Methods"
msgstr "POS支付方式"

#. module: pos_iot_six
#. odoo-javascript
#: code:addons/pos_iot_six/static/src/js/six_terminal_id_field.xml:0
msgid "Saved Terminal ID to IoT Box"
msgstr "已保存终端 ID 到 IoT 盒子"

#. module: pos_iot_six
#: model_terms:ir.ui.view,arch_db:pos_iot_six.pos_payment_method_view_form_six_iot
msgid "Setup Six Terminal"
msgstr "设置 Six 终端"

#. module: pos_iot_six
#: model:ir.actions.act_window,name:pos_iot_six.action_add_six_terminal
#: model_terms:ir.ui.view,arch_db:pos_iot_six.view_add_six_terminal
msgid "Setup a Six Payment Terminal"
msgstr "设置 Six 支付终端"

#. module: pos_iot_six
#: model:ir.model.fields,field_description:pos_iot_six.field_iot_box__six_terminal_id
#: model:ir.model.fields,field_description:pos_iot_six.field_pos_iot_six_add_six_terminal__six_terminal_id
msgid "Six Terminal ID (TID)"
msgstr "Six 终端机识别码（TID）"

#. module: pos_iot_six
#: model:ir.model.fields,field_description:pos_iot_six.field_pos_iot_six_add_six_terminal__terminal_device_id
msgid "Terminal Device"
msgstr "终端机设备"

#. module: pos_iot_six
#: model:ir.model.fields,help:pos_iot_six.field_iot_box__six_terminal_id
#: model:ir.model.fields,help:pos_iot_six.field_pos_iot_six_add_six_terminal__six_terminal_id
msgid ""
"The ID of your Six payment terminal. Please note that after entering this, "
"you will have to wait several seconds before the terminal will appear in "
"your device list."
msgstr "您的 Six 支付终端的 ID。请注意，输入此内容后，您需要等待几秒钟，终端才会出现在您的设备列表中。"

#. module: pos_iot_six
#: model:ir.model.fields,help:pos_iot_six.field_pos_iot_six_add_six_terminal__iot_box_id
msgid "The IoT Box that your Six terminal is connected to."
msgstr "你的 Six 终端机连接至的 IoT Box。"
