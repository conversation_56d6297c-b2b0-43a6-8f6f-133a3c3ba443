from odoo import http
from odoo.http import request
import json
import logging

_logger = logging.getLogger(__name__)


# -----------------------------------------------------
#   Home Care API
# -----------------------------------------------------


class HomeCareController(http.Controller):

    def create_or_update_partner(self, data):
        """Create or update a partner with the provided data."""
        Partner = request.env['res.partner'].sudo()
        partner_id = Partner.search([
            ('name', '=', data.get('name')),
            ('phone', '=', data.get('mobile')),
            ('customer_reference', '=', data.get('reference')),
        ], limit=1)

        partner_data = {
            'name': data.get('name'),
            'email': data.get('email'),
            'street': data.get('street'),
            'city': data.get('city'),
            'customer_reference': data.get('reference'),
            'gender': data.get('gender'),
            'phone': data.get('mobile'),
        }
        if partner_id:
            partner_id.write(partner_data)
        else:
            partner_id = Partner.create(partner_data)
        return partner_id

    @http.route(['/home/<USER>/create/customer'], type='json', auth='public', methods=['POST'], csrf=False)
    def verify_customer_data(self, **kw):
        """ API firstly verify customer data and then create customer """
        data = request.get_json_data()
        required_fields = ['api_key', 'reference', 'name', 'mobile']
        # Check for missing fields
        for field in required_fields:
            if not data.get(field):
                return json.dumps({'status': 400, 'message': f"{field} is required."})
            if field == 'mobile' and len(data.get(field)) != 8:
                return {'status': 400, 'message': "Mobile number must be exactly 8 digits."}

        # Create the customer
        try:
            partner = self.create_or_update_partner(data)
            if partner:
                return {
                    "status": True,
                    "code": 200,
                    "message": "Success",
                    "data": {
                        "message": "Customer created or updated successfully.",
                        "partner_id": partner.id
                    },
                    "errors": []
                }
            else:
                return json.dumps({
                    "status": False,
                    "code": 500,
                    "message": "Failed to create customer",
                    "errors": ["Internal server error while creating customer"]
                })
        except Exception as e:
            return json.dumps({
                "status": False,
                "code": 500,
                "message": "An unexpected error occurred",
                "errors": [str(e)]
            })

    # ------------------------------------------------------------------------------------------------
    #  Home Care Create Invoice API
    # -------------------------------------------------------------------------------------------------

    def check_customer_availability(self, data):
        partner_id = request.env['res.partner'].sudo().search([
            ('name', '=', data.get('customer_name')),
            ('phone', '=', data.get('customer_mobile')),
            ('customer_reference', '=', data.get('customer_reference'))
        ], limit=1)
        return partner_id if partner_id else False

    def create_invoice(self, data, invoice_date):
        created_move_ids = []
        for rec in data.get('product_list'):
            account_move_id = request.env['account.move'].sudo().create({
                'partner_id': data.get('partner_id').id,
                'invoice_date': invoice_date,
                'move_type': 'out_invoice',
                'invoice_user_id': data.get('user_id').id,
                'invoice_line_ids': [(0, 0, {
                    'product_id': rec.get('product_id'),
                    'quantity': rec.get('product_qty'),
                    'tax_ids': [(5, 0, 0)]
                })],
            })
            if account_move_id:
                account_move_id.action_post()
                created_move_ids.append(account_move_id.id)
        return created_move_ids if created_move_ids else False

    @http.route(['/home/<USER>/create/invoice'], type='json', auth='public', methods=['POST'], csrf=False)
    def create_customer_invoice(self, **kw):
        """ API to create customer invoice """
        company_id = self.env.company_id
        prepare_data = {}
        data = request.get_json_data()
        required_fields = [
            'api_key', 'reference', 'invoice_date', 'customer', 'employee_name',
            'salesperson', 'total_payment', 'products', 'customer_name', 'customer_mobile',
            'customer_reference'
        ]
        # Check for missing fields
        for field in required_fields:
            if not data.get(field) and field not in ('customer_name', 'customer_mobile', 'customer_reference'):
                return json.dumps({'status': 400, 'message': f"{field} is required."})
            if field == 'employee_name':
                employee_id = request.env['hr.employee'].sudo().search([
                    ('name', '=', data.get(field)),
                    ('company_id','=',company_id.id)
                ], limit=1)
                if not employee_id:
                    return json.dumps({
                        'status': 400,
                        'message': f"Employee ({data.get(field)}) is not available in the system."
                    })
                else:
                    prepare_data.update({'employee_id': employee_id})
            if field == 'salesperson':
                salesperson = request.env['res.users'].sudo().search([
                    ('name', '=', data.get(field)),
                    ('company_id','=',company_id.id)
                ], limit=1)
                if not salesperson:
                    return json.dumps({
                        'status': 400,
                        'message': f"Salesperson ({data.get(field)}) is not available in the system."
                    })
                else:
                    prepare_data.update({'user_id': salesperson})
            if field == 'customer_name' and not data['customer'][field]:
                return json.dumps({'status': 400, 'message': f"{field} is required."})
            if field == 'customer_mobile' and not data['customer'][field]:
                return json.dumps({'status': 400, 'message': f"{field} is required."})
            if field == 'customer_reference' and not data['customer'][field]:
                return json.dumps({'status': 400, 'message': f"{field} is required."})

        # Check customer is available or not in the system
        partner_id = self.check_customer_availability(data.get('customer'))
        if not partner_id:
            return json.dumps({
                'status': 400,
                'message': f"({data['customer']['customer_name']}) Customer is not available in the system."
            })
        else:
            prepare_data.update({'partner_id': partner_id})

        product_lst = []
        # Check for missing product fields
        for rec in data.get('products'):
            if not rec.get('product_sku'):
                return json.dumps({'status': 400, 'message': f"'product_sku' is required."})
            if not rec.get('product_qty'):
                return json.dumps({'status': 400, 'message': f"'product_qty' is required."})
            if not rec.get('description'):
                return json.dumps({'status': 400, 'message': f"'description' is required."})
            if not rec.get('invoice_line_reference'):
                return json.dumps({'status': 400, 'message': f"'invoice_line_reference'  is required."})
            if rec.get('product_sku'):
                product_id = request.env['product.product'].sudo().search([('name', '=', rec.get('product_sku'))])
                if not product_id:
                    return json.dumps({
                        'status': 400,
                        'message': f"Product ({rec.get('product_sku')})  is not available in the system."
                    })
                else:
                    if product_id.lst_price == rec.get('price_unit'):
                        product_lst.append({'product_id': product_id.id, 'product_qty': rec.get('product_qty')})
                    else:
                        return json.dumps({
                            'status': 400,
                            'message': f"{rec.get('product_sku')} price not match (sale price)."
                        })
            # product_lst.append(rec)
        prepare_data.update({'product_list': product_lst})
        account_move_ids = self.create_invoice(prepare_data, data.get('invoice_date'))

        if account_move_ids:
            return json.dumps({
                "status": True,
                "code": 200,
                "message": "success",
                "data": {"odoo reference": account_move_ids},
                "errors": []
            })
        else:
            return json.dumps({
                "status": False,
                "code": 500,
                "message": "Failed to create customer invoice.",
                "errors": ["Internal server error while creating customer invoice"]
            })
