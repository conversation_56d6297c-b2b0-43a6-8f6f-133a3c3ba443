import { mailModels } from "@mail/../tests/mail_test_helpers";
import { defineModels, mockService } from "@web/../tests/web_test_helpers";

import { MailActivity } from "./mock_server/mock_models/mail_activity";
import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "./mock_server/mock_models/res_partner";
import { ResUsers } from "./mock_server/mock_models/res_users";
import { VoipCall } from "./mock_server/mock_models/voip_call";
import { VoipProvider } from "./mock_server/mock_models/voip_provider";

export function setupVoipTests() {
    const ringtones = {
        dial: {},
        incoming: {},
        ringback: {},
    };
    Object.values(ringtones).forEach((r) => Object.assign(r, { play: () => {} }));
    mockService("voip.ringtone", {
        ...ringtones,
        stopPlaying() {},
    });
    defineModels(voipModels);
}

export const voipModels = {
    ...mailModels,
    MailActivity,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON>oip<PERSON><PERSON><PERSON>,
};
