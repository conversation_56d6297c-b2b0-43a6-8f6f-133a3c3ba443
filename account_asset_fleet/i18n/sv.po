# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_asset_fleet
# 
# Translators:
# Frida E, 2024
# <PERSON> <<EMAIL>>, 2024
# Lasse L, 2024
# <PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:26+0000\n"
"PO-Revision-Date: 2024-09-25 09:43+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Swedish (https://app.transifex.com/odoo/teams/41243/sv/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sv\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: account_asset_fleet
#. odoo-python
#: code:addons/account_asset_fleet/models/account_asset.py:0
msgid "All the lines should be from the same vehicle"
msgstr "Alla rader ska vara från samma fordon"

#. module: account_asset_fleet
#: model:ir.model,name:account_asset_fleet.model_account_asset
msgid "Asset/Revenue Recognition"
msgstr "Tillgång/Intäktsredovisning"

#. module: account_asset_fleet
#: model:ir.model,name:account_asset_fleet.model_account_move
msgid "Journal Entry"
msgstr "Verifikat"

#. module: account_asset_fleet
#: model:ir.model,name:account_asset_fleet.model_fleet_vehicle_log_services
msgid "Services for vehicles"
msgstr "Service av fordon"

#. module: account_asset_fleet
#: model:ir.model.fields,field_description:account_asset_fleet.field_account_asset__vehicle_id
#: model_terms:ir.ui.view,arch_db:account_asset_fleet.view_account_asset_fleet_form
msgid "Vehicle"
msgstr "Fordon"
