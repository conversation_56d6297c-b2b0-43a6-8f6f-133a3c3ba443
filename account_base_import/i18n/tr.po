# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_base_import
# 
# Translators:
# <PERSON><PERSON>, 2024
# <PERSON>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON><PERSON> Gözütok, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-03-26 20:46+0000\n"
"PO-Revision-Date: 2024-09-25 09:43+0000\n"
"Last-Translator: <PERSON><PERSON>, 2025\n"
"Language-Team: Turkish (https://app.transifex.com/odoo/teams/41243/tr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: tr\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: account_base_import
#: model_terms:ir.ui.view,arch_db:account_base_import.res_config_settings_import_view_form
msgid "<span class=\"text-muted\">(end of year balances)</span>"
msgstr ""

#. module: account_base_import
#: model_terms:ir.ui.view,arch_db:account_base_import.res_config_settings_import_view_form
msgid "<span class=\"text-muted\">(for full history)</span>"
msgstr ""

#. module: account_base_import
#: model:ir.model,name:account_base_import.model_account_account
msgid "Account"
msgstr "Hesap"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
msgid "Account Winbooks Import module"
msgstr ""

#. module: account_base_import
#: model:ir.model,name:account_base_import.model_account_import_summary
msgid "Account import summary view"
msgstr ""

#. module: account_base_import
#: model_terms:ir.ui.view,arch_db:account_base_import.res_config_settings_import_view_form
msgid "Accounting Import"
msgstr "Muhasebe Alma"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/js/account_import_guide.js:0
#: model:ir.actions.client,name:account_base_import.action_open_import_guide
msgid "Accounting Import Guide"
msgstr ""

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
msgid "Accounting Import Options"
msgstr ""

#. module: account_base_import
#: model:ir.model,name:account_base_import.model_base_import_import
msgid "Base Import"
msgstr "İçe Aktarım"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/js/account_import_action.js:0
#: model:ir.actions.act_window,name:account_base_import.action_open_coa_setup
msgid "Chart of Accounts"
msgstr "Hesap Planı"

#. module: account_base_import
#: model_terms:ir.ui.view,arch_db:account_base_import.res_config_settings_import_view_form
msgid "Choose how you want to setup your CoA"
msgstr ""

#. module: account_base_import
#: model:ir.model.fields,field_description:account_base_import.field_account_import_summary__create_uid
msgid "Created by"
msgstr "Tarafından oluşturuldu"

#. module: account_base_import
#: model:ir.model.fields,field_description:account_base_import.field_account_import_summary__create_date
msgid "Created on"
msgstr "Oluşturuldu"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/js/account_import_action.js:0
msgid "Customers"
msgstr "Müşteriler"

#. module: account_base_import
#: model:ir.model.fields,field_description:account_base_import.field_account_import_summary__display_name
msgid "Display Name"
msgstr "İsim Göster"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
msgid "Download"
msgstr "İndir"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
msgid "Excel Import"
msgstr ""

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
msgid "FEC"
msgstr ""

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
msgid "FEC Import module"
msgstr ""

#. module: account_base_import
#: model:ir.model.fields,field_description:account_base_import.field_account_import_summary__id
msgid "ID"
msgstr "ID"

#. module: account_base_import
#: model_terms:ir.ui.view,arch_db:account_base_import.res_config_settings_import_view_form
#: model_terms:ir.ui.view,arch_db:account_base_import.view_account_base_import_list
#: model_terms:ir.ui.view,arch_db:account_base_import.view_account_setup_base_import_list
msgid "Import"
msgstr "İçe Aktar"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
#: model:ir.actions.client,name:account_base_import.action_account_import
msgid "Import Chart of Accounts"
msgstr "Hesap Planını İçe Aktar"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
msgid "Import CoA"
msgstr "Hesap Planını İçe Aktar"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
msgid "Import Contacts"
msgstr "Kontakları İçe Aktar"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
#: model:ir.actions.client,name:account_base_import.action_account_move_line_import
msgid "Import Journal Items"
msgstr "Yevmiye Kalemlerini İçe Aktar"

#. module: account_base_import
#: model:ir.actions.client,name:account_base_import.action_partner_import
msgid "Import Partners"
msgstr "İş Ortaklarını İçe Aktar"

#. module: account_base_import
#. odoo-python
#: code:addons/account_base_import/wizard/account_import_summary.py:0
msgid "Import Summary"
msgstr ""

#. module: account_base_import
#: model:ir.model.fields,field_description:account_base_import.field_account_import_summary__import_summary_account_ids
msgid "Import Summary Account"
msgstr ""

#. module: account_base_import
#: model:ir.model.fields,field_description:account_base_import.field_account_import_summary__import_summary_journal_ids
msgid "Import Summary Journal"
msgstr ""

#. module: account_base_import
#: model:ir.model.fields,field_description:account_base_import.field_account_import_summary__import_summary_move_ids
msgid "Import Summary Move"
msgstr ""

#. module: account_base_import
#: model:ir.model.fields,field_description:account_base_import.field_account_import_summary__import_summary_name
msgid "Import Summary Name"
msgstr ""

#. module: account_base_import
#: model:ir.model.fields,field_description:account_base_import.field_account_import_summary__import_summary_partner_ids
msgid "Import Summary Partner"
msgstr ""

#. module: account_base_import
#: model:ir.model.fields,field_description:account_base_import.field_account_import_summary__import_summary_tax_ids
msgid "Import Summary Tax"
msgstr ""

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
msgid "Import contacts"
msgstr "Kontaktları İçe Aktar"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
msgid "Import customers or suppliers (partners) and their contacts using a"
msgstr ""

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
msgid "Import the Chart of Accounts and initial balances using a"
msgstr ""

#. module: account_base_import
#: model_terms:ir.ui.view,arch_db:account_base_import.account_import_summary_form
msgid "Imported Data"
msgstr ""

#. module: account_base_import
#: model_terms:ir.ui.view,arch_db:account_base_import.res_config_settings_import_view_form
msgid "Initial Setup"
msgstr ""

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/js/account_import_guide.js:0
msgid "Install a module"
msgstr ""

#. module: account_base_import
#: model:ir.model,name:account_base_import.model_account_move_line
msgid "Journal Item"
msgstr "Yevmiye Kalemi"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/js/account_import_action.js:0
msgid "Journal Items"
msgstr "Yevmiye Kalemleri"

#. module: account_base_import
#: model:ir.model.fields,field_description:account_base_import.field_account_import_summary__write_uid
msgid "Last Updated by"
msgstr "Son Güncelleyen"

#. module: account_base_import
#: model:ir.model.fields,field_description:account_base_import.field_account_import_summary__write_date
msgid "Last Updated on"
msgstr "Son Güncelleme"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
msgid ""
"Most accounting software in Europe support exporting SAF-T file for audit purposes.\n"
"                            Use the"
msgstr ""

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
msgid ""
"Most accounting software in France support exporting FEC file for audit purposes.\n"
"                            Use the"
msgstr ""

#. module: account_base_import
#: model_terms:ir.ui.view,arch_db:account_base_import.account_import_summary_form
msgid "No data was imported."
msgstr ""

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
msgid "Optional, but useful to import open receivables & payables using a"
msgstr ""

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
#: model_terms:ir.ui.view,arch_db:account_base_import.res_config_settings_import_view_form
msgid "Review Manually"
msgstr ""

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
msgid "SAF-T"
msgstr ""

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
msgid "SAF-T Import module"
msgstr ""

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
msgid "SIE 4 Import module"
msgstr ""

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
msgid "SIE 4/5"
msgstr ""

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
msgid "SIE 5 Import module"
msgstr ""

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
msgid ""
"The SIE standard file format is very common in Sweden for several purposes such as auditing, importing and exporting data\n"
"                            from and to other accounting applications. Odoo support importing data from both type 4 and 5 of SIE."
msgstr ""

#. module: account_base_import
#. odoo-python
#: code:addons/account_base_import/models/account_move_line.py:0
msgid "The import file is missing the following required columns: %s"
msgstr ""

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
msgid ""
"Tip: we recommend importing your initial balances using the Chart of Account"
" import. Only use the Journal Items import for unreconciled entries in your "
"Payable and Receivable Accounts."
msgstr ""

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
msgid "Use predefined format to import your data faster."
msgstr ""

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
msgid "Use templates to import CSV or Excel for your accounting setup."
msgstr ""

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
msgid ""
"We will setup your charts of accounts and the history of journal entries, "
"that will stay in draft."
msgstr ""

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
msgid "Winbooks"
msgstr ""

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
msgid ""
"Winbooks is an old school Belgian accounting software acquired by Exact.\n"
"                            Use the"
msgstr ""

#. module: account_base_import
#. odoo-python
#: code:addons/account_base_import/models/account_account.py:0
msgid ""
"You must provide both the `code_mapping_ids/company_id` and the "
"`code_mapping_ids/code` columns."
msgstr ""

#. module: account_base_import
#: model_terms:ir.ui.view,arch_db:account_base_import.account_import_summary_form
msgid "accounts imported"
msgstr ""

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
msgid ""
"is required to import the SIE 4 files.\n"
"                                    By design, it's also backward compatible with previous SIE versions (version 1 up to 3).\n"
"                                    This import will not validate the correctness of the file."
msgstr ""

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
msgid ""
"is required to import the SIE 5 and SIE 5 entry files.\n"
"                                    For general SIE 5, we will set up your charts of accounts balances, journals, partners, and the history of journal entries\n"
"                                    (journals data must be present in the file).\n"
"                                    For the SIE 5 entry, only entries and partners will be created, the rest must already be present in the system."
msgstr ""

#. module: account_base_import
#: model_terms:ir.ui.view,arch_db:account_base_import.account_import_summary_form
msgid "journals imported"
msgstr ""

#. module: account_base_import
#: model_terms:ir.ui.view,arch_db:account_base_import.account_import_summary_form
msgid "moves imported"
msgstr ""

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
msgid "or"
msgstr "veya"

#. module: account_base_import
#: model_terms:ir.ui.view,arch_db:account_base_import.account_import_summary_form
msgid "partners imported"
msgstr ""

#. module: account_base_import
#: model_terms:ir.ui.view,arch_db:account_base_import.account_import_summary_form
msgid "taxes imported"
msgstr ""

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
msgid "template."
msgstr ""

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
msgid ""
"to import a Winbooks full back-up (Maintenance > Backup) to get the chart of accounts, contacts, taxes, history of journal entries, and documents.\n"
"                            Support versions: Winbooks Desktop 5.50, 6, 7, 8."
msgstr ""

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
msgid ""
"to import the FEC file. We will setup your charts of accounts and the "
"history of journal entries."
msgstr ""

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
msgid "to import the SAF-T file."
msgstr ""
