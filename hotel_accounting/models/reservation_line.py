# -*- coding: utf-8 -*-
# Softprime Consulting Pvt Ltd
# Copyright (C) Softprime Consulting Pvt Ltd
# All Rights Reserved
# https://softprimeconsulting.com/
from odoo import models, fields, api, _
from datetime import timedelta


class ReservationLine(models.Model):
    _inherit = "room.reservation.line"

    tax_ids = fields.Many2many('account.tax', string="Taxes",
                               domain=[('type_tax_use', '=', 'sale')])
    tax_amount = fields.Float('Tax Amount', store=True,
                              compute='_compute_tax_amount')
    total_room_rate_with_tax = fields.Float('Total With Tax', store=True,
                                            compute='_compute_tax_amount')
    reason_tax_not_applicable = fields.Char('Reason tax not applicable?')
    tax_applicable = fields.Boolean('Tax Applicable?', default=True)

    @api.depends('tax_ids', 'total_room_rate',
                 'total_room_rate', 'room_reservation_id.duration')
    def _compute_tax_amount(self):
        for line in self:
            taxes = line.tax_ids.compute_all(line.total_room_rate,
                                             line.room_reservation_id.currency_id,
                                             1,
                                             partner=line.room_reservation_id.guest_id)
            line.tax_amount = taxes['total_included'] - taxes['total_excluded']
            line.total_room_rate_with_tax = line.total_room_rate + line.tax_amount

    @api.onchange('tax_applicable')
    def _onchange_tax_applicable(self):
        if not self.tax_applicable:
            self.tax_ids = False
        if self.tax_applicable:
            self.reason_tax_not_applicable = False

    def default_get(self, fields):
        res = super(ReservationLine, self).default_get(fields)
        tax_id = self.env['account.tax'].search([
            ('type_tax_use', '=', 'sale'),
            ('company_id', '=', self.env.company.id)], limit=1)
        if tax_id and len(tax_id) == 1:
            res['tax_ids'] = [(6, 0, [tax_id.id])]
        return res

    def action_room_folio_invoice_bill(self):
        """Perform check-in, create room bills for manual frequency, and invoices for auto frequency."""
        today = fields.Date.today()
        self.created_folio_id.next_execution_date = self.created_folio_id.actual_checkin + timedelta(days=1)
        if self.created_folio_id.invoice_frequency == 'manually':
            # Create room bill for manual frequency
            if not self.room_reservation_id._bill_exists_for_folio(self.created_folio_id, today):
                bill_vals = self.room_reservation_id._prepare_room_bill_data(
                    self.created_folio_id, today)
                bill = self.env['room.bill'].create(bill_vals)
                self.room_reservation_id.message_post(
                    body=_("Room bill created for folio %s on %s") % (self.created_folio_id.name, today),
                    subtype_xmlid="mail.mt_note"
                )
        else:
            # Generate invoice for auto frequency
            if not self.room_reservation_id._invoice_exists_for_folio(self.created_folio_id, today):
                invoice_vals = self.room_reservation_id._prepare_invoice_data(
                    self.created_folio_id, today)
                invoice = self.env['account.move'].create(invoice_vals)
                self.created_folio_id.write({'invoice_ids': [(4, invoice.id)]})
                if hasattr(invoice, 'action_post'):
                    invoice.action_post()
                self.room_reservation_id.message_post(
                    body=_("Invoice %s created for folio %s on %s") % (invoice.name, self.created_folio_id.name, today),
                    subtype_xmlid="mail.mt_note"
                )

    def action_room_check_reservation_line(self):
        res = super().action_room_check_reservation_line()
        if self.created_folio_id and self.created_folio_id.state == 'check_in':
            self.action_room_folio_invoice_bill()
        return res
