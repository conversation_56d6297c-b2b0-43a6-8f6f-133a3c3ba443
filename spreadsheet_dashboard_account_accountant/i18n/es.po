# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* spreadsheet_dashboard_account_accountant
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-25 09:15+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: Spanish (https://app.transifex.com/odoo/teams/41243/es/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "(Net sales - Total expenses) / Net sales"
msgstr "(Ventas netas - Gastos totales) / Ventas netas"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "(Net sales – COGS) / Net sales"
msgstr "(Ventas netas  – COGS) / Ventas netas"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "< 0.7: company might be stuck with non liquid assets"
msgstr "< 0,7: la empresa podría quedarse con activos no líquidos"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "< 0.8: income might be too low"
msgstr "<0,8: los ingresos pueden ser demasiado bajos"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "< 0: company might not be able to meet obligations"
msgstr "<0: puede que la empresa no tenga que complir con las obligaciones"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "< 1: weak financial performance"
msgstr "<1: debilidad de rendimiento financiero"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "< 2.5: mature company who accumulated a lot of money"
msgstr "<2,5: una empresa madura que han acumulado un montón de dinero"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "< 20%: hard to become profitable"
msgstr "<20 %: dificil de ser rentable"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "< 3%: not efficient at generating business"
msgstr "<3 %: no es eficiente al momento de generar negocios"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "< 45: company gets paid for sales quickly"
msgstr "< 45: la empresa recibe pagos de venta rápidamente"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "< 45: company liquidates debts to suppliers quickly"
msgstr "< 45: la empresa liquida deudas con proveedores rápidamente"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "< 5%: not efficient at operating business"
msgstr "<5% no es eficiente en las operaciones de negocios"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "> 0: company can meet financial obligations at any time"
msgstr ""
">0: la empresa puede cumplir las obligaciones financieras en cualquier "
"momento"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "> 1.5: strong financial performance"
msgstr ">1.5: fuerte rendimiento financiero"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "> 10%: very efficient"
msgstr ">10% muy eficiente"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "> 10%: very efficient at operating business"
msgstr ">10% muy eficiente en las operaciones de negocios"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "> 1: company in highly solvent position"
msgstr "> 1: empresa en una posición muy solvente"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "> 1: income allow to meet financial obligations"
msgstr ">1: los ingresos permite cumplir con las obligaciones financieras"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "> 50%: hugely profitable business"
msgstr "> 50 %: el negocio es muy rentable"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "> 5: company owns a lot of debt and not of a lot of own money"
msgstr ">5: la empresa posee muchas deudas y no mucho dinero"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "> 60: company might not get paid for sales quickly enough"
msgstr ""
"> 60: es posible que la empresa no reciba pagos con suficiente rapidez"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "> 70: company might be slow to pay suppliers"
msgstr "> 70: es posible que la empresa se tarde en pagar a los proveedores"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Account type"
msgstr "Tipo de cuenta"

#. module: spreadsheet_dashboard_account_accountant
#: model:spreadsheet.dashboard,name:spreadsheet_dashboard_account_accountant.dashboard_accounting
msgid "Accounting"
msgstr "Contabilidad"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
msgid "Accounts"
msgstr "Cuentas"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Aggregate"
msgstr "Agregar"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Average creditors days"
msgstr "Periodo medio de pago"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Average debtor days"
msgstr "Periodo medio de cobro"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
msgid "Average debtors days"
msgstr "Periodo promedio de cobro"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Average payable days"
msgstr "Periodo promedio de pago"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_sample_dashboard.json:0
msgid "Balance sheet"
msgstr "Balance de situación"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Bank and Cash"
msgstr "Banco y efectivo"

#. module: spreadsheet_dashboard_account_accountant
#: model:spreadsheet.dashboard,name:spreadsheet_dashboard_account_accountant.dashboard_benchmark
msgid "Benchmark"
msgstr "Punto de referencia"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Best if compared with competitors"
msgstr "Mejor si se compara con competidores"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "COGS"
msgstr "Coste de los bienes vendidos"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_sample_dashboard.json:0
msgid "Cash"
msgstr "Efectivo"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
msgid "Cash asset ratio"
msgstr "Ratio de liquidez"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Cash flow"
msgstr "Flujo de efectivo"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Cash flow / Current liabilities"
msgstr "Flujo de efectivo / Pasivos corrientes"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Cash flow ratio"
msgstr "Proporción de flujo de efectivo"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
msgid "Cash received"
msgstr "Efectivo recibido"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
msgid "Cash spent"
msgstr "Efectivo gastado"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
msgid "Cash surplus"
msgstr "Superávit de efectivo"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
msgid "Closing bank balance"
msgstr "Saldo bancario de cierre"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
msgid "Code"
msgstr "Código"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Cost of Revenue"
msgstr "Coste de ingresos"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
msgid "Cost of revenue"
msgstr "Coste de ingresos"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Credit Card"
msgstr "Tarjeta de crédito"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Current Assets"
msgstr "Activos corrientes"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Current Liabilities"
msgstr "Pasivos corrientes"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Current assets / Current liabilities"
msgstr "Activos corrientes / Pasivos corrientes"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
msgid "Current assets to liabilities"
msgstr "Activos corrientes a pasivos"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Current assets – Current liabilities"
msgstr "Activos corrientes – Pasivos corrientes"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_sample_dashboard.json:0
msgid "Current expense"
msgstr "Gastos corrientes"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_sample_dashboard.json:0
msgid "Current income"
msgstr "Ingresos corrientes"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Current ratio"
msgstr "Índice de razón corriente"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
msgid "Debt ratio"
msgstr "Proporción de deuda"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
msgid "Debt to Equity"
msgstr "Deuda-capital"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Debt-to-equity"
msgstr "Relación deuda / patrimonio"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Depreciation"
msgstr "Depreciación"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "EBIT / Net sales"
msgstr "Benificio antes de intereses e impuestos/ ventas netas"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Equity"
msgstr "Capital"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Expenses"
msgstr "Gastos"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
msgid "Financial balance"
msgstr "Saldo financiero"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
msgid "Financial independence"
msgstr "Independencia financiera"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Fixed Assets"
msgstr "Activos fijos"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Formula"
msgstr "Fórmula"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Gross profit"
msgstr "Ganancia bruta"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Gross profit margin"
msgstr "Margen de ganancias en bruto"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Income"
msgstr "Ingreso"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_sample_dashboard.json:0
msgid "Invoiced"
msgstr "Facturado"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "KPI"
msgstr "KPI"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_sample_dashboard.json:0
msgid "KPIS"
msgstr "Indicadores clave de rendimiento"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_sample_dashboard.json:0
msgid "Liquidity"
msgstr "Liquidez"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
msgid "Long term working capital"
msgstr "Capital de trabajo a largo plazo"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Month"
msgstr "Mes"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Month-Year"
msgstr "Mes-Año"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Net Credit Purchases / Average accounts payable balance for period"
msgstr ""
"Compras netas a crédito / Saldo medio de cuentas por pagar para el periodo"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
msgid "Net assets"
msgstr "Activos netos"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Net income / Revenue"
msgstr "Ingresos netos/renta"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Net profit"
msgstr "Ganancias netas"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Net profit margin"
msgstr "Margen de ganancias en neto"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Net sales"
msgstr "Ventas netas"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Non-current Assets"
msgstr "Activos no corrientes"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Non-current Liabilities"
msgstr "Pasivos no corrientes"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Off-Balance Sheet"
msgstr "Fuera del balance de situación"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Offset -1 - Month"
msgstr "Compensación -1- mes"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Offset -1 - Month-Year"
msgstr "Compensación -1- mes-año"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Offset -1 - Start date"
msgstr "Compensación -1 - Fecha de inicio"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Offset -1 - Year"
msgstr "Compensación -1- año"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Offset -4 - Month"
msgstr "Compensación -4- mes"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Offset -4 - Month-Year"
msgstr "Compensación -4- mes-año"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Offset -4 - Start date"
msgstr "Compensación -4- Fecha de inicio"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Offset -4 - Year"
msgstr "Compensación -4- año"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Operating margin"
msgstr "Margen operativo"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Other Income"
msgstr "Otros ingresos"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
msgid "Other variables"
msgstr "Otras variables"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Payable"
msgstr "Por pagar"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_sample_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Payables"
msgstr "Por pagar"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Payables / Expenses * 30"
msgstr "Por pagar / Gastos * 30"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_sample_dashboard.json:0
msgid "Performance"
msgstr "Rendimiento"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
msgid "Permanence"
msgstr "Permanencia"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_sample_dashboard.json:0
msgid "Position"
msgstr "Cargo"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Prepayments"
msgstr "Prepagos"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_sample_dashboard.json:0
msgid "Previous expense"
msgstr "Gastos previos"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_sample_dashboard.json:0
msgid "Previous income"
msgstr "Ingresos previos"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_sample_dashboard.json:0
msgid "Profitability"
msgstr "Rentabilidad"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Quick assets"
msgstr "Activos rápidos"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Quick assets / Current liabilities"
msgstr "Activos rápidos / pasivos corrientes"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Quick ratio"
msgstr "Prueba ácida"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Receivable"
msgstr "Por cobrar"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_sample_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Receivables"
msgstr "Por cobrar"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Receivables / Income * 30"
msgstr "Por cobrar / Ingresos * 30"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
msgid "Return on equity"
msgstr "Retorno sobre el capital"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
msgid "Return on investments"
msgstr "Retorno sobre la inversión"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Sales on account / Average accounts receivable balance for period"
msgstr ""
"Ventas a cuenta / Saldo medio de las cuentas por cobrar para el periodo"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
msgid "Short term cash forecast"
msgstr "Pronóstico de tesorería a corto plazo"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_sample_dashboard.json:0
msgid "Solvency"
msgstr "Solvencia"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Start date"
msgstr "Fecha de inicio"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Technical name"
msgstr "Nombre técnico"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Total Current assets"
msgstr "Total de activos corrientes"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Total Current liabilities"
msgstr "Total de pasivos corrientes"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Total assets"
msgstr "Activos totales"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Total expense"
msgstr "Gastos totales"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Total income"
msgstr "Ingresos totales"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Total liabilities"
msgstr "Total pasivo"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Total liabilities / Total shareholders’ equity"
msgstr "Total pasivo / Recursos propios"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Total shareholder's equity"
msgstr "Total de recursos propios"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Value"
msgstr "Valor"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Very dependent on the sector"
msgstr "Muy dependientes en el sector"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Working capital"
msgstr "Capital de trabajo"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "Year"
msgstr "Año"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "bank and cash"
msgstr "banco y efectivo"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "cost of revenue"
msgstr "Coste de ingresos"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "expenses + depreciation + cost of revenue"
msgstr "Gastos + depreciación + coste de ingresos"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "income"
msgstr "ingresos"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "income + other income"
msgstr "Ingresos + otros ingresos"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_sample_dashboard.json:0
msgid "last period"
msgstr "último periodo"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "net sales - COGS"
msgstr "Ventas netas - Coste de los bienes vendidos"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid ""
"number of times you can pay off current debts with cash generated per year"
msgstr ""
"número de veces que puede pagar las deudas actuales con el efectivo generado"
" cada año"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "payable"
msgstr "por pagar"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "payable + credit card + current liabilities"
msgstr "por pagar + tarjeta de crédito + pasivos corrientes"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "payable + credit card + current liabilities + non-current liabilities"
msgstr ""
"por pagar + tarjeta de crédito + pasivos corrientes + pasivos no corrientes"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "possible issue in COGS (Cost of Goods sold)"
msgstr "posible problema en COGS (Coste de los bienes vendidos)"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "possible issue in direct and indirect costs"
msgstr "posible problema en costes directos e indirectos"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid ""
"possible issue in payment terms agreement with clients to get paid faster"
msgstr ""
"posible problema en el acuerdo de las condiciones de pago con los clientes "
"para recibir el pago más rápido"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid ""
"possible issue in providers payments, could lead to loss of suppliers trust"
msgstr ""
"posible problema con los pagos de proveedores, podría perder la confianza de"
" los proveedores"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "possible issue in ressources allocation or missed growth opportunities"
msgstr ""
"posible problema en la asignación de recursos u oportunidades de crecimiento"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "possible issue in the business model"
msgstr "posible problema en los modelos de negocios"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "possible issue with asset distribution and cash availability"
msgstr ""
"posible problema con la distribución de activos y disponibilidad de efectivo"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "possible issues in cash availability at short term"
msgstr "posibles problemas en la disponibilidad de efectivo a corto plazo"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "receivable"
msgstr "por cobrar"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid ""
"receivable + bank and cash + current assets + non-current assets + "
"prepayments + fixed assets"
msgstr ""
"cuentas por pagar + banco y caja + activos corrientes + activos no "
"corrientes + prepagos + activos fijos"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "receivable + bank and cash + current assets + prepayments"
msgstr "por cobrar + banco y caja + activos corrientes + prepagos"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "receivable + bank and cash + prepayments"
msgstr "por cobrar + banco y caja + prepagos"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_sample_dashboard.json:0
msgid "to pay"
msgstr "a pagar"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/accounting_sample_dashboard.json:0
msgid "to receive"
msgstr "a recibir"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "total assets - total liabilities"
msgstr "total activo - total pasivo"

#. module: spreadsheet_dashboard_account_accountant
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_dashboard.json:0
#: code:addons/spreadsheet_dashboard_account_accountant/data/files/benchmark_sample_dashboard.json:0
msgid "total income - total expense"
msgstr "ingresos totales - gastos totales"
