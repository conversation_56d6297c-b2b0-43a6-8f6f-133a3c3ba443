<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="view_hr_payslip_form_view_inherit" model="ir.ui.view">
        <field name="name">hr.payslip.form.view.inherit"</field>
        <field name="model">hr.payslip</field>
        <field name="inherit_id" ref="hr_payroll.view_hr_payslip_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='company_id']" position="after">
                <field name="vehicle_id" groups="hr_payroll.group_hr_payroll_user" invisible="country_code != 'BE'" readonly="state in ['cancel', 'done']"/>
            </xpath>
        </field>
    </record>
    <record id="view_hr_payslip_search_view_inherit" model="ir.ui.view">
        <field name="name">hr.payslip.search.view.inherit</field>
        <field name="model">hr.payslip</field>
        <field name="inherit_id" ref="hr_payroll.view_hr_payslip_filter"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='payslip_run_id']" position="after">
                <field name="vehicle_id"/>
            </xpath>
        </field>
    </record>
</odoo>
