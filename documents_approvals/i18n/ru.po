# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* documents_approvals
# 
# Translators:
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 09:15+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Russian (https://app.transifex.com/odoo/teams/41243/ru/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ru\n"
"Plural-Forms: nplurals=4; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<12 || n%100>14) ? 1 : n%10==0 || (n%10>=5 && n%10<=9) || (n%100>=11 && n%100<=14)? 2 : 3);\n"

#. module: documents_approvals
#: model:ir.model,name:documents_approvals.model_approval_request
msgid "Approval Request"
msgstr "Запрос на утверждение"

#. module: documents_approvals
#: model:ir.model.fields,field_description:documents_approvals.field_res_config_settings__documents_approvals_settings
msgid "Approvals"
msgstr "Одобрения"

#. module: documents_approvals
#: model:ir.model.fields,field_description:documents_approvals.field_res_company__approvals_tag_ids
msgid "Approvals Tag"
msgstr "Тег утверждения"

#. module: documents_approvals
#: model:ir.model.fields,field_description:documents_approvals.field_res_config_settings__approvals_tag_ids
msgid "Approvals Tags"
msgstr "Теги утверждения"

#. module: documents_approvals
#: model:ir.model.fields,field_description:documents_approvals.field_res_company__approvals_folder_id
msgid "Approvals Workspace"
msgstr "Рабочая область утверждений"

#. module: documents_approvals
#: model:ir.model.fields,field_description:documents_approvals.field_res_config_settings__approvals_folder_id
msgid "Approvals default workspace"
msgstr "Рабочая область по умолчанию \"Утверждения"

#. module: documents_approvals
#: model_terms:ir.ui.view,arch_db:documents_approvals.res_config_settings_view_form
msgid "Centralize files attached to Approvals"
msgstr "Централизация файлов, прикрепленных к утверждениям"

#. module: documents_approvals
#: model:ir.model,name:documents_approvals.model_res_company
msgid "Companies"
msgstr "Компании"

#. module: documents_approvals
#: model:ir.model,name:documents_approvals.model_res_config_settings
msgid "Config Settings"
msgstr "Параметры конфигурации"

#. module: documents_approvals
#: model_terms:ir.ui.view,arch_db:documents_approvals.res_config_settings_view_form
msgid "Default Tags"
msgstr "Теги по умолчанию"

#. module: documents_approvals
#. odoo-python
#: code:addons/documents_approvals/models/approval_request.py:0
#: model_terms:ir.ui.view,arch_db:documents_approvals.approval_request_view_form
msgid "Documents"
msgstr "Документы"

#. module: documents_approvals
#: model:ir.model.fields,field_description:documents_approvals.field_approval_request__documents_enabled
#: model:ir.model.fields,field_description:documents_approvals.field_res_company__documents_approvals_settings
msgid "Documents Approvals Settings"
msgstr "Настройки утверждения документов"

#. module: documents_approvals
#: model:ir.model.fields,field_description:documents_approvals.field_approval_request__documents_count
msgid "Documents Count"
msgstr "Подсчет документов"

#. module: documents_approvals
#: model_terms:ir.ui.view,arch_db:documents_approvals.res_config_settings_view_form
msgid "Workspace"
msgstr "Рабочая область"
