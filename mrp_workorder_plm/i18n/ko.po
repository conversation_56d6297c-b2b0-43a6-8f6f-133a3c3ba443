# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* mrp_workorder_plm
# 
# Translators:
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:27+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Korean (https://app.transifex.com/odoo/teams/41243/ko/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ko\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: mrp_workorder_plm
#. odoo-python
#: code:addons/mrp_workorder_plm/wizard/propose_change.py:0
msgid ""
"BoM feedback for not found step: %(step)s (%(production)s - %(operation)s)"
msgstr "찾을 수 없는 단계에 대한 BoM 피드백: %(step)s (%(production)s - %(operation)s)"

#. module: mrp_workorder_plm
#: model:ir.model.fields,help:mrp_workorder_plm.field_mrp_eco_routing_change__test_type
msgid "Defines the type of the quality control point."
msgstr "품질 관리점의 유형을 정의합니다."

#. module: mrp_workorder_plm
#: model:ir.model.fields,field_description:mrp_workorder_plm.field_mrp_eco__routing_change_ids_on_operation
msgid "ECO Routing Changes - Operation"
msgstr "ECO 라우팅 변경 - 작업"

#. module: mrp_workorder_plm
#: model:ir.model.fields,field_description:mrp_workorder_plm.field_mrp_eco__routing_change_ids_on_quality_point
msgid "ECO Routing Changes - Quality Point"
msgstr "ECO 라우팅 변경 - 품질 점수"

#. module: mrp_workorder_plm
#: model:ir.model,name:mrp_workorder_plm.model_mrp_eco_routing_change
msgid "Eco Routing changes"
msgstr "ECO 절차 변경"

#. module: mrp_workorder_plm
#: model:ir.model,name:mrp_workorder_plm.model_mrp_eco
msgid "Engineering Change Order (ECO)"
msgstr "설계 변경 주문 (ECO)"

#. module: mrp_workorder_plm
#: model_terms:ir.ui.view,arch_db:mrp_workorder_plm.mrp_workorder_eco_view_form
msgid "Instruction Changes"
msgstr "지침 변경"

#. module: mrp_workorder_plm
#. odoo-python
#: code:addons/mrp_workorder_plm/models/mrp_workorder.py:0
#: code:addons/mrp_workorder_plm/wizard/propose_change.py:0
msgid "Instruction Suggestions (%(wo_name)s)"
msgstr "(%(wo_name)s)의 작업 지침"

#. module: mrp_workorder_plm
#: model:ir.model,name:mrp_workorder_plm.model_mrp_production
msgid "Manufacturing Order"
msgstr "제조 주문"

#. module: mrp_workorder_plm
#. odoo-python
#: code:addons/mrp_workorder_plm/models/mrp_workorder.py:0
msgid "New Step Suggestion: %s"
msgstr "새로운 단계 제안: %s"

#. module: mrp_workorder_plm
#: model_terms:ir.ui.view,arch_db:mrp_workorder_plm.mrp_workorder_eco_view_form
msgid "Open Quality Point"
msgstr "품질 검사점 열기"

#. module: mrp_workorder_plm
#: model:ir.model,name:mrp_workorder_plm.model_propose_change
msgid "Propose a change in the production"
msgstr "생산에 변경 사항 제안"

#. module: mrp_workorder_plm
#: model_terms:ir.ui.view,arch_db:mrp_workorder_plm.mrp_workorder_eco_view_form
msgid "Quality Changes made on the quality point."
msgstr "품질 검사점에 대한 변경 사항."

#. module: mrp_workorder_plm
#: model:ir.model,name:mrp_workorder_plm.model_quality_check
msgid "Quality Check"
msgstr "품질 검사"

#. module: mrp_workorder_plm
#: model:ir.model,name:mrp_workorder_plm.model_quality_point
msgid "Quality Control Point"
msgstr "품질 관리점"

#. module: mrp_workorder_plm
#: model:ir.model.fields,field_description:mrp_workorder_plm.field_mrp_eco_routing_change__quality_point_id
msgid "Quality Point"
msgstr "품질 검사점"

#. module: mrp_workorder_plm
#: model:ir.model.fields,field_description:mrp_workorder_plm.field_mrp_eco_routing_change__step
msgid "Step"
msgstr "단계"

#. module: mrp_workorder_plm
#: model:ir.model.fields,field_description:mrp_workorder_plm.field_mrp_eco_routing_change__test_type
msgid "Step Type"
msgstr "단계 유형"

#. module: mrp_workorder_plm
#: model:ir.model.fields,field_description:mrp_workorder_plm.field_mrp_eco_routing_change__title
msgid "Title"
msgstr "제목"
