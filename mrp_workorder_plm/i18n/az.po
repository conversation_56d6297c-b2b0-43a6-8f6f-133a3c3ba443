# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* mrp_workorder_plm
# 
# Translators:
# <AUTHOR> <EMAIL>, 2024
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:27+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Azerbaijani (https://app.transifex.com/odoo/teams/41243/az/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: az\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: mrp_workorder_plm
#. odoo-python
#: code:addons/mrp_workorder_plm/wizard/propose_change.py:0
msgid ""
"BoM feedback for not found step: %(step)s (%(production)s - %(operation)s)"
msgstr ""

#. module: mrp_workorder_plm
#: model:ir.model.fields,help:mrp_workorder_plm.field_mrp_eco_routing_change__test_type
msgid "Defines the type of the quality control point."
msgstr ""

#. module: mrp_workorder_plm
#: model:ir.model.fields,field_description:mrp_workorder_plm.field_mrp_eco__routing_change_ids_on_operation
msgid "ECO Routing Changes - Operation"
msgstr ""

#. module: mrp_workorder_plm
#: model:ir.model.fields,field_description:mrp_workorder_plm.field_mrp_eco__routing_change_ids_on_quality_point
msgid "ECO Routing Changes - Quality Point"
msgstr ""

#. module: mrp_workorder_plm
#: model:ir.model,name:mrp_workorder_plm.model_mrp_eco_routing_change
msgid "Eco Routing changes"
msgstr ""

#. module: mrp_workorder_plm
#: model:ir.model,name:mrp_workorder_plm.model_mrp_eco
msgid "Engineering Change Order (ECO)"
msgstr ""

#. module: mrp_workorder_plm
#: model_terms:ir.ui.view,arch_db:mrp_workorder_plm.mrp_workorder_eco_view_form
msgid "Instruction Changes"
msgstr ""

#. module: mrp_workorder_plm
#. odoo-python
#: code:addons/mrp_workorder_plm/models/mrp_workorder.py:0
#: code:addons/mrp_workorder_plm/wizard/propose_change.py:0
msgid "Instruction Suggestions (%(wo_name)s)"
msgstr ""

#. module: mrp_workorder_plm
#: model:ir.model,name:mrp_workorder_plm.model_mrp_production
msgid "Manufacturing Order"
msgstr "istehsal sifarişi"

#. module: mrp_workorder_plm
#. odoo-python
#: code:addons/mrp_workorder_plm/models/mrp_workorder.py:0
msgid "New Step Suggestion: %s"
msgstr ""

#. module: mrp_workorder_plm
#: model_terms:ir.ui.view,arch_db:mrp_workorder_plm.mrp_workorder_eco_view_form
msgid "Open Quality Point"
msgstr ""

#. module: mrp_workorder_plm
#: model:ir.model,name:mrp_workorder_plm.model_propose_change
msgid "Propose a change in the production"
msgstr ""

#. module: mrp_workorder_plm
#: model_terms:ir.ui.view,arch_db:mrp_workorder_plm.mrp_workorder_eco_view_form
msgid "Quality Changes made on the quality point."
msgstr ""

#. module: mrp_workorder_plm
#: model:ir.model,name:mrp_workorder_plm.model_quality_check
msgid "Quality Check"
msgstr ""

#. module: mrp_workorder_plm
#: model:ir.model,name:mrp_workorder_plm.model_quality_point
msgid "Quality Control Point"
msgstr ""

#. module: mrp_workorder_plm
#: model:ir.model.fields,field_description:mrp_workorder_plm.field_mrp_eco_routing_change__quality_point_id
msgid "Quality Point"
msgstr ""

#. module: mrp_workorder_plm
#: model:ir.model.fields,field_description:mrp_workorder_plm.field_mrp_eco_routing_change__step
msgid "Step"
msgstr "Addım"

#. module: mrp_workorder_plm
#: model:ir.model.fields,field_description:mrp_workorder_plm.field_mrp_eco_routing_change__test_type
msgid "Step Type"
msgstr ""

#. module: mrp_workorder_plm
#: model:ir.model.fields,field_description:mrp_workorder_plm.field_mrp_eco_routing_change__title
msgid "Title"
msgstr "Başlıq"
