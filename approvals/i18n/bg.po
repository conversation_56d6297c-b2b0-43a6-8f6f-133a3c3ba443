# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* approvals
# 
# Translators:
# <PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON> <igor.shelu<PERSON><PERSON>@gmail.com>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# KeyVillage, 2024
# <PERSON>, 2024
# Ивайл<PERSON> <<EMAIL>>, 2024
# <PERSON> <<EMAIL>>, 2024
# aleksandar i<PERSON>, 2024
# <AUTHOR> <EMAIL>, 2024
# <PERSON>, 2024
# <PERSON><PERSON>, 2024
# <PERSON><PERSON>, 2024
# <PERSON><PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:25+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2025\n"
"Language-Team: Bulgarian (https://app.transifex.com/odoo/teams/41243/bg/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: bg\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: approvals
#: model:ir.actions.report,print_report_name:approvals.action_report_approval_request
msgid "'Approval - %s' % object.name"
msgstr "'Одобрение - %s' % object.name"

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.report_aprroval_request_document
msgid "07/07/2023 10:00:00"
msgstr "07/07/2023 10:00:00"

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.report_aprroval_request_document
msgid "07/08/2023 10:00:00"
msgstr "07/08/2023 10:00:00"

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.report_aprroval_request_document
msgid "1200"
msgstr "1200"

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.approval_category_view_form
msgid "<span class=\"fa fa-warning\" title=\"Invalid minimum approvals\"/>"
msgstr "<span class=\"fa fa-warning\" title=\"Invalid minimum approvals\"/>"

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.report_aprroval_request_document
msgid "<span class=\"fw-bold\">Description:</span>"
msgstr "<span class=\"fw-bold\">Описание:</span>"

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.report_aprroval_request_document
msgid "<span class=\"fw-bold\">To</span>"
msgstr "<span class=\"fw-bold\">Към</span>"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__message_needaction
msgid "Action Needed"
msgstr "Необходимо действие"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_category__active
msgid "Active"
msgstr "Активно"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__activity_ids
msgid "Activities"
msgstr "Дейности"

#. module: approvals
#: model:ir.model,name:approvals.model_mail_activity
msgid "Activity"
msgstr "Дейност"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr " Декорация за изключение на дейност"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__activity_state
msgid "Activity State"
msgstr "Състояние на дейност"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__activity_type_icon
msgid "Activity Type Icon"
msgstr "Икона за вид дейност"

#. module: approvals
#: model:ir.model.fields,help:approvals.field_approval_category__has_product
#: model:ir.model.fields,help:approvals.field_approval_request__has_product
msgid "Additional products that should be specified on the request."
msgstr "Допълнителни продукти, които трябва да бъдат посочени в заявката."

#. module: approvals
#: model:res.groups,name:approvals.group_approval_manager
msgid "Administrator"
msgstr "Администратор"

#. module: approvals
#: model:ir.actions.act_window,name:approvals.approval_request_action_all
#: model:ir.ui.menu,name:approvals.approvals_approval_menu_all
msgid "All Approvals"
msgstr "Всички одобрения"

#. module: approvals
#: model:ir.model.fields,help:approvals.field_approval_category__approval_type
#: model:ir.model.fields,help:approvals.field_approval_request__approval_type
msgid ""
"Allows you to define which documents you would like to create once the "
"request has been approved"
msgstr ""
"Позволява ви да определите кои документи искате да създадете, след като "
"заявката бъде одобрена."

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__amount
#: model_terms:ir.ui.view,arch_db:approvals.approval_category_view_form
msgid "Amount"
msgstr "Количество"

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.report_aprroval_request_document
msgid "Amount:"
msgstr "Сума:"

#. module: approvals
#: model:ir.model.fields,help:approvals.field_approval_category__has_reference
#: model:ir.model.fields,help:approvals.field_approval_request__has_reference
msgid "An additional reference that should be specified on the request."
msgstr "Допълнителни елементи, които трябва да бъдат посочени в заявката."

#. module: approvals
#. odoo-python
#: code:addons/approvals/models/approval_category.py:0
msgid "An user may not be in the approver list multiple times."
msgstr ""
"Един потребител не може да бъде в списъка на одобряващите няколко пъти."

#. module: approvals
#: model:mail.activity.type,name:approvals.mail_activity_data_approval
msgid "Approval"
msgstr "Одобрения"

#. module: approvals
#: model:ir.model,name:approvals.model_approval_category
msgid "Approval Category"
msgstr "Категория на одобрение"

#. module: approvals
#: model:ir.actions.report,name:approvals.action_report_approval_request
#: model:ir.model,name:approvals.model_approval_request
#: model:ir.model.fields,field_description:approvals.field_approval_product_line__approval_request_id
msgid "Approval Request"
msgstr "Искане за одобрение"

#. module: approvals
#: model:mail.message.subtype,description:approvals.mt_approval_request_status
#: model:mail.message.subtype,name:approvals.mt_approval_request_status
msgid "Approval Status Change"
msgstr "Промяна в статуса на одобрение"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__name
msgid "Approval Subject"
msgstr "Тема"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_category__approval_type
#: model:ir.model.fields,field_description:approvals.field_approval_category_approver__category_id
#: model:ir.model.fields,field_description:approvals.field_approval_request__approval_type
#: model_terms:ir.ui.view,arch_db:approvals.approval_category_view_form
msgid "Approval Type"
msgstr "Вид одобрение"

#. module: approvals
#: model:ir.model,name:approvals.model_approval_category_approver
msgid "Approval Type Approver"
msgstr "Тип одобрение Одобряващ"

#. module: approvals
#: model:ir.ui.menu,name:approvals.approvals_menu_root
msgid "Approvals"
msgstr "Одобрения"

#. module: approvals
#: model:ir.actions.act_window,name:approvals.approval_category_action
#: model:ir.ui.menu,name:approvals.approvals_category_menu_config
msgid "Approvals Types"
msgstr "Вид одобрение"

#. module: approvals
#: model:ir.actions.act_window,name:approvals.approval_request_action_to_review
#: model:ir.ui.menu,name:approvals.approvals_approval_menu_to_review
msgid "Approvals to Review"
msgstr "Одобрения за преглед"

#. module: approvals
#: model:ir.actions.act_window,name:approvals.approval_request_action_to_review_category
msgid "Approvals to review"
msgstr "Одобрения за преглед"

#. module: approvals
#. odoo-javascript
#: code:addons/approvals/static/src/web/activity/approval.xml:0
#: model_terms:ir.ui.view,arch_db:approvals.approval_request_view_form
#: model_terms:ir.ui.view,arch_db:approvals.approval_request_view_kanban
msgid "Approve"
msgstr "Одобри"

#. module: approvals
#: model:ir.model.fields.selection,name:approvals.selection__approval_approver__status__approved
#: model:ir.model.fields.selection,name:approvals.selection__approval_request__request_status__approved
#: model:ir.model.fields.selection,name:approvals.selection__approval_request__user_status__approved
#: model_terms:ir.ui.view,arch_db:approvals.approval_search_view_search
#: model_terms:ir.ui.view,arch_db:approvals.report_aprroval_request_document
msgid "Approved"
msgstr "Одобрен"

#. module: approvals
#: model:ir.model,name:approvals.model_approval_approver
#: model_terms:ir.ui.view,arch_db:approvals.approval_request_view_form
msgid "Approver"
msgstr "Одобрявам"

#. module: approvals
#. odoo-python
#: code:addons/approvals/models/approval_category.py:0
msgid ""
"Approver Sequence can only be activated with at least 1 minimum approver."
msgstr ""
"Последователността от одобряващи може да се активира само при поне 1 "
"одобряващ."

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_category__user_ids
msgid "Approver Users"
msgstr "Одобряващи потребители"

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.approval_request_view_form
msgid "Approver(s)"
msgstr "Одобрение(я)"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_category__approver_ids
#: model:ir.model.fields,field_description:approvals.field_approval_request__approver_ids
#: model_terms:ir.ui.view,arch_db:approvals.approval_category_view_form
#: model_terms:ir.ui.view,arch_db:approvals.approval_request_view_tree
msgid "Approvers"
msgstr "Одобряващи"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_category__approver_sequence
#: model:ir.model.fields,field_description:approvals.field_approval_request__approver_sequence
msgid "Approvers Sequence?"
msgstr "Последователност на одобряващите?"

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.report_aprroval_request_document
msgid "Approvers:"
msgstr "Одобряващи:"

#. module: approvals
#. odoo-javascript
#: code:addons/approvals/static/src/views/kanban/approvals_category_kanban_controller.js:0
#: model_terms:ir.ui.view,arch_db:approvals.approval_category_view_kanban
msgid "Archive"
msgstr "Архив"

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.approval_category_view_form
#: model_terms:ir.ui.view,arch_db:approvals.approval_category_view_kanban
#: model_terms:ir.ui.view,arch_db:approvals.approval_category_view_search
msgid "Archived"
msgstr "Архивиран"

#. module: approvals
#. odoo-javascript
#: code:addons/approvals/static/src/views/kanban/approvals_category_kanban_controller.js:0
msgid "Are you sure that you want to archive this record?"
msgstr "Сигурни ли сте, че искате да архивирате този запис?"

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.approval_request_view_form
msgid "Attach Document"
msgstr "Прикачени файлове"

#. module: approvals
#: model:ir.model,name:approvals.model_ir_attachment
msgid "Attachment"
msgstr "Прикачен файл"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__message_attachment_count
msgid "Attachment Count"
msgstr "Брой прикачени файлове"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__attachment_ids
msgid "Attachments"
msgstr "Прикачени файлове"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_category__automated_sequence
#: model:ir.model.fields,field_description:approvals.field_approval_request__automated_sequence
msgid "Automated Sequence?"
msgstr "Автоматизирана последователност?"

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.approval_request_view_form
msgid "Back To Draft"
msgstr "Врънете в чернова"

#. module: approvals
#: model:approval.category,name:approvals.approval_category_data_borrow_items
msgid "Borrow Items"
msgstr "Вземете елемент"

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.report_aprroval_request_document
msgid "Brussels"
msgstr "Брюксел"

#. module: approvals
#: model:approval.category,name:approvals.approval_category_data_business_trip
msgid "Business Trip"
msgstr "Няма бизнес политики"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__change_request_owner
msgid "Can Change Request Owner"
msgstr "Може да промени собственика на заявката"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_approver__can_edit
msgid "Can Edit"
msgstr "Може да редактира"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_approver__can_edit_user_id
msgid "Can Edit User"
msgstr "Можете да редактирате потребител"

#. module: approvals
#: model:ir.model.fields.selection,name:approvals.selection__approval_approver__status__cancel
#: model_terms:ir.ui.view,arch_db:approvals.approval_request_view_form
msgid "Cancel"
msgstr "Отказ"

#. module: approvals
#: model:ir.model.fields.selection,name:approvals.selection__approval_request__request_status__cancel
#: model:ir.model.fields.selection,name:approvals.selection__approval_request__user_status__cancel
#: model_terms:ir.ui.view,arch_db:approvals.approval_search_view_search
msgid "Canceled"
msgstr "Отменен"

#. module: approvals
#: model:approval.category,name:approvals.approval_category_data_car_rental_application
msgid "Car Rental Application"
msgstr "Приложение за коли под наем"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_product_line__product_uom_category_id
#: model:ir.model.fields,field_description:approvals.field_approval_request__category_id
#: model_terms:ir.ui.view,arch_db:approvals.approval_search_view_search
msgid "Category"
msgstr "Категория"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_approver__category_approver
msgid "Category Approver"
msgstr "Одобряващ категория"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_category__sequence_code
msgid "Code"
msgstr "Код"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_approver__company_id
#: model:ir.model.fields,field_description:approvals.field_approval_category__company_id
#: model:ir.model.fields,field_description:approvals.field_approval_category_approver__company_id
#: model:ir.model.fields,field_description:approvals.field_approval_product_line__company_id
#: model:ir.model.fields,field_description:approvals.field_approval_request__company_id
msgid "Company"
msgstr "Фирма"

#. module: approvals
#: model:ir.ui.menu,name:approvals.approvals_menu_config
msgid "Configuration"
msgstr "Конфигурация"

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.approval_request_view_tree
msgid "Confirm Date"
msgstr "Потвърдете датата"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__partner_id
#: model_terms:ir.ui.view,arch_db:approvals.approval_category_view_form
msgid "Contact"
msgstr "Контакт"

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.report_aprroval_request_document
msgid "Contact:"
msgstr "Контакт:"

#. module: approvals
#: model:approval.category,name:approvals.approval_category_data_contract_approval
msgid "Contract Approval"
msgstr "Одобрение на договора"

#. module: approvals
#: model:ir.model.fields,help:approvals.field_approval_product_line__product_uom_category_id
msgid ""
"Conversion between Units of Measure can only occur if they belong to the "
"same category. The conversion will be made based on the ratios."
msgstr ""
"Преобразуването на мерните единици може да възникне, само ако те принадлежат"
" към една и съща категория. Преобразуването ще се извърши въз основа на "
"съотношенията."

#. module: approvals
#: model_terms:ir.actions.act_window,help:approvals.product_product_action
msgid "Create a new product variant"
msgstr "Създайте нов вариант на продукта"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_approver__create_uid
#: model:ir.model.fields,field_description:approvals.field_approval_category__create_uid
#: model:ir.model.fields,field_description:approvals.field_approval_category_approver__create_uid
#: model:ir.model.fields,field_description:approvals.field_approval_product_line__create_uid
#: model:ir.model.fields,field_description:approvals.field_approval_request__create_uid
msgid "Created by"
msgstr "Създаден от"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_approver__create_date
#: model:ir.model.fields,field_description:approvals.field_approval_category__create_date
#: model:ir.model.fields,field_description:approvals.field_approval_category_approver__create_date
#: model:ir.model.fields,field_description:approvals.field_approval_product_line__create_date
#: model:ir.model.fields,field_description:approvals.field_approval_request__create_date
msgid "Created on"
msgstr "Създадено на"

#. module: approvals
#: model:ir.actions.act_window,name:approvals.approval_category_action_new_request
#: model:ir.ui.menu,name:approvals.approvals_category_menu_new
msgid "Dashboard"
msgstr "Табло за управление"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__date
#: model_terms:ir.ui.view,arch_db:approvals.approval_category_view_form
#: model_terms:ir.ui.view,arch_db:approvals.approval_request_view_form
msgid "Date"
msgstr "Дата"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__date_confirmed
msgid "Date Confirmed"
msgstr "Датата е потвърдена"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__date_end
msgid "Date end"
msgstr "Крайна дата"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__date_start
msgid "Date start"
msgstr "Дата на започване"

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.report_aprroval_request_document
msgid "Date:"
msgstr "Дата:"

#. module: approvals
#: model_terms:ir.actions.act_window,help:approvals.product_template_action
msgid ""
"Define the components and finished products you wish to use in\n"
"            bill of materials and manufacturing orders."
msgstr ""
"Дефинирайте компонентите и завършените продукти, които искате да използвате в\n"
"            спецификацията на материалите производствени поръчки."

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.approval_request_view_kanban
msgid "Delete"
msgstr "Изтрий"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_category__description
#: model:ir.model.fields,field_description:approvals.field_approval_product_line__description
#: model:ir.model.fields,field_description:approvals.field_approval_request__reason
#: model_terms:ir.ui.view,arch_db:approvals.approval_request_view_form
msgid "Description"
msgstr "Описание"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_approver__display_name
#: model:ir.model.fields,field_description:approvals.field_approval_category__display_name
#: model:ir.model.fields,field_description:approvals.field_approval_category_approver__display_name
#: model:ir.model.fields,field_description:approvals.field_approval_product_line__display_name
#: model:ir.model.fields,field_description:approvals.field_approval_request__display_name
msgid "Display Name"
msgstr "Име за показване"

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.approval_category_view_form
msgid "Document"
msgstr "Документ"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_category__requirer_document
#: model:ir.model.fields,field_description:approvals.field_approval_request__requirer_document
#: model_terms:ir.ui.view,arch_db:approvals.approval_request_view_form
msgid "Documents"
msgstr "Документи"

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.report_aprroval_request_document
msgid "Documents:"
msgstr "Документи:"

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.approval_request_view_form
msgid "E.g: Expenses Paris business trip"
msgstr "Нп: Разходи за командировка в Париж"

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.approval_category_view_kanban
msgid "Edit"
msgstr "Редактирай"

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.approval_request_view_kanban
msgid "Edit Request"
msgstr "Редактиране на заявка"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_category__manager_approval
msgid "Employee's Manager"
msgstr "Мениджър на служителя"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_approver__existing_request_user_ids
msgid "Existing Request User"
msgstr "Съществуващ потребител на заявката"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_category_approver__existing_user_ids
msgid "Existing User"
msgstr "Съществуващ потребител"

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.approval_category_view_form
msgid "Fields"
msgstr "Полета"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__message_follower_ids
msgid "Followers"
msgstr "Последователи"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__message_partner_ids
msgid "Followers (Partners)"
msgstr "Последователи (партньори)"

#. module: approvals
#: model:ir.model.fields,help:approvals.field_approval_request__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr " Икона, примерно fa-tasks"

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.approval_request_view_form
msgid "From:"
msgstr "От:"

#. module: approvals
#: model:approval.category,name:approvals.approval_category_data_general_approval
msgid "General Approval"
msgstr "Общо одобрение"

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.approval_search_view_search
msgid "Group By"
msgstr "Групиране по"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__has_access_to_request
msgid "Has Access To Request"
msgstr "Има достъп до заявка"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_category__has_amount
#: model:ir.model.fields,field_description:approvals.field_approval_request__has_amount
msgid "Has Amount"
msgstr "Има сума"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_category__has_partner
#: model:ir.model.fields,field_description:approvals.field_approval_request__has_partner
msgid "Has Contact"
msgstr "Контакт"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_category__has_date
#: model:ir.model.fields,field_description:approvals.field_approval_request__has_date
msgid "Has Date"
msgstr "Дата"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_category__has_location
#: model:ir.model.fields,field_description:approvals.field_approval_request__has_location
msgid "Has Location"
msgstr "Добави местонахождение"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__has_message
msgid "Has Message"
msgstr "има съобщение"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_category__has_payment_method
#: model:ir.model.fields,field_description:approvals.field_approval_request__has_payment_method
msgid "Has Payment"
msgstr "Има плащане"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_category__has_period
#: model:ir.model.fields,field_description:approvals.field_approval_request__has_period
msgid "Has Period"
msgstr "Има период"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_category__has_product
#: model:ir.model.fields,field_description:approvals.field_approval_request__has_product
msgid "Has Product"
msgstr "Има продукт"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_category__has_quantity
#: model:ir.model.fields,field_description:approvals.field_approval_request__has_quantity
msgid "Has Quantity"
msgstr "Има количество"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_category__has_reference
#: model:ir.model.fields,field_description:approvals.field_approval_request__has_reference
msgid "Has Reference"
msgstr "Има справка"

#. module: approvals
#: model:ir.model.fields,help:approvals.field_approval_category__manager_approval
msgid ""
"How the employee's manager interacts with this type of approval.\n"
"\n"
"        Empty: do nothing\n"
"        Is Approver: the employee's manager will be in the approver list\n"
"        Is Required Approver: the employee's manager will be required to approve the request.\n"
"    "
msgstr ""
"Как мениджърът на служителя взаимодейства с този вид одобрение. \n"
"\n"
"Празно: не прави нищо \n"
"Е одобряващ: мениджърът на служителя ще бъде в списъка на одобряващите \n"
"Изисква се одобрение: мениджърът на служителя ще трябва да одобри заявката. \n"
"    "

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_approver__id
#: model:ir.model.fields,field_description:approvals.field_approval_category__id
#: model:ir.model.fields,field_description:approvals.field_approval_category_approver__id
#: model:ir.model.fields,field_description:approvals.field_approval_product_line__id
#: model:ir.model.fields,field_description:approvals.field_approval_request__id
msgid "ID"
msgstr "ID"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__activity_exception_icon
msgid "Icon"
msgstr "Икона"

#. module: approvals
#: model:ir.model.fields,help:approvals.field_approval_request__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Икона за обозначаване на дейност по изключение."

#. module: approvals
#: model:ir.model.fields,help:approvals.field_approval_request__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Ако е отметнато, новите съобщения ще изискват внимание."

#. module: approvals
#: model:ir.model.fields,help:approvals.field_approval_request__message_has_error
#: model:ir.model.fields,help:approvals.field_approval_request__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Ако е отметнато, някои съобщения имат грешка при доставката."

#. module: approvals
#: model:ir.model.fields,help:approvals.field_approval_category__automated_sequence
#: model:ir.model.fields,help:approvals.field_approval_request__automated_sequence
msgid ""
"If checked, the Approval Requests will have an automated generated name "
"based on the given code."
msgstr ""
"Ако е маркирано, заявките за одобрение ще имат автоматично генерирано име "
"въз основа на зададения код."

#. module: approvals
#: model:ir.model.fields,help:approvals.field_approval_category__approver_sequence
#: model:ir.model.fields,help:approvals.field_approval_request__approver_sequence
msgid ""
"If checked, the approvers have to approve in sequence (one after the other)."
" If Employee's Manager is selected as approver, they will be the first in "
"line."
msgstr ""
"Ако е маркирано, одобряващите трябва да одобряват последователно (един след "
"друг). Ако мениджърът на служителя е избран като одобряващ, той ще бъде "
"първият по ред."

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_category__image
#: model:ir.model.fields,field_description:approvals.field_approval_request__category_image
msgid "Image"
msgstr "Изображение"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_category__invalid_minimum
msgid "Invalid Minimum"
msgstr "Невалидна минимална стойност"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_category__invalid_minimum_warning
msgid "Invalid Minimum Warning"
msgstr "Предупреждение за невалидна минимална стойност"

#. module: approvals
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__manager_approval__approver
msgid "Is Approver"
msgstr "Одобряващ е"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__message_is_follower
msgid "Is Follower"
msgstr "е последовател"

#. module: approvals
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__manager_approval__required
msgid "Is Required Approver"
msgstr "Изисква се одобряващ"

#. module: approvals
#: model:approval.category,name:approvals.approval_category_data_job_referral_award
msgid "Job Referral Award"
msgstr "Препоръка за работа"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_approver__write_uid
#: model:ir.model.fields,field_description:approvals.field_approval_category__write_uid
#: model:ir.model.fields,field_description:approvals.field_approval_category_approver__write_uid
#: model:ir.model.fields,field_description:approvals.field_approval_product_line__write_uid
#: model:ir.model.fields,field_description:approvals.field_approval_request__write_uid
msgid "Last Updated by"
msgstr "Последно актуализирано от"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_approver__write_date
#: model:ir.model.fields,field_description:approvals.field_approval_category__write_date
#: model:ir.model.fields,field_description:approvals.field_approval_category_approver__write_date
#: model:ir.model.fields,field_description:approvals.field_approval_product_line__write_date
#: model:ir.model.fields,field_description:approvals.field_approval_request__write_date
msgid "Last Updated on"
msgstr "Последно актуализирано на"

#. module: approvals
#: model_terms:ir.actions.act_window,help:approvals.approval_request_action
#: model_terms:ir.actions.act_window,help:approvals.approval_request_action_all
msgid "Let's go to the"
msgstr "Да отидем до"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__location
#: model_terms:ir.ui.view,arch_db:approvals.approval_category_view_form
#: model_terms:ir.ui.view,arch_db:approvals.approval_request_view_tree
msgid "Location"
msgstr "Локация"

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.report_aprroval_request_document
msgid "Location:"
msgstr "Местоположение:"

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.report_aprroval_request_document
msgid ""
"Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod "
"tempor incididunt ut labore et dolore magna aliqua."
msgstr ""
"Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod "
"tempor incididunt ut labore et dolore magna aliqua."

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__message_main_attachment_id
msgid "Main Attachment"
msgstr "Основен прикачен Файл"

#. module: approvals
#: model:ir.ui.menu,name:approvals.approvals_menu_manager
msgid "Manager"
msgstr "Ръководител"

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.report_aprroval_request_document
msgid "Marc Demo"
msgstr "Марк Демо"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__message_has_error
msgid "Message Delivery error"
msgstr "Грешка при доставяне на съобщението"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__message_ids
msgid "Messages"
msgstr "Syob]eniq"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_category__approval_minimum
#: model:ir.model.fields,field_description:approvals.field_approval_request__approval_minimum
msgid "Minimum Approval"
msgstr "Минимално одобрение"

#. module: approvals
#. odoo-python
#: code:addons/approvals/models/approval_category.py:0
msgid ""
"Minimum Approval must be equal or superior to the sum of required Approvers."
msgstr ""
"Минималното одобрение трябва да е равно или по-голямо от сбора на "
"изискваните одобряващи."

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.report_aprroval_request_document
msgid "Mitchell Admin"
msgstr "Mitchell Admin"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Краен срок за моята дейност"

#. module: approvals
#: model:ir.ui.menu,name:approvals.approvals_approval_menu
msgid "My Approvals"
msgstr "Моите одобрения"

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.approval_search_view_search
msgid "My Approvals to Review"
msgstr "Моите одобрения за преглед"

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.approval_search_view_search
msgid "My Request"
msgstr "Моите заявки"

#. module: approvals
#: model:ir.actions.act_window,name:approvals.approval_request_action
#: model:ir.ui.menu,name:approvals.approvals_request_menu_my
msgid "My Requests"
msgstr "Моите заявки"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_category__name
msgid "Name"
msgstr "Име"

#. module: approvals
#. odoo-python
#: code:addons/approvals/models/approval_category.py:0
#: model:ir.model.fields.selection,name:approvals.selection__approval_approver__status__new
#: model:ir.model.fields.selection,name:approvals.selection__approval_request__user_status__new
msgid "New"
msgstr "Нов"

#. module: approvals
#. odoo-javascript
#: code:addons/approvals/static/src/views/kanban/approvals_category_kanban_controller.xml:0
#: model_terms:ir.ui.view,arch_db:approvals.approval_category_view_kanban
msgid "New Request"
msgstr "Нова заявка"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Следващото събитие от календара на дейностите"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Краен срок на следващо действие"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__activity_summary
msgid "Next Activity Summary"
msgstr "Обобщение на следваща дейност"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__activity_type_id
msgid "Next Activity Type"
msgstr "Вид на следващо действие"

#. module: approvals
#: model_terms:ir.actions.act_window,help:approvals.approval_request_action_all
msgid "No Approvals"
msgstr "Няма одобрения"

#. module: approvals
#: model_terms:ir.actions.act_window,help:approvals.approval_request_action
msgid "No Approvals Requests"
msgstr "Няма заявки за одобрение"

#. module: approvals
#: model_terms:ir.actions.act_window,help:approvals.approval_request_action_to_review
#: model_terms:ir.actions.act_window,help:approvals.approval_request_action_to_review_category
msgid "No new approvals to review"
msgstr "Няма нови одобрения за преглед"

#. module: approvals
#: model_terms:ir.actions.act_window,help:approvals.product_template_action
msgid "No product found. Let's create one!"
msgstr "Не е намерен продукт. Нека създадем един!"

#. module: approvals
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__has_amount__no
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__has_date__no
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__has_location__no
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__has_partner__no
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__has_payment_method__no
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__has_period__no
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__has_product__no
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__has_quantity__no
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__has_reference__no
msgid "None"
msgstr "Никакъв"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__message_needaction_counter
msgid "Number of Actions"
msgstr "Брой действия"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__attachment_number
msgid "Number of Attachments"
msgstr "Брой прикачени файлове"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__message_has_error_counter
msgid "Number of errors"
msgstr "Брой грешки"

#. module: approvals
#: model:ir.model.fields,help:approvals.field_approval_request__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Брой съобщения изискващи действие"

#. module: approvals
#: model:ir.model.fields,help:approvals.field_approval_request__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Брой съобщения с грешка при доставка"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_category__request_to_validate_count
msgid "Number of requests to validate"
msgstr "Брой заявки за валидиране"

#. module: approvals
#: model:res.groups,name:approvals.group_approval_user
msgid "Officer: Approve all requests"
msgstr "Служител: Одобри всички заявки"

#. module: approvals
#: model:ir.actions.server,name:approvals.action_open_approval_category
msgid "Open Approval Category"
msgstr "Отвори категория за одобрение"

#. module: approvals
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__has_amount__optional
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__has_date__optional
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__has_location__optional
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__has_partner__optional
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__has_payment_method__optional
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__has_period__optional
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__has_product__optional
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__has_quantity__optional
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__has_reference__optional
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__requirer_document__optional
msgid "Optional"
msgstr "Опция"

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.approval_category_view_form
msgid "Options"
msgstr "Настройки"

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.approval_category_view_form
msgid "Payment"
msgstr "Плащане"

#. module: approvals
#: model:approval.category,name:approvals.approval_category_data_payment_application
msgid "Payment Application"
msgstr "Заявление за плащане"

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.approval_category_view_form
#: model_terms:ir.ui.view,arch_db:approvals.approval_request_view_form
msgid "Period"
msgstr "Период"

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.report_aprroval_request_document
msgid "Period:"
msgstr "Период:"

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.approval_request_view_form
msgid "Print"
msgstr "Печат"

#. module: approvals
#: model:approval.category,name:approvals.approval_category_data_procurement
msgid "Procurement"
msgstr "Снабдяване"

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.approval_category_view_form
msgid "Product"
msgstr "Продукт"

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.report_aprroval_request_document
msgid "Product Description"
msgstr "Описание на продукта"

#. module: approvals
#: model:ir.model,name:approvals.model_approval_product_line
#: model:ir.model.fields,field_description:approvals.field_approval_request__product_line_ids
msgid "Product Line"
msgstr "Линия на продукта"

#. module: approvals
#: model:ir.actions.act_window,name:approvals.product_product_action
#: model:ir.ui.menu,name:approvals.approvals_menu_product_variant
msgid "Product Variants"
msgstr "Продуктови варианти"

#. module: approvals
#: model:ir.actions.act_window,name:approvals.product_template_action
#: model:ir.model.fields,field_description:approvals.field_approval_product_line__product_id
#: model:ir.ui.menu,name:approvals.approvals_menu_product
#: model:ir.ui.menu,name:approvals.approvals_menu_product_template
#: model_terms:ir.ui.view,arch_db:approvals.approval_product_kanban_mobile_view
#: model_terms:ir.ui.view,arch_db:approvals.approval_product_line_view_form
#: model_terms:ir.ui.view,arch_db:approvals.approval_product_line_view_tree
#: model_terms:ir.ui.view,arch_db:approvals.approval_product_line_view_tree_independent
#: model_terms:ir.ui.view,arch_db:approvals.approval_request_view_form
msgid "Products"
msgstr "Продукти"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_product_line__quantity
#: model:ir.model.fields,field_description:approvals.field_approval_request__quantity
#: model_terms:ir.ui.view,arch_db:approvals.approval_category_view_form
#: model_terms:ir.ui.view,arch_db:approvals.report_aprroval_request_document
msgid "Quantity"
msgstr "Количество"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__rating_ids
msgid "Ratings"
msgstr "Оценявания"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__reference
#: model_terms:ir.ui.view,arch_db:approvals.approval_category_view_form
msgid "Reference"
msgstr "Идентификатор"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_category__sequence_id
msgid "Reference Sequence"
msgstr "Референтна последователност"

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.report_aprroval_request_document
msgid "Reference:"
msgstr "Референция:"

#. module: approvals
#. odoo-javascript
#: code:addons/approvals/static/src/web/activity/approval.xml:0
#: model_terms:ir.ui.view,arch_db:approvals.approval_request_view_form
#: model_terms:ir.ui.view,arch_db:approvals.approval_request_view_kanban
msgid "Refuse"
msgstr "Откажете"

#. module: approvals
#: model:ir.model.fields.selection,name:approvals.selection__approval_approver__status__refused
#: model:ir.model.fields.selection,name:approvals.selection__approval_request__request_status__refused
#: model:ir.model.fields.selection,name:approvals.selection__approval_request__user_status__refused
#: model_terms:ir.ui.view,arch_db:approvals.approval_search_view_search
msgid "Refused"
msgstr "Отказан"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_approver__request_id
#: model_terms:ir.ui.view,arch_db:approvals.approval_request_view_form
msgid "Request"
msgstr "Заявка"

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.res_users_view_form
msgid "Request Approval"
msgstr "Одобрени заявки"

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.report_aprroval_request_document
msgid "Request By:"
msgstr "Заявка от:"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__request_owner_id
#: model_terms:ir.ui.view,arch_db:approvals.approval_search_view_search
msgid "Request Owner"
msgstr "Обработил заявка"

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.approval_request_view_tree
msgid "Request Start"
msgstr "Начало на заявката"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__request_status
msgid "Request Status"
msgstr "Състояние на заявката"

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.approval_request_view_tree
msgid "Request To"
msgstr "Заявка до"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_approver__required
#: model:ir.model.fields,field_description:approvals.field_approval_category_approver__required
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__has_amount__required
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__has_date__required
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__has_location__required
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__has_partner__required
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__has_payment_method__required
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__has_period__required
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__has_product__required
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__has_quantity__required
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__has_reference__required
#: model:ir.model.fields.selection,name:approvals.selection__approval_category__requirer_document__required
msgid "Required"
msgstr "Задължителен"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__activity_user_id
msgid "Responsible User"
msgstr "Отговорен потребител"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__message_has_sms_error
msgid "SMS Delivery error"
msgstr "SMS грешка при доставка"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_approver__sequence
#: model:ir.model.fields,field_description:approvals.field_approval_category__sequence
#: model:ir.model.fields,field_description:approvals.field_approval_category_approver__sequence
msgid "Sequence"
msgstr "Последователност"

#. module: approvals
#. odoo-python
#: code:addons/approvals/models/approval_category.py:0
msgid "Sequence %(code)s"
msgstr "Последователност %(code)s"

#. module: approvals
#: model:ir.model.fields,help:approvals.field_approval_approver__can_edit_user_id
msgid ""
"Simple users should not be able to remove themselves as approvers because "
"they will lose access to the record if they misclick."
msgstr ""
"Обикновените потребители не трябва да могат сами да се премахват като "
"одобряващи, тъй като ще загубят достъп до записа, ако кликнат погрешно."

#. module: approvals
#. odoo-python
#: code:addons/approvals/models/approval_request.py:0
msgid "Start date should precede the end date."
msgstr "Началната дата трябва да предхожда крайната дата."

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_approver__status
msgid "Status"
msgstr "Състояние"

#. module: approvals
#: model:ir.model.fields,help:approvals.field_approval_request__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Статус въз основа на дейности\n"
"Просрочени: Срокът вече е изтекъл\n"
"Днес: Датата на дейността е днес\n"
"Планирано: Бъдещи дейности."

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.report_aprroval_request_document
msgid "Status:"
msgstr "Състояние:"

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.approval_request_view_form
msgid "Submit"
msgstr "Изпращане"

#. module: approvals
#: model:ir.model.fields.selection,name:approvals.selection__approval_request__request_status__pending
msgid "Submitted"
msgstr "Внесено"

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.report_aprroval_request_document
msgid "Test Category"
msgstr "Тестова категория"

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.report_aprroval_request_document
msgid "Test Document"
msgstr "Тестови документ"

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.report_aprroval_request_document
msgid "Test Product"
msgstr "Тестов продукт"

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.report_aprroval_request_document
msgid "Test Reference"
msgstr "Тестова Референция"

#. module: approvals
#. odoo-python
#: code:addons/approvals/models/approval_request.py:0
msgid "The request %(request_name)s for %(request_owner)s has been accepted"
msgstr "Заявката %(request_name)s за %(request_owner)s е приета"

#. module: approvals
#. odoo-python
#: code:addons/approvals/models/approval_request.py:0
msgid "The request %(request_name)s for %(request_owner)s has been refused"
msgstr "Заявката %(request_name)s за %(request_owner)s е отхвърлена"

#. module: approvals
#. odoo-python
#: code:addons/approvals/models/approval_request.py:0
msgid ""
"The request created on %(create_date)s by %(request_owner)s has been "
"accepted."
msgstr ""
"Заявката, създадена на %(create_date)s от  %(request_owner)s е приета."

#. module: approvals
#. odoo-python
#: code:addons/approvals/models/approval_request.py:0
msgid ""
"The request created on %(create_date)s by %(request_owner)s has been "
"refused."
msgstr ""
"Заявката, създадена на %(create_date)s от %(request_owner)s е отказана."

#. module: approvals
#: model:res.groups,comment:approvals.group_approval_user
msgid "The user will be able to access all requests and approve/refuse them."
msgstr "Потребителят ще може да достъпва всички заявки и да ги одобри/откаже."

#. module: approvals
#: model:res.groups,comment:approvals.group_approval_manager
msgid "The user will have access to the approvals configuration."
msgstr "Потребителят ще има достъп до конфигурацията за одобрения."

#. module: approvals
#. odoo-python
#: code:addons/approvals/models/approval_request.py:0
msgid ""
"This request needs to be approved by your manager. There is no manager "
"linked to your employee profile."
msgstr ""
"Тази заявка трябва да бъде одобрена от вашия мениджър. В профила ви на "
"служител не е посочен мениджър."

#. module: approvals
#. odoo-python
#: code:addons/approvals/models/approval_request.py:0
msgid ""
"This request needs to be approved by your manager. There is no user linked "
"to your manager."
msgstr ""
"Тази заявка трябва да се одобри от вашия мениджър. Няма потребител, свързан "
"с вашия мениджър."

#. module: approvals
#. odoo-python
#: code:addons/approvals/models/approval_request.py:0
msgid ""
"This request needs to be approved by your manager. Your manager is not in "
"the approvers list."
msgstr ""
"Тази заявка трябва да се одобри от вашия мениджър. Вашият мениджър не е "
"включен в списъка на одобряващите."

#. module: approvals
#. odoo-javascript
#: code:addons/approvals/static/src/web/activity/approval.xml:0
#: model:ir.model.fields.selection,name:approvals.selection__approval_approver__status__pending
#: model:ir.model.fields.selection,name:approvals.selection__approval_request__user_status__pending
msgid "To Approve"
msgstr "За одобрение"

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.approval_category_view_kanban
msgid "To Review:"
msgstr "За преглед:"

#. module: approvals
#: model:ir.model.fields.selection,name:approvals.selection__approval_request__request_status__new
msgid "To Submit"
msgstr "За внасяне"

#. module: approvals
#: model:ir.model.fields,help:approvals.field_approval_request__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Вид на записаната дейност по изключение."

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.approval_category_view_kanban
msgid "Unarchive"
msgstr "Разархивирайте"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_product_line__product_uom_id
msgid "Unit of Measure"
msgstr "Мерна единица"

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.report_aprroval_request_document
msgid "Units"
msgstr "Единици"

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.report_aprroval_request_document
msgid "UoM"
msgstr "Мерна единица"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_approver__user_id
#: model:ir.model.fields,field_description:approvals.field_approval_category_approver__user_id
msgid "User"
msgstr "Потребител"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__user_status
msgid "User Status"
msgstr "Статус на потребителя"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__user_ids
msgid "Users"
msgstr "Потребители"

#. module: approvals
#: model:ir.model.fields.selection,name:approvals.selection__approval_approver__status__waiting
#: model:ir.model.fields.selection,name:approvals.selection__approval_request__user_status__waiting
msgid "Waiting"
msgstr "Изчакващ"

#. module: approvals
#: model:ir.model.fields,field_description:approvals.field_approval_request__website_message_ids
msgid "Website Messages"
msgstr "Съобщения в уебсайт"

#. module: approvals
#: model:ir.model.fields,help:approvals.field_approval_request__website_message_ids
msgid "Website communication history"
msgstr "История на комуникацията на уебсайт"

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.approval_request_view_form
#: model_terms:ir.ui.view,arch_db:approvals.approval_request_view_kanban
msgid "Withdraw"
msgstr "Оттегляне"

#. module: approvals
#. odoo-python
#: code:addons/approvals/models/approval_request.py:0
msgid "You cannot approve before the previous approver."
msgstr "Не можете да одобрите преди предишния одобряващ."

#. module: approvals
#. odoo-python
#: code:addons/approvals/models/approval_request.py:0
msgid ""
"You cannot assign the same approver multiple times on the same request."
msgstr ""
"Не можете да зададете един и същ одобряващ няколко пъти за една и съща "
"заявка."

#. module: approvals
#. odoo-python
#: code:addons/approvals/models/ir_attachment.py:0
msgid ""
"You cannot unlink an attachment which is linked to a validated, refused or "
"cancelled approval request."
msgstr ""
"Не можете да премахнете приложен файл, който е свързан с валидирана, "
"отхвърлена или анулирана заявка за одобрение."

#. module: approvals
#. odoo-python
#: code:addons/approvals/models/approval_request.py:0
msgid "You have to add at least %s approvers to confirm your request."
msgstr "Трябва да добавите поне %sодобрители, за да потвърдите заявката си."

#. module: approvals
#. odoo-python
#: code:addons/approvals/models/approval_request.py:0
msgid "You have to attach at least one document."
msgstr "Трябва да приложите поне един документ."

#. module: approvals
#: model_terms:ir.actions.act_window,help:approvals.product_product_action
msgid ""
"You must define a product for everything you sell or purchase,\n"
"            whether it's a storable product, a consumable or a service."
msgstr ""
"Трябва да определите продукт за всичко, което продавате или купувате,\n"
"            независимо дали става въпрос за продукт, който може да се съхранява, консуматив или услуга."

#. module: approvals
#. odoo-python
#: code:addons/approvals/models/approval_category.py:0
msgid "Your minimum approval exceeds the total of default approvers."
msgstr ""
"Минималният брой одобрения надвишава общия брой одобряващи по подразбиране."

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.approval_request_view_form
msgid "e.g. Brussels"
msgstr "напр. Брюксел"

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.approval_category_view_form
msgid "e.g. Procurement"
msgstr "напр. снабдяване"

#. module: approvals
#: model_terms:ir.actions.act_window,help:approvals.approval_request_action
#: model_terms:ir.actions.act_window,help:approvals.approval_request_action_all
msgid "menu"
msgstr "меню"

#. module: approvals
#: model_terms:ir.actions.act_window,help:approvals.approval_request_action
#: model_terms:ir.actions.act_window,help:approvals.approval_request_action_all
msgid "new request"
msgstr "нова заявка"

#. module: approvals
#: model_terms:ir.ui.view,arch_db:approvals.approval_request_view_form
msgid "to:"
msgstr "към:"
