# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_renting_crm
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:28+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON><PERSON>, 2025\n"
"Language-Team: German (https://app.transifex.com/odoo/teams/41243/de/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: de\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: sale_renting_crm
#: model_terms:ir.ui.view,arch_db:sale_renting_crm.crm_case_form_view_oppor
msgid "<span class=\"o_stat_text\"> Rental Orders</span>"
msgstr "<span class=\"o_stat_text\"> Mietaufträge</span>"

#. module: sale_renting_crm
#: model:ir.model.fields,field_description:sale_renting_crm.field_crm_lead_rental__lead_id
msgid "Associated Lead"
msgstr "Verbundenes Lead"

#. module: sale_renting_crm
#: model_terms:ir.ui.view,arch_db:sale_renting_crm.crm_lead_rental_view_form
msgid "Cancel"
msgstr "Abbrechen"

#. module: sale_renting_crm
#: model_terms:ir.ui.view,arch_db:sale_renting_crm.crm_lead_rental_view_form
msgid "Confirm"
msgstr "Bestätigen"

#. module: sale_renting_crm
#: model:ir.model,name:sale_renting_crm.model_crm_lead_rental
msgid "Convert Lead to Rental Order"
msgstr "Lead in Mietauftrag umwandeln"

#. module: sale_renting_crm
#: model:ir.model.fields.selection,name:sale_renting_crm.selection__crm_lead_rental__action__create
msgid "Create a new customer"
msgstr "Neuen Kunden erstellen"

#. module: sale_renting_crm
#: model_terms:ir.ui.view,arch_db:sale_renting_crm.crm_case_form_view_oppor
msgid "Create new rental"
msgstr "Neuen Mietauftrag erstellen"

#. module: sale_renting_crm
#: model:ir.model.fields,field_description:sale_renting_crm.field_crm_lead_rental__create_uid
msgid "Created by"
msgstr "Erstellt von"

#. module: sale_renting_crm
#: model:ir.model.fields,field_description:sale_renting_crm.field_crm_lead_rental__create_date
msgid "Created on"
msgstr "Erstellt am"

#. module: sale_renting_crm
#: model:ir.model.fields,field_description:sale_renting_crm.field_crm_lead_rental__partner_id
msgid "Customer"
msgstr "Kunde"

#. module: sale_renting_crm
#: model:ir.model.fields,field_description:sale_renting_crm.field_crm_lead_rental__display_name
msgid "Display Name"
msgstr "Anzeigename"

#. module: sale_renting_crm
#: model:ir.model.fields.selection,name:sale_renting_crm.selection__crm_lead_rental__action__nothing
msgid "Do not link to a customer"
msgstr "Nicht mit einem Kunden verknüpfen"

#. module: sale_renting_crm
#: model:ir.model.fields,field_description:sale_renting_crm.field_crm_lead_rental__id
msgid "ID"
msgstr "ID"

#. module: sale_renting_crm
#: model:ir.model.fields,field_description:sale_renting_crm.field_crm_lead_rental__write_uid
msgid "Last Updated by"
msgstr "Zuletzt aktualisiert von"

#. module: sale_renting_crm
#: model:ir.model.fields,field_description:sale_renting_crm.field_crm_lead_rental__write_date
msgid "Last Updated on"
msgstr "Zuletzt aktualisiert am"

#. module: sale_renting_crm
#: model:ir.model,name:sale_renting_crm.model_crm_lead
msgid "Lead/Opportunity"
msgstr "Lead/Verkaufschance"

#. module: sale_renting_crm
#: model:ir.model.fields.selection,name:sale_renting_crm.selection__crm_lead_rental__action__exist
msgid "Link to an existing customer"
msgstr "Mit bestehendem Kunden verknüpfen"

#. module: sale_renting_crm
#: model:ir.actions.act_window,name:sale_renting_crm.crm_lead_to_rental_action
#: model_terms:ir.ui.view,arch_db:sale_renting_crm.crm_case_form_view_oppor
#: model_terms:ir.ui.view,arch_db:sale_renting_crm.crm_lead_rental_view_form
msgid "New Rental"
msgstr "Neue Vermietung"

#. module: sale_renting_crm
#: model:ir.model.fields,field_description:sale_renting_crm.field_crm_lead__rental_order_count
msgid "Number of Rental Orders"
msgstr "Anzahl der Mietaufträge"

#. module: sale_renting_crm
#: model:ir.model.fields,field_description:sale_renting_crm.field_crm_lead__rental_quotation_count
msgid "Number of Rental Quotations"
msgstr "Anzahl der Mietangebote"

#. module: sale_renting_crm
#: model:ir.model.fields,field_description:sale_renting_crm.field_sale_order__opportunity_id
msgid "Opportunity"
msgstr "Verkaufschance"

#. module: sale_renting_crm
#: model:ir.model.fields,field_description:sale_renting_crm.field_crm_lead_rental__action
msgid "Rental Customer"
msgstr "Mietkunde"

#. module: sale_renting_crm
#. odoo-python
#: code:addons/sale_renting_crm/models/crm_lead.py:0
#: model:ir.model.fields,field_description:sale_renting_crm.field_crm_lead__rental_order_ids
msgid "Rental Orders"
msgstr "Mietaufträge"

#. module: sale_renting_crm
#: model_terms:ir.ui.view,arch_db:sale_renting_crm.crm_case_form_view_oppor
msgid "Rentals"
msgstr "Vermietungen"

#. module: sale_renting_crm
#: model:ir.model,name:sale_renting_crm.model_sale_order
msgid "Sales Order"
msgstr "Verkaufsauftrag"

#. module: sale_renting_crm
#: model:ir.model.fields,field_description:sale_renting_crm.field_crm_lead__rental_amount_total
msgid "Sum of Rental Orders"
msgstr "Summe der Mietaufträge"

#. module: sale_renting_crm
#: model:ir.model.fields,help:sale_renting_crm.field_crm_lead__rental_amount_total
msgid "Untaxed Total of Confirmed Orders"
msgstr "Gesamter Nettobetrag der bestätigten Aufträge"

#. module: sale_renting_crm
#. odoo-python
#: code:addons/sale_renting_crm/wizard/crm_lead_rental.py:0
msgid "You can only apply this action from a lead."
msgstr "Sie können diese Aktion nur von einem Lead aus anwenden."
