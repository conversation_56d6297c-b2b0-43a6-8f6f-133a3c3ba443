# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_renting_crm
# 
# Translators:
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:28+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Indonesian (https://app.transifex.com/odoo/teams/41243/id/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: id\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: sale_renting_crm
#: model_terms:ir.ui.view,arch_db:sale_renting_crm.crm_case_form_view_oppor
msgid "<span class=\"o_stat_text\"> Rental Orders</span>"
msgstr "<span class=\"o_stat_text\"> Rental Order</span>"

#. module: sale_renting_crm
#: model:ir.model.fields,field_description:sale_renting_crm.field_crm_lead_rental__lead_id
msgid "Associated Lead"
msgstr "Lead Terkait"

#. module: sale_renting_crm
#: model_terms:ir.ui.view,arch_db:sale_renting_crm.crm_lead_rental_view_form
msgid "Cancel"
msgstr "Batal"

#. module: sale_renting_crm
#: model_terms:ir.ui.view,arch_db:sale_renting_crm.crm_lead_rental_view_form
msgid "Confirm"
msgstr "Konfirmasi"

#. module: sale_renting_crm
#: model:ir.model,name:sale_renting_crm.model_crm_lead_rental
msgid "Convert Lead to Rental Order"
msgstr "Konversikan Lead menjadi Rental Order"

#. module: sale_renting_crm
#: model:ir.model.fields.selection,name:sale_renting_crm.selection__crm_lead_rental__action__create
msgid "Create a new customer"
msgstr "Buat pelanggan baru"

#. module: sale_renting_crm
#: model_terms:ir.ui.view,arch_db:sale_renting_crm.crm_case_form_view_oppor
msgid "Create new rental"
msgstr "Buat rental baru"

#. module: sale_renting_crm
#: model:ir.model.fields,field_description:sale_renting_crm.field_crm_lead_rental__create_uid
msgid "Created by"
msgstr "Dibuat oleh"

#. module: sale_renting_crm
#: model:ir.model.fields,field_description:sale_renting_crm.field_crm_lead_rental__create_date
msgid "Created on"
msgstr "Dibuat pada"

#. module: sale_renting_crm
#: model:ir.model.fields,field_description:sale_renting_crm.field_crm_lead_rental__partner_id
msgid "Customer"
msgstr "Pelanggan"

#. module: sale_renting_crm
#: model:ir.model.fields,field_description:sale_renting_crm.field_crm_lead_rental__display_name
msgid "Display Name"
msgstr "Nama Tampilan"

#. module: sale_renting_crm
#: model:ir.model.fields.selection,name:sale_renting_crm.selection__crm_lead_rental__action__nothing
msgid "Do not link to a customer"
msgstr "Tidak hubungkan ke pelanggan"

#. module: sale_renting_crm
#: model:ir.model.fields,field_description:sale_renting_crm.field_crm_lead_rental__id
msgid "ID"
msgstr "ID"

#. module: sale_renting_crm
#: model:ir.model.fields,field_description:sale_renting_crm.field_crm_lead_rental__write_uid
msgid "Last Updated by"
msgstr "Terakhir Diperbarui oleh"

#. module: sale_renting_crm
#: model:ir.model.fields,field_description:sale_renting_crm.field_crm_lead_rental__write_date
msgid "Last Updated on"
msgstr "Terakhir Diperbarui pada"

#. module: sale_renting_crm
#: model:ir.model,name:sale_renting_crm.model_crm_lead
msgid "Lead/Opportunity"
msgstr "Prospek/Peluang"

#. module: sale_renting_crm
#: model:ir.model.fields.selection,name:sale_renting_crm.selection__crm_lead_rental__action__exist
msgid "Link to an existing customer"
msgstr "Link ke pelanggan yang sudah ada"

#. module: sale_renting_crm
#: model:ir.actions.act_window,name:sale_renting_crm.crm_lead_to_rental_action
#: model_terms:ir.ui.view,arch_db:sale_renting_crm.crm_case_form_view_oppor
#: model_terms:ir.ui.view,arch_db:sale_renting_crm.crm_lead_rental_view_form
msgid "New Rental"
msgstr "Rental Baru"

#. module: sale_renting_crm
#: model:ir.model.fields,field_description:sale_renting_crm.field_crm_lead__rental_order_count
msgid "Number of Rental Orders"
msgstr "Jumlah Rental Order"

#. module: sale_renting_crm
#: model:ir.model.fields,field_description:sale_renting_crm.field_crm_lead__rental_quotation_count
msgid "Number of Rental Quotations"
msgstr "Jumlah Quotation Rental"

#. module: sale_renting_crm
#: model:ir.model.fields,field_description:sale_renting_crm.field_sale_order__opportunity_id
msgid "Opportunity"
msgstr "Peluang"

#. module: sale_renting_crm
#: model:ir.model.fields,field_description:sale_renting_crm.field_crm_lead_rental__action
msgid "Rental Customer"
msgstr "Pelanggan Rental"

#. module: sale_renting_crm
#. odoo-python
#: code:addons/sale_renting_crm/models/crm_lead.py:0
#: model:ir.model.fields,field_description:sale_renting_crm.field_crm_lead__rental_order_ids
msgid "Rental Orders"
msgstr "Rental Order"

#. module: sale_renting_crm
#: model_terms:ir.ui.view,arch_db:sale_renting_crm.crm_case_form_view_oppor
msgid "Rentals"
msgstr "Rental"

#. module: sale_renting_crm
#: model:ir.model,name:sale_renting_crm.model_sale_order
msgid "Sales Order"
msgstr "Order Penjualan"

#. module: sale_renting_crm
#: model:ir.model.fields,field_description:sale_renting_crm.field_crm_lead__rental_amount_total
msgid "Sum of Rental Orders"
msgstr "Jumlah Rental Order"

#. module: sale_renting_crm
#: model:ir.model.fields,help:sale_renting_crm.field_crm_lead__rental_amount_total
msgid "Untaxed Total of Confirmed Orders"
msgstr "Total Sebelum Pajak dari Order Penjualan"

#. module: sale_renting_crm
#. odoo-python
#: code:addons/sale_renting_crm/wizard/crm_lead_rental.py:0
msgid "You can only apply this action from a lead."
msgstr "Anda hanya dapat menerapkan tindakan ini dari lead."
