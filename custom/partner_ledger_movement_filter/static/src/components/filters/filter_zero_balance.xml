<templates xml:space="preserve">
    <t t-name="partner_ledger_movement_filter.option_zero_balance"
        t-inherit="account_reports.PartnerLedgerReportFilterExtraOptions" t-inherit-mode="extension">
        <xpath expr="//Dropdown/t[@t-set-slot='content']" position="inside">
            <t t-if="controller.filters.show_zero_balance">
                <DropdownItem
                    class="{ 'filter_show_zero_balance_hook': true, 'selected': controller.options.zero_balance }"
                    onSelected="() => this.filterClicked({ optionKey: 'zero_balance', reload: true })"
                    closingMode="'none'"
                >
                    Zero Balance
                </DropdownItem>
            </t>
        </xpath>
    </t>
</templates>
