# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_followup
# 
# Translators:
# Wil <PERSON>, 2024
# <PERSON> <<EMAIL>>, 2025
# <PERSON><PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-16 20:44+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2025\n"
"Language-Team: Spanish (Latin America) (https://app.transifex.com/odoo/teams/41243/es_419/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es_419\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: account_followup
#. odoo-python
#: code:addons/account_followup/models/account_followup_report.py:0
msgid "%(company)s Payment Reminder - %(partner)s"
msgstr "%(company)s Recordatorio de pago - %(partner)s"

#. module: account_followup
#. odoo-python
#: code:addons/account_followup/models/account_followup.py:0
msgid "%(delay)s days (copy of %(name)s)"
msgstr "%(delay)s días (copia de %(name)s)"

#. module: account_followup
#: model:ir.actions.report,print_report_name:account_followup.action_report_followup
msgid "'Follow-up ' + object.display_name"
msgstr "'Seguimiento' + object.display_name"

#. module: account_followup
#: model:account_followup.followup.line,name:account_followup.demo_followup_line1
msgid "15 Days"
msgstr "15 días"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.followup_filter_info_template
msgid "2023-09-06"
msgstr "06-09-2023"

#. module: account_followup
#: model:account_followup.followup.line,name:account_followup.demo_followup_line2
msgid "30 Days"
msgstr "30 días"

#. module: account_followup
#: model:account_followup.followup.line,name:account_followup.demo_followup_line3
msgid "40 Days"
msgstr "40 días"

#. module: account_followup
#: model:account_followup.followup.line,name:account_followup.demo_followup_line4
msgid "50 Days"
msgstr "50 días"

#. module: account_followup
#: model:account_followup.followup.line,name:account_followup.demo_followup_line5
msgid "60 Days"
msgstr "60 días"

#. module: account_followup
#: model:mail.template,body_html:account_followup.email_template_followup_1
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"                    <p style=\"margin: 0px; padding: 0px;\">\n"
"                        <t t-if=\"object.id != object.commercial_partner_id.id\">Dear <t t-out=\"object.name or ''\"/> (<t t-out=\"object.commercial_partner_id.name or ''\"/>),</t>\n"
"                        <t t-else=\"\">Dear <t t-out=\"object.name or ''\"/>,</t>\n"
"                        <br/>\n"
"                        It has come to our attention that you have&nbsp;an outstanding balance of <t t-out=\"format_amount(object.total_overdue, object.currency_id) or ''\"/>\n"
"                        <br/>\n"
"                        We kindly request that you take necessary action to settle this amount within the next 8 days.\n"
"                        <br/>\n"
"                        </p><div t-if=\"object._show_pay_now_button()\" class=\"d-flex\">\n"
"                            <a t-att-href=\"'/my/invoices/overdue'\" class=\"btn btn-primary\">Pay now</a>\n"
"                        </div>\n"
"                        If you have already made the payment after receiving this message, please disregard it.\n"
"                        Our accounting department is available if you require any assistance or have any questions.\n"
"                        <br/>\n"
"                        Thank you for your cooperation.\n"
"                        <br/>\n"
"                        Sincerely,\n"
"                        <t t-if=\"not is_html_empty(object._get_followup_responsible().signature)\">\n"
"                            <br/>\n"
"                            <t t-out=\"object._get_followup_responsible().signature\"/>\n"
"                        </t>\n"
"                        <t t-else=\"\">\n"
"                            <br/>\n"
"                            --\n"
"                            <br/>\n"
"                            <t t-out=\"object._get_followup_responsible().name\"/>\n"
"                        </t>\n"
"                    \n"
"                </div>\n"
"            "
msgstr ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"                    <p style=\"margin: 0px; padding: 0px;\">\n"
"                        <t t-if=\"object.id != object.commercial_partner_id.id\">Apreciable <t t-out=\"object.name or ''\"/> (<t t-out=\"object.commercial_partner_id.name or ''\"/>),</t>\n"
"                        <t t-else=\"\">Apreciable <t t-out=\"object.name or ''\"/>,</t>\n"
"                        <br/>\n"
"                        Tiene un&nbsp;saldo pendiente de <t t-out=\"format_amount(object.total_overdue, object.currency_id) or ''\"/>\n"
"                        <br/>\n"
"                        Le pedimos que tome las medidas necesarias para realizar el pago de esta cantidad dentro de los próximos 8 días.\n"
"                        <br/>\n"
"                        </p><div t-if=\"object._show_pay_now_button()\" class=\"d-flex\">\n"
"                            <a t-att-href=\"'/my/invoices/overdue'\" class=\"btn btn-primary\">Pagar ahora</a>\n"
"                        </div>\n"
"                        Omita este mensaje si realizó el pago después de recibirlo.\n"
"                        Nuestro departamento de contabilidad está disponible para ayudarle o responder cualquier pregunta que pueda tener.\n"
"                        <br/>\n"
"                        Gracias por su cooperación.\n"
"                        <br/>\n"
"                        Atentamente,\n"
"                        <t t-if=\"not is_html_empty(object._get_followup_responsible().signature)\">\n"
"                            <br/>\n"
"                            <t t-out=\"object._get_followup_responsible().signature\"/>\n"
"                        </t>\n"
"                        <t t-else=\"\">\n"
"                            <br/>\n"
"                            --\n"
"                            <br/>\n"
"                            <t t-out=\"object._get_followup_responsible().name\"/>\n"
"                        </t>\n"
"                    \n"
"                </div>\n"
"            "

#. module: account_followup
#: model:mail.template,body_html:account_followup.demo_followup_email_template_4
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"                    <p>\n"
"                        <t t-if=\"object.id != object.commercial_partner_id.id\">Dear <t t-out=\"object.name or ''\"/> (<t t-out=\"object.commercial_partner_id.name or ''\"/>),</t>\n"
"                        <t t-else=\"\">Dear <t t-out=\"object.name or ''\"/>,</t>\n"
"                        <br/>\n"
"                        Despite several reminders, your account is still not settled.\n"
"                        Unless full payment is made in next 8 days, then legal action for the recovery of the debt will be taken without further notice.\n"
"                        I trust that this action will prove unnecessary and details of due payments is printed below.\n"
"                        In case of any queries concerning this matter, do not hesitate to contact our accounting department.\n"
"                        <br/>\n"
"                        Best Regards,\n"
"                        <br/>\n"
"                        <t t-if=\"not is_html_empty(object._get_followup_responsible().signature)\">\n"
"                            <br/>\n"
"                            <t t-out=\"object._get_followup_responsible().signature\"/>\n"
"                        </t>\n"
"                        <t t-else=\"\">\n"
"                            <br/>\n"
"                            --\n"
"                            <br/>\n"
"                            <t t-out=\"object._get_followup_responsible().name\"/>\n"
"                        </t>\n"
"                    </p>\n"
"                </div>\n"
"            "
msgstr ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"                    <p>\n"
"                        <t t-if=\"object.id != object.commercial_partner_id.id\">Apreciable <t t-out=\"object.name or ''\"/> (<t t-out=\"object.commercial_partner_id.name or ''\"/>),</t>\n"
"                        <t t-else=\"\">Apreciable <t t-out=\"object.name or ''\"/>,</t>\n"
"                        <br/>\n"
"                        A pesar de que le hemos enviado varios recordatorios, aún no ha liquidado su deuda.\n"
"                        Si no realiza el pago en los próximos 8 días, tomaremos acciones legales sin previo aviso para la recuperación de la deuda.\n"
"                        Confiamos en que esta medida será innecesaria. Los detalles de los pagos pendientes están disponibles a continuación.\n"
"                        En caso de tener alguna duda sobre estos pagos, no dude en contactar a nuestro departamento de contabilidad.\n"
"                        <br/>\n"
"                        Saludos cordiales,\n"
"                        <br/>\n"
"                        <t t-if=\"not is_html_empty(object._get_followup_responsible().signature)\">\n"
"                            <br/>\n"
"                            <t t-out=\"object._get_followup_responsible().signature\"/>\n"
"                        </t>\n"
"                        <t t-else=\"\">\n"
"                            <br/>\n"
"                            --\n"
"                            <br/>\n"
"                            <t t-out=\"object._get_followup_responsible().name\"/>\n"
"                        </t>\n"
"                    </p>\n"
"                </div>\n"
"            "

#. module: account_followup
#: model:mail.template,body_html:account_followup.demo_followup_email_template_2
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"                    <p>\n"
"                        <t t-if=\"object.id != object.commercial_partner_id.id\">Dear <t t-out=\"object.name or ''\"/> (<t t-out=\"object.commercial_partner_id.name or ''\"/>),</t>\n"
"                        <t t-else=\"\">Dear <t t-out=\"object.name or ''\"/>,</t>\n"
"                        <br/>\n"
"                        We are disappointed to see that despite sending a reminder, that your account is now seriously overdue.\n"
"                        <br/>\n"
"                        It is essential that immediate payment is made, otherwise we will have to consider placing a stop on your account which means that we will no longer be able to supply your company with (goods/services). Please, take appropriate measures in order to carry out this payment in the next 8 days.\n"
"                        <br/>\n"
"                        If there is a problem with paying invoice that we are not aware of, do not hesitate to contact our accounting department, so that we can resolve the matter quickly.\n"
"                        <br/>\n"
"                        Details of due payments is printed below.\n"
"                        <br/>\n"
"                        Best Regards,\n"
"                        <t t-if=\"not is_html_empty(object._get_followup_responsible().signature)\">\n"
"                            <br/>\n"
"                            <t t-out=\"object._get_followup_responsible().signature\"/>\n"
"                        </t>\n"
"                        <t t-else=\"\">\n"
"                            <br/>\n"
"                            --\n"
"                            <br/>\n"
"                            <t t-out=\"object._get_followup_responsible().name\"/>\n"
"                        </t>\n"
"                    </p>\n"
"                </div>\n"
"            "
msgstr ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"                    <p>\n"
"                        <t t-if=\"object.id != object.commercial_partner_id.id\">Apreciable <t t-out=\"object.name or ''\"/> (<t t-out=\"object.commercial_partner_id.name or ''\"/>),</t>\n"
"                        <t t-else=\"\">Apreciable <t t-out=\"object.name or ''\"/>,</t>\n"
"                        <br/>\n"
"                        Lamentamos ver que, a pesar de haberle enviado un recordatorio, su cuenta todavía tiene adeudos considerables.\n"
"                        <br/>\n"
"                        Es muy importante que pague de inmediato o tendremos que considerar suspender su cuenta, lo que significa que ya no podremos proveerle (bienes/servicios) a su empresa. Tome las medidas adecuadas para llevar a cabo este pago en los próximos 8 días.\n"
"                        <br/>\n"
"                        Si hay algún problema con el pago de la factura del que no tengamos conocimiento, no dude en contactar a nuestro departamento de contabilidad para que podamos resolver el asunto con rapidez.\n"
"                        <br/>\n"
"                        Los detalles de los pagos pendientes se encuentran impresos a continuación.\n"
"                        <br/>\n"
"                        Saludos cordiales,\n"
"                        <t t-if=\"not is_html_empty(object._get_followup_responsible().signature)\">\n"
"                            <br/>\n"
"                            <t t-out=\"object._get_followup_responsible().signature\"/>\n"
"                        </t>\n"
"                        <t t-else=\"\">\n"
"                            <br/>\n"
"                            --\n"
"                            <br/>\n"
"                            <t t-out=\"object._get_followup_responsible().name\"/>\n"
"                        </t>\n"
"                    </p>\n"
"                </div>\n"
"            "

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.res_partner_view_form
msgid "<span class=\"o_stat_text\">Customer Statement</span>"
msgstr "<span class=\"o_stat_text\">Estado del cliente</span>"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.res_partner_view_form
msgid ""
"<span>Preferred address for follow-up reports. Selected by default when you "
"send reminders about overdue invoices.</span>"
msgstr ""
"<span>Dirección preferida para los reportes de seguimiento. Se selecciona de"
" forma predeterminada al enviar recordatorios de las facturas "
"vencidas.</span>"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.template_followup_report
msgid ""
"<strong>\n"
"                                Pending Invoices\n"
"                            </strong>"
msgstr ""
"<strong>\n"
"                                Facturas pendientes\n"
"                            </strong>"

#. module: account_followup
#: model:ir.model.constraint,message:account_followup.constraint_account_followup_followup_line_uniq_name
msgid ""
"A follow-up action name must be unique. This name is already set to another "
"action."
msgstr ""
"El nombre de una acción de seguimiento debe ser único. Ya hay una acción con"
" este nombre."

#. module: account_followup
#: model:ir.model.fields.selection,name:account_followup.selection__account_followup_followup_line__activity_default_responsible_type__account_manager
msgid "Account Manager"
msgstr "Gerente de cuenta"

#. module: account_followup
#: model:ir.actions.server,name:account_followup.ir_cron_auto_post_draft_entry_ir_actions_server
msgid "Account Report Followup; Execute followup"
msgstr "Reporte del seguimiento de la cuenta; ejecutar seguimiento"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_form
msgid "Actions"
msgstr "Acciones"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_form
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_tree
msgid "Activity"
msgstr "Actividad"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_form
msgid "Activity Notes"
msgstr "Notas de actividad"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__activity_type_id
msgid "Activity Type"
msgstr "Tipo de actividad"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.template_followup_report
msgid "Add a note"
msgstr "Agregar una nota"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.manual_reminder_view_form
msgid "Add contacts to notify..."
msgstr "Agregue contactos por notificar..."

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__additional_follower_ids
msgid "Add followers"
msgstr "Agregar seguidores"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.followup_filter_info_template
msgid "Address"
msgstr "Dirección"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_res_partner__type
#: model:ir.model.fields,field_description:account_followup.field_res_users__type
msgid "Address Type"
msgstr "Tipo de dirección"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__join_invoices
#: model:ir.model.fields,field_description:account_followup.field_account_followup_manual_reminder__join_invoices
msgid "Attach Invoices"
msgstr "Adjuntar facturas "

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_manual_reminder__attachment_ids
msgid "Attachment"
msgstr "Archivo adjunto"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__auto_execute
#: model:ir.model.fields.selection,name:account_followup.selection__res_partner__followup_reminder_type__automatic
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_line_filter
msgid "Automatic"
msgstr "Automático"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_manual_reminder__body_has_template_value
msgid "Body content is the same as the template"
msgstr "El contenido del cuerpo es igual al de la plantilla"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_manual_reminder__can_edit_body
msgid "Can Edit Body"
msgstr "Puede editar el cuerpo"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.manual_reminder_view_form
msgid "Cancel"
msgstr "Cancelar"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.missing_information_view_form
msgid "Close"
msgstr "Cerrar"

#. module: account_followup
#. odoo-python
#: code:addons/account_followup/models/account_followup_report.py:0
msgid "Communication"
msgstr "Comunicación"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__company_id
msgid "Company"
msgstr "Empresa"

#. module: account_followup
#: model:ir.model,name:account_followup.model_res_partner
msgid "Contact"
msgstr "Contacto"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.manual_reminder_view_form
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_form
msgid "Content Template"
msgstr "Plantilla de contenido"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_manual_reminder__body
msgid "Contents"
msgstr "Contenidos"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__create_uid
#: model:ir.model.fields,field_description:account_followup.field_account_followup_manual_reminder__create_uid
#: model:ir.model.fields,field_description:account_followup.field_account_followup_missing_information_wizard__create_uid
msgid "Created by"
msgstr "Creado por"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__create_date
#: model:ir.model.fields,field_description:account_followup.field_account_followup_manual_reminder__create_date
#: model:ir.model.fields,field_description:account_followup.field_account_followup_missing_information_wizard__create_date
msgid "Created on"
msgstr "Creado el"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.followup_filter_info_template
msgid "Customer ref:"
msgstr "Referencia del cliente:"

#. module: account_followup
#: model:ir.actions.client,name:account_followup.action_account_followup
msgid "Customers Statement"
msgstr "Declaración del cliente"

#. module: account_followup
#. odoo-python
#: code:addons/account_followup/models/account_followup_report.py:0
msgid "Date"
msgstr "Fecha"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.followup_filter_info_template
msgid "Date:"
msgstr "Fecha:"

#. module: account_followup
#: model:ir.model.constraint,message:account_followup.constraint_account_followup_followup_line_days_uniq
msgid "Days of the follow-up lines must be different per company"
msgstr ""
"Los días de las líneas de seguimiento deben ser diferentes dependiendo la "
"empresa"

#. module: account_followup
#. odoo-python
#: code:addons/account_followup/models/account_followup_report.py:0
msgid ""
"Dear %s,\n"
"\n"
"\n"
"Exception made if there was a mistake of ours, it seems that the following amount stays unpaid. Please, take appropriate measures in order to carry out this payment in the next 8 days.\n"
"\n"
"Would your payment have been carried out after this mail was sent, please ignore this message. Do not hesitate to contact our accounting department.\n"
"\n"
"Best Regards,\n"
"\n"
msgstr ""
"Apreciable %s,\n"
"\n"
"\n"
"A no ser que se trate de un error nuestro, parece que aún no recibimos el pago por el importe que aparece a continuación. Tome las medidas necesarias para realizar este pago dentro de los siguientes 8 días.\n"
"\n"
"Si realizó el pago después de que recibió este correo, ignore este mensaje. No dude en contactar a nuestro equipo de contabilidad.\n"
"\n"
"Saludos cordiales,\n"
"\n"

#. module: account_followup
#. odoo-python
#: code:addons/account_followup/models/account_followup_report.py:0
msgid ""
"Dear client, we kindly remind you that you still have unpaid invoices. "
"Please check them and take appropriate action. %s"
msgstr ""
"Apreciable cliente, le recordamos que todavía tiene facturas sin pagar. "
"Revíselas y realice las acciones necesarias. %s"

#. module: account_followup
#: model_terms:ir.actions.act_window,help:account_followup.action_account_followup_line_definition_form
msgid "Define follow-up levels and their related actions"
msgstr "Defina los niveles de seguimiento y sus respectivas acciones."

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.followup_filter_info_template
msgid "Demo Ref"
msgstr "Referencia de la demostración"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__name
msgid "Description"
msgstr "Descripción"

#. module: account_followup
#: model:ir.model.fields,help:account_followup.field_account_followup_followup_line__activity_default_responsible_type
msgid ""
"Determine who will be assigned to the activity:\n"
"- Follow-up Responsible (default)\n"
"- Salesperson: Sales Person defined on the invoice\n"
"- Account Manager: Sales Person defined on the customer"
msgstr ""
"Determine a quién se le asignará la actividad:\n"
"- Responsable del seguimiento (predeterminado)\n"
"- Vendedor: vendedor definido en la factura\n"
"- Gerente de cuenta: vendedor definido en el cliente"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__display_name
#: model:ir.model.fields,field_description:account_followup.field_account_followup_manual_reminder__display_name
#: model:ir.model.fields,field_description:account_followup.field_account_followup_missing_information_wizard__display_name
msgid "Display Name"
msgstr "Nombre en pantalla"

#. module: account_followup
#. odoo-python
#: code:addons/account_followup/models/account_followup_report.py:0
msgid "Due Date"
msgstr "Fecha límite"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__delay
msgid "Due Days"
msgstr "Días de vencimiento"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_manual_reminder__email
msgid "Email"
msgstr "Correo electrónico"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.manual_reminder_view_form
msgid "Email Recipients"
msgstr "Destinatarios del correo electrónico"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.manual_reminder_view_form
msgid "Email Subject"
msgstr "Asunto del correo"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_manual_reminder__email_recipient_ids
msgid "Extra Recipients"
msgstr "Destinatarios adicionales"

#. module: account_followup
#. odoo-python
#: code:addons/account_followup/models/account_followup_report.py:0
msgid "Follow-up %(partner)s - %(date)s.pdf"
msgstr "Seguimiento de %(partner)s - %(date)s.pdf"

#. module: account_followup
#: model:ir.model.fields.selection,name:account_followup.selection__res_partner__type__followup
msgid "Follow-up Address"
msgstr "Dirección de seguimiento"

#. module: account_followup
#: model:ir.model,name:account_followup.model_account_followup_followup_line
msgid "Follow-up Criteria"
msgstr "Criterios de seguimiento"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_move_line__followup_line_id
#: model:ir.model.fields,field_description:account_followup.field_res_partner__followup_line_id
#: model:ir.model.fields,field_description:account_followup.field_res_users__followup_line_id
msgid "Follow-up Level"
msgstr "Nivel de seguimiento"

#. module: account_followup
#: model:ir.actions.act_window,name:account_followup.action_account_followup_line_definition_form
#: model:ir.ui.menu,name:account_followup.account_followup_menu
msgid "Follow-up Levels"
msgstr "Niveles de seguimiento"

#. module: account_followup
#: model:ir.model,name:account_followup.model_account_followup_report
msgid "Follow-up Report"
msgstr "Reporte de seguimiento"

#. module: account_followup
#: model:ir.model.fields.selection,name:account_followup.selection__account_followup_followup_line__activity_default_responsible_type__followup
msgid "Follow-up Responsible"
msgstr "Responsable del seguimiento"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_res_partner__followup_status
#: model:ir.model.fields,field_description:account_followup.field_res_users__followup_status
msgid "Follow-up Status"
msgstr "Estado del seguimiento"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_form
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_tree
msgid "Follow-up Steps"
msgstr "Pasos del seguimiento"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.report_followup_print_all
msgid "Follow-up details"
msgstr "Detalles de seguimiento"

#. module: account_followup
#. odoo-python
#: code:addons/account_followup/models/account_followup_report.py:0
msgid "Follow-up letter generated"
msgstr "Carta de seguimiento generada "

#. module: account_followup
#: model:ir.model,name:account_followup.model_account_followup_missing_information_wizard
msgid "Followup missing information wizard"
msgstr "Asistente de información de seguimiento faltante"

#. module: account_followup
#: model_terms:ir.actions.act_window,help:account_followup.action_account_followup_line_definition_form
msgid ""
"For each step, specify the actions to be taken and delay in days. It is\n"
"            possible to use print and e-mail templates to send specific messages to\n"
"            the customer."
msgstr ""
"En todos los pasos debe especificar las acciones a tomar y el retraso en días. Puede\n"
"            usar plantillas para impresión y para correo electrónico para enviar mensajes específicos\n"
"            al cliente."

#. module: account_followup
#: model:mail.template,name:account_followup.demo_followup_email_template_4
msgid "Fourth reminder followup"
msgstr "Cuarto recordatorio"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_res_partner__has_moves
#: model:ir.model.fields,field_description:account_followup.field_res_users__has_moves
msgid "Has Moves"
msgstr "Tiene movimientos "

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__id
#: model:ir.model.fields,field_description:account_followup.field_account_followup_manual_reminder__id
#: model:ir.model.fields,field_description:account_followup.field_account_followup_missing_information_wizard__id
msgid "ID"
msgstr "ID"

#. module: account_followup
#: model:ir.model.fields,help:account_followup.field_account_followup_followup_line__additional_follower_ids
msgid ""
"If set, those users will be added as followers on the partner and receive "
"notifications about any email reply made by the partner on the reminder "
"email."
msgstr ""
"Si se configura, los usuarios se agregarán como seguidores en el contacto y "
"recibirán notificaciones sobre cualquier respuesta que el contacto haya "
"enviado en el correo como recordatorio de correo."

#. module: account_followup
#: model:ir.model.fields.selection,name:account_followup.selection__res_partner__followup_status__in_need_of_action
msgid "In need of action"
msgstr "Se necesita realizar una acción"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.view_partner_property_form_followup
msgid "Invoice Follow-ups"
msgstr "Seguimiento de facturas"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.template_followup_report
msgid "Invoices Analysis"
msgstr "Análisis de facturas"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_manual_reminder__is_mail_template_editor
msgid "Is Editor"
msgstr "Es un editor"

#. module: account_followup
#: model:ir.model,name:account_followup.model_account_move_line
msgid "Journal Item"
msgstr "Apunte contable"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.manual_reminder_view_form
msgid "Kind reminder!"
msgstr "¡Pequeño recordatorio!"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_manual_reminder__lang
msgid "Language"
msgstr "Idioma"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__write_uid
#: model:ir.model.fields,field_description:account_followup.field_account_followup_manual_reminder__write_uid
#: model:ir.model.fields,field_description:account_followup.field_account_followup_missing_information_wizard__write_uid
msgid "Last Updated by"
msgstr "Última actualización por"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__write_date
#: model:ir.model.fields,field_description:account_followup.field_account_followup_manual_reminder__write_date
#: model:ir.model.fields,field_description:account_followup.field_account_followup_missing_information_wizard__write_date
msgid "Last Updated on"
msgstr "Última actualización el"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.manual_reminder_view_form
msgid "Letter/Email Content"
msgstr "Contenido de la carta o el correo"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__mail_template_id
#: model:ir.model.fields,field_description:account_followup.field_account_followup_manual_reminder__template_id
msgid "Mail Template"
msgstr "Plantilla de correo electrónico"

#. module: account_followup
#: model:ir.model.fields.selection,name:account_followup.selection__res_partner__followup_reminder_type__manual
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_line_filter
msgid "Manual"
msgstr "Manual"

#. module: account_followup
#. odoo-python
#: code:addons/account_followup/models/res_partner.py:0
#: code:addons/account_followup/wizard/followup_missing_information.py:0
msgid "Missing information"
msgstr "Información faltante"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.line_template
msgid "Name"
msgstr "Nombre"

#. module: account_followup
#. odoo-python
#: code:addons/account_followup/models/res_partner.py:0
msgid "Next Reminder Date set to %s"
msgstr "La fecha del próximo recordatorio será el %s"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_res_partner__followup_next_action_date
#: model:ir.model.fields,field_description:account_followup.field_res_users__followup_next_action_date
msgid "Next reminder"
msgstr "Siguiente recordatorio"

#. module: account_followup
#: model:ir.model.fields.selection,name:account_followup.selection__res_partner__followup_status__no_action_needed
msgid "No action needed"
msgstr "No se necesita realizar una acción"

#. module: account_followup
#: model:ir.model.fields,help:account_followup.field_res_partner__followup_next_action_date
#: model:ir.model.fields,help:account_followup.field_res_users__followup_next_action_date
msgid ""
"No follow-up action will be taken before this date.\n"
"                Sending a reminder will set this date depending on the levels configuration, and you can change it manually."
msgstr ""
"No se realizará ninguna acción de seguimiento antes de esta fecha.\n"
"                Enviar un recordatorio definirá la fecha de acuerdo a la configuración de los niveles y puede cambiarla de forma manual."

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__activity_note
msgid "Note"
msgstr "Nota"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_form
msgid "Notification"
msgstr "Notificación"

#. module: account_followup
#: model:ir.model.fields,help:account_followup.field_account_followup_manual_reminder__lang
msgid ""
"Optional translation language (ISO code) to select when sending out an "
"email. If not set, the english version will be used. This should usually be "
"a placeholder expression that provides the appropriate language, e.g. {{ "
"object.partner_id.lang }}."
msgstr ""
"Idioma de traducción opcional (código ISO) a seleccionar para el envío de "
"correos electrónicos. Si no se selecciona esta opción, se utilizará la "
"versión en inglés. Por lo general, se usa una expresión de marcador de "
"posición para indicar el idioma adecuado, por ejemplo, {{ "
"object.partner_id.lang }}."

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_form
msgid "Options"
msgstr "Opciones"

#. module: account_followup
#. odoo-python
#: code:addons/account_followup/models/account_followup_report.py:0
#: model:ir.model.fields,field_description:account_followup.field_account_move_line__invoice_origin
msgid "Origin"
msgstr "Origen"

#. module: account_followup
#. odoo-python
#: code:addons/account_followup/models/res_partner.py:0
#: model_terms:ir.ui.view,arch_db:account_followup.customer_statements_search_view
#: model_terms:ir.ui.view,arch_db:account_followup.view_partner_property_form_followup
msgid "Overdue Invoices"
msgstr "Facturas vencidas"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_manual_reminder__partner_id
msgid "Partner"
msgstr "Contacto"

#. module: account_followup
#: model:mail.template,name:account_followup.email_template_followup_1
msgid "Payment Reminder"
msgstr "Recordatorio de pago"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_manual_reminder__print
#: model_terms:ir.ui.view,arch_db:account_followup.manual_reminder_view_form
msgid "Print"
msgstr "Imprimir"

#. module: account_followup
#: model:ir.actions.report,name:account_followup.action_report_followup
msgid "Print Follow-up Letter"
msgstr "Imprimir carta de seguimiento"

#. module: account_followup
#: model:ir.actions.server,name:account_followup.action_account_reports_customer_statements_do_followup
msgid "Process Follow-ups"
msgstr "Procesar seguimiento"

#. module: account_followup
#. odoo-python
#: code:addons/account_followup/models/account_followup_report.py:0
msgid "Reference"
msgstr "Referencia"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_form
msgid "Remind"
msgstr "Recordatorio"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_res_partner__followup_reminder_type
#: model:ir.model.fields,field_description:account_followup.field_res_users__followup_reminder_type
msgid "Reminders"
msgstr "Recordatorios"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_manual_reminder__render_model
msgid "Rendering Model"
msgstr "Modelo de visualización"

#. module: account_followup
#: model:ir.model,name:account_followup.model_ir_actions_report
msgid "Report Action"
msgstr "Reportar acción"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.customer_statements_search_view
msgid "Requires Follow-up"
msgstr "Requiere seguimiento"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__activity_default_responsible_type
#: model:ir.model.fields,field_description:account_followup.field_res_partner__followup_responsible_id
#: model:ir.model.fields,field_description:account_followup.field_res_users__followup_responsible_id
msgid "Responsible"
msgstr "Responsable"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.manual_reminder_view_form
msgid "SMS"
msgstr "SMS"

#. module: account_followup
#: model:ir.model.fields.selection,name:account_followup.selection__account_followup_followup_line__activity_default_responsible_type__salesperson
msgid "Salesperson"
msgstr "Vendedor"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__create_activity
msgid "Schedule Activity"
msgstr "Programar actividad"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_line_filter
msgid "Search Follow-up"
msgstr "Buscar seguimiento"

#. module: account_followup
#: model:mail.template,name:account_followup.demo_followup_email_template_2
msgid "Second reminder followup"
msgstr "Segundo recordatorio"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.manual_reminder_view_form
#: model_terms:ir.ui.view,arch_db:account_followup.view_partner_property_form_followup
msgid "Send"
msgstr "Enviar"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.manual_reminder_view_form
msgid "Send & Print"
msgstr "Enviar e imprimir"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__send_email
msgid "Send Email"
msgstr "Enviar correo electrónico"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__send_sms
msgid "Send SMS Message"
msgstr "Enviar mensaje SMS"

#. module: account_followup
#: model:ir.actions.act_window,name:account_followup.manual_reminder_action
msgid "Send and Print"
msgstr "Enviar e imprimir"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_manual_reminder__sms
msgid "Sms"
msgstr "SMS"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_manual_reminder__sms_body
msgid "Sms Body"
msgstr "Cuerpo del SMS"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.manual_reminder_view_form
msgid "Sms Content"
msgstr "Contenido del SMS"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__sms_template_id
#: model:ir.model.fields,field_description:account_followup.field_account_followup_manual_reminder__sms_template_id
msgid "Sms Template"
msgstr "Plantilla de SMS"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_manual_reminder__subject
msgid "Subject"
msgstr "Asunto"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_account_followup_followup_line__activity_summary
msgid "Summary"
msgstr "Resumen"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.table_header_template_followup_report
msgid "Table Header"
msgstr "Encabezado de la tabla"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.line_template
msgid "Table Value"
msgstr "Valor de la tabla"

#. module: account_followup
#: model:ir.model.fields,help:account_followup.field_account_move_line__invoice_origin
msgid "The document(s) that generated the invoice."
msgstr "Los documentos que generaron esta factura."

#. module: account_followup
#: model:ir.model.fields,help:account_followup.field_account_followup_followup_line__delay
msgid ""
"The number of days after the due date of the invoice to wait before sending "
"the reminder. Can be negative if you want to send the reminder before the "
"invoice due date."
msgstr ""
"El número de días después del vencimiento de la factura que se debe esperar "
"antes de enviar un recordatorio. Puede ser negativo si quiere enviar "
"recordatorios antes de la fecha de vencimiento."

#. module: account_followup
#: model:ir.model.fields,help:account_followup.field_res_partner__followup_responsible_id
#: model:ir.model.fields,help:account_followup.field_res_users__followup_responsible_id
msgid ""
"The responsible assigned to manual followup activities, if defined in the "
"level."
msgstr ""
"El responsable asignado a las actividades de seguimiento manual, si se "
"definen en el nivel."

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.customer_statements_tree_view
msgid "Total"
msgstr "Total"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_res_partner__total_all_due
#: model:ir.model.fields,field_description:account_followup.field_res_users__total_all_due
msgid "Total All Due"
msgstr "Todo el total por pagar"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_res_partner__total_all_overdue
#: model:ir.model.fields,field_description:account_followup.field_res_users__total_all_overdue
msgid "Total All Overdue"
msgstr "Total atrasado"

#. module: account_followup
#. odoo-python
#: code:addons/account_followup/models/account_followup_report.py:0
#: model:ir.model.fields,field_description:account_followup.field_res_partner__total_due
#: model:ir.model.fields,field_description:account_followup.field_res_users__total_due
msgid "Total Due"
msgstr "Adeudo total"

#. module: account_followup
#. odoo-python
#: code:addons/account_followup/models/account_followup_report.py:0
#: model:ir.model.fields,field_description:account_followup.field_res_partner__total_overdue
#: model:ir.model.fields,field_description:account_followup.field_res_users__total_overdue
msgid "Total Overdue"
msgstr "Total atrasado"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_res_partner__unpaid_invoice_ids
#: model:ir.model.fields,field_description:account_followup.field_res_users__unpaid_invoice_ids
msgid "Unpaid Invoice"
msgstr "Factura sin pagar"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_res_partner__unpaid_invoices_count
#: model:ir.model.fields,field_description:account_followup.field_res_users__unpaid_invoices_count
msgid "Unpaid Invoices Count"
msgstr "Número de facturas sin pagar"

#. module: account_followup
#: model:ir.model.fields,field_description:account_followup.field_res_partner__unreconciled_aml_ids
#: model:ir.model.fields,field_description:account_followup.field_res_users__unreconciled_aml_ids
msgid "Unreconciled Aml"
msgstr "Aml no conciliado"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.missing_information_view_form
msgid "View partners"
msgstr "Ver contactos"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.missing_information_view_form
msgid ""
"We were not able to process some of the automated follow-up actions due to "
"missing information on the partners."
msgstr ""
"No pudimos procesar algunas de las acciones de seguimiento automatizadas, "
"falta información en los contactos."

#. module: account_followup
#: model:ir.model.fields.selection,name:account_followup.selection__res_partner__followup_status__with_overdue_invoices
msgid "With overdue invoices"
msgstr "Con facturas vencidas"

#. module: account_followup
#: model:ir.model,name:account_followup.model_account_followup_manual_reminder
msgid "Wizard for sending manual reminders to clients"
msgstr "Asistente para enviar recordatorios manuales a los clientes"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.manual_reminder_view_form
msgid "Write your message here..."
msgstr "Escriba su mensaje aquí..."

#. module: account_followup
#. odoo-python
#: code:addons/account_followup/models/account_followup_report.py:0
msgid ""
"You are trying to send an Email, but no follow-up contact has any email "
"address set for customer '%s'"
msgstr ""
"Está intentando enviar un correo, pero ningún contacto de seguimiento tiene "
"una cuenta de correo electrónico configurada para el cliente \"%s\""

#. module: account_followup
#. odoo-python
#: code:addons/account_followup/models/account_followup_report.py:0
msgid ""
"You are trying to send an SMS, but no follow-up contact has any mobile/phone"
" number set for customer '%s'"
msgstr ""
"Está intentando enviar un SMS, pero ningún contacto de seguimiento tiene un "
"número celular configurado para el cliente \"%s\""

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_form
msgid "days after due date"
msgstr "días después de la fecha de vencimiento"

#. module: account_followup
#: model_terms:ir.ui.view,arch_db:account_followup.view_account_followup_followup_line_form
msgid "e.g. First Reminder Email"
msgstr "Por ejemplo, primer correo de recordatorio"

#. module: account_followup
#. odoo-python
#: code:addons/account_followup/models/account_followup_report.py:0
msgid "payment reminder"
msgstr "recordatorio de pago"

#. module: account_followup
#: model:mail.template,subject:account_followup.demo_followup_email_template_2
#: model:mail.template,subject:account_followup.demo_followup_email_template_4
#: model:mail.template,subject:account_followup.email_template_followup_1
msgid ""
"{{ (object.company_id or object._get_followup_responsible().company_id).name"
" }} Payment Reminder - {{ object.commercial_company_name }}"
msgstr ""
"{{ (object.company_id or object._get_followup_responsible().company_id).name"
" }} Recordatorio de pago - {{ object.commercial_company_name }}"
