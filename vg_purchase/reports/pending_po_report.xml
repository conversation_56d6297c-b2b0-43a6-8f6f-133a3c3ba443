<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Template -->
    <template id="pending_po_template">
        <t t-call="web.html_container">
            <t t-call="web.external_layout">
                <t t-foreach="docs" t-as="o">
                    <div class="page">
                        <table class="table table-borderless" style="width: 100%; margin-bottom: 20px;">
                            <tbody>
                                <tr>
                                    <!-- Purchase Order -->
                                    <td class="text-end fw-bold" style="padding-right: 8px; white-space: nowrap;">
                                        Purchase Order:
                                    </td>
                                    <td class="text-start" style="padding-right: 30px; word-break: break-word;">
                                        <t t-esc="o.name"/>
                                    </td>

                                    <!-- Supplier -->
                                    <td class="text-end fw-bold" style="padding-right: 8px; white-space: nowrap;">
                                        Supplier:
                                    </td>
                                    <td class="text-start" style="padding-right: 30px; word-break: break-word;">
                                        <t t-esc="o.partner_id.name"/>
                                    </td>

                                    <!-- Date -->
                                    <td class="text-end fw-bold" style="padding-right: 8px; white-space: nowrap;">
                                        Date:
                                    </td>
                                    <td class="text-start" style="word-break: break-word;">
                                        <t t-esc="o.date_order"/>
                                    </td>
                                </tr>
                            </tbody>
                        </table>

                        <table class="table table-bordered">
                            <thead>
                                <tr>
                                    <th class="text-center fw-bold">Line.</th>
                                    <th class="text-center fw-bold">ORG</th>
                                    <th class="text-center fw-bold text-nowrap">ITEM Code</th>
                                    <th class="text-center fw-bold text-nowrap">Description</th>
                                    <th class="text-center fw-bold text-nowrap">Pending Qty</th>
                                    <th class="text-center fw-bold text-nowrap">UOM</th>
                                    <th class="text-center fw-bold text-nowrap">UNIT PRICE</th>
                                    <th class="text-center fw-bold text-nowrap">Lot#</th>
                                    <th class="text-center fw-bold text-nowrap">Expiry Date</th>
                                </tr>
                            </thead>
                            <tbody>
                                <t t-set="index" t-value="0"/>
                                <t t-foreach="o.order_line" t-as="line">
                                    <tr>
                                        <td class="text-center">
                                            <t t-esc="index + 1"/>
                                            <t t-set="index" t-value="index + 1"/>
                                        </td>
                                        <td>
                                            <t t-esc="line.company_id.name"/>
                                        </td>
                                        <td>
                                            <t t-esc="line.product_id.default_code"/>
                                        </td>
                                        <td class="text-center">
                                            <t t-esc="line.name"/>
                                        </td>
                                        <td class="text-center">
                                            <t t-esc="line.product_qty - line.qty_received"/>
                                        </td>
                                        <td class="text-end">
                                            <t t-esc="line.product_uom_qty"/>
                                        </td>
                                        <td class="text-end">
                                            <t t-esc="line.price_unit"/>
                                        </td>
                                        <td class="text-end">
                                            <t t-esc="o.get_lot_number(line.product_id.id)"/>
                                        </td>
                                        <td class="text-end">
                                            <t t-esc="o.get_log_expiry_date(line.product_id.id)"/>
                                        </td>
                                    </tr>
                                </t>
                            </tbody>
                        </table>
                        <div style="position: relative; bottom: 0; width: 100%;margin-top:100px;">
                            <h6>INSPECTED By:</h6>
                            <table class="table table-borderless"
                                   style="width: 100%; margin-top: 10px; font-size: 12px;">
                                <tbody>
                                    <tr>
                                        <td style="padding: 5px;">
                                            <strong>Name</strong>
                                            __________________________________
                                        </td>
                                        <td class="text-center" style="padding: 5px;">
                                            <strong>Part Order Rcvd.</strong>
                                            ___________________________
                                        </td>
                                        <td class="text-end" style="padding: 5px;">
                                            <strong>Material Rcvd. on</strong>
                                            _________________
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 5px;">
                                            <strong>Signature</strong>
                                            _____________________________
                                        </td>
                                        <td class="text-center" style="padding: 5px;">
                                            <strong>Full Order Rcvd.</strong>
                                            _____________________________
                                        </td>
                                        <td class="text-end" style="padding: 5px;">
                                            <strong>Store</strong>
                                            _______________________________
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 5px;">
                                            <strong>Date</strong>
                                            __________________________________
                                        </td>
                                        <td></td>
                                        <td></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </t>
            </t>
        </t>
    </template>

<!--    &lt;!&ndash; Inherit footer of sp_custom_header_footer &ndash;&gt;-->
<!--    <template id="sp_footer_template_inherit_po" inherit_id="sp_custom_header_footer.external_layout_footer">-->
<!--        <xpath expr="//div[hasclass('text-muted')]" position="before">-->
<!--            <t t-if="o._name == 'purchase.order'">-->
<!--                <xpath expr="//div[@t-attf-class='footer o_standard_footer o_company_#{company.id}_layout']"-->
<!--                       position="before">-->

<!--                </xpath>-->


<!--            </t>-->
<!--        </xpath>-->
<!--    </template>-->


    <!-- PaperFormate -->
    <record id="pending_po_paperformate" model="report.paperformat">
        <field name="name">Pending Purchase Order Paperformate</field>
        <field name="default" eval="True"/>
        <field name="format">A4</field>
        <field name="orientation">Portrait</field>
        <field name="margin_top">35</field>
        <field name="margin_bottom">32</field>
        <field name="margin_left">1</field>
        <field name="margin_right">1</field>
        <field name="header_line" eval="False"/>
        <field name="header_spacing">35</field>
        <field name="dpi">90</field>
    </record>

    <!-- Action -->
    <record id="action_print_pending_po" model="ir.actions.report">
        <field name="name">Inspection Report</field>
        <field name="model">purchase.order</field>
        <field name="report_type">qweb-pdf</field>
        <field name="paperformat_id" ref="pending_po_paperformate"/>
        <field name="report_name">vg_purchase.pending_po_template</field>
        <field name="report_file">vg_purchase.pending_po_template</field>
        <field name="print_report_name">'Pending Purchase Order Report'</field>
        <field name="binding_model_id" ref="model_purchase_order"/>
        <field name="binding_type">report</field>
    </record>
</odoo>



