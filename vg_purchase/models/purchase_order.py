from odoo import fields, models, api, _
from odoo.exceptions import ValidationError


class PurchaseOrder(models.Model):
    _inherit = 'purchase.order'

    is_b2b_purchase = fields.Boolean(string='Is Back 2 Back PO')
    picking_type_id = fields.Many2one(
        comodel_name='stock.picking.type',
        string='Deliver To',
        required=True,
        default=False,
        domain="['|', ('warehouse_id', '=', False), ('warehouse_id.company_id', '=', company_id)]",
        help="This will determine the operation type of incoming shipment."
    )
    analytic_account_id = fields.Many2one(
        comodel_name='account.analytic.account',
        string='Analytic Account',
        compute="_compute_analytic_account",
        store=True
    )
    approval_status = fields.Selection(
        selection=[
            ('none', 'None'),
            ('new', 'To Submit'),
            ('pending', 'Submitted'),
            ('approved', 'Approved'),
            ('refused', 'Refused'),
            ('cancel', 'Cancel')
        ],
        default='none', string='Approval Status', readonly=True
    )
    branch_id = fields.Many2one(
        comodel_name='multi.branch',
        string='Branch',
        domain=lambda self: [('company_id', '=', self.env.company.id)]
    )
    approval_request_id = fields.Many2one('approval.request', string='Approval Request')
    vendor_code = fields.Char('Vendor Code', related='partner_id.customer_reference', store=True)
    request_count = fields.Integer('Request Count', compute='_compute_approval_request_count', store=True)

    @api.constrains('order_line')
    def check_duplicate_product_in_order_line(self):
        duplicate_product = set()
        for rec in self.order_line:
            if rec.product_id in duplicate_product:
                raise ValidationError(_(
                    f"The product '{rec.product_id.display_name}' is duplicated. Duplicate products are not allowed."
                ))
            duplicate_product.add(rec.product_id)

    # @api.depends('order_line.sale_order_id')
    # def _compute_is_b2b_purchase_order(self):
    #     for rec in self:
    #         if len(rec._get_sale_orders()):
    #             rec.is_b2b_purchase = True
    #         else:
    #             rec.is_b2b_purchase = False

    @api.depends('branch_id', 'order_line')
    def _compute_analytic_account(self):
        for rec in self:
            rec.analytic_account_id = False
            if rec.branch_id:
                analytic_account_id = rec.branch_id.analytic_account_id
                if not analytic_account_id:
                    raise ValidationError(_(
                        f"No analytic account is linked with the selected branch."
                        f"Please assign a analytic account to the branch:{rec.branch_id.name}"
                    ))
                rec.analytic_account_id = analytic_account_id.id
                if rec.order_line:
                    analytic_data = {
                        rec.analytic_account_id.id: 100,
                    } if rec.analytic_account_id else {}
                    rec.order_line.write({'analytic_distribution': analytic_data})

    # @api.depends('branch_id', 'order_line')
    # def _compute_analytic_account(self):
    #     for rec in self:
    #         rec.analytic_account_id = False
    #         if rec.branch_id:
    #             # Fetch the warehouse linked with the branch
    #             warehouse_id = rec.branch_id.warehouse_id
    #             if not warehouse_id:
    #                 raise ValidationError(
    #                     _(f"No warehouse is linked with the selected branch.Please assign a warehouse to the branch:{rec.branch_id.name}")
    #                 )
    #             # Fetch the picking type related to the warehouse
    #             picking_type = rec.env['stock.picking.type'].search([
    #                 ('code', '=', 'incoming'),
    #                 ('warehouse_id', '=', warehouse_id.id)
    #             ], limit=1)
    #             if not picking_type:
    #                 raise ValidationError(_("No picking type found for the warehouse '%s'.") % warehouse_id.name)
    #             rec.picking_type_id = picking_type
    #             analytic_account_id = rec.branch_id.analytic_account_id
    #             if not analytic_account_id:
    #                 raise ValidationError(
    #                     _(f"No analytic account is linked with the selected branch.Please assign a analytic account to the branch:{rec.branch_id.name}"))
    #             rec.analytic_account_id = rec.branch_id.analytic_account_id.id
    #             if rec.order_line:
    #                 analytic_data = {
    #                     rec.analytic_account_id.id: 100,
    #                 } if rec.analytic_account_id else {}
    #                 rec.order_line.write({'analytic_distribution': analytic_data})
    #         else:
    #             # Reset to default picking type if no branch is selected
    #             rec.picking_type_id = rec._default_picking_type()

    def action_create_new_approval_purchase(self):
        """ create approval request """
        if self.approval_status == 'pending':
            raise ValidationError(_(f"Approval request already pending."))
        action = self.env.ref('approvals.approval_category_action_new_request').sudo().read()
        if action:
            if self.amount_total <= 0:
                raise ValidationError(_('Purchase Order amount is 0 '))
            action = action[0]
            ctx = self.env.context.copy()
            ctx.update({
                'default_request_owner_id': self.user_id.id,
                'default_name': f"Approval for : {self.name}",
                'default_purchase_id': self.id,
            })
            action.update({
                'context': ctx,
                "domain": [('apply_on_model', '=', 'purchase')],
            })
            return action

    def action_view_approval_request(self):
        return {
            "name": _("Approval Request"),
            "type": "ir.actions.act_window",
            "res_model": "approval.request",
            "views": [[False, "list"], [False, "form"]],
            "context": {"create": False},
            "domain": [('purchase_id', '=', self.id)],
        }

    @api.depends('approval_status')
    def _compute_approval_request_count(self):
        for rec in self:
            rec.request_count = 0
            if rec.id:
                rec.request_count = self.env['approval.request'].search_count([('purchase_id', '=', rec.id)])

    def action_print_pending_purchase_order(self):
        # if not self.order_line:
        #     raise ValidationError(_("Please first add items to the order line before print pdf."))
        if self.order_line:
            received_qty = sum(self.order_line.mapped('qty_received'))
            if not received_qty:
                raise ValidationError(_(f"Please first receive the product before print pdf."))
        return self.env.ref("vg_purchase.action_print_pending_po").report_action(self)

    def get_lot_number(self, product_id):
        if product_id:
            for picking in self.picking_ids:
                for move in picking.move_ids_without_package:
                    if move.state != 'done':
                        continue
                    if move.product_id.id == product_id:
                        return '' or move.lot_ids[0].name
        else:
            return ''

    def get_log_expiry_date(self, product_id):
        if product_id:
            for picking in self.picking_ids:
                for move in picking.move_ids_without_package:
                    if move.state != 'done':
                        continue
                    if move.product_id.id == product_id:
                        return '' or move.lot_ids[0].expiration_date
        else:
            return ''
