<?xml version="1.0" encoding="UTF-8" ?>
<odoo>
    <!-- Form view  -->
    <record id="form_view_purchase_order" model="ir.ui.view">
        <field name="name">form.view.purchase.order.vg</field>
        <field name="model">purchase.order</field>
        <field name="inherit_id" ref="purchase.purchase_order_form"/>
        <field name="arch" type="xml">
            <field name="partner_ref" position="after">
                <field name="vendor_code" readonly="1"/>
                <field name="is_b2b_purchase" invisible="1"/>
            </field>
            <field name="date_order" position="before">
                <field name="branch_id" required="True"
                       readonly="approval_status in ('pending','approved','refused','cancel') or state in ['purchase','done','cancel']"
                       options="{'no_open':True,'no_create':True}"/>
            </field>
            <field name="picking_type_id" position="after">
                <field name="approval_status" widget="badge"
                       invisible="approval_status == 'none'"
                       decoration-warning="approval_status == 'pending'"
                       decoration-danger="approval_status == 'cancel'"
                       decoration-success="approval_status == 'approved'" readonly="1"/>
            </field>
            <xpath expr="//header" position="inside">
                <button name="action_create_new_approval_purchase" string="Submit For Approval"
                        invisible="approval_status in ['approved','pending'] or state in ['purchase','done','cancel']"
                        type="object" class="btn-primary"/>
            </xpath>
            <field name="currency_id" position="after">
                <field name="analytic_account_id" options="{'no_open':True,'no_create':True}"/>
            </field>
            <xpath expr="//button[@id='draft_confirm']" position="attributes">
                <attribute name="invisible">approval_status != 'approved'</attribute>
            </xpath>
            <xpath expr="//div[hasclass('oe_title')]" position="before">
                <widget name="web_ribbon" title="Approved" bg_color="text-bg-success"
                        invisible="approval_status != 'approved'"/>
            </xpath>
            <xpath expr="//div[@name='button_box']" position="inside">
                <button name="action_view_approval_request" type="object" class="oe_stat_button" icon="fa-external-link"
                        invisible="not request_count">
                    <field name="request_count" string="Request" widget="statinfo"/>
                </button>
            </xpath>
<!--            <xpath expr="//header" position="inside">-->
<!--                <button name="action_print_pending_purchase_order" string="Inspection Report"-->
<!--                        invisible="state not in ['purchase','done']" type="object"-->
<!--                        class="btn-primary"/>-->
<!--            </xpath>-->
        </field>
    </record>
</odoo>