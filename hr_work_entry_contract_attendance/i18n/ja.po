# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_work_entry_contract_attendance
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:26+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON><PERSON>, 2025\n"
"Language-Team: Japanese (https://app.transifex.com/odoo/teams/41243/ja/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ja\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: hr_work_entry_contract_attendance
#: model:ir.model.fields,help:hr_work_entry_contract_attendance.field_hr_contract__work_entry_source
msgid ""
"\n"
"        Defines the source for work entries generation\n"
"\n"
"        Working Schedule: Work entries will be generated from the working hours below.\n"
"        Attendances: Work entries will be generated from the employee's attendances. (requires Attendance app)\n"
"        Planning: Work entries will be generated from the employee's planning. (requires Planning app)\n"
"    "
msgstr ""
"\n"
"        作業エントリ生成元を定義します\n"
"\n"
"        勤務スケジュール: 作業エントリは以下の作業時間から生成されます\n"
"        勤怠管理: 作業エントリは従業員の勤怠管理から生成されます（勤怠管理アプリが必要）\n"
"        計画: 作業エントリは従業員の計画から生成されます（計画アプリが必要）\n"
"    "

#. module: hr_work_entry_contract_attendance
#: model:ir.model,name:hr_work_entry_contract_attendance.model_hr_attendance
#: model:ir.model.fields,field_description:hr_work_entry_contract_attendance.field_hr_work_entry__attendance_id
msgid "Attendance"
msgstr "出勤記録"

#. module: hr_work_entry_contract_attendance
#: model:ir.model.fields.selection,name:hr_work_entry_contract_attendance.selection__hr_contract__work_entry_source__attendance
msgid "Attendances"
msgstr "出勤記録"

#. module: hr_work_entry_contract_attendance
#: model:ir.model,name:hr_work_entry_contract_attendance.model_hr_contract
msgid "Employee Contract"
msgstr "従業員契約"

#. module: hr_work_entry_contract_attendance
#: model:ir.model,name:hr_work_entry_contract_attendance.model_hr_work_entry
msgid "HR Work Entry"
msgstr "HR作業エントリ"

#. module: hr_work_entry_contract_attendance
#: model:ir.model,name:hr_work_entry_contract_attendance.model_hr_work_entry_regeneration_wizard
msgid "Regenerate Employee Work Entries"
msgstr "従業員作業エントリを再生成"

#. module: hr_work_entry_contract_attendance
#. odoo-python
#: code:addons/hr_work_entry_contract_attendance/models/hr_attendance.py:0
msgid ""
"This attendance record is linked to a validated working entry. You can't "
"delete it."
msgstr "この出勤記録は検証済の作業エントリにリンクされています。削除することはできません。"

#. module: hr_work_entry_contract_attendance
#. odoo-python
#: code:addons/hr_work_entry_contract_attendance/models/hr_attendance.py:0
msgid ""
"This attendance record is linked to a validated working entry. You can't "
"modify it."
msgstr "この出勤記録は検証済の作業エントリにリンクされています。変更することはできません。"

#. module: hr_work_entry_contract_attendance
#: model:ir.model.fields,field_description:hr_work_entry_contract_attendance.field_hr_contract__work_entry_source
msgid "Work Entry Source"
msgstr "作業エントリ元"
