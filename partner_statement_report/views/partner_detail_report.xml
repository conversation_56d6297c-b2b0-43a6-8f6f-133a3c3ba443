<odoo>
    <data>
        <!--Paper format for sample label-->
        <record id="partner_statement_details" model="report.paperformat">
            <field name="name">Paper Format Partner Ledger Details</field>
            <field name="default" eval="True"/>
            <field name="format">A4</field>
            <field name="orientation">Portrait</field>
            <field name="margin_top">35</field>
            <field name="margin_bottom">25</field>
            <field name="margin_left">5</field>
            <field name="margin_right">5</field>
            <field name="header_line" eval="False"/>
            <field name="header_spacing">32</field>
            <field name="dpi">90</field>
        </record>

        <record id="print_action_partner_statement_details" model="ir.actions.report">
            <field name="name">Partner Ledger Detail Report</field>
            <field name="model">partner.ledger</field>
            <field name="report_type">qweb-pdf</field>
            <field name="report_name">partner_statement_report.pdf_partner_statement_details</field>
            <field name="report_file">partner_statement_report.pdf_partner_statement_details</field>
            <field name="paperformat_id" ref="partner_statement_details"/>
            <field name="binding_model_id" ref="model_partner_ledger"/>
            <field name="binding_type">report</field>
        </record>

        <template id="pdf_partner_statement_details">
            <t t-call="web.html_container">
                <t t-call="web.external_layout">
                    <style>
                        .wrap {
                        width: 910px;
                        margin-left: auto;
                        margin-right: auto;
                        }
                        .square1 {
                        height: 150px;
                        width: 200px;
                        margin-top: 5px;
                        margin-left: 50px;
                        text-align: left;
                        float:left;
                        }
                        .square2 {
                        height: 140px;
                        width: 350px;
                        background-color: transparent;
                        border: 1px transparent;
                        border-radius:10px;
                        margin-right: 300px;
                        float:right;
                        }
                    </style>
                    <t t-foreach="docs" t-as="o">
                        <t t-foreach="request.env['res.partner'].search([('id', 'in', o.partner_ids.ids)])"
                           t-as="partner">
                            <div class="wrap">
                                <br/>
                                <br/>
                                <div class="row">
                                    <div class="col-xs-3">
                                        <t t-if="o.start_date">
                                            <strong>Date from :</strong>
                                            <span t-esc="o.start_date" t-options="{'widget': 'date'}"/>
                                            <br/>
                                        </t>
                                        <t t-if="o.end_date">
                                            <strong>Date to :</strong>
                                            <span t-esc="o.end_date" t-options="{'widget': 'date'}"/>
                                            <br/>
                                        </t>
                                        <t>
                                            <strong>Partner Name:</strong>
                                            <span t-field="partner.name"/>
                                        </t>
                                    </div>
                                </div>
                                <t t-set="opening_balance" t-value="o._open_balance(partner.id, o.start_date, o.type)"/>
                                <t t-set="balance" t-value="o._open_balance(partner.id, o.start_date, o.type) + sum([round(d.debit, 2) - round(d.credit, 2)
                                                    for d in o._get_report(partner.id,  o.type, o.start_date, o.end_date)])"/>
                                <t t-set="com_total" t-value="0"/>
                                <t t-set="rb" t-value="0"/>
                                <br/>
                                <table class="table table-condensed">
                                    <thead>
                                        <tr>
                                            <th>Date</th>
                                            <th>Transaction Details</th>
                                            <th class="text-right">Amount</th>
                                            <th class="text-right">Balance</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr t-if="opening_balance > 0.0">

                                            <td>Balance forward</td>
                                            <td></td>
                                            <td></td>
                                            <td class="text-right">
                                                <t t-esc="round(opening_balance, 2)"
                                                   t-options="{'widget': 'monetary', 'display_currency': res_company.currency_id}"/>
                                            </td>
                                        </tr>
                                        <tr t-foreach="o._get_report(partner.id,  o.type, o.start_date, o.end_date)"
                                            t-as="d">
                                            <t t-set="rb" t-value="rb + d.debit - d.credit"/>
                                            <t t-set="com_total"
                                               t-value="com_total + opening_balance + d.debit - d.credit"/>
                                            <td>
                                                <span t-esc="d['date']" t-options="{'widget': 'date'}"/>
                                            </td>

                                            <td>
                                                <t t-if="d.payment_id and d.move_id.move_type == 'entry'">
                                                    <span t-esc="d.payment_id.name"/>
                                                </t>
                                                <t t-if="d.move_id.move_type in ['out_invoice', 'in_invoice', 'out_refund', 'in_refund']">
                                                    <t t-set="string_to_output" t-value="o.get_line_details(d).split('\n')" />
                                                       <t t-foreach="string_to_output" t-as="string_line">
                                                          <span t-esc="string_line"/>
                                                          <br/>
                                                       </t>
                                                </t>
                                                <t t-if="d.move_id.move_type == 'entry' and not d.payment_id">
                                                    <span t-esc="d.move_id.ref"/>
                                                </t>
                                            </td>
                                            <td class="text-right">
                                                <t t-esc="round(d.debit - d.credit, 2)"
                                                   t-options="{'widget': 'monetary', 'display_currency': res_company.currency_id}"/>
                                            </td>
                                            <td class="text-right">
                                                <t t-esc="round(com_total, 2)"
                                                   t-options="{'widget': 'monetary', 'display_currency': res_company.currency_id}"/>
                                            </td>
                                            <t t-set="com_total" t-value="com_total - opening_balance"/>
                                        </tr>
                                        <tr>

                                            <td>Final Balance</td>
                                            <td></td>
                                            <td></td>
                                            <td class="text-right">
                                                <t t-esc="round(balance, 2)"
                                                   t-options="{'widget': 'monetary', 'display_currency': res_company.currency_id}"/>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            <p style="page-break-after: always"></p>
                        </t>
                    </t>
                </t>
            </t>
        </template>
    </data>
</odoo>