# Softprime Consulting Pvt Ltd
# Copyright (C) Softprime Consulting Pvt Ltd
# All Rights Reserved
# https://softprimeconsulting.com/
from odoo import api, fields, models


class ResTenantContractWizard(models.TransientModel):
    _name = "res.tenant.contract.wizard"
    _description = "Res Tenant Contract Wizard"

    contract_id = fields.Many2one("res.tenant.contract", string="Contract", required=True)
    building_id = fields.Many2one("res.building", string="Building", required=True)
    res_floor_id = fields.Many2many("res.floor", string="Floor", domain="[('building_id', '=', building_id)]", required=True)
    res_property_id = fields.Many2many("product.template",string="Property",
        domain="[('floor_id', 'in', res_floor_id), ('is_property', '=', True), ('state', '=', 'available')]",required=True)
    start_date = fields.Date(string="Start Date", required=True)
    end_date = fields.Date(string="End Date")

    def action_to_add_properties(self):
        active_id = self.env.context.get('active_id')
        contract = self.env['res.tenant.contract'].browse(active_id)
        for floor in self.res_floor_id:
            for property in self.res_property_id.filtered(lambda p: floor.id in p.floor_id.ids):
                property.write({'state': 'occupy'})
                self.env['add.tenant.property'].create({
                    'contract_id': contract.id,
                    'res_floor_id': floor.id,
                    'res_property_id': property.id,
                    'start_date': self.start_date,
                    'end_date': self.end_date,
                })
        return {'type': 'ir.actions.act_window_close'}
