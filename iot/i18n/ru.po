# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* iot
# 
# Translators:
# <PERSON>il O<PERSON>, 2024
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-01-27 13:54+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON><PERSON>, 2025\n"
"Language-Team: Russian (https://app.transifex.com/odoo/teams/41243/ru/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ru\n"
"Plural-Forms: nplurals=4; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<12 || n%100>14) ? 1 : n%10==0 || (n%10>=5 && n%10<=9) || (n%100>=11 && n%100<=14)? 2 : 3);\n"

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.view_add_iot_box
msgid ""
".\n"
"\n"
"                    <br/><br/>\n"
"\n"
"                    <strong>Option A. Pairing Code</strong><br/>\n"
"                    1. Read the pairing code from a display or thermal printer connected to the IoT Box. You can also find it on the IoT Box Homepage<br/>\n"
"                    2. Enter the code below and click on \"Pair\".<br/>\n"
"                    3. After some time the IoT Box should appear in the list along with the connected devices.<br/>"
msgstr ""
".\n"
"\n"
"                    <br/><br/>\n"
"\n"
"                    <strong>Вариант A. Код сопряжения</strong><br/>\n"
"                    1. Считайте код сопряжения с дисплея или термопринтера, подключённого к IoT Box. Вы также можете найти его на главной странице IoT Box.<br/>\n"
"                    2. Введите код ниже и нажмите «Сопряжение».<br/>\n"
"                    3. Через некоторое время IoT Box должен появиться в списке вместе с подключёнными устройствами.<br/>"

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.view_add_iot_box
msgid ""
"0. Power on the IoT Box and connect it to the internet through ethernet"
msgstr "0. Включите IoT Box и подключите его к интернету через Ethernet."

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.view_add_iot_box
msgid ""
"<br/>\n"
"                    1. Copy the token below<br/>\n"
"                    2. While on the same network as the IoT Box open a web browser and go to the IoT Box ip address<br/>\n"
"                    3. In the \"Server\" section click on \"Configure\" button<br/>\n"
"                    4. Paste the token in the \"Server token\" section and click on \"Connect\" button<br/>\n"
"                    5. After some time the IoT Box should appear in the list along with the connected devices<br/>"
msgstr ""
"<br/>\n"
"                    1. Скопируйте токен ниже.<br/>\n"
"                    2. Находясь в той же сети, что и IoT Box, откройте веб-браузер и перейдите по IP-адресу IoT Box.<br/>\n"
"                    3. В разделе «Сервер» нажмите кнопку «Настроить».<br/>\n"
"                    4. Вставьте токен в поле «Токен сервера» и нажмите кнопку «Подключиться».<br/>\n"
"                    5. Через некоторое время IoT Box должен появиться в списке вместе с подключёнными устройствами. <br/>"

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.iot_device_view_kanban
msgid "<span class=\"badge text-bg-secondary\">Disconnected</span>"
msgstr "<span class=\"badge text-bg-secondary\">Отключено</span>"

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.iot_device_view_kanban
msgid "<span class=\"badge text-bg-success\">Connected</span>"
msgstr "<span class=\"badge text-bg-success\">Подключено</span>"

#. module: iot
#: model:ir.model,name:iot.model_add_iot_box
msgid "Add IoT Box wizard"
msgstr "Мастер добавления IoT Box"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/view_widgets/iot_restart_odoo.js:0
msgid "Are you sure you want to restart Odoo on the IoT box?"
msgstr "Вы уверены, что хотите перезапустить Odoo на коробке IoT?"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_box__drivers_auto_update
msgid "Automatic drivers update"
msgstr "Автоматическое обновление драйверов"

#. module: iot
#: model:ir.model.fields,help:iot.field_iot_box__drivers_auto_update
msgid "Automatically update drivers when the IoT Box boots"
msgstr "Автоматическое обновление драйверов при загрузке IoT Box"

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__type__scanner
msgid "Barcode Scanner"
msgstr "Сканер штрихкода"

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__connection__bluetooth
msgid "Bluetooth"
msgstr "Менеджер Bluetooth"

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__type__camera
msgid "Camera"
msgstr "Камера"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/iot_websocket_service.js:0
msgid "Check IoT Box connection. Try restarting if needed."
msgstr "Проверьте подключение IoT Box. Перезагрузите, если необходимо."

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/views/iot_device_form.js:0
msgid "Check if the device is still connected"
msgstr "Проверьте, подключено ли устройство"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/iot_connection_error_dialog.xml:0
msgid "Click here to open your IoT Homepage"
msgstr "Нажмите здесь, чтобы открыть домашнюю страницу IoT"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/iot_connection_error_dialog.xml:0
msgid "Click on Advanced/Show Details/Details/More information"
msgstr ""
"Нажмите кнопку Дополнительно/Показать детали/Детали/Подробная информация"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/iot_connection_error_dialog.xml:0
msgid ""
"Click on Proceed to .../Add Exception/Visit this website/Go on to the "
"webpage"
msgstr ""
"Нажмите на кнопку Приступить к .../Добавить исключение/Посетить этот веб-"
"сайт/Перейти на веб-страницу"

#. module: iot
#: model_terms:ir.actions.act_window,help:iot.iot_box_action
msgid "Click on the"
msgstr "Нажать на"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/iot_connection_error_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:iot.view_add_iot_box
msgid "Close"
msgstr "Закрыть"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/iot_connection_error_dialog.xml:0
msgid "Close this window and try again"
msgstr "Закройте это окно и попробуйте снова"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_box__company_id
#: model:ir.model.fields,field_description:iot.field_iot_device__company_id
msgid "Company"
msgstr "Компания"

#. module: iot
#: model:ir.ui.menu,name:iot.view_iot_selected_printer_local_action
msgid "Configuration"
msgstr "Конфигурация"

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.iot_box_view_kanban
#: model_terms:ir.ui.view,arch_db:iot.iot_box_view_list
msgid "Connect"
msgstr "Подключить"

#. module: iot
#: model:ir.actions.act_window,name:iot.action_add_iot_box
#: model_terms:ir.ui.view,arch_db:iot.view_add_iot_box
msgid "Connect my IoT Box"
msgstr ""

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_device__connection
#: model_terms:ir.ui.view,arch_db:iot.iot_device_search
msgid "Connection"
msgstr "Подключение"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/iot_connection_error_dialog.js:0
msgid "Connection to IoT Box failed"
msgstr "Не удалось установить соединение с IoT Box"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/views/iot_device_form.js:0
msgid "Connection to device failed"
msgstr "Не удалось установить соединение с устройством"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/iot_websocket_service.js:0
msgid "Connection to printer failed "
msgstr "Сбой подключения к принтеру"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_add_iot_box__create_uid
#: model:ir.model.fields,field_description:iot.field_iot_box__create_uid
#: model:ir.model.fields,field_description:iot.field_iot_device__create_uid
#: model:ir.model.fields,field_description:iot.field_iot_keyboard_layout__create_uid
#: model:ir.model.fields,field_description:iot.field_select_printers_wizard__create_uid
msgid "Created by"
msgstr "Создано"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_add_iot_box__create_date
#: model:ir.model.fields,field_description:iot.field_iot_box__create_date
#: model:ir.model.fields,field_description:iot.field_iot_device__create_date
#: model:ir.model.fields,field_description:iot.field_iot_keyboard_layout__create_date
#: model:ir.model.fields,field_description:iot.field_select_printers_wizard__create_date
msgid "Created on"
msgstr "Создано"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_select_printers_wizard__device_ids
#: model:ir.model.fields.selection,name:iot.selection__iot_device__type__device
msgid "Device"
msgstr "Устройство"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_box__device_count
#: model_terms:ir.ui.view,arch_db:iot.iot_box_view_kanban
msgid "Device Count"
msgstr "Счетчик устройств"

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.iot_device_search
msgid "Device type"
msgstr "Тип устройства"

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.iot_device_view_kanban
msgid "Device type is #{record.type.raw_value}"
msgstr "Тип устройства - #{record.type.raw_value}"

#. module: iot
#: model:ir.actions.act_window,name:iot.iot_device_action
#: model:ir.actions.act_window,name:iot.iot_device_action_search_iot_box
#: model:ir.model.fields,field_description:iot.field_iot_box__device_ids
#: model:ir.ui.menu,name:iot.iot_device_menu_action
#: model_terms:ir.ui.view,arch_db:iot.iot_box_view_form
msgid "Devices"
msgstr "Устройства"

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__type__display
msgid "Display"
msgstr "Показать"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_select_printers_wizard__display_device_ids
msgid "Display Device"
msgstr "Устройство отображения"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_add_iot_box__display_name
#: model:ir.model.fields,field_description:iot.field_iot_box__display_name
#: model:ir.model.fields,field_description:iot.field_iot_device__display_name
#: model:ir.model.fields,field_description:iot.field_iot_keyboard_layout__display_name
#: model:ir.model.fields,field_description:iot.field_select_printers_wizard__display_name
msgid "Display Name"
msgstr "Отображаемое имя"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_device__display_url
msgid "Display URL"
msgstr "Показать ссылку"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_box__ip
#: model:ir.model.fields,field_description:iot.field_iot_device__iot_ip
msgid "Domain Address"
msgstr "Адрес домена"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/view_widgets/iot_download_logs.xml:0
msgid "Download Logs"
msgstr "Скачать журналы"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/view_widgets/iot_download_logs.xml:0
msgid "Download logs"
msgstr "Скачать журналы"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/iot_report_action.js:0
msgid "Failed to send to printer."
msgstr ""

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/iot_connection_error_dialog.xml:0
msgid "Firefox only: Click on Confirm Security Exception"
msgstr ""
"Только для Firefox: Нажмите на кнопку Подтвердить исключение из правил "
"безопасности"

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__type__fiscal_data_module
msgid "Fiscal Data Module"
msgstr "Модуль фискальных данных"

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.iot_device_search
msgid "Group By"
msgstr "Группировать по"

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__connection__hdmi
msgid "HDMI"
msgstr ""

#. module: iot
#: model:ir.model.fields,field_description:iot.field_add_iot_box__id
#: model:ir.model.fields,field_description:iot.field_iot_box__id
#: model:ir.model.fields,field_description:iot.field_iot_device__id
#: model:ir.model.fields,field_description:iot.field_iot_keyboard_layout__id
#: model:ir.model.fields,field_description:iot.field_select_printers_wizard__id
msgid "ID"
msgstr "ID"

#. module: iot
#: model:ir.model,name:iot.model_iot_device
msgid "IOT Device"
msgstr "IOT-устройство"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_device__identifier
msgid "Identifier"
msgstr "Идентификатор"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_box__identifier
msgid "Identifier (Mac Address)"
msgstr "Идентификатор (Mac-адрес)"

#. module: iot
#: model:ir.model.fields,help:iot.field_iot_device__connected
msgid "If device is connected to the IoT Box"
msgstr "Если устройство подключено к IoT Box"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/iot_connection_error_dialog.xml:0
msgid ""
"If you are on a secure server (HTTPS) check if you accepted the certificate:"
msgstr ""
"Если вы находитесь на защищенном сервере (HTTPS), проверьте, приняли ли вы "
"сертификат:"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_box__version
msgid "Image Version"
msgstr "Имидж Версия"

#. module: iot
#: model:ir.ui.menu,name:iot.iot_menu_root
msgid "IoT"
msgstr "IoT"

#. module: iot
#: model:ir.model,name:iot.model_iot_box
#: model:ir.model.fields,field_description:iot.field_iot_device__iot_id
#: model_terms:ir.ui.view,arch_db:iot.iot_device_search
msgid "IoT Box"
msgstr "IoT Box"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_box__ip_url
msgid "IoT Box Home Page"
msgstr "Главная страница IoT Box"

#. module: iot
#: model:ir.actions.act_window,name:iot.iot_box_action
#: model:ir.ui.menu,name:iot.iot_box_menu_action
msgid "IoT Boxes"
msgstr "IoT-боксы"

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.iot_device_search
msgid "IoT Device"
msgstr "IoT-устройство"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_ir_actions_report__device_ids
msgid "IoT Devices"
msgstr "IoT-устройства"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_device__is_scanner
msgid "Is Scanner"
msgstr "Сканер"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_box__is_websocket_active
msgid "Is Websocket active?"
msgstr ""

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__type__keyboard
msgid "Keyboard"
msgstr "Клавиатура"

#. module: iot
#: model:ir.model,name:iot.model_iot_keyboard_layout
#: model:ir.model.fields,field_description:iot.field_iot_device__keyboard_layout
msgid "Keyboard Layout"
msgstr "Раскладка Клавиатуры"

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__subtype__label_printer
msgid "Label Printer"
msgstr ""

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.iot_device_view_form
msgid "Last Sent Value"
msgstr "Последнее отправленное значение"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_add_iot_box__write_uid
#: model:ir.model.fields,field_description:iot.field_iot_box__write_uid
#: model:ir.model.fields,field_description:iot.field_iot_device__write_uid
#: model:ir.model.fields,field_description:iot.field_iot_keyboard_layout__write_uid
#: model:ir.model.fields,field_description:iot.field_select_printers_wizard__write_uid
msgid "Last Updated by"
msgstr "Последнее обновление"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_add_iot_box__write_date
#: model:ir.model.fields,field_description:iot.field_iot_box__write_date
#: model:ir.model.fields,field_description:iot.field_iot_device__write_date
#: model:ir.model.fields,field_description:iot.field_iot_keyboard_layout__write_date
#: model:ir.model.fields,field_description:iot.field_select_printers_wizard__write_date
msgid "Last Updated on"
msgstr "Последнее обновление"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/view_widgets/iot_restart_odoo.js:0
msgid "Last restarting process hasn't finished yet"
msgstr "Последний процесс перезапуска еще не завершен"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_keyboard_layout__layout
msgid "Layout"
msgstr "Макет"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_device__manual_measurement
msgid "Manual Measurement"
msgstr "Ручное измерение"

#. module: iot
#: model:ir.model.fields,help:iot.field_iot_device__manual_measurement
msgid "Manually read the measurement from the device"
msgstr "Ручное считывание результатов измерений с устройства"

#. module: iot
#: model:ir.model.fields,help:iot.field_iot_device__is_scanner
msgid "Manually switch the device type between keyboard and scanner"
msgstr "Ручное переключение типа устройства между клавиатурой и сканером"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_device__manufacturer
msgid "Manufacturer"
msgstr "Производитель"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_box__name
#: model:ir.model.fields,field_description:iot.field_iot_device__name
#: model:ir.model.fields,field_description:iot.field_iot_keyboard_layout__name
msgid "Name"
msgstr "Имя"

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__connection__network
msgid "Network"
msgstr "Сеть"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/views/add_iot_box_form_controller.js:0
msgid "New IoT Box connected!"
msgstr ""

#. module: iot
#: model_terms:ir.actions.act_window,help:iot.iot_box_action
msgid "No IoT Box found!"
msgstr "IoT Box не найден!"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/views/add_iot_box_form_controller.js:0
msgid "No new IoT Box found."
msgstr ""

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/select_printer_wizard.js:0
msgid "No printer selected"
msgstr "Принтер не выбран"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/client_action/delete_local_storage.xml:0
msgid "No report to display"
msgstr "Нет отчета для показа"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/iot_connection_error_dialog.xml:0
msgid "Odoo cannot reach the IoT Box."
msgstr "Odoo не может связаться с IoT Box."

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__subtype__office_printer
msgid "Office Printer"
msgstr ""

#. module: iot
#. odoo-python
#: code:addons/iot/models/ir_actions_report.py:0
msgid ""
"One of the printer used to print document have been removed. Please retry "
"the operation to choose new printers to print."
msgstr ""
"Один из принтеров, используемых для печати документов, был удален. "
"Пожалуйста, повторите операцию, чтобы выбрать новые принтеры для печати."

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.view_add_iot_box
msgid "Option B. Connection Token"
msgstr "Вариант Б. Токен подключения"

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.view_add_iot_box
msgid "Pair"
msgstr "Пара"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_add_iot_box__pairing_code
msgid "Pairing Code"
msgstr "Код сопряжения"

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__type__payment
msgid "Payment Terminal"
msgstr "Платежный терминал"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/iot_connection_error_dialog.xml:0
msgid ""
"Please accept the certificate of your IoT Box (procedure depends on your "
"browser):"
msgstr "Примите сертификат вашего IoT Box (процедура зависит от браузера):"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/iot_connection_error_dialog.xml:0
msgid "Please check if the IoT Box is still connected."
msgstr "Проверьте, подключен ли IoT Box."

#. module: iot
#. odoo-python
#: code:addons/iot/wizard/add_iot_box.py:0
msgid "Please enter a pairing code."
msgstr ""

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/view_widgets/iot_restart_odoo.js:0
msgid "Please wait"
msgstr "Подождите"

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.view_select_printers_wizard
msgid "Print"
msgstr "Печать"

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__type__printer
msgid "Printer"
msgstr "Принтер"

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.iot_device_view_form
msgid "Printer Reports"
msgstr "Отчеты принтера"

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.view_select_printers_wizard
msgid "Printers"
msgstr "Принтеры"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/iot_websocket_service.js:0
msgid "Printing operation completed on printer %s"
msgstr "Печать завершена на принтере %s"

#. module: iot
#: model:ir.model,name:iot.model_publisher_warranty_contract
msgid "Publisher Warranty Contract For IoT Box"
msgstr "Издательский гарантийный контракт для IoT Box"

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__subtype__receipt_printer
msgid "Receipt Printer"
msgstr "Принтер чеков"

#. module: iot
#: model:ir.model,name:iot.model_ir_actions_report
msgid "Report Action"
msgstr "Отчет о действиях"

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.act_report_xml_view_tree_iot
msgid "Report xml"
msgstr "Отчет xml"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_device__report_ids
#: model:ir.ui.menu,name:iot.iot_settings_menu_action
msgid "Reports"
msgstr "Отчеты"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/client_action/delete_local_storage.xml:0
msgid "Reports with linked printers"
msgstr ""

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/client_action/delete_local_storage.xml:0
#: model:ir.ui.menu,name:iot.iot_clear_selected_devices
msgid "Reset Linked Printers"
msgstr ""

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/view_widgets/iot_restart_odoo.js:0
msgid "Restart Failed"
msgstr "Перезапуск не удался"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/view_widgets/iot_restart_odoo.js:0
msgid "Restart finished"
msgstr "Перезапуск завершен"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/view_widgets/iot_restart_odoo.js:0
msgid "Restarting"
msgstr "Перезагрузка"

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.view_select_printers_wizard
msgid "Sales Details"
msgstr "Детали продаж"

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__type__scale
msgid "Scale"
msgstr "Масштаб"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/select_printer_wizard.js:0
msgid "Select at least one printer"
msgstr "Выберите хотя бы один принтер"

#. module: iot
#: model:ir.model,name:iot.model_select_printers_wizard
msgid "Selection of printers"
msgstr "Выбор принтеров"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/iot_report_action.js:0
#: code:addons/iot/static/src/iot_websocket_service.js:0
msgid "Sending to printer %s..."
msgstr "Отправка на принтер%s ..."

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__connection__serial
msgid "Serial"
msgstr "Серийный номер"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_device__connected
msgid "Status"
msgstr "Статус"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_device__subtype
msgid "Subtype"
msgstr "Sottotipo"

#. module: iot
#: model:ir.model.fields,help:iot.field_iot_device__subtype
msgid "Subtype of device."
msgstr ""

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/view_widgets/iot_restart_odoo.js:0
msgid "Success"
msgstr "Успех"

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.iot_box_view_form
msgid "Technical Information"
msgstr "Техническая Информация"

#. module: iot
#: model:ir.model,name:iot.model_iot_channel
msgid "The Websocket Iot Channel"
msgstr "Websocket Iot Channel"

#. module: iot
#. odoo-python
#: code:addons/iot/wizard/add_iot_box.py:0
msgid ""
"The pairing code you provided was not found in our system. Please check that"
" you entered it correctly."
msgstr ""
"Указанный вами код сопряжения не найден в нашей системе. Пожалуйста, "
"проверьте, правильно ли вы его ввели."

#. module: iot
#: model_terms:ir.actions.act_window,help:iot.iot_device_action_search_iot_box
msgid "There is no device connected to this IoT Box"
msgstr "К этому IoT Box не подключено ни одно устройство"

#. module: iot
#: model_terms:ir.actions.act_window,help:iot.iot_device_action
msgid "There is no device connected to your IoT Boxes"
msgstr "К вашим IoT Boxes не подключено ни одно устройство"

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/view_widgets/iot_restart_odoo.js:0
msgid "Timed out"
msgstr "Время истекло"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_add_iot_box__token
msgid "Token"
msgstr "Токен"

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_device__type
msgid "Type"
msgstr "Тип"

#. module: iot
#: model:ir.model.fields,help:iot.field_iot_device__connection
msgid "Type of connection."
msgstr "Тип соединения."

#. module: iot
#: model:ir.model.fields,help:iot.field_iot_device__type
msgid "Type of device."
msgstr "Тип устройства."

#. module: iot
#: model:ir.model.fields,help:iot.field_iot_device__display_url
msgid ""
"URL of the page that will be displayed by the device, leave empty to use the"
" customer facing display of the POS."
msgstr ""
"URL-адрес страницы, которая будет отображаться устройством; оставьте пустым,"
" чтобы использовать дисплей POS, обращенный к клиенту."

#. module: iot
#: model:ir.model.fields.selection,name:iot.selection__iot_device__connection__direct
msgid "USB"
msgstr "USB"

#. module: iot
#. odoo-python
#: code:addons/iot/wizard/add_iot_box.py:0
msgid "Using Pairing Code to connect..."
msgstr ""

#. module: iot
#: model:ir.model.fields,field_description:iot.field_iot_keyboard_layout__variant
msgid "Variant"
msgstr "Вариант"

#. module: iot
#. odoo-python
#: code:addons/iot/wizard/add_iot_box.py:0
msgid "We had troubles pairing your IoT Box. Please try again later."
msgstr ""
"У нас возникли проблемы с сопряжением вашего IoT Box. Пожалуйста, повторите "
"попытку позже."

#. module: iot
#. odoo-javascript
#: code:addons/iot/static/src/views/add_iot_box_form_controller.js:0
msgid "We're looking for your IoT Box"
msgstr ""

#. module: iot
#: model:ir.model.fields,help:iot.field_ir_actions_report__device_ids
msgid ""
"When setting a device here, the report will be printed through this device "
"on the IoT Box"
msgstr ""
"Если задать здесь устройство, отчет будет печататься через это устройство на"
" IoT Box"

#. module: iot
#: model_terms:ir.actions.act_window,help:iot.iot_box_action
msgid "connect"
msgstr "Подключить"

#. module: iot
#: model_terms:ir.ui.view,arch_db:iot.view_add_iot_box
msgid "or wifi"
msgstr "или Wi-Fi"

#. module: iot
#: model:ir.actions.client,name:iot.action_iot_delete_linked_devices_menu
msgid "reset.linked.printers"
msgstr ""

#. module: iot
#: model_terms:ir.actions.act_window,help:iot.iot_box_action
msgid "to add an IoT Box."
msgstr "чтобы добавить IoT Box."

#. module: iot
#: model:ir.model,name:iot.model_ir_websocket
msgid "websocket message handling"
msgstr "обработка сообщений в сокетах"
