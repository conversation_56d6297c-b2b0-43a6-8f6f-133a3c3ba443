.o_web_studio_kanban_view_editor:not(.o_web_studio_kanban_view_editor_legacy) {
        $-web-studio-kanban-edition-gap: 6px;

        .o_kanban_record {
            transition: padding .3s;

            &.o_kanban_demo {
                background: $o-web-studio-bg-light;
                aspect-ratio: 2;
            }
            &:not(.o_kanban_demo) {
                z-index: 1;
            }
            &.o_kanban_ghost {
                opacity: 0;
            }

            // it make no sense to add elements if this class is applied
            aside.o_kanban_aside_full .o_web_studio_hook {
                display: none !important;
            }

            main > div.o-web-studio-editor--element-clickable {
                margin: 0;
                max-width: 100%;
            }

            // make space to show aside/menu hooks
            &:has(>.o_web_studio_hook.o_web_studio_hook_visible[data-position=before]) {
                padding-left: 60px;
            }
            &:has(>.o_web_studio_hook.o_web_studio_hook_visible[data-position=after]) {
                padding-right: 60px;
            }

            // Always show groups, even with empty content
            .o_kanban_card_group {
                &:empty,
                > *:empty,
                &:not(:has( > *:not(:empty))) {
                    display: flex;
                }
            }

            // Always display the dropdown in kanban editor
            .o_dropdown_kanban {
                visibility: visible;
            }

            .o_web_studio_show_invisible {
                // Because transform: scale(1.5) is applied, the context of the layout is changed. Then the
                // background-attachment: fixed property cannot be applied, and the strip makes a poor readibility.
                // We define a custom lighter version of the strip background
                background-image: repeating-linear-gradient(
                    45deg,
                    #FFFFFF20,
                    #FFFFFF20 5px,
                    #00000010 5px,
                    #00000010 10px
                );
            }

            .o_inner_section, .o_inner_aside {
                padding: $-web-studio-kanban-edition-gap !important;
                border-collapse: separate;
                border: $-web-studio-kanban-edition-gap*0.5 solid rgba($o-brand-primary, 0.1);
            }

            .o-web-studio-editor--element-clickable {
                &:has(.o-web-studio-editor--element-clickable) {
                    padding: $-web-studio-kanban-edition-gap*0.5;
                }
            }

            .o_web_studio_hook {
                margin: 1px;
                border: 0px dashed transparent;
                transition: all .2s;

                &.o_web_studio_hook_visible {
                    padding: 6px;
                    background: rgba($o-brand-secondary, 0.1);
                    z-index: 100;

                    &[data-structures="aside"], &[data-type="t"] {
                        width: 50px !important;
                    }

                    &[data-type="ribbon"] {
                        width: 100% !important;
                    }
                }
                &.o_web_studio_nearest_hook {
                    cursor: pointer;
                    background-color: rgba($o-brand-primary, 0.02);
                    border: 1px dashed rgba($o-brand-primary, 0.5);
                    padding: 18px !important;

                    &[data-type="ribbon"] div {
                        opacity: 1 !important;
                    }
                }
            }
        }
}
