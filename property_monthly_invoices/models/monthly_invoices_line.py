# Softprime Consulting Pvt Ltd
# Copyright (C) Softprime Consulting Pvt Ltd
# All Rights Reserved
# https://softprimeconsulting.com/
from odoo import fields, models, api

PAYMENT_STATE_SELECTION = [
    ("not_paid", "Not Paid"),
    ("in_payment", "In Payment"),
    ("paid", "Paid"),
    ("partial", "Partially Paid"),
    ("reversed", "Reversed"),
    ("invoicing_legacy", "Invoicing App Legacy"),
]
STATE_SELECTION = [("draft", "Draft"), ("posted", "Posted"), ("cancel", "Cancel")]


class MonthlyInvoiceLine(models.Model):
    _name = "monthly.invoice.line"
    _description = "Monthly Invoice Line"

    invoice_id = fields.Many2one("monthly.invoice", string="Monthly Invoice", required=True)
    account_move_id = fields.Many2one("account.move", string="Invoice", required=True)
    name = fields.Char(string="Reference", required=True)
    invoice_date = fields.Date(string="Invoice Date", required=True)
    invoice_date_due = fields.Date(string="Invoice Date Due", required=True)
    amount_untaxed_signed = fields.Monetary(string="Tax Excluded")
    amount_total_signed = fields.Monetary(string="Total")
    amount_total_in_currency_signed = fields.Monetary(string="Total in Currency")
    payment_state = fields.Selection(selection=PAYMENT_STATE_SELECTION, string="Payment Status")
    label = fields.Char(string="Description", required=True)
    state = fields.Selection(selection=STATE_SELECTION, string="Status")
    currency_id = fields.Many2one("res.currency", string="Currency", related="invoice_id.currency_id", store=True)
    product_id = fields.Many2one("product.product", string="Product")
    quantity = fields.Float(string="Quantity")
    price_unit = fields.Monetary(string="Price")
    price_subtotal = fields.Monetary(string="Amount")
    serial_number = fields.Integer(string="S.NO", compute="_compute_serial_number", store=True)
    company_id = fields.Many2one('res.company', string='Company', required=True, default=lambda self: self.env.company)

    @api.depends("invoice_id", "invoice_id.invoice_lines")
    def _compute_serial_number(self):
        for record in self.mapped("invoice_id"):
            for index, line in enumerate(
                    sorted(record.invoice_lines, key=lambda l: l.id if isinstance(l.id, int) else float('inf')),
                    start=1):
                line.serial_number = index

    @api.model
    def get_payment_state_display_name(self, value):
        """Return the display name of the payment_state field"""
        selection = dict(self.fields_get(allfields=['payment_state'])['payment_state']['selection'])
        return selection.get(value, value)
