# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* social_youtube
# 
# Translators:
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:28+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Arabic (https://app.transifex.com/odoo/teams/41243/ar/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ar\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#. module: social_youtube
#: model:ir.model.fields,help:social_youtube.field_social_account__youtube_upload_playlist_id
msgid ""
"'Uploads' Playlist ID provided by the YouTube API, this should never be set "
"manually."
msgstr ""
"معرّف قائمة تشغيل \"الفيديوهات المحملة\" في YouTube المقدم من قِبَل الواجهة "
"البرمجية لـ YouTube. يجب ألا يتم تعيين ذلك يدوياً أبداً. "

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.youtube_preview
msgid "123 Views •"
msgstr "123 مشاهدات •"

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.social_stream_post_view_kanban
msgid "<i class=\"fa fa-thumbs-o-up me-1\" title=\"Likes\"/>"
msgstr "<i class=\"fa fa-thumbs-o-up me-1\" title=\"الإعجابات \"/>"

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.youtube_preview
msgid "<span class=\"fw-bold\">Your YouTube Channel</span>"
msgstr "<span class=\"fw-bold\">قناتك على YouTube</span>"

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.social_account_view_form
msgid ""
"<span>These are stored up to 30 days and refreshed often to provide you an accurate depiction of reality. </span>\n"
"                        <span>To delete these from Odoo, simply delete this account.</span>"
msgstr ""
"<span>يتم تخزينها حتى 30 يوم وتحديثها باستمرار لمنحك تصويراً دقيقاً للواقع. </span>\n"
"                        <span>لحذفها من أودو، قم بحذف الحساب بكل بساطة.</span> "

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.social_account_view_form
msgid "Access to your account can be revoked at any time from"
msgstr "يمكن إبطال الوصول إلى حسابك في أي وقت من "

#. module: social_youtube
#: model:ir.model.fields,help:social_youtube.field_social_account__youtube_access_token
msgid ""
"Access token provided by the YouTube API, this should never be set manually."
msgstr ""
"رمز الوصول المقدم من الواجهة البرمجية لـ YouTube. يجب ألا يتم تعيين ذلك "
"يدوياً أبداً. "

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_account_revoke_youtube__account_id
msgid "Account"
msgstr "الحساب "

#. module: social_youtube
#. odoo-python
#: code:addons/social_youtube/models/social_live_post.py:0
#: code:addons/social_youtube/models/social_stream_post.py:0
msgid "An error occurred."
msgstr "حدث خطأ ما. "

#. module: social_youtube
#. odoo-python
#: code:addons/social_youtube/controllers/main.py:0
msgid "Auth endpoint did not provide a refresh token. Please try again."
msgstr "لم تمنح نقطة نهاية المصادقة رمزاً للتحديث. يرجى المحاولة مجدداً. "

#. module: social_youtube
#. odoo-javascript
#: code:addons/social_youtube/static/src/xml/social_youtube_templates.xml:0
#: model_terms:ir.ui.view,arch_db:social_youtube.social_stream_post_view_kanban
msgid "Author Image"
msgstr "صورة الكاتب "

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.social_account_revoke_youtube_view_form
msgid "Cancel"
msgstr "إلغاء"

#. module: social_youtube
#: model:social.stream.type,name:social_youtube.stream_type_youtube_channel_videos
msgid "Channel"
msgstr "القناة"

#. module: social_youtube
#. odoo-javascript
#: code:addons/social_youtube/static/src/xml/social_youtube_templates.xml:0
msgid "Clear"
msgstr "مسح "

#. module: social_youtube
#. odoo-javascript
#: code:addons/social_youtube/static/src/xml/social_youtube_templates.xml:0
#: model_terms:ir.ui.view,arch_db:social_youtube.social_stream_post_view_kanban
msgid "Comments"
msgstr "التعليقات"

#. module: social_youtube
#. odoo-python
#: code:addons/social_youtube/models/social_stream_post.py:0
msgid ""
"Comments are marked as 'disabled' for this video. It could have been set as "
"'private'."
msgstr ""
"تم تعيين التعليقات كـ \"معطلة\" لهذا الفيديو. كان يمكن أن تكون معينة كـ "
"\"خاصة\". "

#. module: social_youtube
#: model:ir.model,name:social_youtube.model_res_config_settings
msgid "Config Settings"
msgstr "تهيئة الإعدادات "

#. module: social_youtube
#. odoo-javascript
#: code:addons/social_youtube/static/src/js/social_youtube_upload_field.js:0
msgid "Confirmation"
msgstr "التأكيد "

#. module: social_youtube
#: model:ir.model.fields,help:social_youtube.field_social_live_post__youtube_video_id
#: model:ir.model.fields,help:social_youtube.field_social_post__youtube_video_id
msgid "Contains the ID of the video as returned by the YouTube API"
msgstr "يحتوي على معرّف الفيديو كما تشير الواجهة البرمجية لـ YouTube "

#. module: social_youtube
#: model:ir.model.fields,help:social_youtube.field_social_post__youtube_video_category_id
msgid "Contains the ID of the video category as returned by the YouTube API"
msgstr "يحتوي على معرّف فئة الفيديو كما تشير الواجهة البرمجية لـ YouTube "

#. module: social_youtube
#. odoo-python
#: code:addons/social_youtube/wizard/social_account_revoke_youtube.py:0
msgid ""
"Could not revoke your account.\n"
"Error: %s"
msgstr ""
"لم نتمكن من إبطال حسابك.\n"
"الخطأ: %s "

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_account_revoke_youtube__create_uid
msgid "Created by"
msgstr "أنشئ بواسطة"

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_account_revoke_youtube__create_date
msgid "Created on"
msgstr "أنشئ في"

#. module: social_youtube
#. odoo-python
#: code:addons/social_youtube/models/social_post.py:0
msgid "Description cannot exceed 5000 characters."
msgstr "لا يمكن أن يتجاوز الوصف 5000 حرف. "

#. module: social_youtube
#. odoo-python
#: code:addons/social_youtube/models/social_post.py:0
msgid "Description should not contain > or < symbol."
msgstr "يجب ألا يحتوي الوصف على رمز > أو <. "

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_account_revoke_youtube__display_name
msgid "Display Name"
msgstr "اسم العرض "

#. module: social_youtube
#. odoo-javascript
#: code:addons/social_youtube/static/src/js/social_youtube_upload_field.js:0
msgid "Do you also want to remove the video from your YouTube account?"
msgstr "هل ترغب أيضاً في إزالة مقطع الفيديو هذا من حسابك على YouTube؟ "

#. module: social_youtube
#: model:ir.model.fields,help:social_youtube.field_social_account__youtube_token_expiration_date
msgid ""
"Expiration date of the Access Token provided by the YouTube API, this should"
" never be set manually."
msgstr ""
"تاريخ انتهاء صلاحية رمز الوصول المقدم من الواجهة البرمجية لـ YouTube. يجب "
"ألا يتم تعيين ذلك يدوياً أبداً. "

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_account__youtube_access_token
msgid "Google Access Token"
msgstr "رمز وصول Google "

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.social_account_view_form
msgid "Google Privacy Policy"
msgstr "سياسة خصوصية Google "

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_account__youtube_refresh_token
msgid "Google Refresh Token"
msgstr "رمز تحديث Google "

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_account_revoke_youtube__id
msgid "ID"
msgstr "المُعرف"

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_account_revoke_youtube__write_uid
msgid "Last Updated by"
msgstr "آخر تحديث بواسطة"

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_account_revoke_youtube__write_date
msgid "Last Updated on"
msgstr "آخر تحديث في"

#. module: social_youtube
#. odoo-javascript
#: code:addons/social_youtube/static/src/xml/social_youtube_templates.xml:0
msgid "Likes"
msgstr "الإعجابات"

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_media__media_type
msgid "Media Type"
msgstr "نوع الوسائط الاجتماعية "

#. module: social_youtube
#. odoo-javascript
#: code:addons/social_youtube/static/src/js/social_youtube_upload_field.js:0
msgid "No"
msgstr "لا"

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.res_config_settings_view_form
msgid "OAuth Client ID"
msgstr "معرّف عميل OAuth "

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.res_config_settings_view_form
msgid "OAuth Client Secret"
msgstr "سر عميل OAuth "

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.social_account_revoke_youtube_view_form
msgid ""
"Odoo will lose access to your YouTube account\n"
"                        and delete all its related data from your database."
msgstr ""
"لن يكون لأودو صلاحية الوصول إلى حساب YouTube الخاص بك\n"
"                        وسيقوم بحذف كافة بياناته ذات الصلة من قاعدة بياناتك. "

#. module: social_youtube
#: model:ir.model.fields,help:social_youtube.field_social_live_post__youtube_video_privacy
#: model:ir.model.fields,help:social_youtube.field_social_post__youtube_video_privacy
msgid "Once posted, set the video as Public/Private/Unlisted"
msgstr "بمجرد أن قد قمت بالنشر، قم بتعيين مقطع الفيديو كعام/خاص/غير مدرج "

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.social_account_view_form
msgid ""
"Our YouTube Social application uses YouTube API Services.\n"
"                        By using it, you implicitly agree to the:"
msgstr ""
"يستخدم تطبيق YouTube الاجتماعي خدمات الواجهة البرمجية لـ YouTube.\n"
"                        باستخدامه، فإنك توافق ضمنياً على: "

#. module: social_youtube
#. odoo-python
#: code:addons/social_youtube/models/social_post.py:0
#: model_terms:ir.ui.view,arch_db:social_youtube.social_post_view_form
msgid "Please select a single YouTube account at a time."
msgstr "يرجى تحديد حساب YouTube واحد في كل مرة. "

#. module: social_youtube
#: model:ir.model.fields.selection,name:social_youtube.selection__social_post__youtube_video_privacy__private
msgid "Private"
msgstr "خاص"

#. module: social_youtube
#. odoo-javascript
#: code:addons/social_youtube/static/src/js/social_youtube_upload_field.js:0
msgid "Processing..."
msgstr "جاري المعالجة... "

#. module: social_youtube
#: model:ir.model.fields.selection,name:social_youtube.selection__social_post__youtube_video_privacy__public
msgid "Public"
msgstr "عام"

#. module: social_youtube
#. odoo-python
#: code:addons/social_youtube/controllers/main.py:0
msgid "Read More about YouTube Channel"
msgstr "اقرأ المزيد عن قناة YouTube "

#. module: social_youtube
#. odoo-python
#: code:addons/social_youtube/controllers/main.py:0
msgid "Reason:"
msgstr "السبب:"

#. module: social_youtube
#: model:ir.model.fields,help:social_youtube.field_social_account__youtube_refresh_token
msgid ""
"Refresh token provided by the YouTube API, this should never be set "
"manually."
msgstr ""
"رمز التحديث المقدم من الواجهة البرمجية لـ YouTube. يجب ألا يتم تعيين ذلك "
"يدوياً أبداً. "

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.social_account_view_form
msgid "Revoke"
msgstr "إلغاء "

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.social_account_revoke_youtube_view_form
msgid "Revoke & Delete"
msgstr "إلغاء وحذف "

#. module: social_youtube
#. odoo-python
#: code:addons/social_youtube/models/social_account.py:0
msgid "Revoke Account"
msgstr "إلغاء الحساب "

#. module: social_youtube
#: model:ir.actions.act_window,name:social_youtube.social_account_revoke_youtube_action
#: model:ir.model,name:social_youtube.model_social_account_revoke_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.social_account_revoke_youtube_view_form
msgid "Revoke YouTube Account"
msgstr "إلغاء حساب يوتيوب "

#. module: social_youtube
#. odoo-python
#: code:addons/social_youtube/models/social_account.py:0
msgid "Revoking access tokens is currently limited to YouTube accounts only."
msgstr "يمكن إلغاء رموز الوصول في حسابات يوتيوب فقط. "

#. module: social_youtube
#. odoo-javascript
#: code:addons/social_youtube/static/src/xml/social_youtube_templates.xml:0
msgid "Select"
msgstr "تحديد "

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_post__youtube_accounts_other_count
msgid "Selected Other Accounts"
msgstr "الحسابات الأخرى المحددة "

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_post__youtube_accounts_count
msgid "Selected YouTube Accounts"
msgstr "حسابات YouTube المحددة "

#. module: social_youtube
#: model:ir.model.fields,help:social_youtube.field_social_post__youtube_video
msgid ""
"Simply holds the filename of the video as the video itself is uploaded "
"directly to YouTube"
msgstr ""
"يحتوي بكل بساطة على اسم ملف الفيديو، بما أن الفيديو بحد ذاته قد تم رفعه "
"مباشرة على YouTube "

#. module: social_youtube
#: model:ir.model,name:social_youtube.model_social_account
msgid "Social Account"
msgstr "الحساب الاجتماعي "

#. module: social_youtube
#: model:ir.model,name:social_youtube.model_social_live_post
msgid "Social Live Post"
msgstr "منشور اجتماعي حي "

#. module: social_youtube
#: model:ir.model,name:social_youtube.model_social_media
msgid "Social Media"
msgstr "مواقع التواصل الاجتماعي"

#. module: social_youtube
#: model:ir.model,name:social_youtube.model_social_post
msgid "Social Post"
msgstr "منشور اجتماعي "

#. module: social_youtube
#: model:ir.model,name:social_youtube.model_social_post_template
msgid "Social Post Template"
msgstr "قالب المنشور الاجتماعي "

#. module: social_youtube
#: model:ir.model,name:social_youtube.model_social_stream
msgid "Social Stream"
msgstr "الوسائط الاجتماعية "

#. module: social_youtube
#: model:ir.model,name:social_youtube.model_social_stream_post
msgid "Social Stream Post"
msgstr "منشور الوسائط الاجتماعية "

#. module: social_youtube
#. odoo-javascript
#: code:addons/social_youtube/static/src/js/social_youtube_upload_field.js:0
msgid "The selected video exceeds the maximum allowed size of %s."
msgstr "يتجاوز حجم مقطع الفيديو المحدد الحجم الأقصى المسموح به وهو %s. "

#. module: social_youtube
#. odoo-python
#: code:addons/social_youtube/models/social_media.py:0
msgid ""
"The url that this service requested returned an error. Please contact the "
"author of the app."
msgstr ""
"حدث خطأ ما في رابط url الذي تم طلبه من قِبَل هذه الخدمة. يرجى التواصل مع "
"منشئ هذا التطبيق. "

#. module: social_youtube
#. odoo-python
#: code:addons/social_youtube/models/social_live_post.py:0
msgid "The video you are trying to publish has been deleted from YouTube."
msgstr "مقطع الفيديو الذي تحاول نشره قد تم حذفه من YouTube. "

#. module: social_youtube
#. odoo-python
#: code:addons/social_youtube/controllers/main.py:0
msgid "There is no channel linked with this YouTube account."
msgstr "لا توجد قناة مرتبطة بحساب يوتيوب هذا. "

#. module: social_youtube
#. odoo-python
#: code:addons/social_youtube/models/social_post.py:0
msgid "Title cannot exceed 100 characters."
msgstr "لا يمكن أن يتجاوز العنوان 100 حرف. "

#. module: social_youtube
#. odoo-python
#: code:addons/social_youtube/models/social_post.py:0
msgid "Title should not contain > or < symbol."
msgstr "يجب ألا يحتوي العنوان على رمز > أو <. "

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.social_account_view_form
msgid ""
"To provide our application services, note that we store the following data "
"from your YouTube account:"
msgstr ""
"لتقديم خدمات تطبيقنا، يرجى العلم بأننا نقوم بتخزين البيانات التالية من حساب "
"YouTube الخاص بك: "

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_account__youtube_token_expiration_date
msgid "Token expiration date"
msgstr "تاريخ انتهاء صلاحية الرمز "

#. module: social_youtube
#. odoo-python
#: code:addons/social_youtube/controllers/main.py:0
msgid "Unauthorized. Please contact your administrator."
msgstr "غير مصرح به. يرجى التواصل مع مديرك. "

#. module: social_youtube
#. odoo-python
#: code:addons/social_youtube/wizard/social_account_revoke_youtube.py:0
msgid "Unknown"
msgstr "غير معروف"

#. module: social_youtube
#: model:ir.model.fields.selection,name:social_youtube.selection__social_post__youtube_video_privacy__unlisted
msgid "Unlisted"
msgstr "غير مدرج "

#. module: social_youtube
#. odoo-javascript
#: code:addons/social_youtube/static/src/xml/social_youtube_templates.xml:0
msgid "Upload Video"
msgstr "رفع الفيديو "

#. module: social_youtube
#. odoo-javascript
#: code:addons/social_youtube/static/src/js/social_youtube_upload_field.js:0
msgid "Upload failed. Please try again."
msgstr "فشلت عملية رفع الفيديو. يرجى المحاولة مجدداً. "

#. module: social_youtube
#. odoo-javascript
#: code:addons/social_youtube/static/src/js/social_youtube_upload_field.js:0
msgid "Uploading... %s%"
msgstr "جاري الرفع... %s% "

#. module: social_youtube
#. odoo-javascript
#: code:addons/social_youtube/static/src/js/social_youtube_upload_field.js:0
msgid "Uploading... 0%"
msgstr "جاري رفع الفيديو... 0% "

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_res_config_settings__youtube_use_own_account
msgid "Use your own YouTube Account"
msgstr "استخدم حساب YouTube الخاص بك "

#. module: social_youtube
#: model:ir.model.fields,help:social_youtube.field_social_media__media_type
msgid ""
"Used to make comparisons when we need to restrict some features to a "
"specific media ('facebook', 'x', ...)."
msgstr ""
"يستخدم لإجراء المقارنات عند الحاجة إلى تقييد بعض الخصائص لموقع تواصل اجتماعي"
" محدد ('facebook'، 'X'، ...). "

#. module: social_youtube
#. odoo-python
#: code:addons/social_youtube/models/social_post.py:0
msgid "Video"
msgstr "الفيديو"

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.social_post_view_form
msgid "Video Description"
msgstr "وصف الفيديو "

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_live_post__youtube_video_privacy
#: model:ir.model.fields,field_description:social_youtube.field_social_post__youtube_video_privacy
msgid "Video Privacy"
msgstr "خصوصية الفيديو"

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.social_post_view_form
msgid "Video Title"
msgstr "عنوان الفيديو "

#. module: social_youtube
#. odoo-javascript
#: code:addons/social_youtube/static/src/js/social_youtube_upload_field.js:0
msgid "Video Upload"
msgstr "رفع الفيديو "

#. module: social_youtube
#. odoo-python
#: code:addons/social_youtube/models/social_stream_post.py:0
msgid "Video not found. It could have been removed from Youtube."
msgstr ""
"لم يتم العثور على مقطع الفيديو. يمكن أن يكون قد تمت إزالته من YouTube. "

#. module: social_youtube
#. odoo-javascript
#: code:addons/social_youtube/static/src/xml/social_youtube_templates.xml:0
#: model_terms:ir.ui.view,arch_db:social_youtube.social_stream_post_view_kanban
msgid "Views"
msgstr "أدوات العرض"

#. module: social_youtube
#. odoo-javascript
#: code:addons/social_youtube/static/src/js/social_youtube_upload_field.js:0
msgid "Yes, delete it"
msgstr "نعم، قم بحذفه "

#. module: social_youtube
#. odoo-javascript
#: code:addons/social_youtube/static/src/js/social_youtube_upload_field.js:0
msgid "You cannot use '>' or '<' in both title and description."
msgstr "لا يمكنك استخدام أي من '>' أو '<' في العنوان أو الوصف. "

#. module: social_youtube
#. odoo-python
#: code:addons/social_youtube/models/social_media.py:0
msgid "You don't have an active subscription. Please buy one here: %s"
msgstr "لا تملك اشتراكاً سارياً. يرجى شراء واحد من هنا: %s "

#. module: social_youtube
#. odoo-python
#: code:addons/social_youtube/models/social_post.py:0
msgid "You have to upload a video when posting on YouTube."
msgstr "عليك رفع مقطع فيديو عند النشر على YouTube. "

#. module: social_youtube
#. odoo-javascript
#: code:addons/social_youtube/static/src/js/social_youtube_upload_field.js:0
msgid "You need to give your video a description."
msgstr "عليك أن تمنح مقطع الفيديو الخاص بك وصفاً. "

#. module: social_youtube
#. odoo-javascript
#: code:addons/social_youtube/static/src/js/social_youtube_upload_field.js:0
msgid "You need to give your video a title."
msgstr "عليك أن تمنح مقطع الفيديو الخاص بك عنواناً. "

#. module: social_youtube
#: model:ir.model.fields.selection,name:social_youtube.selection__social_media__media_type__youtube
#: model:social.media,name:social_youtube.social_media_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.social_post_view_form
msgid "YouTube"
msgstr "YouTube"

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_post__youtube_access_token
msgid "YouTube Access Token"
msgstr "رمز وصول YouTube "

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_post__youtube_video_category_id
msgid "YouTube Category Id"
msgstr "معرّف فئة YouTube "

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_account__youtube_channel_id
msgid "YouTube Channel ID"
msgstr "معرف قناة يوتيوب "

#. module: social_youtube
#: model:ir.model.fields,help:social_youtube.field_social_account__youtube_channel_id
msgid ""
"YouTube Channel ID provided by the YouTube API, this should never be set "
"manually."
msgstr ""
"معرّف قناة YouTube المقدم من قِبَل الواجهة البرمجية لـ YouTube. يجب ألا يتم "
"تعيين ذلك يدوياً أبداً. "

#. module: social_youtube
#. odoo-javascript
#: code:addons/social_youtube/static/src/js/stream_post_kanban_record.js:0
msgid "YouTube Comments"
msgstr "تعليقات YouTube "

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_stream_post__youtube_comments_count
msgid "YouTube Comments Count"
msgstr "عدد تعليقات YouTube "

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.res_config_settings_view_form
msgid "YouTube Developer Account"
msgstr "حساب مطور يوتيوب "

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_stream_post__youtube_dislikes_count
msgid "YouTube Dislikes"
msgstr "عدم الإعجاب في YouTube "

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_stream_post__youtube_likes_count
msgid "YouTube Likes"
msgstr "إعجابات YouTube "

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_res_config_settings__youtube_oauth_client_id
msgid "YouTube OAuth Client ID"
msgstr "معرّف عميل YouTube OAuth "

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_res_config_settings__youtube_oauth_client_secret
msgid "YouTube OAuth Client Secret"
msgstr "سر عميل YouTube OAuth "

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.social_post_view_form
msgid "YouTube Options"
msgstr "خيارات YouTube "

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.youtube_preview
msgid "YouTube Placeholder"
msgstr "عنصر نائب لـ YouTube "

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_post__youtube_preview
msgid "YouTube Preview"
msgstr "معاينة YouTube "

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.social_account_view_form
msgid "YouTube Terms of Service (ToS)"
msgstr "شروط خدمة YouTube (ToS) "

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.social_post_view_kanban
#: model_terms:ir.ui.view,arch_db:social_youtube.social_stream_post_view_kanban
msgid "YouTube Thumbnail"
msgstr "الصورة المصغرة لـ YouTube "

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_post__youtube_thumbnail_url
#: model:ir.model.fields,field_description:social_youtube.field_social_stream_post__youtube_thumbnail_url
msgid "YouTube Thumbnail Url"
msgstr "رابط URL للصورة المصغرة في يوتيوب "

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_account__youtube_upload_playlist_id
msgid "YouTube Upload Playlist ID"
msgstr "معرّف رفع قائمة تشغيل YouTube "

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_post__youtube_video
msgid "YouTube Video"
msgstr "فيديو YouTube "

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_live_post__youtube_description
#: model:ir.model.fields,field_description:social_youtube.field_social_post__youtube_description
msgid "YouTube Video Description"
msgstr "وصف فيديو YouTube "

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_stream_post__youtube_video_id
msgid "YouTube Video ID"
msgstr "معرّف فيديو YouTube "

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_live_post__youtube_video_id
#: model:ir.model.fields,field_description:social_youtube.field_social_post__youtube_video_id
msgid "YouTube Video Id"
msgstr "معرّف فيديو YouTube "

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_live_post__youtube_title
#: model:ir.model.fields,field_description:social_youtube.field_social_post__youtube_title
msgid "YouTube Video Title"
msgstr "عنوان فيديو YouTube "

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_post__youtube_video_url
msgid "YouTube Video Url"
msgstr "رابط URL لفيديو على اليوتيوب "

#. module: social_youtube
#: model:ir.model.fields,field_description:social_youtube.field_social_stream_post__youtube_views_count
msgid "YouTube Views"
msgstr "مشاهدات YouTube "

#. module: social_youtube
#. odoo-python
#: code:addons/social_youtube/controllers/main.py:0
msgid "YouTube did not provide a valid access token or it may have expired."
msgstr "لم يقدم YouTube رمز وصول صالح أو ربما قد انتهت صلاحيته. "

#. module: social_youtube
#. odoo-python
#: code:addons/social_youtube/controllers/main.py:0
msgid "YouTube did not provide a valid authorization code."
msgstr "لم يقدم YouTube رمز تفويض صالح. "

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.social_account_view_form
msgid "Your channel name and picture"
msgstr "اسم وصورة قناتك "

#. module: social_youtube
#. odoo-javascript
#: code:addons/social_youtube/static/src/js/social_youtube_upload_field.js:0
msgid "Your description cannot exceed 5000 characters."
msgstr "لا يمكن أن يتجاوز وصفك 5000 حرف. "

#. module: social_youtube
#. odoo-javascript
#: code:addons/social_youtube/static/src/js/social_youtube_upload_field.js:0
msgid "Your title cannot exceed 100 characters."
msgstr "لا يمكن أن يتجاوز عنوانك 100 حرف. "

#. module: social_youtube
#. odoo-python
#: code:addons/social_youtube/models/social_live_post.py:0
msgid "Your video is missing a correct title or description."
msgstr "ينقص مقطع الفيديو عنوان صحيح أو وصف. "

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.social_account_view_form
msgid ""
"Your videos metadata including title and view counts (but never the video "
"itself)"
msgstr ""
"البيانات الوصفية لمقاطع الفيديو الخاصة بك، شاملة العناوين وعدد المشاهدات "
"(ولكن ليس الفيديو نفسه أبداً) "

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.social_post_view_form
msgid ""
"e.g. Engage your entire community with a single app! "
"https://www.odoo.com/trial"
msgstr ""
"مثال: تمكن من التفاعل مع مجتمعك بأكمله باستخدام تطبيق واحد! "
"https://www.odoo.com/trial "

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.social_post_view_form
msgid "e.g. Odoo Social Tutorial"
msgstr "مثال: درس أودو التعليمي للتواصل الاجتماعي "

#. module: social_youtube
#: model_terms:ir.ui.view,arch_db:social_youtube.social_account_view_form
msgid "the Google Third-party app account access panel"
msgstr "لوحة الوصول إلى حساب تطبيق Google الخارجي "
