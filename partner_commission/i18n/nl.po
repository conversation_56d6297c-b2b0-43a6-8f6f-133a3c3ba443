# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* partner_commission
# 
# Translators:
# <PERSON><PERSON>, 2024
# <PERSON> <<EMAIL>>, 2025
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:27+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON><PERSON>, 2025\n"
"Language-Team: Dutch (https://app.transifex.com/odoo/teams/41243/nl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: nl\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: partner_commission
#. odoo-python
#: code:addons/partner_commission/models/account_move.py:0
msgid ""
"\n"
"%(product)s: from %(start_date)s to %(end_date)s"
msgstr ""
"\n"
"%(product)s: van %(start_date)s tot %(end_date)s"

#. module: partner_commission
#. odoo-python
#: code:addons/partner_commission/models/account_move.py:0
msgid " (%d month(s))"
msgstr " (%d maand(en))"

#. module: partner_commission
#: model:ir.model.fields,field_description:partner_commission.field_commission_plan__active
msgid "Active"
msgstr "Actief"

#. module: partner_commission
#: model:res.groups,name:partner_commission.group_commission_manager
msgid "All Documents"
msgstr "Alle documenten"

#. module: partner_commission
#: model_terms:ir.ui.view,arch_db:partner_commission.commission_plan_search_view
msgid "Archived"
msgstr "Gearchiveerd"

#. module: partner_commission
#: model_terms:ir.ui.view,arch_db:partner_commission.res_config_settings_view_form
msgid "Automatic PO frequency"
msgstr "Frequentie van automatische inkooporders"

#. module: partner_commission
#: model:ir.model.fields,field_description:partner_commission.field_commission_rule__is_capped
msgid "Capped"
msgstr "Plafond"

#. module: partner_commission
#: model:ir.model.fields.selection,name:partner_commission.selection__purchase_order__purchase_type__commission
#: model:product.template,name:partner_commission.product_commission_product_template
msgid "Commission"
msgstr "Commissie"

#. module: partner_commission
#: model:ir.model.fields,field_description:partner_commission.field_res_company__commission_automatic_po_frequency
#: model:ir.model.fields,field_description:partner_commission.field_res_config_settings__commission_automatic_po_frequency
msgid "Commission Automatic Po Frequency"
msgstr "Commissie automatische inkooporder frequentie"

#. module: partner_commission
#: model:ir.model.fields,field_description:partner_commission.field_commission_rule__plan_id
#: model:ir.model.fields,field_description:partner_commission.field_res_partner__commission_plan_id
#: model:ir.model.fields,field_description:partner_commission.field_res_users__commission_plan_id
#: model:ir.model.fields,field_description:partner_commission.field_sale_order__commission_plan_id
#: model:ir.model.fields,field_description:partner_commission.field_sale_order_log_report__commission_plan_id
#: model:ir.model.fields,field_description:partner_commission.field_sale_report__commission_plan_id
#: model:ir.model.fields,field_description:partner_commission.field_sale_subscription_report__commission_plan_id
msgid "Commission Plan"
msgstr "Commissieplan"

#. module: partner_commission
#: model:ir.actions.act_window,name:partner_commission.action_commission_plans
#: model:ir.ui.menu,name:partner_commission.menu_commission_plans
msgid "Commission Plans"
msgstr "Commissieplannen"

#. module: partner_commission
#. odoo-python
#: code:addons/partner_commission/models/account_move.py:0
msgid "Commission on %(invoice)s, %(partner)s, %(amount)s"
msgstr "Commissie op %(invoice)s, %(partner)s, %(amount)s"

#. module: partner_commission
#: model:ir.model,name:partner_commission.model_commission_plan
msgid "Commission plan"
msgstr "Commissieplan"

#. module: partner_commission
#. odoo-python
#: code:addons/partner_commission/models/account_move.py:0
msgid "Commission refunded. Invoice: %(link)s. Amount: %(amount)s."
msgstr "Commissie terugbetaald. Factuur: %(link)s. Bedrag: %(amount)s."

#. module: partner_commission
#: model:ir.model,name:partner_commission.model_commission_rule
msgid "Commission rules management."
msgstr "De regels van de Commissie voor het beheer."

#. module: partner_commission
#: model:ir.model,name:partner_commission.model_res_company
msgid "Companies"
msgstr "Bedrijven"

#. module: partner_commission
#: model:ir.model.fields,field_description:partner_commission.field_commission_plan__company_id
msgid "Company"
msgstr "Bedrijf"

#. module: partner_commission
#: model:ir.model,name:partner_commission.model_res_config_settings
msgid "Config Settings"
msgstr "Configuratie-instellingen"

#. module: partner_commission
#: model:ir.model,name:partner_commission.model_res_partner
msgid "Contact"
msgstr "Contact"

#. module: partner_commission
#: model:ir.model.fields,field_description:partner_commission.field_commission_plan__create_uid
#: model:ir.model.fields,field_description:partner_commission.field_commission_rule__create_uid
msgid "Created by"
msgstr "Aangemaakt door"

#. module: partner_commission
#: model:ir.model.fields,field_description:partner_commission.field_commission_plan__create_date
#: model:ir.model.fields,field_description:partner_commission.field_commission_rule__create_date
msgid "Created on"
msgstr "Aangemaakt op"

#. module: partner_commission
#: model:ir.actions.act_window,name:partner_commission.action_view_customer_invoices
msgid "Customer Invoices"
msgstr "Klantfacturen"

#. module: partner_commission
#: model:ir.model.fields,field_description:partner_commission.field_res_partner_grade__default_commission_plan_id
msgid "Default Commission Plan"
msgstr "Standaard Commissieplan"

#. module: partner_commission
#: model:ir.model.fields,field_description:partner_commission.field_commission_plan__display_name
#: model:ir.model.fields,field_description:partner_commission.field_commission_rule__display_name
msgid "Display Name"
msgstr "Schermnaam"

#. module: partner_commission
#: model:ir.model.fields,field_description:partner_commission.field_sale_order__commission_plan_frozen
msgid "Freeze Plan"
msgstr "Plan bevriezen"

#. module: partner_commission
#: model_terms:ir.ui.view,arch_db:partner_commission.res_config_settings_view_form
msgid "Frequency at which purchase orders will be automatically confirmed"
msgstr "Frequentie waarmee inkooporders automatisch worden bevestigd"

#. module: partner_commission
#: model:ir.model.fields,field_description:partner_commission.field_commission_plan__id
#: model:ir.model.fields,field_description:partner_commission.field_commission_rule__id
msgid "ID"
msgstr "ID"

#. module: partner_commission
#: model:ir.model.fields,help:partner_commission.field_commission_rule__product_id
msgid ""
"If set, the rule does not apply to the whole category but only on the given product.\n"
"The product must belong to the selected category.\n"
"Use several rules if you need to match multiple products within a category."
msgstr ""
"Indien ingesteld, is de regel niet van toepassing op de hele categorie, maar alleen op het gegeven product.\n"
"Het product moet tot de geselecteerde categorie behoren.\n"
"Gebruik verschillende regels als je meerdere producten binnen een categorie moet matchen."

#. module: partner_commission
#: model:ir.model.fields,help:partner_commission.field_purchase_order__invoice_commission_count
msgid "Invoices that have generated commissions included in this order"
msgstr ""
"Facturen die commissies hebben gegenereerd die in deze order zijn opgenomen"

#. module: partner_commission
#: model:ir.model,name:partner_commission.model_account_move
msgid "Journal Entry"
msgstr "Boeking"

#. module: partner_commission
#: model:ir.model,name:partner_commission.model_account_move_line
msgid "Journal Item"
msgstr "Boekingsregel"

#. module: partner_commission
#: model:ir.model.fields,field_description:partner_commission.field_commission_plan__write_uid
#: model:ir.model.fields,field_description:partner_commission.field_commission_rule__write_uid
msgid "Last Updated by"
msgstr "Laatst bijgewerkt door"

#. module: partner_commission
#: model:ir.model.fields,field_description:partner_commission.field_commission_plan__write_date
#: model:ir.model.fields,field_description:partner_commission.field_commission_rule__write_date
msgid "Last Updated on"
msgstr "Laatst bijgewerkt op"

#. module: partner_commission
#: model:ir.model,name:partner_commission.model_crm_lead
msgid "Lead/Opportunity"
msgstr "Lead/Verkoopkans"

#. module: partner_commission
#: model:ir.model.fields.selection,name:partner_commission.selection__res_company__commission_automatic_po_frequency__manually
msgid "Manually"
msgstr "Handmatig"

#. module: partner_commission
#: model:ir.model.fields,field_description:partner_commission.field_commission_rule__max_commission
msgid "Max Commission"
msgstr "Maximale Commissie"

#. module: partner_commission
#: model:ir.model.fields,help:partner_commission.field_commission_rule__max_commission
msgid "Maximum amount, specified in the currency of the pricelist, if given."
msgstr ""
"Maximumbedrag, gespecificeerd in de valuta van de prijslijst, indien "
"opgegeven."

#. module: partner_commission
#: model_terms:ir.ui.view,arch_db:partner_commission.res_config_settings_view_form
msgid "Minimum PO amount total"
msgstr "Minimum totaalbedrag van de inkooporder"

#. module: partner_commission
#: model:ir.model.fields,field_description:partner_commission.field_res_company__commission_po_minimum
#: model:ir.model.fields,field_description:partner_commission.field_res_config_settings__commission_po_minimum
msgid "Minimum Total Amount for PO commission"
msgstr "Minimum totaalbedrag voor inkooporder-commissie"

#. module: partner_commission
#: model:ir.model.fields.selection,name:partner_commission.selection__res_company__commission_automatic_po_frequency__monthly
msgid "Monthly"
msgstr "Maandelijks"

#. module: partner_commission
#: model:ir.model.fields,field_description:partner_commission.field_commission_plan__name
msgid "Name"
msgstr "Naam"

#. module: partner_commission
#. odoo-python
#: code:addons/partner_commission/models/account_move.py:0
msgid "New commission. Invoice: %(link)s. Amount: %(amount)s."
msgstr "Nieuwe commissie. Factuur: %(link)s. Bedrag: %(amount)s."

#. module: partner_commission
#: model:res.groups,name:partner_commission.group_commission_user
msgid "Own Documents Only"
msgstr "Alleen eigen documenten"

#. module: partner_commission
#: model:ir.actions.server,name:partner_commission.cron_confirm_purchase_orders_ir_actions_server
msgid "Partner Commission: confirm purchase orders"
msgstr "Partnercommissie: bevestig inkooporders"

#. module: partner_commission
#: model:ir.model,name:partner_commission.model_res_partner_grade
msgid "Partner Grade"
msgstr "Partner rang"

#. module: partner_commission
#: model_terms:ir.ui.view,arch_db:partner_commission.res_config_settings_view_form
msgid "Partners Commissions"
msgstr "Partners commissies"

#. module: partner_commission
#: model:ir.model.fields,field_description:partner_commission.field_commission_rule__pricelist_id
msgid "Pricelist"
msgstr "Prijslijst"

#. module: partner_commission
#: model:ir.model.fields.selection,name:partner_commission.selection__purchase_order__purchase_type__procurement
msgid "Procurement"
msgstr "Aanvullen"

#. module: partner_commission
#: model:ir.model.fields,field_description:partner_commission.field_commission_rule__product_id
msgid "Product"
msgstr "Product"

#. module: partner_commission
#. odoo-python
#: code:addons/partner_commission/models/commission_plan.py:0
msgid "Product %(product)s does not belong to category %(category)s"
msgstr "Product %(product)s behoort niet tot categorie %(category)s"

#. module: partner_commission
#: model:ir.model.fields,field_description:partner_commission.field_commission_rule__category_id
msgid "Product Category"
msgstr "Productcategorie"

#. module: partner_commission
#: model:ir.model.fields,field_description:partner_commission.field_commission_plan__product_id
msgid "Purchase Default Product"
msgstr "Standaard product voor inkopen"

#. module: partner_commission
#: model:ir.model,name:partner_commission.model_purchase_order
msgid "Purchase Order"
msgstr "Inkooporder"

#. module: partner_commission
#: model:ir.model.fields,field_description:partner_commission.field_purchase_order__purchase_type
msgid "Purchase Type"
msgstr "Inkooptype"

#. module: partner_commission
#: model:ir.model.fields.selection,name:partner_commission.selection__res_company__commission_automatic_po_frequency__quarterly
msgid "Quarterly"
msgstr "Per kwartaal"

#. module: partner_commission
#: model:ir.model.fields,field_description:partner_commission.field_commission_rule__rate
msgid "Rate"
msgstr "Hoeveel"

#. module: partner_commission
#: model:ir.model.constraint,message:partner_commission.constraint_commission_rule_check_rate
msgid "Rate should be between 0 and 100."
msgstr "Het tarief moet tussen 0 en 100 liggen."

#. module: partner_commission
#: model:ir.model.fields,field_description:partner_commission.field_account_bank_statement_line__referrer_id
#: model:ir.model.fields,field_description:partner_commission.field_account_move__referrer_id
#: model:ir.model.fields,field_description:partner_commission.field_sale_order__referrer_id
#: model:ir.model.fields,field_description:partner_commission.field_sale_order_log_report__referrer_id
#: model:ir.model.fields,field_description:partner_commission.field_sale_report__referrer_id
#: model:ir.model.fields,field_description:partner_commission.field_sale_subscription_report__referrer_id
#: model_terms:ir.ui.view,arch_db:partner_commission.account_move_view_search_inherit_partner_commission
#: model_terms:ir.ui.view,arch_db:partner_commission.sale_order_log_search
#: model_terms:ir.ui.view,arch_db:partner_commission.sale_order_subsciption_view_search_inherit_partner_commission
#: model_terms:ir.ui.view,arch_db:partner_commission.sale_order_view_search_inherit_partner_commission
#: model_terms:ir.ui.view,arch_db:partner_commission.sale_report_search
#: model_terms:ir.ui.view,arch_db:partner_commission.sale_subscription_report_search
msgid "Referrer"
msgstr "Verwijzer"

#. module: partner_commission
#: model:ir.model.fields,field_description:partner_commission.field_sale_order__commission
msgid "Referrer Commission"
msgstr "Verwijzingscommissie"

#. module: partner_commission
#: model:ir.model.fields,field_description:partner_commission.field_account_bank_statement_line__commission_po_line_id
#: model:ir.model.fields,field_description:partner_commission.field_account_move__commission_po_line_id
msgid "Referrer Purchase Order line"
msgstr "Verwijzende inkooporderregel"

#. module: partner_commission
#: model:ir.model.fields,field_description:partner_commission.field_commission_plan__commission_rule_ids
#: model_terms:ir.ui.view,arch_db:partner_commission.commission_plan_form_view
msgid "Rules"
msgstr "Regels"

#. module: partner_commission
#: model:ir.model.fields,field_description:partner_commission.field_commission_rule__template_id
msgid "Sale Order Template"
msgstr "Verkoopordersjabloon"

#. module: partner_commission
#: model:ir.model,name:partner_commission.model_sale_report
msgid "Sales Analysis Report"
msgstr "Verkoopanalyserapport"

#. module: partner_commission
#: model:ir.model,name:partner_commission.model_sale_order_log_report
msgid "Sales Log Analysis Report"
msgstr "Analyserapport verkooplogboek"

#. module: partner_commission
#: model:ir.model,name:partner_commission.model_sale_order
msgid "Sales Order"
msgstr "Verkooporder"

#. module: partner_commission
#: model:ir.model.fields,field_description:partner_commission.field_commission_rule__sequence
msgid "Sequence"
msgstr "Reeks"

#. module: partner_commission
#: model:ir.model.fields,field_description:partner_commission.field_purchase_order__invoice_commission_count
#: model_terms:ir.ui.view,arch_db:partner_commission.purchase_order_form_inherit_partner_commission
msgid "Source Invoices"
msgstr "Bronfacturen"

#. module: partner_commission
#: model:ir.model,name:partner_commission.model_sale_subscription_report
msgid "Subscription Analysis"
msgstr "Abonnementanalyse"

#. module: partner_commission
#: model:ir.model.fields,help:partner_commission.field_sale_order__commission_plan_id
msgid "Takes precedence over the Referrer's commission plan."
msgstr "Heeft voorrang op het commissieplan van de verwijzer."

#. module: partner_commission
#. odoo-python
#: code:addons/partner_commission/models/account_move.py:0
msgid ""
"The commission partner order %s must be checked manually (especially refund "
"lines which can be duplicated)."
msgstr ""
"De order partnercommissie %s moet handmatig worden gecontroleerd (vooral "
"terugbetalingsregels die gedupliceerd kunnen worden)."

#. module: partner_commission
#: model:ir.model.fields,help:partner_commission.field_res_partner_grade__default_commission_plan_id
msgid ""
"The default commission plan used for this grade. Can be overwritten on the "
"partner form."
msgstr ""
"Het standaard commissieplan dat voor dit cijfer wordt gebruikt. Dit kan "
"worden overschreven op het partnerformulier."

#. module: partner_commission
#: model_terms:ir.ui.view,arch_db:partner_commission.res_config_settings_view_form
msgid ""
"The required minimum amount total needed to automatically confirm purchase "
"orders"
msgstr ""
"Het vereiste minimumbedrag dat nodig is om inkooporders automatisch te "
"bevestigen"

#. module: partner_commission
#: model:ir.model.fields.selection,name:partner_commission.selection__res_company__commission_automatic_po_frequency__weekly
msgid "Weekly"
msgstr "Wekelijks"

#. module: partner_commission
#: model:ir.model.fields,help:partner_commission.field_commission_rule__is_capped
msgid "Whether the commission is capped."
msgstr "Of de commissie een plafond heeft."

#. module: partner_commission
#: model:ir.model.fields,help:partner_commission.field_sale_order__commission_plan_frozen
msgid ""
"Whether the commission plan is frozen. When checked, the commission plan "
"won't automatically be updated according to the partner level."
msgstr ""
"Of het commissieplan is bevroren. Indien aangevinkt, wordt het commissieplan"
" niet automatisch bijgewerkt op basis van het partnerniveau."
