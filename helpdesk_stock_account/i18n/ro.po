# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* helpdesk_stock_account
# 
# Translators:
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:27+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Romanian (https://app.transifex.com/odoo/teams/41243/ro/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ro\n"
"Plural-Forms: nplurals=3; plural=(n==1?0:(((n%100>19)||((n%100==0)&&(n!=0)))?2:1));\n"

#. module: helpdesk_stock_account
#: model:ir.model,name:helpdesk_stock_account.model_account_move_reversal
msgid "Account Move Reversal"
msgstr "Inversare notă contabilă"

#. module: helpdesk_stock_account
#: model:ir.model.fields,help:helpdesk_stock_account.field_account_move_reversal__tracking
msgid "Ensure the traceability of a storable product in your warehouse."
msgstr ""
"Asigurați trasabilitatea unui produs stocabil în depozitul dumneavoastră."

#. module: helpdesk_stock_account
#: model:ir.model.fields,field_description:helpdesk_stock_account.field_account_move_reversal__lot_id
msgid "Lot/Serial Number"
msgstr "Lot/Număr Serial"

#. module: helpdesk_stock_account
#: model:ir.model.fields,field_description:helpdesk_stock_account.field_account_move_reversal__product_id
msgid "Product"
msgstr "Produs"

#. module: helpdesk_stock_account
#: model:ir.model.fields,help:helpdesk_stock_account.field_account_move_reversal__product_id
msgid ""
"Product this ticket is about. If selected, only the sales orders, deliveries"
" and invoices including this product will be visible."
msgstr ""
"Produsul despre care este acest tichet. Dacă este selectat, doar comenzile "
"de vânzări, livrările și facturile care includ acest produs vor fi vizibile."

#. module: helpdesk_stock_account
#: model:ir.model.fields,field_description:helpdesk_stock_account.field_account_move_reversal__tracking
msgid "Tracking"
msgstr "Urmărire"
