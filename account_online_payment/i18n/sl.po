# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_online_payment
# 
# Translators:
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-01-27 13:55+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Slovenian (https://app.transifex.com/odoo/teams/41243/sl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sl\n"
"Plural-Forms: nplurals=4; plural=(n%100==1 ? 0 : n%100==2 ? 1 : n%100==3 || n%100==4 ? 2 : 3);\n"

#. module: account_online_payment
#. odoo-python
#: code:addons/account_online_payment/models/account_batch_payment.py:0
msgid ""
"\n"
"                This payment requires a KYC flow. As this process can take a few days, please use SEPA XML export in the meantime.\n"
"                You will be notified once the KYC flow is completed and you can proceed with the online payment.\n"
"            "
msgstr ""

#. module: account_online_payment
#: model:ir.model.fields.selection,name:account_online_payment.selection__account_batch_payment__payment_online_status__accepted
msgid "Accepted"
msgstr "Sprejeto"

#. module: account_online_payment
#: model:ir.model.fields,field_description:account_online_payment.field_account_batch_payment__account_online_linked
msgid "Account Online Linked"
msgstr ""

#. module: account_online_payment
#: model:ir.actions.server,name:account_online_payment.ir_cron_bank_sync_update_payment_status_ir_actions_server
msgid "Account: Update payment status"
msgstr ""

#. module: account_online_payment
#: model:ir.model.fields,field_description:account_online_payment.field_account_batch_payment__payment_identifier
msgid "Batch ID"
msgstr ""

#. module: account_online_payment
#: model:ir.model,name:account_online_payment.model_account_batch_payment
msgid "Batch Payment"
msgstr "Paketno plačilo"

#. module: account_online_payment
#: model:ir.model.fields.selection,name:account_online_payment.selection__account_batch_payment__payment_online_status__canceled
msgid "Canceled"
msgstr "Preklicano"

#. module: account_online_payment
#: model:ir.actions.server,name:account_online_payment.action_account_online_payment_check_status
msgid "Check Status"
msgstr ""

#. module: account_online_payment
#: model:ir.model.fields,field_description:account_online_payment.field_account_payment__end_to_end_id
msgid "End to End ID"
msgstr ""

#. module: account_online_payment
#: model_terms:ir.ui.view,arch_db:account_online_payment.view_batch_payment_form_inherit
msgid "Initiate Payment"
msgstr ""

#. module: account_online_payment
#: model:ir.model.fields,field_description:account_online_payment.field_account_batch_payment__payment_online_status
msgid "PIS Status"
msgstr ""

#. module: account_online_payment
#. odoo-python
#: code:addons/account_online_payment/models/account_batch_payment.py:0
msgid "Payment already been signed"
msgstr ""

#. module: account_online_payment
#: model:ir.model,name:account_online_payment.model_account_payment
msgid "Payments"
msgstr "Plačila"

#. module: account_online_payment
#: model:ir.model.fields.selection,name:account_online_payment.selection__account_batch_payment__payment_online_status__pending
msgid "Pending"
msgstr "Nerešeno"

#. module: account_online_payment
#. odoo-python
#: code:addons/account_online_payment/models/account_batch_payment.py:0
msgid ""
"Please be aware that signed payments may have already been processed and "
"sent to the bank."
msgstr ""

#. module: account_online_payment
#: model:ir.model.fields,field_description:account_online_payment.field_account_batch_payment__redirect_url
msgid "Redirect URL"
msgstr ""

#. module: account_online_payment
#: model:ir.model.fields.selection,name:account_online_payment.selection__account_batch_payment__payment_online_status__rejected
msgid "Rejected"
msgstr "Zavrnjeno"

#. module: account_online_payment
#: model_terms:ir.ui.view,arch_db:account_online_payment.view_batch_payment_form_inherit
msgid "Sign Payment"
msgstr ""

#. module: account_online_payment
#. odoo-python
#: code:addons/account_online_payment/models/account_batch_payment.py:0
msgid ""
"This payment might have already been signed. Refreshing the payment "
"status..."
msgstr ""

#. module: account_online_payment
#: model:ir.model.fields.selection,name:account_online_payment.selection__account_batch_payment__payment_online_status__uninitiated
msgid "Uninitiated"
msgstr ""

#. module: account_online_payment
#: model:ir.model.fields.selection,name:account_online_payment.selection__account_batch_payment__payment_online_status__unsigned
msgid "Unsigned"
msgstr ""

#. module: account_online_payment
#: model_terms:ir.ui.view,arch_db:account_online_payment.view_batch_payment_form_inherit
msgid "XML"
msgstr ""

#. module: account_online_payment
#. odoo-python
#: code:addons/account_online_payment/models/account_payment.py:0
msgid "You cannot modify a payment that has already been sent to the bank."
msgstr ""
