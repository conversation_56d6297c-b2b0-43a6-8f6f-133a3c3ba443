from odoo import fields, models, api, _
from odoo.exceptions import ValidationError


class SaleOrder(models.Model):
    _inherit = 'sale.order'

    order_type = fields.Selection(
        selection=[
            ('mts', 'Make To Stock (MTS)'),
            ('mto', 'Back To Back (B2B)')
        ],
        string='Order Type', tracking=True
    )
    route_id = fields.Many2one('stock.route', string='Route')

    @api.onchange('order_type')
    def _update_route_id(self):
        for rec in self:
            if rec.order_type == 'mto':
                mto_route_id = self.env.ref('stock.route_warehouse0_mto', raise_if_not_found=False)
                if not mto_route_id:
                    raise ValidationError(_(
                        "Replenish on Order (MTO) route not found. Cannot proceed with Make to Order."
                    ))
                if mto_route_id:
                    route = self.env['stock.route'].sudo().search([
                        ('id', '=', mto_route_id.id),
                        ('company_id', 'in', [self.company_id.id, False])
                    ], limit=1)
                    if not route:
                        raise ValidationError(_(
                            "Unable to proceed: the route for back-to-back orders is not configured. Please set it up in the system"
                        ))
                    rec.route_id = route.id
                    if rec.order_line:
                        rec.order_line.filtered(lambda rec: rec.product_id.type == 'consu').write({
                            'route_id': route.id
                        })
                        rec.order_line.filtered(lambda rec: rec.product_id.type != 'consu').write({'route_id': False})
            else:
                rec.route_id = False
                rec.order_line.write({'route_id': False})

    def action_confirm(self):
        """ update route if order type is MTO """
        if self.order_type == 'mto' and not self.route_id:
            raise ValidationError(_(
                "Replenish on Order (MTO) route not found. Cannot proceed with Make to Order."
            ))
        if self.order_line:
            self.order_line.write({'route_id': False})
            if self.order_type == 'mto':
                self.order_line.filtered(lambda rec: rec.product_template_id.type == 'consu').write({
                    'route_id': self.route_id.id
                })
        res = super(SaleOrder, self).action_confirm()
        return res

    # @api.onchange('order_type')
    # def onchange_order_type(self):
    #     """  Validates and updates order lines based on the selected order_type. """
    #     for rec in self:
    #         if rec.order_type:
    #             if not rec.order_line:
    #                 rec.order_type = False
    #                 raise ValidationError(_("Please first add items to the order line before selecting an order type."))
    #             mto_route_id = self.env.ref('stock.route_warehouse0_mto', raise_if_not_found=False)
    #             if mto_route_id:
    #                 mto_route_id = self.env['stock.route'].sudo().search([
    #                     ('id', '=', mto_route_id.id),
    #                     ('company_id', 'in', [self.company_id.id, False])
    #                 ], limit=1)
    #             rec.order_line.write({'route_id': False})
    #             if rec.order_type == 'mto':
    #                 if not mto_route_id:
    #                     raise ValidationError(_(
    #                         "Replenish on Order (MTO) route not found. Cannot proceed with Make to Order."
    #                     ))
    #                 # Update route only for non-service products
    #                 non_service_lines = rec.order_line.filtered(lambda l: l.product_template_id.type != 'service')
    #                 non_service_lines.write({'route_id': mto_route_id.id})
    #             elif rec.order_type == 'mts':
    #                 # Validate that no product in order lines uses the MTO route
    #                 if mto_route_id:
    #                     conflicting_lines = rec.order_line.filtered(
    #                         lambda l: mto_route_id.id in l.product_template_id.route_ids.ids
    #                     )
    #                     if conflicting_lines:
    #                         product_names = ", ".join(conflicting_lines.mapped('product_template_id.name'))
    #                         raise ValidationError(_(
    #                             "Order type 'MTS' is selected, but some products have the Replenish on Order (MTO) route enabled. "
    #                             "Please disable the MTO route for these products: %s."
    #                         ) % product_names)
