<?xml version="1.0" encoding="UTF-8" ?>
<odoo>
    <!-- Form view -->
    <record id="form_view_sale_order" model="ir.ui.view">
        <field name="name">form.view.sale.order.inherit.mto</field>
        <field name="model">sale.order</field>
        <field name="inherit_id" ref="sale.view_order_form"/>
        <field name="arch" type="xml">
            <field name="payment_term_id" position="after">
                <field name="order_type" required="1" readonly="state != 'draft'"/>
                <field name="route_id" invisible="1"/>
            </field>
        </field>
    </record>

    <!-- Sale order line -->
    <record id="form_view_sale_order_inherit" model="ir.ui.view">
        <field name="name">form.view.sale.order.inherit.sale.stock</field>
        <field name="model">sale.order</field>
        <field name="inherit_id" ref="sale_stock.view_order_form_inherit_sale_stock"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='route_id' and @optional='hide']" position="attributes">
                <attribute name="readonly">state != 'draft'</attribute>
            </xpath>
        </field>
    </record>
</odoo>