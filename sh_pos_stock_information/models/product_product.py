from odoo import models, api


class ShProductProduct(models.Model):
    _inherit = 'product.product'

    @api.model
    def _load_pos_data_fields(self, config_id):
        result = super()._load_pos_data_fields(config_id)
        result += ["qty_available", "virtual_available", "stock_quant_ids"]
        return result

    def _load_pos_data(self, data):
        res = super()._load_pos_data(data)
        config = self.env['pos.config'].browse(data['pos.config']['data'][0]['id'])
        for product in res['data']:
            stock_quant_ids = product.get('stock_quant_ids')
            all_location = []
            locations = self.env['stock.location'].search([
                ('id', '=', config.picking_type_id.default_location_src_id.id)
            ])
            all_location = list(locations.child_internal_location_ids.ids)
            quants = self.env['stock.quant'].search([
                ('id', 'in', stock_quant_ids),
                ('location_id.usage', '=', 'internal'),
                ('location_id', 'in', all_location)
            ])
            product['sh_available_stock'] = 0.0
            product['sh_on_hand_stock'] = 0.0
            for quant in quants:
                product['sh_available_stock'] += quant.available_quantity
                product['sh_on_hand_stock'] += quant.quantity
        return res
