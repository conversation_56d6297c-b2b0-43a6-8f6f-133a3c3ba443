# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* social_linkedin
# 
# Translators:
# <PERSON> <<EMAIL>>, 2025
# <PERSON>, 2025
# <PERSON><PERSON>, 2025
# <PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:28+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON>, 2025\n"
"Language-Team: Portuguese (https://app.transifex.com/odoo/teams/41243/pt/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: pt\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: social_linkedin
#: model_terms:ir.ui.view,arch_db:social_linkedin.linkedin_preview
msgid "<b>LinkedIn Post</b>"
msgstr ""

#. module: social_linkedin
#: model_terms:ir.ui.view,arch_db:social_linkedin.social_stream_post_view_kanban
msgid "<i class=\"fa fa-comments me-1\" title=\"Comments\"/>"
msgstr ""

#. module: social_linkedin
#: model_terms:ir.ui.view,arch_db:social_linkedin.social_stream_post_view_kanban
msgid "<i class=\"fa fa-thumbs-up me-1\" title=\"Likes\"/>"
msgstr ""

#. module: social_linkedin
#: model:ir.model.fields,field_description:social_linkedin.field_social_live_post__linkedin_post_id
msgid "Actual LinkedIn ID of the post"
msgstr ""

#. module: social_linkedin
#. odoo-python
#: code:addons/social_linkedin/models/social_account.py:0
msgid "An error occurred when fetching your pages data: “%s”."
msgstr ""

#. module: social_linkedin
#. odoo-python
#: code:addons/social_linkedin/models/social_account.py:0
msgid "An error occurred when fetching your pages: “%s”."
msgstr ""

#. module: social_linkedin
#: model:ir.model.fields,field_description:social_linkedin.field_res_config_settings__linkedin_app_id
msgid "App ID"
msgstr ""

#. module: social_linkedin
#: model:ir.model.fields,field_description:social_linkedin.field_res_config_settings__linkedin_client_secret
msgid "App Secret"
msgstr ""

#. module: social_linkedin
#. odoo-javascript
#: code:addons/social_linkedin/static/src/xml/social_linkedin_templates.xml:0
#: model_terms:ir.ui.view,arch_db:social_linkedin.social_stream_post_view_kanban
msgid "Author Image"
msgstr ""

#. module: social_linkedin
#: model:ir.model.fields,help:social_linkedin.field_res_config_settings__linkedin_use_own_account
msgid ""
"Check this if you want to use your personal LinkedIn Developer Account "
"instead of the provided one."
msgstr ""

#. module: social_linkedin
#: model:ir.model,name:social_linkedin.model_res_config_settings
msgid "Config Settings"
msgstr "Definições de Configuração"

#. module: social_linkedin
#. odoo-python
#: code:addons/social_linkedin/models/social_stream_post.py:0
msgid "Could not like / dislike this comment."
msgstr ""

#. module: social_linkedin
#. odoo-python
#: code:addons/social_linkedin/models/social_stream_post.py:0
msgid "Couldn't delete the post: “%s”."
msgstr ""

#. module: social_linkedin
#. odoo-python
#: code:addons/social_linkedin/models/social_stream_post.py:0
msgid "Couldn't update the post: “%s”."
msgstr ""

#. module: social_linkedin
#: model:ir.model.fields,field_description:social_linkedin.field_social_post__display_linkedin_preview
#: model:ir.model.fields,field_description:social_linkedin.field_social_post_template__display_linkedin_preview
msgid "Display LinkedIn Preview"
msgstr ""

#. module: social_linkedin
#. odoo-python
#: code:addons/social_linkedin/models/social_stream_post.py:0
msgid "Failed to post the comment: “%(error_description)s”"
msgstr ""

#. module: social_linkedin
#. odoo-python
#: code:addons/social_linkedin/models/social_stream_post.py:0
msgid ""
"Failed to retrieve the post. It might have been deleted or you may not have "
"permission to view it."
msgstr ""

#. module: social_linkedin
#: model:ir.model.fields,field_description:social_linkedin.field_social_post__has_linkedin_account
#: model:ir.model.fields,field_description:social_linkedin.field_social_post_template__has_linkedin_account
msgid "Has LinkedIn Account"
msgstr ""

#. module: social_linkedin
#: model_terms:ir.ui.view,arch_db:social_linkedin.social_post_template_view_form
msgid "Images"
msgstr "Imagens"

#. module: social_linkedin
#. odoo-javascript
#: code:addons/social_linkedin/static/src/xml/social_linkedin_templates.xml:0
msgid "Likes"
msgstr "Likes"

#. module: social_linkedin
#: model:ir.model.fields.selection,name:social_linkedin.selection__social_media__media_type__linkedin
#: model:social.media,name:social_linkedin.social_media_linkedin
#: model_terms:ir.ui.view,arch_db:social_linkedin.social_post_template_view_form
msgid "LinkedIn"
msgstr "LinkedIn"

#. module: social_linkedin
#: model:ir.model.fields,field_description:social_linkedin.field_social_account__linkedin_account_id
msgid "LinkedIn Account ID"
msgstr ""

#. module: social_linkedin
#: model:ir.model.fields,field_description:social_linkedin.field_social_account__linkedin_account_urn
#: model:ir.model.fields,help:social_linkedin.field_social_account__linkedin_account_urn
msgid "LinkedIn Account URN"
msgstr ""

#. module: social_linkedin
#. odoo-javascript
#: code:addons/social_linkedin/static/src/js/stream_post_kanban_record.js:0
#: model:ir.model.fields,field_description:social_linkedin.field_social_stream_post__linkedin_comments_count
msgid "LinkedIn Comments"
msgstr ""

#. module: social_linkedin
#: model:ir.model.fields,field_description:social_linkedin.field_social_post__linkedin_image_ids
#: model:ir.model.fields,field_description:social_linkedin.field_social_post_template__linkedin_image_ids
msgid "LinkedIn Images"
msgstr ""

#. module: social_linkedin
#: model:ir.model.fields,field_description:social_linkedin.field_social_stream_post__linkedin_likes_count
msgid "LinkedIn Likes"
msgstr ""

#. module: social_linkedin
#: model:ir.model.fields,field_description:social_linkedin.field_social_post__linkedin_message
#: model:ir.model.fields,field_description:social_linkedin.field_social_post_template__linkedin_message
msgid "LinkedIn Message"
msgstr ""

#. module: social_linkedin
#: model:ir.model.fields,field_description:social_linkedin.field_social_post__linkedin_preview
#: model:ir.model.fields,field_description:social_linkedin.field_social_post_template__linkedin_preview
msgid "LinkedIn Preview"
msgstr ""

#. module: social_linkedin
#: model:ir.model.fields,field_description:social_linkedin.field_social_stream_post__linkedin_author_vanity_name
msgid "LinkedIn Vanity Name"
msgstr ""

#. module: social_linkedin
#: model:ir.model.fields,field_description:social_linkedin.field_social_account__linkedin_access_token
msgid "LinkedIn access token"
msgstr "Código de acesso de Linkedin"

#. module: social_linkedin
#: model:ir.model.fields,field_description:social_linkedin.field_social_stream_post__linkedin_author_id
msgid "LinkedIn author ID"
msgstr ""

#. module: social_linkedin
#: model:ir.model.fields,field_description:social_linkedin.field_social_stream_post__linkedin_author_urn
msgid "LinkedIn author URN"
msgstr ""

#. module: social_linkedin
#: model:ir.model.fields,field_description:social_linkedin.field_social_stream_post__linkedin_author_image_url
msgid "LinkedIn author image URL"
msgstr ""

#. module: social_linkedin
#. odoo-python
#: code:addons/social_linkedin/controllers/main.py:0
msgid "LinkedIn did not provide a valid access token."
msgstr ""

#. module: social_linkedin
#: model:ir.model.fields,field_description:social_linkedin.field_social_stream_post__linkedin_post_urn
msgid "LinkedIn post URN"
msgstr ""

#. module: social_linkedin
#: model_terms:ir.ui.view,arch_db:social_linkedin.res_config_settings_view_form
msgid "Linkedin Developer Account"
msgstr ""

#. module: social_linkedin
#: model:ir.model.fields,field_description:social_linkedin.field_social_media__media_type
msgid "Media Type"
msgstr ""

#. module: social_linkedin
#: model_terms:ir.ui.view,arch_db:social_linkedin.social_post_template_view_form
msgid "Message"
msgstr "Mensagem"

#. module: social_linkedin
#. odoo-python
#: code:addons/social_linkedin/models/social_post_template.py:0
msgid ""
"Please specify either a LinkedIn Message or upload some LinkedIn Images."
msgstr ""

#. module: social_linkedin
#: model_terms:ir.ui.view,arch_db:social_linkedin.linkedin_preview
msgid "Post Image"
msgstr ""

#. module: social_linkedin
#: model:social.stream.type,name:social_linkedin.stream_type_linkedin_company_post
msgid "Posts"
msgstr "Posts"

#. module: social_linkedin
#. odoo-python
#: code:addons/social_linkedin/models/social_account.py:0
msgid "Read More about Business Accounts"
msgstr ""

#. module: social_linkedin
#: model:ir.model,name:social_linkedin.model_social_account
msgid "Social Account"
msgstr ""

#. module: social_linkedin
#: model:ir.model,name:social_linkedin.model_social_live_post
msgid "Social Live Post"
msgstr ""

#. module: social_linkedin
#: model:ir.model,name:social_linkedin.model_social_media
msgid "Social Media"
msgstr "Redes Sociais"

#. module: social_linkedin
#: model:ir.model,name:social_linkedin.model_social_post
msgid "Social Post"
msgstr "Publicação Social"

#. module: social_linkedin
#: model:ir.model,name:social_linkedin.model_social_post_template
msgid "Social Post Template"
msgstr ""

#. module: social_linkedin
#: model:ir.model,name:social_linkedin.model_social_stream
msgid "Social Stream"
msgstr "Social Stream"

#. module: social_linkedin
#: model:ir.model,name:social_linkedin.model_social_stream_post
msgid "Social Stream Post"
msgstr ""

#. module: social_linkedin
#: model:ir.model.fields,help:social_linkedin.field_social_account__linkedin_access_token
msgid "The access token is used to perform request to the REST API"
msgstr ""

#. module: social_linkedin
#. odoo-python
#: code:addons/social_linkedin/models/social_media.py:0
msgid ""
"The url that this service requested returned an error. Please contact the "
"author of the app."
msgstr ""

#. module: social_linkedin
#. odoo-python
#: code:addons/social_linkedin/controllers/main.py:0
msgid "There was a authentication issue during your request."
msgstr ""

#. module: social_linkedin
#. odoo-python
#: code:addons/social_linkedin/controllers/main.py:0
msgid "Unauthorized. Please contact your administrator."
msgstr ""

#. module: social_linkedin
#. odoo-python
#: code:addons/social_linkedin/models/social_stream.py:0
#: code:addons/social_linkedin/models/social_stream_post.py:0
msgid "Unknown"
msgstr "Desconhecido"

#. module: social_linkedin
#: model:ir.model.fields,field_description:social_linkedin.field_res_config_settings__linkedin_use_own_account
msgid "Use your own LinkedIn Account"
msgstr ""

#. module: social_linkedin
#: model:ir.model.fields,help:social_linkedin.field_social_media__media_type
msgid ""
"Used to make comparisons when we need to restrict some features to a "
"specific media ('facebook', 'x', ...)."
msgstr ""

#. module: social_linkedin
#: model:ir.model.fields,help:social_linkedin.field_social_stream_post__linkedin_author_vanity_name
msgid "Vanity name, used to generate a link to the author"
msgstr ""

#. module: social_linkedin
#. odoo-python
#: code:addons/social_linkedin/models/social_account.py:0
msgid ""
"We could not upload your image, try reducing its size and posting it again "
"(error: Failed during upload registering)."
msgstr ""

#. module: social_linkedin
#. odoo-python
#: code:addons/social_linkedin/models/social_account.py:0
msgid ""
"We could not upload your image, try reducing its size and posting it again."
msgstr ""

#. module: social_linkedin
#: model:ir.model.fields,help:social_linkedin.field_social_post__linkedin_image_ids
#: model:ir.model.fields,help:social_linkedin.field_social_post_template__linkedin_image_ids
msgid "Will attach images to your posts."
msgstr ""

#. module: social_linkedin
#. odoo-python
#: code:addons/social_linkedin/models/social_stream.py:0
msgid "Wrong stream type for \"%s\""
msgstr ""

#. module: social_linkedin
#. odoo-python
#: code:addons/social_linkedin/models/social_media.py:0
msgid "You don't have an active subscription. Please buy one here: %s"
msgstr "Não tem uma subscrição ativa. Por favor adquira uma aqui: %s"

#. module: social_linkedin
#. odoo-python
#: code:addons/social_linkedin/models/social_account.py:0
msgid ""
"You need a Business Account to post on LinkedIn with Odoo Social.\n"
" Please create one and make sure it is linked to your account"
msgstr ""

#. module: social_linkedin
#. odoo-python
#: code:addons/social_linkedin/models/social_live_post.py:0
msgid "unknown"
msgstr "desconhecido"
