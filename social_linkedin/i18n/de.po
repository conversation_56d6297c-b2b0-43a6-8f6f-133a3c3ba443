# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* social_linkedin
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:28+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: German (https://app.transifex.com/odoo/teams/41243/de/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: de\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: social_linkedin
#: model_terms:ir.ui.view,arch_db:social_linkedin.linkedin_preview
msgid "<b>LinkedIn Post</b>"
msgstr "<b>LinkedIn-Beitrag</b>"

#. module: social_linkedin
#: model_terms:ir.ui.view,arch_db:social_linkedin.social_stream_post_view_kanban
msgid "<i class=\"fa fa-comments me-1\" title=\"Comments\"/>"
msgstr "<i class=\"fa fa-comments me-1\" title=\"Comments\"/>"

#. module: social_linkedin
#: model_terms:ir.ui.view,arch_db:social_linkedin.social_stream_post_view_kanban
msgid "<i class=\"fa fa-thumbs-up me-1\" title=\"Likes\"/>"
msgstr "<i class=\"fa fa-thumbs-up me-1\" title=\"Likes\"/>"

#. module: social_linkedin
#: model:ir.model.fields,field_description:social_linkedin.field_social_live_post__linkedin_post_id
msgid "Actual LinkedIn ID of the post"
msgstr "Tatsächliche LinkedIn-ID des Beitrags"

#. module: social_linkedin
#. odoo-python
#: code:addons/social_linkedin/models/social_account.py:0
msgid "An error occurred when fetching your pages data: “%s”."
msgstr "Beim Abrufen Ihrer Seitendaten ist ein Fehler aufgetreten: „%s“."

#. module: social_linkedin
#. odoo-python
#: code:addons/social_linkedin/models/social_account.py:0
msgid "An error occurred when fetching your pages: “%s”."
msgstr "Beim Abrufen Ihrer Seiten ist ein Fehler aufgetreten: „%s“"

#. module: social_linkedin
#: model:ir.model.fields,field_description:social_linkedin.field_res_config_settings__linkedin_app_id
msgid "App ID"
msgstr "App-ID"

#. module: social_linkedin
#: model:ir.model.fields,field_description:social_linkedin.field_res_config_settings__linkedin_client_secret
msgid "App Secret"
msgstr "App-Geheimnis"

#. module: social_linkedin
#. odoo-javascript
#: code:addons/social_linkedin/static/src/xml/social_linkedin_templates.xml:0
#: model_terms:ir.ui.view,arch_db:social_linkedin.social_stream_post_view_kanban
msgid "Author Image"
msgstr "Bild des Autors"

#. module: social_linkedin
#: model:ir.model.fields,help:social_linkedin.field_res_config_settings__linkedin_use_own_account
msgid ""
"Check this if you want to use your personal LinkedIn Developer Account "
"instead of the provided one."
msgstr ""
"Aktivieren Sie dieses Kontrollkästchen, wenn Sie Ihr persönliches LinkedIn-"
"Entwicklerkonto anstelle des bereitgestellten Kontos verwenden möchten."

#. module: social_linkedin
#: model:ir.model,name:social_linkedin.model_res_config_settings
msgid "Config Settings"
msgstr "Konfigurationseinstellungen"

#. module: social_linkedin
#. odoo-python
#: code:addons/social_linkedin/models/social_stream_post.py:0
msgid "Could not like / dislike this comment."
msgstr ""
"Konnte Kommentar nicht mit „Gefällt mir“/„Gefällt mir nicht“ markieren."

#. module: social_linkedin
#. odoo-python
#: code:addons/social_linkedin/models/social_stream_post.py:0
msgid "Couldn't delete the post: “%s”."
msgstr "Der Beitrag konnte nicht gelöscht werden: „%s“"

#. module: social_linkedin
#. odoo-python
#: code:addons/social_linkedin/models/social_stream_post.py:0
msgid "Couldn't update the post: “%s”."
msgstr "Der Beitrag konnte nicht aktualisiert werden: „%s“."

#. module: social_linkedin
#: model:ir.model.fields,field_description:social_linkedin.field_social_post__display_linkedin_preview
#: model:ir.model.fields,field_description:social_linkedin.field_social_post_template__display_linkedin_preview
msgid "Display LinkedIn Preview"
msgstr "LinkedIn-Vorschau anzeigen"

#. module: social_linkedin
#. odoo-python
#: code:addons/social_linkedin/models/social_stream_post.py:0
msgid "Failed to post the comment: “%(error_description)s”"
msgstr "Fehler beim Veröffentlichen des Kommentars: „%(error_description)s“"

#. module: social_linkedin
#. odoo-python
#: code:addons/social_linkedin/models/social_stream_post.py:0
msgid ""
"Failed to retrieve the post. It might have been deleted or you may not have "
"permission to view it."
msgstr ""
"Der Beitrag konnte nicht abgerufen werden. Möglicherweise wurde er gelöscht "
"oder Sie haben keine Berechtigung, ihn anzuzeigen."

#. module: social_linkedin
#: model:ir.model.fields,field_description:social_linkedin.field_social_post__has_linkedin_account
#: model:ir.model.fields,field_description:social_linkedin.field_social_post_template__has_linkedin_account
msgid "Has LinkedIn Account"
msgstr "Hat LinkedIn-Konto"

#. module: social_linkedin
#: model_terms:ir.ui.view,arch_db:social_linkedin.social_post_template_view_form
msgid "Images"
msgstr "Bilder"

#. module: social_linkedin
#. odoo-javascript
#: code:addons/social_linkedin/static/src/xml/social_linkedin_templates.xml:0
msgid "Likes"
msgstr "Likes"

#. module: social_linkedin
#: model:ir.model.fields.selection,name:social_linkedin.selection__social_media__media_type__linkedin
#: model:social.media,name:social_linkedin.social_media_linkedin
#: model_terms:ir.ui.view,arch_db:social_linkedin.social_post_template_view_form
msgid "LinkedIn"
msgstr "LinkedIn"

#. module: social_linkedin
#: model:ir.model.fields,field_description:social_linkedin.field_social_account__linkedin_account_id
msgid "LinkedIn Account ID"
msgstr "LinkedIn-Konto-ID"

#. module: social_linkedin
#: model:ir.model.fields,field_description:social_linkedin.field_social_account__linkedin_account_urn
#: model:ir.model.fields,help:social_linkedin.field_social_account__linkedin_account_urn
msgid "LinkedIn Account URN"
msgstr "LinkedIn-Konto-URN"

#. module: social_linkedin
#. odoo-javascript
#: code:addons/social_linkedin/static/src/js/stream_post_kanban_record.js:0
#: model:ir.model.fields,field_description:social_linkedin.field_social_stream_post__linkedin_comments_count
msgid "LinkedIn Comments"
msgstr "LinkedIn-Kommentare"

#. module: social_linkedin
#: model:ir.model.fields,field_description:social_linkedin.field_social_post__linkedin_image_ids
#: model:ir.model.fields,field_description:social_linkedin.field_social_post_template__linkedin_image_ids
msgid "LinkedIn Images"
msgstr "LinkedIn-Bilder"

#. module: social_linkedin
#: model:ir.model.fields,field_description:social_linkedin.field_social_stream_post__linkedin_likes_count
msgid "LinkedIn Likes"
msgstr "LinkedIn-Likes"

#. module: social_linkedin
#: model:ir.model.fields,field_description:social_linkedin.field_social_post__linkedin_message
#: model:ir.model.fields,field_description:social_linkedin.field_social_post_template__linkedin_message
msgid "LinkedIn Message"
msgstr "LinkedIn-Nachricht"

#. module: social_linkedin
#: model:ir.model.fields,field_description:social_linkedin.field_social_post__linkedin_preview
#: model:ir.model.fields,field_description:social_linkedin.field_social_post_template__linkedin_preview
msgid "LinkedIn Preview"
msgstr "LinkedIn-Vorschau"

#. module: social_linkedin
#: model:ir.model.fields,field_description:social_linkedin.field_social_stream_post__linkedin_author_vanity_name
msgid "LinkedIn Vanity Name"
msgstr "LinkedIn-Vanity-Name"

#. module: social_linkedin
#: model:ir.model.fields,field_description:social_linkedin.field_social_account__linkedin_access_token
msgid "LinkedIn access token"
msgstr "LinkedIn-Zugriffstoken"

#. module: social_linkedin
#: model:ir.model.fields,field_description:social_linkedin.field_social_stream_post__linkedin_author_id
msgid "LinkedIn author ID"
msgstr "LinkedIn-Autor-ID"

#. module: social_linkedin
#: model:ir.model.fields,field_description:social_linkedin.field_social_stream_post__linkedin_author_urn
msgid "LinkedIn author URN"
msgstr "URN des LinkedIn-Autors"

#. module: social_linkedin
#: model:ir.model.fields,field_description:social_linkedin.field_social_stream_post__linkedin_author_image_url
msgid "LinkedIn author image URL"
msgstr "URL des LinkedIn-Autorenbild"

#. module: social_linkedin
#. odoo-python
#: code:addons/social_linkedin/controllers/main.py:0
msgid "LinkedIn did not provide a valid access token."
msgstr "LinkedIn hat kein gültiges Zugriffstoken zur Verfügung gestellt."

#. module: social_linkedin
#: model:ir.model.fields,field_description:social_linkedin.field_social_stream_post__linkedin_post_urn
msgid "LinkedIn post URN"
msgstr "URN des LinkedIn-Beitrags"

#. module: social_linkedin
#: model_terms:ir.ui.view,arch_db:social_linkedin.res_config_settings_view_form
msgid "Linkedin Developer Account"
msgstr "Linkedin-Entwicklerkonto"

#. module: social_linkedin
#: model:ir.model.fields,field_description:social_linkedin.field_social_media__media_type
msgid "Media Type"
msgstr "Medientyp"

#. module: social_linkedin
#: model_terms:ir.ui.view,arch_db:social_linkedin.social_post_template_view_form
msgid "Message"
msgstr "Nachricht"

#. module: social_linkedin
#. odoo-python
#: code:addons/social_linkedin/models/social_post_template.py:0
msgid ""
"Please specify either a LinkedIn Message or upload some LinkedIn Images."
msgstr ""
"Bitte geben Sie entweder eine LinkedIn-Nachricht ein oder laden Sie einige "
"LinkedIn-Bilder hoch."

#. module: social_linkedin
#: model_terms:ir.ui.view,arch_db:social_linkedin.linkedin_preview
msgid "Post Image"
msgstr "Bild posten"

#. module: social_linkedin
#: model:social.stream.type,name:social_linkedin.stream_type_linkedin_company_post
msgid "Posts"
msgstr "Beiträge"

#. module: social_linkedin
#. odoo-python
#: code:addons/social_linkedin/models/social_account.py:0
msgid "Read More about Business Accounts"
msgstr "Mehr über Firmenkonten erfahren"

#. module: social_linkedin
#: model:ir.model,name:social_linkedin.model_social_account
msgid "Social Account"
msgstr "Social-Media-Konto"

#. module: social_linkedin
#: model:ir.model,name:social_linkedin.model_social_live_post
msgid "Social Live Post"
msgstr "Live-Beitrag in Social Media"

#. module: social_linkedin
#: model:ir.model,name:social_linkedin.model_social_media
msgid "Social Media"
msgstr "Social Media"

#. module: social_linkedin
#: model:ir.model,name:social_linkedin.model_social_post
msgid "Social Post"
msgstr "Social-Media-Beitrag"

#. module: social_linkedin
#: model:ir.model,name:social_linkedin.model_social_post_template
msgid "Social Post Template"
msgstr "Vorlage für Social-Media-Beiträge"

#. module: social_linkedin
#: model:ir.model,name:social_linkedin.model_social_stream
msgid "Social Stream"
msgstr "Social-Media-Stream"

#. module: social_linkedin
#: model:ir.model,name:social_linkedin.model_social_stream_post
msgid "Social Stream Post"
msgstr "Beitrag aus Social-Media-Stream"

#. module: social_linkedin
#: model:ir.model.fields,help:social_linkedin.field_social_account__linkedin_access_token
msgid "The access token is used to perform request to the REST API"
msgstr ""
"Das Zugriffstoken wird verwendet, um eine Anfrage an die REST-API zu "
"stellen."

#. module: social_linkedin
#. odoo-python
#: code:addons/social_linkedin/models/social_media.py:0
msgid ""
"The url that this service requested returned an error. Please contact the "
"author of the app."
msgstr ""
"Die von diesem Dienst angeforderte URL hat einen Fehler gemeldet. Bitte "
"kontaktieren Sie den Autor der App."

#. module: social_linkedin
#. odoo-python
#: code:addons/social_linkedin/controllers/main.py:0
msgid "There was a authentication issue during your request."
msgstr "Bei Ihrer Anfrage gab es ein Authentifizierungsproblem."

#. module: social_linkedin
#. odoo-python
#: code:addons/social_linkedin/controllers/main.py:0
msgid "Unauthorized. Please contact your administrator."
msgstr "Nicht autorisiert. Bitte wenden Sie sich an Ihren Administrator."

#. module: social_linkedin
#. odoo-python
#: code:addons/social_linkedin/models/social_stream.py:0
#: code:addons/social_linkedin/models/social_stream_post.py:0
msgid "Unknown"
msgstr "Unbekannt"

#. module: social_linkedin
#: model:ir.model.fields,field_description:social_linkedin.field_res_config_settings__linkedin_use_own_account
msgid "Use your own LinkedIn Account"
msgstr "Benutzen Sie Ihr eigenes LinkedIn-Konto"

#. module: social_linkedin
#: model:ir.model.fields,help:social_linkedin.field_social_media__media_type
msgid ""
"Used to make comparisons when we need to restrict some features to a "
"specific media ('facebook', 'x', ...)."
msgstr ""
"Wird verwendet, um Vergleiche anzustellen, wenn wir einige Funktionen auf "
"ein bestimmtes Medium beschränken müssen („Facebook“, „X“ ...)."

#. module: social_linkedin
#: model:ir.model.fields,help:social_linkedin.field_social_stream_post__linkedin_author_vanity_name
msgid "Vanity name, used to generate a link to the author"
msgstr "Vanity-Name, der verwendet wird, um einen Link zum Autor zu erzeugen"

#. module: social_linkedin
#. odoo-python
#: code:addons/social_linkedin/models/social_account.py:0
msgid ""
"We could not upload your image, try reducing its size and posting it again "
"(error: Failed during upload registering)."
msgstr ""
"Wir konnten Ihr Bild nicht hochladen. Versuchen Sie, es zu verkleinern und "
"erneut hochzuladen (Fehler: Fehlgeschlagen während Upload-Registrierung)."

#. module: social_linkedin
#. odoo-python
#: code:addons/social_linkedin/models/social_account.py:0
msgid ""
"We could not upload your image, try reducing its size and posting it again."
msgstr ""
"Wir konnten Ihr Bild nicht hochladen. Versuchen Sie, es zu verkleinern und "
"erneut zu posten."

#. module: social_linkedin
#: model:ir.model.fields,help:social_linkedin.field_social_post__linkedin_image_ids
#: model:ir.model.fields,help:social_linkedin.field_social_post_template__linkedin_image_ids
msgid "Will attach images to your posts."
msgstr "Ihrem Beitrag werden Bilder angehängt."

#. module: social_linkedin
#. odoo-python
#: code:addons/social_linkedin/models/social_stream.py:0
msgid "Wrong stream type for \"%s\""
msgstr "Falscher Streamtyp für „%s“"

#. module: social_linkedin
#. odoo-python
#: code:addons/social_linkedin/models/social_media.py:0
msgid "You don't have an active subscription. Please buy one here: %s"
msgstr "Sie haben kein aktives Abo. Bitte erwerben sie eins hier: %s"

#. module: social_linkedin
#. odoo-python
#: code:addons/social_linkedin/models/social_account.py:0
msgid ""
"You need a Business Account to post on LinkedIn with Odoo Social.\n"
" Please create one and make sure it is linked to your account"
msgstr ""
"Sie benötigen ein Firmenkonto, um mit Odoo Social auf Linkedin zu posten.\n"
" Bitte erstellen Sie eins und stellen Sie sicher, dass es mit Ihrem Konto verknüpft ist"

#. module: social_linkedin
#. odoo-python
#: code:addons/social_linkedin/models/social_live_post.py:0
msgid "unknown"
msgstr "unbekannt"
