{
    'name': 'Hide Product Cost',
    'version': '********',
    'category': 'Inventory/Inventory',
    'summary': 'Hide product cost price based on user group',
    'description': """This module adds a security group that controls the visibility of product cost prices.
    Only users who belong to the 'Show Product Cost' group will be able to see the cost price
    of products in the product form view and other related views.
    """,
    'depends': ['product','stock'],
    'data': [
        'security/security.xml',
        'views/product_views.xml',
    ],
    'installable': True,
    'auto_install': False,
    'license': 'LGPL-3',
}
