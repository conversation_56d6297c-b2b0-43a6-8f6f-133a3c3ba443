# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* delivery_sendcloud
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON> <alina.lisnen<PERSON>@erp.co.ua>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-02-10 15:41+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Ukrainian (https://app.transifex.com/odoo/teams/41243/uk/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: uk\n"
"Plural-Forms: nplurals=4; plural=(n % 1 == 0 && n % 10 == 1 && n % 100 != 11 ? 0 : n % 1 == 0 && n % 10 >= 2 && n % 10 <= 4 && (n % 100 < 12 || n % 100 > 14) ? 1 : n % 1 == 0 && (n % 10 ==0 || (n % 10 >=5 && n % 10 <=9) || (n % 100 >=11 && n % 100 <=14 )) ? 2: 3);\n"

#. module: delivery_sendcloud
#. odoo-python
#: code:addons/delivery_sendcloud/models/sendcloud_service.py:0
msgid ""
"\n"
"Additionally, some individual product(s) are too heavy for the heaviest available shipping method.\n"
"                             \n"
"Divide the quantity of the following product(s) across your packages if possible or choose another carrier:\n"
"\t%s"
msgstr ""
"\n"
"Крім того, деякі окремі товари є занадто важкими для найважчого способу доставки.\n"
"                             \n"
"Якщо це можливо, розподіліть кількість наведених нижче товарів між своїми упаковками або виберіть іншого перевізника:\n"
"\t%s"

#. module: delivery_sendcloud
#. odoo-python
#: code:addons/delivery_sendcloud/models/sendcloud_service.py:0
msgid ""
"\n"
"Try to distribute your products across your packages so that they weigh less than %(max_weight)s %(unit)s or choose another carrier."
msgstr ""
"\n"
"Спробуйте розподілити товари по упаковках так, щоб вони важили менше ніж %(max_weight)s %(unit)s або виберіть іншого перевізника."

#. module: delivery_sendcloud
#. odoo-python
#: code:addons/delivery_sendcloud/models/sendcloud_service.py:0
msgid "%(partner_name)s email required"
msgstr "%(partner_name)s потрібен email"

#. module: delivery_sendcloud
#. odoo-python
#: code:addons/delivery_sendcloud/models/sendcloud_service.py:0
msgid "%(partner_name)s phone required"
msgstr "%(partner_name)s потрібен телефон"

#. module: delivery_sendcloud
#: model_terms:ir.ui.view,arch_db:delivery_sendcloud.view_delivery_carrier_form
msgid ""
"<i class=\"fa fa-exclamation-triangle\"/> Do not forget to load your "
"SendCloud shipping products for a valid configuration."
msgstr ""
"<i class=\"fa fa-exclamation-triangle\"/> Не забудьте завантажити свої "
"товари доставки SendCloud для дійсної конфігурації."

#. module: delivery_sendcloud
#: model_terms:ir.ui.view,arch_db:delivery_sendcloud.view_delivery_carrier_form
msgid ""
"<i class=\"fa fa-exclamation-triangle\"/> In test environment, to avoid "
"charges, your shippings are automatically <b>cancelled</b> after the label "
"creation."
msgstr ""
"<i class=\"fa fa-exclamation-triangle\"/> У тестовому середовищі, для "
"уникнення стягнень, ваші доставки автаматично <b>скасовуються</b> після "
"створення."

#. module: delivery_sendcloud
#: model_terms:ir.ui.view,arch_db:delivery_sendcloud.view_delivery_carrier_form
msgid ""
"<i class=\"fa fa-exclamation-triangle\"/> Only administrators can configure "
"the public and private keys."
msgstr ""
"<i class=\"fa fa-exclamation-triangle\"/> Лише адміністратори можуть "
"налаштувати відкритий і закритий ключі."

#. module: delivery_sendcloud
#: model_terms:ir.ui.view,arch_db:delivery_sendcloud.view_shipping_method_sendcloud
msgid ""
"<i class=\"fa fa-info-circle\"/> Available shipping products depend on "
"enabled carriers in your Sendcloud account."
msgstr ""
"<i class=\"fa fa-info-circle\"/> Доступні товари доставки залежать від "
"увімкнених перевізників в обліковому записі Sendcloud."

#. module: delivery_sendcloud
#: model:ir.model.fields,field_description:delivery_sendcloud.field_sendcloud_shipping_wizard__sendcloud_products_code
msgid "Active Products Code"
msgstr "Активний код товару"

#. module: delivery_sendcloud
#: model:ir.model.fields,field_description:delivery_sendcloud.field_sendcloud_shipping_product__functionalities
msgid "Available Functionalities"
msgstr "Функції доступності"

#. module: delivery_sendcloud
#: model:ir.model.fields,field_description:delivery_sendcloud.field_delivery_carrier__sendcloud_has_custom_functionalities
#: model:ir.model.fields,field_description:delivery_sendcloud.field_sendcloud_shipping_product__can_customize_functionalities
msgid "Can Customize Functionalities"
msgstr "Можна кастомізувати функціональності"

#. module: delivery_sendcloud
#: model_terms:ir.ui.view,arch_db:delivery_sendcloud.view_shipping_method_sendcloud
msgid "Cancel"
msgstr "Скасувати"

#. module: delivery_sendcloud
#. odoo-javascript
#: code:addons/delivery_sendcloud/static/src/views/sendcloud_product_selection_widget.xml:0
#: model:ir.model.fields,field_description:delivery_sendcloud.field_stock_package_type__package_carrier_type
msgid "Carrier"
msgstr "Перевізник"

#. module: delivery_sendcloud
#. odoo-python
#: code:addons/delivery_sendcloud/models/delivery_carrier.py:0
msgid "Choose Sendcloud Shipping Products"
msgstr "Оберіть товари доставки Sendcloud"

#. module: delivery_sendcloud
#: model:ir.model,name:delivery_sendcloud.model_sendcloud_shipping_wizard
msgid "Choose from the available sendcloud shipping methods"
msgstr "Оберіть з доступних способів доставки sendcloud"

#. module: delivery_sendcloud
#: model:ir.model,name:delivery_sendcloud.model_sendcloud_shipping_product
msgid "Choose from the available sendcloud shipping products"
msgstr "Оберіть з доступних товарів доставки sendcloud"

#. module: delivery_sendcloud
#: model_terms:ir.ui.view,arch_db:delivery_sendcloud.view_shipping_method_sendcloud
msgid "Confirm"
msgstr "Підтвердити"

#. module: delivery_sendcloud
#. odoo-python
#: code:addons/delivery_sendcloud/models/sendcloud_service.py:0
msgid "Could not find currency %s"
msgstr "Неможливо знайти валюту %s"

#. module: delivery_sendcloud
#. odoo-python
#: code:addons/delivery_sendcloud/models/sendcloud_service.py:0
msgid "Could not get document!"
msgstr "Неможливо отримати документ!"

#. module: delivery_sendcloud
#: model:ir.model.fields,field_description:delivery_sendcloud.field_sendcloud_shipping_product__create_uid
#: model:ir.model.fields,field_description:delivery_sendcloud.field_sendcloud_shipping_wizard__create_uid
msgid "Created by"
msgstr "Створив"

#. module: delivery_sendcloud
#: model:ir.model.fields,field_description:delivery_sendcloud.field_sendcloud_shipping_product__create_date
#: model:ir.model.fields,field_description:delivery_sendcloud.field_sendcloud_shipping_wizard__create_date
msgid "Created on"
msgstr "Створено"

#. module: delivery_sendcloud
#: model_terms:ir.ui.view,arch_db:delivery_sendcloud.view_delivery_carrier_form
msgid "Default Package Type"
msgstr "Тип упаковки за замовчуванням"

#. module: delivery_sendcloud
#: model:ir.model.fields,field_description:delivery_sendcloud.field_delivery_carrier__sendcloud_default_package_type_id
msgid "Default Package Type for Sendcloud"
msgstr "Тип упаковки за замовчуванням для Sendcloud"

#. module: delivery_sendcloud
#: model:ir.model.fields,field_description:delivery_sendcloud.field_sendcloud_shipping_wizard__carrier_id
msgid "Delivery"
msgstr "Доставка"

#. module: delivery_sendcloud
#: model_terms:ir.ui.view,arch_db:delivery_sendcloud.view_shipping_method_sendcloud
msgid "Delivery Product"
msgstr "Товар доставки"

#. module: delivery_sendcloud
#: model:ir.model.fields,help:delivery_sendcloud.field_delivery_carrier__sendcloud_shipping_rules
msgid ""
"Depending your Sendcloud account type, through rules you can define the shipping method to use depending on different conditions like destination, weight, value, etc.\n"
"Rules can override shipping product selected in Odoo"
msgstr ""
"В залежності від типу вашого облікового запису Sendcloud, через правила ви можете визначати спосіб доставки, використовуючи залежності різних умов, таких як місце призначення, ширина, вартість тощо.\n"
"Правила можуть мати перевагу над доставкою товару, обраною в Odoo"

#. module: delivery_sendcloud
#: model:ir.model.fields,field_description:delivery_sendcloud.field_sendcloud_shipping_product__display_name
#: model:ir.model.fields,field_description:delivery_sendcloud.field_sendcloud_shipping_wizard__display_name
msgid "Display Name"
msgstr "Назва для відображення"

#. module: delivery_sendcloud
#. odoo-python
#: code:addons/delivery_sendcloud/models/sendcloud_service.py:0
msgid ""
"Each address line can only contain a maximum of 75 characters. You can split"
" the address into multiple lines to try to avoid this limitation."
msgstr ""
"Кожен адресний рядок може містити не більше 75 символів. Ви можете розділити"
" адресу на кілька рядків, щоб спробувати уникнути цього обмеження."

#. module: delivery_sendcloud
#. odoo-python
#: code:addons/delivery_sendcloud/models/sendcloud_service.py:0
msgid ""
"Ensure picking has shipping weight, if using packages, each package should "
"have a shipping weight"
msgstr ""
"Переконайтеся, що комплектування має вагу транспортування, якщо "
"використовуються упаковки, кожна упаковка має мати вагу транспортування"

#. module: delivery_sendcloud
#. odoo-python
#: code:addons/delivery_sendcloud/models/delivery_carrier.py:0
msgid "Failed to create the return label!"
msgstr "Не вдалося створити етикетку повернення!"

#. module: delivery_sendcloud
#. odoo-python
#: code:addons/delivery_sendcloud/models/delivery_carrier.py:0
msgid "Failed to get the actual price!"
msgstr "Не вдалося отримати актуальну ціну!"

#. module: delivery_sendcloud
#: model:ir.model.fields,field_description:delivery_sendcloud.field_delivery_carrier__sendcloud_product_functionalities
msgid "Functionalities"
msgstr "Функціональні можливості"

#. module: delivery_sendcloud
#: model_terms:ir.ui.view,arch_db:delivery_sendcloud.view_delivery_carrier_form
msgid "Functionality Filters"
msgstr "Функіональні фільтри"

#. module: delivery_sendcloud
#. odoo-python
#: code:addons/delivery_sendcloud/models/delivery_carrier.py:0
msgid "Go to the shipping product"
msgstr "Перейдіть до товару доставки"

#. module: delivery_sendcloud
#: model:ir.model.fields,field_description:delivery_sendcloud.field_delivery_carrier__sendcloud_can_batch_shipping
#: model:ir.model.fields,field_description:delivery_sendcloud.field_sendcloud_shipping_product__has_multicollo
msgid "Has Multicollo"
msgstr "Має Multicollo"

#. module: delivery_sendcloud
#: model:ir.model.fields,field_description:delivery_sendcloud.field_sendcloud_shipping_product__id
#: model:ir.model.fields,field_description:delivery_sendcloud.field_sendcloud_shipping_wizard__id
msgid "ID"
msgstr "ID"

#. module: delivery_sendcloud
#: model_terms:ir.ui.view,arch_db:delivery_sendcloud.view_delivery_carrier_form
msgid "Integration"
msgstr "Інтеграція"

#. module: delivery_sendcloud
#: model:ir.model.fields,field_description:delivery_sendcloud.field_sendcloud_shipping_product__write_uid
#: model:ir.model.fields,field_description:delivery_sendcloud.field_sendcloud_shipping_wizard__write_uid
msgid "Last Updated by"
msgstr "Востаннє оновив"

#. module: delivery_sendcloud
#: model:ir.model.fields,field_description:delivery_sendcloud.field_sendcloud_shipping_product__write_date
#: model:ir.model.fields,field_description:delivery_sendcloud.field_sendcloud_shipping_wizard__write_date
msgid "Last Updated on"
msgstr "Останнє оновлення"

#. module: delivery_sendcloud
#: model_terms:ir.ui.view,arch_db:delivery_sendcloud.view_delivery_carrier_form
msgid "Load your products"
msgstr "Завантажте ваші товари"

#. module: delivery_sendcloud
#. odoo-python
#: code:addons/delivery_sendcloud/models/sendcloud_service.py:0
msgid ""
"Make sure country codes are set in partner country and warehouse country"
msgstr ""
"Переконайтеся, що коди країни встановлено в країні-партнері та країні складу"

#. module: delivery_sendcloud
#. odoo-javascript
#: code:addons/delivery_sendcloud/static/src/views/sendcloud_product_selection_widget.xml:0
msgid "Max height"
msgstr "Максимальна висота"

#. module: delivery_sendcloud
#. odoo-javascript
#: code:addons/delivery_sendcloud/static/src/views/sendcloud_product_selection_widget.xml:0
msgid "Max length"
msgstr "Максимальна довжина"

#. module: delivery_sendcloud
#. odoo-javascript
#: code:addons/delivery_sendcloud/static/src/views/sendcloud_product_selection_widget.xml:0
msgid "Max width"
msgstr "Максимальна ширина"

#. module: delivery_sendcloud
#: model:ir.model.fields,field_description:delivery_sendcloud.field_sendcloud_shipping_product__max_weight
msgid "Maximum Weight"
msgstr "Максимальна вага"

#. module: delivery_sendcloud
#: model:ir.model.fields,field_description:delivery_sendcloud.field_sendcloud_shipping_product__min_weight
msgid "Minimum Weight"
msgstr "Мінімальна вага"

#. module: delivery_sendcloud
#. odoo-python
#: code:addons/delivery_sendcloud/models/delivery_carrier.py:0
msgid "Must be a Sendcloud carrier!"
msgstr "Має бути перевізником Sendcloud!"

#. module: delivery_sendcloud
#. odoo-python
#: code:addons/delivery_sendcloud/models/sendcloud_service.py:0
msgid "No address found with contact name %s on your sendcloud account."
msgstr ""
"Жодної адреси не знайдено з контактом %s у вашому обліковому записі "
"sendcloud."

#. module: delivery_sendcloud
#. odoo-python
#: code:addons/delivery_sendcloud/models/sendcloud_service.py:0
msgid "No picking or order provided"
msgstr "Не надано комплектування чи замовлення"

#. module: delivery_sendcloud
#. odoo-python
#: code:addons/delivery_sendcloud/models/delivery_carrier.py:0
msgid ""
"Note that a unit of the product '%s' is heavier than the maximum weight "
"allowed by the shipping method."
msgstr ""
"Занотуйте, що одиниця товару '%s' важча за максимальну вагу дозволену "
"способом доставки."

#. module: delivery_sendcloud
#. odoo-python
#: code:addons/delivery_sendcloud/models/delivery_carrier.py:0
msgid ""
"Note that this price is for %s packages since the order weight is more than "
"the maximum weight allowed by the shipping method."
msgstr ""
"Занотуйте, що ціна для %s упаковок з часу, як вага перевищує максимально "
"дозволену способом доставки."

#. module: delivery_sendcloud
#: model_terms:ir.ui.view,arch_db:delivery_sendcloud.view_delivery_carrier_form
msgid "Options"
msgstr "Опції"

#. module: delivery_sendcloud
#. odoo-python
#: code:addons/delivery_sendcloud/models/sendcloud_service.py:0
msgid "Order below minimum weight of carrier"
msgstr "Замовляйте менше мінімальної ваги перевізника"

#. module: delivery_sendcloud
#. odoo-javascript
#: code:addons/delivery_sendcloud/static/src/views/sendcloud_product_selection_widget.xml:0
msgid "Other Functionalities"
msgstr "Інші можливості"

#. module: delivery_sendcloud
#. odoo-javascript
#: code:addons/delivery_sendcloud/static/src/views/sendcloud_product_selection_widget.xml:0
msgid ""
"Please check SendCloud return product documentation before selecting return "
"product. For some products, you can be charged for return labels printed but"
" not used."
msgstr ""
"Будь ласка, перевірте документацію товару повернення SendCloud, перш ніж "
"вибрати товар повернення. Для деяких товарів з вас можуть стягувати плату за"
" надруковані, але не використані етикетки для повернення."

#. module: delivery_sendcloud
#: model:ir.model.fields,field_description:delivery_sendcloud.field_delivery_carrier__delivery_type
msgid "Provider"
msgstr "Провайдер"

#. module: delivery_sendcloud
#: model_terms:ir.ui.view,arch_db:delivery_sendcloud.view_shipping_method_sendcloud
msgid "Return Product"
msgstr "Повернути товар"

#. module: delivery_sendcloud
#: model:ir.model.fields,field_description:delivery_sendcloud.field_sendcloud_shipping_wizard__return_products
msgid "Return Products"
msgstr "Повернути  товари"

#. module: delivery_sendcloud
#: model:ir.model.fields,help:delivery_sendcloud.field_delivery_carrier__country_id
msgid "Select the country to be used by this delivery method"
msgstr ""

#. module: delivery_sendcloud
#: model_terms:ir.ui.view,arch_db:delivery_sendcloud.view_delivery_carrier_form
msgid "SendCloud Configuration"
msgstr "Налаштування SendCloud"

#. module: delivery_sendcloud
#: model_terms:ir.ui.view,arch_db:delivery_sendcloud.res_config_settings_view_form_sale
msgid "SendCloud Shipping Methods"
msgstr "Способи доставки SendCloud"

#. module: delivery_sendcloud
#: model:ir.model.fields.selection,name:delivery_sendcloud.selection__delivery_carrier__delivery_type__sendcloud
#: model:ir.model.fields.selection,name:delivery_sendcloud.selection__stock_package_type__package_carrier_type__sendcloud
msgid "Sendcloud"
msgstr "Sendcloud"

#. module: delivery_sendcloud
#: model:ir.model.fields,help:delivery_sendcloud.field_delivery_carrier__sendcloud_public_key
msgid "Sendcloud API Integration Public key"
msgstr "Публічний ключ інтеграції API Sendcloud"

#. module: delivery_sendcloud
#: model:ir.model.fields,help:delivery_sendcloud.field_delivery_carrier__sendcloud_secret_key
msgid "Sendcloud API Integration Secret key"
msgstr "Секретний ключ інтеграції API Sendcloud"

#. module: delivery_sendcloud
#: model:ir.model.fields,field_description:delivery_sendcloud.field_stock_picking__sendcloud_parcel_ref
msgid "Sendcloud Parcel Reference"
msgstr "Референс посилки Sendcloud"

#. module: delivery_sendcloud
#: model:ir.model.fields,field_description:delivery_sendcloud.field_sendcloud_shipping_product__sendcloud_code
msgid "Sendcloud Product Identifier"
msgstr "Ідентифікатор товару Sendcloud "

#. module: delivery_sendcloud
#: model:ir.model.fields,field_description:delivery_sendcloud.field_delivery_carrier__sendcloud_public_key
msgid "Sendcloud Public Key"
msgstr "Публічний ключ Sendcloud"

#. module: delivery_sendcloud
#: model:ir.model.fields,field_description:delivery_sendcloud.field_delivery_carrier__sendcloud_return_id
msgid "Sendcloud Return"
msgstr "Повернення Sendcloud"

#. module: delivery_sendcloud
#: model:ir.model.fields,field_description:delivery_sendcloud.field_stock_picking__sendcloud_return_parcel_ref
msgid "Sendcloud Return Parcel Ref"
msgstr "Реф. посилки повернення Sendcloud"

#. module: delivery_sendcloud
#: model:ir.model.fields,field_description:delivery_sendcloud.field_delivery_carrier__sendcloud_return_name
msgid "Sendcloud Return Shipping Product"
msgstr "Товар доставки Sendcloud"

#. module: delivery_sendcloud
#: model:ir.model.fields,field_description:delivery_sendcloud.field_delivery_carrier__sendcloud_secret_key
msgid "Sendcloud Secret Key"
msgstr "Секретний ключ Sendcloud"

#. module: delivery_sendcloud
#: model:ir.model.fields,field_description:delivery_sendcloud.field_delivery_carrier__sendcloud_shipping_id
msgid "Sendcloud Shipping"
msgstr "Доставка Sendcloud"

#. module: delivery_sendcloud
#: model_terms:ir.ui.view,arch_db:delivery_sendcloud.res_config_settings_view_form_stock
msgid "Sendcloud Shipping Methods"
msgstr "Способи доставки Sendcloud"

#. module: delivery_sendcloud
#: model:ir.model.fields,field_description:delivery_sendcloud.field_delivery_carrier__sendcloud_shipping_name
msgid "Sendcloud Shipping Product"
msgstr "Товар доставки Sendcloud"

#. module: delivery_sendcloud
#. odoo-python
#: code:addons/delivery_sendcloud/models/sendcloud_service.py:0
msgid ""
"Sendcloud is not responding. Check your credentials and/or retry later."
msgstr ""

#. module: delivery_sendcloud
#: model:ir.model.fields,field_description:delivery_sendcloud.field_delivery_carrier__country_id
msgid "Ship From"
msgstr "Доставити з"

#. module: delivery_sendcloud
#. odoo-python
#: code:addons/delivery_sendcloud/models/delivery_carrier.py:0
msgid "Shipment %s cancelled"
msgstr "Shipment %s скасовано"

#. module: delivery_sendcloud
#: model:ir.model.fields,field_description:delivery_sendcloud.field_sendcloud_shipping_product__carrier
msgid "Shipping Carrier"
msgstr "Перевізник доставки"

#. module: delivery_sendcloud
#: model:ir.model,name:delivery_sendcloud.model_delivery_carrier
msgid "Shipping Methods"
msgstr "Способи доставки"

#. module: delivery_sendcloud
#: model:ir.model.fields,field_description:delivery_sendcloud.field_sendcloud_shipping_product__name
#: model_terms:ir.ui.view,arch_db:delivery_sendcloud.view_shipping_method_sendcloud
msgid "Shipping Product"
msgstr "Товар доставки"

#. module: delivery_sendcloud
#: model:ir.model.fields,field_description:delivery_sendcloud.field_sendcloud_shipping_wizard__shipping_products
msgid "Shipping Products"
msgstr "Товари доставки"

#. module: delivery_sendcloud
#: model:ir.model.fields,help:delivery_sendcloud.field_delivery_carrier__sendcloud_default_package_type_id
msgid ""
"Some carriers require package dimensions, you can define these in a package "
"type that you set as default"
msgstr ""
"Деякі перевізники вимагають розмірів упаковки, ви можете визначити їх у типі"
" упаковки, який ви встановите за замовчуванням"

#. module: delivery_sendcloud
#. odoo-python
#: code:addons/delivery_sendcloud/models/sendcloud_service.py:0
msgid ""
"Some packages in your transfer are too heavy for the heaviest available "
"shipping method."
msgstr ""
"Деякі пакунки у вашій передачі занадто важкі для найважчого доступного "
"способу доставки."

#. module: delivery_sendcloud
#. odoo-python
#: code:addons/delivery_sendcloud/models/sendcloud_service.py:0
msgid ""
"Something went wrong, parcel not returned from Sendcloud:\n"
" %s'."
msgstr ""
"Щось пішло не так, посилка не повернулася з Sendcloud:\n"
" %s'."

#. module: delivery_sendcloud
#: model:ir.model,name:delivery_sendcloud.model_stock_package_type
msgid "Stock package type"
msgstr "Тип складської упаковки"

#. module: delivery_sendcloud
#. odoo-python
#: code:addons/delivery_sendcloud/models/sendcloud_service.py:0
msgid "The %s address needs to have the street, city, zip, and country"
msgstr "Адреса %s повинна мати вулицю, місто, індекс та країну"

#. module: delivery_sendcloud
#. odoo-python
#: code:addons/delivery_sendcloud/models/sendcloud_service.py:0
msgid ""
"The delivery address of the customer has been removed from the pickup "
"location. This information is required by Sendcloud. Please go to the "
"delivery partner via the delivery order and make sure the parent of the "
"delivery partner is the partner you want to ship to."
msgstr ""
"Адресу доставки клієнта видалено з місця отримання. Ця інформація потрібна "
"Sendcloud. Будь ласка, перейдіть до партнера по доставці через замовлення на"
" доставку та переконайтеся, що батьківський партнер партнера по доставці є "
"партнером, якому ви хочете відправити."

#. module: delivery_sendcloud
#. odoo-python
#: code:addons/delivery_sendcloud/models/sendcloud_service.py:0
msgid ""
"The estimated shipping price cannot be computed because the weight is missing for the following product(s): \n"
" %s"
msgstr ""
"Приблизну ціну доставки неможливо обчислити, оскільки відсутня вага для таких товарів: \n"
" %s"

#. module: delivery_sendcloud
#. odoo-python
#: code:addons/delivery_sendcloud/models/delivery_carrier.py:0
msgid "The shipping product actually configured can't handle this delivery"
msgstr "Фактично налаштований товар доставки не може обробити цю доставку"

#. module: delivery_sendcloud
#. odoo-python
#: code:addons/delivery_sendcloud/models/sendcloud_service.py:0
msgid ""
"The total weight of your transfer is too heavy for the heaviest available "
"shipping method."
msgstr ""
"Загальна вага вашого переказу завелика для найважчого доступного способу "
"доставки."

#. module: delivery_sendcloud
#. odoo-python
#: code:addons/delivery_sendcloud/models/delivery_carrier.py:0
msgid ""
"There are no shipping products available, please update the 'Shipping From' "
"field or activate suitable carriers in your sendcloud account"
msgstr ""
"Немає доступних товарів для доставки, оновіть поле «Відправлення» або "
"активуйте відповідних перевізників у своєму обліковому записі sendcloud"

#. module: delivery_sendcloud
#. odoo-python
#: code:addons/delivery_sendcloud/models/delivery_carrier.py:0
msgid ""
"There is no rate available for this order with the selected shipping "
"product."
msgstr ""

#. module: delivery_sendcloud
#. odoo-python
#: code:addons/delivery_sendcloud/models/sendcloud_service.py:0
msgid ""
"There is no shipping method available for this order with the selected "
"carrier"
msgstr ""
"Для цього замовлення з обраним перевізником немає доступного способу "
"доставки"

#. module: delivery_sendcloud
#. odoo-python
#: code:addons/delivery_sendcloud/models/sendcloud_service.py:0
msgid ""
"There is no shipping method available for this picking with the selected "
"carrier"
msgstr ""
"Для цього надходження немає доступного способу доставки з обраним "
"перевізником"

#. module: delivery_sendcloud
#. odoo-python
#: code:addons/delivery_sendcloud/models/sendcloud_service.py:0
msgid ""
"There's no method with matching weight range for packages :\n"
"%s\n"
"You can either choose another carrier, change your filters or redefine the content of your package(s)."
msgstr ""
"Немає методу зі збігом діапазону ваги для упаковок:\n"
"%s\n"
"Ви можете вибрати іншого перевізника, змінити фільтри або перевизначити вміст своїх упаковок."

#. module: delivery_sendcloud
#. odoo-python
#: code:addons/delivery_sendcloud/models/sendcloud_service.py:0
msgid ""
"There's no shipping method matching all your selected filters for this "
"picking/order."
msgstr ""
"Немає способу доставки, який би відповідав усім вибраним фільтрам для цього "
"комплектування/замовлення."

#. module: delivery_sendcloud
#. odoo-python
#: code:addons/delivery_sendcloud/models/delivery_carrier.py:0
msgid "There's no unit of measure with the name \"%s\"."
msgstr "Немає одиниці вимірювання з назвою \"%s\"."

#. module: delivery_sendcloud
#: model:ir.model,name:delivery_sendcloud.model_stock_picking
msgid "Transfer"
msgstr "Переміщення"

#. module: delivery_sendcloud
#: model:ir.model.fields,field_description:delivery_sendcloud.field_delivery_carrier__sendcloud_use_batch_shipping
msgid "Use Batch Shipping"
msgstr "Використовувати групову доставку"

#. module: delivery_sendcloud
#: model:ir.model.fields,field_description:delivery_sendcloud.field_delivery_carrier__sendcloud_shipping_rules
msgid "Use Sendcloud shipping rules"
msgstr "Використовуйте правила доставки Sendcloud"

#. module: delivery_sendcloud
#. odoo-javascript
#: code:addons/delivery_sendcloud/static/src/views/sendcloud_product_selection_widget.xml:0
msgid "Weight range"
msgstr "Діапазон ваги"

#. module: delivery_sendcloud
#: model:ir.model.fields,help:delivery_sendcloud.field_delivery_carrier__sendcloud_use_batch_shipping
msgid ""
"When sending multiple parcels, combine them in one shipment. Not supported "
"for international shipping requiring customs' documentation"
msgstr ""
"Відправляючи кілька посилок, об’єднайте їх в одне відправлення. Не "
"підтримується для міжнародних перевезень, для яких потрібна митна "
"документація"

#. module: delivery_sendcloud
#. odoo-python
#: code:addons/delivery_sendcloud/models/delivery_carrier.py:0
msgid "You must add your public and secret key for sendcloud delivery type!"
msgstr ""
"Ви повинні додати ваш публічний або секретний ключ для типу доставки "
"sendcloud!"

#. module: delivery_sendcloud
#. odoo-python
#: code:addons/delivery_sendcloud/models/delivery_carrier.py:0
msgid ""
"You must assign the required 'Shipping From' field in order to search for "
"available products"
msgstr ""
"Ви повинні призначити обов’язкове поле «Відправити з», щоб шукати доступні "
"товари"

#. module: delivery_sendcloud
#. odoo-python
#: code:addons/delivery_sendcloud/models/delivery_carrier.py:0
msgid "You must have a shipping product configured!"
msgstr "Ви повинні налаштувати товар доставки!"

#. module: delivery_sendcloud
#. odoo-javascript
#: code:addons/delivery_sendcloud/static/src/views/sendcloud_product_selection_widget.xml:0
msgid "cm"
msgstr "см"

#. module: delivery_sendcloud
#: model_terms:ir.ui.view,arch_db:delivery_sendcloud.sendcloud_label_tracking
msgid ""
"created in Sendcloud. <br/>\n"
"            <b>Tracking Numbers:</b>"
msgstr ""
"створено у Sendcloud. <br/>\n"
"            <b>Номери відстеження:</b>"
