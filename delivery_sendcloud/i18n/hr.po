# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* delivery_sendcloud
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON>, 2024
# <PERSON><PERSON><PERSON> <karol<PERSON>.ton<PERSON>@storm.hr>, 2024
# <PERSON>, 2024
# <PERSON>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-02-10 15:41+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON><PERSON> <gklis<PERSON>@gmail.com>, 2025\n"
"Language-Team: Croatian (https://app.transifex.com/odoo/teams/41243/hr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: hr\n"
"Plural-Forms: nplurals=3; plural=n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2;\n"

#. module: delivery_sendcloud
#. odoo-python
#: code:addons/delivery_sendcloud/models/sendcloud_service.py:0
msgid ""
"\n"
"Additionally, some individual product(s) are too heavy for the heaviest available shipping method.\n"
"                             \n"
"Divide the quantity of the following product(s) across your packages if possible or choose another carrier:\n"
"\t%s"
msgstr ""

#. module: delivery_sendcloud
#. odoo-python
#: code:addons/delivery_sendcloud/models/sendcloud_service.py:0
msgid ""
"\n"
"Try to distribute your products across your packages so that they weigh less than %(max_weight)s %(unit)s or choose another carrier."
msgstr ""

#. module: delivery_sendcloud
#. odoo-python
#: code:addons/delivery_sendcloud/models/sendcloud_service.py:0
msgid "%(partner_name)s email required"
msgstr ""

#. module: delivery_sendcloud
#. odoo-python
#: code:addons/delivery_sendcloud/models/sendcloud_service.py:0
msgid "%(partner_name)s phone required"
msgstr ""

#. module: delivery_sendcloud
#: model_terms:ir.ui.view,arch_db:delivery_sendcloud.view_delivery_carrier_form
msgid ""
"<i class=\"fa fa-exclamation-triangle\"/> Do not forget to load your "
"SendCloud shipping products for a valid configuration."
msgstr ""

#. module: delivery_sendcloud
#: model_terms:ir.ui.view,arch_db:delivery_sendcloud.view_delivery_carrier_form
msgid ""
"<i class=\"fa fa-exclamation-triangle\"/> In test environment, to avoid "
"charges, your shippings are automatically <b>cancelled</b> after the label "
"creation."
msgstr ""

#. module: delivery_sendcloud
#: model_terms:ir.ui.view,arch_db:delivery_sendcloud.view_delivery_carrier_form
msgid ""
"<i class=\"fa fa-exclamation-triangle\"/> Only administrators can configure "
"the public and private keys."
msgstr ""

#. module: delivery_sendcloud
#: model_terms:ir.ui.view,arch_db:delivery_sendcloud.view_shipping_method_sendcloud
msgid ""
"<i class=\"fa fa-info-circle\"/> Available shipping products depend on "
"enabled carriers in your Sendcloud account."
msgstr ""

#. module: delivery_sendcloud
#: model:ir.model.fields,field_description:delivery_sendcloud.field_sendcloud_shipping_wizard__sendcloud_products_code
msgid "Active Products Code"
msgstr ""

#. module: delivery_sendcloud
#: model:ir.model.fields,field_description:delivery_sendcloud.field_sendcloud_shipping_product__functionalities
msgid "Available Functionalities"
msgstr ""

#. module: delivery_sendcloud
#: model:ir.model.fields,field_description:delivery_sendcloud.field_delivery_carrier__sendcloud_has_custom_functionalities
#: model:ir.model.fields,field_description:delivery_sendcloud.field_sendcloud_shipping_product__can_customize_functionalities
msgid "Can Customize Functionalities"
msgstr ""

#. module: delivery_sendcloud
#: model_terms:ir.ui.view,arch_db:delivery_sendcloud.view_shipping_method_sendcloud
msgid "Cancel"
msgstr "Otkaži"

#. module: delivery_sendcloud
#. odoo-javascript
#: code:addons/delivery_sendcloud/static/src/views/sendcloud_product_selection_widget.xml:0
#: model:ir.model.fields,field_description:delivery_sendcloud.field_stock_package_type__package_carrier_type
msgid "Carrier"
msgstr "Prijevoznik"

#. module: delivery_sendcloud
#. odoo-python
#: code:addons/delivery_sendcloud/models/delivery_carrier.py:0
msgid "Choose Sendcloud Shipping Products"
msgstr ""

#. module: delivery_sendcloud
#: model:ir.model,name:delivery_sendcloud.model_sendcloud_shipping_wizard
msgid "Choose from the available sendcloud shipping methods"
msgstr ""

#. module: delivery_sendcloud
#: model:ir.model,name:delivery_sendcloud.model_sendcloud_shipping_product
msgid "Choose from the available sendcloud shipping products"
msgstr ""

#. module: delivery_sendcloud
#: model_terms:ir.ui.view,arch_db:delivery_sendcloud.view_shipping_method_sendcloud
msgid "Confirm"
msgstr "Potvrdi"

#. module: delivery_sendcloud
#. odoo-python
#: code:addons/delivery_sendcloud/models/sendcloud_service.py:0
msgid "Could not find currency %s"
msgstr ""

#. module: delivery_sendcloud
#. odoo-python
#: code:addons/delivery_sendcloud/models/sendcloud_service.py:0
msgid "Could not get document!"
msgstr ""

#. module: delivery_sendcloud
#: model:ir.model.fields,field_description:delivery_sendcloud.field_sendcloud_shipping_product__create_uid
#: model:ir.model.fields,field_description:delivery_sendcloud.field_sendcloud_shipping_wizard__create_uid
msgid "Created by"
msgstr "Kreirao"

#. module: delivery_sendcloud
#: model:ir.model.fields,field_description:delivery_sendcloud.field_sendcloud_shipping_product__create_date
#: model:ir.model.fields,field_description:delivery_sendcloud.field_sendcloud_shipping_wizard__create_date
msgid "Created on"
msgstr "Kreirano"

#. module: delivery_sendcloud
#: model_terms:ir.ui.view,arch_db:delivery_sendcloud.view_delivery_carrier_form
msgid "Default Package Type"
msgstr "Zadani tip paketa"

#. module: delivery_sendcloud
#: model:ir.model.fields,field_description:delivery_sendcloud.field_delivery_carrier__sendcloud_default_package_type_id
msgid "Default Package Type for Sendcloud"
msgstr ""

#. module: delivery_sendcloud
#: model:ir.model.fields,field_description:delivery_sendcloud.field_sendcloud_shipping_wizard__carrier_id
msgid "Delivery"
msgstr "Dostava"

#. module: delivery_sendcloud
#: model_terms:ir.ui.view,arch_db:delivery_sendcloud.view_shipping_method_sendcloud
msgid "Delivery Product"
msgstr "Proizvod za isporuku"

#. module: delivery_sendcloud
#: model:ir.model.fields,help:delivery_sendcloud.field_delivery_carrier__sendcloud_shipping_rules
msgid ""
"Depending your Sendcloud account type, through rules you can define the shipping method to use depending on different conditions like destination, weight, value, etc.\n"
"Rules can override shipping product selected in Odoo"
msgstr ""

#. module: delivery_sendcloud
#: model:ir.model.fields,field_description:delivery_sendcloud.field_sendcloud_shipping_product__display_name
#: model:ir.model.fields,field_description:delivery_sendcloud.field_sendcloud_shipping_wizard__display_name
msgid "Display Name"
msgstr "Naziv za prikaz"

#. module: delivery_sendcloud
#. odoo-python
#: code:addons/delivery_sendcloud/models/sendcloud_service.py:0
msgid ""
"Each address line can only contain a maximum of 75 characters. You can split"
" the address into multiple lines to try to avoid this limitation."
msgstr ""

#. module: delivery_sendcloud
#. odoo-python
#: code:addons/delivery_sendcloud/models/sendcloud_service.py:0
msgid ""
"Ensure picking has shipping weight, if using packages, each package should "
"have a shipping weight"
msgstr ""

#. module: delivery_sendcloud
#. odoo-python
#: code:addons/delivery_sendcloud/models/delivery_carrier.py:0
msgid "Failed to create the return label!"
msgstr ""

#. module: delivery_sendcloud
#. odoo-python
#: code:addons/delivery_sendcloud/models/delivery_carrier.py:0
msgid "Failed to get the actual price!"
msgstr ""

#. module: delivery_sendcloud
#: model:ir.model.fields,field_description:delivery_sendcloud.field_delivery_carrier__sendcloud_product_functionalities
msgid "Functionalities"
msgstr ""

#. module: delivery_sendcloud
#: model_terms:ir.ui.view,arch_db:delivery_sendcloud.view_delivery_carrier_form
msgid "Functionality Filters"
msgstr ""

#. module: delivery_sendcloud
#. odoo-python
#: code:addons/delivery_sendcloud/models/delivery_carrier.py:0
msgid "Go to the shipping product"
msgstr ""

#. module: delivery_sendcloud
#: model:ir.model.fields,field_description:delivery_sendcloud.field_delivery_carrier__sendcloud_can_batch_shipping
#: model:ir.model.fields,field_description:delivery_sendcloud.field_sendcloud_shipping_product__has_multicollo
msgid "Has Multicollo"
msgstr ""

#. module: delivery_sendcloud
#: model:ir.model.fields,field_description:delivery_sendcloud.field_sendcloud_shipping_product__id
#: model:ir.model.fields,field_description:delivery_sendcloud.field_sendcloud_shipping_wizard__id
msgid "ID"
msgstr "ID"

#. module: delivery_sendcloud
#: model_terms:ir.ui.view,arch_db:delivery_sendcloud.view_delivery_carrier_form
msgid "Integration"
msgstr ""

#. module: delivery_sendcloud
#: model:ir.model.fields,field_description:delivery_sendcloud.field_sendcloud_shipping_product__write_uid
#: model:ir.model.fields,field_description:delivery_sendcloud.field_sendcloud_shipping_wizard__write_uid
msgid "Last Updated by"
msgstr "Promijenio"

#. module: delivery_sendcloud
#: model:ir.model.fields,field_description:delivery_sendcloud.field_sendcloud_shipping_product__write_date
#: model:ir.model.fields,field_description:delivery_sendcloud.field_sendcloud_shipping_wizard__write_date
msgid "Last Updated on"
msgstr "Promijenjeno"

#. module: delivery_sendcloud
#: model_terms:ir.ui.view,arch_db:delivery_sendcloud.view_delivery_carrier_form
msgid "Load your products"
msgstr ""

#. module: delivery_sendcloud
#. odoo-python
#: code:addons/delivery_sendcloud/models/sendcloud_service.py:0
msgid ""
"Make sure country codes are set in partner country and warehouse country"
msgstr ""

#. module: delivery_sendcloud
#. odoo-javascript
#: code:addons/delivery_sendcloud/static/src/views/sendcloud_product_selection_widget.xml:0
msgid "Max height"
msgstr ""

#. module: delivery_sendcloud
#. odoo-javascript
#: code:addons/delivery_sendcloud/static/src/views/sendcloud_product_selection_widget.xml:0
msgid "Max length"
msgstr ""

#. module: delivery_sendcloud
#. odoo-javascript
#: code:addons/delivery_sendcloud/static/src/views/sendcloud_product_selection_widget.xml:0
msgid "Max width"
msgstr ""

#. module: delivery_sendcloud
#: model:ir.model.fields,field_description:delivery_sendcloud.field_sendcloud_shipping_product__max_weight
msgid "Maximum Weight"
msgstr ""

#. module: delivery_sendcloud
#: model:ir.model.fields,field_description:delivery_sendcloud.field_sendcloud_shipping_product__min_weight
msgid "Minimum Weight"
msgstr ""

#. module: delivery_sendcloud
#. odoo-python
#: code:addons/delivery_sendcloud/models/delivery_carrier.py:0
msgid "Must be a Sendcloud carrier!"
msgstr ""

#. module: delivery_sendcloud
#. odoo-python
#: code:addons/delivery_sendcloud/models/sendcloud_service.py:0
msgid "No address found with contact name %s on your sendcloud account."
msgstr ""

#. module: delivery_sendcloud
#. odoo-python
#: code:addons/delivery_sendcloud/models/sendcloud_service.py:0
msgid "No picking or order provided"
msgstr ""

#. module: delivery_sendcloud
#. odoo-python
#: code:addons/delivery_sendcloud/models/delivery_carrier.py:0
msgid ""
"Note that a unit of the product '%s' is heavier than the maximum weight "
"allowed by the shipping method."
msgstr ""

#. module: delivery_sendcloud
#. odoo-python
#: code:addons/delivery_sendcloud/models/delivery_carrier.py:0
msgid ""
"Note that this price is for %s packages since the order weight is more than "
"the maximum weight allowed by the shipping method."
msgstr ""

#. module: delivery_sendcloud
#: model_terms:ir.ui.view,arch_db:delivery_sendcloud.view_delivery_carrier_form
msgid "Options"
msgstr "Opcije"

#. module: delivery_sendcloud
#. odoo-python
#: code:addons/delivery_sendcloud/models/sendcloud_service.py:0
msgid "Order below minimum weight of carrier"
msgstr ""

#. module: delivery_sendcloud
#. odoo-javascript
#: code:addons/delivery_sendcloud/static/src/views/sendcloud_product_selection_widget.xml:0
msgid "Other Functionalities"
msgstr ""

#. module: delivery_sendcloud
#. odoo-javascript
#: code:addons/delivery_sendcloud/static/src/views/sendcloud_product_selection_widget.xml:0
msgid ""
"Please check SendCloud return product documentation before selecting return "
"product. For some products, you can be charged for return labels printed but"
" not used."
msgstr ""

#. module: delivery_sendcloud
#: model:ir.model.fields,field_description:delivery_sendcloud.field_delivery_carrier__delivery_type
msgid "Provider"
msgstr "Davatelj "

#. module: delivery_sendcloud
#: model_terms:ir.ui.view,arch_db:delivery_sendcloud.view_shipping_method_sendcloud
msgid "Return Product"
msgstr ""

#. module: delivery_sendcloud
#: model:ir.model.fields,field_description:delivery_sendcloud.field_sendcloud_shipping_wizard__return_products
msgid "Return Products"
msgstr "Storniraj"

#. module: delivery_sendcloud
#: model:ir.model.fields,help:delivery_sendcloud.field_delivery_carrier__country_id
msgid "Select the country to be used by this delivery method"
msgstr ""

#. module: delivery_sendcloud
#: model_terms:ir.ui.view,arch_db:delivery_sendcloud.view_delivery_carrier_form
msgid "SendCloud Configuration"
msgstr ""

#. module: delivery_sendcloud
#: model_terms:ir.ui.view,arch_db:delivery_sendcloud.res_config_settings_view_form_sale
msgid "SendCloud Shipping Methods"
msgstr ""

#. module: delivery_sendcloud
#: model:ir.model.fields.selection,name:delivery_sendcloud.selection__delivery_carrier__delivery_type__sendcloud
#: model:ir.model.fields.selection,name:delivery_sendcloud.selection__stock_package_type__package_carrier_type__sendcloud
msgid "Sendcloud"
msgstr ""

#. module: delivery_sendcloud
#: model:ir.model.fields,help:delivery_sendcloud.field_delivery_carrier__sendcloud_public_key
msgid "Sendcloud API Integration Public key"
msgstr ""

#. module: delivery_sendcloud
#: model:ir.model.fields,help:delivery_sendcloud.field_delivery_carrier__sendcloud_secret_key
msgid "Sendcloud API Integration Secret key"
msgstr ""

#. module: delivery_sendcloud
#: model:ir.model.fields,field_description:delivery_sendcloud.field_stock_picking__sendcloud_parcel_ref
msgid "Sendcloud Parcel Reference"
msgstr ""

#. module: delivery_sendcloud
#: model:ir.model.fields,field_description:delivery_sendcloud.field_sendcloud_shipping_product__sendcloud_code
msgid "Sendcloud Product Identifier"
msgstr ""

#. module: delivery_sendcloud
#: model:ir.model.fields,field_description:delivery_sendcloud.field_delivery_carrier__sendcloud_public_key
msgid "Sendcloud Public Key"
msgstr ""

#. module: delivery_sendcloud
#: model:ir.model.fields,field_description:delivery_sendcloud.field_delivery_carrier__sendcloud_return_id
msgid "Sendcloud Return"
msgstr ""

#. module: delivery_sendcloud
#: model:ir.model.fields,field_description:delivery_sendcloud.field_stock_picking__sendcloud_return_parcel_ref
msgid "Sendcloud Return Parcel Ref"
msgstr ""

#. module: delivery_sendcloud
#: model:ir.model.fields,field_description:delivery_sendcloud.field_delivery_carrier__sendcloud_return_name
msgid "Sendcloud Return Shipping Product"
msgstr ""

#. module: delivery_sendcloud
#: model:ir.model.fields,field_description:delivery_sendcloud.field_delivery_carrier__sendcloud_secret_key
msgid "Sendcloud Secret Key"
msgstr ""

#. module: delivery_sendcloud
#: model:ir.model.fields,field_description:delivery_sendcloud.field_delivery_carrier__sendcloud_shipping_id
msgid "Sendcloud Shipping"
msgstr ""

#. module: delivery_sendcloud
#: model_terms:ir.ui.view,arch_db:delivery_sendcloud.res_config_settings_view_form_stock
msgid "Sendcloud Shipping Methods"
msgstr ""

#. module: delivery_sendcloud
#: model:ir.model.fields,field_description:delivery_sendcloud.field_delivery_carrier__sendcloud_shipping_name
msgid "Sendcloud Shipping Product"
msgstr ""

#. module: delivery_sendcloud
#. odoo-python
#: code:addons/delivery_sendcloud/models/sendcloud_service.py:0
msgid ""
"Sendcloud is not responding. Check your credentials and/or retry later."
msgstr ""

#. module: delivery_sendcloud
#: model:ir.model.fields,field_description:delivery_sendcloud.field_delivery_carrier__country_id
msgid "Ship From"
msgstr ""

#. module: delivery_sendcloud
#. odoo-python
#: code:addons/delivery_sendcloud/models/delivery_carrier.py:0
msgid "Shipment %s cancelled"
msgstr ""

#. module: delivery_sendcloud
#: model:ir.model.fields,field_description:delivery_sendcloud.field_sendcloud_shipping_product__carrier
msgid "Shipping Carrier"
msgstr ""

#. module: delivery_sendcloud
#: model:ir.model,name:delivery_sendcloud.model_delivery_carrier
msgid "Shipping Methods"
msgstr "Načini dostave"

#. module: delivery_sendcloud
#: model:ir.model.fields,field_description:delivery_sendcloud.field_sendcloud_shipping_product__name
#: model_terms:ir.ui.view,arch_db:delivery_sendcloud.view_shipping_method_sendcloud
msgid "Shipping Product"
msgstr ""

#. module: delivery_sendcloud
#: model:ir.model.fields,field_description:delivery_sendcloud.field_sendcloud_shipping_wizard__shipping_products
msgid "Shipping Products"
msgstr ""

#. module: delivery_sendcloud
#: model:ir.model.fields,help:delivery_sendcloud.field_delivery_carrier__sendcloud_default_package_type_id
msgid ""
"Some carriers require package dimensions, you can define these in a package "
"type that you set as default"
msgstr ""

#. module: delivery_sendcloud
#. odoo-python
#: code:addons/delivery_sendcloud/models/sendcloud_service.py:0
msgid ""
"Some packages in your transfer are too heavy for the heaviest available "
"shipping method."
msgstr ""

#. module: delivery_sendcloud
#. odoo-python
#: code:addons/delivery_sendcloud/models/sendcloud_service.py:0
msgid ""
"Something went wrong, parcel not returned from Sendcloud:\n"
" %s'."
msgstr ""

#. module: delivery_sendcloud
#: model:ir.model,name:delivery_sendcloud.model_stock_package_type
msgid "Stock package type"
msgstr ""

#. module: delivery_sendcloud
#. odoo-python
#: code:addons/delivery_sendcloud/models/sendcloud_service.py:0
msgid "The %s address needs to have the street, city, zip, and country"
msgstr ""

#. module: delivery_sendcloud
#. odoo-python
#: code:addons/delivery_sendcloud/models/sendcloud_service.py:0
msgid ""
"The delivery address of the customer has been removed from the pickup "
"location. This information is required by Sendcloud. Please go to the "
"delivery partner via the delivery order and make sure the parent of the "
"delivery partner is the partner you want to ship to."
msgstr ""

#. module: delivery_sendcloud
#. odoo-python
#: code:addons/delivery_sendcloud/models/sendcloud_service.py:0
msgid ""
"The estimated shipping price cannot be computed because the weight is missing for the following product(s): \n"
" %s"
msgstr ""

#. module: delivery_sendcloud
#. odoo-python
#: code:addons/delivery_sendcloud/models/delivery_carrier.py:0
msgid "The shipping product actually configured can't handle this delivery"
msgstr ""

#. module: delivery_sendcloud
#. odoo-python
#: code:addons/delivery_sendcloud/models/sendcloud_service.py:0
msgid ""
"The total weight of your transfer is too heavy for the heaviest available "
"shipping method."
msgstr ""

#. module: delivery_sendcloud
#. odoo-python
#: code:addons/delivery_sendcloud/models/delivery_carrier.py:0
msgid ""
"There are no shipping products available, please update the 'Shipping From' "
"field or activate suitable carriers in your sendcloud account"
msgstr ""

#. module: delivery_sendcloud
#. odoo-python
#: code:addons/delivery_sendcloud/models/delivery_carrier.py:0
msgid ""
"There is no rate available for this order with the selected shipping "
"product."
msgstr ""

#. module: delivery_sendcloud
#. odoo-python
#: code:addons/delivery_sendcloud/models/sendcloud_service.py:0
msgid ""
"There is no shipping method available for this order with the selected "
"carrier"
msgstr ""

#. module: delivery_sendcloud
#. odoo-python
#: code:addons/delivery_sendcloud/models/sendcloud_service.py:0
msgid ""
"There is no shipping method available for this picking with the selected "
"carrier"
msgstr ""

#. module: delivery_sendcloud
#. odoo-python
#: code:addons/delivery_sendcloud/models/sendcloud_service.py:0
msgid ""
"There's no method with matching weight range for packages :\n"
"%s\n"
"You can either choose another carrier, change your filters or redefine the content of your package(s)."
msgstr ""

#. module: delivery_sendcloud
#. odoo-python
#: code:addons/delivery_sendcloud/models/sendcloud_service.py:0
msgid ""
"There's no shipping method matching all your selected filters for this "
"picking/order."
msgstr ""

#. module: delivery_sendcloud
#. odoo-python
#: code:addons/delivery_sendcloud/models/delivery_carrier.py:0
msgid "There's no unit of measure with the name \"%s\"."
msgstr ""

#. module: delivery_sendcloud
#: model:ir.model,name:delivery_sendcloud.model_stock_picking
msgid "Transfer"
msgstr "Prijenos"

#. module: delivery_sendcloud
#: model:ir.model.fields,field_description:delivery_sendcloud.field_delivery_carrier__sendcloud_use_batch_shipping
msgid "Use Batch Shipping"
msgstr ""

#. module: delivery_sendcloud
#: model:ir.model.fields,field_description:delivery_sendcloud.field_delivery_carrier__sendcloud_shipping_rules
msgid "Use Sendcloud shipping rules"
msgstr ""

#. module: delivery_sendcloud
#. odoo-javascript
#: code:addons/delivery_sendcloud/static/src/views/sendcloud_product_selection_widget.xml:0
msgid "Weight range"
msgstr ""

#. module: delivery_sendcloud
#: model:ir.model.fields,help:delivery_sendcloud.field_delivery_carrier__sendcloud_use_batch_shipping
msgid ""
"When sending multiple parcels, combine them in one shipment. Not supported "
"for international shipping requiring customs' documentation"
msgstr ""

#. module: delivery_sendcloud
#. odoo-python
#: code:addons/delivery_sendcloud/models/delivery_carrier.py:0
msgid "You must add your public and secret key for sendcloud delivery type!"
msgstr ""

#. module: delivery_sendcloud
#. odoo-python
#: code:addons/delivery_sendcloud/models/delivery_carrier.py:0
msgid ""
"You must assign the required 'Shipping From' field in order to search for "
"available products"
msgstr ""

#. module: delivery_sendcloud
#. odoo-python
#: code:addons/delivery_sendcloud/models/delivery_carrier.py:0
msgid "You must have a shipping product configured!"
msgstr ""

#. module: delivery_sendcloud
#. odoo-javascript
#: code:addons/delivery_sendcloud/static/src/views/sendcloud_product_selection_widget.xml:0
msgid "cm"
msgstr "cm"

#. module: delivery_sendcloud
#: model_terms:ir.ui.view,arch_db:delivery_sendcloud.sendcloud_label_tracking
msgid ""
"created in Sendcloud. <br/>\n"
"            <b>Tracking Numbers:</b>"
msgstr ""
