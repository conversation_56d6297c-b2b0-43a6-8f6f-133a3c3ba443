# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* helpdesk
# 
# Translators:
# <PERSON>fur A Banter <<EMAIL>>, 2024
# <AUTHOR> <EMAIL>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON>, 2024
# <AUTHOR> <EMAIL>, 2024
# MichaelHadar, 2024
# <PERSON><PERSON>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# Ha <PERSON>tem <<EMAIL>>, 2024
# NoaFarkash, 2024
# <AUTHOR> <EMAIL>, 2024
# <AUTHOR> <EMAIL>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON>ta W<PERSON>, 2024
# <PERSON>, 2024
# da<PERSON>, 2024
# <AUTHOR> <EMAIL>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2025
# ya<PERSON> terner, 2025
# He<PERSON> <PERSON><PERSON> <<EMAIL>>, 2025
# or balmas, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-19 09:51+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: or balmas, 2025\n"
"Language-Team: Hebrew (https://app.transifex.com/odoo/teams/41243/he/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: he\n"
"Plural-Forms: nplurals=3; plural=(n == 1 && n % 1 == 0) ? 0 : (n == 2 && n % 1 == 0) ? 1: 2;\n"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__answered_customer_message_count
msgid "# Exchanges"
msgstr "# החלפות"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__open_ticket_count
msgid "# Open Tickets"
msgstr "# קריאות שירות פתוחות"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__rating_count
msgid "# Ratings"
msgstr "# דירוגים"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__sla_policy_count
msgid "# SLA Policy"
msgstr "# מדיניות SLA"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__urgent_ticket
msgid "# Urgent Ticket"
msgstr "# קריאת שירות דחופה"

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/models/res_partner.py:0
msgid "%(partner_name)s's Tickets"
msgstr "קריאות שירות של %(partner_name)s"

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/models/helpdesk_sla.py:0
#: code:addons/helpdesk/models/helpdesk_team.py:0
#: code:addons/helpdesk/models/helpdesk_ticket.py:0
msgid "%s (copy)"
msgstr "%s (העתק)"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_view_form
msgid "(any of these tags)"
msgstr "(כל אחת מהתגיות הללו)"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.ticket_invitation_follower
msgid ""
",\n"
"    <br/><br/>"
msgstr ""
",\n"
" <br/><br/>"

#. module: helpdesk
#: model:helpdesk.sla,name:helpdesk.helpdesk_sla_1
msgid "2 days to start"
msgstr "2 ימים כדי להתחיל"

#. module: helpdesk
#: model:helpdesk.sla,name:helpdesk.helpdesk_sla_2
msgid "7 days to finish"
msgstr "7 ימים לסיום"

#. module: helpdesk
#: model:helpdesk.sla,name:helpdesk.helpdesk_sla_3
msgid "8 hours to finish"
msgstr "8 שעות לסיום"

#. module: helpdesk
#: model_terms:digest.tip,tip_description:helpdesk.digest_tip_helpdesk_0
msgid "<b class=\"tip_title\">Tip: Create tickets from incoming emails</b>"
msgstr "<b class=\"tip_title\">טיפ: צור קריאות שירות ממיילים נכנסים</b>"

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/js/tours/helpdesk.js:0
msgid "<b>Drag &amp; drop</b> the card to change the stage of your ticket."
msgstr ""

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/js/tours/helpdesk.js:0
msgid ""
"<b>Log notes</b> for internal communications (you will only notify the "
"persons you specifically tag). Use <b>@ mentions</b> to ping a colleague or "
"<b># mentions</b> to contact a group of people."
msgstr ""
"<b>יומן הערות</b> לתקשורת פנימית (תודיע רק לאנשים שאתה מתייג ספציפית). השתמש"
" <b>באזכורים</b> ב-@ כדי להתריע לעמית או ב-<b># אזכורים </b>כדי ליצור קשר עם"
" קבוצת אנשים."

#. module: helpdesk
#: model:mail.template,body_html:helpdesk.solved_ticket_request_email_template
msgid ""
"<div>\n"
"        Dear <t t-out=\"object.sudo().partner_id.name or 'Madam/Sir'\">Madam/Sir</t>,<br/><br/>\n"
"        We would like to inform you that we have closed your ticket (reference <t t-out=\"object.id or ''\">15</t>). \n"
"        We trust that the services provided have met your expectations and that you have found a satisfactory resolution to your issue.<br/><br/>\n"
"        However, if you have any further questions or comments, please do not hesitate to reply to this email to re-open your ticket. \n"
"        Our team is always here to help you and we will be happy to assist you with any further concerns you may have.<br/><br/>\n"
"        Thank you for choosing our services and for your cooperation throughout this process. We truly value your business and appreciate the opportunity to serve you.<br/><br/>\n"
"        Kind regards,<br/><br/>\n"
"        <t t-out=\"object.team_id.name or 'Helpdesk'\">Helpdesk</t> Team.\n"
"    </div>\n"
"        "
msgstr ""
"<br/><div>\n"
"<t t-out=\"object.sudo().partner_id.name or 'Madam/Sir'\">גברתי/אדוני </t>היקר,<br/><br/>\n"
"ברצוננו להודיע ​​לך שסגרנו את הכרטיס שלך (אסמכתא <t t-out=\"object.id or ''\"></t>15). \n"
"אנו סומכים על כך שהשירותים הניתנים עמדו בציפיות שלך ושמצאת פתרון משביע רצון לבעיה שלך.<br/><br/>\n"
"עם זאת, אם יש לך שאלות או הערות נוספות, אנא אל תהסס להשיב לדוא\"ל זה כדי לפתוח מחדש את קריאת השירות שלך. \n"
"הצוות שלנו תמיד כאן כדי לעזור לך ואנו נשמח לסייע לך בכל חשש נוסף שיש לך.<br/><br/>\n"
"תודה שבחרת בשירותים שלנו ועל שיתוף הפעולה שלך לאורך תהליך זה. אנו באמת מעריכים את העסק שלך ומעריכים את ההזדמנות לשרת אותך<br/>.\n"
"בברכה,<br/><br/>\n"
"צוות <t t-out=\"object.team_id.name or 'Helpdesk'\">מוקד שירות ללקוחות</t>.\n"
"</div>"

#. module: helpdesk
#: model:mail.template,body_html:helpdesk.rating_ticket_request_email_template
msgid ""
"<div>\n"
"    <t t-set=\"access_token\" t-value=\"object._rating_get_access_token()\"/>\n"
"    <t t-set=\"partner\" t-value=\"object._rating_get_partner()\"/>\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:100%; margin:0;\">\n"
"    <tbody>\n"
"        <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"            <t t-if=\"partner.name\">\n"
"                Hello <t t-out=\"partner.name or ''\">Brandon Freeman</t>,<br/><br/>\n"
"            </t>\n"
"            <t t-else=\"\">\n"
"                Hello,<br/>\n"
"            </t>\n"
"            Please take a moment to rate our services related to the ticket \"<strong t-out=\"object.name or ''\">Table legs are unbalanced</strong>\"\n"
"            <t t-if=\"object._rating_get_operator().name\">\n"
"                assigned to <strong t-out=\"object._rating_get_operator().name or ''\">Mitchell Admin</strong>.<br/>\n"
"            </t>\n"
"            <t t-else=\"\">\n"
"                .<br/><br/>\n"
"            </t>\n"
"        </td></tr>\n"
"        <tr><td style=\"text-align: center;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:100%; margin: 32px 0px 32px 0px; display: inline-table;\">\n"
"                <tr><td style=\"font-size: 14px; text-align:center;\">\n"
"                    <strong>Tell us how you feel about our services</strong><br/>\n"
"                    <span style=\"text-color: #888888\">(click on one of these smileys)</span>\n"
"                </td></tr>\n"
"                <tr><td style=\"font-size: 14px;\">\n"
"                    <table style=\"width:100%;text-align:center;margin-top:2rem;\">\n"
"                        <tr>\n"
"                            <td>\n"
"                                <a t-attf-href=\"/rate/{{ access_token }}/5\" t-att-class=\"'pe-none' if object._rating_get_operator() else ''\">\n"
"                                    <img alt=\"Satisfied\" src=\"/rating/static/src/img/rating_5.png\" title=\"Satisfied\"/>\n"
"                                </a>\n"
"                            </td>\n"
"                            <td>\n"
"                                <a t-attf-href=\"/rate/{{ access_token }}/3\" t-att-class=\"'pe-none' if object._rating_get_operator() else ''\">\n"
"                                    <img alt=\"Okay\" src=\"/rating/static/src/img/rating_3.png\" title=\"Okay\"/>\n"
"                                </a>\n"
"                            </td>\n"
"                            <td>\n"
"                                <a t-attf-href=\"/rate/{{ access_token }}/1\" t-att-class=\"'pe-none' if object._rating_get_operator() else ''\">\n"
"                                    <img alt=\"Dissatisfied\" src=\"/rating/static/src/img/rating_1.png\" title=\"Dissatisfied\"/>\n"
"                                </a>\n"
"                            </td>\n"
"                        </tr>\n"
"                    </table>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td></tr>\n"
"        <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"            We appreciate your feedback. It helps us improve continuously.\n"
"            <br/><br/><span style=\"margin: 0px 0px 0px 0px; font-size: 12px; opacity: 0.5; color: #454748;\">This customer survey has been sent because your ticket has been moved to the stage <b t-out=\"object.stage_id.name or ''\">In Progress</b>.</span>\n"
"        </td></tr>\n"
"    </tbody>\n"
"    </table>\n"
"</div>\n"
"        "
msgstr ""

#. module: helpdesk
#: model:mail.template,body_html:helpdesk.new_ticket_request_email_template
msgid ""
"<div>\n"
"    Dear <t t-out=\"object.sudo().partner_id.name or object.sudo().partner_name or 'Madam/Sir'\">Madam/Sir</t>,<br/><br/>\n"
"    Your request\n"
"    <t t-if=\"hasattr(object.team_id, 'website_id') and object.get_portal_url()\">\n"
"        <a t-attf-href=\"{{ object.team_id.website_id.domain }}/my/ticket/{{ object.id }}/{{ object.access_token }}\" t-out=\"object.name or ''\">Table legs are unbalanced</a>\n"
"    </t>\n"
"    has been received and is being reviewed by our <t t-out=\"object.team_id.name or ''\">VIP Support</t> team.<br/><br/>\n"
"    The reference for your ticket is <strong><t t-out=\"object.ticket_ref or ''\">15</t></strong>.<br/><br/>\n"
"\n"
"    To provide any additional information, simply reply to this email.<br/><br/>\n"
"    <t t-if=\"object.team_id.show_knowledge_base\">\n"
"        Don't hesitate to visit our <a t-attf-href=\"{{ object.team_id.get_knowledge_base_url() }}\">Help Center</a>. You might find the answer to your question.\n"
"        <br/><br/>\n"
"    </t>\n"
"    <t t-if=\"object.team_id.allow_portal_ticket_closing\">\n"
"        Feel free to close your ticket if our help is no longer needed. Thank you for your collaboration.<br/><br/>\n"
"    </t>\n"
"\n"
"    <div style=\"text-align: center; padding: 16px 0px 16px 0px;\">\n"
"        <t t-if=\"hasattr(object.team_id, 'website_id') and object.team_id.use_website_helpdesk_form\">\n"
"            <a style=\"background-color: #875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size: 13px;\" t-att-href=\"'%s%s' % (object.team_id.website_id.domain or '', object.get_portal_url())\" target=\"_blank\">View Ticket</a>\n"
"        </t>\n"
"        <t t-else=\"\">\n"
"            <a style=\"background-color: #875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\" t-att-href=\"object.get_portal_url()\" target=\"_blank\">View Ticket</a>\n"
"        </t>\n"
"        <t t-if=\"hasattr(object.team_id, 'website_id') and object.team_id.allow_portal_ticket_closing\">\n"
"            <a style=\"background-color: #875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\" t-att-href=\"'%s/my/ticket/close/%s/%s' % (object.team_id.website_id.domain or '', object.id, object.access_token)\" target=\"_blank\">Close Ticket</a>\n"
"        </t>\n"
"        <t t-elif=\"object.team_id.allow_portal_ticket_closing\">\n"
"            <a style=\"background-color: #875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\" t-att-href=\"'/my/ticket/close/%s/%s' % (object.id, object.access_token)\" target=\"_blank\">Close Ticket</a>\n"
"        </t>\n"
"        <t t-if=\"object.team_id.use_website_helpdesk_forum or object.team_id.use_website_helpdesk_knowledge or object.team_id.use_website_helpdesk_slides\">\n"
"            <a style=\"background-color: #875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\" t-att-href=\"object.team_id.feature_form_url\" target=\"_blank\">Visit Help Center</a>\n"
"        </t><br/><br/>\n"
"    </div>\n"
"\n"
"    Best regards,<br/><br/>\n"
"    <t t-out=\"object.team_id.name or 'Helpdesk'\">Helpdesk</t> Team\n"
"</div>\n"
"        "
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
msgid ""
"<i class=\"fa fa-envelope-o\" title=\"Domain alias\" role=\"img\" aria-"
"label=\"Domain alias\"/>&amp;nbsp;"
msgstr ""
"<i class=\"fa fa-envelope-o\" title=\"Domain alias\" role=\"img\" aria-"
"label=\"Domain alias\"/>ו"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_form
msgid ""
"<i class=\"fa fa-fw o_button_icon fa-smile-o text-success\" invisible=\"rating_avg &lt; 3.66\" title=\"Satisfied\"/>\n"
"                            <i class=\"fa fa-fw o_button_icon fa-meh-o text-warning\" invisible=\"rating_avg &lt; 2.33 or rating_avg &gt;= 3.66\" title=\"Okay\"/>\n"
"                            <i class=\"fa fa-fw o_button_icon fa-frown-o text-danger\" invisible=\"rating_avg &gt;= 2.33\" title=\"Dissatisfied\"/>"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_form
msgid ""
"<i class=\"fa fa-lg fa-clock-o me-2 mt-1\" aria-label=\"Sla Deadline\" "
"title=\"Sla Deadline\"/>"
msgstr ""
"<i class=\"fa fa-lg fa-clock-o me-2 mt-1\" aria-label=\"Sla Deadline\" "
"title=\"Sla Deadline\"/>תאריך אחרון להסכם תנאי שירות"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid ""
"<i class=\"fa fa-lightbulb-o\" role=\"img\"/><span class=\"ms-2\">To use an "
"email alias, the first step is to configure an Alias Domain. You can achieve"
" this by navigating to the General Settings and configuring the "
"corresponding field.</span>"
msgstr ""
"<i class=\"fa fa-lightbulb-o\" role=\"img\"/><span class=\"ms-2\">כדי להשתמש"
" בכינוי דוא\"ל, הצעד הראשון הוא להגדיר דומיין כינוי. אתה יכול להשיג זאת על "
"ידי ניווט אל ההגדרות הכלליות והגדרת השדה המתאים.</span>"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid ""
"<i class=\"fa fa-lightbulb-o\"/>\n"
"                                    <span class=\"ms-2\">A rating request will automatically be sent by email to the customer when their ticket reaches the corresponding stage with the email template set.</span>"
msgstr ""
"<i class=\"fa fa-lightbulb-o\"/>\n"
"<span class=\"ms-2\">בקשת דירוג תישלח אוטומטית בדוא\"ל ללקוח כאשר קריאת השירות שלו תגיע לשלב המתאים עם תבנית המייל.</span>"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid ""
"<i class=\"fa fa-lightbulb-o\"/>\n"
"                                <span class=\"ms-2\">\n"
"                                    Type <b>/ticket</b> to create tickets<br/>\n"
"                                    Type <b>/search_tickets</b> to find tickets<br/>\n"
"                                </span>"
msgstr ""
"<i class=\"fa fa-lightbulb-o\"/>\n"
"<span class=\"ms-2\">\n"
"סוג <b>/קריאת שירות</b> לייצר קריאות שירות<br/>\n"
"סוג <b>חיפש_קריאות שירות</b> כדי למצוא קריאות שירות<br/> \n"
"</span>"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "<i class=\"fa fa-lightbulb-o\"/>&amp;nbsp;"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "<i class=\"fa fa-warning\"/>&amp;nbsp;"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "<i class=\"oi oi-arrow-right\"/> Set an Alias Domain"
msgstr "<i class=\"oi oi-arrow-right\"/> הגדר קידומת מייל"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.tickets_followup
msgid "<small class=\"text-muted\">Assigned to</small>"
msgstr "<small class=\"text-muted\">משויך ל</small>"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.tickets_followup
msgid "<small class=\"text-muted\">Customer</small>"
msgstr "<small class=\"text-muted\">לקוח</small>"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.portal_helpdesk_ticket
msgid "<small>#</small>"
msgstr "<small>#</small>"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.tickets_followup
msgid "<small>Stage:</small>"
msgstr "<small>שלב:</small>"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_form
msgid ""
"<span class=\"fa fa-exclamation-triangle text-warning oe_edit_only\" "
"title=\"By saving this change, the customer phone number will also be "
"updated.\" invisible=\"not is_partner_phone_update\"/>"
msgstr ""
"<span class=\"fa fa-exclamation-triangle text-warning oe_edit_only\" "
"title=\"By saving this change, the customer phone number will also be "
"updated.\" invisible=\"not is_partner_phone_update\"/>על ידי שמירת שינוי זה,"
" מספר הטלפון של הלקוח יעודכן."

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_activity
msgid "<span class=\"m-1\"/>#"
msgstr "<span class=\"m-1\"/>#"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_view_form
msgid "<span class=\"o_field_widget o_readonly_modifier\">Working Hours</span>"
msgstr "<span class=\"o_field_widget o_readonly_modifier\">שעות עבודה</span>"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_form
msgid "<span class=\"o_stat_text order-2\">Open</span>"
msgstr "<span class=\"o_stat_text order-2\">פתוח</span>"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_form
msgid "<span class=\"o_stat_text order-2\">Tickets</span>"
msgstr "<span class=\"o_stat_text order-2\">קריאות שירות</span>"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid ""
"<span class=\"o_stat_text\">\n"
"                                    Avg. Rating\n"
"                                </span>"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.view_partner_form_inherit_helpdesk
msgid "<span class=\"o_stat_text\"> Tickets</span>"
msgstr "<span class=\"o_stat_text\"> קריאות שירות</span>"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_form
msgid "<span class=\"o_stat_text\">Rating</span>"
msgstr "<span class=\"o_stat_text\">דירוג</span>"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
msgid "<span class=\"text-muted text-nowrap\">Failed</span>"
msgstr "<span class=\"text-muted text-nowrap\">נכשל</span>"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
msgid "<span class=\"text-muted text-nowrap\">Open</span>"
msgstr "<span class=\"text-muted text-nowrap\">פתוח</span>"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
msgid "<span class=\"text-muted text-nowrap\">Unassigned</span>"
msgstr "<span class=\"text-muted text-nowrap\">לא משויך</span>"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
msgid "<span class=\"text-muted text-nowrap\">Urgent</span>"
msgstr "<span class=\"text-muted text-nowrap\">דחוף</span>"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "<span><b>Followers </b></span>"
msgstr "<span><b>עוקבים</b></span>"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
msgid "<span>Average Rating</span>"
msgstr "<span>דירוג ממוצע</span>"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
msgid "<span>Reporting</span>"
msgstr "<span>דו\"חות</span>"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
msgid "<span>SLA Success Rate</span>"
msgstr "<span>דירוג מוצלח בSLA</span>"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
msgid "<span>Tickets Closed</span>"
msgstr "<span>קריאות שירות סגורות</span>"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
msgid "<span>View</span>"
msgstr "<span>תצוגה</span>"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.tickets_followup
msgid ""
"<span>Your ticket has successfully been closed. Thank you for your "
"collaboration.</span>"
msgstr "<span>קריאת השירות שלך נסגרה טופלה. תודה על שיתוף הפעולה</span>"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "<span>days of inactivity</span>"
msgstr "<span>ימים של חוסר פעילות</span>"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.tickets_followup
msgid "<strong class=\"col-lg-3\">Reported on</strong>"
msgstr "<strong class=\"col-lg-3\">תאריך יצירה</strong>"

#. module: helpdesk
#: model_terms:web_tour.tour,rainbow_man_message:helpdesk.helpdesk_tour
msgid ""
"<strong><b>Good job!</b> You walked through all steps of this tour.</strong>"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "<strong>After</strong>"
msgstr "<strong>אחרי</strong>"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "<strong>Alias </strong>"
msgstr "<strong>קידומת</strong>"

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team__alias_defaults
msgid ""
"A Python dictionary that will be evaluated to provide default values when "
"creating new records for this alias."
msgstr ""
"מילון פייתון שיוערך כדי לספק ערכי ברירת מחדל בעת יצירת רשומות חדשות לכינוי "
"זה."

#. module: helpdesk
#: model:ir.model.constraint,message:helpdesk.constraint_helpdesk_tag_name_uniq
msgid "A tag with the same name already exists."
msgstr "תגית עם אותו שם כבר קיים."

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__description
msgid "About Team"
msgstr "על הצוות"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Accept Emails From"
msgstr "קבל הודעות דוא\"ל מ"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__access_warning
msgid "Access warning"
msgstr "אזהרת גישה"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__message_needaction
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__message_needaction
msgid "Action Needed"
msgstr "נדרשת פעולה"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla__active
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__active
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage__active
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__active
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__active
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__active
msgid "Active"
msgstr "פעיל"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__activity_ids
msgid "Activities"
msgstr "פעילויות"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "סימון פעילות חריגה"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__activity_state
msgid "Activity State"
msgstr "מצב פעילות"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__activity_type_icon
msgid "Activity Type Icon"
msgstr "סוג פעילות"

#. module: helpdesk
#: model:ir.actions.act_window,name:helpdesk.mail_activity_type_action_config_helpdesk
#: model:ir.ui.menu,name:helpdesk.helpdesk_menu_config_activity_type
msgid "Activity Types"
msgstr "סוגי פעילויות"

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/js/tours/helpdesk.js:0
msgid ""
"Adapt your <b>pipeline</b> to your workflow by adding <b>stages</b> <i>(e.g."
" Awaiting Customer Feedback, etc.).</i>"
msgstr ""

#. module: helpdesk
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_stage_action
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_stage_team_action
msgid ""
"Adapt your pipeline to your workflow and track the progress of your tickets."
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_form
msgid "Add details about this ticket..."
msgstr "הוסף פרטים על קריאת שירות זו..."

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/js/tours/helpdesk.js:0
msgid ""
"Add your stage and place it at the right step of your workflow by dragging &"
" dropping it."
msgstr ""
"הוסף את השלב שלך והצב אותו בשלב הנכון של זרימת העבודה שלך על ידי גרירה "
"ושחרור שלו."

#. module: helpdesk
#: model:res.groups,name:helpdesk.group_helpdesk_manager
msgid "Administrator"
msgstr "מנהל מערכת"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "After-Sales"
msgstr "אחרי-מכירות"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__alias_id
msgid "Alias"
msgstr "כינוי"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__alias_contact
msgid "Alias Contact Security"
msgstr "כינוי אבטחה של איש קשר"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__alias_domain_id
msgid "Alias Domain"
msgstr "שם-מתחם (דומיין)"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__alias_domain
msgid "Alias Domain Name"
msgstr "כינוי שם (דומיין)"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__alias_full_name
msgid "Alias Email"
msgstr "קידומת מייל"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__alias_name
msgid "Alias Name"
msgstr "שם כינוי"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__alias_status
msgid "Alias Status"
msgstr "סטטוס"

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team__alias_status
msgid "Alias status assessed on the last message received."
msgstr "סטטוס כינוי הוערך בהודעה האחרונה שהתקבלה."

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__alias_model_id
msgid "Aliased Model"
msgstr "מודל בעל כינוי"

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/controllers/portal.py:0
msgid "All"
msgstr "הכל"

#. module: helpdesk
#: model:ir.actions.act_window,name:helpdesk.helpdesk_ticket_action_main_tree
#: model:ir.ui.menu,name:helpdesk.helpdesk_ticket_menu_all
msgid "All Tickets"
msgstr "כל קריאות השירות"

#. module: helpdesk
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_team__privacy_visibility__internal
msgid "All internal users (company)"
msgstr "כל המשתמשים הפנימיים (חברה)"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid ""
"Allow customers to help each other on a forum. Share answers from your "
"tickets directly."
msgstr ""
"אפשר ללקוחות לעזור זה לזה בפורום. שתף תשובות מהקריאות שירות שלך ישירות."

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Allow your customers to close their own tickets"
msgstr "אפשר ללקוחות שלך לסגור את קריאות השירות של עצמם"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.view_helpdesk_stage_delete_wizard
msgid "Archive Stages"
msgstr "שלב ארכיון"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_view_form
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_stage_view_form
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_stage_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_form
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis_closed
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search_base
msgid "Archived"
msgstr "בארכיון"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.view_helpdesk_stage_delete_confirmation_wizard
msgid "Are you sure you want to continue?"
msgstr "האם אתה בטוח שברצונך להמשיך?"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.view_helpdesk_stage_delete_wizard
msgid "Are you sure you want to delete these stages?"
msgstr "האם אתה בטוח שברצונך למחוק את השלבים האלה?"

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/controllers/portal.py:0
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_graph_main
msgid "Assigned"
msgstr "משויך"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__user_id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__user_id
msgid "Assigned To"
msgstr "משויכת ל"

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/controllers/portal.py:0
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__user_id
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search_base
#: model_terms:ir.ui.view,arch_db:helpdesk.portal_helpdesk_ticket
#: model_terms:ir.ui.view,arch_db:helpdesk.rating_rating_view_form_inherit_helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.rating_rating_view_search_inherit_helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.rating_rating_view_tree_inherit_helpdesk
msgid "Assigned to"
msgstr "משויכת ל"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis_closed
msgid "Assignee"
msgstr "ממונה"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__assign_method
msgid "Assignment Method"
msgstr "שיטת הקצאה"

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/models/helpdesk_ticket.py:0
msgid ""
"At this time, there is no customer preview available to show. The current "
"ticket cannot be accessed by the customer, as it belongs to a helpdesk team "
"that is not publicly available, or there is no customer associated with the "
"ticket."
msgstr ""
"בשלב זה, אין תצוגה מקדימה של לקוחות זמינה להצגה. הלקוח לא יכול לגשת לקריאת "
"שירות הנוכחית, מכיוון שהוא שייך לצוות עזרה שאינו זמין לציבור, או שאין לקוח "
"המשויך לקריאת השירות."

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__message_attachment_count
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__message_attachment_count
msgid "Attachment Count"
msgstr "כמות קבצים מצורפים"

#. module: helpdesk
#: model:res.groups,name:helpdesk.group_auto_assignment
msgid "Auto Assigment"
msgstr "שיוך אוטומטי"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid ""
"Automate the assignment of new tickets to the right people, and make sure "
"all tickets are being handled"
msgstr ""
"הפוך את ההקצאה של קריאות שירות חדשות לאנשים הנכונים לאוטומטית, וודא שכל "
"הקריאות שירות מטופלות"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__auto_assignment
msgid "Automatic Assignment"
msgstr "הקצאה אוטומטית"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__auto_close_ticket
msgid "Automatic Closing"
msgstr "סגירה אוטומטית"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.team_rating_progress_data
msgid "Average"
msgstr "ממוצע"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__avg_response_hours
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__avg_response_hours
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__avg_response_hours
msgid "Average Hours to Respond"
msgstr "שעות ממוצעות לתגובה"

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/components/helpdesk_team_dashboard/helpdesk_team_dashboard.xml:0
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__rating_avg
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__rating_avg
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__rating_avg
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__rating_avg
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Average Rating"
msgstr "דירוג ממוצע"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__rating_avg_percentage
msgid "Average Rating (%)"
msgstr "דירוג ממוצע (%)"

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/components/helpdesk_team_dashboard/helpdesk_team_dashboard.xml:0
msgid "Average Rating for the Past 7 Days"
msgstr "דירוג ממוצע של 7 הימים האחרונים"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_kanban
msgid "Average Rating: Dissatisfied"
msgstr "דירוג ממוצע: לא מרוצה"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_kanban
msgid "Average Rating: Okay"
msgstr "דירוג ממוצע: בסדר"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_kanban
msgid "Average Rating: Satisfied"
msgstr "דירוג ממוצע: מרוצה"

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/components/helpdesk_team_target/helpdesk_team_target.js:0
msgid "Average rating daily target"
msgstr "יעד דירוג ממוצע יומי"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
msgid "Average rating for the last 7 days"
msgstr "דירוג ממוצע של 7 הימים האחרונים"

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/components/helpdesk_team_dashboard/helpdesk_team_dashboard.xml:0
msgid "Avg Last 7 days"
msgstr "ממוצע 7 ימים אחרונים"

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/components/helpdesk_team_dashboard/helpdesk_team_dashboard.xml:0
msgid "Avg Open Hours"
msgstr "ממוצע שעות פתוחות"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.team_rating_progress_data
msgid "Bad"
msgstr "רע"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Bill the time spent on your tickets to your customers"
msgstr "חייב את הזמן המושקע בקריאות השירות שלך ללקוחות שלך"

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/models/helpdesk_stage.py:0
#: model:helpdesk.stage,legend_blocked:helpdesk.stage_cancelled
#: model:helpdesk.stage,legend_blocked:helpdesk.stage_in_progress
#: model:helpdesk.stage,legend_blocked:helpdesk.stage_new
#: model:helpdesk.stage,legend_blocked:helpdesk.stage_on_hold
#: model:helpdesk.stage,legend_blocked:helpdesk.stage_solved
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_ticket__kanban_state__blocked
#: model_terms:ir.ui.view,arch_db:helpdesk.portal_helpdesk_ticket
msgid "Blocked"
msgstr "חסום"

#. module: helpdesk
#: model:helpdesk.tag,name:helpdesk.tag_crm
msgid "CRM"
msgstr "ניהול קשרי לקוחות"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__campaign_id
msgid "Campaign"
msgstr "קמפיין"

#. module: helpdesk
#: model:helpdesk.stage,name:helpdesk.stage_cancelled
msgid "Cancelled"
msgstr "בוטל"

#. module: helpdesk
#: model:ir.ui.menu,name:helpdesk.helpdesk_team_canned_response_menu
msgid "Canned Responses"
msgstr "קיצורי דרך לתגובות"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid ""
"Centralize, manage, share and grow your knowledge library. Allow customers "
"to search your articles in the help center for answers to their questions."
msgstr ""
"רכז, נהל, שתף והגדל את ספריית הידע שלך. אפשר ללקוחות לחפש במאמרים שלך במרכז "
"העזרה כדי לקבל תשובות לשאלותיהם."

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Channels"
msgstr "ערוצים"

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/components/helpdesk_team_target/helpdesk_team_target.js:0
msgid "Click to Set Your Daily Rating Target"
msgstr "לחץ כדי להגדיר את יעד הדירוג היומי שלך"

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/components/helpdesk_team_target/helpdesk_team_target.js:0
#: code:addons/helpdesk/static/src/components/helpdesk_team_target/helpdesk_team_target.xml:0
msgid "Click to set"
msgstr "לחץ כדי להגדיר"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.tickets_followup
msgid "Close"
msgstr "סגור"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.tickets_followup
msgid "Close Ticket"
msgstr "סגור קריאת שירות"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__close_date
msgid "Close date"
msgstr "תאריך סגירה"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Close inactive tickets automatically"
msgstr "סגור קריאות שירות לא פעילות באופן אוטומטי"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.tickets_followup
msgid "Close ticket"
msgstr "סגור קריאת שירות"

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/controllers/portal.py:0
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search_base
msgid "Closed"
msgstr "סגור"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search_base
msgid "Closed On"
msgstr "נסגר ב"

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/components/helpdesk_team_dashboard/helpdesk_team_dashboard.xml:0
#: model:ir.actions.act_window,name:helpdesk.helpdesk_ticket_action_7days_tickets
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis
msgid "Closed Tickets"
msgstr "קריאות שירות סגורות"

#. module: helpdesk
#: model:ir.actions.act_window,name:helpdesk.helpdesk_ticket_action_close_analysis
msgid "Closed Tickets Analysis"
msgstr "ניתוח נתוני קריאות שירות סגורות"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__closed_by_partner
msgid "Closed by Partner"
msgstr "נסגר ע\"י לקוח"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__close_date
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__close_date
msgid "Closing Date"
msgstr "תאריך סגירה"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__allow_portal_ticket_closing
msgid "Closure by Customers"
msgstr "סגירה על ידי לקוחות"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_tag__color
msgid "Color"
msgstr "צבע"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_status__color
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__color
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__color
msgid "Color Index"
msgstr "אינדקס צבעים"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.rating_rating_view_search_inherit_helpdesk
msgid "Comment"
msgstr "תגובה"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__commercial_partner_id
msgid "Commercial Entity"
msgstr "יישות מסחרית"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.tickets_followup
msgid "Communication history"
msgstr "היסטוריית התקשרות"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__use_website_helpdesk_forum
msgid "Community Forum"
msgstr "פורום קהילתי"

#. module: helpdesk
#: model:ir.model,name:helpdesk.model_res_company
msgid "Companies"
msgstr "חברות"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla__company_id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__company_id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__company_id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__company_id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__company_id
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search_base
msgid "Company"
msgstr "חברה"

#. module: helpdesk
#: model:ir.ui.menu,name:helpdesk.helpdesk_menu_config
msgid "Configuration"
msgstr "תצורה"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.view_helpdesk_stage_delete_confirmation_wizard
#: model_terms:ir.ui.view,arch_db:helpdesk.view_helpdesk_stage_unarchive_wizard
msgid "Confirm"
msgstr "אשר"

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/wizard/helpdesk_stage_delete.py:0
msgid "Confirmation"
msgstr "אישור"

#. module: helpdesk
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_sla
msgid "Congratulations!"
msgstr "מזל טוב!"

#. module: helpdesk
#: model:ir.model,name:helpdesk.model_res_partner
#: model_terms:ir.ui.view,arch_db:helpdesk.portal_helpdesk_ticket
msgid "Contact"
msgstr "איש קשר"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__use_coupons
msgid "Coupons"
msgstr "קופונים"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search_base
msgid "Create Date"
msgstr "תאריך יצירה"

#. module: helpdesk
#: model_terms:ir.actions.act_window,help:helpdesk.email_template_action_helpdesk
msgid "Create a new template"
msgstr "צור תבנית חדשה"

#. module: helpdesk
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_team_action
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_team_dashboard_action_main
msgid ""
"Create teams to organize your tickets by expertise or geographical region, "
"and define a different workflow for each team."
msgstr ""
"צור צוותים כדי לארגן את הקריאות שירות שלך לפי מומחיות או אזור גיאוגרפי, "
"והגדר זרימת עבודה שונה עבור כל צוות."

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Create tickets by sending an email to an alias"
msgstr "צור קריאות שירות על ידי שליחת אימייל לכינוי"

#. module: helpdesk
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_7days_success
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_7days_tickets
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_7dayssuccess
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_close_analysis
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_dashboard
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_success
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_team_performance
msgid "Create tickets to get statistics."
msgstr "צור קריאות שירות על מנת לקבל סטטיסטיקות."

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla__create_uid
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_status__create_uid
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage__create_uid
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage_delete_wizard__create_uid
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_tag__create_uid
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__create_uid
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__create_uid
msgid "Created by"
msgstr "נוצר על-ידי"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla__create_date
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_status__create_date
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage__create_date
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage_delete_wizard__create_date
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_tag__create_date
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__create_date
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__create_date
msgid "Created on"
msgstr "נוצר ב-"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search_base
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_tree
msgid "Creation Date"
msgstr "תאריך יצירה"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_view_form
msgid "Criteria"
msgstr "קריטריונים"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.tickets_followup
msgid "Current stage of this ticket"
msgstr "שלב נוכחי של קריאת שירות זו"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__alias_bounced_content
msgid "Custom Bounced Message"
msgstr "הודעה מותאמת אישית להודעות שגויות"

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/controllers/portal.py:0
#: code:addons/helpdesk/models/helpdesk_ticket.py:0
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__partner_id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__partner_id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__partner_id
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search_base
msgid "Customer"
msgstr "לקוח"

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/models/helpdesk_team.py:0
#: code:addons/helpdesk/models/res_company.py:0
#: model:helpdesk.team,name:helpdesk.helpdesk_team1
msgid "Customer Care"
msgstr "שירות לקוחות"

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/models/helpdesk_ticket.py:0
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__partner_email
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__partner_email
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__partner_email
msgid "Customer Email"
msgstr "דוא\"ל לקוח"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__partner_name
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__partner_name
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__partner_name
msgid "Customer Name"
msgstr "שם לקוח"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__partner_phone
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__partner_phone
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__partner_phone
msgid "Customer Phone"
msgstr "טלפון לקוח"

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_ticket__access_url
msgid "Customer Portal URL"
msgstr "כתובת אתר של פורטל לקוחות"

#. module: helpdesk
#: model:ir.actions.server,name:helpdesk.action_open_customer_preview
msgid "Customer Preview"
msgstr "צפיה מקדימה ללקוח"

#. module: helpdesk
#: model:ir.actions.act_window,name:helpdesk.rating_rating_action_helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__use_rating
#: model:ir.ui.menu,name:helpdesk.helpdesk_ticket_report_menu_ratings
msgid "Customer Ratings"
msgstr "דירוגי לקוח"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla__partner_ids
msgid "Customers"
msgstr "לקוחות"

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/models/helpdesk_team.py:0
msgid "Customers will be added to the followers of their tickets."
msgstr "לקוחות יתווספו לעוקבים של קריאות השירות שלהם."

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/components/helpdesk_team_dashboard/helpdesk_team_dashboard.xml:0
msgid "Daily Target"
msgstr "יעד יומי"

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_sla_status__reached_datetime
msgid "Datetime at which the SLA stage was reached for the first time"
msgstr "שעה ותאריך בהם הגיע לראשונה שלב הסכם תנאי השירות"

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_sla_report_analysis__sla_exceeded_hours
msgid ""
"Day to reach the stage of the SLA, without taking the working calendar into "
"account"
msgstr ""
"ימים להגיע לשלב הסכם תנאי שירות, מבלי לקחת בחשבון את ימי העבודה של העובד"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_status__deadline
msgid "Deadline"
msgstr "תאריך יעד"

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/views/helpdesk_ticket_graph/helpdesk_ticket_graph_model.js:0
#: code:addons/helpdesk/static/src/views/helpdesk_ticket_kanban/helpdesk_ticket_kanban_header.js:0
#: code:addons/helpdesk/static/src/views/helpdesk_ticket_list/helpdesk_ticket_list_renderer.js:0
#: code:addons/helpdesk/static/src/views/helpdesk_ticket_pivot/helpdesk_ticket_pivot_model.js:0
msgid "Deadline reached"
msgstr "תאריך יעד הגיע"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__alias_defaults
msgid "Default Values"
msgstr "ערכי ברירת מחדל"

#. module: helpdesk
#: model:ir.actions.server,name:helpdesk.unlink_helpdesk_stage_action
#: model_terms:ir.ui.view,arch_db:helpdesk.view_helpdesk_stage_delete_wizard
msgid "Delete"
msgstr "מחיקה"

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/models/helpdesk_stage.py:0
#: model_terms:ir.ui.view,arch_db:helpdesk.view_helpdesk_stage_delete_confirmation_wizard
#: model_terms:ir.ui.view,arch_db:helpdesk.view_helpdesk_stage_delete_wizard
#: model_terms:ir.ui.view,arch_db:helpdesk.view_helpdesk_stage_unarchive_wizard
msgid "Delete Stage"
msgstr "מחק שלב"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Describe your team to your colleagues and customers..."
msgstr "תאר את הצוות שלך לעמיתים וללקוחות שלך..."

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__description
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage__description
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__description
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__description
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_form
msgid "Description"
msgstr "תיאור"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_view_form
msgid "Description of the policy..."
msgstr "תיאור המדיניות ..."

#. module: helpdesk
#: model:ir.model,name:helpdesk.model_digest_digest
msgid "Digest"
msgstr "תמצית"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.tickets_followup
#: model_terms:ir.ui.view,arch_db:helpdesk.view_helpdesk_stage_delete_confirmation_wizard
#: model_terms:ir.ui.view,arch_db:helpdesk.view_helpdesk_stage_delete_wizard
#: model_terms:ir.ui.view,arch_db:helpdesk.view_helpdesk_stage_unarchive_wizard
msgid "Discard"
msgstr "בטל"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla__display_name
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__display_name
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_status__display_name
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage__display_name
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage_delete_wizard__display_name
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_tag__display_name
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__display_name
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__display_name
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__display_name
msgid "Display Name"
msgstr "שם לתצוגה"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search_base
#: model_terms:ir.ui.view,arch_db:helpdesk.team_rating_progress_data
msgid "Dissatisfied"
msgstr "לא מרוצה"

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/models/digest.py:0
msgid "Do not have access, skip this data for user's digest email"
msgstr "אין לך גישה, דלג על נתונים אלה עבור תקציר דוא\"ל של המשתמשים"

#. module: helpdesk
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_team__assign_method__balanced
msgid "Each user has an equal number of open tickets"
msgstr "לכל משתמש יש מספר שווה של קריאות שירות פתוחות"

#. module: helpdesk
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_team__assign_method__randomly
msgid "Each user is assigned an equal number of tickets"
msgstr "לכל משתמש מוקצה מספר שווה של קריאות שירות"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_kanban
msgid "Edit"
msgstr "ערוך"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__alias_email
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_tree
msgid "Email Alias"
msgstr "קידומת מייל"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage__template_id
msgid "Email Template"
msgstr "תבנית דוא\"ל"

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_stage__template_id
msgid ""
"Email automatically sent to the customer when the ticket reaches this stage.\n"
"By default, the email will be sent from the email alias of the helpdesk team.\n"
"Otherwise it will be sent from the company's email address, or from the catchall (as defined in the System Parameters)."
msgstr ""
"מייל נשלח אוטומטית ללקוח כאשר קריאת השירות מגיעה לשלב זה.\n"
"כברירת מחדל, הדוא\"ל יישלח מהקידומת הדוא\"ל של צוות התמיכה הטכנית.\n"
"אחרת הוא יישלח מכתובת המייל של החברה, או מה-catchall (כפי שמוגדר בפרמטרי המערכת)."

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__email_cc
msgid "Email cc"
msgstr "עותק דוא\"ל"

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team__alias_domain
msgid "Email domain e.g. 'example.com' in '<EMAIL>'"
msgstr "דומיין דוא\"ל, למשל, 'example.com' ב-'<EMAIL>'"

#. module: helpdesk
#: model_terms:digest.tip,tip_description:helpdesk.digest_tip_helpdesk_0
msgid "Emails sent to"
msgstr "מיילים שנשלחו אל"

#. module: helpdesk
#: model_terms:digest.tip,tip_description:helpdesk.digest_tip_helpdesk_0
msgid ""
"Emails sent to a Helpdesk Team alias generate tickets in your pipeline."
msgstr "הודעות דוא\"ל שנשלחות לקידומת מוקד תמיכה בלקוחות  מייצרים קריאות שירות."

#. module: helpdesk
#: model:mail.template,description:helpdesk.rating_ticket_request_email_template
msgid "Enable \"customer ratings\" feature on the helpdesk team"
msgstr "הפעל את התכונה \"דירוג לקוחות\" בצוות העזרה"

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/js/tours/helpdesk.js:0
msgid ""
"Enter the <b>subject</b> of your ticket <br/><i>(e.g. Problem with my "
"installation, Wrong order, etc.).</i>"
msgstr ""
"הזן את<b> נושא</b> קריאת השירות שלך<br/><i> (למשל בעיה בהתקנה שלי, הזמנה "
"שגויה וכו').</i>"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_status__exceeded_hours
msgid "Exceeded Working Hours"
msgstr "חרג משעות העבודה"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla__exclude_stage_ids
msgid "Excluding Stages"
msgstr "לא כולל שלבים"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_form
msgid "Extra Info"
msgstr "מידע נוסף"

#. module: helpdesk
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_sla_status__status__failed
msgid "Failed"
msgstr "נכשל"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__sla_fail
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__sla_fail
msgid "Failed SLA Policy"
msgstr "מדיניות הסכם תנאי שירות (SLA) נכשלה"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__sla_failed
msgid "Failed SLA Ticket"
msgstr "קריאות שירות עם SLA נכשל"

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/components/helpdesk_team_dashboard/helpdesk_team_dashboard.xml:0
msgid "Failed Tickets"
msgstr "קריאות שירות שנכשלו בSLA"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__use_fsm
msgid "Field Service"
msgstr "שירות שטח"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis
msgid "First Assignment Date"
msgstr "תאריך שיוך ראשוני"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__assign_date
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__assign_date
msgid "First assignment date"
msgstr "תאריך שיוך ראשוני"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage__fold
msgid "Folded in Kanban"
msgstr "מקופל בקנבן"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Follow All Team's Tickets"
msgstr "עקוב אחר קריאות השירות של כל הצוות"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.portal_my_home_helpdesk_ticket
msgid "Follow all your helpdesk tickets"
msgstr "עקוב אחר כל קריאות השירות שלך"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search_base
msgid "Followed"
msgstr "במעקב"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__message_follower_ids
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__message_follower_ids
msgid "Followers"
msgstr "עוקבים"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__message_partner_ids
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__message_partner_ids
msgid "Followers (Partners)"
msgstr "עוקבים (לקוחות/ספקים)"

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_ticket__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "פונט מדהים למשל עבור משימות fa-tasks"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis_closed
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search
msgid "Future Activities"
msgstr "פעילויות עתידיות"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid ""
"Get in touch with your website visitors, and engage them with scripted "
"chatbot conversations. Create and search tickets from your conversations."
msgstr ""
"צור קשר עם המבקרים באתר שלך, וצור איתם מעורבות בשיחות צ'טבוט מתוסרטות. צור "
"וחפש קריאות שירות מהשיחות שלך."

#. module: helpdesk
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_analysis_action
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_analysis_dashboard_action
msgid ""
"Get statistics on your tickets and how long it takes to assign and resolve "
"them."
msgstr ""
"קבל נתונים סטטיסטיים על הקריאות שירות שלך וכמה זמן לוקח להקצות ולפתור אותם."

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Get tickets through an online form"
msgstr "קבל קריאות שירות באמצעות טופס מקוון"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Grant discounts, free products or free shipping"
msgstr "הענק הנחות, מוצרים בחינם או משלוח חינם"

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/models/helpdesk_team.py:0
msgid ""
"Grant employees access to your helpdesk team or tickets by adding them as "
"followers. Employees automatically get access to the tickets they are "
"assigned to."
msgstr ""
"הענק לעובדים גישה לצוות העזרה או לכרטיסים שלך על ידי הוספתם כעוקבים. עובדים "
"מקבלים באופן אוטומטי גישה לכרטיסים שהם מוקצים אליהם."

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/models/helpdesk_team.py:0
msgid ""
"Grant portal users access to your helpdesk team or tickets by adding them as"
" followers. Customers automatically get access to their tickets in their "
"portal."
msgstr ""
"הענק למשתמשי הפורטל גישה לצוות העזרה שלך או לקריאות השירות על ידי הוספתם "
"כעוקבים. לקוחות מקבלים באופן אוטומטי גישה לקריאות השירות שלהם בפורטל שלהם."

#. module: helpdesk
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_sla_report_analysis__kanban_state__done
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_ticket_report_analysis__kanban_state__done
msgid "Green"
msgstr "ירוק"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage__legend_done
msgid "Green Kanban Label"
msgstr "תווית קנבן ירוקה"

#. module: helpdesk
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_sla_report_analysis__kanban_state__normal
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_ticket_report_analysis__kanban_state__normal
msgid "Grey"
msgstr "אפור"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage__legend_normal
msgid "Grey Kanban Label"
msgstr "תווית קנבן אפורה"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis_closed
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search_base
msgid "Group By"
msgstr "קבץ לפי"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.team_rating_progress_data
msgid "Happy"
msgstr "שמח"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_kanban
msgid "Happy face"
msgstr "פרצוף שמח"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__has_message
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__has_message
msgid "Has Message"
msgstr "יש הודעה"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__sla_reached
msgid "Has SLA reached"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__sla_reached_late
msgid "Has SLA reached late"
msgstr "Has SLA reached late"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.ticket_invitation_follower
msgid "Hello"
msgstr "שלום"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Help Center"
msgstr "מרכז עזרה"

#. module: helpdesk
#: model:ir.ui.menu,name:helpdesk.menu_helpdesk_root
#: model_terms:ir.ui.view,arch_db:helpdesk.digest_digest_view_form
msgid "Helpdesk"
msgstr "מוקד תמיכה ללקוחות"

#. module: helpdesk
#: model:ir.actions.act_window,name:helpdesk.helpdesk_team_dashboard_action_main
msgid "Helpdesk Overview"
msgstr "סקירה כללית של מוקד תמיכה"

#. module: helpdesk
#: model:ir.model,name:helpdesk.model_helpdesk_sla
msgid "Helpdesk SLA Policies"
msgstr "מדיניות הסמכי רמת שירות של מוקד תמיכה"

#. module: helpdesk
#: model:ir.model,name:helpdesk.model_helpdesk_stage
msgid "Helpdesk Stage"
msgstr "שלב מוקד תמיכה"

#. module: helpdesk
#: model:ir.model,name:helpdesk.model_helpdesk_stage_delete_wizard
msgid "Helpdesk Stage Delete Wizard"
msgstr "אשף מחיקת שלב במוקד תמיכה ללקוחות"

#. module: helpdesk
#: model:ir.model,name:helpdesk.model_helpdesk_tag
msgid "Helpdesk Tags"
msgstr "תגיות מוקד תמיכה"

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/controllers/portal.py:0
#: model:ir.model,name:helpdesk.model_helpdesk_team
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla__team_id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__team_id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__name
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__team_id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__team_id
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_tree
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis_closed
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search_base
#: model_terms:ir.ui.view,arch_db:helpdesk.rating_rating_view_search_inherit_helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.rating_rating_view_tree_inherit_helpdesk
msgid "Helpdesk Team"
msgstr "צוות תמיכה"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_search
msgid "Helpdesk Team Search"
msgstr "חיפוש צוות של התמיכה טכנית"

#. module: helpdesk
#: model:ir.actions.act_window,name:helpdesk.helpdesk_team_action
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage__team_ids
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage_delete_wizard__team_ids
#: model:ir.ui.menu,name:helpdesk.helpdesk_team_menu
msgid "Helpdesk Teams"
msgstr "צוותי תמיכה"

#. module: helpdesk
#: model:ir.model,name:helpdesk.model_helpdesk_ticket
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_form
msgid "Helpdesk Ticket"
msgstr "קריאת שירות מוקד תמיכה"

#. module: helpdesk
#: model:ir.actions.server,name:helpdesk.ir_cron_auto_close_ticket_ir_actions_server
msgid "Helpdesk Ticket: Automatically close the tickets"
msgstr ""
"קריאת שירות של מוקד תמיכה ללקוחות: סגור את קריאות השירות באופן אוטומטי"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_graph_main
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_pivot_main
msgid "Helpdesk Tickets"
msgstr "קריאות שירות מוקד תמיכה"

#. module: helpdesk
#: model:mail.template,name:helpdesk.solved_ticket_request_email_template
msgid "Helpdesk: Ticket Closed"
msgstr "מוקד תמיכה ללקוחות: קריאת שירות נסגרה"

#. module: helpdesk
#: model:mail.template,name:helpdesk.rating_ticket_request_email_template
msgid "Helpdesk: Ticket Rating Request"
msgstr "מוקד תמיכה ללקוחות: נדרש דירוג לקריאת שירות"

#. module: helpdesk
#: model:mail.template,name:helpdesk.new_ticket_request_email_template
msgid "Helpdesk: Ticket Received"
msgstr "מוקד תמיכה ללקוחות: קריאת שירות התקבלה"

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/components/helpdesk_team_dashboard/helpdesk_team_dashboard.xml:0
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search_base
msgid "High Priority"
msgstr "עדיפות גבוהה"

#. module: helpdesk
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_sla__priority__2
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_sla_report_analysis__priority__2
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_ticket__priority__2
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_ticket_report_analysis__priority__2
msgid "High priority"
msgstr "עדיפות גבוהה"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.tickets_followup
msgid "History"
msgstr "היסטוריה"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__ticket_open_hours
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__ticket_open_hours
msgid "Hours Open"
msgstr "שעות פתוחות"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__first_response_hours
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__first_response_hours
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__first_response_hours
msgid "Hours to First Response"
msgstr "שעות עד לתגובה הראשונה"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla__id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_status__id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage__id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage_delete_wizard__id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_tag__id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__id
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_tree
msgid "ID"
msgstr "מזהה"

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team__alias_parent_thread_id
msgid ""
"ID of the parent record holding the alias (example: project holding the task"
" creation alias)"
msgstr ""
"מזהה של רשומת האב המחזיקה בכינוי (דוגמה: פרויקט המחזיק בכינוי ליצירת "
"המשימות)"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__activity_exception_icon
msgid "Icon"
msgstr "סמל"

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_ticket__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "סמל לציון פעילות חריגה."

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team__message_needaction
#: model:ir.model.fields,help:helpdesk.field_helpdesk_ticket__message_needaction
msgid "If checked, new messages require your attention."
msgstr "אם מסומן, הודעות חדשות דורשות את תשומת לבך."

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team__message_has_error
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team__message_has_sms_error
#: model:ir.model.fields,help:helpdesk.field_helpdesk_ticket__message_has_error
#: model:ir.model.fields,help:helpdesk.field_helpdesk_ticket__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "אם מסומן, בחלק מההודעות קיימת שגיאת משלוח."

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team__alias_bounced_content
msgid ""
"If set, this content will automatically be sent out to unauthorized users "
"instead of the default message."
msgstr ""
"אם מוגדר, תוכן זה יישלח אוטומטית למשתמשים לא מורשים במקום הודעת ברירת המחדל."

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/models/helpdesk_stage.py:0
#: model:helpdesk.stage,legend_normal:helpdesk.stage_cancelled
#: model:helpdesk.stage,legend_normal:helpdesk.stage_in_progress
#: model:helpdesk.stage,legend_normal:helpdesk.stage_new
#: model:helpdesk.stage,legend_normal:helpdesk.stage_on_hold
#: model:helpdesk.stage,legend_normal:helpdesk.stage_solved
#: model:helpdesk.stage,name:helpdesk.stage_in_progress
#: model_terms:ir.ui.view,arch_db:helpdesk.portal_helpdesk_ticket
msgid "In Progress"
msgstr "בתהליך"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__from_stage_ids
msgid "In Stages"
msgstr "נמצא בשלבים"

#. module: helpdesk
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_ticket__kanban_state__normal
msgid "In progress"
msgstr "בתהליך"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__auto_close_day
msgid "Inactive Period(days)"
msgstr "תקופה לא פעילה (ימים)"

#. module: helpdesk
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_team__privacy_visibility__invited_internal
msgid "Invited internal users (private)"
msgstr "משתמשים פנימיים שהוזמנו (פרטי)"

#. module: helpdesk
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_team__privacy_visibility__portal
msgid "Invited portal users and all internal users (public)"
msgstr "משתמשי פורטל מוזמנים וכל המשתמשים הפנימיים (פומבי)"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__message_is_follower
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__message_is_follower
msgid "Is Follower"
msgstr "עוקב"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Issue credits notes"
msgstr "הנפק שטרות זיכויים"

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_ticket__duration_tracking
msgid "JSON that maps ids from a many2one field to seconds spent"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__legend_blocked
msgid "Kanban Blocked Explanation"
msgstr "קנבן הסבר חסום"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__legend_normal
msgid "Kanban Ongoing Explanation"
msgstr "קנבן הסבר מתמשך"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__kanban_state
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__kanban_state
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__kanban_state
msgid "Kanban State"
msgstr "מצב קנבן "

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__kanban_state_label
msgid "Kanban State Label"
msgstr "קנבן תווית מצב"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__legend_done
msgid "Kanban Valid Explanation"
msgstr "הסבר קנבן תקף"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__use_website_helpdesk_knowledge
msgid "Knowledge"
msgstr "ניהול ידע"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.team_rating_data
msgid "Last 3 months"
msgstr "3 חודשים אחרונים"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search_base
msgid "Last 30 Days"
msgstr "30 ימים אחרונים"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.team_rating_data
msgid "Last 30 days"
msgstr "30 ימים אחרונים"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search_base
msgid "Last 365 Days"
msgstr "365 ימים אחרונים"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search_base
msgid "Last 7 Days"
msgstr "7 ימים אחרונים"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.team_rating_data
msgid "Last 7 days"
msgstr "7 ימים אחרונים"

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/controllers/portal.py:0
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__date_last_stage_update
msgid "Last Stage Update"
msgstr "עדכון השלב האחרון"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla__write_uid
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_status__write_uid
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage__write_uid
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage_delete_wizard__write_uid
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_tag__write_uid
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__write_uid
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__write_uid
msgid "Last Updated by"
msgstr "עודכן לאחרונה על-ידי"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla__write_date
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_status__write_date
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage__write_date
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage_delete_wizard__write_date
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_tag__write_date
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__write_date
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__write_date
msgid "Last Updated on"
msgstr "עדכון אחרון ב"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis_closed
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search
msgid "Late Activities"
msgstr "פעילויות באיחור"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.team_rating_data
msgid "Latest Ratings"
msgstr "ביקורות אחרונות"

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/js/tours/helpdesk.js:0
msgid "Let's create your first <b>ticket</b>."
msgstr "בוא ניצור את ה<b>קריאת שירות</b> הראשונה שלך."

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/js/tours/helpdesk.js:0
msgid ""
"Let's go back to the <b>kanban view</b> to get an overview of your next "
"tickets."
msgstr ""
"בוא נחזור <b>לתצוגת הקנבן </b>כדי לקבל סקירה כללית של הקריאות שירות הבאות "
"שלך."

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/js/tours/helpdesk.js:0
msgid "Let's view your <b>team's tickets</b>."
msgstr "בוא נראה את <b>קריאות השירות של הצוות שלך</b>."

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__use_website_helpdesk_livechat
msgid "Live Chat"
msgstr " צ'אט חי"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__alias_incoming_local
msgid "Local-part based incoming detection"
msgstr "זיהוי הודעות נכנסות מבוסס על החלק המקומי בכתובת"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search_base
msgid "Low Priority"
msgstr "עדיפות נמוכה"

#. module: helpdesk
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_sla__priority__0
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_sla_report_analysis__priority__0
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_ticket__priority__0
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_ticket_report_analysis__priority__0
msgid "Low priority"
msgstr "עדיפות נמוכה"

#. module: helpdesk
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_sla_action
msgid ""
"Make sure tickets are handled in a timely manner by using SLA Policies.<br>"
msgstr "ודא שהקריאות שירות מטופלות בזמן על ידי שימוש במדיניות SLA.<br>"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Make sure tickets are handled on time"
msgstr "הקפד לטפל בקריאות שירות בזמן"

#. module: helpdesk
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_sla_action_main
msgid "Make sure tickets are handled on time by using SLA Policies.<br>"
msgstr "ודא שהקריאות שירות מטופלות בזמן על ידי שימוש במדיניות SLA.<br>"

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_sla__time
msgid ""
"Maximum number of working hours a ticket should take to reach the target "
"stage, starting from the date it was created."
msgstr ""
"מספר שעות העבודה המקסימלי שכרטיס צריך לקחת כדי להגיע לשלב היעד, החל מתאריך "
"יצירתו."

#. module: helpdesk
#: model_terms:ir.actions.act_window,help:helpdesk.rating_rating_action_helpdesk
msgid ""
"Measure your customer satisfaction by sending rating requests when your "
"tickets are solved."
msgstr ""
"מדוד את שביעות הרצון של הלקוחות שלך על ידי שליחת בקשות דירוג כאשר קריאות "
"השירות שלך יפתרו."

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__medium_id
msgid "Medium"
msgstr "אמצעי תקשורת"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search_base
msgid "Medium Priority"
msgstr "עדיפות בינונית"

#. module: helpdesk
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_sla__priority__1
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_sla_report_analysis__priority__1
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_ticket__priority__1
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_ticket_report_analysis__priority__1
msgid "Medium priority"
msgstr "עדיפות בינונית"

#. module: helpdesk
#: model:ir.model,name:helpdesk.model_ir_ui_menu
msgid "Menu"
msgstr "תפריט"

#. module: helpdesk
#: model:ir.model,name:helpdesk.model_mail_message
msgid "Message"
msgstr "הודעה"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__message_has_error
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__message_has_error
msgid "Message Delivery error"
msgstr "הודעת שגיאת שליחה"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__message_ids
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__message_ids
msgid "Messages"
msgstr "הודעות"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__priority
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__priority
msgid "Minimum Priority"
msgstr "עדיפות מינימום"

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_sla__stage_id
#: model:ir.model.fields,help:helpdesk.field_helpdesk_sla_status__sla_stage_id
msgid "Minimum stage a ticket needs to reach in order to satisfy this SLA."
msgstr ""
"שלב מינימלי אליו צריכה להגיע קריאת שירות בכדי לספק הסכם תנאי שירות זה."

#. module: helpdesk
#: model:ir.model,name:helpdesk.model_ir_module_module
msgid "Module"
msgstr "מודול"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__to_stage_id
msgid "Move to Stage"
msgstr "עבר לשלב"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "מועד אחרון לפעילות שלי"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_tree
msgid "My Deadline"
msgstr "תאריך היעד האחרון לפעילת הזאת"

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/components/helpdesk_team_dashboard/helpdesk_team_dashboard.xml:0
msgid "My Performance"
msgstr "הביצועים שלי"

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/components/helpdesk_team_dashboard/helpdesk_team_dashboard.xml:0
#: model:ir.actions.act_window,name:helpdesk.helpdesk_ticket_action_main_my
#: model:ir.ui.menu,name:helpdesk.helpdesk_ticket_menu_my
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis_closed
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search_base
msgid "My Tickets"
msgstr "קריאות השירות שלי"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla__name
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage__name
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_tag__name
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_tree
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_list_analysis
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_tree
msgid "Name"
msgstr "שם"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_kanban
msgid "Neutral face"
msgstr "פרצוף ניטרלי"

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/models/helpdesk_team.py:0
#: model:helpdesk.stage,name:helpdesk.stage_new
msgid "New"
msgstr "חדש"

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team__assign_method
msgid ""
"New tickets will automatically be assigned to the team members that are "
"available, according to their working hours and their time off."
msgstr ""
"קריאות שירות חדשות יוקצו אוטומטית לחברי הצוות הפנויים, בהתאם לשעות העבודה "
"שלהם ולחופשה."

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/controllers/portal.py:0
msgid "Newest"
msgstr "החדש ביותר"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "הפעילות הבאה ביומן"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "מועד אחרון לפעילות הבאה"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__activity_summary
msgid "Next Activity Summary"
msgstr "תיאור הפעילות הבאה "

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__activity_type_id
msgid "Next Activity Type"
msgstr "סוג הפעילות הבאה"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.portal_helpdesk_ticket
msgid "No Customer"
msgstr "לקוח חסר"

#. module: helpdesk
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_sla_action
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_sla_action_main
msgid "No SLA policies found. Let's create one!"
msgstr "לא נמצאו הסכמי תנאי שירות. בוא ניצור אחד!"

#. module: helpdesk
#: model_terms:ir.actions.act_window,help:helpdesk.mail_activity_type_action_config_helpdesk
msgid "No activity types found. Let's create one!"
msgstr "לא נמצאו סוגי פעילות. בואו נייצר אחד!"

#. module: helpdesk
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_sla_report_analysis_action
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_sla_report_analysis_dashboard_action
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_7days_success
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_7days_tickets
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_7dayssuccess
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_close_analysis
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_dashboard
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_success
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_team_performance
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_analysis_action
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_analysis_dashboard_action
#: model_terms:ir.actions.act_window,help:helpdesk.rating_rating_action_helpdesk
msgid "No data yet!"
msgstr "אין מידע עדיין"

#. module: helpdesk
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_stage_action
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_stage_team_action
msgid "No stages found. Let's create one!"
msgstr "לא נמצאו שלבים. בואו ניצור אחת!"

#. module: helpdesk
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_tag_action
msgid "No tags found. Let's create one!"
msgstr "לא נמצאו תגיות. בואו ניצור אחת!"

#. module: helpdesk
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_team_dashboard_action_main
msgid "No teams found"
msgstr "לא נמצאו צוותים"

#. module: helpdesk
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_team_action
msgid "No teams found. Let's create one!"
msgstr "לא נמצא צוות. בוא ניצור אחד !"

#. module: helpdesk
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_my_ticket_action_no_create
msgid "No tickets found"
msgstr "לא נמצאו קריאות שירות"

#. module: helpdesk
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_main_my
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_main_tree
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_team
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_unassigned
msgid "No tickets found. Let's create one!"
msgstr "לא נמצאו קריאות שירות. בואו ניצור!"

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/controllers/portal.py:0
msgid "None"
msgstr "אף אחד"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__message_needaction_counter
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__message_needaction_counter
msgid "Number of Actions"
msgstr "מספר פעולות"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__sla_status_failed
msgid "Number of SLAs Failed"
msgstr "מספר SLAs שנכשלו"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage_delete_wizard__ticket_count
msgid "Number of Tickets"
msgstr "מספר קריאות שירות"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__message_has_error_counter
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__message_has_error_counter
msgid "Number of errors"
msgstr "מספר השגיאות"

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team__message_needaction_counter
#: model:ir.model.fields,help:helpdesk.field_helpdesk_ticket__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "מספר הודעות הדורשות פעולה"

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team__message_has_error_counter
#: model:ir.model.fields,help:helpdesk.field_helpdesk_ticket__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "מספר הודעות עם שגיאת משלוח"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
msgid "Number of open tickets with at least one SLA failed."
msgstr "מספר קריאות שירות פתוחות עם לפחות SLA אחד נכשל."

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__partner_open_ticket_count
msgid "Number of other open tickets from the same partner"
msgstr "מס' קריאות שירות שונות פתוחות מאותו לקוח"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__partner_ticket_count
msgid "Number of other tickets from the same partner"
msgstr "מס' קריאות שירות שונות מאותו לקוח"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
msgid "Number of tickets closed in the past 7 days."
msgstr "מספר קריאות שירות שנסגרו ב7 הימים האחרונים"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search_base
#: model_terms:ir.ui.view,arch_db:helpdesk.team_rating_progress_data
msgid "Okay"
msgstr "בסדר"

#. module: helpdesk
#: model:helpdesk.stage,name:helpdesk.stage_on_hold
msgid "On Hold"
msgstr "מושהה"

#. module: helpdesk
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_sla_status__status__ongoing
msgid "Ongoing"
msgstr "מתמשך"

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/controllers/portal.py:0
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search_base
msgid "Open"
msgstr "פתוח"

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/components/helpdesk_team_dashboard/helpdesk_team_dashboard.xml:0
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis
msgid "Open Tickets"
msgstr "קריאות שירות פתוחות"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__open_hours
msgid "Open Time (hours)"
msgstr "זמן פתוח (שעות)"

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/js/tours/helpdesk.js:0
msgid "Open the ticket."
msgstr "פתח את קריאת השירות ."

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team__alias_force_thread_id
msgid ""
"Optional ID of a thread (record) to which all incoming messages will be "
"attached, even if they did not reply to it. If set, this will disable the "
"creation of new records completely."
msgstr ""
"מזהה אפשרי של שרשור (רשומה) שאליו יצורפו כל ההודעות הנכנסות, גם אם לא השיבו "
"אליו. אם מוגדר, הדבר יבטל את יצירת הרשומות החדשות לחלוטין."

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.portal_my_home_menu_helpdesk
msgid "Our Ratings"
msgstr "הדירוגים שלנו"

#. module: helpdesk
#: model:ir.ui.menu,name:helpdesk.helpdesk_menu_team_dashboard
msgid "Overview"
msgstr "סקירה כללית"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__alias_parent_model_id
msgid "Parent Model"
msgstr "מודל אב"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__alias_parent_thread_id
msgid "Parent Record Thread ID"
msgstr "מזהה רשומת שרשור אב "

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team__alias_parent_model_id
msgid ""
"Parent model holding the alias. The model holding the alias reference is not"
" necessarily the model given by alias_model_id (example: project "
"(parent_model) and task (model))"
msgstr ""
"מודל אב המחזיק בכינוי. המודל המחזיק במזהה לכינוי אינו בהכרח המודל שניתן על "
"ידי alias_model_id (דוגמה: project (parent_model) ומשימה (model))"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__partner_ticket_ids
msgid "Partner Tickets"
msgstr "קריאת שירות לקוח"

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team__privacy_visibility
#: model:ir.model.fields,help:helpdesk.field_helpdesk_ticket__team_privacy_visibility
msgid ""
"People to whom this helpdesk team and its tickets will be visible.\n"
"\n"
"- Invited internal users: internal users can access the team and the tickets they are following. This access can be modified on each ticket individually by adding or removing the user as follower.\n"
"A user with the helpdesk > administrator access right level can still access this team and its tickets, even if they are not explicitely part of the followers.\n"
"\n"
"- All internal users: all internal users can access the team and all of its tickets without distinction.\n"
"\n"
"- Invited portal users and all internal users: all internal users can access the team and all of its tickets without distinction.\n"
"Portal users can only access the tickets they are following. This access can be modified on each ticket individually by adding or removing the portal user as follower."
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "People to whom this team and its tickets will be visible"
msgstr "אנשים שהצוות הזה וקריאות השירות בו יהיו גלויים להם"

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team__rating_percentage_satisfaction
msgid "Percentage of happy ratings"
msgstr "אחוז הדירוגים השמחים"

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/components/helpdesk_team_dashboard/helpdesk_team_dashboard.xml:0
msgid "Percentage of tickets that were closed without failing any SLAs."
msgstr "אחוז קריאות השירות שנסגרו מבלי שנכשלו ב-SLA."

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
msgid ""
"Percentage of tickets whose SLAs have successfully been reached on time over"
" the total number of tickets closed within the past 7 days."
msgstr ""
"אחוז קריאות השירות שה-SLA שלהם הושג בהצלחה בזמן על פני המספר הכולל של "
"הקריאות שירות  שנסגרו במהלך 7 הימים האחרונים."

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Performance"
msgstr "שיעורי רווחיות"

#. module: helpdesk
#: model:ir.actions.act_window,name:helpdesk.helpdesk_ticket_action_team_performance
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_graph_analysis
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_pivot_analysis
msgid "Performance Analysis"
msgstr "ניתוח ביצועים"

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team__auto_close_day
msgid "Period of inactivity after which tickets will be automatically closed."
msgstr "תקופת חוסר פעילות שלאחריה הקריאות שירות ייסגרו אוטומטית."

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_form
msgid "Phone"
msgstr "טלפון"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Plan onsite interventions"
msgstr "תכנן התערבויות במקום"

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/components/helpdesk_team_target/helpdesk_team_target.js:0
msgid "Please enter a number."
msgstr "אנא הכנס מספר."

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/components/helpdesk_team_target/helpdesk_team_target.js:0
msgid "Please enter a percentage below 100."
msgstr "אנא הכנס אחוז מתחת ל-100."

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/components/helpdesk_team_target/helpdesk_team_target.js:0
msgid "Please enter a positive value."
msgstr "אנא הכנס ערך חיובי."

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/components/helpdesk_team_target/helpdesk_team_target.js:0
msgid "Please enter a value less than or equal to 5."
msgstr "אנא הכנס ערך שווה או פחות מ-5. "

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team__alias_contact
msgid ""
"Policy to post a message on the document using the mailgateway.\n"
"- everyone: everyone can post\n"
"- partners: only authenticated partners\n"
"- followers: only followers of the related document or members of following channels\n"
msgstr ""
"מדיניות שליחת הודעה במסמך באמצעות שער הדואר.\n"
"- כולם: כולם יכולים לשלוח\n"
"- לקוחות/ספקים: רק לקוחות/ספקים מאומתים\n"
"- עוקבים: רק עוקבים של המסמך הקשור או חברים בערוצים הבאים\n"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__access_url
msgid "Portal Access URL"
msgstr "כתובת גישה לפורטל"

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/models/helpdesk_team.py:0
msgid ""
"Portal users will be removed from the followers of the team and its tickets."
msgstr "משתמשי הפורטל יוסרו מהעוקבים של הצוות והקריאות שירות שלו."

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla__priority
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__priority
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search_base
msgid "Priority"
msgstr "קְדִימוּת"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__properties
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search
msgid "Properties"
msgstr "מאפיינים"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_tree
msgid "Rating"
msgstr "דירוג"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__rating_last_value
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__rating_last_value
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_graph_main
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_pivot_main
#: model_terms:ir.ui.view,arch_db:helpdesk.rating_rating_pivot_inherit_helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.rating_rating_view_graph_inherit_helpdesk
msgid "Rating (1-5)"
msgstr "דירוג (1-5)"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__rating_avg_text
msgid "Rating Avg Text"
msgstr "דירוג טקסט ממוצע"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__rating_last_feedback
msgid "Rating Last Feedback"
msgstr "דירוג משוב אחרון"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__rating_last_image
msgid "Rating Last Image"
msgstr "דירוג תמונה אחרונה"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__rating_last_value
msgid "Rating Last Value"
msgstr "דירוג ערך אחרון"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__rating_percentage_satisfaction
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__rating_percentage_satisfaction
msgid "Rating Satisfaction"
msgstr "שביעות רצון דירוג"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__rating_last_text
msgid "Rating Text"
msgstr "סטטוס דירוג"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__rating_count
msgid "Rating count"
msgstr "כמות דירוגים"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__rating_ids
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__rating_ids
msgid "Ratings"
msgstr "דירוגים"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_view_form
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_view_tree
msgid "Reach Stage"
msgstr "הגעה לשלב"

#. module: helpdesk
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_sla_status__status__reached
msgid "Reached"
msgstr "הגיע"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_status__reached_datetime
msgid "Reached Date"
msgstr "תאריך הגעה"

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/models/helpdesk_stage.py:0
#: model:helpdesk.stage,legend_done:helpdesk.stage_cancelled
#: model:helpdesk.stage,legend_done:helpdesk.stage_in_progress
#: model:helpdesk.stage,legend_done:helpdesk.stage_new
#: model:helpdesk.stage,legend_done:helpdesk.stage_on_hold
#: model:helpdesk.stage,legend_done:helpdesk.stage_solved
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_ticket__kanban_state__done
#: model_terms:ir.ui.view,arch_db:helpdesk.portal_helpdesk_ticket
msgid "Ready"
msgstr "מוכן"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid ""
"Receive notifications whenever tickets are created, rated or discussed on in"
" this team"
msgstr ""
"קבל הודעות בכל פעם שקריאות שירות נוצרות, מדורגות או מדוברות בדיון בצוות הזה"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__alias_force_thread_id
msgid "Record Thread ID"
msgstr "מזהה רשומת שרשור"

#. module: helpdesk
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_sla_report_analysis__kanban_state__blocked
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_ticket_report_analysis__kanban_state__blocked
msgid "Red"
msgstr "אדום"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage__legend_blocked
msgid "Red Kanban Label"
msgstr "תווית קנבן אדומה"

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/controllers/portal.py:0
msgid "Reference"
msgstr "מזהה"

#. module: helpdesk
#: model:mail.message.subtype,name:helpdesk.mt_ticket_refund_status
msgid "Refund Status"
msgstr "סטטוס זיכוי"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__use_credit_notes
msgid "Refunds"
msgstr "החזרים כספיים"

#. module: helpdesk
#: model:helpdesk.tag,name:helpdesk.tag_repair
msgid "Repair"
msgstr "תיקון "

#. module: helpdesk
#: model:mail.message.subtype,name:helpdesk.mt_ticket_repair_status
msgid "Repair Status"
msgstr "סטטוס תיקון"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__use_product_repairs
msgid "Repairs"
msgstr "תיקונים"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.portal_helpdesk_ticket
msgid "Reported on"
msgstr "תאריך יצירה"

#. module: helpdesk
#: model:ir.ui.menu,name:helpdesk.helpdesk_ticket_report_menu_main
msgid "Reporting"
msgstr "דו\"חות"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__activity_user_id
msgid "Responsible User"
msgstr "משתמש אחראי"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_kanban
msgid "Restore"
msgstr "שחזר"

#. module: helpdesk
#: model:mail.message.subtype,name:helpdesk.mt_ticket_return_status
msgid "Return Status"
msgstr "סטטוס החזרה"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Return faulty products"
msgstr "החזר מוצרים פגומים"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__use_product_returns
msgid "Returns"
msgstr "החזרות"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__sla_id
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search_base
msgid "SLA"
msgstr "הסכם תנאי שירות"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__sla_deadline
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__sla_deadline
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search_base
msgid "SLA Deadline"
msgstr "תאריך יעד הסכם תנאי שירות"

#. module: helpdesk
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_sla_report_analysis__sla_status__failed
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis_closed
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search_base
msgid "SLA Failed"
msgstr "הסכם תנאי שירות (SLA) נכשל"

#. module: helpdesk
#: model:ir.actions.act_window,name:helpdesk.helpdesk_sla_action
#: model:ir.actions.act_window,name:helpdesk.helpdesk_sla_action_main
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__use_sla
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__use_sla
#: model:ir.model.fields,field_description:helpdesk.field_res_partner__sla_ids
#: model:ir.model.fields,field_description:helpdesk.field_res_users__sla_ids
#: model:ir.ui.menu,name:helpdesk.helpdesk_sla_menu_main
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_view_tree
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "SLA Policies"
msgstr "מדיניות הסכם תנאי שירות"

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_res_partner__sla_ids
#: model:ir.model.fields,help:helpdesk.field_res_users__sla_ids
msgid ""
"SLA Policies that will automatically apply to the tickets submitted by this "
"customer."
msgstr "מדיניות SLA שתחול אוטומטית על הקריאות שירות שנשלחו על ידי לקוח זה."

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_view_form
msgid "SLA Policy"
msgstr "מדיניות הסכם תנאי שירות"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla__description
msgid "SLA Policy Description"
msgstr "תיאור מדיניות הסכם תנאי שירות"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__sla_stage_id
msgid "SLA Stage"
msgstr "שלב הסכם תנאי שירות"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__sla_status_ids
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__sla_status_ids
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__sla_status_ids
msgid "SLA Status"
msgstr "סטטוס הסכם תנאי שירות"

#. module: helpdesk
#: model:ir.actions.act_window,name:helpdesk.helpdesk_sla_report_analysis_action
#: model:ir.actions.act_window,name:helpdesk.helpdesk_sla_report_analysis_dashboard_action
#: model:ir.model,name:helpdesk.model_helpdesk_sla_report_analysis
#: model:ir.ui.menu,name:helpdesk.helpdesk_ticket_report_menu_sla_analysis
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_report_analysis_view_cohort
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_report_analysis_view_graph
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_report_analysis_view_pivot
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
msgid "SLA Status Analysis"
msgstr "ניתוח סטטוס הסכם תנאי שירות"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__sla_fail
msgid "SLA Status Failed"
msgstr "סטטוס הסכם תנאי שירות (SLA) נכשל"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__sla_success
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__sla_success
msgid "SLA Status Success"
msgstr "הצלחת סטטוס SLA"

#. module: helpdesk
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_sla_report_analysis__sla_status__reached
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search_base
msgid "SLA Success"
msgstr "SLA מוצלח"

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/components/helpdesk_team_dashboard/helpdesk_team_dashboard.xml:0
msgid "SLA Success Rate"
msgstr "אחוז הצלחה של הסכם תנאי שירות"

#. module: helpdesk
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_sla_report_analysis__sla_status__ongoing
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search_base
msgid "SLA in Progress"
msgstr "SLA בתהליך"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__sla_ids
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__sla_ids
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__sla_ids
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_tree
msgid "SLAs"
msgstr "הסכמי תנאי שירות"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__message_has_sms_error
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__message_has_sms_error
msgid "SMS Delivery error"
msgstr "שגיאה בשליחת SMS"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_kanban
msgid "Sad face"
msgstr "פרצוף עצוב"

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/components/helpdesk_team_dashboard/helpdesk_team_dashboard.xml:0
msgid "Sample"
msgstr "דגימה"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search_base
#: model_terms:ir.ui.view,arch_db:helpdesk.team_rating_progress_data
msgid "Satisfied"
msgstr "מרוצה"

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/js/tours/helpdesk.js:0
msgid "Save this ticket and the modifications you've made to it."
msgstr "שמור קריאת שירות זו ואת השינויים שביצעת בה."

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/js/tours/helpdesk.js:0
msgid "Schedule your <b>activity</b>."
msgstr "תזמן את <b>הפעילות</b> שלך."

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_view_search
msgid "Search SLA Policies"
msgstr "חפש מדיניות הסכם תנאי שירות"

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/controllers/portal.py:0
msgid "Search in Assigned to"
msgstr "חפש במוקצה ל"

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/controllers/portal.py:0
msgid "Search in Customer"
msgstr "חפש בלקוח"

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/controllers/portal.py:0
msgid "Search in Helpdesk Team"
msgstr "חפש ב-צוות מוקד תמיכה ללקוחות "

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/controllers/portal.py:0
msgid "Search in Stage"
msgstr "חפש בשלב"

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/controllers/portal.py:0
msgid "Search%(left)s Tickets%(right)s"
msgstr "חפש %(left)s קריאות שירות %(right)s"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__access_token
msgid "Security Token"
msgstr "אסימון אבטחה"

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/js/tours/helpdesk.js:0
msgid "Select the <b>customer</b> of your ticket."
msgstr "בחר את ה<b>לקוח</b> לקריאות השירות שלך."

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Self-Service"
msgstr "שירות עצמי"

#. module: helpdesk
#: model:ir.actions.act_window,name:helpdesk.action_helpdesk_ticket_mass_mail
msgid "Send Email"
msgstr "שלח דוא\"ל"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Send broken products for repair"
msgstr "שלח מוצרים שבורים לתיקון"

#. module: helpdesk
#: model:mail.template,description:helpdesk.new_ticket_request_email_template
msgid ""
"Send customers a confirmation email to notify them that their helpdesk "
"ticket has been received and is currently being reviewed by the helpdesk "
"team. Automatically send an email to customers when a ticket reaches a "
"specific stage in a helpdesk team by setting this template on that stage."
msgstr ""
"שלח ללקוחות דוא\"ל אישור כדי להודיע ​​להם שקריאת השירות שלהם התקבלה ונבדקת "
"כעת על ידי צוות המוקד לתמיכה בלקוחות. שלח דוא\"ל אוטומטית ללקוחות כאשר קריאת"
" השירות מגיעה לשלב מסוים בצוות המוקד לתמיכה בלקוחות על ידי הגדרת תבנית זו "
"בשלב זה."

#. module: helpdesk
#: model:helpdesk.tag,name:helpdesk.tag_service
msgid "Service"
msgstr "שירות"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Set an Email Template on Stages"
msgstr "הגדר תבנית דוא\"ל בשלבים"

#. module: helpdesk
#: model:mail.template,description:helpdesk.solved_ticket_request_email_template
msgid ""
"Set this template on a project's stage to automate email when tasks reach "
"stages"
msgstr ""
"הגדר תבנית זו בשלב של פרויקט כדי להפוך דוא\"ל לאוטומטי כאשר המשימות מגיעות "
"לשלבים"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
msgid "Settings"
msgstr "הגדרות"

#. module: helpdesk
#: model:ir.actions.act_window,name:helpdesk.portal_share_action
msgid "Share Ticket"
msgstr "שתף קריאת שירות"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid ""
"Share presentations and videos, and organize them into courses. Allow "
"customers to search your eLearning courses in the help center for answers to"
" their questions."
msgstr ""
"שתף מצגות וסרטונים וארגן אותם לקורסים. אפשר ללקוחות לחפש בקורסי ה-eLearning "
"שלך במרכז העזרה כדי לקבל תשובות לשאלותיהם."

#. module: helpdesk
#: model:res.groups,name:helpdesk.group_use_rating
msgid "Show Customer Ratings"
msgstr "הצג דירוגי לקוח "

#. module: helpdesk
#: model:res.groups,name:helpdesk.group_use_sla
msgid "Show SLA Policies"
msgstr "הצג מדיניות הסכם תנאי שירות"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis_closed
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search
msgid "Show all records which has next action date is before today"
msgstr "הצג את כל הרשומות שתאריך הפעולה הבא שלהן הוא עד היום"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_status__sla_id
msgid "Sla"
msgstr "הסכם תנאי שירות"

#. module: helpdesk
#: model:helpdesk.stage,name:helpdesk.stage_solved
msgid "Solved"
msgstr "טופל"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__source_id
msgid "Source"
msgstr "מקור"

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/controllers/portal.py:0
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__stage_id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__stage_id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__stage_id
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_stage_view_tree
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search_base
#: model_terms:ir.ui.view,arch_db:helpdesk.portal_helpdesk_ticket
msgid "Stage"
msgstr "שלב"

#. module: helpdesk
#: model:mail.message.subtype,description:helpdesk.mt_ticket_stage
#: model:mail.message.subtype,name:helpdesk.mt_ticket_stage
msgid "Stage Changed"
msgstr "השלב השתנה"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_stage_view_form
msgid "Stage Search"
msgstr "חפש שלב"

#. module: helpdesk
#: model:ir.actions.act_window,name:helpdesk.helpdesk_stage_action
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__stage_ids
#: model:ir.ui.menu,name:helpdesk.helpdesk_stage_menu
msgid "Stages"
msgstr "שלבים"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage_delete_wizard__stage_ids
msgid "Stages To Delete"
msgstr "שלבים למחיקה"

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team__stage_ids
msgid ""
"Stages the team will use. This team's tickets will only be able to be in "
"these stages."
msgstr ""
"שלבים בהם הצוות ישתמש. קריאות השירות של הצוות הזה יוכלו להיות רק בשלבים אלה."

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/controllers/portal.py:0
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__sla_status
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_status__status
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search_base
msgid "Status"
msgstr "סטטוס"

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_ticket__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"סטטוס על בסיס פעילויות\n"
"איחור: תאריך היעד כבר חלף\n"
"היום: תאריך הפעילות הוא היום\n"
"מתוכנן: פעילויות עתידיות."

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__duration_tracking
msgid "Status time"
msgstr "סטטוס זמן"

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/controllers/portal.py:0
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__name
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__name
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__name
msgid "Subject"
msgstr "נושא"

#. module: helpdesk
#: model:ir.actions.act_window,name:helpdesk.helpdesk_ticket_action_7days_success
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__success_rate
msgid "Success Rate"
msgstr "שיעור הצלחה"

#. module: helpdesk
#: model:ir.actions.act_window,name:helpdesk.helpdesk_ticket_action_7dayssuccess
#: model:ir.actions.act_window,name:helpdesk.helpdesk_ticket_action_success
msgid "Success Rate Analysis"
msgstr "ניתוח שיעורי הצלחה"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__sla_success
msgid "Success SLA Policy"
msgstr "מדיניות SLA מוצלחת"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tag_view_tree
msgid "Tag"
msgstr "תגית"

#. module: helpdesk
#: model:ir.actions.act_window,name:helpdesk.helpdesk_tag_action
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla__tag_ids
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__tag_ids
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__tag_ids
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__tag_ids
#: model:ir.ui.menu,name:helpdesk.helpdesk_tag_menu
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tag_view_form
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search_base
msgid "Tags"
msgstr "תגיות"

#. module: helpdesk
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_tag_action
msgid "Tags are perfect for organizing your tickets."
msgstr "תגיות הן הפתרון המושלם לסידור קריאות השירות שלך."

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_view_form
msgid "Target"
msgstr "יעד"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla__stage_id
msgid "Target Stage"
msgstr "שלב יעד"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.rating_rating_view_form_inherit_helpdesk
msgid "Team"
msgstr "צוות"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__member_ids
msgid "Team Members"
msgstr "חברי צוות"

#. module: helpdesk
#: model:ir.actions.act_window,name:helpdesk.helpdesk_stage_team_action
msgid "Team Stages"
msgstr "שלבי צוות"

#. module: helpdesk
#: model:ir.actions.act_window,name:helpdesk.email_template_action_helpdesk
msgid "Templates"
msgstr "תבניות"

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team__alias_model_id
msgid ""
"The model (Odoo Document Kind) to which this alias corresponds. Any incoming"
" email that does not reply to an existing record will cause the creation of "
"a new record of this model (e.g. a Project Task)"
msgstr ""
"המודל (סוג מסמך Odoo) שאליו הכינוי הזה תואם. כל דוא\"ל נכנס שלא יענה לרשומה "
"קיימת יביא ליצירת רשומה חדשה של מודל זה (למשל משימת פרויקט)"

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team__alias_name
msgid ""
"The name of the email alias, e.g. 'jobs' if you want to catch emails for "
"<<EMAIL>>"
msgstr ""
"שם כינוי הדוא\"ל, למשל 'עבודות' אם ברצונך לקבל הודעות דוא\"ל ל "
"<<EMAIL>>"

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/controllers/portal.py:0
msgid "The team does not allow ticket closing through portal"
msgstr "הצוות אינו מאפשר סגירת קריאות שירות דרך הפורטל"

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_sla__exclude_stage_ids
msgid ""
"The time spent in these stages won't be taken into account in the "
"calculation of the SLA."
msgstr "הזמן המושקע בשלבים אלה לא יילקח בחשבון בחישוב ה-SLA."

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/models/helpdesk_team.py:0
msgid ""
"The visibility of the team needs to be set as \"Invited portal users and all"
" internal users\" in order to use the website form."
msgstr ""
"יש להגדיר את הנראות של הצוות כ\"משתמשי פורטל מוזמנים וכל המשתמשים הפנימיים\""
" על מנת להשתמש בטופס האתר."

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.portal_helpdesk_ticket
msgid "There are currently no Ticket for your account."
msgstr "כרגע אין קריאות שירות לחשבונך."

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.ticket_creation
msgid "This"
msgstr "זה"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_view_form
msgid ""
"This SLA Policy will apply to tickets matching ALL of the following "
"criteria:"
msgstr "מדיניות SLA זו תחול על קריאות שירות התואמות את כל הקריטריונים הבאים:"

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_ticket__campaign_id
msgid ""
"This is a name that helps you keep track of your different campaign efforts,"
" e.g. Fall_Drive, Christmas_Special"
msgstr ""
"זהו שם שעוזר לך לעקוב אחר מאמצי הקמפיין השונים שלך, למשל Fall_Drive, "
"Christmas_Special"

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_ticket__medium_id
msgid "This is the method of delivery, e.g. Postcard, Email, or Banner Ad"
msgstr "זוהי שיטת המשלוח, למשל גלויה, דוא\"ל או מודעת באנר"

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_ticket__source_id
msgid ""
"This is the source of the link, e.g. Search Engine, another domain, or name "
"of email list"
msgstr "זהו מקור הקישור, למשל מנוע חיפוש, דומיין אחר או שם של רשימת דוא\"ל"

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/models/helpdesk_ticket.py:0
msgid "This ticket was closed %s hours after its SLA deadline."
msgstr "קריאת השירות נסגרה %s שעות אחרי הSLA"

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/models/helpdesk_ticket.py:0
msgid "This ticket was successfully closed %s hours before its SLA deadline."
msgstr "קריאת השירות נסגרה בהצלחה %s שעות לפני הSLA"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.view_helpdesk_stage_delete_confirmation_wizard
msgid ""
"This will archive the stages and all of the tickets they contain from the "
"following teams:"
msgstr ""
"פעולה זו תכניס לארכיון את השלבים ואת כל הקריאות השירות שהם מכילים מהצוותים "
"הבאים:"

#. module: helpdesk
#: model_terms:ir.actions.act_window,help:helpdesk.mail_activity_type_action_config_helpdesk
msgid ""
"Those represent the different categories of things you have to do (e.g. "
"\"Call\" or \"Send email\")."
msgstr ""
"אלה מייצגים את הקטגוריות השונות של דברים שעליך לעשות (למשל \"התקשר\" או "
"\"שלח אימייל\")."

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/components/helpdesk_team_dashboard/helpdesk_team_dashboard.xml:0
msgid "Three stars, maximum score"
msgstr "שלושה כוכבים, ציון מקסימלי"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__ticket_id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_status__ticket_id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__ticket_id
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_activity
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search_base
#: model_terms:ir.ui.view,arch_db:helpdesk.portal_helpdesk_ticket
#: model_terms:ir.ui.view,arch_db:helpdesk.rating_rating_view_form_inherit_helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.rating_rating_view_search_inherit_helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.rating_rating_view_tree_inherit_helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.tickets_followup
msgid "Ticket"
msgstr "קריאת שירות"

#. module: helpdesk
#: model:ir.actions.act_window,name:helpdesk.helpdesk_ticket_action_dashboard
#: model:ir.actions.act_window,name:helpdesk.helpdesk_ticket_analysis_dashboard_action
#: model:ir.model,name:helpdesk.model_helpdesk_ticket_report_analysis
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_pivot_analysis
msgid "Ticket Analysis"
msgstr "ניתוח נתוני קריאות שירות"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__ticket_closed
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__ticket_closed
msgid "Ticket Closed"
msgstr "קריאת שירות נסגרה"

#. module: helpdesk
#: model:mail.template,subject:helpdesk.solved_ticket_request_email_template
msgid "Ticket Closed - Reference {{ object.id if object.id else 15 }}"
msgstr "קריאת שירות- אסמכתא {{ object.id if object.id else 15 }}"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla__ticket_count
msgid "Ticket Count"
msgstr "כמות פניות"

#. module: helpdesk
#: model:mail.message.subtype,name:helpdesk.mt_team_ticket_new
#: model:mail.message.subtype,name:helpdesk.mt_ticket_new
msgid "Ticket Created"
msgstr "קריאת שירות נוצרה"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__create_date
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__create_date
msgid "Ticket Creation Date"
msgstr "תאריך יצירת קריאת שירות"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__sla_deadline
msgid "Ticket Deadline"
msgstr "תאריך יעד קריאת שירות"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__ticket_ref
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__ticket_ref
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__ticket_ref
msgid "Ticket IDs Sequence"
msgstr "רצף מזהה קריאות שירות"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__ticket_properties
msgid "Ticket Properties"
msgstr "מאפייני קריאת שירות"

#. module: helpdesk
#: model:mail.message.subtype,name:helpdesk.mt_team_ticket_rated
#: model:mail.message.subtype,name:helpdesk.mt_ticket_rated
msgid "Ticket Rated"
msgstr "קריאת שירות שדורגה"

#. module: helpdesk
#: model:ir.model,name:helpdesk.model_helpdesk_sla_status
msgid "Ticket SLA Status"
msgstr "סטטוס הסכם תנאי שירות (SLA) של קריאת שירות"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.quick_create_ticket_form
msgid "Ticket Title"
msgstr "כותרת קריאת שירות"

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/controllers/portal.py:0
msgid "Ticket closed by the customer"
msgstr "כרטיס נסגר על ידי הלקוח"

#. module: helpdesk
#: model:mail.message.subtype,description:helpdesk.mt_ticket_new
msgid "Ticket created"
msgstr "קריאת שירות נוצרה"

#. module: helpdesk
#. odoo-javascript
#. odoo-python
#: code:addons/helpdesk/models/helpdesk_team.py:0
#: code:addons/helpdesk/static/src/components/helpdesk_team_dashboard/helpdesk_team_dashboard.xml:0
#: model:ir.actions.act_window,name:helpdesk.helpdesk_my_ticket_action_no_create
#: model:ir.actions.act_window,name:helpdesk.helpdesk_ticket_action_sla
#: model:ir.actions.act_window,name:helpdesk.helpdesk_ticket_action_team
#: model:ir.actions.act_window,name:helpdesk.helpdesk_ticket_action_unassigned
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__ticket_ids
#: model:ir.model.fields,field_description:helpdesk.field_res_partner__ticket_count
#: model:ir.model.fields,field_description:helpdesk.field_res_users__ticket_count
#: model:ir.ui.menu,name:helpdesk.helpdesk_ticket_menu_main
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_view_form
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_stage_view_form
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_cohort
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_list_analysis
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_tree
#: model_terms:ir.ui.view,arch_db:helpdesk.portal_helpdesk_ticket
#: model_terms:ir.ui.view,arch_db:helpdesk.portal_my_home_helpdesk_ticket
#: model_terms:ir.ui.view,arch_db:helpdesk.portal_my_home_menu_helpdesk
msgid "Tickets"
msgstr "קריאות שירות"

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/components/helpdesk_team_dashboard/helpdesk_team_dashboard.xml:0
#: model:ir.actions.act_window,name:helpdesk.helpdesk_ticket_analysis_action
#: model:ir.ui.menu,name:helpdesk.helpdesk_ticket_report_menu
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_report_analysis_view_tree
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_report_view_cohort
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_graph_analysis
msgid "Tickets Analysis"
msgstr "ניתוח נתוני קריאות שירות"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_digest_digest__kpi_helpdesk_tickets_closed
msgid "Tickets Closed"
msgstr "קריאות שירות נסגרו"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis_closed
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search_base
msgid "Tickets Search"
msgstr "חיפוש קריאות שירות"

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_stage__fold
#: model:ir.model.fields,help:helpdesk.field_helpdesk_ticket__fold
msgid "Tickets in a folded stage are considered as closed."
msgstr "קריאות שירות במצב מקופל נחשבות כסגורות."

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__use_helpdesk_sale_timesheet
msgid "Time Billing"
msgstr "חיוב זמנים"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__close_hours
msgid "Time to close (hours)"
msgstr "זמן לסגירה (שעות)"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__assign_hours
msgid "Time to first assignment (hours)"
msgstr "זמן עד לשיוך ראשוני (בשעות)"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__use_helpdesk_timesheet
msgid "Timesheets"
msgstr "דיווחי שעות"

#. module: helpdesk
#: model:digest.tip,name:helpdesk.digest_tip_helpdesk_0
msgid "Tip: Create tickets from incoming emails"
msgstr "טיפ: צור קריאות שירות ממיילים נכנסים"

#. module: helpdesk
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_my_ticket_action_no_create
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_main_my
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_main_tree
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_team
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_unassigned
msgid ""
"To get things done, plan activities and use the ticket status.<br>\n"
"                Collaborate efficiently by chatting in real-time or via email."
msgstr ""
"כדי לבצע דברים, תכנן פעילויות והשתמש בסטטוס קריאת השירות. שתף פעולה ביעילות "
"באמצעות צ'אט חי או דוא\"ל"

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/components/helpdesk_team_dashboard/helpdesk_team_dashboard.xml:0
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search_base
#: model_terms:ir.ui.view,arch_db:helpdesk.rating_rating_view_search_inherit_helpdesk
msgid "Today"
msgstr "היום"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis_closed
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search
msgid "Today Activities"
msgstr "פעילויות היום"

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/components/helpdesk_team_dashboard/helpdesk_team_dashboard.xml:0
msgid "Today's Average Rating"
msgstr "הדירוג הממוצע היומי"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__total_response_hours
msgid "Total Exchange Time in Hours"
msgstr "זמן החלפה כולל בשעות"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Track &amp; Bill Time"
msgstr "מעקב וזמן חיוב"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Track customer satisfaction on tickets"
msgstr "עקוב אחר שביעות רצון הלקוחות על קריאות השירות"

#. module: helpdesk
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_sla_report_analysis_action
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_sla_report_analysis_dashboard_action
msgid ""
"Track the performance of your teams, the success rate of your tickets, and "
"how quickly you reach your service level agreements (SLAs)."
msgstr ""
"עקוב אחר הביצועים של הצוותים שלך, שיעור ההצלחה של הקריאות שירות שלך וכמה מהר"
" אתה מגיע להסכמי רמת השירות שלך (SLAs)."

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Track the time spent on tickets"
msgstr "עקוב אחר הזמן המושקע בקריאות שירות"

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/components/helpdesk_team_dashboard/helpdesk_team_dashboard.xml:0
msgid "Two stars, with a maximum of three"
msgstr "שני כוכבים, עם מקסימום שלושה"

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_ticket__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "סוג הפעילות החריגה ברשומה."

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/models/helpdesk_stage.py:0
msgid "Unarchive Tickets"
msgstr "הוצא מהארכיון קריאות שירות"

#. module: helpdesk
#. odoo-javascript
#. odoo-python
#: code:addons/helpdesk/controllers/portal.py:0
#: code:addons/helpdesk/static/src/views/helpdesk_ticket_graph/helpdesk_ticket_graph_model.js:0
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search_base
#: model_terms:ir.ui.view,arch_db:helpdesk.portal_helpdesk_ticket
msgid "Unassigned"
msgstr "לא משויך"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__unassigned_tickets
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis_closed
msgid "Unassigned Tickets"
msgstr "קריאות שירות לא משויכות"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search
msgid "Unread Messages"
msgstr "הודעות שלא נקראו"

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/components/helpdesk_team_dashboard/helpdesk_team_dashboard.xml:0
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_sla__priority__3
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_sla_report_analysis__priority__3
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_ticket__priority__3
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_ticket_report_analysis__priority__3
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search_base
msgid "Urgent"
msgstr "דחוף"

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/js/tours/helpdesk.js:0
msgid "Use <b>activities</b> to organize your daily work."
msgstr "השתמש<b>בפעילויות</b>לארגן את העבודה היומיומית שלך."

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__use_alias
msgid "Use Alias"
msgstr "השתמש בכינויים"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__use_coupons
msgid "Use Coupons"
msgstr "השתמש בקופונים"

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/js/tours/helpdesk.js:0
msgid ""
"Use the chatter to <b>send emails</b> and communicate efficiently with your "
"customers. Add new people to the followers' list to make them aware of the "
"progress of this ticket."
msgstr ""
"השתמש בצ'אט כדי <b>לשלוח מיילים </b>ולתקשר ביעילות עם הלקוחות שלך. הוסף "
"אנשים חדשים לרשימת העוקבים כדי לגרום להם להיות מודעים להתקדמות בקריאת שירות "
"זו."

#. module: helpdesk
#: model:ir.model,name:helpdesk.model_res_users
#: model:res.groups,name:helpdesk.group_helpdesk_user
msgid "User"
msgstr "משתמש"

#. module: helpdesk
#: model:helpdesk.team,name:helpdesk.helpdesk_team3
msgid "VIP Support"
msgstr "תמיכת VIP"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__privacy_visibility
msgid "Visibility"
msgstr "יוצג ל:"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Visibility &amp; Assignment"
msgstr "נראות &amp; הקצאה"

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/js/tours/helpdesk.js:0
msgid ""
"Want to <b>boost your customer satisfaction</b>?<br/><i>Click Helpdesk to "
"start.</i>"
msgstr ""
"מעוניין <b>לשפר את שביעות רצון הלקוחות</b>?<br/><i>לחץ על מוקד תמיכה כדי "
"להתחיל.</i>"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.tickets_followup
msgid ""
"We hope to have addressed your request satisfactorily. If you no longer need"
" our assistance, please close this ticket. Thank you for your collaboration."
msgstr ""
"אנו מקווים להתייחס לבקשתך בצורה משביעת רצון. אם אינך זקוק עוד לעזרתנו, אנא "
"סגור קריאת שירות זו . תודה על שיתוף הפעולה."

#. module: helpdesk
#: model_terms:helpdesk.team,description:helpdesk.helpdesk_team1
#: model_terms:helpdesk.team,description:helpdesk.helpdesk_team3
msgid ""
"We provide 24/7 support, Monday through Friday. Ticket responses are usually provided within 2 working days.<br>\n"
"            Support is mainly provided in English. We can also assist in Spanish, French, and Dutch."
msgstr ""
"אנו מספקים תמיכה 24/7, שני עד שישי. תשובות לקריאות שירות ניתנות בדרך כלל תוך 2 ימי עבודה.<br>\n"
"התמיכה ניתנת בעיקר באנגלית. אנחנו יכולים לסייע גם בספרדית, צרפתית והולנדית."

#. module: helpdesk
#: model:helpdesk.tag,name:helpdesk.tag_website
msgid "Website"
msgstr "אתר אינטרנט"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__use_website_helpdesk_form
msgid "Website Form"
msgstr "טופס אתר אינטרנט"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__website_message_ids
msgid "Website Messages"
msgstr "הודעות מאתר האינטרנט"

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team__website_message_ids
#: model:ir.model.fields,help:helpdesk.field_helpdesk_ticket__website_message_ids
msgid "Website communication history"
msgstr "היסטורית התקשרויות מאתר האינטרנט"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla__time
msgid "Within"
msgstr "בתוך"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__resource_calendar_id
msgid "Working Hours"
msgstr "שעות עבודה "

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__ticket_assignation_hours
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__ticket_assignation_hours
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_graph_main
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_pivot_main
msgid "Working Hours to Assign"
msgstr "שעות עבודה לשיוך"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__ticket_close_hours
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__ticket_close_hours
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_graph_main
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_pivot_main
msgid "Working Hours to Close"
msgstr "שעות עבודה לסגירה"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__sla_exceeded_hours
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__sla_deadline_hours
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__ticket_deadline_hours
msgid "Working Hours until SLA Deadline"
msgstr "שעות עבודה עד לתאריך אחרון ל-SLA"

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_sla_status__exceeded_hours
msgid ""
"Working hours exceeded for reached SLAs compared with deadline. Positive "
"number means the SLA was reached after the deadline."
msgstr ""
"חריגה משעות העבודה עבור SLAs שהגיעו בהשוואה למועד האחרון. מספר חיובי אומר "
"שה-SLA הושג לאחר המועד האחרון."

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team__resource_calendar_id
msgid "Working hours used to determine the deadline of SLA Policies."
msgstr "שעות העבודה המשמשות לקביעת המועד האחרון של מדיניות SLA."

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.view_helpdesk_stage_unarchive_wizard
msgid ""
"Would you like to unarchive all of the tickets contained in these stages as "
"well?"
msgstr "האם תרצה להסיר מהארכיון גם את כל הקריאות השירות הכלולות בשלבים אלה?"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.view_helpdesk_stage_delete_wizard
msgid ""
"You cannot delete stages containing tickets. You can either archive them or "
"first delete all of their tickets."
msgstr ""
"לא ניתן למחוק שלבים המכילים קריאות שירות. אתה יכול לאחסן אותם בארכיון או "
"למחוק תחילה את כל קריאות השירות שלהם."

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.view_helpdesk_stage_delete_wizard
msgid ""
"You cannot delete stages containing tickets. You should first delete all of "
"their tickets."
msgstr ""
"לא ניתן למחוק שלבים המכילים קריאות שירות. תחילה עליך למחוק את כל הקריאות "
"השירות שלהם."

#. module: helpdesk
#: model:ir.model.constraint,message:helpdesk.constraint_res_users_target_closed_not_zero
#: model:ir.model.constraint,message:helpdesk.constraint_res_users_target_rating_not_zero
#: model:ir.model.constraint,message:helpdesk.constraint_res_users_target_success_not_zero
msgid "You cannot have negative targets"
msgstr "לא יכולים להיות לך יעדים שליליים"

#. module: helpdesk
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_sla
msgid "You completed all your tickets on time."
msgstr "השלמת את כל הקריאות שירות שלך בזמן."

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/models/helpdesk_ticket.py:0
msgid "You have been invited to follow %s"
msgstr "הוזמנת לעקוב אחרי%s"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.ticket_invitation_follower
msgid "You have been invited to follow Ticket Document :"
msgstr "הוזמנת לעקוב אחר מסמך של קריאת שירות:"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "alias"
msgstr "כינויים"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_view_form
msgid "e.g. Close urgent tickets within 36 hours"
msgstr "למשל: סגור קריאות שירות דחופות תוך 36 שעות"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "e.g. Customer Care"
msgstr "למשל, שירות לקוחות"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "e.g. My Company"
msgstr "לדוגמא: \"החברה שלי\""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_form
#: model_terms:ir.ui.view,arch_db:helpdesk.quick_create_ticket_form
msgid "e.g. Product arrived damaged"
msgstr "למשל, מוצר הגיע פגום"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "e.g. mycompany.com"
msgstr "למשל, e.g. mycompany.com"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__use_website_helpdesk_slides
msgid "eLearning"
msgstr "למידה אלקטרונית"

#. module: helpdesk
#: model_terms:digest.tip,tip_description:helpdesk.digest_tip_helpdesk_0
msgid "generate tickets in your pipeline."
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.ticket_creation
msgid "has been created from ticket:"
msgstr "נוצר מקריאת שירות:"

#. module: helpdesk
#: model:ir.actions.server,name:helpdesk.helpdesk_ratings_server_action
msgid "helpdesk view rating"
msgstr " צפה בדירוג תמיכה טכנית "

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "team search"
msgstr "חיפוש צוות"

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/models/helpdesk_ticket.py:0
msgid "tickets"
msgstr "קריאות שירות"

#. module: helpdesk
#: model:mail.template,subject:helpdesk.rating_ticket_request_email_template
msgid ""
"{{ object.company_id.name or object.user_id.company_id.name or 'Helpdesk' "
"}}: Service Rating Request"
msgstr ""

#. module: helpdesk
#: model:mail.template,subject:helpdesk.new_ticket_request_email_template
msgid "{{ object.name }}"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.team_rating_data
msgid "{{rating.res_name if t['is_helpdesk_user'] else ''}}"
msgstr ""
