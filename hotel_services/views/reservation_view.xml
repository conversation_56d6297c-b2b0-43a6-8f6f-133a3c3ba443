<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <!-- Inherit Form View to Modify it -->
    <record id="reservation_form_inherit_service" model="ir.ui.view">
        <field name="name">room.reservation</field>
        <field name="model">room.reservation</field>
        <field name="inherit_id" ref="hotel_reservation.view_form_room_reservation"/>
        <field name="arch" type="xml">
            <xpath expr="//page[@name='reservation_line']" position="after">
                <page string="Addon Services" name="service">
                    <field name="room_service_line" readonly="state in ['check_out', 'cancel']" context="{'default_check_in': check_in,
                    'default_check_out': check_out, 'default_partner_id': guest_id, 'default_is_create_room_reservation': True}">
                        <list string="Service Line" editable="bottom">
                            <field name="product_id" readonly="state != 'draft'"
                                   domain="[('is_hotel_service', '=', True)]"
                                   options="{'no_create': True}"/>
                            <field name="quantity" readonly="state != 'draft'"/>
                            <field name="price_unit" readonly="state != 'draft'"/>
                            <field name="tax_ids" readonly="state != 'draft'"
                                   widget="many2many_tags" options="{'no_create': True}"/>
                            <field name="tax_amount" sum="total"/>
                            <field name="price_subtotal" sum="total"/>
                            <field name="check_out" column_invisible="1"/>
                            <field name="check_in" column_invisible="1"/>
                            <field name="partner_id" column_invisible="1"/>
                            <field name="state" readonly="1" force_save="1"/>
                            <field name="is_create_room_reservation" column_invisible="1"/>
                        </list>
                    </field>
                </page>
            </xpath>
        </field>
    </record>
</odoo>
