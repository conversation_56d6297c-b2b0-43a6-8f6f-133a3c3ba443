<?xml version="1.0" encoding="utf-8" ?>
<odoo>
    <!--  Tree View  -->
    <record id="view_multi_product_scrap_tree" model="ir.ui.view">
        <field name="name">Multi Product Scrap Tree View</field>
        <field name="model">res.product.scrap</field>
        <field name="arch" type="xml">
            <list>
                <field name="reference_no"/>
                <field name="source_location_id"/>
                <field name="scrap_location_id"/>
                <field name="confirm_date"/>
                <field name="state" widget="badge" decoration-success="state == 'done'"
                       decoration-info="state == 'draft'"/>
            </list>
        </field>
    </record>
    <!-- Form View  -->
    <record id="view_multi_product_scrap_form" model="ir.ui.view">
        <field name="name">Multi Product Scrap Form View</field>
        <field name="model">res.product.scrap</field>
        <field name="arch" type="xml">
            <form>
                <header>
                    <button type="object" name="action_scrap_approval_request" string="Send For Approval"
                            class="oe_highlight"
                            invisible="state != 'draft'"
                    />
                    <button type="object" name="action_scrap_approved" string="Approve" class="oe_highlight"
                            invisible="state != 'request_to_approval'"
                            groups="sp_multi_product_scrap.group_scrap_approval_user"/>
                    <button type="object" name="action_validate" string="Validate" class="oe_highlight"
                            invisible="state != 'approved'"
                            confirm="Are you sure to validate the scrap transaction"/>
                    <button type="object" name="action_scrap_cancel"
                            string="Cancel" class="oe_highlight"
                            invisible="state in ('done', 'cancel')"
                    />
                    <button type="object" name="action_scrap_set_to_draft"
                            invisible="state != 'cancel'"
                            string="Set To Draft" class="oe_highlight"
                    />
                    <field name="state" widget="statusbar" statusbar_visible="draft,done"/>
                </header>
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button class="oe_stat_button" name="action_view_stock_moves"
                                type="object" icon="fa-exchange">
                            <div class="o_stat_info">
                                <span class="o_stat_text">Product Moves</span>
                            </div>
                        </button>
                    </div>
                    <div class="oe_title">
                        <h1>
                            <field name="reference_no" nolabel="1"/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="source_location_id" required="1"
                                   domain="[('usage', '=', 'internal'), ('company_id', '=', company_id)]"
                                   options="{'no_create': True, 'no_open': True}"
                                   readonly="state == 'done'"
                            />
                            <field name="source_document" required="1"
                                   readonly="state == 'done'"
                            />
                            <field name="confirm_date"
                                   readonly="state == 'done'"
                                   invisible="state == 'draft'"
                            />
                        </group>
                        <group>
                            <field name="scrap_location_id" required="1"
                                   domain="[('usage', '=', 'inventory'), ('scrap_location', '=', True) ,('company_id', '=', company_id)]"
                                   options="{'no_create': True, 'no_open': True}"
                                   readonly="state == 'done'"
                            />
                            <field name="company_id" options="{'no_create': True}"
                                   readonly="1"/>
                            <field name="accounting_date"
                                   readonly="state == 'done'"
                            />
                        </group>
                    </group>
                    <notebook>
                        <page string="Product Information">
                            <field name="multi_scrap_ids" context="{'default_location_id': source_location_id}"
                                   readonly="state == 'done'">
                                <list editable="bottom" decoration-danger="onhand_qty &lt; 0.0">
                                    <field name="product_id"
                                           context="{'default_detailed_type':'product'}"
                                           domain="[('type', 'in', ['product', 'consu'])]" required="1"/>
                                    <field name="location_id" invisible="0" readonly="1" force_save="1"/>
                                    <field name="lot_id" options="{'no_create': True}" column_invisible="1"/>
                                    <field name="scrap_reason_tag_ids" widget="many2many_tags"
                                           options="{'no_create_edit': True}"/>
                                    <field name="prd_cost" readonly="1" force_save="1"/>
                                    <field name="onhand_qty" readonly="1" force_save="1"/>
                                    <field name="scrap_qty" required="1"/>
                                    <field name="total_cost" readonly="1" force_save="1" sum="Total" widget="monetary"/>
                                    <field name="total_qty" readonly="1" force_save="1" sum="Total" widget="monetary"/>
                                    <field name="state" column_invisible="1"/>
                                    <field name="product_uom_category_id" column_invisible="1"/>
                                    <field name="name" column_invisible="1"/>
                                    <field name="product_uom_id" column_invisible="1"/>
                                    <field name="lot_id" column_invisible="1"/>
                                    <field name="package_id" column_invisible="1"/>
                                    <field name="owner_id" column_invisible="1"/>
                                    <field name="picking_id" column_invisible="1"/>
                                    <field name="date_done" column_invisible="1"/>
                                    <field name="company_id" column_invisible="1"/>
                                    <field name="scrap_location_id" column_invisible="1"/>
                                </list>
                            </field>
                        </page>
                    </notebook>
                </sheet>
                <chatter/>
            </form>
        </field>
    </record>

    <!--  Action  -->
    <record id="action_multi_product_scrap" model="ir.actions.act_window">
        <field name="name">Multi Product Scrap</field>
        <field name="res_model">res.product.scrap</field>
        <field name="view_mode">list,form</field>
        <field name="view_id" ref="view_multi_product_scrap_tree"/>
        <field name="target">current</field>
    </record>
    <!-- Menu -->
    <menuitem id="menu_multi_product_scrap"
              name="Multi Product Scrap"
              action="sp_multi_product_scrap.action_multi_product_scrap"
              parent="stock.menu_stock_warehouse_mgmt"
              sequence="19"/>

    <!--  Inherit stock Scrap View  -->
    <record id="view_inherited_stock_scrap_view" model="ir.ui.view">
        <field name="name">Inherited Stock Scrap Form View</field>
        <field name="model">stock.scrap</field>
        <field name="inherit_id" ref="stock.stock_scrap_form_view"/>
        <field name="arch" type="xml">
            <field name="company_id" position="after">
                <field name="multi_product_scrap_id" invisible="1"/>
            </field>
        </field>
    </record>
</odoo>
