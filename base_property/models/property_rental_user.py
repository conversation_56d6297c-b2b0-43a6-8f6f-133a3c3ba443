# Softprime Consulting Pvt Ltd
# Copyright (C) Softprime Consulting Pvt Ltd
# All Rights Reserved
# https://softprimeconsulting.com/
from odoo import _, api, fields, models
from odoo.exceptions import UserError


class User(models.Model):
    _inherit = "res.users"

    discount_mode = fields.Selection([("amount", "Amount"), ("percentage", "Percentage")], string="Mode of Discount", default="amount")
    discount_percentage = fields.Float(string="Discount Percentage", store=True)
    discount_amount = fields.Float(string="Discount Amount", store=True)
    property_ids = fields.One2many("res.building", "user_id", string="Properties")

    @api.constrains("discount_percentage", "discount_mode")
    def verify_discount_percentage(self):
        """Validate discount percentage for tenants."""
        for user in self:
            if user.has_group(
                    'base.group_user') and user.discount_mode == "percentage" and user.discount_percentage <= 0.0:
                raise UserError(_("Discount percentage should be greater than 0!"))

    @api.constrains("discount_amount", "discount_mode")
    def verify_discount_amount(self):
        """Validate discount amount for tenants."""
        for user in self:
            if user.has_group('base.group_user') and user.discount_mode == "amount" and user.discount_amount <= 0.0:
                raise UserError(_("Discount amount should be greater than 0!"))
