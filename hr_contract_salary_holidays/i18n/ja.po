# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_contract_salary_holidays
# 
# Translators:
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:27+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Japanese (https://app.transifex.com/odoo/teams/41243/ja/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ja\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: hr_contract_salary_holidays
#: model_terms:ir.ui.view,arch_db:hr_contract_salary_holidays.hr_contract_view_form
msgid "<span>Allocations</span>"
msgstr "<span>割当</span>"

#. module: hr_contract_salary_holidays
#: model:ir.model.constraint,message:hr_contract_salary_holidays.constraint_res_company_auto_allocation
msgid ""
"A Time Off Type is required once the Extra Time Off automatic allocation is "
"set."
msgstr "追加休暇の自動割当が設定されると、休暇タイプが必要になります。"

#. module: hr_contract_salary_holidays
#: model:ir.model.fields,field_description:hr_contract_salary_holidays.field_hr_contract__leave_allocation_id
msgid "Allocation"
msgstr "割当"

#. module: hr_contract_salary_holidays
#. odoo-python
#: code:addons/hr_contract_salary_holidays/controllers/main.py:0
msgid "Allocation automatically created from Contract Signature."
msgstr "契約書署名から割当が自動的に作成されました。"

#. module: hr_contract_salary_holidays
#: model:ir.model,name:hr_contract_salary_holidays.model_res_company
msgid "Companies"
msgstr "会社"

#. module: hr_contract_salary_holidays
#: model:ir.model,name:hr_contract_salary_holidays.model_res_config_settings
msgid "Config Settings"
msgstr "コンフィグ設定"

#. module: hr_contract_salary_holidays
#. odoo-python
#: code:addons/hr_contract_salary_holidays/models/hr_contract.py:0
msgid "Contract has been cancelled."
msgstr "契約書が取消されました。"

#. module: hr_contract_salary_holidays
#: model:ir.model,name:hr_contract_salary_holidays.model_hr_contract
msgid "Employee Contract"
msgstr "従業員契約"

#. module: hr_contract_salary_holidays
#: model:hr.leave.type,name:hr_contract_salary_holidays.holiday_status_eto
msgid "Extra Time Off"
msgstr "追加休暇"

#. module: hr_contract_salary_holidays
#: model_terms:ir.ui.view,arch_db:hr_contract_salary_holidays.res_config_settings_view_form
msgid "Extra Time Off Allocation"
msgstr "追加休暇割当"

#. module: hr_contract_salary_holidays
#: model:ir.model.fields,field_description:hr_contract_salary_holidays.field_res_company__hr_contract_timeoff_auto_allocation
#: model:ir.model.fields,field_description:hr_contract_salary_holidays.field_res_config_settings__hr_contract_timeoff_auto_allocation
msgid "Extra Time Off Allocation on contract signature"
msgstr "契約署名時の追加休暇割当"

#. module: hr_contract_salary_holidays
#: model_terms:ir.ui.view,arch_db:hr_contract_salary_holidays.res_config_settings_view_form
msgid ""
"If the employee requested extra time off in his salary configurator, create "
"automatically the allocation request"
msgstr "従業員が給与コンフィギュレータで追加休暇を要求した場合、自動的に割り当て要求を作成します。"

#. module: hr_contract_salary_holidays
#: model_terms:ir.ui.view,arch_db:hr_contract_salary_holidays.res_config_settings_view_form
msgid ""
"If the employee requested extra time off in his salary configurator, create "
"automatically the allocation request."
msgstr "従業員が給与コンフィギュレータで追加休暇を要求した場合、自動的に割り当て要求を作成します。"

#. module: hr_contract_salary_holidays
#: model:ir.model.fields,field_description:hr_contract_salary_holidays.field_res_company__hr_contract_timeoff_auto_allocation_type_id
#: model:ir.model.fields,field_description:hr_contract_salary_holidays.field_res_config_settings__hr_contract_timeoff_auto_allocation_type_id
msgid "Time Off Type"
msgstr "休暇タイプ"
