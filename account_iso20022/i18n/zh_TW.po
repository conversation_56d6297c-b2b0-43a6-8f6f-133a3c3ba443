# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_iso20022
# 
# Translators:
# Wil <PERSON>, 2024
# <PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-01-27 13:55+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON>, 2025\n"
"Language-Team: Chinese (Taiwan) (https://app.transifex.com/odoo/teams/41243/zh_TW/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_TW\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: account_iso20022
#. odoo-python
#: code:addons/account_iso20022/models/account_batch_payment.py:0
msgid "A bank account is not defined."
msgstr "銀行賬戶未設定。"

#. module: account_iso20022
#. odoo-python
#: code:addons/account_iso20022/models/account_payment_register.py:0
msgid "A bank account must be set on the following documents: %s(doc_name)"
msgstr "下列文件必須設定銀行賬戶：%s(doc_name)"

#. module: account_iso20022
#: model:ir.model.fields.selection,name:account_iso20022.selection__account_journal__sepa_pain_version__pain_001_001_03_austrian_004
msgid "Austrian"
msgstr "奧地利"

#. module: account_iso20022
#: model_terms:ir.ui.view,arch_db:account_iso20022.view_batch_payment_form_inherit
msgid "Batch Booking"
msgstr "大批預訂"

#. module: account_iso20022
#: model:ir.model,name:account_iso20022.model_account_batch_payment
msgid "Batch Payment"
msgstr "批量付款"

#. module: account_iso20022
#: model:ir.model.fields,field_description:account_iso20022.field_account_batch_payment__iso20022_charge_bearer
msgid "Charge Bearer"
msgstr "費用承擔者"

#. module: account_iso20022
#: model:ir.model,name:account_iso20022.model_res_company
msgid "Companies"
msgstr "公司"

#. module: account_iso20022
#: model:ir.model,name:account_iso20022.model_res_config_settings
msgid "Config Settings"
msgstr "設定"

#. module: account_iso20022
#. odoo-python
#: code:addons/account_iso20022/models/account_batch_payment.py:0
msgid "Configure Journal"
msgstr "配置日記賬"

#. module: account_iso20022
#: model:ir.model,name:account_iso20022.model_res_partner
msgid "Contact"
msgstr "聯絡人"

#. module: account_iso20022
#: model:ir.model.fields.selection,name:account_iso20022.selection__account_batch_payment__iso20022_charge_bearer__cred
msgid "Creditor"
msgstr "收款方"

#. module: account_iso20022
#: model:ir.model.fields.selection,name:account_iso20022.selection__account_batch_payment__iso20022_charge_bearer__debt
msgid "Debtor"
msgstr "付款方"

#. module: account_iso20022
#. odoo-python
#: code:addons/account_iso20022/models/account_journal.py:0
msgid "Employee %s has no country in their address."
msgstr "員工 %s 的地址未設定國家/地區。"

#. module: account_iso20022
#: model:ir.model.fields,help:account_iso20022.field_res_company__iso20022_orgid_issr
msgid ""
"Entity that assigns the identification (eg. KBE-BCO or Finanzamt Muenchen "
"IV)."
msgstr "全部分配認證 (eg. KBE-BCO or Finanzamt Muenchen IV)"

#. module: account_iso20022
#: model:ir.model.fields.selection,name:account_iso20022.selection__account_journal__sepa_pain_version__pain_001_001_03_de
msgid "German"
msgstr "德文"

#. module: account_iso20022
#. odoo-python
#: code:addons/account_iso20022/models/account_batch_payment.py:0
msgid "Go to settings"
msgstr "前往設定"

#. module: account_iso20022
#: model:ir.model.fields,field_description:account_iso20022.field_account_journal__has_sepa_ct_payment_method
msgid "Has Sepa Ct Payment Method"
msgstr "有 SEPA 轉賬付款方法"

#. module: account_iso20022
#: model:account.payment.method,name:account_iso20022.account_payment_method_iso20022
msgid "ISO20022"
msgstr "ISO 20022"

#. module: account_iso20022
#: model:ir.model.fields,field_description:account_iso20022.field_res_company__iso20022_orgid_id
#: model:ir.model.fields,field_description:account_iso20022.field_res_config_settings__iso20022_orgid_id
msgid "Identification"
msgstr "ID"

#. module: account_iso20022
#: model:ir.model.fields,help:account_iso20022.field_res_company__iso20022_orgid_id
#: model:ir.model.fields,help:account_iso20022.field_res_config_settings__iso20022_orgid_id
msgid "Identification assigned by an institution (eg. VAT number)."
msgstr "被一家機構分配的標識例(如.增值稅 號碼)"

#. module: account_iso20022
#: model:ir.model.fields,field_description:account_iso20022.field_res_company__iso20022_orgid_issr
#: model:ir.model.fields,field_description:account_iso20022.field_res_config_settings__iso20022_orgid_issr
msgid "Issuer"
msgstr "發行人"

#. module: account_iso20022
#: model:ir.model,name:account_iso20022.model_account_journal
msgid "Journal"
msgstr "日記賬"

#. module: account_iso20022
#: model:ir.model.fields,field_description:account_iso20022.field_res_company__iso20022_lei
#: model:ir.model.fields,field_description:account_iso20022.field_res_partner__iso20022_lei
#: model:ir.model.fields,field_description:account_iso20022.field_res_users__iso20022_lei
msgid "LEI"
msgstr "LEI"

#. module: account_iso20022
#: model:ir.model.fields,help:account_iso20022.field_res_company__iso20022_lei
#: model:ir.model.fields,help:account_iso20022.field_res_partner__iso20022_lei
#: model:ir.model.fields,help:account_iso20022.field_res_users__iso20022_lei
msgid "Legal Entity Identifier"
msgstr "法律實體識別碼"

#. module: account_iso20022
#: model_terms:ir.ui.view,arch_db:account_iso20022.res_partner_sepa_inherit_form
msgid "Miscellaneous"
msgstr "雜項"

#. module: account_iso20022
#: model:ir.model.fields,help:account_iso20022.field_res_config_settings__iso20022_initiating_party_name
msgid ""
"Name of the Creditor Reference Party. Usage Rule: Limited to 70 characters "
"in length."
msgstr "債權人參考方姓名。使用規則：長度限制在70字以內。"

#. module: account_iso20022
#. odoo-python
#: code:addons/account_iso20022/models/account_batch_payment.py:0
msgid "Non-EUR Payments"
msgstr "非歐元付款"

#. module: account_iso20022
#. odoo-python
#: code:addons/account_iso20022/models/account_journal.py:0
msgid "Partner %s has no country code defined."
msgstr "合作夥伴 %s 未設定國家/地區代碼。"

#. module: account_iso20022
#. odoo-python
#: code:addons/account_iso20022/models/account_journal.py:0
msgid "Partner %s has not bank account defined."
msgstr "合作夥伴 %s 未設定銀行賬戶。"

#. module: account_iso20022
#: model:ir.model,name:account_iso20022.model_account_payment_register
msgid "Pay"
msgstr "付款"

#. module: account_iso20022
#: model:ir.model,name:account_iso20022.model_account_payment_method
msgid "Payment Methods"
msgstr "付款方法"

#. module: account_iso20022
#: model_terms:ir.ui.view,arch_db:account_iso20022.account_journal_dashboard_kanban_view_inherited
msgid "Payment to send via SEPA"
msgstr "通過SEPA發送付款"

#. module: account_iso20022
#: model:ir.model,name:account_iso20022.model_account_payment
msgid "Payments"
msgstr "收付款"

#. module: account_iso20022
#: model_terms:ir.ui.view,arch_db:account_iso20022.account_journal_dashboard_kanban_view_inherited
msgid "Payments to send via SEPA"
msgstr "通過SEPA發送付款"

#. module: account_iso20022
#. odoo-python
#: code:addons/account_iso20022/models/account_batch_payment.py:0
msgid "Payments without IBAN"
msgstr "無 IBAN 的付款"

#. module: account_iso20022
#: model:ir.model.fields,help:account_iso20022.field_account_batch_payment__iso20022_batch_booking
msgid "Request batch booking from the bank for the related bank statements."
msgstr "對相關銀行結單，要求銀行作大批記賬。"

#. module: account_iso20022
#: model:ir.model.fields,field_description:account_iso20022.field_account_batch_payment__iso20022_batch_booking
msgid "SCT Batch Booking"
msgstr "SEPA 轉賬付款大批記賬"

#. module: account_iso20022
#: model:account.payment.method,name:account_iso20022.account_payment_method_sepa_ct
#: model_terms:ir.ui.view,arch_db:account_iso20022.view_account_journal_form
msgid "SEPA Credit Transfer"
msgstr "SEPA 信用轉帳"

#. module: account_iso20022
#. odoo-python
#: code:addons/account_iso20022/models/account_journal_dashboard.py:0
msgid "SEPA Credit Transfers to Send"
msgstr "SEPA 信用轉帳發送"

#. module: account_iso20022
#. odoo-python
#: code:addons/account_iso20022/models/account_payment.py:0
msgid "SEPA only accepts payments in EUR."
msgstr "SEPA 只接受歐元（EUR）付款。"

#. module: account_iso20022
#. odoo-python
#: code:addons/account_iso20022/models/account_batch_payment.py:0
msgid ""
"SEPA only accepts payments in EUR. Some payments are using another currency."
msgstr "SEPA 只接受歐元（EUR）付款。部份付款使用了其他貨幣。"

#. module: account_iso20022
#: model:ir.model.fields,help:account_iso20022.field_account_journal__sepa_pain_version
#: model:ir.model.fields,help:account_iso20022.field_account_payment__sepa_pain_version
msgid ""
"SEPA version to use to generate Credit Transfer XML files from this journal"
msgstr "根據此日記賬資料產生轉賬付款 XML 檔案時所使用的 SEPA 版本"

#. module: account_iso20022
#: model:ir.model.fields.selection,name:account_iso20022.selection__account_batch_payment__iso20022_charge_bearer__slev
msgid "Service Level"
msgstr "服務水平"

#. module: account_iso20022
#: model:ir.model.fields.selection,name:account_iso20022.selection__account_batch_payment__iso20022_charge_bearer__shar
msgid "Shared"
msgstr "分享"

#. module: account_iso20022
#. odoo-python
#: code:addons/account_iso20022/models/account_batch_payment.py:0
msgid "Some payments are above the maximum amount allowed."
msgstr "部份付款超出允許的最高金額。"

#. module: account_iso20022
#. odoo-python
#: code:addons/account_iso20022/models/account_journal_sepa_ct.py:0
msgid ""
"Some payments are missing a value for 'UETR', required for the SEPA "
"Pain.001.001.09 format."
msgstr "部份付款缺漏 UETR 值，該值是 SEPA Pain.001.001.09 格式所要求的。"

#. module: account_iso20022
#. odoo-python
#: code:addons/account_iso20022/models/account_batch_payment.py:0
msgid "Some payments have no recipient bank account set."
msgstr "部份付款未有設定收款人銀行賬戶。"

#. module: account_iso20022
#: model:ir.model.fields,help:account_iso20022.field_account_batch_payment__iso20022_charge_bearer
msgid ""
"Specifies which party/parties will bear the charges associated with the "
"processing of the payment transaction."
msgstr "指定由哪一方承擔與處理付款交易相關的費用。"

#. module: account_iso20022
#: model:account.payment.method,name:account_iso20022.account_payment_method_iso20022_se
msgid "Swedish ISO20022"
msgstr "瑞典 ISO 20022"

#. module: account_iso20022
#: model:account.payment.method,name:account_iso20022.account_payment_method_iso20022_ch
msgid "Swiss ISO20022"
msgstr "瑞士 ISO 20022"

#. module: account_iso20022
#. odoo-python
#: code:addons/account_iso20022/models/account_journal.py:0
msgid ""
"The BIC code '%(bic_code)s' associated to the bank '%(bank)s' of bank account '%(account)s' of partner '%(partner)s' does not respect the required convention.\n"
"It must contain 8 or 11 characters and match the following structure:\n"
"- 4 letters: institution code or bank code\n"
"- 2 letters: country code\n"
"- 2 letters or digits: location code\n"
"- 3 letters or digits: branch code, optional\n"
msgstr ""
"BIC 代碼「%(bic_code)s」（已連結銀行及銀行賬戶：%(bank)s，%(account)s；合作夥伴為 %(partner)s）不符合格式要求。\n"
"只可有 8 個或 11 個字符，依序符合以下結構：\n"
"- 4 個字母：機構代碼或銀行代碼\n"
"- 2 個字母：國家/地區代碼\n"
"- 2 個字母或數字：位置代碼\n"
"- 3 個字母或數字：銀行分行代碼（可選填）\n"

#. module: account_iso20022
#. odoo-python
#: code:addons/account_iso20022/models/res_partner.py:0
msgid ""
"The LEI number must contain 20 characters and match the following structure:\n"
"- 18 alphanumeric characters with capital letters\n"
"- 2 digits in the end\n"
msgstr ""
"LEI 碼必須包含 20 個字符, 並符合以下結構：\n"
"- 18個大寫字母數字字符\n"
"- 末尾2個數字\n"

#. module: account_iso20022
#. odoo-python
#: code:addons/account_iso20022/models/account_batch_payment.py:0
msgid ""
"The Name Identification and Issuer details are required to proceed. Please "
"configure them in the settings."
msgstr "需要有名稱識別符及發行人詳細資料，才可繼續。請在設定中配置這些資料。"

#. module: account_iso20022
#. odoo-python
#: code:addons/account_iso20022/models/account_batch_payment.py:0
msgid ""
"The bank account %(account)s, of journal '%(journal)s', is not of type IBAN.\n"
"A valid IBAN account is required to use SEPA features."
msgstr ""
"銀行賬戶 %(account)s（所屬日記賬 %(journal)s）並非 IBAN 類型。使用 SEPA 功能需要有效的 IBAN 賬戶。"

#. module: account_iso20022
#. odoo-python
#: code:addons/account_iso20022/models/account_batch_payment.py:0
msgid ""
"The customer bank account set on some payments does not have an IBAN number."
" This is required for SEPA."
msgstr "部份付款所設定的客戶銀行賬戶，沒有 IBAN 號碼。進行 SEPA 交易必須要有此項資料。"

#. module: account_iso20022
#. odoo-python
#: code:addons/account_iso20022/models/account_payment.py:0
msgid ""
"The journal '%s' requires a proper IBAN account to pay via SEPA. Please "
"configure it first."
msgstr "日記帳 '%s' 需要一個正確的IBAN 帳號來通過SEPA付費。請先設定它。"

#. module: account_iso20022
#. odoo-python
#: code:addons/account_iso20022/models/account_journal.py:0
msgid "This journal does not have a bank account defined."
msgstr "此日記賬未有定義銀行賬戶。"

#. module: account_iso20022
#. odoo-python
#: code:addons/account_iso20022/models/account_journal.py:0
msgid "Too many transactions for a single file."
msgstr "在單個文件裡太多的交易了。"

#. module: account_iso20022
#: model:ir.model.fields,field_description:account_iso20022.field_account_payment__iso20022_uetr
msgid "UETR"
msgstr "UETR（獨特端對端交易參考）"

#. module: account_iso20022
#: model:ir.model.fields,help:account_iso20022.field_account_payment__iso20022_uetr
msgid "Unique end-to-end transaction reference"
msgstr "端對端獨特交易參考"

#. module: account_iso20022
#. odoo-python
#: code:addons/account_iso20022/models/account_batch_payment.py:0
msgid "View Payments"
msgstr "查看付款"

#. module: account_iso20022
#: model:ir.model.fields,help:account_iso20022.field_res_company__iso20022_initiating_party_name
msgid ""
"Will appear as the name of the party initiating the payment. Limited to 70 "
"characters."
msgstr "作為付款發起方的顯示名稱。上限 70 個字元。"

#. module: account_iso20022
#: model:ir.model.fields,help:account_iso20022.field_res_config_settings__iso20022_orgid_issr
msgid ""
"Will appear in SEPA payments as the name of the party initiating the "
"payment. Limited to 70 characters."
msgstr "出現在SEPA支付裡作為支付發起方的名稱。限70個字元。"

#. module: account_iso20022
#: model:ir.model.fields,field_description:account_iso20022.field_account_journal__sepa_pain_version
#: model:ir.model.fields,field_description:account_iso20022.field_account_payment__sepa_pain_version
msgid "XML Format"
msgstr "XML 格式"

#. module: account_iso20022
#: model:ir.model.fields,field_description:account_iso20022.field_res_company__iso20022_initiating_party_name
#: model:ir.model.fields,field_description:account_iso20022.field_res_config_settings__iso20022_initiating_party_name
msgid "Your Company Name"
msgstr "公司名稱"

#. module: account_iso20022
#: model:ir.model.fields.selection,name:account_iso20022.selection__account_journal__sepa_pain_version__pain_001_001_03
msgid "pain.001.001.03"
msgstr "pain.001.001.03"

#. module: account_iso20022
#: model:ir.model.fields.selection,name:account_iso20022.selection__account_journal__sepa_pain_version__pain_001_001_09
msgid "pain.001.001.09"
msgstr "pain.001.001.09"
