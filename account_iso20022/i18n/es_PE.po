# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * account_sepa
#
# Translators:
# <PERSON> <<EMAIL>>, 2017
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 11.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-05-16 16:02+0000\n"
"PO-Revision-Date: 2017-10-24 09:23+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2017\n"
"Language-Team: Spanish (Peru) (https://www.transifex.com/odoo/teams/41243/es_PE/)\n"
"Language: es_PE\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: account_sepa
#. odoo-python
#: code:addons/account_sepa/models/account_batch_payment.py:0
msgid "A bank account is not defined."
msgstr ""

#. module: account_sepa
#. odoo-python
#: code:addons/account_sepa/models/account_payment_register.py:0
msgid "A bank account must be set on the following documents: "
msgstr ""

#. module: account_sepa
#: model:ir.model.fields.selection,name:account_sepa.selection__account_journal__sepa_pain_version__pain_001_001_03_austrian_004
msgid "Austrian"
msgstr ""

#. module: account_sepa
#. odoo-python
#: code:addons/account_sepa/models/account_journal.py:0
msgid "Bank account %s 's bank does not have any BIC number associated. Please define one."
msgstr ""

#. module: account_sepa
#: model_terms:ir.ui.view,arch_db:account_sepa.view_batch_payment_form_inherit
msgid "Batch Booking"
msgstr ""

#. module: account_sepa
#: model:ir.model,name:account_sepa.model_account_batch_payment
msgid "Batch Payment"
msgstr ""

#. module: account_sepa
#: model:ir.model,name:account_sepa.model_res_company
msgid "Companies"
msgstr "Compañias"

#. module: account_sepa
#: model:ir.model,name:account_sepa.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: account_sepa
#: model:ir.model.fields,help:account_sepa.field_res_company__sepa_orgid_issr
msgid "Entity that assigns the identification (eg. KBE-BCO or Finanzamt Muenchen IV)."
msgstr ""

#. module: account_sepa
#: model:ir.model.fields.selection,name:account_sepa.selection__account_journal__sepa_pain_version__pain_001_001_03
msgid "Generic"
msgstr ""

#. module: account_sepa
#: model:ir.model.fields.selection,name:account_sepa.selection__account_journal__sepa_pain_version__pain_001_001_03_de
msgid "German"
msgstr ""

#. module: account_sepa
#: model:ir.model.fields,field_description:account_sepa.field_account_journal__has_sepa_ct_payment_method
msgid "Has Sepa Ct Payment Method"
msgstr ""

#. module: account_sepa
#: model:ir.model.fields,field_description:account_sepa.field_res_company__sepa_orgid_id
#: model:ir.model.fields,field_description:account_sepa.field_res_config_settings__sepa_orgid_id
msgid "Identification"
msgstr ""

#. module: account_sepa
#: model:ir.model.fields,help:account_sepa.field_res_company__sepa_orgid_id
#: model:ir.model.fields,help:account_sepa.field_res_config_settings__sepa_orgid_id
msgid "Identification assigned by an institution (eg. VAT number)."
msgstr ""

#. module: account_sepa
#: model:ir.model.fields,field_description:account_sepa.field_res_company__sepa_orgid_issr
#: model:ir.model.fields,field_description:account_sepa.field_res_config_settings__sepa_orgid_issr
msgid "Issuer"
msgstr ""

#. module: account_sepa
#: model:ir.model,name:account_sepa.model_account_journal
msgid "Journal"
msgstr ""

#. module: account_sepa
#. odoo-python
#: code:addons/account_sepa/models/account_batch_payment.py:0
msgid "Maximum amount is %s for payments in Euros, %s for other currencies."
msgstr ""

#. module: account_sepa
#: model:ir.model.fields,help:account_sepa.field_res_config_settings__sepa_initiating_party_name
msgid "Name of the Creditor Reference Party. Usage Rule: Limited to 70 characters in length."
msgstr ""

#. module: account_sepa
#. odoo-python
#: code:addons/account_sepa/models/account_journal.py:0
msgid "Partner %s has no country code defined."
msgstr ""

#. module: account_sepa
#. odoo-python
#: code:addons/account_sepa/models/account_journal.py:0
msgid "Partner %s has not bank account defined."
msgstr ""

#. module: account_sepa
#: model:ir.model,name:account_sepa.model_account_payment_method
msgid "Payment Methods"
msgstr ""

#. module: account_sepa
#: model_terms:ir.ui.view,arch_db:account_sepa.account_journal_dashboard_kanban_view_inherited
msgid "Payment to send via SEPA"
msgstr ""

#. module: account_sepa
#: model:ir.model,name:account_sepa.model_account_payment
msgid "Payments"
msgstr ""

#. module: account_sepa
#: model_terms:ir.ui.view,arch_db:account_sepa.account_journal_dashboard_kanban_view_inherited
msgid "Payments to send via SEPA"
msgstr ""

#. module: account_sepa
#. odoo-python
#: code:addons/account_sepa/models/account_journal.py:0
msgid "Please first set a SEPA identification number in the accounting settings."
msgstr ""

#. module: account_sepa
#: model:ir.model,name:account_sepa.model_account_payment_register
msgid "Register Payment"
msgstr ""

#. module: account_sepa
#: model:ir.model.fields,help:account_sepa.field_account_batch_payment__sct_batch_booking
msgid "Request batch booking from the bank for the related bank statements."
msgstr ""

#. module: account_sepa
#: model:ir.model.fields,field_description:account_sepa.field_account_batch_payment__sct_batch_booking
msgid "SCT Batch Booking"
msgstr ""

#. module: account_sepa
#: model:account.payment.method,name:account_sepa.account_payment_method_sepa_ct
msgid "SEPA Credit Transfer"
msgstr ""

#. module: account_sepa
#. odoo-python
#: code:addons/account_sepa/models/account_journal_dashboard.py:0
msgid "SEPA Credit Transfers to Send"
msgstr ""

#. module: account_sepa
#: model:ir.model.fields,field_description:account_sepa.field_account_journal__sepa_pain_version
#: model_terms:ir.ui.view,arch_db:account_sepa.view_account_journal_form
msgid "SEPA Pain Version"
msgstr ""

#. module: account_sepa
#: model:ir.model.fields,help:account_sepa.field_account_journal__sepa_pain_version
msgid "SEPA may be a generic format, some countries differ from the SEPA recommendations made by the EPC (European Payment Council) and thus the XML created need some tweaking."
msgstr ""

#. module: account_sepa
#: model:ir.model.fields,field_description:account_sepa.field_account_batch_payment__sct_generic
msgid "Sct Generic"
msgstr ""

#. module: account_sepa
#. odoo-python
#: code:addons/account_sepa/models/account_batch_payment.py:0
msgid "Some payments are above the maximum amount allowed."
msgstr ""

#. module: account_sepa
#. odoo-python
#: code:addons/account_sepa/models/account_batch_payment.py:0
msgid "Some payments are not made on an IBAN recipient account. This batch might not be accepted by certain banks because of that."
msgstr ""

#. module: account_sepa
#. odoo-python
#: code:addons/account_sepa/models/account_batch_payment.py:0
msgid "Some payments have no recipient bank account set."
msgstr ""

#. module: account_sepa
#. odoo-python
#: code:addons/account_sepa/models/account_batch_payment.py:0
msgid "Some payments were instructed in another currency than Euro. This batch might not be accepted by certain banks because of that."
msgstr ""

#. module: account_sepa
#: model:ir.model.fields.selection,name:account_sepa.selection__account_journal__sepa_pain_version__pain_001_001_03_se
msgid "Swedish"
msgstr ""

#. module: account_sepa
#: model:ir.model.fields.selection,name:account_sepa.selection__account_journal__sepa_pain_version__pain_001_001_03_ch_02
msgid "Swiss"
msgstr ""

#. module: account_sepa
#: model:ir.model.fields,help:account_sepa.field_account_batch_payment__sct_generic
msgid "Technical feature used during the file creation. A SEPA message is said to be 'generic' if it cannot be considered as a standard european credit transfer. That is if the bank journal is not in €, a transaction is not in € or a payee is not identified by an IBAN account number."
msgstr ""

#. module: account_sepa
#. odoo-python
#: code:addons/account_sepa/models/account_journal.py:0
msgid ""
"The BIC code '%s' associated to the bank '%s' of bank account '%s' of partner '%s' does not respect the required convention.\n"
"It must contain 8 or 11 characters and match the following structure:\n"
"- 4 letters: institution code or bank code\n"
"- 2 letters: country code\n"
"- 2 letters or digits: location code\n"
"- 3 letters or digits: branch code, optional\n"
msgstr ""

#. module: account_sepa
#. odoo-python
#: code:addons/account_sepa/models/account_journal.py:0
msgid ""
"The account %s, linked to partner '%s', is not of type IBAN.\n"
"A valid IBAN account is required to use SEPA features."
msgstr ""

#. module: account_sepa
#. odoo-python
#: code:addons/account_sepa/models/account_batch_payment.py:0
msgid ""
"The account %s, of journal '%s', is not of type IBAN.\n"
"A valid IBAN account is required to use SEPA features."
msgstr ""

#. module: account_sepa
#. odoo-python
#: code:addons/account_sepa/models/account_journal.py:0
msgid "The amount of the payment '%(payment)s' is too high. The maximum permitted is %(limit)s."
msgstr ""

#. module: account_sepa
#. odoo-python
#: code:addons/account_sepa/models/account_journal.py:0
msgid "The bank defined on account %s (from partner %s) has no BIC. Please first set one."
msgstr ""

#. module: account_sepa
#. odoo-python
#: code:addons/account_sepa/models/account_payment.py:0
msgid "The journal '%s' requires a proper IBAN account to pay via SEPA. Please configure it first."
msgstr ""

#. module: account_sepa
#. odoo-python
#: code:addons/account_sepa/models/account_journal.py:0
msgid "Too many transactions for a single file."
msgstr ""

#. module: account_sepa
#: model:ir.model.fields,help:account_sepa.field_res_company__sepa_initiating_party_name
#: model:ir.model.fields,help:account_sepa.field_res_config_settings__sepa_orgid_issr
msgid "Will appear in SEPA payments as the name of the party initiating the payment. Limited to 70 characters."
msgstr ""

#. module: account_sepa
#: model:ir.model.fields,field_description:account_sepa.field_res_company__sepa_initiating_party_name
#: model:ir.model.fields,field_description:account_sepa.field_res_config_settings__sepa_initiating_party_name
msgid "Your Company Name"
msgstr ""
