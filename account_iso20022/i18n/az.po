# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_iso20022
# 
# Translators:
# <AUTHOR> <EMAIL>, 2024
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-01-27 13:55+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Azerbaijani (https://app.transifex.com/odoo/teams/41243/az/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: az\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: account_iso20022
#. odoo-python
#: code:addons/account_iso20022/models/account_batch_payment.py:0
msgid "A bank account is not defined."
msgstr ""

#. module: account_iso20022
#. odoo-python
#: code:addons/account_iso20022/models/account_payment_register.py:0
msgid "A bank account must be set on the following documents: %s(doc_name)"
msgstr ""

#. module: account_iso20022
#: model:ir.model.fields.selection,name:account_iso20022.selection__account_journal__sepa_pain_version__pain_001_001_03_austrian_004
msgid "Austrian"
msgstr ""

#. module: account_iso20022
#: model_terms:ir.ui.view,arch_db:account_iso20022.view_batch_payment_form_inherit
msgid "Batch Booking"
msgstr ""

#. module: account_iso20022
#: model:ir.model,name:account_iso20022.model_account_batch_payment
msgid "Batch Payment"
msgstr "Məlumat Paketi Ödənişi "

#. module: account_iso20022
#: model:ir.model.fields,field_description:account_iso20022.field_account_batch_payment__iso20022_charge_bearer
msgid "Charge Bearer"
msgstr ""

#. module: account_iso20022
#: model:ir.model,name:account_iso20022.model_res_company
msgid "Companies"
msgstr "Şirkətlər"

#. module: account_iso20022
#: model:ir.model,name:account_iso20022.model_res_config_settings
msgid "Config Settings"
msgstr "Parametrləri Konfiqurasiya edin"

#. module: account_iso20022
#. odoo-python
#: code:addons/account_iso20022/models/account_batch_payment.py:0
msgid "Configure Journal"
msgstr ""

#. module: account_iso20022
#: model:ir.model,name:account_iso20022.model_res_partner
msgid "Contact"
msgstr "Kontakt"

#. module: account_iso20022
#: model:ir.model.fields.selection,name:account_iso20022.selection__account_batch_payment__iso20022_charge_bearer__cred
msgid "Creditor"
msgstr ""

#. module: account_iso20022
#: model:ir.model.fields.selection,name:account_iso20022.selection__account_batch_payment__iso20022_charge_bearer__debt
msgid "Debtor"
msgstr ""

#. module: account_iso20022
#. odoo-python
#: code:addons/account_iso20022/models/account_journal.py:0
msgid "Employee %s has no country in their address."
msgstr ""

#. module: account_iso20022
#: model:ir.model.fields,help:account_iso20022.field_res_company__iso20022_orgid_issr
msgid ""
"Entity that assigns the identification (eg. KBE-BCO or Finanzamt Muenchen "
"IV)."
msgstr ""

#. module: account_iso20022
#: model:ir.model.fields.selection,name:account_iso20022.selection__account_journal__sepa_pain_version__pain_001_001_03_de
msgid "German"
msgstr ""

#. module: account_iso20022
#. odoo-python
#: code:addons/account_iso20022/models/account_batch_payment.py:0
msgid "Go to settings"
msgstr ""

#. module: account_iso20022
#: model:ir.model.fields,field_description:account_iso20022.field_account_journal__has_sepa_ct_payment_method
msgid "Has Sepa Ct Payment Method"
msgstr ""

#. module: account_iso20022
#: model:account.payment.method,name:account_iso20022.account_payment_method_iso20022
msgid "ISO20022"
msgstr ""

#. module: account_iso20022
#: model:ir.model.fields,field_description:account_iso20022.field_res_company__iso20022_orgid_id
#: model:ir.model.fields,field_description:account_iso20022.field_res_config_settings__iso20022_orgid_id
msgid "Identification"
msgstr ""

#. module: account_iso20022
#: model:ir.model.fields,help:account_iso20022.field_res_company__iso20022_orgid_id
#: model:ir.model.fields,help:account_iso20022.field_res_config_settings__iso20022_orgid_id
msgid "Identification assigned by an institution (eg. VAT number)."
msgstr ""

#. module: account_iso20022
#: model:ir.model.fields,field_description:account_iso20022.field_res_company__iso20022_orgid_issr
#: model:ir.model.fields,field_description:account_iso20022.field_res_config_settings__iso20022_orgid_issr
msgid "Issuer"
msgstr ""

#. module: account_iso20022
#: model:ir.model,name:account_iso20022.model_account_journal
msgid "Journal"
msgstr "Jurnal"

#. module: account_iso20022
#: model:ir.model.fields,field_description:account_iso20022.field_res_company__iso20022_lei
#: model:ir.model.fields,field_description:account_iso20022.field_res_partner__iso20022_lei
#: model:ir.model.fields,field_description:account_iso20022.field_res_users__iso20022_lei
msgid "LEI"
msgstr ""

#. module: account_iso20022
#: model:ir.model.fields,help:account_iso20022.field_res_company__iso20022_lei
#: model:ir.model.fields,help:account_iso20022.field_res_partner__iso20022_lei
#: model:ir.model.fields,help:account_iso20022.field_res_users__iso20022_lei
msgid "Legal Entity Identifier"
msgstr ""

#. module: account_iso20022
#: model_terms:ir.ui.view,arch_db:account_iso20022.res_partner_sepa_inherit_form
msgid "Miscellaneous"
msgstr "Müxtəlif"

#. module: account_iso20022
#: model:ir.model.fields,help:account_iso20022.field_res_config_settings__iso20022_initiating_party_name
msgid ""
"Name of the Creditor Reference Party. Usage Rule: Limited to 70 characters "
"in length."
msgstr ""

#. module: account_iso20022
#. odoo-python
#: code:addons/account_iso20022/models/account_batch_payment.py:0
msgid "Non-EUR Payments"
msgstr ""

#. module: account_iso20022
#. odoo-python
#: code:addons/account_iso20022/models/account_journal.py:0
msgid "Partner %s has no country code defined."
msgstr ""

#. module: account_iso20022
#. odoo-python
#: code:addons/account_iso20022/models/account_journal.py:0
msgid "Partner %s has not bank account defined."
msgstr ""

#. module: account_iso20022
#: model:ir.model,name:account_iso20022.model_account_payment_register
msgid "Pay"
msgstr "Ödəyin"

#. module: account_iso20022
#: model:ir.model,name:account_iso20022.model_account_payment_method
msgid "Payment Methods"
msgstr "Ödəniş Üsulları"

#. module: account_iso20022
#: model_terms:ir.ui.view,arch_db:account_iso20022.account_journal_dashboard_kanban_view_inherited
msgid "Payment to send via SEPA"
msgstr ""

#. module: account_iso20022
#: model:ir.model,name:account_iso20022.model_account_payment
msgid "Payments"
msgstr "Ödənişlər"

#. module: account_iso20022
#: model_terms:ir.ui.view,arch_db:account_iso20022.account_journal_dashboard_kanban_view_inherited
msgid "Payments to send via SEPA"
msgstr ""

#. module: account_iso20022
#. odoo-python
#: code:addons/account_iso20022/models/account_batch_payment.py:0
msgid "Payments without IBAN"
msgstr ""

#. module: account_iso20022
#: model:ir.model.fields,help:account_iso20022.field_account_batch_payment__iso20022_batch_booking
msgid "Request batch booking from the bank for the related bank statements."
msgstr ""

#. module: account_iso20022
#: model:ir.model.fields,field_description:account_iso20022.field_account_batch_payment__iso20022_batch_booking
msgid "SCT Batch Booking"
msgstr ""

#. module: account_iso20022
#: model:account.payment.method,name:account_iso20022.account_payment_method_sepa_ct
#: model_terms:ir.ui.view,arch_db:account_iso20022.view_account_journal_form
msgid "SEPA Credit Transfer"
msgstr ""

#. module: account_iso20022
#. odoo-python
#: code:addons/account_iso20022/models/account_journal_dashboard.py:0
msgid "SEPA Credit Transfers to Send"
msgstr ""

#. module: account_iso20022
#. odoo-python
#: code:addons/account_iso20022/models/account_payment.py:0
msgid "SEPA only accepts payments in EUR."
msgstr ""

#. module: account_iso20022
#. odoo-python
#: code:addons/account_iso20022/models/account_batch_payment.py:0
msgid ""
"SEPA only accepts payments in EUR. Some payments are using another currency."
msgstr ""

#. module: account_iso20022
#: model:ir.model.fields,help:account_iso20022.field_account_journal__sepa_pain_version
#: model:ir.model.fields,help:account_iso20022.field_account_payment__sepa_pain_version
msgid ""
"SEPA version to use to generate Credit Transfer XML files from this journal"
msgstr ""

#. module: account_iso20022
#: model:ir.model.fields.selection,name:account_iso20022.selection__account_batch_payment__iso20022_charge_bearer__slev
msgid "Service Level"
msgstr ""

#. module: account_iso20022
#: model:ir.model.fields.selection,name:account_iso20022.selection__account_batch_payment__iso20022_charge_bearer__shar
msgid "Shared"
msgstr "Paylaşılan"

#. module: account_iso20022
#. odoo-python
#: code:addons/account_iso20022/models/account_batch_payment.py:0
msgid "Some payments are above the maximum amount allowed."
msgstr ""

#. module: account_iso20022
#. odoo-python
#: code:addons/account_iso20022/models/account_journal_sepa_ct.py:0
msgid ""
"Some payments are missing a value for 'UETR', required for the SEPA "
"Pain.001.001.09 format."
msgstr ""

#. module: account_iso20022
#. odoo-python
#: code:addons/account_iso20022/models/account_batch_payment.py:0
msgid "Some payments have no recipient bank account set."
msgstr ""

#. module: account_iso20022
#: model:ir.model.fields,help:account_iso20022.field_account_batch_payment__iso20022_charge_bearer
msgid ""
"Specifies which party/parties will bear the charges associated with the "
"processing of the payment transaction."
msgstr ""

#. module: account_iso20022
#: model:account.payment.method,name:account_iso20022.account_payment_method_iso20022_se
msgid "Swedish ISO20022"
msgstr ""

#. module: account_iso20022
#: model:account.payment.method,name:account_iso20022.account_payment_method_iso20022_ch
msgid "Swiss ISO20022"
msgstr ""

#. module: account_iso20022
#. odoo-python
#: code:addons/account_iso20022/models/account_journal.py:0
msgid ""
"The BIC code '%(bic_code)s' associated to the bank '%(bank)s' of bank account '%(account)s' of partner '%(partner)s' does not respect the required convention.\n"
"It must contain 8 or 11 characters and match the following structure:\n"
"- 4 letters: institution code or bank code\n"
"- 2 letters: country code\n"
"- 2 letters or digits: location code\n"
"- 3 letters or digits: branch code, optional\n"
msgstr ""

#. module: account_iso20022
#. odoo-python
#: code:addons/account_iso20022/models/res_partner.py:0
msgid ""
"The LEI number must contain 20 characters and match the following structure:\n"
"- 18 alphanumeric characters with capital letters\n"
"- 2 digits in the end\n"
msgstr ""

#. module: account_iso20022
#. odoo-python
#: code:addons/account_iso20022/models/account_batch_payment.py:0
msgid ""
"The Name Identification and Issuer details are required to proceed. Please "
"configure them in the settings."
msgstr ""

#. module: account_iso20022
#. odoo-python
#: code:addons/account_iso20022/models/account_batch_payment.py:0
msgid ""
"The bank account %(account)s, of journal '%(journal)s', is not of type IBAN.\n"
"A valid IBAN account is required to use SEPA features."
msgstr ""

#. module: account_iso20022
#. odoo-python
#: code:addons/account_iso20022/models/account_batch_payment.py:0
msgid ""
"The customer bank account set on some payments does not have an IBAN number."
" This is required for SEPA."
msgstr ""

#. module: account_iso20022
#. odoo-python
#: code:addons/account_iso20022/models/account_payment.py:0
msgid ""
"The journal '%s' requires a proper IBAN account to pay via SEPA. Please "
"configure it first."
msgstr ""

#. module: account_iso20022
#. odoo-python
#: code:addons/account_iso20022/models/account_journal.py:0
msgid "This journal does not have a bank account defined."
msgstr ""

#. module: account_iso20022
#. odoo-python
#: code:addons/account_iso20022/models/account_journal.py:0
msgid "Too many transactions for a single file."
msgstr ""

#. module: account_iso20022
#: model:ir.model.fields,field_description:account_iso20022.field_account_payment__iso20022_uetr
msgid "UETR"
msgstr ""

#. module: account_iso20022
#: model:ir.model.fields,help:account_iso20022.field_account_payment__iso20022_uetr
msgid "Unique end-to-end transaction reference"
msgstr ""

#. module: account_iso20022
#. odoo-python
#: code:addons/account_iso20022/models/account_batch_payment.py:0
msgid "View Payments"
msgstr ""

#. module: account_iso20022
#: model:ir.model.fields,help:account_iso20022.field_res_company__iso20022_initiating_party_name
msgid ""
"Will appear as the name of the party initiating the payment. Limited to 70 "
"characters."
msgstr ""

#. module: account_iso20022
#: model:ir.model.fields,help:account_iso20022.field_res_config_settings__iso20022_orgid_issr
msgid ""
"Will appear in SEPA payments as the name of the party initiating the "
"payment. Limited to 70 characters."
msgstr ""

#. module: account_iso20022
#: model:ir.model.fields,field_description:account_iso20022.field_account_journal__sepa_pain_version
#: model:ir.model.fields,field_description:account_iso20022.field_account_payment__sepa_pain_version
msgid "XML Format"
msgstr ""

#. module: account_iso20022
#: model:ir.model.fields,field_description:account_iso20022.field_res_company__iso20022_initiating_party_name
#: model:ir.model.fields,field_description:account_iso20022.field_res_config_settings__iso20022_initiating_party_name
msgid "Your Company Name"
msgstr ""

#. module: account_iso20022
#: model:ir.model.fields.selection,name:account_iso20022.selection__account_journal__sepa_pain_version__pain_001_001_03
msgid "pain.001.001.03"
msgstr ""

#. module: account_iso20022
#: model:ir.model.fields.selection,name:account_iso20022.selection__account_journal__sepa_pain_version__pain_001_001_09
msgid "pain.001.001.09"
msgstr ""
