# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_iso20022
# 
# Translators:
# Wil <PERSON>do<PERSON>, 2024
# <PERSON><PERSON>, 2024
# <PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-01-27 13:55+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Sarah Park, 2025\n"
"Language-Team: Korean (https://app.transifex.com/odoo/teams/41243/ko/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ko\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: account_iso20022
#. odoo-python
#: code:addons/account_iso20022/models/account_batch_payment.py:0
msgid "A bank account is not defined."
msgstr "은행 계좌가 정의되지 않았습니다."

#. module: account_iso20022
#. odoo-python
#: code:addons/account_iso20022/models/account_payment_register.py:0
msgid "A bank account must be set on the following documents: %s(doc_name)"
msgstr "다음 문서에 반드시 은행 계좌를 설정해야 합니다: %s(문서명) "

#. module: account_iso20022
#: model:ir.model.fields.selection,name:account_iso20022.selection__account_journal__sepa_pain_version__pain_001_001_03_austrian_004
msgid "Austrian"
msgstr "오스트리아"

#. module: account_iso20022
#: model_terms:ir.ui.view,arch_db:account_iso20022.view_batch_payment_form_inherit
msgid "Batch Booking"
msgstr "일괄 작업 예약"

#. module: account_iso20022
#: model:ir.model,name:account_iso20022.model_account_batch_payment
msgid "Batch Payment"
msgstr "일괄 결제"

#. module: account_iso20022
#: model:ir.model.fields,field_description:account_iso20022.field_account_batch_payment__iso20022_charge_bearer
msgid "Charge Bearer"
msgstr "요금 부담자"

#. module: account_iso20022
#: model:ir.model,name:account_iso20022.model_res_company
msgid "Companies"
msgstr "회사"

#. module: account_iso20022
#: model:ir.model,name:account_iso20022.model_res_config_settings
msgid "Config Settings"
msgstr "설정 구성"

#. module: account_iso20022
#. odoo-python
#: code:addons/account_iso20022/models/account_batch_payment.py:0
msgid "Configure Journal"
msgstr "전표 설정"

#. module: account_iso20022
#: model:ir.model,name:account_iso20022.model_res_partner
msgid "Contact"
msgstr "연락처"

#. module: account_iso20022
#: model:ir.model.fields.selection,name:account_iso20022.selection__account_batch_payment__iso20022_charge_bearer__cred
msgid "Creditor"
msgstr "채권자"

#. module: account_iso20022
#: model:ir.model.fields.selection,name:account_iso20022.selection__account_batch_payment__iso20022_charge_bearer__debt
msgid "Debtor"
msgstr "채무자"

#. module: account_iso20022
#. odoo-python
#: code:addons/account_iso20022/models/account_journal.py:0
msgid "Employee %s has no country in their address."
msgstr "%s 직원 주소에 국가가 없습니다."

#. module: account_iso20022
#: model:ir.model.fields,help:account_iso20022.field_res_company__iso20022_orgid_issr
msgid ""
"Entity that assigns the identification (eg. KBE-BCO or Finanzamt Muenchen "
"IV)."
msgstr "ID를 할당하는 엔티티 (예 : KBE-BCO 또는 Finanzamt Muenchen IV)."

#. module: account_iso20022
#: model:ir.model.fields.selection,name:account_iso20022.selection__account_journal__sepa_pain_version__pain_001_001_03_de
msgid "German"
msgstr "독일"

#. module: account_iso20022
#. odoo-python
#: code:addons/account_iso20022/models/account_batch_payment.py:0
msgid "Go to settings"
msgstr "설정으로 이동"

#. module: account_iso20022
#: model:ir.model.fields,field_description:account_iso20022.field_account_journal__has_sepa_ct_payment_method
msgid "Has Sepa Ct Payment Method"
msgstr "Sepa Ct 결제 방법 사용"

#. module: account_iso20022
#: model:account.payment.method,name:account_iso20022.account_payment_method_iso20022
msgid "ISO20022"
msgstr "ISO20022"

#. module: account_iso20022
#: model:ir.model.fields,field_description:account_iso20022.field_res_company__iso20022_orgid_id
#: model:ir.model.fields,field_description:account_iso20022.field_res_config_settings__iso20022_orgid_id
msgid "Identification"
msgstr "신분증"

#. module: account_iso20022
#: model:ir.model.fields,help:account_iso20022.field_res_company__iso20022_orgid_id
#: model:ir.model.fields,help:account_iso20022.field_res_config_settings__iso20022_orgid_id
msgid "Identification assigned by an institution (eg. VAT number)."
msgstr "기관에서 지정한 식별 번호 (예 : VAT 번호)"

#. module: account_iso20022
#: model:ir.model.fields,field_description:account_iso20022.field_res_company__iso20022_orgid_issr
#: model:ir.model.fields,field_description:account_iso20022.field_res_config_settings__iso20022_orgid_issr
msgid "Issuer"
msgstr "발급자"

#. module: account_iso20022
#: model:ir.model,name:account_iso20022.model_account_journal
msgid "Journal"
msgstr "분개장"

#. module: account_iso20022
#: model:ir.model.fields,field_description:account_iso20022.field_res_company__iso20022_lei
#: model:ir.model.fields,field_description:account_iso20022.field_res_partner__iso20022_lei
#: model:ir.model.fields,field_description:account_iso20022.field_res_users__iso20022_lei
msgid "LEI"
msgstr "LEI"

#. module: account_iso20022
#: model:ir.model.fields,help:account_iso20022.field_res_company__iso20022_lei
#: model:ir.model.fields,help:account_iso20022.field_res_partner__iso20022_lei
#: model:ir.model.fields,help:account_iso20022.field_res_users__iso20022_lei
msgid "Legal Entity Identifier"
msgstr "법인 식별 기호"

#. module: account_iso20022
#: model_terms:ir.ui.view,arch_db:account_iso20022.res_partner_sepa_inherit_form
msgid "Miscellaneous"
msgstr "기타"

#. module: account_iso20022
#: model:ir.model.fields,help:account_iso20022.field_res_config_settings__iso20022_initiating_party_name
msgid ""
"Name of the Creditor Reference Party. Usage Rule: Limited to 70 characters "
"in length."
msgstr "채권자 참조 당사자의 이름 사용 규칙 : 길이는 70 자로 제한됩니다."

#. module: account_iso20022
#. odoo-python
#: code:addons/account_iso20022/models/account_batch_payment.py:0
msgid "Non-EUR Payments"
msgstr "비 유로 결제"

#. module: account_iso20022
#. odoo-python
#: code:addons/account_iso20022/models/account_journal.py:0
msgid "Partner %s has no country code defined."
msgstr "협력사 %s에 부여된 국가 코드가 없습니다."

#. module: account_iso20022
#. odoo-python
#: code:addons/account_iso20022/models/account_journal.py:0
msgid "Partner %s has not bank account defined."
msgstr "%s 협력사의 은행 계좌가 정의되지 않았습니다."

#. module: account_iso20022
#: model:ir.model,name:account_iso20022.model_account_payment_register
msgid "Pay"
msgstr "지불"

#. module: account_iso20022
#: model:ir.model,name:account_iso20022.model_account_payment_method
msgid "Payment Methods"
msgstr "지급 방법"

#. module: account_iso20022
#: model_terms:ir.ui.view,arch_db:account_iso20022.account_journal_dashboard_kanban_view_inherited
msgid "Payment to send via SEPA"
msgstr "SEPA를 통해 송금"

#. module: account_iso20022
#: model:ir.model,name:account_iso20022.model_account_payment
msgid "Payments"
msgstr "결제"

#. module: account_iso20022
#: model_terms:ir.ui.view,arch_db:account_iso20022.account_journal_dashboard_kanban_view_inherited
msgid "Payments to send via SEPA"
msgstr "SEPA를 통해 송금"

#. module: account_iso20022
#. odoo-python
#: code:addons/account_iso20022/models/account_batch_payment.py:0
msgid "Payments without IBAN"
msgstr "IBAN 없이 결제"

#. module: account_iso20022
#: model:ir.model.fields,help:account_iso20022.field_account_batch_payment__iso20022_batch_booking
msgid "Request batch booking from the bank for the related bank statements."
msgstr "관련 은행 명세서에 대해 은행에 일괄 작업 예약을 요청하십시오."

#. module: account_iso20022
#: model:ir.model.fields,field_description:account_iso20022.field_account_batch_payment__iso20022_batch_booking
msgid "SCT Batch Booking"
msgstr "SCT 일괄 예약"

#. module: account_iso20022
#: model:account.payment.method,name:account_iso20022.account_payment_method_sepa_ct
#: model_terms:ir.ui.view,arch_db:account_iso20022.view_account_journal_form
msgid "SEPA Credit Transfer"
msgstr "SEPA 계좌 이체"

#. module: account_iso20022
#. odoo-python
#: code:addons/account_iso20022/models/account_journal_dashboard.py:0
msgid "SEPA Credit Transfers to Send"
msgstr "보낼 SEPA 신용 이체"

#. module: account_iso20022
#. odoo-python
#: code:addons/account_iso20022/models/account_payment.py:0
msgid "SEPA only accepts payments in EUR."
msgstr "SEPA는 EUR로만 결제할 수 있습니다."

#. module: account_iso20022
#. odoo-python
#: code:addons/account_iso20022/models/account_batch_payment.py:0
msgid ""
"SEPA only accepts payments in EUR. Some payments are using another currency."
msgstr "SEPA는 EUR로만 결제할 수 있습니다. 일부 결제의 경우 다른 통화를 사용합니다."

#. module: account_iso20022
#: model:ir.model.fields,help:account_iso20022.field_account_journal__sepa_pain_version
#: model:ir.model.fields,help:account_iso20022.field_account_payment__sepa_pain_version
msgid ""
"SEPA version to use to generate Credit Transfer XML files from this journal"
msgstr "이 전표에서 계좌 이체 XML 파일을 생성할 때 사용할 SEPA 버전"

#. module: account_iso20022
#: model:ir.model.fields.selection,name:account_iso20022.selection__account_batch_payment__iso20022_charge_bearer__slev
msgid "Service Level"
msgstr "서비스 수준"

#. module: account_iso20022
#: model:ir.model.fields.selection,name:account_iso20022.selection__account_batch_payment__iso20022_charge_bearer__shar
msgid "Shared"
msgstr "공유됨"

#. module: account_iso20022
#. odoo-python
#: code:addons/account_iso20022/models/account_batch_payment.py:0
msgid "Some payments are above the maximum amount allowed."
msgstr "일부 결제 항목이 허용된 최대 금액을 초과합니다."

#. module: account_iso20022
#. odoo-python
#: code:addons/account_iso20022/models/account_journal_sepa_ct.py:0
msgid ""
"Some payments are missing a value for 'UETR', required for the SEPA "
"Pain.001.001.09 format."
msgstr "일부 결제에 SEPA Pain.001.001.09 형식에 필요한 'UETR' 값이 누락되었습니다."

#. module: account_iso20022
#. odoo-python
#: code:addons/account_iso20022/models/account_batch_payment.py:0
msgid "Some payments have no recipient bank account set."
msgstr "일부 결제에 수취인 은행 계좌 정보가 설정되어 있지 않습니다."

#. module: account_iso20022
#: model:ir.model.fields,help:account_iso20022.field_account_batch_payment__iso20022_charge_bearer
msgid ""
"Specifies which party/parties will bear the charges associated with the "
"processing of the payment transaction."
msgstr "결제 거래 처리와 관련된 수수료를 부담할 대상을 지정합니다."

#. module: account_iso20022
#: model:account.payment.method,name:account_iso20022.account_payment_method_iso20022_se
msgid "Swedish ISO20022"
msgstr "스웨덴 ISO20022"

#. module: account_iso20022
#: model:account.payment.method,name:account_iso20022.account_payment_method_iso20022_ch
msgid "Swiss ISO20022"
msgstr "스위스 ISO20022"

#. module: account_iso20022
#. odoo-python
#: code:addons/account_iso20022/models/account_journal.py:0
msgid ""
"The BIC code '%(bic_code)s' associated to the bank '%(bank)s' of bank account '%(account)s' of partner '%(partner)s' does not respect the required convention.\n"
"It must contain 8 or 11 characters and match the following structure:\n"
"- 4 letters: institution code or bank code\n"
"- 2 letters: country code\n"
"- 2 letters or digits: location code\n"
"- 3 letters or digits: branch code, optional\n"
msgstr ""
"BIC 코드 '%(bic_code)s'가 필수 규칙을 준수하지 않았습니다. 연결된 거래 은행 '%(bank)s'의 계좌 '%(account)s' 및 협력사 '%(partner)s'를 확인해 주시기 바랍니다.\n"
"다음 내용을 포함하여 8자에서 11자로 구성되어야 합니다:\n"
"- 4글자: 기관 코드 또는 은행 코드\n"
"- 2글자: 국가 코드\n"
"- 2글자 또는 숫자: 지역 코드\n"
"- 2글자 또는 숫자: 지점 코드, 선택사항\n"

#. module: account_iso20022
#. odoo-python
#: code:addons/account_iso20022/models/res_partner.py:0
msgid ""
"The LEI number must contain 20 characters and match the following structure:\n"
"- 18 alphanumeric characters with capital letters\n"
"- 2 digits in the end\n"
msgstr ""
"LEI 번호는 다음과 같이 이루어진 20자로 구성되어야 합니다:\n"
"- 대문자를 포함한 영숫자 18자\n"
"- 마지막 숫자 2자리\n"

#. module: account_iso20022
#. odoo-python
#: code:addons/account_iso20022/models/account_batch_payment.py:0
msgid ""
"The Name Identification and Issuer details are required to proceed. Please "
"configure them in the settings."
msgstr "계속하려면 이름 확인 및 발급자 세부 정보가 필요합니다. 설정에서 환경 설정을 하세요."

#. module: account_iso20022
#. odoo-python
#: code:addons/account_iso20022/models/account_batch_payment.py:0
msgid ""
"The bank account %(account)s, of journal '%(journal)s', is not of type IBAN.\n"
"A valid IBAN account is required to use SEPA features."
msgstr ""
"%(account)s 계정이 '%(journal)s' 분개장에 있지만 IBAN 유형이 아닙니다.\n"
"SEPA 기능을 사용하려면 유효한 IBAN 계정이 필요합니다."

#. module: account_iso20022
#. odoo-python
#: code:addons/account_iso20022/models/account_batch_payment.py:0
msgid ""
"The customer bank account set on some payments does not have an IBAN number."
" This is required for SEPA."
msgstr "일부 결제 중에 설정된 고객 은행 계좌에 IBAN 번호가 없습니다. 이는 SEPA 필수 사항입니다."

#. module: account_iso20022
#. odoo-python
#: code:addons/account_iso20022/models/account_payment.py:0
msgid ""
"The journal '%s' requires a proper IBAN account to pay via SEPA. Please "
"configure it first."
msgstr "'%s' 분개장은 SEPA를 이용해 적절한 IBAN 계정으로 지불해야 합니다. 먼저 구성하십시오."

#. module: account_iso20022
#. odoo-python
#: code:addons/account_iso20022/models/account_journal.py:0
msgid "This journal does not have a bank account defined."
msgstr "이 전표에는 지정된 은행 계좌가 없습니다."

#. module: account_iso20022
#. odoo-python
#: code:addons/account_iso20022/models/account_journal.py:0
msgid "Too many transactions for a single file."
msgstr "단일 파일에 대한 트랜잭션이 너무 많습니다."

#. module: account_iso20022
#: model:ir.model.fields,field_description:account_iso20022.field_account_payment__iso20022_uetr
msgid "UETR"
msgstr "UETR"

#. module: account_iso20022
#: model:ir.model.fields,help:account_iso20022.field_account_payment__iso20022_uetr
msgid "Unique end-to-end transaction reference"
msgstr "고유한 엔드 투 엔드 거래 참조"

#. module: account_iso20022
#. odoo-python
#: code:addons/account_iso20022/models/account_batch_payment.py:0
msgid "View Payments"
msgstr "결제 보기"

#. module: account_iso20022
#: model:ir.model.fields,help:account_iso20022.field_res_company__iso20022_initiating_party_name
msgid ""
"Will appear as the name of the party initiating the payment. Limited to 70 "
"characters."
msgstr "결제를 시작한 당사자의 이름이 표시됩니다. 70자로 제한됩니다."

#. module: account_iso20022
#: model:ir.model.fields,help:account_iso20022.field_res_config_settings__iso20022_orgid_issr
msgid ""
"Will appear in SEPA payments as the name of the party initiating the "
"payment. Limited to 70 characters."
msgstr "지불을 시작한 당사자의 이름으로 SEPA 지불에 표시됩니다. 70 자로 제한됩니다."

#. module: account_iso20022
#: model:ir.model.fields,field_description:account_iso20022.field_account_journal__sepa_pain_version
#: model:ir.model.fields,field_description:account_iso20022.field_account_payment__sepa_pain_version
msgid "XML Format"
msgstr "XML 형식"

#. module: account_iso20022
#: model:ir.model.fields,field_description:account_iso20022.field_res_company__iso20022_initiating_party_name
#: model:ir.model.fields,field_description:account_iso20022.field_res_config_settings__iso20022_initiating_party_name
msgid "Your Company Name"
msgstr "회사 명"

#. module: account_iso20022
#: model:ir.model.fields.selection,name:account_iso20022.selection__account_journal__sepa_pain_version__pain_001_001_03
msgid "pain.001.001.03"
msgstr "pain.001.001.03"

#. module: account_iso20022
#: model:ir.model.fields.selection,name:account_iso20022.selection__account_journal__sepa_pain_version__pain_001_001_09
msgid "pain.001.001.09"
msgstr "pain.001.001.09"
