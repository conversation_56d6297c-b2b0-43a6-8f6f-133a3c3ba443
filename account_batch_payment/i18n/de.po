# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_batch_payment
# 
# Translators:
# Wil Odo<PERSON>, 2024
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-23 18:45+0000\n"
"PO-Revision-Date: 2024-09-25 09:43+0000\n"
"Last-Translator: <PERSON><PERSON>, 2025\n"
"Language-Team: German (https://app.transifex.com/odoo/teams/41243/de/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: de\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.print_batch_payment
msgid "$1000.0"
msgstr "1000,00 €"

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.print_batch_payment
msgid "2023-08-14"
msgstr "14-08-2023"

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.print_batch_payment
msgid "2023-08-15"
msgstr "15-08-2023"

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.print_batch_payment
msgid "*************"
msgstr "*************"

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.view_account_move_kanban
msgid "<i class=\"fa fa-clock-o\" aria-label=\"Date\" role=\"img\" title=\"Date\"/>"
msgstr "<i class=\"fa fa-clock-o\" aria-label=\"Date\" role=\"img\" title=\"Date\"/>"

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.view_account_payment_form_inherit_account_batch_payment
msgid "<span>Batch Payment</span>"
msgstr "<span>Sammelzahlungen</span>"

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.batch_error_wizard_form
msgid ""
"<strong invisible=\"not warning_line_ids\">The following warnings were also "
"raised; they do not impeach validation</strong>"
msgstr ""
"<strong invisible=\"not warning_line_ids\">Es wurden auch folgende Warnungen"
" ausgesprochen, die die Validierung jedoch nicht beeinträchtigen</strong>"

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.batch_error_wizard_form
msgid "<strong>Please first consider the following warnings</strong>"
msgstr ""
"<strong>Bitte beachten Sie zunächst die folgenden Warnhinweise</strong>"

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.batch_error_wizard_form
msgid "<strong>The following errors occurred</strong>"
msgstr "<strong>Die folgenden Fehler sind aufgetreten</strong>"

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.print_batch_payment
msgid "ABC Holder Name"
msgstr "ABC-Inhabername"

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.print_batch_payment
msgid "ABC Suppliers"
msgstr "ABC-Lieferanten"

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.print_batch_payment
msgid "Account Holder Name"
msgstr "Kontoinhabername"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__message_needaction
msgid "Action Needed"
msgstr "Aktion notwendig"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__activity_ids
msgid "Activities"
msgstr "Aktivitäten"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Aktivitätsausnahme-Dekoration"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__activity_state
msgid "Activity State"
msgstr "Status der Aktivität"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__activity_type_icon
msgid "Activity Type Icon"
msgstr "Symbol des Aktivitätstyps"

#. module: account_batch_payment
#. odoo-python
#: code:addons/account_batch_payment/models/account_batch_payment.py:0
msgid "All payments in the batch must share the same payment method."
msgstr "Alle Zahlungen im Stapel müssen dieselbe Zahlungsmethode haben."

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__amount
#: model_terms:ir.ui.view,arch_db:account_batch_payment.print_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.view_batch_payment_form
msgid "Amount"
msgstr "Betrag"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__amount_residual
msgid "Amount Residual"
msgstr "Restbetrag"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__amount_residual_currency
msgid "Amount Residual Currency"
msgstr "Währung des Restbetrags"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_payment__amount_signed
msgid "Amount Signed"
msgstr "Betrag unterzeichnet"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__message_attachment_count
msgid "Attachment Count"
msgstr "Anzahl Anhänge"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__available_payment_method_ids
msgid "Available Payment Method"
msgstr "Verfügbare Zahlungsmethode"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__journal_id
msgid "Bank"
msgstr "Bank"

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.view_batch_payment_search
msgid "Bank Journal"
msgstr "Bankjournal"

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.print_batch_payment
msgid "Bank Transfer"
msgstr "Banküberweisung"

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.view_batch_payment_form
msgid "Batch Content"
msgstr "Stapelinhalt"

#. module: account_batch_payment
#: model:account.payment.method,name:account_batch_payment.account_payment_method_batch_deposit
#: model_terms:ir.ui.view,arch_db:account_batch_payment.account_journal_dashboard_kanban_view_inherited
msgid "Batch Deposit"
msgstr "Sammeleinzahlung"

#. module: account_batch_payment
#: model:ir.model,name:account_batch_payment.model_report_account_batch_payment_print_batch_payment
msgid "Batch Deposit Report"
msgstr "Bericht über Sammeleinzahlung von Schecks"

#. module: account_batch_payment
#. odoo-python
#: code:addons/account_batch_payment/models/account_payment.py:0
#: model:ir.model,name:account_batch_payment.model_account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_error_wizard__batch_payment_id
#: model:ir.model.fields,field_description:account_batch_payment.field_account_payment__batch_payment_id
#: model_terms:ir.ui.view,arch_db:account_batch_payment.view_batch_payment_form
#: model_terms:ir.ui.view,arch_db:account_batch_payment.view_batch_payment_search
msgid "Batch Payment"
msgstr "Sammelzahlung"

#. module: account_batch_payment
#: model:ir.ui.menu,name:account_batch_payment.menu_batch_payment_purchases
#: model:ir.ui.menu,name:account_batch_payment.menu_batch_payment_sales
#: model_terms:ir.ui.view,arch_db:account_batch_payment.view_account_payment_search_inherit_account_batch_payment
msgid "Batch Payments"
msgstr "Sammelzahlungen"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__batch_type
msgid "Batch Type"
msgstr "Sammelzahlungstyp"

#. module: account_batch_payment
#: model_terms:ir.actions.act_window,help:account_batch_payment.action_batch_payment_in
#: model_terms:ir.actions.act_window,help:account_batch_payment.action_batch_payment_out
msgid ""
"Batch payments allow you grouping different payments to ease\n"
"                    reconciliation. They are also useful when depositing checks\n"
"                    to the bank."
msgstr ""
"Sammelzahlungen ermöglichen es Ihnen, verschiedene Zahlungen zu gruppieren, um\n"
"                    um die Abstimmung (Konten) zu erleichtern. Sie sind auch nützlich bei der Einreichung von Schecks\n"
"                    bei der Bank."

#. module: account_batch_payment
#: model:ir.model,name:account_batch_payment.model_account_batch_error_wizard
msgid "Batch payments error reporting wizard"
msgstr "Assistent für die Fehlerberichterstattung bei Sammelzahlungen"

#. module: account_batch_payment
#: model:ir.model,name:account_batch_payment.model_account_batch_error_wizard_line
msgid "Batch payments error reporting wizard line"
msgstr ""
"Zeile des Assistenten für die Fehlerberichterstattung bei Sammelzahlungen"

#. module: account_batch_payment
#. odoo-python
#: code:addons/account_batch_payment/models/account_batch_payment.py:0
msgid "Cannot validate an empty batch. Please add some payments to it first."
msgstr ""
"Ein leerer Stapel kann nicht validiert werden. Bitte fügen Sie ihm zuerst "
"einige Zahlungen hinzu."

#. module: account_batch_payment
#. odoo-python
#: code:addons/account_batch_payment/models/account_batch_payment.py:0
msgid "Check Payments"
msgstr "Zahlungen prüfen"

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.batch_error_wizard_form
msgid "Close"
msgstr "Schließen"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__payment_method_code
msgid "Code"
msgstr "Code"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__company_id
msgid "Company"
msgstr "Unternehmen"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__company_currency_id
msgid "Company Currency"
msgstr "Unternehmenswährung"

#. module: account_batch_payment
#: model:ir.model.fields,help:account_batch_payment.field_account_batch_payment__company_id
msgid "Company related to this journal"
msgstr "Unternehmen für dieses Journal"

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.view_account_payment_tree_inherit_account_batch_payment
msgid "Create Batch"
msgstr "Stapel erstellen"

#. module: account_batch_payment
#. odoo-python
#: code:addons/account_batch_payment/models/account_journal.py:0
msgid "Create Batch Payment"
msgstr "Sammelzahlung erstellen"

#. module: account_batch_payment
#: model_terms:ir.actions.act_window,help:account_batch_payment.action_batch_payment_in
msgid "Create a new customer batch payment"
msgstr "Eine neue Kunden-Sammelzahlung erstellen"

#. module: account_batch_payment
#: model_terms:ir.actions.act_window,help:account_batch_payment.action_batch_payment_out
msgid "Create a new vendor batch payment"
msgstr "Eine neue Lieferanten-Sammelzahlung erstellen"

#. module: account_batch_payment
#: model:ir.actions.server,name:account_batch_payment.action_account_create_batch_payment
msgid "Create batch payment"
msgstr "Sammelzahlung erstellen"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_error_wizard__create_uid
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_error_wizard_line__create_uid
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__create_uid
msgid "Created by"
msgstr "Erstellt von"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_error_wizard__create_date
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_error_wizard_line__create_date
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__create_date
msgid "Created on"
msgstr "Erstellt am"

#. module: account_batch_payment
#: model:ir.model.fields,help:account_batch_payment.field_account_batch_payment__export_file_create_date
msgid "Creation date of the related export file."
msgstr "Erstellungsdatum der zugehörigen Exportdatei."

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__currency_id
msgid "Currency"
msgstr "Währung"

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.print_batch_payment
msgid "Customer"
msgstr "Kunde"

#. module: account_batch_payment
#: model:ir.actions.act_window,name:account_batch_payment.action_batch_payment_in
msgid "Customer Batch Payments"
msgstr "Kundensammelzahlungen"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__date
#: model_terms:ir.ui.view,arch_db:account_batch_payment.print_batch_payment
msgid "Date"
msgstr "Datum"

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.print_batch_payment
msgid "Demo Ref"
msgstr "Demoref."

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_error_wizard_line__description
msgid "Description"
msgstr "Beschreibung"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_error_wizard__display_name
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_error_wizard_line__display_name
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__display_name
msgid "Display Name"
msgstr "Anzeigename"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_error_wizard__error_line_ids
msgid "Error Line"
msgstr "Fehlerzeile"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_error_wizard_line__error_wizard_id
msgid "Error Wizard"
msgstr "Fehler-Assistent"

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.batch_error_wizard_line_tree
msgid "Exclude Payments"
msgstr "Zahlungen ausschließen"

#. module: account_batch_payment
#: model:ir.model.fields,help:account_batch_payment.field_account_batch_payment__export_file
msgid "Export file related to this batch"
msgstr "Exportdatei für diesen Stapel"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__export_file
msgid "File"
msgstr "Datei"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__file_generation_enabled
msgid "File Generation Enabled"
msgstr "Dateierstellung aktiviert"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__export_filename
msgid "File Name"
msgstr "Dateiname"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__message_follower_ids
msgid "Followers"
msgstr "Follower"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__message_partner_ids
msgid "Followers (Partners)"
msgstr "Follower (Partner)"

#. module: account_batch_payment
#: model:ir.model.fields,help:account_batch_payment.field_account_batch_payment__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "FontAwesome-Icon, z. B. fa-tasks"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__export_file_create_date
msgid "Generation Date"
msgstr "Erstellungsdatum"

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.view_batch_payment_search
msgid "Group By"
msgstr "Gruppieren nach"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__has_message
msgid "Has Message"
msgstr "Hat eine Nachricht"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_error_wizard_line__help_message
msgid "Help"
msgstr "Hilfe"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_error_wizard__id
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_error_wizard_line__id
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__id
msgid "ID"
msgstr "ID"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__activity_exception_icon
msgid "Icon"
msgstr "Icon"

#. module: account_batch_payment
#: model:ir.model.fields,help:account_batch_payment.field_account_batch_payment__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Icon, um eine Ausnahmeaktivität anzuzeigen."

#. module: account_batch_payment
#: model:ir.model.fields,help:account_batch_payment.field_account_batch_payment__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Falls markiert, erfordern neue Nachrichten Ihre Aufmerksamkeit."

#. module: account_batch_payment
#: model:ir.model.fields,help:account_batch_payment.field_account_batch_payment__message_has_error
#: model:ir.model.fields,help:account_batch_payment.field_account_batch_payment__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr ""
"Falls markiert, weisen einige Nachrichten einen Zustellungsfehler auf."

#. module: account_batch_payment
#: model:ir.model.fields.selection,name:account_batch_payment.selection__account_batch_payment__batch_type__inbound
msgid "Inbound"
msgstr "Eingehend"

#. module: account_batch_payment
#. odoo-python
#: code:addons/account_batch_payment/models/account_journal.py:0
msgid "Inbound Batch Payments Sequence"
msgstr "Sequenz für eingehende Sammelzahlungen"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__message_is_follower
msgid "Is Follower"
msgstr "Ist Follower"

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.print_batch_payment
msgid "Issuing bank account :"
msgstr "Ausstellendes Bankkonto:"

#. module: account_batch_payment
#: model:ir.model,name:account_batch_payment.model_account_journal
msgid "Journal"
msgstr "Journal"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_error_wizard__write_uid
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_error_wizard_line__write_uid
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__write_uid
msgid "Last Updated by"
msgstr "Zuletzt aktualisiert von"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_error_wizard__write_date
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_error_wizard_line__write_date
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__write_date
msgid "Last Updated on"
msgstr "Zuletzt aktualisiert am"

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.view_batch_payment_form
msgid "Leave empty to generate automatically..."
msgstr "Leer lassen, um automatisch zu generieren ..."

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.print_batch_payment
msgid "Memo"
msgstr "Vermerk"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__message_has_error
msgid "Message Delivery error"
msgstr "Nachricht mit Zustellungsfehler"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__message_ids
msgid "Messages"
msgstr "Nachrichten"

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.print_batch_payment
msgid "Monthly Payment"
msgstr "Monatliche Zahlung"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Frist für meine Aktivitäten"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_payment__payment_method_name
msgid "Name"
msgstr "Name"

#. module: account_batch_payment
#: model:ir.model.fields,help:account_batch_payment.field_account_batch_payment__export_filename
msgid "Name of the export file generated for this batch"
msgstr "Name der für diesen Stapel erzeugten Exportdatei"

#. module: account_batch_payment
#: model:ir.model.fields,help:account_batch_payment.field_account_payment__amount_signed
msgid "Negative value of amount field if payment_type is outbound"
msgstr "Negativer Wert des Betragsfeldes, wenn payment_type ausgehend ist"

#. module: account_batch_payment
#: model:ir.model.fields.selection,name:account_batch_payment.selection__account_batch_payment__state__draft
msgid "New"
msgstr "Neu"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Nächstes Aktivitätskalenderereignis"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Nächste Aktivitätsfrist"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__activity_summary
msgid "Next Activity Summary"
msgstr "Zusammenfassung der nächsten Aktivität"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__activity_type_id
msgid "Next Activity Type"
msgstr "Nächster Aktivitätstyp"

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.view_account_payment_search_inherit_account_batch_payment
msgid "Not Batch Payments"
msgstr "Keine Sammelzahlungen"

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.view_account_payment_search_inherit_account_batch_payment
msgid "Not reconciled"
msgstr "Nicht abgestimmt"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__message_needaction_counter
msgid "Number of Actions"
msgstr "Anzahl der Aktionen"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__message_has_error_counter
msgid "Number of errors"
msgstr "Anzahl der Fehler"

#. module: account_batch_payment
#: model:ir.model.fields,help:account_batch_payment.field_account_batch_payment__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Anzahl der Nachrichten, die eine Aktion erfordern"

#. module: account_batch_payment
#: model:ir.model.fields,help:account_batch_payment.field_account_batch_payment__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Anzahl der Nachrichten mit Zustellungsfehler."

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.print_batch_payment
msgid "Odoo Payments LLC"
msgstr "Odoo Payments LLC"

#. module: account_batch_payment
#: model:ir.model.fields.selection,name:account_batch_payment.selection__account_batch_payment__batch_type__outbound
msgid "Outbound"
msgstr "Ausgehend"

#. module: account_batch_payment
#. odoo-python
#: code:addons/account_batch_payment/models/account_journal.py:0
msgid "Outbound Batch Payments Sequence"
msgstr "Sequenz für ausgehende Sammelzahlungen"

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.view_account_payment_tree_inherit_account_batch_payment
msgid "Partner"
msgstr "Partner"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__payment_ids_domain
msgid "Payment Ids Domain"
msgstr "Domain der Zahlungs-ID"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__payment_method_id
msgid "Payment Method"
msgstr "Zahlungsmethode"

#. module: account_batch_payment
#: model:ir.model,name:account_batch_payment.model_account_payment_method
msgid "Payment Methods"
msgstr "Zahlungsmethoden"

#. module: account_batch_payment
#. odoo-python
#: code:addons/account_batch_payment/models/account_payment.py:0
msgid "Payment added in batch %s"
msgstr "Zahlung zum Stapel %s hinzugefügt"

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.view_batch_payment_form
msgid "Payment method"
msgstr "Zahlungsmethode"

#. module: account_batch_payment
#. odoo-python
#: code:addons/account_batch_payment/models/account_batch_payment.py:0
#: code:addons/account_batch_payment/models/account_payment.py:0
msgid "Payment removed from batch %s"
msgstr "Zahlung aus Stapel %s entfernt"

#. module: account_batch_payment
#: model:ir.model,name:account_batch_payment.model_account_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_error_wizard_line__payment_ids
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__payment_ids
#: model_terms:ir.ui.view,arch_db:account_batch_payment.view_batch_payment_form
msgid "Payments"
msgstr "Zahlungen"

#. module: account_batch_payment
#. odoo-python
#: code:addons/account_batch_payment/models/account_batch_payment.py:0
#: code:addons/account_batch_payment/wizard/batch_error.py:0
msgid "Payments in Error"
msgstr "Fehlerhafte Zahlungen"

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.view_batch_payment_form
msgid "Print"
msgstr "Drucken"

#. module: account_batch_payment
#: model:ir.actions.report,name:account_batch_payment.action_print_batch_payment
msgid "Print Batch Payment"
msgstr "Sammelzahlung drucken"

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.batch_error_wizard_form
msgid "Proceed with validation"
msgstr "Mit Validierung fortfahren"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__rating_ids
msgid "Ratings"
msgstr "Bewertungen"

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.view_batch_payment_form
msgid "Re-generate Export File"
msgstr "Exportdatei neu generieren"

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.print_batch_payment
msgid "Recipient Bank Account"
msgstr "Bankkonto des Empfängers"

#. module: account_batch_payment
#: model:ir.model.fields.selection,name:account_batch_payment.selection__account_batch_payment__state__reconciled
msgid "Reconciled"
msgstr "Abgestimmt"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__name
msgid "Reference"
msgstr "Referenz"

#. module: account_batch_payment
#. odoo-python
#: code:addons/account_batch_payment/models/account_batch_payment.py:0
msgid "Remove the payments from the batch or change their state."
msgstr ""
"Entfernen Sie die Zahlungen aus dem Stapel oder ändern Sie deren Status."

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__activity_user_id
msgid "Responsible User"
msgstr "Verantwortlicher Benutzer"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__message_has_sms_error
msgid "SMS Delivery error"
msgstr "SMS-Zustellungsfehler"

#. module: account_batch_payment
#: model:ir.model.fields.selection,name:account_batch_payment.selection__account_batch_payment__state__sent
msgid "Sent"
msgstr "Gesendet"

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.batch_error_wizard_line_tree
msgid "Show"
msgstr "Zeigen"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_error_wizard_line__show_remove_button
msgid "Show Remove Button"
msgstr "Schaltfläche „Entfernen“ anzeigen"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_error_wizard__show_remove_options
msgid "Show Remove Options"
msgstr "„Optionen entfernen“ anzeigen"

#. module: account_batch_payment
#. odoo-python
#: code:addons/account_batch_payment/models/account_batch_payment.py:0
msgid "Some payments have already been sent."
msgstr "Einige Zahlungen wurden bereits veranlasst."

#. module: account_batch_payment
#. odoo-python
#: code:addons/account_batch_payment/models/account_batch_payment.py:0
msgid "Some recipient accounts do not allow out payments."
msgstr "Einige Empfängerkonten erlauben keine Auszahlungen."

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__state
#: model_terms:ir.ui.view,arch_db:account_batch_payment.view_batch_payment_search
msgid "State"
msgstr "Status"

#. module: account_batch_payment
#: model:ir.model.fields,help:account_batch_payment.field_account_batch_payment__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Status basierend auf Aktivitäten\n"
"Überfällig: Fälligkeitsdatum bereits überschritten\n"
"Heute: Aktivitätsdatum ist heute\n"
"Geplant: anstehende Aktivitäten."

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.print_batch_payment
msgid "TOTAL"
msgstr "GESAMT"

#. module: account_batch_payment
#. odoo-python
#: code:addons/account_batch_payment/models/account_batch_payment.py:0
msgid ""
"Target another recipient account or allow sending money to the current one."
msgstr ""
"Legen Sie ein anderes Empfängerkonto fest oder erlauben Sie den Versand von "
"Geld an das aktuelle."

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.batch_error_wizard_form
msgid "The batch cannot be validated."
msgstr "Der Stapel kann nicht validiert werden."

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.batch_error_wizard_form
msgid "The batch could not be validated"
msgstr "Der Stapel konnte nicht validiert werden"

#. module: account_batch_payment
#. odoo-python
#: code:addons/account_batch_payment/models/account_batch_payment.py:0
msgid ""
"The batch must have the same payment method as the payments it contains."
msgstr ""
"Der Stapel muss dieselbe Zahlungsmethode haben wie die darin enthaltenen "
"Zahlungen."

#. module: account_batch_payment
#. odoo-python
#: code:addons/account_batch_payment/models/account_batch_payment.py:0
msgid "The batch must have the same type as the payments it contains."
msgstr ""
"Die Stapel muss vom selben Typ sein wie die darin enthaltenen Zahlungen."

#. module: account_batch_payment
#: model:ir.model.fields,help:account_batch_payment.field_account_batch_error_wizard__batch_payment_id
msgid ""
"The batch payment generating the errors and warnings displayed in this "
"wizard."
msgstr ""
"Die Sammelzahlung erzeugt die in diesem Assistenten angezeigten Fehler und "
"Warnungen."

#. module: account_batch_payment
#. odoo-python
#: code:addons/account_batch_payment/models/account_batch_payment.py:0
msgid ""
"The journal of the batch payment and of the payments it contains must be the"
" same."
msgstr ""
"Das Journal der Sammelzahlung und der darin enthaltenen Zahlungen muss "
"identisch sein."

#. module: account_batch_payment
#: model:ir.model.fields,help:account_batch_payment.field_account_batch_payment__payment_method_id
msgid "The payment method used by the payments in this batch."
msgstr ""
"Die Zahlungsmethode, die von den Zahlungen in diesem Stapel verwendet wird."

#. module: account_batch_payment
#. odoo-python
#: code:addons/account_batch_payment/models/account_batch_payment.py:0
msgid ""
"To validate the batch, payments must be in process. But some are already "
"matched with a bank statement."
msgstr ""
"Um den Stapel zu validieren, müssen die Zahlungen in Bearbeitung sein. "
"Einige sind jedoch bereits mit einem Kontoauszug abgeglichen."

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.view_batch_payment_form
msgid "Total"
msgstr "Gesamt"

#. module: account_batch_payment
#: model:ir.model.fields,help:account_batch_payment.field_account_batch_error_wizard__show_remove_options
msgid ""
"True if and only if the options to remove the payments causing the errors or"
" warnings from the batch should be shown"
msgstr ""
"Nur Wahr, wenn die Optionen zum Entfernen der Zahlungen die Fehler "
"verursachen oder es sollten Warnungen von der Sammelzahlung gezeigt werden"

#. module: account_batch_payment
#: model:ir.model.fields,help:account_batch_payment.field_account_batch_payment__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Typ der Ausnahmeaktivität im Datensatz."

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.view_batch_payment_search
msgid "Unreconciled"
msgstr "Nicht abgestimmt"

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.view_batch_payment_form
msgid "Validate"
msgstr "Validieren"

#. module: account_batch_payment
#: model_terms:ir.ui.view,arch_db:account_batch_payment.print_batch_payment
msgid "Vendor"
msgstr "Lieferant"

#. module: account_batch_payment
#: model:ir.actions.act_window,name:account_batch_payment.action_batch_payment_out
msgid "Vendor Batch Payments"
msgstr "Lieferantensammelzahlungen"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_error_wizard__warning_line_ids
msgid "Warning Line"
msgstr "Warnungzeile"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_error_wizard_line__warning_wizard_id
msgid "Warning Wizard"
msgstr "Warnungsassistent"

#. module: account_batch_payment
#: model:ir.model.fields,field_description:account_batch_payment.field_account_batch_payment__website_message_ids
msgid "Website Messages"
msgstr "Website-Nachrichten"

#. module: account_batch_payment
#: model:ir.model.fields,help:account_batch_payment.field_account_batch_payment__website_message_ids
msgid "Website communication history"
msgstr "Website-Kommunikationsverlauf"

#. module: account_batch_payment
#: model:ir.model.fields,help:account_batch_payment.field_account_batch_payment__file_generation_enabled
msgid ""
"Whether or not this batch payment should display the 'Generate File' button "
"instead of 'Print' in form view."
msgstr ""
"Gibt an, ob bei dieser Sammelzahlung die Schaltfläche „Datei generieren“ "
"anstelle von „Drucken“ in der Formularansicht angezeigt werden soll."

#. module: account_batch_payment
#. odoo-python
#: code:addons/account_batch_payment/models/account_batch_payment.py:0
msgid "You cannot add payments with zero amount in a Batch Payment."
msgstr ""
"Sie können keine Zahlungen mit dem Betrag Null zu einer Sammelzahlung "
"hinzufügen."

#. module: account_batch_payment
#. odoo-python
#: code:addons/account_batch_payment/models/account_batch_payment.py:0
msgid "You cannot add the same payment to multiple batches."
msgstr "Sie können nicht dieselbe Zahlung für mehrere Stapel hinzufügen."

#. module: account_batch_payment
#. odoo-python
#: code:addons/account_batch_payment/models/account_batch_payment.py:0
msgid ""
"You cannot create a batch with payments that are already in another batch."
msgstr ""
"Sie können keinen Stapel von Zahlungen erstellen, die sich bereits in einem "
"anderen Stapel befinden."

#. module: account_batch_payment
#. odoo-python
#: code:addons/account_batch_payment/models/account_batch_payment.py:0
msgid "You cannot create batches with overlapping payments."
msgstr ""
"Sie können keine Stapel mit sich überschneidenden Zahlungen erstellen."
