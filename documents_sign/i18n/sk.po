# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* documents_sign
# 
# Translators:
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 09:15+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Slovak (https://app.transifex.com/odoo/teams/41243/sk/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sk\n"
"Plural-Forms: nplurals=4; plural=(n % 1 == 0 && n == 1 ? 0 : n % 1 == 0 && n >= 2 && n <= 4 ? 1 : n % 1 != 0 ? 2: 3);\n"

#. module: documents_sign
#: model_terms:ir.ui.view,arch_db:documents_sign.res_config_settings_view_form_inherit_documents_sign
msgid ""
"Base folder for the folders created on-the-fly in the Sign App. Permission "
"of the created folders will be inherited from this base folder."
msgstr ""

#. module: documents_sign
#: model:ir.model,name:documents_sign.model_res_company
msgid "Companies"
msgstr "Spoločnosti"

#. module: documents_sign
#: model:ir.model,name:documents_sign.model_res_config_settings
msgid "Config Settings"
msgstr "Nastavenia konfigurácie"

#. module: documents_sign
#: model:ir.model,name:documents_sign.model_documents_document
msgid "Document"
msgstr "Dokument"

#. module: documents_sign
#: model_terms:ir.ui.view,arch_db:documents_sign.res_config_settings_view_form_inherit_documents_sign
msgid ""
"Each document template can be configured to centralize signed documents into"
" a specific folder."
msgstr ""

#. module: documents_sign
#: model_terms:ir.ui.view,arch_db:documents_sign.res_config_settings_view_form_inherit_documents_sign
msgid "Folder"
msgstr "Priečinok"

#. module: documents_sign
#: model_terms:ir.ui.view,arch_db:documents_sign.res_config_settings_view_form_inherit_documents_sign
msgid "Go to Sign Document Templates"
msgstr ""

#. module: documents_sign
#. odoo-python
#: code:addons/documents_sign/models/documents_document.py:0
msgid "Invalid model %s"
msgstr ""

#. module: documents_sign
#. odoo-python
#: code:addons/documents_sign/models/documents_document.py:0
msgid "New templates"
msgstr "Nové šablóny"

#. module: documents_sign
#: model:ir.actions.server,name:documents_sign.ir_actions_server_create_sign_template_direct
#: model_terms:ir.ui.view,arch_db:documents_sign.res_config_settings_view_form_inherit_documents_sign
msgid "Sign"
msgstr "E-podpis"

#. module: documents_sign
#: model:ir.model.fields,field_description:documents_sign.field_res_config_settings__documents_sign_folder_id
#: model_terms:ir.ui.view,arch_db:documents_sign.res_config_settings_view_form_inherit_documents_sign
msgid "Sign Base Folder"
msgstr ""

#. module: documents_sign
#: model:ir.model.fields,field_description:documents_sign.field_res_company__documents_sign_folder_id
msgid "Sign Folder"
msgstr ""

#. module: documents_sign
#: model:ir.actions.server,name:documents_sign.ir_actions_server_create_sign_template_direct_create
msgid "Sign create"
msgstr ""

#. module: documents_sign
#: model:ir.model,name:documents_sign.model_sign_request
msgid "Signature Request"
msgstr "Podpisová žiadosť"

#. module: documents_sign
#: model:ir.model,name:documents_sign.model_sign_request_item
msgid "Signature Request Item"
msgstr "Položka žiadosti o podpis"

#. module: documents_sign
#: model:ir.model,name:documents_sign.model_sign_template
msgid "Signature Template"
msgstr "Podpisová šablóna"

#. module: documents_sign
#: model:ir.model.fields,field_description:documents_sign.field_sign_template__folder_id
msgid "Signed Document Folder"
msgstr ""

#. module: documents_sign
#: model:ir.model.fields,field_description:documents_sign.field_sign_template__documents_tag_ids
msgid "Signed Document Tags"
msgstr "Tagy podpísaných dokumentov"

#. module: documents_sign
#. odoo-python
#: code:addons/documents_sign/models/documents_document.py:0
msgid "This action can only be applied on a single record."
msgstr ""

#. module: documents_sign
#. odoo-python
#: code:addons/documents_sign/models/documents_document.py:0
msgid "This action can only be applied on pdf document."
msgstr ""
