<odoo>
    <record id="account_move_eg_discount_form_view" model="ir.ui.view">
        <field name="name">account.move.eg.discount.form.view</field>
        <field name="model">account.move</field>
        <field name="inherit_id" ref="account.view_move_form"/>
        <field name="arch" type="xml">
            <xpath expr="//form/sheet/notebook/page/group" position="before">
                <field name="is_global_discount" invisible="1"/>
                <div class="container" invisible="is_global_discount == False">
                    <label for="discount_method" string="Sale Discount Type:"
                           readonly="state != 'draft'"/>
                    <field name="discount_method" readonly="state != 'draft'"/>
                </div>
                <div class="container" invisible="is_global_discount == False">
                    <label for="discount_amount" string="Sale Discount:"
                           readonly="state != 'draft'"/>
                    <field name="discount_amount" readonly="state != 'draft'"/>
                </div>
            </xpath>
            <xpath expr="//field[@name='amount_residual']" position="after">
                <field name="sale_order" invisible="1"/>
                <field name="total_discount" options="{'currency_field': 'currency_id'}"
                       invisible="state != 'draft' or sale_order == True"/>
                <field name="stored_total_discounts" options="{'currency_field': 'currency_id'}"
                       invisible="state != 'draft' or sale_order == True"/>
            </xpath>
            <xpath expr="//field[@name='invoice_line_ids']//field[@name='price_unit']" position="after">
                    <field name="stored_total_discounts"
                        invisible="1" readonly="1"/>
                </xpath>
        </field>
    </record>
</odoo>
