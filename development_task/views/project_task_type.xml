<?xml version="1.0" encoding="UTF-8" ?>
<odoo>
    <!-- Form view -->
    <record id="form_view_project_task_type" model="ir.ui.view">
        <field name="name">form.view.project.task.type</field>
        <field name="model">project.task.type</field>
        <field name="inherit_id" ref="project.task_type_edit"/>
        <field name="arch" type="xml">
            <field name="sequence" position="after">
                <field name="is_new_stage"/>
                <field name="is_inprogress_stage"/>
                <field name="is_done_stage"/>
                <field name="is_on_hold_stage"/>
                <field name="is_cancel_stage"/>
            </field>
        </field>
    </record>

    <!-- List view -->
    <record id="list_view_project_task_type" model="ir.ui.view">
        <field name="name">list.view.project.task.type</field>
        <field name="model">project.task.type</field>
        <field name="inherit_id" ref="project.task_type_tree_inherited"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='project_ids' and @optional='show']" position="attributes">
                <attribute name="optional">hide</attribute>
            </xpath>
            <xpath expr="//field[@name='project_ids']" position="attributes">
                <attribute name="column_invisible">1</attribute>
            </xpath>
            <xpath expr="//list" position="attributes">
                <attribute name="default_group_by"></attribute>
            </xpath>
        </field>
    </record>
</odoo>