from odoo import fields, models


class ReopenReasonWizard(models.TransientModel):
    _name = 'reopen.reason.wizard'
    _description = 'reopen reason'

    reason = fields.Text('Reason', required=True)

    def update_reopen_reason(self):
        active_id = self.env.context.get('active_id')
        if active_id:
            project_id = self.env['project.task'].browse(active_id)
            if project_id:
                project_id.update_state_or_reopen_count(self.reason)
