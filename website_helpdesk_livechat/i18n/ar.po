# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_helpdesk_livechat
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON> <<EMAIL>>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:25+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2025\n"
"Language-Team: Arabic (https://app.transifex.com/odoo/teams/41243/ar/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ar\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#. module: website_helpdesk_livechat
#. odoo-python
#: code:addons/website_helpdesk_livechat/models/chatbot_script_step.py:0
msgid "%(name)s's Ticket"
msgstr "تذكرة %(name)s "

#. module: website_helpdesk_livechat
#: model:chatbot.script.step,message:website_helpdesk_livechat.chatbot_script_helpdesk_step_done
msgid "Alright, we should have everything we need"
msgstr "حسناً، لدينا كل ما نحتاج إليه "

#. module: website_helpdesk_livechat
#. odoo-python
#: code:addons/website_helpdesk_livechat/models/chatbot_script.py:0
msgid ""
"An \"Email\" step type must exist before the \"Create Ticket\" step for a "
"ticket to be created."
msgstr ""
"يجب أن تأتي الخطوة من نوع \"البريد الإلكتروني\" قبل خطوة \"إنشاء تذكرة\" حتى"
" يتم إنشاء تذكرة. "

#. module: website_helpdesk_livechat
#: model:chatbot.script.step,message:website_helpdesk_livechat.chatbot_script_helpdesk_step_technical_serial_misc
msgid "Anything else to add?"
msgstr "أهناك ما تود إضافته؟ "

#. module: website_helpdesk_livechat
#: model:ir.model,name:website_helpdesk_livechat.model_chatbot_script
msgid "Chatbot Script"
msgstr "نص Chatbot "

#. module: website_helpdesk_livechat
#: model:ir.model,name:website_helpdesk_livechat.model_chatbot_script_step
msgid "Chatbot Script Step"
msgstr "خطوة نَص Chatbot "

#. module: website_helpdesk_livechat
#: model:ir.ui.menu,name:website_helpdesk_livechat.chatbot_config
msgid "Chatbots"
msgstr "برامج الدردشة الآلية "

#. module: website_helpdesk_livechat
#: model_terms:ir.ui.view,arch_db:website_helpdesk_livechat.helpdesk_team_view_form_inherit_website_helpdesk_livechat
msgid "Configure Chatbots"
msgstr "تهيئة برامج الدردشة الآلية "

#. module: website_helpdesk_livechat
#: model_terms:ir.ui.view,arch_db:website_helpdesk_livechat.helpdesk_team_view_form_inherit_website_helpdesk_livechat
msgid "Configure Live Chat Channel"
msgstr "تهيئة قناة الدردشة المباشرة "

#. module: website_helpdesk_livechat
#: model:ir.model.fields.selection,name:website_helpdesk_livechat.selection__chatbot_script_step__step_type__create_ticket
msgid "Create Ticket"
msgstr "إنشاء تذكرة "

#. module: website_helpdesk_livechat
#. odoo-javascript
#: code:addons/website_helpdesk_livechat/static/src/core/web/store_service_patch.js:0
msgid "Create a new helpdesk ticket (/ticket ticket title)"
msgstr "أنشئ تذكرة جديدة في مكتب المساعدة (/ticket عنوان التذكرة) "

#. module: website_helpdesk_livechat
#. odoo-python
#: code:addons/website_helpdesk_livechat/models/helpdesk.py:0
msgid ""
"Create a new helpdesk ticket by typing: %(pre_start)s%(ticket_command)s "
"%(i_start)sticket title%(i_end)s%(pre_end)s"
msgstr ""
"قم بإنشاء تذكرة مساعدة جديدة عن طريق كتابة: %(pre_start)s%(ticket_command)s "
"%(i_start)sعنوان التذكرة %(i_end)s%(pre_end)s "

#. module: website_helpdesk_livechat
#. odoo-python
#: code:addons/website_helpdesk_livechat/models/helpdesk.py:0
msgid "Created a new ticket: %s"
msgstr "تم إنشاء تذكرة جديدة: %s "

#. module: website_helpdesk_livechat
#: model:ir.model,name:website_helpdesk_livechat.model_discuss_channel
msgid "Discussion Channel"
msgstr "قناة المناقشة"

#. module: website_helpdesk_livechat
#: model:chatbot.script.step,message:website_helpdesk_livechat.chatbot_script_helpdesk_step_dispatch
msgid "First, what is the nature of your issue?"
msgstr "أولاً، ما هي طبيعة مشكلتك؟ "

#. module: website_helpdesk_livechat
#: model:chatbot.script.step,message:website_helpdesk_livechat.chatbot_script_helpdesk_step_administrative_customer_ref_yes
msgid "Great, that will make our lives easier."
msgstr "رائع، سيسهّل علينا ذلك كثيراً. "

#. module: website_helpdesk_livechat
#: model:chatbot.script,title:website_helpdesk_livechat.chatbot_script_helpdesk_bot
msgid "Helpdesk Bot"
msgstr "Bot مكتب المساعدة "

#. module: website_helpdesk_livechat
#: model:ir.model,name:website_helpdesk_livechat.model_helpdesk_team
#: model:ir.model.fields,field_description:website_helpdesk_livechat.field_chatbot_script_step__helpdesk_team_id
msgid "Helpdesk Team"
msgstr "فريق مكتب المساعدة"

#. module: website_helpdesk_livechat
#: model:chatbot.script.step,message:website_helpdesk_livechat.chatbot_script_helpdesk_step_welcome
msgid "Here we go, help is on the way!"
msgstr "هيا، المساعدة في طريقها إليك! "

#. module: website_helpdesk_livechat
#: model:chatbot.script.answer,name:website_helpdesk_livechat.chatbot_script_helpdesk_step_dispatch_answer_technical
msgid "I have a technical issue"
msgstr "لدي مشكلة تقنية "

#. module: website_helpdesk_livechat
#: model:chatbot.script.answer,name:website_helpdesk_livechat.chatbot_script_helpdesk_step_dispatch_answer_administrative
msgid "I have an administrative question"
msgstr "لدي سؤال إداري "

#. module: website_helpdesk_livechat
#: model:chatbot.script.step,message:website_helpdesk_livechat.chatbot_script_helpdesk_step_administrative_customer_ref_no
msgid "It's OK, we can also find your contract by other means."
msgstr "لا بأس، يمكننا العثور على عقدك بطرق أخرى أيضاً. "

#. module: website_helpdesk_livechat
#: model:chatbot.script.step,message:website_helpdesk_livechat.chatbot_script_helpdesk_step_email
msgid "Just a last thing, can we please have your email address?"
msgstr "شيء أخير، أيمكنك كتابة عنوان بريدك الإلكتروني؟ "

#. module: website_helpdesk_livechat
#: model:ir.model.fields,field_description:website_helpdesk_livechat.field_helpdesk_team__use_website_helpdesk_livechat
msgid "Live Chat"
msgstr "الدردشة المباشرة "

#. module: website_helpdesk_livechat
#. odoo-python
#: code:addons/website_helpdesk_livechat/models/helpdesk.py:0
msgid "Load More"
msgstr "تحميل المزيد "

#. module: website_helpdesk_livechat
#: model:chatbot.script.answer,name:website_helpdesk_livechat.chatbot_script_helpdesk_step_administrative_customer_ref_answer_no
msgid "No"
msgstr "لا"

#. module: website_helpdesk_livechat
#. odoo-python
#: code:addons/website_helpdesk_livechat/models/helpdesk.py:0
msgid ""
"No tickets found for %(b_start)s%(keywords)s%(b_end)s.%(br)sMake sure you "
"are using the right format: %(pre_start)s%(search_tickets_command)s "
"%(i_start)skeywords%(i_end)s%(pre_end)s"
msgstr ""
"لم يتم العثور على تذاكر لـ %(b_start)s%(keywords)s%(b_end)s.%(br)sتأكد من "
"استخدام التنسيق الصحيح: %(pre_start)s%(search_tickets_command)s "
"%(i_start)sالكلمات المفتاحية%(i_end)s%(pre_end)s "

#. module: website_helpdesk_livechat
#: model:chatbot.script.step,message:website_helpdesk_livechat.chatbot_script_helpdesk_step_ticket
msgid ""
"OK, I just created a ticket for you. You should receive an email "
"confirmation very soon."
msgstr ""
"حسنا، لقد قمت بإنشاء تذكرة من أجلك الآن. يجب أن يصلك تأكيد عبر البريد "
"الإلكتروني في أي لحظة. "

#. module: website_helpdesk_livechat
#: model:chatbot.script.step,message:website_helpdesk_livechat.chatbot_script_helpdesk_step_technical_serial
msgid "Please write below the serial number of your equipment."
msgstr "رجاءً قم بكتابة الرقم التسلسلي لمعداتك أدناه. "

#. module: website_helpdesk_livechat
#: model:chatbot.script.step,message:website_helpdesk_livechat.chatbot_script_helpdesk_step_administrative_customer_ref_input
msgid "Please write below your customer reference."
msgstr "رجاءً قم بكتابة مرجع العميل الخاص بك أدناه. "

#. module: website_helpdesk_livechat
#. odoo-javascript
#: code:addons/website_helpdesk_livechat/static/src/core/web/store_service_patch.js:0
msgid "Search helpdesk tickets (/search_tickets keyword)"
msgstr "ابحث عن تذاكر مكتب المساعدة (/search_tickets الكلمة المفتاحية) "

#. module: website_helpdesk_livechat
#. odoo-python
#: code:addons/website_helpdesk_livechat/models/helpdesk.py:0
msgid ""
"Search helpdesk tickets by typing: %(pre_start)s%(search_tickets_command)s "
"%(i_start)skeywords%(i_end)s%(pre_end)s"
msgstr ""
"قم بالبحث في تذاكر مكتب المساعدة عن طريق كتابة الكلمات المفتاحية: "
"%(pre_start)s%(search_tickets_command)s %(i_start)s%(i_end)s%(pre_end)s "

#. module: website_helpdesk_livechat
#. odoo-python
#: code:addons/website_helpdesk_livechat/models/helpdesk.py:0
msgid "Something is missing or wrong in command"
msgstr "هناك خطأ ما أو كلمة ناقصة في الأمر"

#. module: website_helpdesk_livechat
#. odoo-python
#: code:addons/website_helpdesk_livechat/models/helpdesk.py:0
msgid "Something is missing or wrong in the command"
msgstr "هناك خطأ ما أو كلمة ناقصة في الأمر"

#. module: website_helpdesk_livechat
#: model:ir.model.fields,field_description:website_helpdesk_livechat.field_chatbot_script_step__step_type
msgid "Step Type"
msgstr "نوع الخطوة "

#. module: website_helpdesk_livechat
#: model:chatbot.script.step,message:website_helpdesk_livechat.chatbot_script_helpdesk_step_technical_serial_thanks
msgid "Thank you, that will help our engineers see what went wrong."
msgstr "شكراً لك، سيساعد ذلك مهندسينا على تحديد المشكلة. "

#. module: website_helpdesk_livechat
#: model_terms:ir.ui.view,arch_db:website_helpdesk_livechat.chatbot_script_view_form
msgid "Tickets"
msgstr "التذاكر "

#. module: website_helpdesk_livechat
#. odoo-python
#: code:addons/website_helpdesk_livechat/models/helpdesk.py:0
msgid ""
"Tickets search results for %(b_start)s%(keywords)s%(b_end)s: "
"%(br)s%(tickets)s"
msgstr ""
"نتائج البحث عن التذاكر %(b_start)s%(keywords)s%(b_end)s: %(br)s%(tickets)s "

#. module: website_helpdesk_livechat
#: model:chatbot.script.step,message:website_helpdesk_livechat.chatbot_script_helpdesk_step_administrative_customer_ref
msgid ""
"To start with, do you have a customer reference?\n"
"They are written on each invoice you received, next to your name."
msgstr ""
"بدايةً، ألديك مرجع عميل؟ \n"
"يكون مكتوباً في أي فاتورة تقوم باستلامها بجانب اسمك. "

#. module: website_helpdesk_livechat
#: model:res.groups,name:website_helpdesk_livechat.group_use_website_helpdesk_livechat
msgid "Use Live Chat"
msgstr "استخدام الدردشة المباشرة "

#. module: website_helpdesk_livechat
#: model:ir.model,name:website_helpdesk_livechat.model_res_users
msgid "User"
msgstr "المستخدم"

#. module: website_helpdesk_livechat
#: model:chatbot.script.step,message:website_helpdesk_livechat.chatbot_script_helpdesk_step_administrative_issue
msgid "We're all set. Now, what is your issue?"
msgstr "لقد انتيهيت. والآن، ما هي المشكلة؟ "

#. module: website_helpdesk_livechat
#: model:chatbot.script.answer,name:website_helpdesk_livechat.chatbot_script_helpdesk_step_administrative_customer_ref_answer_yes
msgid "Yes"
msgstr "نعم"
