# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_referral
# 
# Translators:
# <PERSON>re <PERSON>tem, 2024
# <PERSON>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <AUTHOR> <EMAIL>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# Halil, 2024
# <AUTHOR> <EMAIL>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> A<PERSON> <<EMAIL>>, 2024
# Melih Melik Sonmez, 2024
# <AUTHOR> <EMAIL>, 2024
# <AUTHOR> <EMAIL>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON>, 2024
# <PERSON>rat <PERSON> <<EMAIL>>, 2024
# <AUTHOR> <EMAIL>, 2025
# Wil Odoo, 2025
# <AUTHOR> <EMAIL>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-25 09:12+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Deniz Guvener_Odoo <<EMAIL>>, 2025\n"
"Language-Team: Turkish (https://app.transifex.com/odoo/teams/41243/tr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: tr\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: hr_referral
#. odoo-python
#: code:addons/hr_referral/models/hr_applicant.py:0
msgid ""
" You've gained {gained} points with this progress.{new_line}It makes you a "
"new total of {total} points. Visit {link1}this link{link2} to pick a gift!"
msgstr ""

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_recruitment_report__referral_hired
msgid "# Hired by Referral"
msgstr "# Yönlendirme ile İşe Alındı"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.hr_referral_send_sms_view_form
msgid "+1 123 456 789"
msgstr "+1 123 456 789"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.referral_campaign_email_body_template
msgid ""
",\n"
"            <br/>\n"
"            <br/>"
msgstr ""

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.referral_campaign_sms_template
msgid ""
".\n"
"If you know someone who would be a great fit for this position, please share this link with them:"
msgstr ""

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.referral_email_body_template
msgid "/ Job Offers"
msgstr ""

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_applicant_employee_referral_kanban
msgid "<b>Responsible: </b>"
msgstr "<b>Sorumlu: </b>"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.referral_campaign_email_body_template
msgid ""
"<br/>\n"
"            <br/>\n"
"            Thanks!"
msgstr ""

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_job_employee_referral_kanban
msgid ""
"<i class=\"fa fa-2x fa-envelope-o\" role=\"img\" aria-label=\"Send by Mail\"/>\n"
"                                    <br/>\n"
"                                    <span title=\"Send by Mail\">Send Email</span>"
msgstr ""

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_job_employee_referral_kanban
msgid ""
"<i class=\"fa fa-2x fa-globe\" role=\"img\" aria-label=\"Job Page\"/>\n"
"                                    <br/>\n"
"                                    <span title=\"More info\">Job Page</span>"
msgstr ""

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_job_employee_referral_kanban
msgid ""
"<i class=\"fa fa-2x fa-mobile\" role=\"img\" aria-label=\"Send by SMS\"/>\n"
"                                    <br/>\n"
"                                    <span title=\"Send by SMS\">Send SMS</span>"
msgstr ""

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_applicant_employee_referral_kanban
msgid "<i class=\"fa fa-square-o\" title=\"Open\"/>"
msgstr "<i class=\"fa fa-square-o\" title=\"Open\"/>"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_applicant_employee_referral_kanban
msgid "<i class=\"text-danger fa fa-times\" title=\"Closed\"/>"
msgstr "<i class=\"text-danger fa fa-times\" title=\"Closed\"/>"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_applicant_employee_referral_kanban
msgid "<i class=\"text-success fa fa-check\" title=\"Done\"/>"
msgstr "<i class=\"text-success fa fa-check\" title=\"Done\"/>"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_referral_level_form
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_referral_reward_form
msgid "<span class=\"ml8\">Points</span>"
msgstr "<span class=\"ml8\">Puanlar</span>"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_referral_reward_form
msgid ""
"<span class=\"o_stat_text\">\n"
"                                    Awarded\n"
"                                </span>"
msgstr ""
"<span class=\"o_stat_text\">\n"
"                                    Ödüllü\n"
"                                </span>"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_referral_reward_kanban
msgid ""
"<span title=\"Buy\"><i class=\"fa fa-shopping-basket me-3\" role=\"img\" "
"aria-label=\"Buy\"/>Buy</span>"
msgstr ""

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_job_employee_referral_kanban
msgid ""
"<span title=\"Share on Facebook\">\n"
"                                        <i class=\"fa fa-lg fa-facebook\" role=\"img\" aria-label=\"Share on facebook\"/>\n"
"                                    </span>"
msgstr ""

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_job_employee_referral_kanban
msgid ""
"<span title=\"Share on Linkedin\">\n"
"                                        <i class=\"fa fa-lg fa-linkedin\" role=\"img\" aria-label=\"Share on linkedin\"/>\n"
"                                    </span>"
msgstr ""

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_job_employee_referral_kanban
msgid ""
"<span title=\"Share on Twitter\">\n"
"                                        <i class=\"fa fa-lg fa-twitter\" role=\"img\" aria-label=\"Share on twitter\"/>\n"
"                                    </span>"
msgstr ""

#. module: hr_referral
#. odoo-python
#: code:addons/hr_referral/wizard/hr_referral_alert_mail_wizard.py:0
msgid ""
"A new alert has been added to the Referrals app! Check your <a "
"href=%(url)s>dashboard</a> now!"
msgstr ""
"Yönlendirmeler uygulamasına yeni bir uyarı eklendi! <a "
"href=\"%(url)s\">Kontrol panelinizi</a> şimdi kontrol edin!"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__message_needaction
msgid "Action Needed"
msgstr "Aksiyon Gerekiyor"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_alert__active
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__active
msgid "Active"
msgstr "Etkin"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__activity_ids
msgid "Activities"
msgstr "Aktiviteler"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Etkinlik İstisna Dekorasyonu"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__activity_state
msgid "Activity State"
msgstr "Aktivite Durumu"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__activity_type_icon
msgid "Activity Type Icon"
msgstr "Aktivite Türü İmgesi"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_alert__name
msgid "Alert"
msgstr "İkaz"

#. module: hr_referral
#: model:ir.model,name:hr_referral.model_hr_referral_alert
msgid "Alert in Referral App"
msgstr "Yönlendirme Uygulamasında Uyarı"

#. module: hr_referral
#: model:ir.actions.act_window,name:hr_referral.action_hr_referral_alert_configuration
#: model:ir.ui.menu,name:hr_referral.menu_hr_referral_alert_configuration
msgid "Alerts"
msgstr "İkazlar"

#. module: hr_referral
#: model_terms:ir.actions.act_window,help:hr_referral.action_hr_referral_alert_configuration
msgid ""
"Alerts will be displayed on the first screen to give information or "
"redirects the employees"
msgstr ""

#. module: hr_referral
#: model:ir.model.fields.selection,name:hr_referral.selection__hr_referral_campaign_wizard__target__all
msgid "All"
msgstr "Tümü"

#. module: hr_referral
#: model:ir.model.fields,help:hr_referral.field_hr_recruitment_stage__points
msgid ""
"Amount of points that the referent will receive when the applicant will "
"reach this stage"
msgstr "Başvuran bu aşamaya ulaştığında başvuranın alacağı puan miktarı"

#. module: hr_referral
#: model:ir.model,name:hr_referral.model_hr_applicant
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_points__applicant_id
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_report__applicant_id
msgid "Applicant"
msgstr "Aday"

#. module: hr_referral
#. odoo-python
#: code:addons/hr_referral/models/hr_applicant.py:0
msgid "Applicant must have a company."
msgstr "Başvuru sahibinin bir şirketi olmalıdır."

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.hr_referral_alert_view_search
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_referral_alert_form
msgid "Archived"
msgstr "Arşivlendi"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__message_attachment_count
msgid "Attachment Count"
msgstr "Ek Sayısı"

#. module: hr_referral
#. odoo-python
#: code:addons/hr_referral/models/hr_referral_reward.py:0
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__awarded_employees
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_referral_reward_backend_kanban
msgid "Awarded Employees"
msgstr "Ödül Kazanan Personeller"

#. module: hr_referral
#: model:ir.model.fields.selection,name:hr_referral.selection__hr_referral_friend__position__back
msgid "Back"
msgstr "Geri"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.res_config_settings_view__form
msgid "Background"
msgstr "Arkaplan"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.res_config_settings_view__form
msgid "Background Image"
msgstr "Arkaplan Resmi"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_alert_mail_wizard__body
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_campaign_wizard__mail_body
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_send_mail__body_html
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_send_sms__body_plaintext
msgid "Body"
msgstr "Gövde"

#. module: hr_referral
#: model:hr.referral.onboarding,text:hr_referral.welcome2
msgid ""
"Browse through open job positions, promote them on social media, or refer "
"friends."
msgstr ""

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_job__utm_campaign_id
msgid "Campaign"
msgstr "Kampanya"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.hr_referral_alert_mail_wizard_view_form
#: model_terms:ir.ui.view,arch_db:hr_referral.hr_referral_campaign_wizard_view_form
#: model_terms:ir.ui.view,arch_db:hr_referral.hr_referral_send_mail_view_form
#: model_terms:ir.ui.view,arch_db:hr_referral.hr_referral_send_sms_view_form
msgid "Cancel"
msgstr "İptal"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_link_to_share__channel
msgid "Channel"
msgstr "Kanal"

#. module: hr_referral
#. odoo-javascript
#: code:addons/hr_referral/static/src/views/dashboard.xml:0
msgid "Choose an avatar for your new friend!"
msgstr "Yeni arkadaşınız için bir avatar seçin!"

#. module: hr_referral
#. odoo-javascript
#: code:addons/hr_referral/static/src/views/dashboard.xml:0
msgid "Click to level up!"
msgstr "Seviye atlamak için tıklayın!"

#. module: hr_referral
#. odoo-javascript
#: code:addons/hr_referral/static/src/widgets/copy_referral_link_button.xml:0
msgid "Click(s)"
msgstr ""

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.hr_referral_link_to_share_view_form
msgid "Close"
msgstr "Kapat"

#. module: hr_referral
#: model:hr.referral.onboarding,text:hr_referral.welcome3
msgid "Collect points and exchange them for awesome gifts in the shop."
msgstr ""

#. module: hr_referral
#: model:ir.model,name:hr_referral.model_res_company
msgid "Companies"
msgstr "Şirketler"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_alert__company_id
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_onboarding__company_id
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_points__company_id
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_report__company_id
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__company_id
#: model_terms:ir.ui.view,arch_db:hr_referral.employee_referral_report_view_search
msgid "Company"
msgstr "Firma"

#. module: hr_referral
#: model:hr.referral.onboarding,text:hr_referral.welcome4
msgid "Compete against your colleagues to build the best justice league!"
msgstr ""

#. module: hr_referral
#: model:ir.model,name:hr_referral.model_res_config_settings
msgid "Config Settings"
msgstr "Yapılandırma Ayarları"

#. module: hr_referral
#: model:ir.ui.menu,name:hr_referral.menu_hr_referral_configuration
msgid "Configuration"
msgstr "Ayarlar"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_campaign_wizard__target
msgid "Contacted Employees"
msgstr ""

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__cost
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_referral_reward_form
msgid "Cost"
msgstr "Maliyet"

#. module: hr_referral
#: model_terms:ir.actions.act_window,help:hr_referral.action_hr_referral_friend_configuration
msgid "Create a new friend"
msgstr ""

#. module: hr_referral
#: model_terms:ir.actions.act_window,help:hr_referral.action_hr_referral_level_configuration
msgid "Create a new level"
msgstr ""

#. module: hr_referral
#: model_terms:ir.actions.act_window,help:hr_referral.action_hr_referral_alert_configuration
msgid "Create new alerts"
msgstr "Yeni uyarı oluştur"

#. module: hr_referral
#: model_terms:ir.actions.act_window,help:hr_referral.action_hr_referral_points
msgid "Create new reward points"
msgstr ""

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_alert__create_uid
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_alert_mail_wizard__create_uid
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_campaign_wizard__create_uid
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_friend__create_uid
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_level__create_uid
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_link_to_share__create_uid
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_onboarding__create_uid
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_points__create_uid
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__create_uid
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_send_mail__create_uid
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_send_sms__create_uid
msgid "Created by"
msgstr "Tarafından oluşturuldu"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_alert__create_date
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_alert_mail_wizard__create_date
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_campaign_wizard__create_date
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_friend__create_date
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_level__create_date
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_link_to_share__create_date
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_onboarding__create_date
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_points__create_date
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__create_date
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_send_mail__create_date
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_send_sms__create_date
msgid "Created on"
msgstr "Oluşturuldu"

#. module: hr_referral
#. odoo-javascript
#: code:addons/hr_referral/static/src/views/dashboard.js:0
#: model:ir.actions.client,name:hr_referral.action_hr_referral_welcome_screen
#: model:ir.ui.menu,name:hr_referral.menu_hr_applicant_employee_referral_dashboard
msgid "Dashboard"
msgstr "Kontrol Paneli"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_friend__image
msgid "Dashboard Image"
msgstr "Panel Resmi"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.employee_referral_report_view_search
msgid "Date"
msgstr "Tarih"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_alert__date_from
msgid "Date From"
msgstr "Tarihi Itibaren"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_alert__date_to
msgid "Date To"
msgstr "Date To"

#. module: hr_referral
#: model:ir.model.fields,help:hr_referral.field_hr_referral_friend__position
msgid ""
"Define the position of the friend. If it's a small friend like a dog, you "
"must select Front, it will be placed in the front of the dashboard, above "
"superhero."
msgstr ""
"Arkadaşın konumunu tanımlayın. Eğer köpek gibi küçük bir arkadaşsa, Ön kısmı"
" seçmelisiniz; gösterge panelinin ön tarafına, süper kahramanın üzerine "
"yerleştirilecektir."

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_report__department_id
#: model_terms:ir.ui.view,arch_db:hr_referral.employee_referral_report_view_search
msgid "Department"
msgstr "Departman"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__description
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_referral_reward_form
msgid "Description"
msgstr "Açıklama"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_job__direct_clicks
msgid "Direct Clicks"
msgstr "Doğrudan Tıklamalar"

#. module: hr_referral
#. odoo-python
#: code:addons/hr_referral/report/hr_referral_report.py:0
msgid "Direct Referral"
msgstr "Doğrudan Yönlendirme"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_alert__dismissed_user_ids
msgid "Dismissed User"
msgstr "Kapatılan Kullanıcı"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_alert__display_name
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_alert_mail_wizard__display_name
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_campaign_wizard__display_name
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_friend__display_name
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_level__display_name
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_link_to_share__display_name
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_onboarding__display_name
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_points__display_name
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_report__display_name
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__display_name
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_send_mail__display_name
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_send_sms__display_name
msgid "Display Name"
msgstr "İsim Göster"

#. module: hr_referral
#. odoo-python
#: code:addons/hr_referral/wizard/hr_referral_send_mail.py:0
msgid "Do not have access"
msgstr "Erişim yok"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_referral_reward_kanban
msgid ""
"Do you want to confirm this reward? After confirmation an HR will contact "
"you."
msgstr ""
"Bu ödülü onaylamak ister misiniz? Onaydan sonra bir İK sorumlusu sizinle "
"iletişime geçecektir."

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_applicant__earned_points
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_report__earned_points
msgid "Earned Points"
msgstr "Kazanılan Puanlar"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_send_mail__email_to
#: model:ir.model.fields.selection,name:hr_referral.selection__hr_referral_campaign_wizard__sending_method__work_email
msgid "Email"
msgstr "E-Posta"

#. module: hr_referral
#. odoo-javascript
#: code:addons/hr_referral/static/src/views/dashboard.xml:0
msgid "Email a friend"
msgstr "Arkadaşına e-posta gönder"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.employee_referral_report_view_search
msgid "Employee"
msgstr "Çalışan"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_report__employee_referral_hired
msgid "Employee Referral Hired"
msgstr "İşe Alınan Personel Yönlendirmesi"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_report__employee_referral_refused
msgid "Employee Referral Refused"
msgstr "Yönlendirme ile Gelen Personel Reddedildi"

#. module: hr_referral
#: model:ir.model,name:hr_referral.model_hr_referral_report
msgid "Employee Referral Report"
msgstr "Yönlendirme ile Gelen Personel Raporu"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_referral_reward_form
msgid "Employees"
msgstr "Çalışanlar"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.employee_referral_report_view_graph
#: model_terms:ir.ui.view,arch_db:hr_referral.employee_referral_report_view_pivot
#: model_terms:ir.ui.view,arch_db:hr_referral.employee_referral_report_view_search
#: model_terms:ir.ui.view,arch_db:hr_referral.hr_referral_report_view_form
#: model_terms:ir.ui.view,arch_db:hr_referral.hr_referral_report_view_tree
msgid "Employees Analysis"
msgstr "Personel Analizi"

#. module: hr_referral
#: model:ir.actions.act_window,name:hr_referral.employee_referral_report_action
msgid "Employees Referral Analysis"
msgstr "Yönlendirme ile Gelen Personel Analizi"

#. module: hr_referral
#. odoo-javascript
#: code:addons/hr_referral/static/src/widgets/copy_referral_link_button.js:0
msgid "Error while copying the Referral link: %s to clipboard"
msgstr ""

#. module: hr_referral
#: model:ir.model.fields,help:hr_referral.field_hr_referral_alert__url
msgid ""
"External links must start with 'http://www.'. For an internal url, you don't"
" need to put domain name, you can just insert the path."
msgstr ""
"Harici bağlantılar 'http: // www.' İle başlamalıdır. Dahili bir URL için "
"alan adı girmenize gerek yoktur, sadece yolu ekleyebilirsiniz."

#. module: hr_referral
#: model:ir.model.fields.selection,name:hr_referral.selection__hr_referral_link_to_share__channel__facebook
msgid "Facebook"
msgstr "Facebook"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_job__facebook_clicks
msgid "Facebook Clicks"
msgstr "Facebook Tıklamaları"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__message_follower_ids
msgid "Followers"
msgstr "Takipçiler"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__message_partner_ids
msgid "Followers (Partners)"
msgstr "Takipçiler (İş ortakları)"

#. module: hr_referral
#: model:ir.model.fields,help:hr_referral.field_hr_referral_reward__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Yazı tipi harika simgesi ör. fa-görevler"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_referral_onboarding_tree
msgid "Force Onboarding"
msgstr ""

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_applicant__friend_id
msgid "Friend"
msgstr "Arkadaş"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_friend__name
msgid "Friend Name"
msgstr "Arkadaş İsmi"

#. module: hr_referral
#: model:ir.actions.act_window,name:hr_referral.action_hr_referral_friend_configuration
#: model:ir.ui.menu,name:hr_referral.menu_hr_referral_friend_configuration
msgid "Friends"
msgstr "Arkadaşlar"

#. module: hr_referral
#: model:ir.model,name:hr_referral.model_hr_referral_friend
msgid "Friends for Referrals"
msgstr "Yönlendirmeler için Arkadaşlar"

#. module: hr_referral
#: model:ir.model.fields.selection,name:hr_referral.selection__hr_referral_friend__position__front
msgid "Front"
msgstr "Ön"

#. module: hr_referral
#. odoo-javascript
#: code:addons/hr_referral/static/src/views/dashboard.xml:0
msgid "Gather your team"
msgstr "Ekibinizi toplayın"

#. module: hr_referral
#. odoo-javascript
#: code:addons/hr_referral/static/src/views/dashboard.xml:0
msgid "Gather your team!"
msgstr "Takımını topla!"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__gift_manager_id
msgid "Gift Responsible"
msgstr "Hediye Sorumlusu"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_referral_points_filter
msgid "Gifts"
msgstr "Hediyeler"

#. module: hr_referral
#: model:ir.model.fields.selection,name:hr_referral.selection__hr_referral_alert__onclick__all_jobs
msgid "Go to All Jobs"
msgstr "Tüm İşlere Git"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.employee_referral_report_view_search
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_referral_points_filter
msgid "Group By"
msgstr "Grupla"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.hr_referral_alert_view_search
msgid "HR Referral Alert Search"
msgstr "İK Yönlendirme Uyarısı Arama"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__has_message
msgid "Has Message"
msgstr "Mesaj Var"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_recruitment_report__has_referrer
msgid "Has Referrer"
msgstr "Yönlendirme vardır"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.referral_campaign_email_body_template
#: model_terms:ir.ui.view,arch_db:hr_referral.referral_campaign_sms_template
msgid "Hello"
msgstr "Merhaba"

#. module: hr_referral
#. odoo-python
#: code:addons/hr_referral/wizard/hr_referral_send_sms.py:0
msgid ""
"Hello! There is an amazing job offer for %(job_name)s in my company! It will"
" be a fit for you %(referral_url)s"
msgstr ""

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.referral_email_body_template
msgid ""
"Hello,<br/><br/>\n"
"                There are some amazing job offers in my company! Have a look, they can be interesting for you:"
msgstr ""

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.referral_email_body_template
msgid ""
"Hello,<br/><br/>\n"
"                There is an amazing job offer for a"
msgstr ""

#. module: hr_referral
#: model:ir.model.fields.selection,name:hr_referral.selection__hr_applicant__referral_state__hired
#: model:ir.model.fields.selection,name:hr_referral.selection__hr_referral_report__referral_state__hired
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_applicant_employee_referral_kanban
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_referral_filter
msgid "Hired"
msgstr "İşe Alınan"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_res_users__hr_referral_level_id
msgid "Hr Referral Level"
msgstr "İk Yönlendirme Seviyesi"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_res_users__hr_referral_onboarding_page
msgid "Hr Referral Onboarding Page"
msgstr "İk Yönlendirme Giriş Sayfası"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_alert__id
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_alert_mail_wizard__id
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_campaign_wizard__id
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_friend__id
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_level__id
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_link_to_share__id
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_onboarding__id
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_points__id
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_report__id
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__id
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_send_mail__id
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_send_sms__id
msgid "ID"
msgstr "ID"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__activity_exception_icon
msgid "Icon"
msgstr "Simge"

#. module: hr_referral
#: model:ir.model.fields,help:hr_referral.field_hr_referral_reward__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "İstisnai bir etkinliği belirtmek için kullanılan simge."

#. module: hr_referral
#: model:ir.model.fields,help:hr_referral.field_hr_referral_reward__message_needaction
msgid "If checked, new messages require your attention."
msgstr "İşaretliyse, yeni mesajlar dikkatinize sunulacak."

#. module: hr_referral
#: model:ir.model.fields,help:hr_referral.field_hr_referral_reward__message_has_error
#: model:ir.model.fields,help:hr_referral.field_hr_referral_reward__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "İşaretliyse, bazı mesajlar gönderi hatası içermektedir."

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_friend__image_head
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_level__image
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_onboarding__image
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__image
msgid "Image"
msgstr "Görsel"

#. module: hr_referral
#: model:ir.model.fields.selection,name:hr_referral.selection__hr_referral_report__referral_state__progress
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_referral_filter
msgid "In Progress"
msgstr "İşlemde"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_applicant__is_accessible_to_current_user
msgid "Is Accessible To Current User"
msgstr "Geçerli Kullanıcı Tarafından Erişilebilir"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__message_is_follower
msgid "Is Follower"
msgstr "Takipçi mi"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_link_to_share__job_id
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_report__job_id
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_send_mail__job_id
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_send_sms__job_id
#: model_terms:ir.ui.view,arch_db:hr_referral.employee_referral_report_view_search
msgid "Job"
msgstr "İş"

#. module: hr_referral
#. odoo-python
#: code:addons/hr_referral/wizard/hr_referral_campaign_wizard.py:0
msgid "Job Offer for a %(job_title)s at %(company_name)s"
msgstr ""

#. module: hr_referral
#: model:ir.model,name:hr_referral.model_hr_job
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_campaign_wizard__job_id
msgid "Job Position"
msgstr "İş Pozisyonu"

#. module: hr_referral
#: model:ir.actions.act_window,name:hr_referral.action_hr_job_employee_referral
msgid "Job Positions"
msgstr "İş Pozisyonları"

#. module: hr_referral
#. odoo-javascript
#: code:addons/hr_referral/static/src/views/dashboard.xml:0
msgid "Job Referral Program"
msgstr "İş Yönlendirme Programı"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_job__job_open_date
msgid "Job Start Recruitment Date"
msgstr "İşe Başlama İşe Alım Tarihi"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_report__write_date
msgid "Last Update Date"
msgstr "Son Güncelleme"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_alert__write_uid
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_alert_mail_wizard__write_uid
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_campaign_wizard__write_uid
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_friend__write_uid
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_level__write_uid
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_link_to_share__write_uid
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_onboarding__write_uid
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_points__write_uid
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__write_uid
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_send_mail__write_uid
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_send_sms__write_uid
msgid "Last Updated by"
msgstr "Son Güncelleyen"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_alert__write_date
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_alert_mail_wizard__write_date
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_campaign_wizard__write_date
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_friend__write_date
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_level__write_date
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_link_to_share__write_date
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_onboarding__write_date
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_points__write_date
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__write_date
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_send_mail__write_date
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_send_sms__write_date
msgid "Last Updated on"
msgstr "Son Güncelleme"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_applicant__last_valuable_stage_id
msgid "Last Valuable Stage"
msgstr "Son Değerli Aşama"

#. module: hr_referral
#: model_terms:ir.actions.act_window,help:hr_referral.action_hr_referral_reward_configuration
msgid "Let's create Super Rewards to thank your employees."
msgstr "Çalışanlarınıza teşekkür etmek için Süper Ödüller oluşturalım."

#. module: hr_referral
#: model_terms:ir.actions.act_window,help:hr_referral.action_hr_referral_reward
msgid "Let's create super Rewards to thank<br>your employees."
msgstr "Teşekkür etmek için süper ödüller oluşturalım<br>çalışanlarınız."

#. module: hr_referral
#: model_terms:ir.actions.act_window,help:hr_referral.action_hr_applicant_employee_referral
#: model_terms:ir.actions.act_window,help:hr_referral.action_hr_refused_applicant_employee_referral
msgid "Let's share a job position."
msgstr "Bir iş pozisyonunu paylaşalım."

#. module: hr_referral
#. odoo-javascript
#: code:addons/hr_referral/static/src/views/dashboard.xml:0
msgid "Level"
msgstr "Seviye"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_level__name
msgid "Level Name"
msgstr "Düzey Adı"

#. module: hr_referral
#: model:ir.model,name:hr_referral.model_hr_referral_level
msgid "Level for referrals"
msgstr "Yönlendirmeler için seviye"

#. module: hr_referral
#: model:ir.actions.act_window,name:hr_referral.action_hr_referral_level_configuration
#: model:ir.ui.menu,name:hr_referral.menu_hr_referral_level_configuration
msgid "Levels"
msgstr "Seviyeleri"

#. module: hr_referral
#: model:ir.model.fields.selection,name:hr_referral.selection__hr_referral_link_to_share__channel__direct
msgid "Link"
msgstr "Bağlantı"

#. module: hr_referral
#: model:ir.actions.act_window,name:hr_referral.hr_referral_link_to_share_action
#: model_terms:ir.ui.view,arch_db:hr_referral.hr_referral_link_to_share_view_form
msgid "Link to Share"
msgstr "Paylaşma Linki"

#. module: hr_referral
#. odoo-javascript
#: code:addons/hr_referral/static/src/widgets/copy_referral_link_button.xml:0
msgid "Link to share"
msgstr ""

#. module: hr_referral
#: model:ir.model.fields.selection,name:hr_referral.selection__hr_referral_link_to_share__channel__linkedin
msgid "Linkedin"
msgstr "Linkedin"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_job__linkedin_clicks
msgid "Linkedin Clicks"
msgstr "Linkedin tıklanma"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.hr_referral_campaign_wizard_view_form
msgid "Mail"
msgstr "E-Posta"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_applicant__max_points
#: model:ir.model.fields,field_description:hr_referral.field_hr_job__max_points
msgid "Max Points"
msgstr "Maksimum Puan"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_report__medium_id
msgid "Medium"
msgstr "Aracı:"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__message_has_error
msgid "Message Delivery error"
msgstr "Mesaj Teslim hatası"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__message_ids
msgid "Messages"
msgstr "Mesajlar"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Aktivite Son Tarihim"

#. module: hr_referral
#: model:ir.actions.act_window,name:hr_referral.action_hr_applicant_employee_referral
#: model:ir.actions.act_window,name:hr_referral.action_hr_refused_applicant_employee_referral
msgid "My Referral"
msgstr "Yönlendirmem"

#. module: hr_referral
#. odoo-python
#: code:addons/hr_referral/wizard/hr_referral_alert_mail_wizard.py:0
msgid "New Alert In Referrals App"
msgstr "Yönlendirmeler Uygulamasında Yeni Uyarı"

#. module: hr_referral
#. odoo-python
#: code:addons/hr_referral/models/hr_referral_reward.py:0
msgid "New gift awarded for %s"
msgstr "Yeni hediye için ödüllendirildi %s"

#. module: hr_referral
#. odoo-javascript
#: code:addons/hr_referral/static/src/views/dashboard.xml:0
msgid "Next"
msgstr "Sonraki"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Sonraki Aktivite Takvimi Etkinliği"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Sonraki Aktivite Son Tarihi"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__activity_summary
msgid "Next Activity Summary"
msgstr "Sonraki Aktivite Özeti"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__activity_type_id
msgid "Next Activity Type"
msgstr "Sonraki Aktivitie Türü"

#. module: hr_referral
#: model_terms:ir.actions.act_window,help:hr_referral.employee_referral_report_action
msgid "No data to display"
msgstr "Gösterilecek veri yok"

#. module: hr_referral
#: model_terms:ir.actions.act_window,help:hr_referral.action_hr_job_employee_referral
msgid "No job positions are available to share."
msgstr "Paylaşılacak iş pozisyonu yok."

#. module: hr_referral
#. odoo-python
#: code:addons/hr_referral/wizard/hr_referral_campaign_wizard.py:0
msgid "No users to send the campaign to. Please adapt your target."
msgstr ""

#. module: hr_referral
#: model:ir.model.fields.selection,name:hr_referral.selection__hr_referral_alert__onclick__no
msgid "Not Clickable"
msgstr "Tıklanabilir Değil"

#. module: hr_referral
#: model:ir.model.fields.selection,name:hr_referral.selection__hr_applicant__referral_state__closed
#: model:ir.model.fields.selection,name:hr_referral.selection__hr_referral_report__referral_state__closed
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_applicant_employee_referral_kanban
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_referral_filter
msgid "Not Hired"
msgstr "İşe Alınmadı"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__message_needaction_counter
msgid "Number of Actions"
msgstr "Aksiyon Sayısı"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__message_has_error_counter
msgid "Number of errors"
msgstr "Hata adedi"

#. module: hr_referral
#: model:ir.model.fields,help:hr_referral.field_hr_referral_reward__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "İşlem gerektiren mesaj sayısı"

#. module: hr_referral
#: model:ir.model.fields,help:hr_referral.field_hr_referral_reward__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Teslimat hatası olan mesaj adedi"

#. module: hr_referral
#: model:hr.referral.onboarding,text:hr_referral.welcome1
msgid ""
"Oh no!\n"
"Villains are lurking the city!\n"
"Help us recruit a team of superheroes to save the day!"
msgstr ""

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_alert__onclick
msgid "On Click"
msgstr "Tıklamada"

#. module: hr_referral
#: model:ir.actions.act_window,name:hr_referral.action_hr_referral_onboarding_configuration
#: model:ir.ui.menu,name:hr_referral.menu_hr_referral_onboarding_configuration
msgid "Onboarding"
msgstr "İşe Alım"

#. module: hr_referral
#. odoo-javascript
#: code:addons/hr_referral/static/src/views/dashboard.xml:0
#: model:ir.model.fields.selection,name:hr_referral.selection__hr_applicant__referral_state__progress
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_applicant_employee_referral_kanban
msgid "Ongoing"
msgstr "Devam eden"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_job_employee_referral_kanban
msgid "Open Position"
msgstr ""

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_job_employee_referral_kanban
msgid "Open Positions"
msgstr ""

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.referral_campaign_email_body_template
msgid "Our company is hiring for a"
msgstr ""

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_points__applicant_name
msgid "Partner Name"
msgstr "İş Ortağı Adı"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_referral_reward_kanban
msgid "Point icon"
msgstr "Nokta simgesi"

#. module: hr_referral
#: model:ir.actions.act_window,name:hr_referral.action_hr_referral_points
#: model:ir.model.fields,field_description:hr_referral.field_hr_recruitment_stage__points
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_level__points
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_points__points
#: model:ir.ui.menu,name:hr_referral.menu_hr_points_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_applicant_employee_referral_kanban
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_job_employee_referral_kanban
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_referral_points_filter
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_referral_reward_backend_kanban
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_referral_reward_kanban
msgid "Points"
msgstr "Puanlar"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_report__points_not_hired
msgid "Points Given For Not Hired"
msgstr "İşe Alınmaması İçin Verilen Puanlar"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__points_missing
msgid "Points Missing"
msgstr "Puan Eksik"

#. module: hr_referral
#. odoo-javascript
#: code:addons/hr_referral/static/src/views/dashboard.xml:0
msgid "Points Received"
msgstr ""

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_job_employee_referral_kanban
msgid "Points icon"
msgstr "Puanlar simgesi"

#. module: hr_referral
#: model:ir.model,name:hr_referral.model_hr_referral_points
msgid "Points line for referrals"
msgstr "Yönlendirmeler için puan satırı"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_referral_reward_kanban
msgid "Points to buy this"
msgstr "Bunu satın almak için puanlar"

#. module: hr_referral
#. odoo-javascript
#: code:addons/hr_referral/static/src/views/dashboard.xml:0
msgid "Points to spend"
msgstr ""

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_friend__position
msgid "Position"
msgstr "Pozisyon"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_referral_reward_kanban
msgid "Product"
msgstr "Ürün"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__name
msgid "Product Name"
msgstr "Ürün Adı"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.hr_referral_campaign_wizard_view_form
msgid "Publish & Send"
msgstr "Yayınla ve Gönder"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__rating_ids
msgid "Ratings"
msgstr "Değerlendirmeler"

#. module: hr_referral
#: model_terms:ir.actions.act_window,help:hr_referral.action_hr_applicant_employee_referral
#: model_terms:ir.actions.act_window,help:hr_referral.action_hr_refused_applicant_employee_referral
msgid "Ready to receive points?"
msgstr "Puan almaya hazır mısınız?"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_send_sms__recipient
msgid "Recipient"
msgstr "Alıcı"

#. module: hr_referral
#: model:ir.model,name:hr_referral.model_hr_recruitment_report
msgid "Recruitment Analysis Report"
msgstr "İşe Alım Analiz Raporu"

#. module: hr_referral
#: model:ir.model,name:hr_referral.model_hr_recruitment_stage
msgid "Recruitment Stages"
msgstr "İşe Alım Aşamaları"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.hr_referral_report_view_form
#: model_terms:ir.ui.view,arch_db:hr_referral.hr_referral_report_view_tree
msgid "Referer"
msgstr "Yönlendiren"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_referral_points_filter
msgid "Referral"
msgstr "Yönlendirme"

#. module: hr_referral
#. odoo-python
#: code:addons/hr_referral/models/hr_job.py:0
msgid "Referral %(user)s: %(job_url)s"
msgstr ""

#. module: hr_referral
#: model:ir.model,name:hr_referral.model_hr_referral_alert_mail_wizard
msgid "Referral Alert Mail Wizard"
msgstr "Yönlendirme Uyarısı Posta Sihirbazı"

#. module: hr_referral
#: model:ir.ui.menu,name:hr_referral.menu_report_employee_referral_all
msgid "Referral Analysis"
msgstr ""

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_res_company__hr_referral_background
#: model:ir.model.fields,field_description:hr_referral.field_res_config_settings__hr_referral_background
msgid "Referral Background"
msgstr ""

#. module: hr_referral
#: model:ir.actions.server,name:hr_referral.action_hr_job_launch_referral_campaign
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_job_kanban_inherit_referral
msgid "Referral Campaign"
msgstr ""

#. module: hr_referral
#: model:ir.model,name:hr_referral.model_hr_referral_campaign_wizard
msgid "Referral Campaign Wizard"
msgstr ""

#. module: hr_referral
#. odoo-python
#: code:addons/hr_referral/models/hr_job.py:0
msgid "Referral Campaign for %(job)s"
msgstr ""

#. module: hr_referral
#: model:ir.model,name:hr_referral.model_hr_referral_link_to_share
msgid "Referral Link To Share"
msgstr "Paylaşmak için Yönlendirme Bağlantısı"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_res_users__referral_point_ids
msgid "Referral Point"
msgstr "Yönlendirme Puanı"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_applicant__referral_points_ids
msgid "Referral Points"
msgstr "Yönlendirme Puanları"

#. module: hr_referral
#: model:res.groups,name:hr_referral.group_hr_referral_reward_responsible_user
msgid "Referral Reward Responsible User"
msgstr "Yönlendirme Ödülü Sorumlu Kullanıcı"

#. module: hr_referral
#: model:ir.model,name:hr_referral.model_hr_referral_send_mail
msgid "Referral Send Mail"
msgstr "Yönlendirme E-Postası Gönder"

#. module: hr_referral
#: model:ir.model,name:hr_referral.model_hr_referral_send_sms
msgid "Referral Send sms"
msgstr ""

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_applicant__referral_state
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_report__referral_state
msgid "Referral State"
msgstr "Yönlendirme Durumu"

#. module: hr_referral
#. odoo-javascript
#: code:addons/hr_referral/static/src/widgets/copy_referral_link_button.js:0
msgid "Referral link: %s has been copied to clipboard"
msgstr ""

#. module: hr_referral
#. odoo-python
#: code:addons/hr_referral/wizard/hr_referral_link_to_share.py:0
msgid "Referral: %(name)s"
msgstr "Yönlendirme: %(name)s"

#. module: hr_referral
#. odoo-python
#: code:addons/hr_referral/models/hr_applicant.py:0
msgid "Referral: %(partner)s (%(applicant)s)"
msgstr "Yönlendirme: %(partner)s (%(applicant)s)"

#. module: hr_referral
#. odoo-python
#: code:addons/hr_referral/wizard/hr_referral_link_to_share.py:0
msgid "Referral: %(url)s"
msgstr "Yönlendirme: %(url)s"

#. module: hr_referral
#. odoo-python
#: code:addons/hr_referral/models/hr_applicant.py:0
msgid "Referral: %s"
msgstr "Yönlendirme: %s"

#. module: hr_referral
#. odoo-javascript
#: code:addons/hr_referral/static/src/views/dashboard.xml:0
#: model:ir.ui.menu,name:hr_referral.menu_hr_referral_root
#: model_terms:ir.ui.view,arch_db:hr_referral.res_config_settings_view__form
msgid "Referrals"
msgstr "Yönlendirmeler"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.hr_applicant_view_search_bis_inherit_referral
msgid "Referred By"
msgstr "Yönlendiren"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_applicant__ref_user_id
msgid "Referred By User"
msgstr "Kullanıcı Tarafından Yönlendirilen"

#. module: hr_referral
#: model:ir.ui.menu,name:hr_referral.menu_hr_referral_reporting
msgid "Reporting"
msgstr "Raporlama"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_referral_level_form
msgid "Requirements"
msgstr "Gereksinimler"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__activity_user_id
msgid "Responsible User"
msgstr "Sorumlu Kullanıcı"

#. module: hr_referral
#. odoo-javascript
#: code:addons/hr_referral/static/src/views/dashboard.xml:0
msgid "Restart Onboarding"
msgstr "İşe Alımı Yeniden Başlat"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.res_config_settings_view__form
msgid "Restore Default"
msgstr ""

#. module: hr_referral
#. odoo-javascript
#: code:addons/hr_referral/static/src/views/dashboard.xml:0
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_points__hr_referral_reward_id
msgid "Reward"
msgstr "Ödül"

#. module: hr_referral
#: model:ir.model,name:hr_referral.model_hr_referral_reward
msgid "Reward for Referrals"
msgstr "Yönlendirmeler için Ödül"

#. module: hr_referral
#. odoo-javascript
#: code:addons/hr_referral/static/src/views/dashboard.xml:0
#: model:ir.actions.act_window,name:hr_referral.action_hr_referral_reward
#: model:ir.actions.act_window,name:hr_referral.action_hr_referral_reward_configuration
#: model:ir.ui.menu,name:hr_referral.menu_hr_referral_reward_configuration
msgid "Rewards"
msgstr "Ödüller"

#. module: hr_referral
#: model:ir.model.fields.selection,name:hr_referral.selection__hr_referral_campaign_wizard__sending_method__work_phone
#: model_terms:ir.ui.view,arch_db:hr_referral.hr_referral_campaign_wizard_view_form
msgid "SMS"
msgstr "SMS"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_campaign_wizard__sms_body
msgid "SMS Content"
msgstr "SMS İçeriği"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__message_has_sms_error
msgid "SMS Delivery error"
msgstr "SMS İleti hatası"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_referral_points_filter
msgid "Search Points / Gifts"
msgstr "Puanlar / Hediyeler Arama"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_referral_filter
msgid "Search Referral"
msgstr "Yönlendirme Ara"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.referral_email_body_template
msgid "See Job Offer"
msgstr ""

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.hr_referral_campaign_wizard_view_form
msgid "Select employees"
msgstr ""

#. module: hr_referral
#: model:ir.model.fields.selection,name:hr_referral.selection__hr_referral_campaign_wizard__target__selection
msgid "Selection"
msgstr "Seçim"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.hr_referral_alert_mail_wizard_view_form
#: model_terms:ir.ui.view,arch_db:hr_referral.hr_referral_campaign_wizard_view_form
msgid "Send"
msgstr "Gönder"

#. module: hr_referral
#: model:ir.actions.act_window,name:hr_referral.hr_referral_send_mail_action
msgid "Send Job Offer by Mail"
msgstr "İş Teklifini E-Posta ile Gönder"

#. module: hr_referral
#: model:ir.actions.act_window,name:hr_referral.hr_referral_send_sms_action
msgid "Send Job Offer by SMS"
msgstr ""

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.hr_referral_send_mail_view_form
msgid "Send Job by Mail"
msgstr "İşi E-Posta ile Gönder"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.hr_referral_send_sms_view_form
msgid "Send Job by sms"
msgstr ""

#. module: hr_referral
#: model:ir.actions.act_window,name:hr_referral.hr_referral_alert_mail_wizard_action
#: model_terms:ir.ui.view,arch_db:hr_referral.hr_referral_send_mail_view_form
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_referral_alert_form
msgid "Send Mail"
msgstr "İleti Gönder"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.hr_referral_send_sms_view_form
msgid "Send SMS"
msgstr "SMS Gönder"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_campaign_wizard__sending_method
msgid "Sending Method"
msgstr ""

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_onboarding__sequence
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__sequence
msgid "Sequence"
msgstr "Sıralama"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_points__sequence_stage
msgid "Sequence of stage"
msgstr "Durum sırası"

#. module: hr_referral
#: model:ir.actions.act_window,name:hr_referral.action_hr_referral_configuration
#: model:ir.ui.menu,name:hr_referral.hr_referral_menu_configuration
msgid "Settings"
msgstr "Ayarlar"

#. module: hr_referral
#. odoo-javascript
#: code:addons/hr_referral/static/src/views/dashboard.xml:0
msgid "Share"
msgstr "Paylaş"

#. module: hr_referral
#. odoo-javascript
#: code:addons/hr_referral/static/src/widgets/copy_referral_link_button.xml:0
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_job_employee_referral_kanban
msgid "Share Now"
msgstr "Şimdi Paylaş"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_applicant__shared_item_infos
msgid "Shared Item Infos"
msgstr "Paylaşılan Öğe Bilgileri"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_recruitment_stage__use_in_referral
msgid "Show in Referrals"
msgstr "Yönlendirmelerde Göster"

#. module: hr_referral
#. odoo-javascript
#: code:addons/hr_referral/static/src/views/dashboard.xml:0
msgid "Skip"
msgstr "Atla"

#. module: hr_referral
#. odoo-javascript
#: code:addons/hr_referral/static/src/views/dashboard.xml:0
msgid "Skip and Start"
msgstr "Atla ve Başlat"

#. module: hr_referral
#. odoo-python
#: code:addons/hr_referral/models/hr_applicant.py:0
msgid "Sorry, your referral %s has been refused in the recruitment process."
msgstr ""

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_res_users__utm_source_id
msgid "Source"
msgstr "Kaynak"

#. module: hr_referral
#: model:ir.model.fields.selection,name:hr_referral.selection__hr_referral_alert__onclick__url
msgid "Specify URL"
msgstr "URL belirtin"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_points__stage_id
msgid "Stage"
msgstr "Aşama"

#. module: hr_referral
#. odoo-javascript
#: code:addons/hr_referral/static/src/views/dashboard.xml:0
msgid "Start"
msgstr "Başla"

#. module: hr_referral
#. odoo-javascript
#: code:addons/hr_referral/static/src/views/dashboard.xml:0
msgid "Start Now"
msgstr "Şimdi Başla"

#. module: hr_referral
#: model:ir.model.fields,help:hr_referral.field_hr_referral_reward__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Aktivitelere göre durum \n"
"Gecikmiş\\: Son tarih geçmiş\n"
"Bugün\\: Aktivite tarihi bugün\n"
"Planlanmış\\: Gelecek Aktiviteler."

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_alert_mail_wizard__subject
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_campaign_wizard__mail_subject
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_send_mail__subject
msgid "Subject"
msgstr "Konu"

#. module: hr_referral
#. odoo-javascript
#: code:addons/hr_referral/static/src/views/dashboard.xml:0
msgid "Successful"
msgstr "Başarılı"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_onboarding__text
msgid "Text"
msgstr "Metin"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.referral_campaign_sms_template
msgid "Thanks!"
msgstr ""

#. module: hr_referral
#: model:ir.model.fields,help:hr_referral.field_hr_referral_friend__image
msgid ""
"This field holds the image used as image for the friend on the dashboard, "
"limited to 1024x1024px."
msgstr "Bu alanda 1024x1024px kontrol panelindeki arkadaş görselini tutar."

#. module: hr_referral
#: model:ir.model.fields,help:hr_referral.field_hr_referral_friend__image_head
msgid ""
"This field holds the image used as image for the head's friend when the user"
" must choose a new friend, limited to 1024x1024px."
msgstr ""
"Bu alan, kullanıcının 1024x1024 piksel ile sınırlı yeni bir arkadaş seçmesi "
"gerektiğinde arkadaşı için görüntü olarak kullanılan görüntüyü tutar."

#. module: hr_referral
#: model:ir.model.fields,help:hr_referral.field_hr_referral_reward__image
msgid ""
"This field holds the image used as image for the product, limited to "
"1024x1024px."
msgstr ""
"Bu alan, resimi ürünün resimi olarak tutar, 1024x1024px ile "
"sınırlandırılmıştır."

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.hr_referral_campaign_wizard_view_form
msgid ""
"This job position is not published. \n"
"                    The referral campaign will automatically publish it."
msgstr ""

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.hr_referral_link_to_share_view_form
msgid ""
"This link contains a tracker so that people clicking on it will account to a"
" referral for you, even if they apply on a position after a few days."
msgstr ""
"Bu bağlantı bir izleyici içerir, böylece onu tıklayan insanlar birkaç gün "
"sonra bir pozisyona başvursalar bile sizin için bir yönlendirme olarak "
"hesaplanacaktır."

#. module: hr_referral
#: model:ir.model.fields,help:hr_referral.field_hr_recruitment_stage__use_in_referral
msgid ""
"This option is used in app 'Referrals'. If checked, the stage is displayed "
"in 'Referrals Dashboard' and points are given to the employee."
msgstr ""
"Bu seçenek 'Yönlendirmeler' uygulamasında kullanılır. İşaretlenirse, aşama "
"'Yönlendirmeler Kontrol Paneli'nde görüntülenir ve çalışana puan verilir."

#. module: hr_referral
#: model_terms:ir.actions.act_window,help:hr_referral.employee_referral_report_action
msgid "This reports allow you to follow the referrals and their evolution."
msgstr ""

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_referral_onboarding_tree
msgid ""
"This will make the onboarding visible to all employees who have already seen"
" it. Do you want to continue?"
msgstr ""

#. module: hr_referral
#. odoo-javascript
#: code:addons/hr_referral/static/src/views/dashboard.xml:0
msgid "To Spend"
msgstr "Harcamak İçin"

#. module: hr_referral
#. odoo-javascript
#: code:addons/hr_referral/static/src/views/dashboard.xml:0
msgid "Total"
msgstr "Toplam"

#. module: hr_referral
#: model:ir.model.fields,help:hr_referral.field_hr_referral_reward__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Kayıtlardaki istisna etkinliğinin türü."

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_alert__url
msgid "URL"
msgstr "URL"

#. module: hr_referral
#: model:ir.model,name:hr_referral.model_utm_campaign
msgid "UTM Campaign"
msgstr "UTM Kampanyası"

#. module: hr_referral
#: model:ir.model,name:hr_referral.model_utm_source
msgid "UTM Source"
msgstr "UTM Kaynak"

#. module: hr_referral
#. odoo-python
#: code:addons/hr_referral/models/hr_applicant.py:0
msgid ""
"Unsupported search on field is_accessible_to_current_user: %(operator)s "
"operator & %(value)s value. Only = and != operator and boolean values are "
"supported."
msgstr ""

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_link_to_share__url
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_send_mail__url
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_send_sms__url
msgid "Url"
msgstr "Url"

#. module: hr_referral
#: model:ir.model,name:hr_referral.model_res_users
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_alert_mail_wizard__user_ids
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_points__ref_user_id
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_report__ref_user_id
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_referral_points_filter
msgid "User"
msgstr "Kullanıcı"

#. module: hr_referral
#: model:ir.model.fields,help:hr_referral.field_hr_referral_reward__gift_manager_id
msgid "User responsible of this gift."
msgstr "Bu hediyeden sorumlu kullanıcı."

#. module: hr_referral
#: model:res.groups,name:hr_referral.group_hr_recruitment_referral_user
msgid "User: Referral only"
msgstr ""

#. module: hr_referral
#. odoo-javascript
#: code:addons/hr_referral/static/src/views/dashboard.xml:0
msgid "View Jobs"
msgstr "İşleri Görüntüle"

#. module: hr_referral
#. odoo-python
#: code:addons/hr_referral/models/hr_job.py:0
msgid "Visit Webpage"
msgstr "Web Sayfasını Ziyareti"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__website_message_ids
msgid "Website Messages"
msgstr "Websitesi Mesajları"

#. module: hr_referral
#: model:ir.model.fields,help:hr_referral.field_hr_referral_reward__website_message_ids
msgid "Website communication history"
msgstr "Websitesi iletişim geçmişi"

#. module: hr_referral
#: model:ir.model,name:hr_referral.model_hr_referral_onboarding
msgid "Welcome Onboarding in Referral App"
msgstr "Yönlendirme Uygulamasında Hoş Geldiniz"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_job__twitter_clicks
msgid "X Clicks"
msgstr ""

#. module: hr_referral
#. odoo-python
#: code:addons/hr_referral/models/hr_applicant.py:0
msgid "You are not allowed to access applicant records."
msgstr "Başvuru sahibi kayıtlarına erişmenize izin verilmez."

#. module: hr_referral
#. odoo-python
#: code:addons/hr_referral/models/hr_applicant.py:0
msgid ""
"You are not allowed to access this application record because you're not one"
" of the interviewers of this application. If you think that's an error, "
"please contact your administrator."
msgstr ""

#. module: hr_referral
#. odoo-python
#: code:addons/hr_referral/models/utm_campaign.py:0
msgid ""
"You cannot delete these UTM Campaigns as they are linked to the following jobs in Referral:\n"
"%(job_names)s"
msgstr ""

#. module: hr_referral
#. odoo-python
#: code:addons/hr_referral/models/utm_source.py:0
msgid ""
"You cannot delete these UTM Sources as they are linked to the following users in Referral:\n"
"%(employee_names)s"
msgstr ""

#. module: hr_referral
#. odoo-python
#: code:addons/hr_referral/models/hr_referral_reward.py:0
msgid ""
"You do not have enough points in this company to buy this product. In this "
"company, you have %s points."
msgstr ""
"Bu şirkette bu ürünü satın almak için yeterli puanınız yok. Bu şirkette %s "
"puanınız var."

#. module: hr_referral
#. odoo-python
#: code:addons/hr_referral/wizard/hr_referral_send_sms.py:0
msgid "You do not have the required access rights to send SMS."
msgstr ""

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_applicant_employee_referral_kanban
msgid "You earned"
msgstr "Kazandın"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_referral_reward_kanban
msgid "You need another"
msgstr "Başka bir şeye ihtiyacın var"

#. module: hr_referral
#. odoo-python
#: code:addons/hr_referral/models/hr_applicant.py:0
msgid "Your referrer got a step further! %(message)s"
msgstr ""

#. module: hr_referral
#. odoo-python
#: code:addons/hr_referral/models/hr_applicant.py:0
msgid "Your referrer is hired! %(message)s"
msgstr "Sizi yönlendiren kişi işe alındı! %(message)s"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_job_employee_referral_kanban
msgid "click(s)"
msgstr "tıklama(lar)"

#. module: hr_referral
#. odoo-javascript
#: code:addons/hr_referral/static/src/views/dashboard.xml:0
msgid "has been hired!"
msgstr "işe alındı!"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_referral_alert_form
msgid "https://www.google.com"
msgstr "https://www.google.com"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.referral_email_body_template
msgid ""
"in my company! It will be a fit for you.\n"
"                <br/>"
msgstr ""

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.referral_campaign_sms_template
msgid "is hiring for a position"
msgstr ""

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.referral_campaign_email_body_template
msgid ""
"position.\n"
"                If you know someone who would be a great fit for this position, please share this link with them:"
msgstr ""

#. module: hr_referral
#. odoo-javascript
#: code:addons/hr_referral/static/src/views/dashboard.xml:0
msgid "reward"
msgstr "ödül"

#. module: hr_referral
#. odoo-javascript
#: code:addons/hr_referral/static/src/views/dashboard.xml:0
msgid "share"
msgstr "paylaş"
