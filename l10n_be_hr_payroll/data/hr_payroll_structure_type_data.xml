<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="structure_type_student" model="hr.payroll.structure.type">
        <field name="name">Belgian Student</field>
        <field name="wage_type">hourly</field>
        <field name="default_resource_calendar_id" ref="l10n_be_hr_payroll.resource_calendar_std_0h"/>
        <field name="country_id" ref="base.be"/>
        <!-- DEFAULT STRUCT ID -->
    </record>
    <record id="hr_contract.structure_type_employee_cp200" model="hr.payroll.structure.type">
        <field name="sequence">1</field>
    </record>
</odoo>
