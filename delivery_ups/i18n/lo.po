# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * delivery_ups
#
# Translators:
# <PERSON> <<EMAIL>>, 2017
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.saas~18+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-05-16 16:01+0000\n"
"PO-Revision-Date: 2017-10-02 11:50+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2017\n"
"Language-Team: Lao (https://www.transifex.com/odoo/teams/41243/lo/)\n"
"Language: lo\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/ups_request.py:0
msgid "A shipment cannot have a KGS/IN or LBS/CM as its unit of measurements. Configure it from the delivery method."
msgstr ""

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/ups_request.py:0
msgid "Access License number is Invalid. Provide a valid number (Length should be 0-35 alphanumeric characters)"
msgstr ""

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/ups_request.py:0
msgid "Access License number is invalid for this delivery provider."
msgstr ""

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/ups_request.py:0
msgid "Access License number is invalid for this provider.Please re-license."
msgstr ""

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/ups_request.py:0
msgid "Access License number is revoked contact UPS to get access."
msgstr ""

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/ups_request.py:0
msgid "Authorization system is currently unavailable , try again later."
msgstr ""

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_delivery_carrier__ups_bill_my_account
#: model:ir.model.fields,field_description:delivery_ups.field_res_partner__bill_my_account
#: model:ir.model.fields,field_description:delivery_ups.field_res_users__bill_my_account
#: model:ir.model.fields,field_description:delivery_ups.field_sale_order__ups_bill_my_account
msgid "Bill My Account"
msgstr ""

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_delivery_carrier__ups_cod_funds_code
msgid "COD Funding Option"
msgstr ""

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/ups_request.py:0
msgid "Cancel shipment not available at this time , Please try again Later."
msgstr ""

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_stock_package_type__package_carrier_type
msgid "Carrier"
msgstr ""

#. module: delivery_ups
#: model:ir.model.fields.selection,name:delivery_ups.selection__delivery_carrier__ups_cod_funds_code__8
msgid "Cashier's Check or MoneyOrder"
msgstr ""

#. module: delivery_ups
#: model:ir.model.fields.selection,name:delivery_ups.selection__delivery_carrier__ups_package_dimension_unit__cm
msgid "Centimeters"
msgstr ""

#. module: delivery_ups
#: model:ir.model.fields.selection,name:delivery_ups.selection__delivery_carrier__ups_cod_funds_code__0
msgid "Check, Cashier's Check or MoneyOrder"
msgstr ""

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_delivery_carrier__ups_cod
msgid "Collect on Delivery"
msgstr ""

#. module: delivery_ups
#: model:ir.model,name:delivery_ups.model_res_partner
msgid "Contact"
msgstr ""

#. module: delivery_ups
#: model_terms:ir.ui.view,arch_db:delivery_ups.view_delivery_carrier_form_with_provider_ups
msgid "Duties paid by"
msgstr ""

#. module: delivery_ups
#: model:ir.model.fields.selection,name:delivery_ups.selection__delivery_carrier__ups_label_file_type__epl
msgid "EPL"
msgstr ""

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/delivery_ups.py:0
msgid ""
"Error:\n"
"%s"
msgstr ""

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/ups_request.py:0
msgid "Exceeds Total Number of allowed pieces per World Wide Express Shipment."
msgstr ""

#. module: delivery_ups
#: model:ir.model.fields,help:delivery_ups.field_delivery_carrier__ups_bill_my_account
#: model:ir.model.fields,help:delivery_ups.field_res_partner__bill_my_account
#: model:ir.model.fields,help:delivery_ups.field_res_users__bill_my_account
#: model:ir.model.fields,help:delivery_ups.field_sale_order__ups_bill_my_account
msgid ""
"If checked, ecommerce users will be prompted their UPS account number\n"
"and delivery fees will be charged on it."
msgstr ""

#. module: delivery_ups
#: model:ir.model.fields.selection,name:delivery_ups.selection__delivery_carrier__ups_package_dimension_unit__in
msgid "Inches"
msgstr ""

#. module: delivery_ups
#: model:ir.model.fields.selection,name:delivery_ups.selection__delivery_carrier__ups_package_weight_unit__kgs
msgid "Kilograms"
msgstr ""

#. module: delivery_ups
#: model_terms:ir.ui.view,arch_db:delivery_ups.view_delivery_carrier_form_with_provider_ups
msgid "Label Format"
msgstr ""

#. module: delivery_ups
#: model_terms:ir.ui.view,arch_db:delivery_ups.view_delivery_carrier_form_with_provider_ups
msgid "Options"
msgstr ""

#. module: delivery_ups
#: model:ir.model.fields.selection,name:delivery_ups.selection__delivery_carrier__ups_label_file_type__gif
msgid "PDF"
msgstr ""

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_delivery_carrier__ups_package_dimension_unit
msgid "Package Size Unit"
msgstr ""

#. module: delivery_ups
#: model_terms:ir.ui.view,arch_db:delivery_ups.view_delivery_carrier_form_with_provider_ups
msgid "Package Weight Unit"
msgstr ""

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/ups_request.py:0
msgid "Packages %s do not have a positive shipping weight."
msgstr ""

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/delivery_ups.py:0
msgid "Packages:"
msgstr ""

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/ups_request.py:0
msgid "Please provide a valid City in the warehouse address."
msgstr ""

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/ups_request.py:0
msgid "Please provide a valid Country in recipient's address."
msgstr ""

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/ups_request.py:0
msgid "Please provide a valid Country in the warehouse address."
msgstr ""

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/ups_request.py:0
msgid "Please provide a valid State in the warehouse address."
msgstr ""

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/ups_request.py:0
msgid "Please provide a valid Zip in the warehouse address."
msgstr ""

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/ups_request.py:0
msgid "Please provide a valid city in the recipient address."
msgstr ""

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/ups_request.py:0
msgid "Please provide a valid city in the shipper's address."
msgstr ""

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/ups_request.py:0
msgid "Please provide a valid country in the shipper's address."
msgstr ""

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/ups_request.py:0
msgid "Please provide a valid package type available for service and selected locations."
msgstr ""

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/ups_request.py:0
msgid "Please provide a valid phone number for the recipient."
msgstr ""

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/ups_request.py:0
msgid "Please provide a valid shipper Number/Carrier Account."
msgstr ""

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/ups_request.py:0
msgid "Please provide a valid shipper number/Carrier Account."
msgstr ""

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/ups_request.py:0
msgid "Please provide a valid shipper phone number."
msgstr ""

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/ups_request.py:0
msgid "Please provide a valid state in the recipient address."
msgstr ""

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/ups_request.py:0
msgid "Please provide a valid state in the shipper's address."
msgstr ""

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/ups_request.py:0
msgid "Please provide a valid street in shipper's address."
msgstr ""

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/ups_request.py:0
msgid "Please provide a valid street in the recipient address."
msgstr ""

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/ups_request.py:0
msgid "Please provide a valid street in the warehouse address."
msgstr ""

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/ups_request.py:0
msgid "Please provide a valid warehouse Phone Number"
msgstr ""

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/ups_request.py:0
msgid "Please provide a valid zip code in the recipient address."
msgstr ""

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/ups_request.py:0
msgid "Please provide a valid zip code in the shipper's address."
msgstr ""

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/ups_request.py:0
msgid "Please provide a valid zip code in the warehouse address."
msgstr ""

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/ups_request.py:0
msgid "Please provide a valid zipcode in the recipient address."
msgstr ""

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/ups_request.py:0
msgid "Please provide at least one item to ship"
msgstr ""

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/ups_request.py:0
msgid "Please provide at least one item to ship."
msgstr ""

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/ups_request.py:0
msgid "Please set a valid country in the recipient address."
msgstr ""

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/ups_request.py:0
msgid "Please set a valid country in the warehouse address."
msgstr ""

#. module: delivery_ups
#: model:ir.model.fields.selection,name:delivery_ups.selection__delivery_carrier__ups_package_weight_unit__lbs
msgid "Pounds"
msgstr ""

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/ups_request.py:0
msgid "Provided Access License Number not found in the UPS database"
msgstr ""

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/ups_request.py:0
msgid "Provided Tracking Ref. Number is invalid."
msgstr ""

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_delivery_carrier__delivery_type
msgid "Provider"
msgstr ""

#. module: delivery_ups
#: model:ir.model.fields.selection,name:delivery_ups.selection__delivery_carrier__ups_duty_payment__recipient
msgid "Recipient"
msgstr ""

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/ups_request.py:0
msgid "Recipient Phone must be at least 10 alphanumeric characters."
msgstr ""

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/ups_request.py:0
msgid "Recipient PhoneExtension cannot exceed the length of 4."
msgstr ""

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/ups_request.py:0
msgid "Recipient PhoneExtension must contain only numbers."
msgstr ""

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/delivery_ups.py:0
msgid "Return label generated"
msgstr ""

#. module: delivery_ups
#: model:ir.model.fields.selection,name:delivery_ups.selection__delivery_carrier__ups_label_file_type__spl
msgid "SPL"
msgstr ""

#. module: delivery_ups
#: model:ir.model,name:delivery_ups.model_sale_order
msgid "Sales Order"
msgstr ""

#. module: delivery_ups
#: model_terms:ir.ui.view,arch_db:delivery_ups.view_delivery_carrier_form_with_provider_ups
msgid "Saturday Delivery"
msgstr ""

#. module: delivery_ups
#: model:ir.model.fields.selection,name:delivery_ups.selection__delivery_carrier__ups_duty_payment__sender
msgid "Sender"
msgstr ""

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/delivery_ups.py:0
msgid "Shipment #%s has been cancelled"
msgstr ""

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/delivery_ups.py:0
msgid "Shipment created into UPS"
msgstr ""

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/ups_request.py:0
msgid "Shipper Phone must be at least 10 alphanumeric characters."
msgstr ""

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/ups_request.py:0
msgid "Shipper number must contain alphanumeric characters only."
msgstr ""

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/ups_request.py:0
msgid "Shipper phone extension cannot exceed the length of 4."
msgstr ""

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/ups_request.py:0
msgid "Shipper phone extension must contain only numbers."
msgstr ""

#. module: delivery_ups
#: model:ir.model,name:delivery_ups.model_delivery_carrier
msgid "Shipping Methods"
msgstr ""

#. module: delivery_ups
#: model:ir.model,name:delivery_ups.model_stock_package_type
msgid "Stock package type"
msgstr ""

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/ups_request.py:0
msgid "The UserId is currently locked out; please try again in 24 hours."
msgstr ""

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/ups_request.py:0
msgid ""
"The address of your company is missing or wrong.\n"
"(Missing field(s) : %s)"
msgstr ""

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/ups_request.py:0
msgid ""
"The address of your warehouse is missing or wrong.\n"
"(Missing field(s) : %s)"
msgstr ""

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/ups_request.py:0
msgid "The delivery cannot be done because the weight of your product is missing."
msgstr ""

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/ups_request.py:0
msgid ""
"The estimated shipping price cannot be computed because the weight is missing for the following product(s): \n"
" %s"
msgstr ""

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/ups_request.py:0
msgid "The maximum number of user access attempts was exceeded. So please try again later"
msgstr ""

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/ups_request.py:0
msgid "The name of the customer should be no more than 35 characters."
msgstr ""

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/ups_request.py:0
msgid ""
"The recipient address is missing or wrong.\n"
"(Missing field(s) : %s)"
msgstr ""

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/ups_request.py:0
msgid "The requested service is unavailable between the selected locations."
msgstr ""

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/ups_request.py:0
msgid "The selected service is invalid from the requested warehouse, please choose another service."
msgstr ""

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/ups_request.py:0
msgid "The selected service is invalid to the recipient address, please choose another service."
msgstr ""

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/ups_request.py:0
msgid "The selected service is not possible from your warehouse to the recipient address, please choose another service."
msgstr ""

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/ups_request.py:0
msgid "The selected service is not valid with the selected packaging."
msgstr ""

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/ups_request.py:0
msgid "This measurement system is not valid for the selected country. Please switch from LBS/IN to KGS/CM (or vice versa). Configure it from delivery method"
msgstr ""

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/ups_request.py:0
msgid "This measurement system is not valid for the selected country. Please switch from LBS/IN to KGS/CM (or vice versa). Configure it from the delivery method."
msgstr ""

#. module: delivery_ups
#: model:ir.model.fields,help:delivery_ups.field_delivery_carrier__ups_cod
msgid "This value added service enables UPS to collect the payment of the shipment from your customer."
msgstr ""

#. module: delivery_ups
#: model:ir.model.fields,help:delivery_ups.field_delivery_carrier__ups_saturday_delivery
msgid "This value added service will allow you to ship the package on saturday also."
msgstr ""

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/delivery_ups.py:0
msgid "Tracking Numbers:"
msgstr ""

#. module: delivery_ups
#: model:ir.model.fields.selection,name:delivery_ups.selection__delivery_carrier__delivery_type__ups
#: model:ir.model.fields.selection,name:delivery_ups.selection__stock_package_type__package_carrier_type__ups
msgid "UPS"
msgstr ""

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_delivery_carrier__ups_access_number
msgid "UPS Access Key"
msgstr ""

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_res_partner__property_ups_carrier_account
#: model:ir.model.fields,field_description:delivery_ups.field_res_users__property_ups_carrier_account
msgid "UPS Account Number"
msgstr ""

#. module: delivery_ups
#: model:delivery.carrier,name:delivery_ups.delivery_carrier_ups_be
#: model:product.template,name:delivery_ups.product_product_delivery_ups_be_product_template
msgid "UPS BE"
msgstr ""

#. module: delivery_ups
#: model_terms:ir.ui.view,arch_db:delivery_ups.view_delivery_carrier_form_with_provider_ups
msgid "UPS Configuration"
msgstr ""

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_delivery_carrier__ups_label_file_type
msgid "UPS Label File Type"
msgstr ""

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_delivery_carrier__ups_default_package_type_id
msgid "UPS Package Type"
msgstr ""

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_delivery_carrier__ups_passwd
msgid "UPS Password"
msgstr ""

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_delivery_carrier__ups_saturday_delivery
msgid "UPS Saturday Delivery"
msgstr ""

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/ups_request.py:0
msgid "UPS Server Not Found"
msgstr ""

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_delivery_carrier__ups_default_service_type
msgid "UPS Service Type"
msgstr ""

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_delivery_carrier__ups_shipper_number
msgid "UPS Shipper Number"
msgstr ""

#. module: delivery_ups
#: model_terms:ir.ui.view,arch_db:delivery_ups.res_config_settings_view_form_sale
#: model_terms:ir.ui.view,arch_db:delivery_ups.res_config_settings_view_form_stock
msgid "UPS Shipping Methods"
msgstr ""

#. module: delivery_ups
#: model:delivery.carrier,name:delivery_ups.delivery_carrier_ups_us
#: model:product.template,name:delivery_ups.product_product_delivery_ups_us_product_template
msgid "UPS US"
msgstr ""

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_delivery_carrier__ups_username
msgid "UPS Username"
msgstr ""

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_sale_order__partner_ups_carrier_account
msgid "UPS account number"
msgstr ""

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/ups_request.py:0
msgid "UPS address lines can only contain a maximum of 35 characters. You can split the contacts addresses on multiple lines to try to avoid this limitation."
msgstr ""

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_delivery_carrier__ups_duty_payment
msgid "Ups Duty Payment"
msgstr ""

#. module: delivery_ups
#: model:ir.model.fields,field_description:delivery_ups.field_delivery_carrier__ups_package_weight_unit
msgid "Ups Package Weight Unit"
msgstr ""

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/ups_request.py:0
msgid "Username/Password is invalid for this delivery provider."
msgstr ""

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/ups_request.py:0
msgid "Warehouse Phone must be at least 10 alphanumeric characters."
msgstr ""

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/ups_request.py:0
msgid "Warehouse Phone must contain only numbers."
msgstr ""

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/ups_request.py:0
msgid "Warehouse PhoneExtension cannot exceed the length of 4."
msgstr ""

#. module: delivery_ups
#. odoo-python
#: code:addons/delivery_ups/models/sale.py:0
msgid "You must enter an UPS account number."
msgstr ""

#. module: delivery_ups
#: model:ir.model.fields.selection,name:delivery_ups.selection__delivery_carrier__ups_label_file_type__zpl
msgid "ZPL"
msgstr ""
