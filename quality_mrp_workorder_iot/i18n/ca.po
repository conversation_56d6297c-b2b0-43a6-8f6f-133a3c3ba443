# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* quality_mrp_workorder_iot
# 
# Translators:
# <PERSON>, 2024
# <PERSON><PERSON><PERSON>, 2024
# <PERSON><PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:28+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Arnau Ros, 2024\n"
"Language-Team: Catalan (https://app.transifex.com/odoo/teams/41243/ca/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ca\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: quality_mrp_workorder_iot
#: model:ir.model.fields,field_description:quality_mrp_workorder_iot.field_iot_trigger__action
msgid "Action"
msgstr "Acció"

#. module: quality_mrp_workorder_iot
#: model:ir.model.fields.selection,name:quality_mrp_workorder_iot.selection__iot_trigger__action__fail
msgid "Fail"
msgstr "Falla"

#. module: quality_mrp_workorder_iot
#: model:ir.model,name:quality_mrp_workorder_iot.model_iot_trigger
msgid "IOT Trigger"
msgstr "Activador IOT"

#. module: quality_mrp_workorder_iot
#: model:ir.model.fields.selection,name:quality_mrp_workorder_iot.selection__iot_trigger__action__pass
msgid "Pass"
msgstr "Passar"

#. module: quality_mrp_workorder_iot
#: model:ir.model.fields.selection,name:quality_mrp_workorder_iot.selection__iot_trigger__action__measure
msgid "Take Measure"
msgstr "Prengui la mesura"
