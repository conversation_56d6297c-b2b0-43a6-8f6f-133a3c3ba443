# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* helpdesk_timesheet
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0beta+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-05-16 16:02+0000\n"
"PO-Revision-Date: 2022-09-22 05:46+0000\n"
"Language-Team: Amharic (https://app.transifex.com/odoo/teams/41243/am/)\n"
"Language: am\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_project_project__ticket_count
msgid "# Tickets"
msgstr ""

#. module: helpdesk_timesheet
#: model:ir.model,name:helpdesk_timesheet.model_account_analytic_line
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket__analytic_account_id
msgid "Analytic Account"
msgstr ""

#. module: helpdesk_timesheet
#. odoo-python
#: code:addons/helpdesk_timesheet/models/helpdesk_ticket.py:0
msgid "Confirm Time Spent"
msgstr ""

#. module: helpdesk_timesheet
#: model:ir.model,name:helpdesk_timesheet.model_helpdesk_ticket_convert_wizard
msgid "Convert Helpdesk Tickets to Tasks"
msgstr ""

#. module: helpdesk_timesheet
#: model:ir.model,name:helpdesk_timesheet.model_project_task_convert_wizard
msgid "Convert Project Tasks to Tickets"
msgstr ""

#. module: helpdesk_timesheet
#: model:ir.model,name:helpdesk_timesheet.model_helpdesk_ticket_create_timesheet
msgid "Create Timesheet from ticket"
msgstr ""

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket_create_timesheet__create_uid
msgid "Created by"
msgstr ""

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket_create_timesheet__create_date
msgid "Created on"
msgstr ""

#. module: helpdesk_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_ticket_view_form_inherit_helpdesk_timesheet
msgid "Days Spent"
msgstr ""

#. module: helpdesk_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_ticket_create_timesheet_view_form
msgid "Delete"
msgstr ""

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_sla_report_analysis__department_id
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket_report_analysis__department_id
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_sla_report_analysis_view_search_timesheet
msgid "Department"
msgstr ""

#. module: helpdesk_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_ticket_create_timesheet_view_form
msgid "Describe your activity..."
msgstr ""

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket_create_timesheet__description
msgid "Description"
msgstr "ማብራርያ"

#. module: helpdesk_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_ticket_create_timesheet_view_form
msgid "Discard"
msgstr ""

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket_create_timesheet__display_name
msgid "Display Name"
msgstr ""

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_account_analytic_line__display_task
msgid "Display Task"
msgstr ""

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket__display_timer
msgid "Display Timer"
msgstr ""

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket__display_timer_pause
msgid "Display Timer Pause"
msgstr ""

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket__display_timer_resume
msgid "Display Timer Resume"
msgstr ""

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket__display_timer_start_primary
msgid "Display Timer Start Primary"
msgstr ""

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket__display_timer_start_secondary
msgid "Display Timer Start Secondary"
msgstr ""

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket__display_timer_stop
msgid "Display Timer Stop"
msgstr ""

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket__display_timesheet_timer
msgid "Display Timesheet Time"
msgstr ""

#. module: helpdesk_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_ticket_view_form_inherit_helpdesk_timesheet
msgid "Duration"
msgstr ""

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_sla_report_analysis__employee_id
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket_report_analysis__employee_id
msgid "Employee"
msgstr ""

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket__encode_uom_in_days
msgid "Encode Uom In Days"
msgstr ""

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_account_analytic_line__has_helpdesk_team
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_project_project__has_helpdesk_team
msgid "Has Helpdesk Teams"
msgstr ""

#. module: helpdesk_timesheet
#: model:ir.model,name:helpdesk_timesheet.model_helpdesk_team
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_project_project__helpdesk_team
msgid "Helpdesk Team"
msgstr ""

#. module: helpdesk_timesheet
#: model:ir.model,name:helpdesk_timesheet.model_helpdesk_ticket
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_account_analytic_line__helpdesk_ticket_id
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_timesheets_analysis_report__helpdesk_ticket_id
msgid "Helpdesk Ticket"
msgstr ""

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket__total_hours_spent
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket_report_analysis__total_hours_spent
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_ticket_create_timesheet_view_form
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_ticket_view_cohort_inherit_helpdesk_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_ticket_view_form_inherit_helpdesk_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_ticket_view_graph_main_inherit_helpdesk_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_ticket_view_pivot_main_inherit_helpdesk_timesheet
msgid "Hours Spent"
msgstr ""

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket_create_timesheet__id
msgid "ID"
msgstr ""

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket__is_timer_running
msgid "Is Timer Running"
msgstr ""

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket_create_timesheet__write_uid
msgid "Last Updated by"
msgstr ""

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket_create_timesheet__write_date
msgid "Last Updated on"
msgstr ""

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_sla_report_analysis__manager_id
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket_report_analysis__employee_parent_id
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_sla_report_analysis_view_search_timesheet
msgid "Manager"
msgstr ""

#. module: helpdesk_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_sla_report_analysis_view_search_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_ticket_report_analysis_view_search_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_ticket_view_search_inherit_helpdesk_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.rating_rating_view_search_inherit_helpdesk_timesheet
msgid "My Department"
msgstr ""

#. module: helpdesk_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.rating_rating_view_search_inherit_helpdesk_timesheet
msgid "My Team's Ratings"
msgstr ""

#. module: helpdesk_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_sla_report_analysis_view_search_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_ticket_report_analysis_view_search_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_ticket_view_search_inherit_helpdesk_timesheet
msgid "My Team's Tickets"
msgstr ""

#. module: helpdesk_timesheet
#. odoo-python
#: code:addons/helpdesk_timesheet/models/helpdesk_team.py:0
msgid "New"
msgstr ""

#. module: helpdesk_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_ticket_view_form_inherit_helpdesk_timesheet
msgid "Pause"
msgstr ""

#. module: helpdesk_timesheet
#: model:ir.model,name:helpdesk_timesheet.model_project_project
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_team__project_id
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket__project_id
msgid "Project"
msgstr ""

#. module: helpdesk_timesheet
#: model:ir.model.fields,help:helpdesk_timesheet.field_helpdesk_team__project_id
#: model:ir.model.fields,help:helpdesk_timesheet.field_helpdesk_ticket__project_id
msgid "Project to which the timesheets of this helpdesk team's tickets will be linked."
msgstr ""

#. module: helpdesk_timesheet
#: model_terms:ir.actions.act_window,help:helpdesk_timesheet.act_hr_timesheet_line_helpdesk
msgid "Record a new activity"
msgstr ""

#. module: helpdesk_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_team_view_form_inherit_helpdesk_timesheet
msgid "Recorded"
msgstr ""

#. module: helpdesk_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_ticket_view_form_inherit_helpdesk_timesheet
msgid "Resume"
msgstr ""

#. module: helpdesk_timesheet
#: model:ir.model,name:helpdesk_timesheet.model_helpdesk_sla_report_analysis
msgid "SLA Status Analysis"
msgstr ""

#. module: helpdesk_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_ticket_create_timesheet_view_form
msgid "Save"
msgstr ""

#. module: helpdesk_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_ticket_create_timesheet_view_form
msgid "Save time"
msgstr ""

#. module: helpdesk_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_ticket_view_form_inherit_helpdesk_timesheet
msgid "Start"
msgstr ""

#. module: helpdesk_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_ticket_view_form_inherit_helpdesk_timesheet
msgid "Stop"
msgstr ""

#. module: helpdesk_timesheet
#: model:project.project,label_tasks:helpdesk_timesheet.helpdesk_team3_projet
msgid "Tasks"
msgstr ""

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket_create_timesheet__ticket_id
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.hr_timesheet_line_search_inherit_helpdesk_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.hr_timesheet_line_tree_inherit_helpdesk_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.report_timesheet_ticket
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.timesheet_table
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.timesheet_view_form_helpdesk
msgid "Ticket"
msgstr ""

#. module: helpdesk_timesheet
#: model:ir.model,name:helpdesk_timesheet.model_helpdesk_ticket_report_analysis
msgid "Ticket Analysis"
msgstr ""

#. module: helpdesk_timesheet
#: model:ir.model.fields,help:helpdesk_timesheet.field_helpdesk_ticket_create_timesheet__ticket_id
msgid "Ticket for which we are creating a sales order"
msgstr ""

#. module: helpdesk_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.report_timesheet_ticket
msgid "Ticket:"
msgstr ""

#. module: helpdesk_timesheet
#. odoo-python
#: code:addons/helpdesk_timesheet/models/project.py:0
#: model:ir.actions.act_window,name:helpdesk_timesheet.project_project_action_view_helpdesk_tickets
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_project_project__ticket_ids
msgid "Tickets"
msgstr ""

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket_create_timesheet__time_spent
msgid "Time"
msgstr ""

#. module: helpdesk_timesheet
#: model:ir.model.fields,help:helpdesk_timesheet.field_helpdesk_ticket__timesheet_ids
msgid ""
"Time spent on this ticket. By default, your timesheets will be linked to the sales order item of your ticket.\n"
"Remove the sales order item to make your timesheet entries non billable."
msgstr ""

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket__timer_pause
msgid "Timer Last Pause"
msgstr ""

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket__timer_start
msgid "Timer Start"
msgstr ""

#. module: helpdesk_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_ticket_view_form_inherit_helpdesk_timesheet
msgid "Timesheet Activities"
msgstr ""

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_team__timesheet_encode_uom_id
msgid "Timesheet Encoding Unit"
msgstr ""

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket__use_helpdesk_timesheet
msgid "Timesheet activated on Team"
msgstr ""

#. module: helpdesk_timesheet
#: model:ir.actions.act_window,name:helpdesk_timesheet.act_hr_timesheet_line_helpdesk
#: model:ir.actions.report,name:helpdesk_timesheet.timesheet_report_ticket
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket__timesheet_ids
#: model:res.groups,name:helpdesk_timesheet.group_use_helpdesk_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.helpdesk_ticket_view_form_inherit_helpdesk_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.report_timesheet_ticket
msgid "Timesheets"
msgstr ""

#. module: helpdesk_timesheet
#: model:ir.model,name:helpdesk_timesheet.model_timesheets_analysis_report
msgid "Timesheets Analysis Report"
msgstr ""

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_team__total_timesheet_time
msgid "Total Timesheet Time"
msgstr ""

#. module: helpdesk_timesheet
#: model_terms:ir.actions.act_window,help:helpdesk_timesheet.act_hr_timesheet_line_helpdesk
msgid "Track your working hours by projects every day and invoice this time to your customers."
msgstr ""

#. module: helpdesk_timesheet
#: model:ir.model.fields,field_description:helpdesk_timesheet.field_helpdesk_ticket__user_timer_id
msgid "User Timer"
msgstr ""

#. module: helpdesk_timesheet
#: model:project.project,name:helpdesk_timesheet.helpdesk_team3_projet
msgid "VIP Support"
msgstr ""

#. module: helpdesk_timesheet
#. odoo-python
#: code:addons/helpdesk_timesheet/models/analytic.py:0
msgid "You cannot link a timesheet entry to a task and a ticket at the same time."
msgstr ""

#. module: helpdesk_timesheet
#: model_terms:ir.ui.view,arch_db:helpdesk_timesheet.report_timesheet_ticket
msgid "for the"
msgstr ""
