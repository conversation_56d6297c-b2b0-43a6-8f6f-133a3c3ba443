# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* documents_account
# 
# Translators:
# <PERSON>, 2024
# Wil Odoo, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-07 20:47+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Wil Odoo, 2025\n"
"Language-Team: Polish (https://app.transifex.com/odoo/teams/41243/pl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: pl\n"
"Plural-Forms: nplurals=4; plural=(n==1 ? 0 : (n%10>=2 && n%10<=4) && (n%100<12 || n%100>14) ? 1 : n!=1 && (n%10>=0 && n%10<=1) || (n%10>=5 && n%10<=9) || (n%100>=12 && n%100<=14) ? 2 : 3);\n"

#. module: documents_account
#. odoo-javascript
#: code:addons/documents_account/static/src/js/tour.js:0
msgid "<b>Deselect this page</b> as we plan to process all bills first."
msgstr ""
"<b>Odznacz tę stronę</b>, ponieważ planujemy najpierw przetworzyć wszystkie "
"rachunki."

#. module: documents_account
#. odoo-javascript
#: code:addons/documents_account/static/src/js/tour.js:0
msgid "<b>Select</b> this page to continue."
msgstr "<b>Wybierz</b> tę stronę, aby kontynować."

#. module: documents_account
#: model_terms:ir.ui.view,arch_db:documents_account.account_move_view_form
msgid "<span class=\"o_stat_text\">Documents</span>"
msgstr "<span class=\"o_stat_text\">Dokumenty</span>"

#. module: documents_account
#: model:ir.model.constraint,message:documents_account.constraint_documents_account_folder_setting_journal_unique
msgid "A setting already exists for this journal"
msgstr "Ustawienie dla tego dziennika już istnieje"

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_res_config_settings__documents_account_settings
msgid "Accounting "
msgstr "Księgowość "

#. module: documents_account
#: model:ir.model,name:documents_account.model_account_report
msgid "Accounting Report"
msgstr "Raport księgowy"

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_documents_account_folder_setting__company_account_folder_id
#: model:ir.model.fields,field_description:documents_account.field_res_company__account_folder_id
msgid "Accounting Workspace"
msgstr "Obszar roboczy księgowości"

#. module: documents_account
#. odoo-javascript
#: code:addons/documents_account/static/src/js/tour.js:0
msgid ""
"As this PDF contains multiple documents, let's split and process in bulk."
msgstr ""
"Ponieważ ten plik PDF zawiera wiele dokumentów, podzielmy go i przetworzmy "
"zbiorczo."

#. module: documents_account
#: model:ir.model,name:documents_account.model_ir_attachment
msgid "Attachment"
msgstr "Załącznik"

#. module: documents_account
#: model_terms:ir.ui.view,arch_db:documents_account.res_config_settings_view_form
msgid "Centralize accounting files and documents"
msgstr "Centralizacja plików i dokumentów księgowych"

#. module: documents_account
#: model_terms:ir.ui.view,arch_db:documents_account.view_account_move_form_inherit_documents_account
msgid "Check them"
msgstr "Sprawdź je"

#. module: documents_account
#. odoo-javascript
#: code:addons/documents_account/static/src/js/tour.js:0
msgid "Click on a card to <b>select the document</b>."
msgstr "Kliknij kartę, aby <b>wybrać dokument</b>."

#. module: documents_account
#. odoo-javascript
#: code:addons/documents_account/static/src/js/tour.js:0
msgid "Click on a thumbnail to <b>preview the document</b>."
msgstr "Kliknij miniaturę, aby wyświetlić <b>podgląd dokumentu</b>."

#. module: documents_account
#. odoo-javascript
#: code:addons/documents_account/static/src/js/tour.js:0
msgid ""
"Click on the <b>page separator</b>: we don't want to split these two pages "
"as they belong to the same document."
msgstr ""
"Kliknij <b>separator stron</b>: nie chcemy rozdzielać tych dwóch stron, "
"ponieważ należą one do tego samego dokumentu."

#. module: documents_account
#. odoo-javascript
#: code:addons/documents_account/static/src/js/tour.js:0
msgid "Click the cross to <b>exit preview</b>."
msgstr "Kliknij na krzyżyk, aby <b>opuścić podgląd</b>."

#. module: documents_account
#: model:ir.model,name:documents_account.model_res_company
msgid "Companies"
msgstr "Firmy"

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_documents_account_folder_setting__company_id
msgid "Company"
msgstr "Firma"

#. module: documents_account
#: model:ir.model,name:documents_account.model_res_config_settings
msgid "Config Settings"
msgstr "Ustawienia konfiguracji"

#. module: documents_account
#. odoo-python
#: code:addons/documents_account/models/account_report.py:0
msgid "Copy to Documents"
msgstr ""

#. module: documents_account
#: model:ir.actions.server,name:documents_account.ir_actions_server_create_credit_note
#: model:ir.actions.server,name:documents_account.ir_actions_server_create_credit_note_code
msgid "Create Customer Credit Note"
msgstr ""

#. module: documents_account
#: model:ir.actions.server,name:documents_account.ir_actions_server_create_customer_invoice
#: model:ir.actions.server,name:documents_account.ir_actions_server_create_customer_invoice_code
msgid "Create Customer Invoice"
msgstr "Utwórz fakturę dla klienta"

#. module: documents_account
#: model:ir.actions.server,name:documents_account.ir_actions_server_create_misc_entry
#: model:ir.actions.server,name:documents_account.ir_actions_server_create_misc_entry_code
msgid "Create Misc Entry"
msgstr ""

#. module: documents_account
#: model:ir.actions.server,name:documents_account.ir_actions_server_create_vendor_bill
#: model:ir.actions.server,name:documents_account.ir_actions_server_create_vendor_bill_code
msgid "Create Vendor Bill"
msgstr "Tworzenie rachunku sprzedawcy"

#. module: documents_account
#: model:ir.actions.server,name:documents_account.ir_actions_server_create_vendor_receipt
msgid "Create Vendor Receipt"
msgstr ""

#. module: documents_account
#: model:ir.actions.server,name:documents_account.ir_actions_server_create_vendor_refund
#: model:ir.actions.server,name:documents_account.ir_actions_server_create_vendor_refund_code
msgid "Create Vendor Refund"
msgstr ""

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_documents_account_folder_setting__create_uid
msgid "Created by"
msgstr "Utworzył(a)"

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_documents_account_folder_setting__create_date
msgid "Created on"
msgstr "Data utworzenia"

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_documents_account_folder_setting__display_name
msgid "Display Name"
msgstr "Nazwa wyświetlana"

#. module: documents_account
#: model:ir.model,name:documents_account.model_documents_document
msgid "Document"
msgstr "Dokument"

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_res_company__documents_account_settings
msgid "Documents Account Settings"
msgstr "Ustawienia konta dokumentów"

#. module: documents_account
#. odoo-python
#: code:addons/documents_account/models/account_report.py:0
msgid "Export"
msgstr "Eksport"

#. module: documents_account
#: model:ir.model,name:documents_account.model_account_reports_export_wizard_format
msgid "Export format for accounting's reports"
msgstr "Format eksportu raportów księgowych"

#. module: documents_account
#: model:ir.model,name:documents_account.model_account_reports_export_wizard
msgid "Export wizard for accounting's reports"
msgstr "Kreator eksportu raportów księgowych"

#. module: documents_account
#: model:mail.activity.type,name:documents_account.mail_documents_activity_data_fs
msgid "Financial Statement"
msgstr "Sprawozdanie Finansowe"

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_account_reports_export_wizard__folder_id
msgid "Folder"
msgstr "Folder"

#. module: documents_account
#: model:ir.model.fields,help:documents_account.field_account_reports_export_wizard__folder_id
msgid "Folder where to save the generated file"
msgstr "Folder, w którym ma zostać zapisany wygenerowany plik"

#. module: documents_account
#. odoo-python
#: code:addons/documents_account/models/account_journal.py:0
msgid "Generated Bank Statements"
msgstr "Wygenerowano wyciągi bankowe"

#. module: documents_account
#. odoo-python
#: code:addons/documents_account/wizard/account_reports_export_wizard.py:0
msgid "Generated Documents"
msgstr "Wygenerowane dokumenty"

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_account_bank_statement_line__has_documents
#: model:ir.model.fields,field_description:documents_account.field_account_move__has_documents
msgid "Has Documents"
msgstr ""

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_documents_document__has_embedded_pdf
msgid "Has Embedded PDF"
msgstr ""

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_documents_account_folder_setting__id
msgid "ID"
msgstr "ID"

#. module: documents_account
#: model:ir.actions.server,name:documents_account.ir_actions_server_bank_statement
#: model:ir.actions.server,name:documents_account.ir_actions_server_bank_statement_code
msgid "Import Bank Statement"
msgstr "Pobierz wyciąg bankowy"

#. module: documents_account
#. odoo-python
#: code:addons/documents_account/models/documents_document.py:0
msgid "Invoices"
msgstr "Faktury"

#. module: documents_account
#: model:ir.model,name:documents_account.model_account_journal
#: model:ir.model.fields,field_description:documents_account.field_documents_account_folder_setting__journal_id
msgid "Journal"
msgstr "Dziennik"

#. module: documents_account
#: model:ir.model,name:documents_account.model_account_move
msgid "Journal Entry"
msgstr "Zapis dziennika"

#. module: documents_account
#: model:ir.model,name:documents_account.model_documents_account_folder_setting
msgid "Journal and Folder settings"
msgstr "Ustawienia dziennika i folderu"

#. module: documents_account
#: model_terms:ir.ui.view,arch_db:documents_account.res_config_settings_view_form
msgid "Journals"
msgstr "Dzienniki"

#. module: documents_account
#: model:ir.actions.act_window,name:documents_account.action_folder_settings_installer
msgid "Journals to synchronize"
msgstr "Dzienniki do synchronizacji"

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_documents_account_folder_setting__write_uid
msgid "Last Updated by"
msgstr "Ostatnio aktualizowane przez"

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_documents_account_folder_setting__write_date
msgid "Last Updated on"
msgstr "Data ostatniej aktualizacji"

#. module: documents_account
#. odoo-javascript
#: code:addons/documents_account/static/src/js/tour.js:0
msgid ""
"Let's process documents in your Inbox.<br/><i>Tip: Use Tags to filter "
"documents and structure your process.</i>"
msgstr ""
"Przetwórzmy dokumenty w skrzynce odbiorczej. <br/><i>Wskazówka: Użyj tagów "
"do filtrowania dokumentów i strukturyzowania procesu.</i>"

#. module: documents_account
#. odoo-javascript
#: code:addons/documents_account/static/src/js/tour.js:0
msgid "Let's process these bills: turn them into vendor bills."
msgstr "Przetwórzmy te rachunki: przekształćmy je w rachunki dla sprzedawców."

#. module: documents_account
#. odoo-javascript
#: code:addons/documents_account/static/src/js/tour.js:0
msgid "Let's process this document, coming from our scanner."
msgstr "Przetwórzmy ten dokument pochodzący z naszego skanera."

#. module: documents_account
#. odoo-javascript
#: code:addons/documents_account/static/src/js/tour.js:0
msgid ""
"Let's tag this mail as legal<br/> <i>Tips: actions can be tailored to your "
"process, according to the workspace.</i>"
msgstr ""
"Oznaczmy tę wiadomość jako legal <br/><i>Porada: działania mogą być "
"dostosowane do procesu, zgodnie z obszarem roboczym.</i>"

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_account_bank_statement_line__suspense_statement_line_id
#: model:ir.model.fields,field_description:documents_account.field_account_move__suspense_statement_line_id
msgid "Request document from a bank statement line"
msgstr "Żądanie dokumentu z linii wyciągu bankowego"

#. module: documents_account
#. odoo-javascript
#: code:addons/documents_account/static/src/js/tour.js:0
msgid "Send this letter to the legal department, by assigning the right tags."
msgstr "Wyślij ten list do działu prawnego, przypisując mu odpowiednie tagi."

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_account_reports_export_wizard__tag_ids
#: model:ir.model.fields,field_description:documents_account.field_documents_account_folder_setting__tag_ids
msgid "Tags"
msgstr "Tagi"

#. module: documents_account
#: model:mail.activity.type,name:documents_account.mail_documents_activity_data_vat
msgid "Tax Statement"
msgstr "Deklaracja podatkowa"

#. module: documents_account
#: model_terms:ir.ui.view,arch_db:documents_account.view_account_move_form_inherit_documents_account
msgid "This invoice has been initiated by a bank transaction."
msgstr "Ta faktura została zainicjowana przez transakcję bankową."

#. module: documents_account
#. odoo-javascript
#: code:addons/documents_account/static/src/js/tour.js:0
msgid ""
"Want to become a <b>paperless company</b>? Let's discover Odoo Documents."
msgstr "Chcesz stać się <b>firmą bez papieru</b>? Odkryjmy Odoo Documents."

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_documents_account_folder_setting__folder_id
#: model_terms:ir.ui.view,arch_db:documents_account.res_config_settings_view_form
msgid "Workspace"
msgstr "Przestrzeń robocza"

#. module: documents_account
#: model_terms:web_tour.tour,rainbow_man_message:documents_account.documents_account_tour
msgid ""
"Wow... 6 documents processed in a few seconds, You're good.<br>The tour is "
"complete. Try uploading your own documents now."
msgstr ""

#. module: documents_account
#. odoo-python
#: code:addons/documents_account/models/documents_document.py:0
msgid "You can not create account move on folder."
msgstr ""

#. module: documents_account
#: model:ir.model.fields,field_description:documents_account.field_res_config_settings__account_folder_id
msgid "account default folder"
msgstr "domyślny folder konta"

#. module: documents_account
#: model_terms:ir.ui.view,arch_db:documents_account.view_account_move_form_inherit_documents_account
msgid "to mark this invoice as paid."
msgstr "aby oznaczyć tę fakturę jako opłaconą."
