# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* quality_mrp
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:25+0000\n"
"PO-Revision-Date: 2024-09-25 09:25+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: quality_mrp
#: model_terms:ir.ui.view,arch_db:quality_mrp.mrp_production_view_form_inherit_quality
msgid "<span class=\"o_stat_text text-danger\">Quality Checks</span>"
msgstr ""

#. module: quality_mrp
#: model_terms:ir.ui.view,arch_db:quality_mrp.mrp_production_view_form_inherit_quality
msgid "<span class=\"o_stat_text text-success\">Quality Checks</span>"
msgstr ""

#. module: quality_mrp
#: model_terms:ir.ui.view,arch_db:quality_mrp.mrp_production_view_form_inherit_quality
msgid "<span class=\"o_stat_text\">Quality Alerts</span>"
msgstr ""

#. module: quality_mrp
#: model_terms:ir.ui.view,arch_db:quality_mrp.mrp_production_view_form_inherit_quality
msgid "<span class=\"o_stat_text\">Quality Checks</span>"
msgstr ""

#. module: quality_mrp
#: model_terms:ir.ui.view,arch_db:quality_mrp.worksheet_page
msgid "<strong>Manufacturing Order: </strong>"
msgstr ""

#. module: quality_mrp
#: model:ir.model.fields,field_description:quality_mrp.field_mrp_production__quality_alert_ids
msgid "Alerts"
msgstr ""

#. module: quality_mrp
#: model:ir.model.fields,field_description:quality_mrp.field_mrp_production__check_ids
msgid "Checks"
msgstr ""

#. module: quality_mrp
#: model:ir.model,name:quality_mrp.model_mrp_production
#: model:ir.model.fields,field_description:quality_mrp.field_quality_check_on_demand__production_id
msgid "Manufacturing Order"
msgstr ""

#. module: quality_mrp
#. odoo-python
#: code:addons/quality_mrp/models/mrp_production.py:0
msgid "On-Demand Quality Check"
msgstr ""

#. module: quality_mrp
#: model:ir.model,name:quality_mrp.model_stock_move_line
msgid "Product Moves (Stock Move Line)"
msgstr ""

#. module: quality_mrp
#: model:ir.model.fields,field_description:quality_mrp.field_quality_alert__production_id
#: model:ir.model.fields,field_description:quality_mrp.field_quality_check__production_id
msgid "Production Order"
msgstr ""

#. module: quality_mrp
#: model:ir.model,name:quality_mrp.model_quality_alert
#: model_terms:ir.ui.view,arch_db:quality_mrp.mrp_production_view_form_inherit_quality
msgid "Quality Alert"
msgstr ""

#. module: quality_mrp
#: model:ir.model.fields,field_description:quality_mrp.field_mrp_production__quality_alert_count
msgid "Quality Alert Count"
msgstr ""

#. module: quality_mrp
#: model:ir.actions.server,name:quality_mrp.mrp_production_action_quality_check_on_demand
#: model:ir.model,name:quality_mrp.model_quality_check
msgid "Quality Check"
msgstr ""

#. module: quality_mrp
#: model:ir.model.fields,field_description:quality_mrp.field_mrp_production__quality_check_fail
msgid "Quality Check Fail"
msgstr ""

#. module: quality_mrp
#: model:ir.model.fields,field_description:quality_mrp.field_mrp_production__quality_check_todo
msgid "Quality Check Todo"
msgstr ""

#. module: quality_mrp
#: model:ir.actions.act_window,name:quality_mrp.quality_check_action_mo
#: model_terms:ir.ui.view,arch_db:quality_mrp.mrp_production_view_form_inherit_quality
msgid "Quality Checks"
msgstr ""

#. module: quality_mrp
#: model:ir.model,name:quality_mrp.model_quality_point
msgid "Quality Control Point"
msgstr ""

#. module: quality_mrp
#: model:ir.model,name:quality_mrp.model_stock_move
msgid "Stock Move"
msgstr ""

#. module: quality_mrp
#. odoo-python
#: code:addons/quality_mrp/models/quality.py:0
msgid ""
"The Quantity quality check type is not possible with manufacturing operation"
" types."
msgstr ""

#. module: quality_mrp
#: model:ir.model,name:quality_mrp.model_quality_check_on_demand
msgid "Wizard to select on-demand quality check points"
msgstr ""

#. module: quality_mrp
#. odoo-python
#: code:addons/quality_mrp/models/mrp_production.py:0
#: code:addons/quality_mrp/wizard/on_demand_quality_check_wizard.py:0
msgid ""
"You can not create quality check for a draft, done or cancelled "
"manufacturing order."
msgstr ""

#. module: quality_mrp
#. odoo-python
#: code:addons/quality_mrp/models/mrp_production.py:0
msgid ""
"You cannot perform a quality check if the quantity producing is zero. Please"
" set the production quantity first."
msgstr ""

#. module: quality_mrp
#. odoo-python
#: code:addons/quality_mrp/models/mrp_production.py:0
msgid "You still need to do the quality checks!"
msgstr ""
