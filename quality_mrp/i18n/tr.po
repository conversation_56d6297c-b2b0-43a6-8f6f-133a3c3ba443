# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* quality_mrp
# 
# Translators:
# emre oktem, 2024
# <AUTHOR> <EMAIL>, 2024
# Yedigen, 2024
# <PERSON><PERSON> Melik Sonmez, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:25+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Melih Melik Sonmez, 2025\n"
"Language-Team: Turkish (https://app.transifex.com/odoo/teams/41243/tr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: tr\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: quality_mrp
#: model_terms:ir.ui.view,arch_db:quality_mrp.mrp_production_view_form_inherit_quality
msgid "<span class=\"o_stat_text text-danger\">Quality Checks</span>"
msgstr ""

#. module: quality_mrp
#: model_terms:ir.ui.view,arch_db:quality_mrp.mrp_production_view_form_inherit_quality
msgid "<span class=\"o_stat_text text-success\">Quality Checks</span>"
msgstr ""

#. module: quality_mrp
#: model_terms:ir.ui.view,arch_db:quality_mrp.mrp_production_view_form_inherit_quality
msgid "<span class=\"o_stat_text\">Quality Alerts</span>"
msgstr "<span class=\"o_stat_text\">Kalite Uyarıları</span>"

#. module: quality_mrp
#: model_terms:ir.ui.view,arch_db:quality_mrp.mrp_production_view_form_inherit_quality
msgid "<span class=\"o_stat_text\">Quality Checks</span>"
msgstr "<span class=\"o_stat_text\">Kalite Kontrolleri</span>"

#. module: quality_mrp
#: model_terms:ir.ui.view,arch_db:quality_mrp.worksheet_page
msgid "<strong>Manufacturing Order: </strong>"
msgstr ""

#. module: quality_mrp
#: model:ir.model.fields,field_description:quality_mrp.field_mrp_production__quality_alert_ids
msgid "Alerts"
msgstr "İkazlar"

#. module: quality_mrp
#: model:ir.model.fields,field_description:quality_mrp.field_mrp_production__check_ids
msgid "Checks"
msgstr "Kontroller"

#. module: quality_mrp
#: model:ir.model,name:quality_mrp.model_mrp_production
#: model:ir.model.fields,field_description:quality_mrp.field_quality_check_on_demand__production_id
msgid "Manufacturing Order"
msgstr "Üretim Emri"

#. module: quality_mrp
#. odoo-python
#: code:addons/quality_mrp/models/mrp_production.py:0
msgid "On-Demand Quality Check"
msgstr ""

#. module: quality_mrp
#: model:ir.model,name:quality_mrp.model_stock_move_line
msgid "Product Moves (Stock Move Line)"
msgstr "Ürün Harketleri (Stok Hareket Satırları)"

#. module: quality_mrp
#: model:ir.model.fields,field_description:quality_mrp.field_quality_alert__production_id
#: model:ir.model.fields,field_description:quality_mrp.field_quality_check__production_id
msgid "Production Order"
msgstr "Üretim Emri"

#. module: quality_mrp
#: model:ir.model,name:quality_mrp.model_quality_alert
#: model_terms:ir.ui.view,arch_db:quality_mrp.mrp_production_view_form_inherit_quality
msgid "Quality Alert"
msgstr "Kalite Alarmı"

#. module: quality_mrp
#: model:ir.model.fields,field_description:quality_mrp.field_mrp_production__quality_alert_count
msgid "Quality Alert Count"
msgstr "Kalite Alarmı Sayısı"

#. module: quality_mrp
#: model:ir.actions.server,name:quality_mrp.mrp_production_action_quality_check_on_demand
#: model:ir.model,name:quality_mrp.model_quality_check
msgid "Quality Check"
msgstr "Kalite Kontrol"

#. module: quality_mrp
#: model:ir.model.fields,field_description:quality_mrp.field_mrp_production__quality_check_fail
msgid "Quality Check Fail"
msgstr "Başarısız Kalite Kontrol"

#. module: quality_mrp
#: model:ir.model.fields,field_description:quality_mrp.field_mrp_production__quality_check_todo
msgid "Quality Check Todo"
msgstr "Yapılacak Kalite Kontrol"

#. module: quality_mrp
#: model:ir.actions.act_window,name:quality_mrp.quality_check_action_mo
#: model_terms:ir.ui.view,arch_db:quality_mrp.mrp_production_view_form_inherit_quality
msgid "Quality Checks"
msgstr "Kalite Kontrolleri"

#. module: quality_mrp
#: model:ir.model,name:quality_mrp.model_quality_point
msgid "Quality Control Point"
msgstr "Kalite Kontrol Noktası"

#. module: quality_mrp
#: model:ir.model,name:quality_mrp.model_stock_move
msgid "Stock Move"
msgstr "Stok Hareketi"

#. module: quality_mrp
#. odoo-python
#: code:addons/quality_mrp/models/quality.py:0
msgid ""
"The Quantity quality check type is not possible with manufacturing operation"
" types."
msgstr ""

#. module: quality_mrp
#: model:ir.model,name:quality_mrp.model_quality_check_on_demand
msgid "Wizard to select on-demand quality check points"
msgstr ""

#. module: quality_mrp
#. odoo-python
#: code:addons/quality_mrp/models/mrp_production.py:0
#: code:addons/quality_mrp/wizard/on_demand_quality_check_wizard.py:0
msgid ""
"You can not create quality check for a draft, done or cancelled "
"manufacturing order."
msgstr ""

#. module: quality_mrp
#. odoo-python
#: code:addons/quality_mrp/models/mrp_production.py:0
msgid ""
"You cannot perform a quality check if the quantity producing is zero. Please"
" set the production quantity first."
msgstr ""

#. module: quality_mrp
#. odoo-python
#: code:addons/quality_mrp/models/mrp_production.py:0
msgid "You still need to do the quality checks!"
msgstr "Hala kalite kontrollerini yapmanız gerekiyor!"
