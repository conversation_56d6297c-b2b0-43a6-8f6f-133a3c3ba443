# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_contract_sign
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-19 09:53+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Jun<PERSON>, 2025\n"
"Language-Team: Japanese (https://app.transifex.com/odoo/teams/41243/ja/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ja\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: hr_contract_sign
#. odoo-python
#: code:addons/hr_contract_sign/wizard/hr_contract_sign_document_wizard.py:0
msgid "%(employee)s and %(responsible)s are the signatories."
msgstr "%(employee)s および %(responsible)s は署名者です。"

#. module: hr_contract_sign
#. odoo-python
#: code:addons/hr_contract_sign/wizard/hr_contract_sign_document_wizard.py:0
msgid "%(user_name)s requested a new signature on the following documents:"
msgstr "%(user_name)sが以下のドキュメントに対して新規署名を要求しました: "

#. module: hr_contract_sign
#. odoo-python
#: code:addons/hr_contract_sign/wizard/hr_contract_sign_document_wizard.py:0
msgid "%s does not have a private email set."
msgstr "%sは私用メールを設定していません。 "

#. module: hr_contract_sign
#. odoo-python
#: code:addons/hr_contract_sign/wizard/hr_contract_sign_document_wizard.py:0
msgid "%s does not have a work email set."
msgstr "%sは社用メールを設定していません。 "

#. module: hr_contract_sign
#: model_terms:ir.ui.view,arch_db:hr_contract_sign.hr_contract_sign_view_form
#: model_terms:ir.ui.view,arch_db:hr_contract_sign.hr_employee_sign_view_form
#: model_terms:ir.ui.view,arch_db:hr_contract_sign.res_users_request_sign_view_form
msgid "<span class=\"o_stat_text\">Signature Requests</span>"
msgstr "<span class=\"o_stat_text\">署名依頼</span>"

#. module: hr_contract_sign
#: model:ir.model,name:hr_contract_sign.model_mail_activity_plan_template
msgid "Activity plan template"
msgstr "活動計画テンプレート"

#. module: hr_contract_sign
#: model:ir.model,name:hr_contract_sign.model_mail_activity_schedule
msgid "Activity schedule plan Wizard"
msgstr "活動スケジュール計画ウィザード"

#. module: hr_contract_sign
#: model_terms:ir.ui.view,arch_db:hr_contract_sign.view_hr_contract_sign
msgid "Attach a file"
msgstr "ファイル添付"

#. module: hr_contract_sign
#: model:ir.model.fields,field_description:hr_contract_sign.field_hr_contract_sign_document_wizard__attachment_ids
msgid "Attachment"
msgstr "添付ファイル"

#. module: hr_contract_sign
#: model:sign.template,redirect_url_text:hr_contract_sign.template_sign_termination
msgid "Close"
msgstr "クローズ"

#. module: hr_contract_sign
#: model:ir.model.fields,field_description:hr_contract_sign.field_hr_contract_sign_document_wizard__contract_id
msgid "Contract"
msgstr "契約"

#. module: hr_contract_sign
#: model:ir.model,name:hr_contract_sign.model_hr_contract_history
msgid "Contract history"
msgstr "契約履歴"

#. module: hr_contract_sign
#: model:ir.model.fields,field_description:hr_contract_sign.field_hr_contract_sign_document_wizard__cc_partner_ids
msgid "Copy to"
msgstr "以下へコピー"

#. module: hr_contract_sign
#: model:ir.model.fields,field_description:hr_contract_sign.field_hr_contract_sign_document_wizard__create_uid
msgid "Created by"
msgstr "作成者"

#. module: hr_contract_sign
#: model:ir.model.fields,field_description:hr_contract_sign.field_hr_contract_sign_document_wizard__create_date
msgid "Created on"
msgstr "作成日"

#. module: hr_contract_sign
#: model_terms:ir.ui.view,arch_db:hr_contract_sign.view_hr_contract_sign
msgid "Discard"
msgstr "破棄"

#. module: hr_contract_sign
#: model:ir.model.fields,field_description:hr_contract_sign.field_hr_contract_sign_document_wizard__display_name
msgid "Display Name"
msgstr "表示名"

#. module: hr_contract_sign
#: model:ir.actions.act_window,name:hr_contract_sign.sign_contract_wizard_action
msgid "Document Signature"
msgstr "ドキュメント署名"

#. module: hr_contract_sign
#: model:ir.model.fields,field_description:hr_contract_sign.field_mail_activity_plan_template__sign_template_id
msgid "Document to sign"
msgstr "未署名ドキュメント"

#. module: hr_contract_sign
#: model:ir.model.fields,field_description:hr_contract_sign.field_hr_contract_sign_document_wizard__sign_template_ids
msgid "Documents to sign"
msgstr "未署名ドキュメント"

#. module: hr_contract_sign
#: model:ir.model.fields,help:hr_contract_sign.field_hr_contract_sign_document_wizard__sign_template_ids
msgid ""
"Documents to sign. Only documents with 1 or 2 different responsible are selectable.\n"
"        Documents with 1 responsible will only have to be signed by the employee while documents with 2 different responsible will have to be signed by both the employee and the responsible.\n"
"        "
msgstr ""
"未署名ドキュメント。選択できるのは、1つまたは2つの異なる責任者を持つドキュメントのみです。\n"
"        責任者が1名のドキュメントには従業員のみが署名する必要がありますが、責任者が2名のドキュメントには従業員と責任者の両方が署名する必要があります。\n"
"        "

#. module: hr_contract_sign
#: model:ir.model.fields,field_description:hr_contract_sign.field_hr_contract_sign_document_wizard__mail_to
msgid "Email"
msgstr "メール"

#. module: hr_contract_sign
#: model:ir.model.fields,help:hr_contract_sign.field_hr_contract_sign_document_wizard__mail_to
msgid ""
"Email used to send the signature request.\n"
"                - Work takes the email defined in \"work email\"\n"
"                - Private takes the email defined in Private Information\n"
"                - If the selected email is not defined, the available one will be used."
msgstr ""
"署名依頼の送信に使用されるEメール。\n"
"                - 社用は、\"社用メール\"で定義されたEメールを使用します。\n"
"                - 非公開は、私用メールで定義されたEメールを使用します。\n"
"                - 選択したEメールが定義されていない場合、利用可能なものが使用されます。"

#. module: hr_contract_sign
#: model:ir.model,name:hr_contract_sign.model_hr_employee
msgid "Employee"
msgstr "従業員"

#. module: hr_contract_sign
#: model:ir.model,name:hr_contract_sign.model_hr_contract
msgid "Employee Contract"
msgstr "従業員契約"

#. module: hr_contract_sign
#: model:ir.model.fields,field_description:hr_contract_sign.field_hr_contract_sign_document_wizard__employee_role_id
#: model:ir.model.fields,field_description:hr_contract_sign.field_mail_activity_plan_template__employee_role_id
msgid "Employee Role"
msgstr "従業員役職"

#. module: hr_contract_sign
#: model:ir.model.fields,help:hr_contract_sign.field_hr_contract_sign_document_wizard__employee_role_id
#: model:ir.model.fields,help:hr_contract_sign.field_mail_activity_plan_template__employee_role_id
msgid ""
"Employee's role on the templates to sign. The same role must be present in "
"all the templates"
msgstr "署名するテンプレート上の従業員の役割。全てのテンプレートに同じ役割が存在する必要があります。"

#. module: hr_contract_sign
#: model:ir.model.fields,field_description:hr_contract_sign.field_hr_contract_sign_document_wizard__employee_ids
msgid "Employees"
msgstr "従業員"

#. module: hr_contract_sign
#: model:sign.item.role,name:hr_contract_sign.sign_item_role_job_responsible
msgid "HR Responsible"
msgstr "担当者"

#. module: hr_contract_sign
#: model:ir.model.fields,field_description:hr_contract_sign.field_hr_contract_sign_document_wizard__has_both_template
msgid "Has Both Template"
msgstr "両方のテンプレートあり"

#. module: hr_contract_sign
#: model:ir.model.fields,field_description:hr_contract_sign.field_hr_contract_sign_document_wizard__id
msgid "ID"
msgstr "ID"

#. module: hr_contract_sign
#: model:ir.model.fields,field_description:hr_contract_sign.field_mail_activity_plan_template__is_signature_request
msgid "Is Signature Request"
msgstr "署名依頼"

#. module: hr_contract_sign
#: model:ir.model.fields,field_description:hr_contract_sign.field_hr_contract_sign_document_wizard__write_uid
msgid "Last Updated by"
msgstr "最終更新者"

#. module: hr_contract_sign
#: model:ir.model.fields,field_description:hr_contract_sign.field_hr_contract_sign_document_wizard__write_date
msgid "Last Updated on"
msgstr "最終更新日"

#. module: hr_contract_sign
#: model:ir.model.fields,field_description:hr_contract_sign.field_hr_contract_sign_document_wizard__mail_displayed
msgid "Mail Displayed"
msgstr "メール表示済"

#. module: hr_contract_sign
#: model_terms:ir.ui.view,arch_db:hr_contract_sign.view_hr_contract_sign
msgid "Mail Options"
msgstr "メールオプション"

#. module: hr_contract_sign
#: model:ir.model.fields,field_description:hr_contract_sign.field_hr_contract_sign_document_wizard__message
msgid "Message"
msgstr "メッセージ"

#. module: hr_contract_sign
#. odoo-python
#: code:addons/hr_contract_sign/wizard/hr_contract_sign_document_wizard.py:0
msgid ""
"No appropriate template could be found, please make sure you configured them"
" properly."
msgstr "適切なテンプレートが見つかりません。正確に設定したか確認して下さい。"

#. module: hr_contract_sign
#: model_terms:ir.ui.view,arch_db:hr_contract_sign.view_hr_contract_sign
msgid "No template available"
msgstr "利用可能なテンプレートなし"

#. module: hr_contract_sign
#. odoo-python
#: code:addons/hr_contract_sign/wizard/hr_contract_sign_document_wizard.py:0
msgid "Only %s has to sign."
msgstr " %s のみ署名が必要です。"

#. module: hr_contract_sign
#: model_terms:ir.ui.view,arch_db:hr_contract_sign.view_hr_contract_sign
msgid "Optional Message..."
msgstr "任意のメッセージ..."

#. module: hr_contract_sign
#: model:ir.model.fields,field_description:hr_contract_sign.field_hr_contract_sign_document_wizard__possible_template_ids
msgid "Possible Template"
msgstr "可能なテンプレート"

#. module: hr_contract_sign
#: model:ir.model.fields.selection,name:hr_contract_sign.selection__hr_contract_sign_document_wizard__mail_to__private
msgid "Private"
msgstr "非公開"

#. module: hr_contract_sign
#: model:ir.model.fields,field_description:hr_contract_sign.field_hr_contract__sign_request_ids
#: model:ir.model.fields,field_description:hr_contract_sign.field_hr_employee__sign_request_ids
msgid "Requested Signatures"
msgstr "依頼された署名"

#. module: hr_contract_sign
#: model:ir.model.fields,field_description:hr_contract_sign.field_hr_contract_sign_document_wizard__responsible_id
msgid "Responsible"
msgstr "担当者"

#. module: hr_contract_sign
#: model:ir.model.fields,field_description:hr_contract_sign.field_mail_activity_plan_template__responsible_count
msgid "Responsible Count"
msgstr "責任者数"

#. module: hr_contract_sign
#: model_terms:ir.ui.view,arch_db:hr_contract_sign.view_hr_contract_sign
msgid "Send"
msgstr "送信"

#. module: hr_contract_sign
#: model:ir.model.fields,field_description:hr_contract_sign.field_hr_contract__sign_request_count
#: model:ir.model.fields,field_description:hr_contract_sign.field_hr_employee__sign_request_count
#: model:ir.model.fields,field_description:hr_contract_sign.field_res_users__sign_request_count
msgid "Sign Request Count"
msgstr "署名依頼数"

#. module: hr_contract_sign
#: model_terms:ir.ui.view,arch_db:hr_contract_sign.view_hr_contract_sign
msgid "Sign Request Options"
msgstr "署名要求オプション"

#. module: hr_contract_sign
#: model:ir.model.fields,field_description:hr_contract_sign.field_hr_contract_sign_document_wizard__sign_template_responsible_ids
#: model:ir.model.fields,field_description:hr_contract_sign.field_mail_activity_plan_template__sign_template_responsible_ids
msgid "Sign Template Responsible"
msgstr "署名テンプレート担当者"

#. module: hr_contract_sign
#: model:ir.model,name:hr_contract_sign.model_hr_contract_sign_document_wizard
msgid "Sign document in contract"
msgstr "契約書でドキュメントに署名"

#. module: hr_contract_sign
#. odoo-python
#: code:addons/hr_contract_sign/models/hr_employee.py:0
#: code:addons/hr_contract_sign/wizard/mail_activity_schedule.py:0
#: model:ir.actions.server,name:hr_contract_sign.action_signature_request_multi
#: model_terms:ir.ui.view,arch_db:hr_contract_sign.hr_contract_history_view_form
#: model_terms:ir.ui.view,arch_db:hr_contract_sign.hr_employee_sign_view_form
#: model_terms:ir.ui.view,arch_db:hr_contract_sign.view_hr_contract_sign
msgid "Signature Request"
msgstr "署名依頼"

#. module: hr_contract_sign
#: model:ir.actions.server,name:hr_contract_sign.action_signature_request_wizard
msgid "Signature request"
msgstr "署名依頼"

#. module: hr_contract_sign
#: model:ir.model.fields,field_description:hr_contract_sign.field_hr_contract_sign_document_wizard__subject
msgid "Subject"
msgstr "件名"

#. module: hr_contract_sign
#: model:ir.model.fields,field_description:hr_contract_sign.field_hr_contract_sign_document_wizard__template_warning
msgid "Template Warning"
msgstr "テンプレート警告"

#. module: hr_contract_sign
#. odoo-python
#: code:addons/hr_contract_sign/models/hr_contract.py:0
msgid ""
"This sign request has been cancelled due to the cancellation of the related "
"contract."
msgstr "このサイン依頼は、関連契約の解除により取消されました。"

#. module: hr_contract_sign
#: model:ir.model,name:hr_contract_sign.model_res_users
msgid "User"
msgstr "ユーザ"

#. module: hr_contract_sign
#: model:ir.model.fields.selection,name:hr_contract_sign.selection__hr_contract_sign_document_wizard__mail_to__work
msgid "Work"
msgstr "仕事"

#. module: hr_contract_sign
#: model_terms:ir.ui.view,arch_db:hr_contract_sign.view_hr_contract_sign
msgid "Write email or search contact..."
msgstr "Eメールを登録もしくは連絡先を検索..."

#. module: hr_contract_sign
#. odoo-python
#: code:addons/hr_contract_sign/models/hr_contract.py:0
msgid ""
"You can't delete a contract linked to a signed document, archive it instead."
msgstr "署名済の文書にリンクされた契約書を削除することはできません。"

#. module: hr_contract_sign
#: model_terms:ir.ui.view,arch_db:hr_contract_sign.hr_contract_history_view_form
msgid "document"
msgstr "ドキュメント"

#. module: hr_contract_sign
#: model_terms:ir.ui.view,arch_db:hr_contract_sign.hr_contract_history_view_form
msgid "documents"
msgstr "ドキュメント"
