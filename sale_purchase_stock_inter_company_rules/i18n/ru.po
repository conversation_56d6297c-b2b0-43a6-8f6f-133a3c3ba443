# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* sale_purchase_stock_inter_company_rules
# 
# Translators:
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-25 09:15+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Russian (https://app.transifex.com/odoo/teams/41243/ru/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ru\n"
"Plural-Forms: nplurals=4; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<12 || n%100>14) ? 1 : n%10==0 || (n%10>=5 && n%10<=9) || (n%100>=11 && n%100<=14)? 2 : 3);\n"

#. module: sale_purchase_stock_inter_company_rules
#: model:ir.model,name:sale_purchase_stock_inter_company_rules.model_res_company
msgid "Companies"
msgstr "Компании"

#. module: sale_purchase_stock_inter_company_rules
#: model:ir.model,name:sale_purchase_stock_inter_company_rules.model_res_config_settings
msgid "Config Settings"
msgstr "Параметры конфигурации"

#. module: sale_purchase_stock_inter_company_rules
#. odoo-python
#: code:addons/sale_purchase_stock_inter_company_rules/models/purchase_order.py:0
#: code:addons/sale_purchase_stock_inter_company_rules/models/sale_order.py:0
msgid ""
"Configure correct warehouse for company(%s) from Menu: "
"Settings/Users/<USER>"
msgstr ""
"Настройте правильный склад для компании(%s) из Меню: "
"Настройки/Пользователи/Компании"

#. module: sale_purchase_stock_inter_company_rules
#: model:ir.model.fields,help:sale_purchase_stock_inter_company_rules.field_res_company__intercompany_receipt_type_id
#: model:ir.model.fields,help:sale_purchase_stock_inter_company_rules.field_res_config_settings__intercompany_receipt_type_id
msgid ""
"Default Operation type to set on Receipts that will be created for inter-"
"company transfers"
msgstr ""

#. module: sale_purchase_stock_inter_company_rules
#: model:ir.model.fields,help:sale_purchase_stock_inter_company_rules.field_res_company__intercompany_warehouse_id
#: model:ir.model.fields,help:sale_purchase_stock_inter_company_rules.field_res_config_settings__intercompany_warehouse_id
msgid ""
"Default value to set on Purchase(Sales) Orders that will be created based on"
" Sale(Purchase) Orders made to this company"
msgstr ""
"Значение по умолчанию для Покупки (или Продажи), которое будет создано на "
"основе Продажи (Закупки), сделанной этой компанией"

#. module: sale_purchase_stock_inter_company_rules
#: model:ir.model,name:sale_purchase_stock_inter_company_rules.model_purchase_order
msgid "Purchase Order"
msgstr "Заказ на покупку"

#. module: sale_purchase_stock_inter_company_rules
#: model:ir.model.fields,field_description:sale_purchase_stock_inter_company_rules.field_res_company__intercompany_receipt_type_id
#: model:ir.model.fields,field_description:sale_purchase_stock_inter_company_rules.field_res_config_settings__intercompany_receipt_type_id
msgid "Receipt Operation Type"
msgstr ""

#. module: sale_purchase_stock_inter_company_rules
#: model:ir.model,name:sale_purchase_stock_inter_company_rules.model_sale_order
msgid "Sales Order"
msgstr "Заказ на продажу"

#. module: sale_purchase_stock_inter_company_rules
#: model:ir.model,name:sale_purchase_stock_inter_company_rules.model_sale_order_line
msgid "Sales Order Line"
msgstr "Позиция заказа на продажу"

#. module: sale_purchase_stock_inter_company_rules
#: model:ir.model.fields,field_description:sale_purchase_stock_inter_company_rules.field_res_company__intercompany_sync_delivery_receipt
#: model:ir.model.fields,field_description:sale_purchase_stock_inter_company_rules.field_res_config_settings__intercompany_sync_delivery_receipt
msgid "Synchronize Deliveries to your Receipts"
msgstr ""

#. module: sale_purchase_stock_inter_company_rules
#: model:ir.model,name:sale_purchase_stock_inter_company_rules.model_stock_picking
msgid "Transfer"
msgstr "Перевод"

#. module: sale_purchase_stock_inter_company_rules
#: model_terms:ir.ui.view,arch_db:sale_purchase_stock_inter_company_rules.res_config_settings_view_form
msgid "Use Operation"
msgstr ""

#. module: sale_purchase_stock_inter_company_rules
#: model_terms:ir.ui.view,arch_db:sale_purchase_stock_inter_company_rules.res_config_settings_view_form
msgid "Use Warehouse"
msgstr "Использование склада"

#. module: sale_purchase_stock_inter_company_rules
#: model:ir.model.fields,field_description:sale_purchase_stock_inter_company_rules.field_res_company__intercompany_warehouse_id
msgid "Warehouse"
msgstr "Склад"

#. module: sale_purchase_stock_inter_company_rules
#: model:ir.model.fields,field_description:sale_purchase_stock_inter_company_rules.field_res_config_settings__intercompany_warehouse_id
msgid "Warehouse For Purchase Orders"
msgstr "Склад для заказов на закупку"
