<Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Invoice-2"
    xmlns:cac="urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2"
    xmlns:cbc="urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2"
    xmlns:ext="urn:oasis:names:specification:ubl:schema:xsd:CommonExtensionComponents-2"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="urn:oasis:names:specification:ubl:schema:xsd:Invoice-2     http://docs.oasis-open.org/ubl/os-UBL-2.1/xsd/maindoc/UBL-Invoice-2.1.xsd">
    <ext:UBLExtensions>___ignore___</ext:UBLExtensions>
    <cbc:UBLVersionID>UBL 2.1</cbc:UBLVersionID>
    <cbc:CustomizationID>10</cbc:CustomizationID>
    <cbc:ProfileID>DIAN 2.1: Factura Electrónica de Venta</cbc:ProfileID>
    <cbc:ProfileExecutionID>2</cbc:ProfileExecutionID>
    <cbc:ID>SETP/2024/00001</cbc:ID>
    <cbc:UUID schemeID="2" schemeName="CUFE-SHA384">bc8f656ae00ec5d9761f413588551730a7c45ca3fc01b3ec86ebbfe19b95110ea2b2710667317ac0bcff90184586eea4</cbc:UUID>
    <cbc:IssueDate>2024-01-29</cbc:IssueDate>
    <cbc:IssueTime>19:00:00-05:00</cbc:IssueTime>
    <cbc:DueDate>2024-01-30</cbc:DueDate>
    <cbc:InvoiceTypeCode>01</cbc:InvoiceTypeCode>
    <cbc:Note>SETP/2024/*********-01-2919:00:00-05:00200.000124.00040.00030.00224.00******************dummy_journal_technical_key2</cbc:Note>
    <cbc:DocumentCurrencyCode listAgencyID="6" listAgencyName="United Nations Economic Commission for Europe" listID="ISO 4217 Alpha">COP</cbc:DocumentCurrencyCode>
    <cbc:LineCountNumeric>2</cbc:LineCountNumeric>
    <cac:OrderReference>
        <cbc:ID>SETP/2024/00001</cbc:ID>
    </cac:OrderReference>
    <cac:AccountingSupplierParty>
        <cbc:AdditionalAccountID>1</cbc:AdditionalAccountID>
        <cac:Party>
            <cbc:IndustryClassificationCode>0114</cbc:IndustryClassificationCode>
            <cac:PartyName>
                <cbc:Name>CO Company Test</cbc:Name>
            </cac:PartyName>
            <cac:PhysicalLocation>
                <cac:Address>
                    <cbc:ID>11001</cbc:ID>
                    <cbc:CityName>Bogotá D.C.</cbc:CityName>
                    <cbc:PostalZone>110110</cbc:PostalZone>
                    <cbc:CountrySubentity>Bogotá</cbc:CountrySubentity>
                    <cbc:CountrySubentityCode>11</cbc:CountrySubentityCode>
                    <cac:AddressLine>
                        <cbc:Line>CL 12A </cbc:Line>
                    </cac:AddressLine>
                    <cac:Country>
                        <cbc:IdentificationCode>CO</cbc:IdentificationCode>
                        <cbc:Name languageID="es">Colombia</cbc:Name>
                    </cac:Country>
                </cac:Address>
            </cac:PhysicalLocation>
            <cac:PartyTaxScheme>
                <cbc:RegistrationName>CO Company Test</cbc:RegistrationName>
                <cbc:CompanyID schemeName="31" schemeAgencyName="CO, DIAN (Dirección de Impuestos y Aduanas Nacionales)" schemeAgencyID="195" schemeID="5">**********</cbc:CompanyID>
                <cbc:TaxLevelCode>O-47</cbc:TaxLevelCode>
                <cac:RegistrationAddress>
                    <cbc:ID>11001</cbc:ID>
                    <cbc:CityName>Bogotá D.C.</cbc:CityName>
                    <cbc:PostalZone>110110</cbc:PostalZone>
                    <cbc:CountrySubentity>Bogotá</cbc:CountrySubentity>
                    <cbc:CountrySubentityCode>11</cbc:CountrySubentityCode>
                    <cac:AddressLine>
                        <cbc:Line>CL 12A </cbc:Line>
                    </cac:AddressLine>
                    <cac:Country>
                        <cbc:IdentificationCode>CO</cbc:IdentificationCode>
                        <cbc:Name languageID="es">Colombia</cbc:Name>
                    </cac:Country>
                </cac:RegistrationAddress>
                <cac:TaxScheme>
                    <cbc:ID>01</cbc:ID>
                    <cbc:Name>IVA</cbc:Name>
                </cac:TaxScheme>
            </cac:PartyTaxScheme>
            <cac:PartyLegalEntity>
                <cbc:RegistrationName>CO Company Test</cbc:RegistrationName>
                <cbc:CompanyID schemeName="31" schemeAgencyName="CO, DIAN (Dirección de Impuestos y Aduanas Nacionales)" schemeAgencyID="195" schemeID="5">**********</cbc:CompanyID>
                <cac:CorporateRegistrationScheme>
                    <cbc:ID>SETP</cbc:ID>
                    <cbc:Name>**********</cbc:Name>
                </cac:CorporateRegistrationScheme>
            </cac:PartyLegalEntity>
            <cac:Contact>
                <cbc:Name>CO Company Test</cbc:Name>
            </cac:Contact>
        </cac:Party>
    </cac:AccountingSupplierParty>
    <cac:AccountingCustomerParty>
        <cbc:AdditionalAccountID>2</cbc:AdditionalAccountID>
        <cac:Party>
            <cac:PartyIdentification>
                <cbc:ID schemeName="31" schemeID="1">********</cbc:ID>
            </cac:PartyIdentification>
            <cac:PartyName>
                <cbc:Name>ADQUIRIENTE DE EJEMPLO</cbc:Name>
            </cac:PartyName>
            <cac:PhysicalLocation>
                <cac:Address>
                    <cbc:ID>11001</cbc:ID>
                    <cbc:CityName>Bogotá D.C.</cbc:CityName>
                    <cbc:PostalZone>110110</cbc:PostalZone>
                    <cbc:CountrySubentity>Bogotá</cbc:CountrySubentity>
                    <cbc:CountrySubentityCode>11</cbc:CountrySubentityCode>
                    <cac:AddressLine>
                        <cbc:Line>CARRERA 8 No 20-14/40 </cbc:Line>
                    </cac:AddressLine>
                    <cac:Country>
                        <cbc:IdentificationCode>CO</cbc:IdentificationCode>
                        <cbc:Name languageID="es">Colombia</cbc:Name>
                    </cac:Country>
                </cac:Address>
            </cac:PhysicalLocation>
            <cac:PartyTaxScheme>
                <cbc:RegistrationName>ADQUIRIENTE DE EJEMPLO</cbc:RegistrationName>
                <cbc:CompanyID schemeName="31" schemeAgencyName="CO, DIAN (Dirección de Impuestos y Aduanas Nacionales)" schemeAgencyID="195" schemeID="1">********</cbc:CompanyID>
                <cbc:TaxLevelCode>O-47</cbc:TaxLevelCode>
                <cac:RegistrationAddress>
                    <cbc:ID>11001</cbc:ID>
                    <cbc:CityName>Bogotá D.C.</cbc:CityName>
                    <cbc:PostalZone>110110</cbc:PostalZone>
                    <cbc:CountrySubentity>Bogotá</cbc:CountrySubentity>
                    <cbc:CountrySubentityCode>11</cbc:CountrySubentityCode>
                    <cac:AddressLine>
                        <cbc:Line>CARRERA 8 No 20-14/40 </cbc:Line>
                    </cac:AddressLine>
                    <cac:Country>
                        <cbc:IdentificationCode>CO</cbc:IdentificationCode>
                        <cbc:Name languageID="es">Colombia</cbc:Name>
                    </cac:Country>
                </cac:RegistrationAddress>
                <cac:TaxScheme>
                    <cbc:ID>01</cbc:ID>
                    <cbc:Name>IVA</cbc:Name>
                </cac:TaxScheme>
            </cac:PartyTaxScheme>
            <cac:PartyLegalEntity>
                <cbc:RegistrationName>ADQUIRIENTE DE EJEMPLO</cbc:RegistrationName>
                <cbc:CompanyID schemeName="31" schemeAgencyName="CO, DIAN (Dirección de Impuestos y Aduanas Nacionales)" schemeAgencyID="195" schemeID="1">********</cbc:CompanyID>
            </cac:PartyLegalEntity>
            <cac:Contact>
                <cbc:Name>ADQUIRIENTE DE EJEMPLO</cbc:Name>
            </cac:Contact>
        </cac:Party>
    </cac:AccountingCustomerParty>
    <cac:PaymentMeans>
        <cbc:ID>1</cbc:ID>
        <cbc:PaymentMeansCode>1</cbc:PaymentMeansCode>
        <cbc:PaymentDueDate>2024-01-30</cbc:PaymentDueDate>
        <cbc:PaymentID>SETP/2024/00001</cbc:PaymentID>
    </cac:PaymentMeans>
    <cac:TaxTotal>
        <cbc:TaxAmount currencyID="COP">24.00</cbc:TaxAmount>
        <cac:TaxSubtotal>
            <cbc:TaxableAmount currencyID="COP">100.00</cbc:TaxableAmount>
            <cbc:TaxAmount currencyID="COP">19.00</cbc:TaxAmount>
            <cac:TaxCategory>
                <cbc:Percent>19.00</cbc:Percent>
                <cac:TaxScheme>
                    <cbc:ID>01</cbc:ID>
                    <cbc:Name>IVA</cbc:Name>
                </cac:TaxScheme>
            </cac:TaxCategory>
        </cac:TaxSubtotal>
        <cac:TaxSubtotal>
            <cbc:TaxableAmount currencyID="COP">100.00</cbc:TaxableAmount>
            <cbc:TaxAmount currencyID="COP">5.00</cbc:TaxAmount>
            <cac:TaxCategory>
                <cbc:Percent>5.00</cbc:Percent>
                <cac:TaxScheme>
                    <cbc:ID>01</cbc:ID>
                    <cbc:Name>IVA</cbc:Name>
                </cac:TaxScheme>
            </cac:TaxCategory>
        </cac:TaxSubtotal>
    </cac:TaxTotal>
    <cac:WithholdingTaxTotal>
        <cbc:TaxAmount currencyID="COP">3.60</cbc:TaxAmount>
        <cac:TaxSubtotal>
            <cbc:TaxableAmount currencyID="COP">100.00</cbc:TaxableAmount>
            <cbc:TaxAmount currencyID="COP">2.85</cbc:TaxAmount>
            <cac:TaxCategory>
                <cbc:Percent>2.85</cbc:Percent>
                <cac:TaxScheme>
                    <cbc:ID>05</cbc:ID>
                    <cbc:Name>ReteIVA</cbc:Name>
                </cac:TaxScheme>
            </cac:TaxCategory>
        </cac:TaxSubtotal>
        <cac:TaxSubtotal>
            <cbc:TaxableAmount currencyID="COP">100.00</cbc:TaxableAmount>
            <cbc:TaxAmount currencyID="COP">0.75</cbc:TaxAmount>
            <cac:TaxCategory>
                <cbc:Percent>0.75</cbc:Percent>
                <cac:TaxScheme>
                    <cbc:ID>05</cbc:ID>
                    <cbc:Name>ReteIVA</cbc:Name>
                </cac:TaxScheme>
            </cac:TaxCategory>
        </cac:TaxSubtotal>
    </cac:WithholdingTaxTotal>
    <cac:WithholdingTaxTotal>
        <cbc:TaxAmount currencyID="COP">0.41</cbc:TaxAmount>
        <cac:TaxSubtotal>
            <cbc:TaxableAmount currencyID="COP">100.00</cbc:TaxableAmount>
            <cbc:TaxAmount currencyID="COP">0.41</cbc:TaxAmount>
            <cac:TaxCategory>
                <cbc:Percent>0.414</cbc:Percent>
                <cac:TaxScheme>
                    <cbc:ID>07</cbc:ID>
                    <cbc:Name>ReteICA</cbc:Name>
                </cac:TaxScheme>
            </cac:TaxCategory>
        </cac:TaxSubtotal>
    </cac:WithholdingTaxTotal>
    <cac:LegalMonetaryTotal>
        <cbc:LineExtensionAmount currencyID="COP">200.00</cbc:LineExtensionAmount>
        <cbc:TaxExclusiveAmount currencyID="COP">200.00</cbc:TaxExclusiveAmount>
        <cbc:TaxInclusiveAmount currencyID="COP">224.00</cbc:TaxInclusiveAmount>
        <cbc:PayableAmount currencyID="COP">224.00</cbc:PayableAmount>
    </cac:LegalMonetaryTotal>
    <cac:InvoiceLine>
        <cbc:ID>1</cbc:ID>
        <cbc:InvoicedQuantity unitCode="94">1.0</cbc:InvoicedQuantity>
        <cbc:LineExtensionAmount currencyID="COP">100.00</cbc:LineExtensionAmount>
        <cac:TaxTotal>
            <cbc:TaxAmount currencyID="COP">19.00</cbc:TaxAmount>
            <cac:TaxSubtotal>
                <cbc:TaxableAmount currencyID="COP">100.00</cbc:TaxableAmount>
                <cbc:TaxAmount currencyID="COP">19.00</cbc:TaxAmount>
                <cac:TaxCategory>
                    <cbc:Percent>19.00</cbc:Percent>
                    <cac:TaxScheme>
                        <cbc:ID>01</cbc:ID>
                        <cbc:Name>IVA</cbc:Name>
                    </cac:TaxScheme>
                </cac:TaxCategory>
            </cac:TaxSubtotal>
        </cac:TaxTotal>
        <cac:WithholdingTaxTotal>
            <cbc:TaxAmount currencyID="COP">2.85</cbc:TaxAmount>
            <cac:TaxSubtotal>
                <cbc:TaxableAmount currencyID="COP">100.00</cbc:TaxableAmount>
                <cbc:TaxAmount currencyID="COP">2.85</cbc:TaxAmount>
                <cac:TaxCategory>
                    <cbc:Percent>2.85</cbc:Percent>
                    <cac:TaxScheme>
                        <cbc:ID>05</cbc:ID>
                        <cbc:Name>ReteIVA</cbc:Name>
                    </cac:TaxScheme>
                </cac:TaxCategory>
            </cac:TaxSubtotal>
        </cac:WithholdingTaxTotal>
        <cac:WithholdingTaxTotal>
            <cbc:TaxAmount currencyID="COP">0.41</cbc:TaxAmount>
            <cac:TaxSubtotal>
                <cbc:TaxableAmount currencyID="COP">100.00</cbc:TaxableAmount>
                <cbc:TaxAmount currencyID="COP">0.41</cbc:TaxAmount>
                <cac:TaxCategory>
                    <cbc:Percent>0.414</cbc:Percent>
                    <cac:TaxScheme>
                        <cbc:ID>07</cbc:ID>
                        <cbc:Name>ReteICA</cbc:Name>
                    </cac:TaxScheme>
                </cac:TaxCategory>
            </cac:TaxSubtotal>
        </cac:WithholdingTaxTotal>
        <cac:Item>
            <cbc:Description>product_a</cbc:Description>
            <cbc:Name>product_a</cbc:Name>
            <cac:StandardItemIdentification>
                <cbc:ID schemeID="010" schemeName="GTIN">*********</cbc:ID>
            </cac:StandardItemIdentification>
        </cac:Item>
        <cac:Price>
            <cbc:PriceAmount currencyID="COP">100.0</cbc:PriceAmount>
            <cbc:BaseQuantity unitCode="94">1.0</cbc:BaseQuantity>
        </cac:Price>
    </cac:InvoiceLine>
    <cac:InvoiceLine>
        <cbc:ID>2</cbc:ID>
        <cbc:InvoicedQuantity unitCode="DZN">1.0</cbc:InvoicedQuantity>
        <cbc:LineExtensionAmount currencyID="COP">100.00</cbc:LineExtensionAmount>
        <cac:TaxTotal>
            <cbc:TaxAmount currencyID="COP">5.00</cbc:TaxAmount>
            <cac:TaxSubtotal>
                <cbc:TaxableAmount currencyID="COP">100.00</cbc:TaxableAmount>
                <cbc:TaxAmount currencyID="COP">5.00</cbc:TaxAmount>
                <cac:TaxCategory>
                    <cbc:Percent>5.00</cbc:Percent>
                    <cac:TaxScheme>
                        <cbc:ID>01</cbc:ID>
                        <cbc:Name>IVA</cbc:Name>
                    </cac:TaxScheme>
                </cac:TaxCategory>
            </cac:TaxSubtotal>
        </cac:TaxTotal>
        <cac:WithholdingTaxTotal>
            <cbc:TaxAmount currencyID="COP">0.75</cbc:TaxAmount>
            <cac:TaxSubtotal>
                <cbc:TaxableAmount currencyID="COP">100.00</cbc:TaxableAmount>
                <cbc:TaxAmount currencyID="COP">0.75</cbc:TaxAmount>
                <cac:TaxCategory>
                    <cbc:Percent>0.75</cbc:Percent>
                    <cac:TaxScheme>
                        <cbc:ID>05</cbc:ID>
                        <cbc:Name>ReteIVA</cbc:Name>
                    </cac:TaxScheme>
                </cac:TaxCategory>
            </cac:TaxSubtotal>
        </cac:WithholdingTaxTotal>
        <cac:Item>
            <cbc:Description>[prod_b] product_b</cbc:Description>
            <cbc:Name>product_b</cbc:Name>
            <cac:SellersItemIdentification>
                <cbc:ID>prod_b</cbc:ID>
            </cac:SellersItemIdentification>
            <cac:StandardItemIdentification>
                <cbc:ID schemeID="999" schemeName="Estándar de adopción del contribuyente">prod_b</cbc:ID>
            </cac:StandardItemIdentification>
        </cac:Item>
        <cac:Price>
            <cbc:PriceAmount currencyID="COP">100.0</cbc:PriceAmount>
            <cbc:BaseQuantity unitCode="DZN">1.0</cbc:BaseQuantity>
        </cac:Price>
    </cac:InvoiceLine>
</Invoice>
