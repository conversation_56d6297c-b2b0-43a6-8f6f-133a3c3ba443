from odoo import fields, models, api


class AccountReport(models.Model):
    _inherit = 'account.report'


    filter_zero_balance = fields.<PERSON><PERSON><PERSON>(
        string="Zero Balance",
        compute=lambda x: x._compute_report_option_filter('filter_zero_balance', True),
        readonly=False, store=True,
        depends=['root_report_id', 'section_main_report_ids']
    )
    filter_balance = fields.Boolean(
        string="Balance",
        compute=lambda x: x._compute_report_option_filter('filter_balance', True),
        readonly=False, store=True,
        depends=['root_report_id', 'section_main_report_ids']
    )
    filter_debit_credit_movement = fields.Bo<PERSON>an(
        string="Debit Credit Movement",
        compute=lambda x: x._compute_report_option_filter('filter_debit_credit_movement', True),
        readonly=False,
        store=True,
        depends=['root_report_id', 'section_main_report_ids']
    )

    def _init_options_zero_balance(self, options, previous_options=None):
        if self.filter_zero_balance and previous_options:
            options['zero_balance'] = previous_options.get('zero_balance', False)
        else:
            options['zero_balance'] = False

    def _init_options_balance(self, options, previous_options=None):
        if self.filter_balance and previous_options:
            options['balance'] = previous_options.get('balance', False)
        else:
            options['balance'] = False

    def _init_options_debit_credit_movement(self, options, previous_options=None):
        if self.filter_balance and previous_options:
            options['debit_credit_movement'] = previous_options.get('debit_credit_movement', False)
        else:
            options['debit_credit_movement'] = False

    def get_report_information(self, options):
        """ It is set option zero balance filter show or not on account reports form view """
        info = super().get_report_information(options)
        info['filters']['show_zero_balance'] = self.filter_zero_balance
        info['filters']['show_balance'] = self.filter_balance
        info['filters']['show_debit_credit_movement'] = self.filter_debit_credit_movement
        return info
