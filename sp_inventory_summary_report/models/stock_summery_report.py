# -*- coding: utf-8 -*-
# Copyright  Softprime Consulting Pvt Ltd

from odoo.exceptions import UserError

from odoo import models, fields, _


class StockReportDbView(models.Model):
    _name = "stock.report.db.view"
    _description = "Stock Summery Report"

    product_id = fields.Many2one('product.product', 'Product Name')
    location_id = fields.Many2one('stock.location', string='Locations')
    opening = fields.Float('Opening')
    purchase = fields.Float('Purchase')
    purchase_return = fields.Float('Purchase Returned')
    sale = fields.Float('Sale')
    sale_return = fields.Float('Sale Returned')
    internal = fields.Float('Internal')
    consume = fields.Float('Consume')
    adjustment = fields.Float('Adjustment')
    at_transit = fields.Float('At Transit')
    scrapped = fields.Float('Scraped')
    closing = fields.Float('Closing')

    def incoming_outgoing_domain(self, from_date, to_date, product_ids):
        query = " AND sm.company_id = %s"
        if product_ids:
            query += " AND sm.product_id in %s"
        if from_date:
            query += " AND (sm.date AT TIME ZONE 'UTC' AT TIME ZONE %s)::date >= %s"
        if to_date:
            query += " AND (sm.date AT TIME ZONE 'UTC' AT TIME ZONE %s)::date <= %s"
        return query

    def stock_move_basic_query(self):
        """Stock Move Basic Query"""
        query = "SELECT sm.id from stock_move sm where sm.state = 'done' "
        return query

    def get_basic_query_incoming_outgoing_opening(self, company_id, product_ids, from_date, to_date):
        """Get Basic Query for incoming outgoing and opening"""
        args = [company_id.id, tuple(product_ids.ids)]
        if from_date:
            args.append(self.env.user.tz)
            args.append(from_date)
        if to_date:
            args.append(self.env.user.tz)
            args.append(to_date)
        stock_move_qry = self.stock_move_basic_query()
        qry_incoming_outgoing = stock_move_qry + self.incoming_outgoing_domain(from_date, to_date, product_ids)
        self.env.cr.execute(qry_incoming_outgoing, tuple(args))
        result_incoming_outgoing = self.env.cr.fetchall()
        incoming_outgoing_move_ids = [i[0] for i in result_incoming_outgoing]
        return incoming_outgoing_move_ids

    def get_initial_query(self, location_string):
        """Get Initial Query"""
        query = "select sm.product_id as product_id, " + location_string + " as location_id," + """
                sum(sm.quantity * round(1 / uom.factor, 2)) as qty
                from stock_move sm
                left join uom_uom uom on uom.id = sm.product_uom
                where 
                    sm.state = 'done' 
                    and sm.product_id in %s
                    and company_id = %s
                """
        return query

    def get_location_string(self, location_string):
        query = " and " + location_string + " in %s and (sm.date AT TIME ZONE 'UTC' AT TIME ZONE %s)::date < %s "
        return query

    def get_product_opening_qty(self, product_ids, location_ids, from_date):
        """Get product Available qty from and to date"""
        args_list = [tuple(product_ids.ids), self.env.company.id, tuple(location_ids.ids), self.env.user.tz, from_date]
        query_incoming = self.get_initial_query("sm.location_dest_id") + self.get_location_string("sm.location_dest_id")
        query_outgoing = self.get_initial_query("sm.location_id") + self.get_location_string("sm.location_id")
        query_incoming += """ group by sm.product_id, sm.location_dest_id"""
        query_outgoing += """ group by sm.product_id, sm.location_id"""
        self._cr.execute(query_incoming, tuple(args_list))
        result_incoming = self.env.cr.fetchall()
        self._cr.execute(query_outgoing, tuple(args_list))
        result_outgoing = self.env.cr.fetchall()
        res_incoming = {(product_id, location_id): quantity for product_id, location_id, quantity in result_incoming}
        res_outgoing = {(product_id, location_id): quantity for product_id, location_id, quantity in result_outgoing}
        result = []
        all_keys = res_incoming.keys() | res_outgoing.keys()
        for key in all_keys:
            incoming_qty = res_incoming.get(key, 0.0)
            outgoing_qty = res_outgoing.get(key, 0.0)
            opening_qty = incoming_qty - outgoing_qty
            result.append({'product_id': key[0], 'location_id': key[1], 'opening': opening_qty})
        return result

    def get_purchase_move_record(self, product_ids, location_ids, move_ids):
        """Get Purchase Move Record"""
        args = [tuple(move_ids), tuple(product_ids.ids), tuple(location_ids.ids)]
        query = """
                select 
                    sm.product_id as product_id,
                    sm.location_dest_id as location_id,
                    sum(sm.quantity * round(1 / uom.factor , 2)) as qty
                from stock_move sm
                left join uom_uom uom on uom.id = sm.product_uom
                where 
                    sm.id in %s
                    and sm.product_id in %s
                    and sm.location_dest_id in %s
                    and sm.purchase_line_id is not null
                group by 
                    sm.product_id, 
                    sm.location_dest_id
                """
        self._cr.execute(query, tuple(args))
        result_purchase = self.env.cr.fetchall()
        dict_list = [{'product_id': t[0], 'location_id': t[1], 'purchase': t[2]} for t in result_purchase]
        return dict_list

    def get_purchase_return_move_record(self, product_ids, location_ids, move_ids):
        """Get Purchase Move Record"""
        args = [tuple(move_ids), tuple(product_ids.ids), tuple(location_ids.ids)]
        query = """
                select 
                    sm.product_id as product_id,
                    sm.location_id as location_id,
                    sum(sm.quantity * round(1 / uom.factor , 2)) as qty
                from stock_move sm
                left join uom_uom uom on uom.id = sm.product_uom
                where 
                    sm.id in %s
                    and sm.product_id in %s
                    and sm.location_id in %s
                    and sm.purchase_line_id is not null
                group by 
                    sm.product_id, 
                    sm.location_id
                """
        self._cr.execute(query, tuple(args))
        result_purchase = self.env.cr.fetchall()
        dict_list = [{'product_id': t[0], 'location_id': t[1], 'purchase_return': t[2]} for t in result_purchase]
        return dict_list

    def get_sale_moves_record(self, product_ids, location_ids, move_ids):
        """Get Sale Move Record"""
        args = [tuple(move_ids), tuple(product_ids.ids), tuple(location_ids.ids)]
        query = """
                select 
                    sm.product_id as product_id,
                    sm.location_id as location_id,
                    sum(sm.quantity * round(1 / uom.factor , 2)) as qty
                from stock_move sm
                left join uom_uom uom on uom.id = sm.product_uom
                where 
                    sm.id in %s
                    and sm.product_id in %s
                    and sm.location_id in %s
                    and sm.sale_line_id is not null
                group by 
                    sm.product_id, 
                    sm.location_id
                """
        self._cr.execute(query, tuple(args))
        result_sale = self.env.cr.fetchall()
        dict_list = [{'product_id': t[0], 'location_id': t[1], 'sale': t[2]} for t in result_sale]
        return dict_list

    def get_sale_return_moves_record(self, product_ids, location_ids, move_ids):
        """Get Sale Return Move Record"""
        args = [tuple(move_ids), tuple(product_ids.ids), tuple(location_ids.ids)]
        query = """
                select 
                    sm.product_id as product_id,
                    sm.location_dest_id as location_id,
                    sum(sm.quantity * round(1 / uom.factor , 2)) as qty
                from stock_move sm
                left join uom_uom uom on uom.id = sm.product_uom
                where 
                    sm.id in %s
                    and sm.product_id in %s
                    and sm.location_dest_id in %s
                    and sm.sale_line_id is not null
                group by 
                    sm.product_id, 
                    sm.location_dest_id
                """
        self._cr.execute(query, tuple(args))
        result_sale = self.env.cr.fetchall()
        dict_list = [{'product_id': t[0], 'location_id': t[1], 'sale_return': t[2]} for t in result_sale]
        return dict_list

    def get_at_transit_moves_record(self, product_ids, location_ids, move_ids):
        """Get Move At Transit Record"""
        args = [tuple(move_ids), tuple(product_ids.ids), tuple(location_ids.ids),
                tuple(move_ids), tuple(product_ids.ids), tuple(location_ids.ids)]
        query = """
                with table_incoming as(
                select 
                    sm.product_id as product_id,
                    sm.location_id as location_id,
                    sum(sm.quantity * round(1 / uom.factor , 2)) as qty
                from stock_move sm
                left join uom_uom uom on uom.id = sm.product_uom
                left join stock_location sl on sl.id = sm.location_dest_id
                where 
                    sm.id in %s
                    and sm.product_id in %s
                    and sm.location_id in %s
                    and sl.usage = 'transit'
                group by 
                    sm.product_id, 
                    sm.location_id
                ), 
                table_outgoing as(
                select 
                    sm.product_id as product_id,
                    sm.location_dest_id as location_id,
                    -1 * sum(sm.quantity * round(1 / uom.factor , 2)) as qty
                from stock_move sm
                left join uom_uom uom on uom.id = sm.product_uom
                left join stock_location sl on sl.id = sm.location_id
                where 
                    sm.id in %s
                    and sm.product_id in %s
                    and sm.location_dest_id in %s
                    and sl.usage = 'transit'
                group by 
                    sm.product_id, 
                    sm.location_dest_id
                ),
                table_main as(
                select * from table_incoming
                union
                select * from table_outgoing
                )
                select 
                    product_id as product_d, 
                    location_id as location_id, 
                    sum(qty) as qty 
                from table_main 
                group by product_id, location_id
                """
        self._cr.execute(query, tuple(args))
        result_transit = self.env.cr.fetchall()
        dict_list = [{'product_id': t[0], 'location_id': t[1], 'at_transit': t[2]} for t in result_transit]
        return dict_list

    def get_internal_moves_record(self, product_ids, location_ids, move_ids):
        """Get Internal Moves"""
        args = [tuple(move_ids), tuple(product_ids.ids), tuple(location_ids.ids),
                tuple(move_ids), tuple(product_ids.ids), tuple(location_ids.ids)]
        query = """
                with table_incoming as(
                select 
                    sm.product_id as product_id,
                    sm.location_dest_id as location_id,
                    sum(sm.quantity * round(1 / uom.factor , 2)) as qty
                from stock_move sm
                left join uom_uom uom on uom.id = sm.product_uom
                left join stock_location sl on sl.id = sm.location_id
                where 
                    sm.id in %s
                    and sm.product_id in %s
                    and sm.location_dest_id in %s
                    and sl.usage = 'internal'
                group by 
                    sm.product_id, 
                    sm.location_dest_id
                ), 
                table_outgoing as(
                select 
                    sm.product_id as product_id,
                    sm.location_id as location_id,
                    -1 * sum(sm.quantity * round(1 / uom.factor , 2)) as qty
                from stock_move sm
                left join uom_uom uom on uom.id = sm.product_uom
                left join stock_location sl on sl.id = sm.location_dest_id
                where 
                    sm.id in %s
                    and sm.product_id in %s
                    and sm.location_id in %s
                    and sl.usage = 'internal'
                group by 
                    sm.product_id, 
                    sm.location_id
                ),
                table_main as(
                select * from table_incoming
                union
                select * from table_outgoing
                )
                select 
                    product_id as product_d, 
                    location_id as location_id, 
                    sum(qty) as qty 
                from table_main 
                group by product_id, location_id
                """
        self._cr.execute(query, tuple(args))
        result_internal = self.env.cr.fetchall()
        dict_list = [{'product_id': t[0], 'location_id': t[1], 'internal': t[2]} for t in result_internal]
        return dict_list

    def get_consume_moves_record(self, product_ids, location_ids, move_ids):
        """Get Consume Moves"""
        args = [tuple(move_ids), tuple(product_ids.ids), tuple(location_ids.ids),
                tuple(move_ids), tuple(product_ids.ids), tuple(location_ids.ids)]
        query = """
                with table_incoming as(
                select 
                    sm.product_id as product_id,
                    sm.location_id as location_id,
                    sum(sm.quantity * round(1 / uom.factor , 2)) as qty
                from stock_move sm
                left join uom_uom uom on uom.id = sm.product_uom
                left join stock_location sl on sl.id = sm.location_dest_id
                where 
                    sm.id in %s
                    and sm.product_id in %s
                    and sm.location_id in %s
                    and sl.usage = 'customer'
                    and sm.scrapped != True
                    and sm.sale_line_id is null
                    and sm.purchase_line_id is null
                group by 
                    sm.product_id, 
                    sm.location_id
                ), 
                table_outgoing as(
                select 
                    sm.product_id as product_id,
                    sm.location_dest_id as location_id,
                    -1 * sum(sm.quantity * round(1 / uom.factor , 2)) as qty
                from stock_move sm
                left join uom_uom uom on uom.id = sm.product_uom
                left join stock_location sl on sl.id = sm.location_id
                where 
                    sm.id in %s
                    and sm.product_id in %s
                    and sm.location_dest_id in %s
                    and sl.usage = 'customer'
                    and sm.scrapped != True
                    and sm.sale_line_id is null
                    and sm.purchase_line_id is null
                group by 
                    sm.product_id, 
                    sm.location_dest_id
                ),
                table_main as(
                select * from table_incoming
                union
                select * from table_outgoing
                )
                select 
                    product_id as product_d, 
                    location_id as location_id, 
                    sum(qty) as qty 
                from table_main 
                group by product_id, location_id
                """
        self._cr.execute(query, tuple(args))
        result_consume = self.env.cr.fetchall()
        dict_list = [{'product_id': t[0], 'location_id': t[1], 'consume': t[2]} for t in result_consume]
        return dict_list

    def get_adjustment_moves_record(self, product_ids, location_ids, move_ids):
        """Get Adjustment Moves"""
        args = [tuple(move_ids), tuple(product_ids.ids), tuple(location_ids.ids),
                tuple(move_ids), tuple(product_ids.ids), tuple(location_ids.ids)]
        query = """
                with table_incoming as(
                select 
                    sm.product_id as product_id,
                    sm.location_dest_id as location_id,
                    sum(sm.quantity * round(1 / uom.factor , 2)) as qty
                from stock_move sm
                left join uom_uom uom on uom.id = sm.product_uom
                where 
                    sm.id in %s
                    and sm.product_id in %s
                    and sm.location_dest_id in %s
                    and sm.is_inventory = True
                group by 
                    sm.product_id, 
                    sm.location_dest_id
                ), 
                table_outgoing as(
                select 
                    sm.product_id as product_id,
                    sm.location_id as location_id,
                    -1 * sum(sm.quantity * round(1 / uom.factor , 2)) as qty
                from stock_move sm
                left join uom_uom uom on uom.id = sm.product_uom
                where 
                    sm.id in %s
                    and sm.product_id in %s
                    and sm.location_id in %s
                    and sm.is_inventory = True
                group by 
                    sm.product_id, 
                    sm.location_id
                ),
                table_main as(
                select * from table_incoming
                union
                select * from table_outgoing
                )
                select 
                    product_id as product_d, 
                    location_id as location_id, 
                    sum(qty) as qty 
                from table_main 
                group by product_id, location_id
                """
        self._cr.execute(query, tuple(args))
        result_adjustment = self.env.cr.fetchall()
        dict_list = [{'product_id': t[0], 'location_id': t[1], 'adjustment': t[2]} for t in result_adjustment]
        return dict_list

    def get_scrapped_moves_record(self, product_ids, location_ids, move_ids):
        """Get Scraped Moves"""
        args = [tuple(move_ids), tuple(product_ids.ids), tuple(location_ids.ids)]
        query = """
                select 
                    sm.product_id as product_id,
                    sm.location_id as location_id,
                    sum(sm.quantity * round(1 / uom.factor , 2)) as qty
                from stock_move sm
                left join uom_uom uom on uom.id = sm.product_uom
                where 
                    sm.id in %s
                    and sm.product_id in %s
                    and sm.location_id in %s
                    and sm.scrapped = True
                group by 
                    sm.product_id, 
                    sm.location_id
                """
        self._cr.execute(query, tuple(args))
        result_scrapped = self.env.cr.fetchall()
        dict_list = [{'product_id': t[0], 'location_id': t[1], 'scrapped': t[2]} for t in result_scrapped]
        return dict_list

    def get_total_report_details(self, from_date, to_date, warehouse_ids, location_ids, product_ids, company_id):
        """Get Total Report Details One"""
        stock_move_obj = self.env['stock.move']
        location_obj = self.env['stock.location']
        product_obj = self.env['product.product']
        if not product_ids:
            product_ids = product_obj.search([
                ('type', '=', 'consu'),
                '|', ('company_id', '=', company_id.id),
                ('company_id', '=', False)
            ])
        if not location_ids:
            location_ids = location_obj.search([
                ('company_id', '=', company_id.id),
                ('usage', '=', 'internal')
            ])
        move_ids = self.get_basic_query_incoming_outgoing_opening(company_id, product_ids, from_date, to_date)
        opening_moves  = []
        purchase_moves = []
        purchase_return_moves = []
        sale_return_moves = []
        sale_moves = []
        at_transit_moves = []
        internal_moves = []
        consume_moves = []
        adjustment_moves = []
        scrapped_moves = []
        if from_date:
            opening_moves = self.get_product_opening_qty(product_ids, location_ids, from_date)
        if not move_ids and not opening_moves:
            raise UserError(_('There is not Entry for these Product.!!'))
        if move_ids:
            purchase_moves = self.get_purchase_move_record(product_ids, location_ids, move_ids)
            purchase_return_moves = self.get_purchase_return_move_record(product_ids, location_ids, move_ids)
            sale_moves = self.get_sale_moves_record(product_ids, location_ids, move_ids)
            sale_return_moves = self.get_sale_return_moves_record(product_ids, location_ids, move_ids)
            at_transit_moves = self.get_at_transit_moves_record(product_ids, location_ids, move_ids)

            internal_moves = self.get_internal_moves_record(product_ids, location_ids, move_ids)
            consume_moves = self.get_consume_moves_record(product_ids, location_ids, move_ids)
            adjustment_moves = self.get_adjustment_moves_record(product_ids, location_ids, move_ids)
            scrapped_moves = self.get_scrapped_moves_record(product_ids, location_ids, move_ids)
        combined_list = opening_moves + purchase_moves + purchase_return_moves + sale_moves + sale_return_moves + at_transit_moves + internal_moves + consume_moves + adjustment_moves + scrapped_moves
        merged_dict = {}
        for item in combined_list:
            key = (item['product_id'], item['location_id'])
            if key not in merged_dict:
                merged_dict[key] = {
                    'product_id': item['product_id'],
                    'location_id': item['location_id'],
                    'opening': 0, 'purchase': 0, 'purchase_return': 0, 'sale': 0, 'sale_return': 0,
                    'internal': 0, 'consume': 0, 'adjustment': 0, 'at_transit': 0, 'scrapped': 0
                }
            if 'opening' in item:
                merged_dict[key]['opening'] = item['opening']
            if 'purchase' in item:
                merged_dict[key]['purchase'] = item['purchase']
            if 'purchase_return' in item:
                merged_dict[key]['purchase_return'] = item['purchase_return']
            if 'sale' in item:
                merged_dict[key]['sale'] = item['sale']
            if 'sale_return' in item:
                merged_dict[key]['sale_return'] = item['sale_return']
            if 'internal' in item:
                merged_dict[key]['internal'] = item['internal']
            if 'consume' in item:
                merged_dict[key]['consume'] = item['consume']
            if 'adjustment' in item:
                merged_dict[key]['adjustment'] = item['adjustment']
            if 'at_transit' in item:
                merged_dict[key]['at_transit'] = item['at_transit']
            if 'scrapped' in item:
                merged_dict[key]['scrapped'] = item['scrapped']
        result_list = list(merged_dict.values())
        list_details = []
        for ln in result_list:
            list_field = ['opening', 'purchase', 'purchase_return', 'sale', 'sale_return', 'consume', 'internal',
                          'adjustment', 'at_transit']
            closing_qty = ln['opening'] + ln['sale_return'] + ln['purchase'] + ln['internal'] + ln['adjustment'] - (
                    ln['sale'] + ln['purchase_return'] + ln['at_transit'] + ln['scrapped'] + ln['consume'])
            if any(ln[field] != 0.0 for field in list_field):
                vals = {
                    'product_id': ln['product_id'],
                    'location_id': ln['location_id'],
                    'opening': ln['opening'],
                    'purchase': ln['purchase'],
                    'purchase_return': ln['purchase_return'],
                    'sale': ln['sale'],
                    'sale_return': ln['sale_return'],
                    'internal': ln['internal'],
                    'consume': ln['consume'],
                    'adjustment': ln['adjustment'],
                    'at_transit': ln['at_transit'],
                    'scrapped': ln['scrapped'],
                    'closing': closing_qty,
                }
                list_details.append(vals)
        return list_details

    def create_data_records(self, from_date, to_date, warehouse_ids, location_ids, product_ids, company_id):
        """Create Data Record"""
        self.env.cr.execute("""delete from stock_report_db_view""")
        report_values = self.get_total_report_details(from_date, to_date, warehouse_ids, location_ids, product_ids, company_id)
        self.env['stock.report.db.view'].create(report_values)
