# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_expense_extract
# 
# Translators:
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-25 09:14+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Czech (https://app.transifex.com/odoo/teams/41243/cs/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: cs\n"
"Plural-Forms: nplurals=4; plural=(n == 1 && n % 1 == 0) ? 0 : (n >= 2 && n <= 4 && n % 1 == 0) ? 1: (n % 1 != 0 ) ? 2 : 3;\n"

#. module: hr_expense_extract
#. odoo-javascript
#: code:addons/hr_expense_extract/static/src/js/tours/expense_tour.js:0
msgid "<b>Wasting time recording your receipts?</b> Let’s try a better way."
msgstr ""

#. module: hr_expense_extract
#: model_terms:web_tour.tour,rainbow_man_message:hr_expense_extract.hr_expense_extract_tour
msgid ""
"<span><b>Congratulations</b>, you are now an expert of Expenses.\n"
"        </span>"
msgstr ""

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_hr_expense__message_needaction
msgid "Action Needed"
msgstr "Vyžadována akce"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_register__amount
msgid "Amount"
msgstr "Částka"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_hr_expense__message_attachment_count
msgid "Attachment Count"
msgstr "Počet příloh"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_register__available_payment_method_line_ids
msgid "Available Payment Method Line"
msgstr "Dostupná položka platební metody"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_hr_expense__extract_can_show_send_button
msgid "Can show the ocr send button"
msgstr "Může zobrazit tlačítko OCR Odeslat"

#. module: hr_expense_extract
#: model_terms:ir.ui.view,arch_db:hr_expense_extract.expense_sample_register_view_form
msgid "Cancel"
msgstr "Zrušit"

#. module: hr_expense_extract
#. odoo-javascript
#: code:addons/hr_expense_extract/static/src/js/tours/expense_tour.js:0
msgid "Choose a receipt."
msgstr "Vyberte potvrzení."

#. module: hr_expense_extract
#: model:ir.model,name:hr_expense_extract.model_res_company
msgid "Companies"
msgstr "Společnosti"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_register__company_id
msgid "Company"
msgstr "Společnost"

#. module: hr_expense_extract
#: model:ir.model,name:hr_expense_extract.model_res_config_settings
msgid "Config Settings"
msgstr "Konfigurační nastavení"

#. module: hr_expense_extract
#: model_terms:ir.ui.view,arch_db:hr_expense_extract.expense_sample_register_view_form
msgid "Create Payment"
msgstr "Vytvořit platbu"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_receipt__create_uid
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_register__create_uid
msgid "Created by"
msgstr "Vytvořeno uživatelem"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_receipt__create_date
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_register__create_date
msgid "Created on"
msgstr "Vytvořeno dne"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_register__currency_id
msgid "Currency"
msgstr "Měna"

#. module: hr_expense_extract
#: model:ir.model.fields.selection,name:hr_expense_extract.selection__res_company__expense_extract_show_ocr_option_selection__auto_send
msgid "Digitize automatically"
msgstr "Digitalizovat automaticky"

#. module: hr_expense_extract
#: model:ir.actions.server,name:hr_expense_extract.hr_expense_parse_action_server
#: model_terms:ir.ui.view,arch_db:hr_expense_extract.hr_expense_extract_view_form
msgid "Digitize document"
msgstr "Digitalizovat dokument"

#. module: hr_expense_extract
#: model:ir.model.fields.selection,name:hr_expense_extract.selection__res_company__expense_extract_show_ocr_option_selection__manual_send
msgid "Digitize on demand only"
msgstr "Digitalizovat pouze na vyžádání"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_receipt__display_name
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_register__display_name
msgid "Display Name"
msgstr "Zobrazovací název"

#. module: hr_expense_extract
#: model:ir.model.fields.selection,name:hr_expense_extract.selection__res_company__expense_extract_show_ocr_option_selection__no_send
msgid "Do not digitize"
msgstr "Nedigitalizovat"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_hr_expense__extract_error_message
msgid "Error message"
msgstr "Chybové hlášení"

#. module: hr_expense_extract
#: model:ir.model,name:hr_expense_extract.model_hr_expense
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_register__sheet_id
msgid "Expense"
msgstr "Výdaj"

#. module: hr_expense_extract
#: model:ir.actions.server,name:hr_expense_extract.ir_cron_update_ocr_status_ir_actions_server
msgid "Expense OCR: Update All Status"
msgstr "OCR výdajů: Aktualizujte veškerý stav"

#. module: hr_expense_extract
#: model:ir.actions.server,name:hr_expense_extract.ir_cron_ocr_validate_ir_actions_server
msgid "Expense OCR: Validate Expenses"
msgstr ""

#. module: hr_expense_extract
#: model:ir.model,name:hr_expense_extract.model_hr_expense_sheet
msgid "Expense Report"
msgstr "Výkaz výdajů"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_res_config_settings__expense_extract_show_ocr_option_selection
msgid "Expense processing option"
msgstr "Možnost zpracování výdajů"

#. module: hr_expense_extract
#: model:ir.model.fields,help:hr_expense_extract.field_hr_expense__sample
msgid "Expenses created from sample receipt"
msgstr ""

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_hr_expense__extract_state_processed
msgid "Extract State Processed"
msgstr "Vytěžování - stav procesu"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_hr_expense__extract_state
msgid "Extract state"
msgstr "Stav extraktu"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_hr_expense__extract_status
msgid "Extract status"
msgstr "Status vytěžování"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_hr_expense__message_follower_ids
msgid "Followers"
msgstr "Odběratelé"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_hr_expense__message_partner_ids
msgid "Followers (Partners)"
msgstr "Odběratelé (partneři)"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_hr_expense__has_message
msgid "Has Message"
msgstr "Má zprávu"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_register__hide_partial
msgid "Hide Partial"
msgstr "Skrýt částečné"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_register__hide_payment_method_line
msgid "Hide Payment Method Line"
msgstr "Skrýt řádek způsobu platby"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_receipt__id
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_register__id
msgid "ID"
msgstr "ID"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_hr_expense__extract_document_uuid
msgid "ID of the request to IAP-OCR"
msgstr "ID požadavku na IAP-OCR"

#. module: hr_expense_extract
#: model:ir.model.fields,help:hr_expense_extract.field_hr_expense__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Pokud zaškrtnuto, nové zprávy vyžadují vaši pozornost."

#. module: hr_expense_extract
#: model:ir.model.fields,help:hr_expense_extract.field_hr_expense__message_has_error
#: model:ir.model.fields,help:hr_expense_extract.field_hr_expense__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Pokud zaškrtnuto, některé zprávy mají chybu při doručení."

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_hr_expense__message_is_follower
msgid "Is Follower"
msgstr "Je odběratel"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_hr_expense__is_in_extractable_state
msgid "Is In Extractable State"
msgstr "Je ve stavu umožňujícím vytěžení"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_register__journal_id
msgid "Journal"
msgstr "Účetní deník"

#. module: hr_expense_extract
#: model:ir.model.fields.selection,name:hr_expense_extract.selection__expense_sample_register__partial_mode__open
msgid "Keep open"
msgstr "Ponechat otevřený zůstatek"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_receipt__write_uid
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_register__write_uid
msgid "Last Updated by"
msgstr "Naposledy upraveno uživatelem"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_receipt__write_date
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_register__write_date
msgid "Last Updated on"
msgstr "Naposledy upraveno dne"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_hr_expense__message_main_attachment_id
msgid "Main Attachment"
msgstr "Hlavní příloha"

#. module: hr_expense_extract
#: model:ir.model.fields,help:hr_expense_extract.field_expense_sample_register__payment_method_line_id
msgid ""
"Manual: Pay or Get paid by any method outside of Odoo.\n"
"Payment Providers: Each payment provider has its own Payment Method. Request a transaction on/to a card thanks to a payment token saved by the partner when buying or subscribing online.\n"
"Check: Pay bills by check and print it from Odoo.\n"
"Batch Deposit: Collect several customer checks at once generating and submitting a batch deposit to your bank. Module account_batch_payment is necessary.\n"
"SEPA Credit Transfer: Pay in the SEPA zone by submitting a SEPA Credit Transfer file to your bank. Module account_iso20022 is necessary.\n"
"SEPA Direct Debit: Get paid in the SEPA zone thanks to a mandate your partner will have granted to you. Module account_iso20022 is necessary.\n"
msgstr ""

#. module: hr_expense_extract
#: model:ir.model.fields.selection,name:hr_expense_extract.selection__expense_sample_register__partial_mode__paid
msgid "Mark as fully paid"
msgstr "Označit jako plně zaplacené"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_register__memo
msgid "Memo"
msgstr "Poznámka"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_hr_expense__message_has_error
msgid "Message Delivery error"
msgstr "Chyba při doručování zprávy"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_hr_expense__message_ids
msgid "Messages"
msgstr "Zprávy"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_hr_expense__message_needaction_counter
msgid "Number of Actions"
msgstr "Počet akcí"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_hr_expense__message_has_error_counter
msgid "Number of errors"
msgstr "Počet chyb"

#. module: hr_expense_extract
#: model:ir.model.fields,help:hr_expense_extract.field_hr_expense__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Počet zpráv vyžadujících akci"

#. module: hr_expense_extract
#: model:ir.model.fields,help:hr_expense_extract.field_hr_expense__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Počet zpráv s chybou při doručení"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_register__date
msgid "Payment Date"
msgstr "Datum platby"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_register__partial_mode
msgid "Payment Difference"
msgstr "Rozdíl platby"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_register__payment_method_line_id
msgid "Payment Method"
msgstr "Platební metoda"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_hr_expense__rating_ids
msgid "Ratings"
msgstr "Hodnocení"

#. module: hr_expense_extract
#: model_terms:ir.ui.view,arch_db:hr_expense_extract.expense_sample_register_view_form
msgid "Register Payment"
msgstr "Vytvořit platbu"

#. module: hr_expense_extract
#: model:ir.actions.act_window,name:hr_expense_extract.action_expense_sample_register
msgid "Register Sample Payment"
msgstr "Zaregistrujte si ukázkovou platbu"

#. module: hr_expense_extract
#: model:ir.model,name:hr_expense_extract.model_expense_sample_register
msgid "Register Sample Payments"
msgstr "Vytvořit vzorové platby"

#. module: hr_expense_extract
#. odoo-javascript
#: code:addons/hr_expense_extract/static/src/js/tours/expense_tour.js:0
msgid "Report this expense to your manager for validation."
msgstr "Nahlaste tento výdaj svému manažerovi k ověření."

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_hr_expense__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Chyba doručení SMS"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_hr_expense__sample
msgid "Sample"
msgstr "Vzorek"

#. module: hr_expense_extract
#. odoo-python
#: code:addons/hr_expense_extract/wizard/expense_sample_receipt.py:0
msgid "Sample Employee"
msgstr "Ukázkový zaměstnanec"

#. module: hr_expense_extract
#. odoo-python
#: code:addons/hr_expense_extract/wizard/expense_sample_receipt.py:0
msgid "Sample Receipt: %s"
msgstr "Ukázkový příjem: %s"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_res_company__expense_extract_show_ocr_option_selection
msgid "Send mode on expense attachments"
msgstr "Režim odesílání na přílohách výdajů"

#. module: hr_expense_extract
#: model_terms:ir.ui.view,arch_db:hr_expense_extract.expense_sample_receipt_view_form
msgid "Try Expense Digitization"
msgstr ""

#. module: hr_expense_extract
#: model:ir.actions.act_window,name:hr_expense_extract.action_expense_sample_receipt
msgid "Try Sample Receipt"
msgstr "Vyzkoušejte vzorový příjem"

#. module: hr_expense_extract
#: model:ir.model,name:hr_expense_extract.model_expense_sample_receipt
msgid "Try Sample Receipts"
msgstr "Vyzkoušet vzorové účtenky"

#. module: hr_expense_extract
#. odoo-javascript
#: code:addons/hr_expense_extract/static/src/js/tours/expense_tour.js:0
msgid "Try the AI with a sample receipt."
msgstr "Vyzkoušejte AI se vzorovým potvrzením."

#. module: hr_expense_extract
#. odoo-python
#: code:addons/hr_expense_extract/models/hr_expense.py:0
msgid "Upload or drop an expense receipt"
msgstr ""

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_hr_expense__website_message_ids
msgid "Website Messages"
msgstr "Webové zprávy"

#. module: hr_expense_extract
#: model:ir.model.fields,help:hr_expense_extract.field_hr_expense__website_message_ids
msgid "Website communication history"
msgstr "Webová historie komunikace"

#. module: hr_expense_extract
#. odoo-python
#: code:addons/hr_expense_extract/models/hr_expense.py:0
msgid "You can't mix sample expenses and regular ones"
msgstr "Nemůžete kombinovat výdaje vzorku a běžné"

#. module: hr_expense_extract
#. odoo-python
#: code:addons/hr_expense_extract/models/hr_expense.py:0
msgid "You cannot send a expense that is not in draft state!"
msgstr "Nelze odeslat výdaj, který není ve stavu konceptu!"

#. module: hr_expense_extract
#. odoo-javascript
#: code:addons/hr_expense_extract/static/src/js/tours/expense_tour.js:0
msgid "Your manager will have to approve (or refuse) your expense reports."
msgstr ""
"Váš nadřízený bude muset schválit (nebo zamítnout) vaše zprávy o výdajích."

#. module: hr_expense_extract
#. odoo-python
#: code:addons/hr_expense_extract/models/hr_expense.py:0
msgid "try a sample receipt"
msgstr ""
