# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_expense_extract
# 
# Translators:
# Wil <PERSON>, 2024
# <PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-25 09:14+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON>, 2025\n"
"Language-Team: Polish (https://app.transifex.com/odoo/teams/41243/pl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: pl\n"
"Plural-Forms: nplurals=4; plural=(n==1 ? 0 : (n%10>=2 && n%10<=4) && (n%100<12 || n%100>14) ? 1 : n!=1 && (n%10>=0 && n%10<=1) || (n%10>=5 && n%10<=9) || (n%100>=12 && n%100<=14) ? 2 : 3);\n"

#. module: hr_expense_extract
#. odoo-javascript
#: code:addons/hr_expense_extract/static/src/js/tours/expense_tour.js:0
msgid "<b>Wasting time recording your receipts?</b> Let’s try a better way."
msgstr ""

#. module: hr_expense_extract
#: model_terms:web_tour.tour,rainbow_man_message:hr_expense_extract.hr_expense_extract_tour
msgid ""
"<span><b>Congratulations</b>, you are now an expert of Expenses.\n"
"        </span>"
msgstr ""

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_hr_expense__message_needaction
msgid "Action Needed"
msgstr "Wymagane działanie"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_register__amount
msgid "Amount"
msgstr "Kwota"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_hr_expense__message_attachment_count
msgid "Attachment Count"
msgstr "Liczba załączników"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_register__available_payment_method_line_ids
msgid "Available Payment Method Line"
msgstr "Dostępna pozycja metody płatności"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_hr_expense__extract_can_show_send_button
msgid "Can show the ocr send button"
msgstr "Można wyświetlić przycisk wysyłania ocr"

#. module: hr_expense_extract
#: model_terms:ir.ui.view,arch_db:hr_expense_extract.expense_sample_register_view_form
msgid "Cancel"
msgstr "Anuluj"

#. module: hr_expense_extract
#. odoo-javascript
#: code:addons/hr_expense_extract/static/src/js/tours/expense_tour.js:0
msgid "Choose a receipt."
msgstr "Wybierz paragon."

#. module: hr_expense_extract
#: model:ir.model,name:hr_expense_extract.model_res_company
msgid "Companies"
msgstr "Firmy"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_register__company_id
msgid "Company"
msgstr "Firma"

#. module: hr_expense_extract
#: model:ir.model,name:hr_expense_extract.model_res_config_settings
msgid "Config Settings"
msgstr "Ustawienia konfiguracji"

#. module: hr_expense_extract
#: model_terms:ir.ui.view,arch_db:hr_expense_extract.expense_sample_register_view_form
msgid "Create Payment"
msgstr "Utwórz płatność"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_receipt__create_uid
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_register__create_uid
msgid "Created by"
msgstr "Utworzył(a)"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_receipt__create_date
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_register__create_date
msgid "Created on"
msgstr "Data utworzenia"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_register__currency_id
msgid "Currency"
msgstr "Waluta"

#. module: hr_expense_extract
#: model:ir.model.fields.selection,name:hr_expense_extract.selection__res_company__expense_extract_show_ocr_option_selection__auto_send
msgid "Digitize automatically"
msgstr "Digitalizacja automatyczna"

#. module: hr_expense_extract
#: model:ir.actions.server,name:hr_expense_extract.hr_expense_parse_action_server
#: model_terms:ir.ui.view,arch_db:hr_expense_extract.hr_expense_extract_view_form
msgid "Digitize document"
msgstr "Digitalizuj dokument"

#. module: hr_expense_extract
#: model:ir.model.fields.selection,name:hr_expense_extract.selection__res_company__expense_extract_show_ocr_option_selection__manual_send
msgid "Digitize on demand only"
msgstr "Digitalizacja tylko na żądanie"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_receipt__display_name
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_register__display_name
msgid "Display Name"
msgstr "Nazwa wyświetlana"

#. module: hr_expense_extract
#: model:ir.model.fields.selection,name:hr_expense_extract.selection__res_company__expense_extract_show_ocr_option_selection__no_send
msgid "Do not digitize"
msgstr "Nie digitalizować"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_hr_expense__extract_error_message
msgid "Error message"
msgstr "Komunikat o błędzie"

#. module: hr_expense_extract
#: model:ir.model,name:hr_expense_extract.model_hr_expense
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_register__sheet_id
msgid "Expense"
msgstr "Wydatek"

#. module: hr_expense_extract
#: model:ir.actions.server,name:hr_expense_extract.ir_cron_update_ocr_status_ir_actions_server
msgid "Expense OCR: Update All Status"
msgstr "OCR wydatków: Aktualizuj wszystkie statusy"

#. module: hr_expense_extract
#: model:ir.actions.server,name:hr_expense_extract.ir_cron_ocr_validate_ir_actions_server
msgid "Expense OCR: Validate Expenses"
msgstr "OCR wydatków: walidacja wydatków"

#. module: hr_expense_extract
#: model:ir.model,name:hr_expense_extract.model_hr_expense_sheet
msgid "Expense Report"
msgstr "Raport wydatków"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_res_config_settings__expense_extract_show_ocr_option_selection
msgid "Expense processing option"
msgstr "Opcja przetwarzania wydatków"

#. module: hr_expense_extract
#: model:ir.model.fields,help:hr_expense_extract.field_hr_expense__sample
msgid "Expenses created from sample receipt"
msgstr ""

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_hr_expense__extract_state_processed
msgid "Extract State Processed"
msgstr "Przetworzono status ekstrakcji"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_hr_expense__extract_state
msgid "Extract state"
msgstr "Wyciągnij stan"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_hr_expense__extract_status
msgid "Extract status"
msgstr "Status ekstrakcji"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_hr_expense__message_follower_ids
msgid "Followers"
msgstr "Obserwatorzy"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_hr_expense__message_partner_ids
msgid "Followers (Partners)"
msgstr "Obserwatorzy (partnerzy)"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_hr_expense__has_message
msgid "Has Message"
msgstr "Ma wiadomość"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_register__hide_partial
msgid "Hide Partial"
msgstr "Ukryj częściowo"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_register__hide_payment_method_line
msgid "Hide Payment Method Line"
msgstr "Ukryj linię metody płatności"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_receipt__id
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_register__id
msgid "ID"
msgstr "ID"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_hr_expense__extract_document_uuid
msgid "ID of the request to IAP-OCR"
msgstr "ID wniosku do IAP-OCR"

#. module: hr_expense_extract
#: model:ir.model.fields,help:hr_expense_extract.field_hr_expense__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Jeśli zaznaczone, nowe wiadomości wymagają twojej uwagi."

#. module: hr_expense_extract
#: model:ir.model.fields,help:hr_expense_extract.field_hr_expense__message_has_error
#: model:ir.model.fields,help:hr_expense_extract.field_hr_expense__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr ""
"Jeśli zaznaczone, niektóre wiadomości napotkały błędy podczas doręczenia."

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_hr_expense__message_is_follower
msgid "Is Follower"
msgstr "Jest obserwatorem"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_hr_expense__is_in_extractable_state
msgid "Is In Extractable State"
msgstr "Jest w stanie ekstraktowalnym"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_register__journal_id
msgid "Journal"
msgstr "Dziennik"

#. module: hr_expense_extract
#: model:ir.model.fields.selection,name:hr_expense_extract.selection__expense_sample_register__partial_mode__open
msgid "Keep open"
msgstr "Pozostaw otwarte"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_receipt__write_uid
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_register__write_uid
msgid "Last Updated by"
msgstr "Ostatnio aktualizowane przez"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_receipt__write_date
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_register__write_date
msgid "Last Updated on"
msgstr "Data ostatniej aktualizacji"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_hr_expense__message_main_attachment_id
msgid "Main Attachment"
msgstr "Główny załącznik"

#. module: hr_expense_extract
#: model:ir.model.fields,help:hr_expense_extract.field_expense_sample_register__payment_method_line_id
msgid ""
"Manual: Pay or Get paid by any method outside of Odoo.\n"
"Payment Providers: Each payment provider has its own Payment Method. Request a transaction on/to a card thanks to a payment token saved by the partner when buying or subscribing online.\n"
"Check: Pay bills by check and print it from Odoo.\n"
"Batch Deposit: Collect several customer checks at once generating and submitting a batch deposit to your bank. Module account_batch_payment is necessary.\n"
"SEPA Credit Transfer: Pay in the SEPA zone by submitting a SEPA Credit Transfer file to your bank. Module account_iso20022 is necessary.\n"
"SEPA Direct Debit: Get paid in the SEPA zone thanks to a mandate your partner will have granted to you. Module account_iso20022 is necessary.\n"
msgstr ""
"Instrukcja: Płać lub otrzymuj płatności za pomocą dowolnej metody poza Odoo.\n"
"Dostawcy płatności: Każdy dostawca płatności ma swoją własną Metodę płatności. Zażądaj transakcji na/do karty dzięki tokenowi płatniczemu zapisanemu przez partnera podczas zakupu lub subskrypcji online.\n"
"Czek: Płać rachunki czekiem i drukuj go z Odoo.\n"
"Batch Deposit: Zbierz kilka czeków klientów jednocześnie generując i składając depozyt wsadowy do swojego banku. Konieczny jest moduł account_batch_payment.\n"
"SEPA Credit Transfer: Płać w strefie SEPA składając plik SEPA Credit Transfer do swojego banku. Moduł account_iso20022 jest niezbędny.\n"
"SEPA Direct Debit: Otrzymuj płatności w strefie SEPA dzięki mandatowi, który udzieli Ci Twój partner. Moduł account_iso20022 jest niezbędny.\n"

#. module: hr_expense_extract
#: model:ir.model.fields.selection,name:hr_expense_extract.selection__expense_sample_register__partial_mode__paid
msgid "Mark as fully paid"
msgstr "Oznaczone jako w pełni opłacone"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_register__memo
msgid "Memo"
msgstr "Notatka"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_hr_expense__message_has_error
msgid "Message Delivery error"
msgstr "Błąd doręczenia wiadomości"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_hr_expense__message_ids
msgid "Messages"
msgstr "Wiadomości"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_hr_expense__message_needaction_counter
msgid "Number of Actions"
msgstr "Liczba akcji"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_hr_expense__message_has_error_counter
msgid "Number of errors"
msgstr "Liczba błędów"

#. module: hr_expense_extract
#: model:ir.model.fields,help:hr_expense_extract.field_hr_expense__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Liczba wiadomości wymagających akcji"

#. module: hr_expense_extract
#: model:ir.model.fields,help:hr_expense_extract.field_hr_expense__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Liczba wiadomości z błędami przy doręczeniu"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_register__date
msgid "Payment Date"
msgstr "Data płatności"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_register__partial_mode
msgid "Payment Difference"
msgstr "Różnica w płatności"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_register__payment_method_line_id
msgid "Payment Method"
msgstr "Metoda płatności"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_hr_expense__rating_ids
msgid "Ratings"
msgstr "Oceny"

#. module: hr_expense_extract
#: model_terms:ir.ui.view,arch_db:hr_expense_extract.expense_sample_register_view_form
msgid "Register Payment"
msgstr "Rejestruj płatność"

#. module: hr_expense_extract
#: model:ir.actions.act_window,name:hr_expense_extract.action_expense_sample_register
msgid "Register Sample Payment"
msgstr "Zarejestruj przykładową płatność"

#. module: hr_expense_extract
#: model:ir.model,name:hr_expense_extract.model_expense_sample_register
msgid "Register Sample Payments"
msgstr "Zarejestruj przykładowe płatności"

#. module: hr_expense_extract
#. odoo-javascript
#: code:addons/hr_expense_extract/static/src/js/tours/expense_tour.js:0
msgid "Report this expense to your manager for validation."
msgstr "Zgłoś ten wydatek swojemu przełożonemu w celu zatwierdzenia."

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_hr_expense__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Błąd dostarczenia wiadomości SMS"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_hr_expense__sample
msgid "Sample"
msgstr "Próbka"

#. module: hr_expense_extract
#. odoo-python
#: code:addons/hr_expense_extract/wizard/expense_sample_receipt.py:0
msgid "Sample Employee"
msgstr "Przykładowy pracownik"

#. module: hr_expense_extract
#. odoo-python
#: code:addons/hr_expense_extract/wizard/expense_sample_receipt.py:0
msgid "Sample Receipt: %s"
msgstr "Przykładowy paragon:  %s"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_res_company__expense_extract_show_ocr_option_selection
msgid "Send mode on expense attachments"
msgstr "Tryb wysyłania załączników wydatków"

#. module: hr_expense_extract
#: model_terms:ir.ui.view,arch_db:hr_expense_extract.expense_sample_receipt_view_form
msgid "Try Expense Digitization"
msgstr ""

#. module: hr_expense_extract
#: model:ir.actions.act_window,name:hr_expense_extract.action_expense_sample_receipt
msgid "Try Sample Receipt"
msgstr "Wypróbuj przykładowy paragon"

#. module: hr_expense_extract
#: model:ir.model,name:hr_expense_extract.model_expense_sample_receipt
msgid "Try Sample Receipts"
msgstr "Wypróbuj przykładowe paragony"

#. module: hr_expense_extract
#. odoo-javascript
#: code:addons/hr_expense_extract/static/src/js/tours/expense_tour.js:0
msgid "Try the AI with a sample receipt."
msgstr "Wypróbuj AI z przykładowym paragonem."

#. module: hr_expense_extract
#. odoo-python
#: code:addons/hr_expense_extract/models/hr_expense.py:0
msgid "Upload or drop an expense receipt"
msgstr ""

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_hr_expense__website_message_ids
msgid "Website Messages"
msgstr "Wiadomości"

#. module: hr_expense_extract
#: model:ir.model.fields,help:hr_expense_extract.field_hr_expense__website_message_ids
msgid "Website communication history"
msgstr "Historia komunikacji"

#. module: hr_expense_extract
#. odoo-python
#: code:addons/hr_expense_extract/models/hr_expense.py:0
msgid "You can't mix sample expenses and regular ones"
msgstr "Nie można mieszać wydatków próbnych z normalnymi"

#. module: hr_expense_extract
#. odoo-python
#: code:addons/hr_expense_extract/models/hr_expense.py:0
msgid "You cannot send a expense that is not in draft state!"
msgstr "Nie można wysłać wydatku, który nie jest w wersji roboczej!"

#. module: hr_expense_extract
#. odoo-javascript
#: code:addons/hr_expense_extract/static/src/js/tours/expense_tour.js:0
msgid "Your manager will have to approve (or refuse) your expense reports."
msgstr ""
"Twój przełożony będzie musiał zaakceptować (lub odrzucić) Twoje raporty "
"wydatków."

#. module: hr_expense_extract
#. odoo-python
#: code:addons/hr_expense_extract/models/hr_expense.py:0
msgid "try a sample receipt"
msgstr ""
