# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_helpdesk_knowledge
# 
# Translators:
# jabiri7, 2024
# <PERSON><PERSON><PERSON>, 2024
# <PERSON><PERSON>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <man<PERSON><PERSON>@outlook.com>, 2024
# <PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:29+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON>, 2024\n"
"Language-Team: Catalan (https://app.transifex.com/odoo/teams/41243/ca/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ca\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: website_helpdesk_knowledge
#: model_terms:ir.ui.view,arch_db:website_helpdesk_knowledge.search_result
msgid ", by"
msgstr ", per"

#. module: website_helpdesk_knowledge
#: model_terms:ir.ui.view,arch_db:website_helpdesk_knowledge.search_result
msgid "<i class=\"fa fa-fw fa-2x fa-book\" title=\"Knowledge Article\"/>"
msgstr "<i class=\"fa fa-fw fa-2x fa-book\" title=\"Knowledge Article\"/>"

#. module: website_helpdesk_knowledge
#: model:ir.model.fields,field_description:website_helpdesk_knowledge.field_helpdesk_team__website_article_id
msgid "Article"
msgstr "Article"

#. module: website_helpdesk_knowledge
#: model:ir.model.fields,help:website_helpdesk_knowledge.field_helpdesk_team__website_article_id
msgid ""
"Article on which customers will land by default, and to which the search "
"will be restricted."
msgstr ""
"Article sobre el qual els clients aterraran per defecte i al qual es "
"restringirà la cerca."

#. module: website_helpdesk_knowledge
#. odoo-python
#: code:addons/website_helpdesk_knowledge/models/helpdesk.py:0
#: model_terms:ir.ui.view,arch_db:website_helpdesk_knowledge.knowledge_base_articles_card
msgid "Articles"
msgstr "Articles"

#. module: website_helpdesk_knowledge
#: model_terms:ir.ui.view,arch_db:website_helpdesk_knowledge.knowledge_base_articles_card
msgid "Browse articles"
msgstr ""

#. module: website_helpdesk_knowledge
#: model_terms:ir.ui.view,arch_db:website_helpdesk_knowledge.knowledge_base_articles_card
msgid "Favorites"
msgstr "Favorits"

#. module: website_helpdesk_knowledge
#: model:ir.model,name:website_helpdesk_knowledge.model_helpdesk_team
msgid "Helpdesk Team"
msgstr "Equip de helpdesk"

#. module: website_helpdesk_knowledge
#: model:ir.model,name:website_helpdesk_knowledge.model_knowledge_article
msgid "Knowledge Article"
msgstr "Article de coneixement"

#. module: website_helpdesk_knowledge
#: model_terms:ir.ui.view,arch_db:website_helpdesk_knowledge.knowledge_base_articles_card
msgid "Quick Links"
msgstr ""

#. module: website_helpdesk_knowledge
#: model_terms:ir.ui.view,arch_db:website_helpdesk_knowledge.knowledge_base_articles_card
msgid "Search our documentation for answers to common questions"
msgstr ""

#. module: website_helpdesk_knowledge
#. odoo-python
#: code:addons/website_helpdesk_knowledge/models/knowledge_article.py:0
msgid ""
"You cannot delete, unpublish or set a parent on an article that is used by a"
" helpdesk team."
msgstr ""
"No podeu suprimir, despublicar o establir un pare en un article que utilitza"
" un equip del centre d'ajuda."
