# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_helpdesk_knowledge
# 
# Translators:
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:29+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Indonesian (https://app.transifex.com/odoo/teams/41243/id/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: id\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: website_helpdesk_knowledge
#: model_terms:ir.ui.view,arch_db:website_helpdesk_knowledge.search_result
msgid ", by"
msgstr ", oleh"

#. module: website_helpdesk_knowledge
#: model_terms:ir.ui.view,arch_db:website_helpdesk_knowledge.search_result
msgid "<i class=\"fa fa-fw fa-2x fa-book\" title=\"Knowledge Article\"/>"
msgstr "<i class=\"fa fa-fw fa-2x fa-book\" title=\"Artikel Pengetahuan\"/>"

#. module: website_helpdesk_knowledge
#: model:ir.model.fields,field_description:website_helpdesk_knowledge.field_helpdesk_team__website_article_id
msgid "Article"
msgstr "Artikel"

#. module: website_helpdesk_knowledge
#: model:ir.model.fields,help:website_helpdesk_knowledge.field_helpdesk_team__website_article_id
msgid ""
"Article on which customers will land by default, and to which the search "
"will be restricted."
msgstr ""
"Artikel yang pelanggan akan secara default temukan, dan merupakan artikel "
"yang di mana digunakan sebagai batasan pencarian."

#. module: website_helpdesk_knowledge
#. odoo-python
#: code:addons/website_helpdesk_knowledge/models/helpdesk.py:0
#: model_terms:ir.ui.view,arch_db:website_helpdesk_knowledge.knowledge_base_articles_card
msgid "Articles"
msgstr "Artikel"

#. module: website_helpdesk_knowledge
#: model_terms:ir.ui.view,arch_db:website_helpdesk_knowledge.knowledge_base_articles_card
msgid "Browse articles"
msgstr "Browse artikel"

#. module: website_helpdesk_knowledge
#: model_terms:ir.ui.view,arch_db:website_helpdesk_knowledge.knowledge_base_articles_card
msgid "Favorites"
msgstr "Favorit"

#. module: website_helpdesk_knowledge
#: model:ir.model,name:website_helpdesk_knowledge.model_helpdesk_team
msgid "Helpdesk Team"
msgstr "Tim Helpdesk"

#. module: website_helpdesk_knowledge
#: model:ir.model,name:website_helpdesk_knowledge.model_knowledge_article
msgid "Knowledge Article"
msgstr "Artikel Pengetahuan"

#. module: website_helpdesk_knowledge
#: model_terms:ir.ui.view,arch_db:website_helpdesk_knowledge.knowledge_base_articles_card
msgid "Quick Links"
msgstr "Link-Link Cepat"

#. module: website_helpdesk_knowledge
#: model_terms:ir.ui.view,arch_db:website_helpdesk_knowledge.knowledge_base_articles_card
msgid "Search our documentation for answers to common questions"
msgstr "Cari dokumentasi kami untuk jawaban mengenai pertanyaan umum"

#. module: website_helpdesk_knowledge
#. odoo-python
#: code:addons/website_helpdesk_knowledge/models/knowledge_article.py:0
msgid ""
"You cannot delete, unpublish or set a parent on an article that is used by a"
" helpdesk team."
msgstr ""
"Anda tidak dapat menghapus, menghilangkan publikasi atau menetapkan induk "
"dari artikel yang digunakan oleh tim helpdesk."
