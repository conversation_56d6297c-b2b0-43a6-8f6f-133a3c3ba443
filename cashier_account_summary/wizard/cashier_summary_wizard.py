from odoo import models, fields, api, _
from datetime import datetime


class CashierSummary<PERSON>izard(models.TransientModel):
    _name = 'cashier.summary.wizard'
    _description = 'Cashier Summary Report Wizard'

    date_from = fields.Date(string='From Date', required=True)
    date_to = fields.Date(string='To Date', required=True)
    cashier_ids = fields.Many2many('res.users', string='Cashiers',
                                   domain=lambda self: [
                                       ('groups_id', 'in', self.env.ref('cashier_closing.group_cashier_user').id)])

    def action_generate_report(self):
        self.ensure_one()
        data = {
            'date_from': self.date_from,
            'date_to': self.date_to,
            'cashier_ids': self.cashier_ids.ids,
        }

        # Generate report lines
        vals = self.env['cashier.summary.report'].get_report_data(data)
        vals = [{**line, 'date_from': self.date_from, 'date_to': self.date_to} for line in vals]
        report_lines = self.env['cashier.summary.report'].create(vals)

        return {
            'name': 'Cashier Summary Report',
            'type': 'ir.actions.act_window',
            'res_model': 'cashier.summary.report',
            'view_mode': 'list',
            'view_id': self.env.ref('cashier_account_summary.view_cashier_summary_report_tree').id,
            'target': 'current',
            'domain': [('id', 'in', report_lines.ids)]
        }

    def get_report_data(self):
        self.ensure_one()
        data = {
            'date_from': self.date_from,
            'date_to': self.date_to,
            'cashier_ids': self.cashier_ids.ids,
        }
        vals = self.env['cashier.summary.report'].get_report_data(data)
        return self.env['cashier.summary.report'].create(vals)


    def action_export_pdf(self):
        self.ensure_one()
        return self.env.ref('cashier_account_summary.action_report_cashier_summary').report_action(self)

    def action_export_excel(self):
        self.ensure_one()
        data = {
            'date_from': self.date_from,
            'date_to': self.date_to,
            'cashier_ids': self.cashier_ids.ids,
        }
        return self.env.ref('cashier_account_summary.action_report_cashier_summary_xlsx').report_action(self, data=data)