# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_payroll_account
# 
# Translators:
# <PERSON><PERSON>, 2024
# hr<PERSON><PERSON> si<PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON>, 2024
# <PERSON><PERSON><PERSON> <ka<PERSON><PERSON>.ton<PERSON>@storm.hr>, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:27+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: Croatian (https://app.transifex.com/odoo/teams/41243/hr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: hr\n"
"Plural-Forms: nplurals=3; plural=n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2;\n"

#. module: hr_payroll_account
#: model:ir.model.fields,field_description:hr_payroll_account.field_account_bank_statement_line__payslip_count
#: model:ir.model.fields,field_description:hr_payroll_account.field_account_move__payslip_count
msgid "# of Payslips"
msgstr ""

#. module: hr_payroll_account
#: model_terms:ir.ui.view,arch_db:hr_payroll_account.hr_payslip_run_view_form
#: model_terms:ir.ui.view,arch_db:hr_payroll_account.hr_payslip_view_form
msgid ""
"<span class=\"o_stat_text\" invisible=\"move_state != 'draft'\">Journal Entry <br/>(Draft)</span>\n"
"                        <span class=\"o_stat_text\" invisible=\"move_state != 'posted'\">Journal Entry <br/>(Posted)</span>\n"
"                        <span class=\"o_stat_text\" invisible=\"move_state != 'cancel'\">Journal Entry <br/>(Canceled)</span>"
msgstr ""

#. module: hr_payroll_account
#: model_terms:ir.ui.view,arch_db:hr_payroll_account.view_move_form_inherit
msgid "<span class=\"o_stat_text\">Payslip</span>"
msgstr ""

#. module: hr_payroll_account
#: model:ir.model,name:hr_payroll_account.model_account_chart_template
msgid "Account Chart Template"
msgstr "Predložak kontnog plana"

#. module: hr_payroll_account
#. odoo-python
#: code:addons/hr_payroll_account/models/account_chart_template.py:0
msgid "Account Payslip Houserental"
msgstr ""

#. module: hr_payroll_account
#: model_terms:ir.ui.view,arch_db:hr_payroll_account.hr_contract_view_form
#: model_terms:ir.ui.view,arch_db:hr_payroll_account.hr_salary_rule_view_form
msgid "Accounting"
msgstr "Računovodstvo"

#. module: hr_payroll_account
#: model:ir.model.fields,field_description:hr_payroll_account.field_hr_payslip__move_id
msgid "Accounting Entry"
msgstr "Temeljnica"

#. module: hr_payroll_account
#. odoo-python
#: code:addons/hr_payroll_account/models/hr_payslip.py:0
msgid "Adjustment Entry"
msgstr "Knjiženje ispravka"

#. module: hr_payroll_account
#: model:ir.model.fields,field_description:hr_payroll_account.field_hr_contract__analytic_account_id
#: model:ir.model.fields,field_description:hr_payroll_account.field_hr_contract_history__analytic_account_id
#: model:ir.model.fields,field_description:hr_payroll_account.field_hr_salary_rule__analytic_account_id
msgid "Analytic Account"
msgstr "Konto analitike"

#. module: hr_payroll_account
#: model_terms:ir.ui.view,arch_db:hr_payroll_account.hr_payslip_run_view_form
#: model_terms:ir.ui.view,arch_db:hr_payroll_account.hr_payslip_view_form
msgid "Are you sure you want to proceed?"
msgstr ""

#. module: hr_payroll_account
#: model:ir.actions.act_window,name:hr_payroll_account.action_res_partner_bank_account_form
#: model:ir.model,name:hr_payroll_account.model_res_partner_bank
#: model:ir.ui.menu,name:hr_payroll_account.menu_hr_employee_bank_account
msgid "Bank Accounts"
msgstr "Bankovni računi"

#. module: hr_payroll_account
#: model_terms:ir.ui.view,arch_db:hr_payroll_account.res_config_settings_view_form
msgid "Batch Account Move Lines"
msgstr ""

#. module: hr_payroll_account
#: model:ir.model.fields,field_description:hr_payroll_account.field_hr_payslip__batch_payroll_move_lines
#: model:ir.model.fields,field_description:hr_payroll_account.field_res_company__batch_payroll_move_lines
#: model:ir.model.fields,field_description:hr_payroll_account.field_res_config_settings__batch_payroll_move_lines
msgid "Batch Payroll Move Lines"
msgstr ""

#. module: hr_payroll_account
#: model:ir.model,name:hr_payroll_account.model_res_company
msgid "Companies"
msgstr "Tvrtke"

#. module: hr_payroll_account
#: model:ir.model,name:hr_payroll_account.model_res_config_settings
msgid "Config Settings"
msgstr "Konfiguracijske postavke"

#. module: hr_payroll_account
#: model:ir.model,name:hr_payroll_account.model_hr_contract_history
msgid "Contract history"
msgstr "Povijest ugovora"

#. module: hr_payroll_account
#: model_terms:ir.ui.view,arch_db:hr_payroll_account.hr_payslip_run_view_form
#: model_terms:ir.ui.view,arch_db:hr_payroll_account.hr_payslip_view_form
msgid "Create Draft Entry"
msgstr "Kreirajte temeljnicu"

#. module: hr_payroll_account
#: model_terms:ir.actions.act_window,help:hr_payroll_account.action_res_partner_bank_account_form
msgid "Create a Bank Account"
msgstr "Kreiranje bankovnog računa."

#. module: hr_payroll_account
#: model:ir.model.fields,field_description:hr_payroll_account.field_hr_salary_rule__account_credit
msgid "Credit Account"
msgstr "Kreditni račun"

#. module: hr_payroll_account
#: model:ir.model.fields,field_description:hr_payroll_account.field_hr_payslip_line__credit_tag_ids
#: model:ir.model.fields,field_description:hr_payroll_account.field_hr_salary_rule__credit_tag_ids
msgid "Credit Tax Grids"
msgstr ""

#. module: hr_payroll_account
#: model:ir.model.fields,field_description:hr_payroll_account.field_hr_payslip__date
msgid "Date Account"
msgstr "Datum računa"

#. module: hr_payroll_account
#: model:ir.model.fields,field_description:hr_payroll_account.field_hr_salary_rule__account_debit
msgid "Debit Account"
msgstr "Debitni račun"

#. module: hr_payroll_account
#: model:ir.model.fields,field_description:hr_payroll_account.field_hr_payslip_line__debit_tag_ids
#: model:ir.model.fields,field_description:hr_payroll_account.field_hr_salary_rule__debit_tag_ids
msgid "Debit Tax Grids"
msgstr ""

#. module: hr_payroll_account
#: model:ir.model,name:hr_payroll_account.model_hr_contract
msgid "Employee Contract"
msgstr "Ugovor djelatnika"

#. module: hr_payroll_account
#: model_terms:ir.ui.view,arch_db:hr_payroll_account.view_partner_bank_search_inherit
msgid "Employees"
msgstr "Djelatnici"

#. module: hr_payroll_account
#: model:ir.model.fields,help:hr_payroll_account.field_res_config_settings__batch_payroll_move_lines
#: model_terms:ir.ui.view,arch_db:hr_payroll_account.res_config_settings_view_form
msgid ""
"Enable this option to merge all the accounting entries for the same period "
"into a single account move line. This will anonymize the accounting entries "
"but also disable single payment generations."
msgstr ""

#. module: hr_payroll_account
#: model:ir.model.fields,help:hr_payroll_account.field_hr_salary_rule__split_move_lines
msgid ""
"Enable this option to split the accountig entries for this rule according to"
" the payslip line name. It could be useful for deduction/reimbursement or "
"salary attachments for instance."
msgstr ""

#. module: hr_payroll_account
#: model_terms:ir.actions.act_window,help:hr_payroll_account.action_res_partner_bank_account_form
msgid ""
"From here you can manage all bank accounts linked to you and your contacts."
msgstr ""
"Odavde možete upravljati svim bankovnim računima povezanim s vama i vašim "
"kontaktima."

#. module: hr_payroll_account
#: model:ir.model,name:hr_payroll_account.model_hr_payroll_payment_report_wizard
msgid "HR Payroll Payment Report Wizard"
msgstr ""

#. module: hr_payroll_account
#: model:ir.model.fields,field_description:hr_payroll_account.field_account_setup_bank_manual_config__has_alt_bank_account
#: model:ir.model.fields,field_description:hr_payroll_account.field_res_partner_bank__has_alt_bank_account
msgid "Has Alt Bank Account"
msgstr ""

#. module: hr_payroll_account
#. odoo-python
#: code:addons/hr_payroll_account/models/hr_payroll_structure.py:0
msgid ""
"Incorrect journal: The journal must be in the same currency as the company"
msgstr ""
"Neispravna temeljnica: Temeljnica mora biti u istoj valuti koja je "
"definirana na postavi tvrtke."

#. module: hr_payroll_account
#. odoo-python
#: code:addons/hr_payroll_account/wizard/hr_payroll_payment_report_wizard.py:0
msgid ""
"Invalid IBAN for the following employees:\n"
"%s"
msgstr ""

#. module: hr_payroll_account
#: model:ir.model,name:hr_payroll_account.model_account_journal
msgid "Journal"
msgstr "Dnevnik"

#. module: hr_payroll_account
#. odoo-python
#: code:addons/hr_payroll_account/models/hr_payslip.py:0
#: code:addons/hr_payroll_account/models/hr_payslip_run.py:0
#: model:ir.model,name:hr_payroll_account.model_account_move
msgid "Journal Entry"
msgstr "Temeljnica"

#. module: hr_payroll_account
#: model:ir.model.fields,help:hr_payroll_account.field_hr_payslip__date
msgid "Keep empty to use the period of the validation(Payslip) date."
msgstr "Ostavite prazno za koirištenje perioda prema datumu platne liste."

#. module: hr_payroll_account
#: model:ir.model.fields,field_description:hr_payroll_account.field_hr_payslip_run__move_id
msgid "Move"
msgstr "Temeljnica"

#. module: hr_payroll_account
#: model:ir.model.fields,field_description:hr_payroll_account.field_hr_payslip_run__move_state
msgid "Move State"
msgstr ""

#. module: hr_payroll_account
#: model:ir.model.fields,field_description:hr_payroll_account.field_hr_salary_rule__not_computed_in_net
msgid "Not computed in net accountably"
msgstr ""

#. module: hr_payroll_account
#. odoo-python
#: code:addons/hr_payroll_account/models/hr_payslip.py:0
msgid "One of the contract for these payslips has no structure type."
msgstr ""
"Za jedan od ugovora na ovim isplatnim listama nije definirana vrsta "
"strukture plaće."

#. module: hr_payroll_account
#. odoo-python
#: code:addons/hr_payroll_account/models/hr_payslip.py:0
msgid "One of the payroll structures has no account journal defined on it."
msgstr "Jedna od struktura plaće nema definiran dnevnik."

#. module: hr_payroll_account
#: model:ir.model,name:hr_payroll_account.model_account_payment_register
#: model_terms:ir.ui.view,arch_db:hr_payroll_account.hr_payslip_view_form
msgid "Pay"
msgstr "Plati"

#. module: hr_payroll_account
#: model:ir.model,name:hr_payroll_account.model_hr_payslip
msgid "Pay Slip"
msgstr "Obračunski list"

#. module: hr_payroll_account
#. odoo-python
#: code:addons/hr_payroll_account/wizard/account_payment_register.py:0
msgid "Payment done at %s"
msgstr ""

#. module: hr_payroll_account
#: model:ir.model,name:hr_payroll_account.model_account_payment
msgid "Payments"
msgstr "Plaćanja"

#. module: hr_payroll_account
#: model:ir.model,name:hr_payroll_account.model_hr_payslip_run
msgid "Payslip Batches"
msgstr "Obračuni plaća"

#. module: hr_payroll_account
#: model:ir.model,name:hr_payroll_account.model_hr_payslip_line
msgid "Payslip Line"
msgstr "Redak obračunskog  lista"

#. module: hr_payroll_account
#: model:ir.model.fields,field_description:hr_payroll_account.field_account_bank_statement_line__payslip_ids
#: model:ir.model.fields,field_description:hr_payroll_account.field_account_move__payslip_ids
msgid "Payslips"
msgstr "Isplatni listići"

#. module: hr_payroll_account
#. odoo-python
#: code:addons/hr_payroll_account/models/account_chart_template.py:0
msgid "SLR"
msgstr ""

#. module: hr_payroll_account
#. odoo-python
#: code:addons/hr_payroll_account/models/account_chart_template.py:0
msgid "Salaries"
msgstr "Plaće"

#. module: hr_payroll_account
#: model:ir.model.fields,field_description:hr_payroll_account.field_hr_payroll_structure__journal_id
#: model:ir.model.fields,field_description:hr_payroll_account.field_hr_payslip__journal_id
msgid "Salary Journal"
msgstr "Dnevnik plaća"

#. module: hr_payroll_account
#: model:ir.model,name:hr_payroll_account.model_hr_salary_rule
msgid "Salary Rule"
msgstr "Pravilo obračuna"

#. module: hr_payroll_account
#: model:ir.model,name:hr_payroll_account.model_hr_payroll_structure
msgid "Salary Structure"
msgstr "Struktura plaće"

#. module: hr_payroll_account
#: model:ir.model.fields,field_description:hr_payroll_account.field_hr_salary_rule__split_move_lines
msgid "Split account line based on name"
msgstr ""

#. module: hr_payroll_account
#: model:ir.model.fields,help:hr_payroll_account.field_hr_payslip_line__credit_tag_ids
#: model:ir.model.fields,help:hr_payroll_account.field_hr_salary_rule__credit_tag_ids
msgid ""
"Tags assigned to this line will impact financial reports when translated "
"into an accounting journal entry.They will be applied on the credit account "
"line in the journal entry."
msgstr ""

#. module: hr_payroll_account
#: model:ir.model.fields,help:hr_payroll_account.field_hr_payslip_line__debit_tag_ids
#: model:ir.model.fields,help:hr_payroll_account.field_hr_salary_rule__debit_tag_ids
msgid ""
"Tags assigned to this line will impact financial reports when translated "
"into an accounting journal entry.They will be applied on the debit account "
"line in the journal entry."
msgstr ""

#. module: hr_payroll_account
#. odoo-python
#: code:addons/hr_payroll_account/models/hr_payslip.py:0
msgid ""
"The Expense Journal \"%s\" has not properly configured the default Account!"
msgstr "Dnevnik troškova \"%s\" nije ispravno podešen zadani konto!"

#. module: hr_payroll_account
#. odoo-python
#: code:addons/hr_payroll_account/models/hr_payslip.py:0
msgid "The credit account on the NET salary rule is not reconciliable"
msgstr ""

#. module: hr_payroll_account
#. odoo-python
#: code:addons/hr_payroll_account/models/hr_payslip.py:0
msgid "The employee bank account is untrusted"
msgstr ""

#. module: hr_payroll_account
#: model:ir.model.fields,help:hr_payroll_account.field_hr_salary_rule__not_computed_in_net
msgid ""
"This field allows you to delete the value of this rule in the \"Net Salary\""
" rule at the accounting level to explicitly display the value of this rule "
"in the accounting. For example, if you want to display the value of your "
"representation fees, you can check this field."
msgstr ""

#. module: hr_payroll_account
#: model_terms:ir.ui.view,arch_db:hr_payroll_account.view_partner_bank_form_inherit_hr_payroll_account
msgid "This will replace the employee existing bank account."
msgstr ""

#. module: hr_payroll_account
#. odoo-python
#: code:addons/hr_payroll_account/models/hr_payslip.py:0
msgid "You can only register payment for posted journal entries."
msgstr ""

#. module: hr_payroll_account
#. odoo-python
#: code:addons/hr_payroll_account/models/hr_payslip.py:0
msgid "You can only register payments for unpaid documents."
msgstr ""

#. module: hr_payroll_account
#. odoo-python
#: code:addons/hr_payroll_account/models/account_journal.py:0
msgid "You cannot delete the journal linked to a Salary Structure"
msgstr ""

#. module: hr_payroll_account
#. odoo-python
#: code:addons/hr_payroll_account/models/hr_payroll_account.py:0
#, python-format
msgid "You can't create a journal entry for a paid payslip."
msgstr ""
