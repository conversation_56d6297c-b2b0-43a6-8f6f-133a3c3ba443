# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * hr_payroll_account
#
# Translators:
# <PERSON> <<EMAIL>>, 2017
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.saas~18\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-05-16 16:02+0000\n"
"PO-Revision-Date: 2017-09-20 09:53+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2017\n"
"Language-Team: Kabyle (https://www.transifex.com/odoo/teams/41243/kab/)\n"
"Language: kab\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: hr_payroll_account
#: model:ir.model,name:hr_payroll_account.model_account_chart_template
msgid "Account Chart Template"
msgstr ""

#. module: hr_payroll_account
#. odoo-python
#: code:addons/hr_payroll_account/models/account_chart_template.py:0
msgid "Account Payslip Houserental"
msgstr ""

#. module: hr_payroll_account
#: model_terms:ir.ui.view,arch_db:hr_payroll_account.hr_salary_rule_view_form
msgid "Accounting"
msgstr ""

#. module: hr_payroll_account
#: model:ir.model.fields,field_description:hr_payroll_account.field_hr_payslip__move_id
msgid "Accounting Entry"
msgstr ""

#. module: hr_payroll_account
#. odoo-python
#: code:addons/hr_payroll_account/models/hr_payroll_account.py:0
msgid "Adjustment Entry"
msgstr ""

#. module: hr_payroll_account
#: model:ir.model.fields,field_description:hr_payroll_account.field_hr_contract__analytic_account_id
#: model:ir.model.fields,field_description:hr_payroll_account.field_hr_contract_history__analytic_account_id
#: model:ir.model.fields,field_description:hr_payroll_account.field_hr_salary_rule__analytic_account_id
msgid "Analytic Account"
msgstr "Amiḍan n tusliṭ"

#. module: hr_payroll_account
#: model_terms:ir.ui.view,arch_db:hr_payroll_account.hr_payslip_run_view_form
#: model_terms:ir.ui.view,arch_db:hr_payroll_account.hr_payslip_view_form
msgid "Are you sure you want to proceed?"
msgstr ""

#. module: hr_payroll_account
#: model:ir.model,name:hr_payroll_account.model_hr_contract_history
msgid "Contract history"
msgstr ""

#. module: hr_payroll_account
#: model_terms:ir.ui.view,arch_db:hr_payroll_account.hr_payslip_run_view_form
#: model_terms:ir.ui.view,arch_db:hr_payroll_account.hr_payslip_view_form
msgid "Create Draft Entry"
msgstr ""

#. module: hr_payroll_account
#: model:ir.model.fields,field_description:hr_payroll_account.field_hr_salary_rule__account_credit
msgid "Credit Account"
msgstr ""

#. module: hr_payroll_account
#: model:ir.model.fields,field_description:hr_payroll_account.field_hr_payslip__date
msgid "Date Account"
msgstr ""

#. module: hr_payroll_account
#: model:ir.model.fields,field_description:hr_payroll_account.field_hr_salary_rule__account_debit
msgid "Debit Account"
msgstr ""

#. module: hr_payroll_account
#: model:ir.model,name:hr_payroll_account.model_hr_contract
msgid "Employee Contract"
msgstr ""

#. module: hr_payroll_account
#. odoo-python
#: code:addons/hr_payroll_account/models/hr_payroll_account.py:0
msgid "Incorrect journal: The journal must be in the same currency as the company"
msgstr ""

#. module: hr_payroll_account
#: model:ir.model,name:hr_payroll_account.model_account_journal
msgid "Journal"
msgstr ""

#. module: hr_payroll_account
#: model:ir.model.fields,help:hr_payroll_account.field_hr_payslip__date
msgid "Keep empty to use the period of the validation(Payslip) date."
msgstr ""

#. module: hr_payroll_account
#. odoo-python
#: code:addons/hr_payroll_account/models/account_chart_template.py:0
msgid "No existing account for code %s"
msgstr ""

#. module: hr_payroll_account
#: model:ir.model.fields,field_description:hr_payroll_account.field_hr_salary_rule__not_computed_in_net
msgid "Not computed in net accountably"
msgstr ""

#. module: hr_payroll_account
#. odoo-python
#: code:addons/hr_payroll_account/models/hr_payroll_account.py:0
msgid "One of the contract for these payslips has no structure type."
msgstr ""

#. module: hr_payroll_account
#. odoo-python
#: code:addons/hr_payroll_account/models/hr_payroll_account.py:0
msgid "One of the payroll structures has no account journal defined on it."
msgstr ""

#. module: hr_payroll_account
#: model:ir.model,name:hr_payroll_account.model_hr_payslip
msgid "Pay Slip"
msgstr ""

#. module: hr_payroll_account
#. odoo-python
#: code:addons/hr_payroll_account/models/account_chart_template.py:0
msgid "SLR"
msgstr ""

#. module: hr_payroll_account
#. odoo-python
#: code:addons/hr_payroll_account/models/account_chart_template.py:0
msgid "Salaries"
msgstr ""

#. module: hr_payroll_account
#: model:ir.model.fields,field_description:hr_payroll_account.field_hr_payroll_structure__journal_id
#: model:ir.model.fields,field_description:hr_payroll_account.field_hr_payslip__journal_id
msgid "Salary Journal"
msgstr ""

#. module: hr_payroll_account
#: model:ir.model,name:hr_payroll_account.model_hr_salary_rule
msgid "Salary Rule"
msgstr ""

#. module: hr_payroll_account
#: model:ir.model,name:hr_payroll_account.model_hr_payroll_structure
msgid "Salary Structure"
msgstr ""

#. module: hr_payroll_account
#. odoo-python
#: code:addons/hr_payroll_account/models/hr_payroll_account.py:0
msgid "The Expense Journal \"%s\" has not properly configured the default Account!"
msgstr ""

#. module: hr_payroll_account
#: model:ir.model.fields,help:hr_payroll_account.field_hr_salary_rule__not_computed_in_net
msgid "This field allows you to delete the value of this rule in the \"Net Salary\" rule at the accounting level to explicitly display the value of this rule in the accounting. For example, if you want to display the value of your representation fees, you can check this field."
msgstr ""

#. module: hr_payroll_account
#. odoo-python
#: code:addons/hr_payroll_account/models/account_journal.py:0
msgid "You cannot delete the journal linked to a Salary Structure"
msgstr ""
