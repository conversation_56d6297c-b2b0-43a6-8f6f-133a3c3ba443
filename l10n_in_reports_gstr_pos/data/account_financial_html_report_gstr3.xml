<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- 3.1(a) Outward Taxable supplies (other than zero rated, nil rated and exempted) -->
    <record id="l10n_in_reports.account_report_gstr3b_3_1_a_tax_base" model="account.report.expression">
        <field name="formula" eval="[
        '&amp;',
            '|',
                '&amp;',
                    ('move_id.l10n_in_gst_treatment', '!=', 'overseas'), ('move_id.move_type', '=', 'out_invoice'),
                '&amp;',
                    ('move_id.move_type', '=', 'entry'),
                    '|',
                        ('move_id.pos_session_ids', '!=', False),
                        ('move_id.reversed_pos_order_id', '!=', False),
            ('tax_tag_ids', 'in', [ref('l10n_in.tax_tag_base_sgst'), ref('l10n_in.tax_tag_base_cgst'), ref('l10n_in.tax_tag_base_igst'), ref('l10n_in.tax_tag_base_cess')])
        ]"/>
    </record>

    <record id="l10n_in_reports.account_report_gstr3b_3_1_a_tax_cgst" model="account.report.expression">
        <field name="formula" eval="[
        '&amp;',
            '|',
                '&amp;',
                    ('move_id.l10n_in_gst_treatment', '!=', 'overseas'), ('move_id.move_type', '=', 'out_invoice'),
                '&amp;',
                    ('move_id.move_type', '=', 'entry'),
                    '|',
                        ('move_id.pos_session_ids', '!=', False),
                        ('move_id.reversed_pos_order_id', '!=', False),
            ('tax_tag_ids', '=', ref('l10n_in.tax_tag_cgst'))
        ]"/>
    </record>

    <record id="l10n_in_reports.account_report_gstr3b_3_1_a_tax_sgst" model="account.report.expression">
        <field name="formula" eval="[
        '&amp;',
            '|',
                '&amp;',
                    ('move_id.l10n_in_gst_treatment', '!=', 'overseas'), ('move_id.move_type', '=', 'out_invoice'),
                '&amp;',
                    ('move_id.move_type', '=', 'entry'),
                    '|',
                        ('move_id.pos_session_ids', '!=', False),
                        ('move_id.reversed_pos_order_id', '!=', False),
            ('tax_tag_ids', '=', ref('l10n_in.tax_tag_sgst'))
        ]"/>
    </record>

    <record id="l10n_in_reports.account_report_gstr3b_3_1_a_tax_igst" model="account.report.expression">
        <field name="formula" eval="[
        '&amp;',
            '|',
                '&amp;',
                    ('move_id.l10n_in_gst_treatment', '!=', 'overseas'), ('move_id.move_type', '=', 'out_invoice'),
                '&amp;',
                    ('move_id.move_type', '=', 'entry'),
                    '|',
                        ('move_id.pos_session_ids', '!=', False),
                        ('move_id.reversed_pos_order_id', '!=', False),
            ('tax_tag_ids', '=', ref('l10n_in.tax_tag_igst'))
        ]"/>
    </record>

    <record id="l10n_in_reports.account_report_gstr3b_3_1_a_tax_cess" model="account.report.expression">
        <field name="formula" eval="[
        '&amp;',
            '|',
                '&amp;',
                    ('move_id.l10n_in_gst_treatment', '!=', 'overseas'), ('move_id.move_type', '=', 'out_invoice'),
                '&amp;',
                    ('move_id.move_type', '=', 'entry'),
                    '|',
                        ('move_id.pos_session_ids', '!=', False),
                        ('move_id.reversed_pos_order_id', '!=', False),
            ('tax_tag_ids', '=', ref('l10n_in.tax_tag_cess'))
        ]"/>
    </record>

    <!-- 3.1(b) Outward Taxable supplies (zero rated) -->
    <record id="l10n_in_reports.account_report_gstr3b_3_1_b_tax_base" model="account.report.expression">
        <field name="formula" eval="[
        '&amp;',
            '|',
                '&amp;',
                    ('move_id.l10n_in_gst_treatment', '=', 'overseas'), ('move_id.move_type', '=', 'out_invoice'),
                '&amp;',
                    ('move_id.move_type', '=', 'entry'),
                    '|',
                        ('move_id.pos_session_ids', '!=', False),
                        ('move_id.reversed_pos_order_id', '!=', False),
            ('tax_tag_ids', 'in', [ref('l10n_in.tax_tag_base_igst'), ref('l10n_in.tax_tag_base_cess')])
        ]"/>
    </record>

    <record id="l10n_in_reports.account_report_gstr3b_3_1_b_tax_igst" model="account.report.expression">
        <field name="formula" eval="[
        '&amp;',
            '|',
                '&amp;',
                    ('move_id.l10n_in_gst_treatment', '=', 'overseas'), ('move_id.move_type', '=', 'out_invoice'),
                '&amp;',
                    ('move_id.move_type', '=', 'entry'),
                    '|',
                        ('move_id.pos_session_ids', '!=', False),
                        ('move_id.reversed_pos_order_id', '!=', False),
            ('tax_tag_ids', '=', ref('l10n_in.tax_tag_igst'))
        ]"/>
    </record>

    <record id="l10n_in_reports.account_report_gstr3b_3_1_b_tax_cess" model="account.report.expression">
        <field name="formula" eval="[
        '&amp;',
            '|',
                '&amp;',
                    ('move_id.l10n_in_gst_treatment', '=', 'overseas'), ('move_id.move_type', '=', 'out_invoice'),
                '&amp;',
                    ('move_id.move_type', '=', 'entry'),
                    '|',
                        ('move_id.pos_session_ids', '!=', False),
                        ('move_id.reversed_pos_order_id', '!=', False),
            ('tax_tag_ids', '=', ref('l10n_in.tax_tag_cess'))
        ]"/>
    </record>

    <!-- 3.1(c) Other Outward Taxable supplies (Nil rated, exempted) -->
    <record id="l10n_in_reports.account_report_gstr3b_3_1_c_tax_base" model="account.report.expression">
        <field name="formula" eval="[
        '&amp;',
            '|',
                ('move_id.move_type', '=', 'out_invoice'),
            '&amp;',
                ('move_id.move_type', '=', 'entry'),
                '|',
                    ('move_id.pos_session_ids', '!=', False),
                    ('move_id.reversed_pos_order_id', '!=', False),
            ('tax_tag_ids', 'in', [ref('l10n_in.tax_tag_exempt'), ref('l10n_in.tax_tag_nil_rated')])
        ]"/>
    </record>

    <!-- 3.1(e) Non-GST Outward supplies -->
    <record id="l10n_in_reports.account_report_gstr3b_3_1_e_tax_base" model="account.report.expression">
        <field name="formula" eval="[
        '&amp;',
            '|',
                ('move_id.move_type', '=', 'out_invoice'),
            '&amp;',
                ('move_id.move_type', '=', 'entry'),
                '|',
                    ('move_id.pos_session_ids', '!=', False),
                    ('move_id.reversed_pos_order_id', '!=', False),
            ('tax_tag_ids', '=', ref('l10n_in.tax_tag_non_gst_supplies'))
        ]"/>
    </record>

    <!-- 4(A)(1) Import of goods -->
    <record id="l10n_in_reports.account_report_gstr3b_4_a_1_tax_igst" model="account.report.expression">
        <field name="formula" eval="[('move_id.move_type', 'in', ('entry', 'in_invoice')), ('move_id.l10n_in_gst_treatment', '=', 'overseas'), ('move_id.pos_session_ids', '=', False), ('move_id.reversed_pos_order_id', '=', False), ('tax_line_id.tax_scope', '!=', 'service'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_igst'))]"/>
    </record>

    <record id="l10n_in_reports.account_report_gstr3b_4_a_1_tax_cess" model="account.report.expression">
        <field name="formula" eval="[('move_id.move_type', 'in', ('entry', 'in_invoice')), ('move_id.l10n_in_gst_treatment', '=', 'overseas'), ('move_id.pos_session_ids', '=', False), ('move_id.reversed_pos_order_id', '=', False), ('tax_line_id.tax_scope', '!=', 'service'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_cess'))]"/>
    </record>

    <!-- 4(A)(2) Import of services -->
    <record id="l10n_in_reports.account_report_gstr3b_4_a_2_tax_igst" model="account.report.expression">
        <field name="formula" eval="[('move_id.move_type', 'in', ('entry', 'in_invoice')), ('move_id.l10n_in_gst_treatment', '=', 'overseas'), ('move_id.pos_session_ids', '=', False), ('move_id.reversed_pos_order_id', '=', False), ('tax_line_id.tax_scope', '=', 'service'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_igst'))]"/>
    </record>

    <record id="l10n_in_reports.account_report_gstr3b_4_a_2_tax_cess" model="account.report.expression">
        <field name="formula" eval="[('move_id.move_type', 'in', ('entry', 'in_invoice')), ('move_id.l10n_in_gst_treatment', '=', 'overseas'), ('move_id.pos_session_ids', '=', False), ('move_id.reversed_pos_order_id', '=', False), ('tax_line_id.tax_scope', '=', 'service'), ('tax_tag_ids','=', ref('l10n_in.tax_tag_cess'))]"/>
    </record>

    <!-- 4(A)(3) Inward supplies liable to reverse charge (other than 1 & 2 above) -->
    <record id="l10n_in_reports.account_report_gstr3b_4_a_3_tax_cgst" model="account.report.expression">
        <field name="formula" eval="[('move_id.move_type', 'in', ('entry', 'in_invoice')), ('move_id.pos_session_ids', '=', False), ('move_id.reversed_pos_order_id', '=', False), ('tax_tag_ids','=', ref('l10n_in.tax_tag_cgst_rc'))]"/>
    </record>

    <record id="l10n_in_reports.account_report_gstr3b_4_a_3_tax_sgst" model="account.report.expression">
        <field name="formula" eval="[('move_id.move_type', 'in', ('entry', 'in_invoice')), ('move_id.pos_session_ids', '=', False), ('move_id.reversed_pos_order_id', '=', False), ('tax_tag_ids','=', ref('l10n_in.tax_tag_sgst_rc'))]"/>
    </record>

    <record id="l10n_in_reports.account_report_gstr3b_4_a_3_tax_igst" model="account.report.expression">
        <field name="formula" eval="[('move_id.move_type', 'in', ('entry', 'in_invoice')), ('move_id.pos_session_ids', '=', False), ('move_id.reversed_pos_order_id', '=', False), ('tax_tag_ids','=', ref('l10n_in.tax_tag_igst_rc'))]"/>
    </record>

    <record id="l10n_in_reports.account_report_gstr3b_4_a_3_tax_cess" model="account.report.expression">
        <field name="formula" eval="[('move_id.move_type', 'in', ('entry', 'in_invoice')), ('move_id.pos_session_ids', '=', False), ('move_id.reversed_pos_order_id', '=', False), ('tax_tag_ids','=', ref('l10n_in.tax_tag_cess_rc'))]"/>
    </record>

    <!-- 4(A)(4) All other ITC -->
    <record id="l10n_in_reports.account_report_gstr3b_4_a_4_tax_cgst" model="account.report.expression">
        <field name="formula" eval="[('move_id.move_type', 'in', ('entry', 'in_invoice')), ('move_id.l10n_in_gst_treatment','!=', 'overseas'), ('move_id.pos_session_ids', '=', False), ('move_id.reversed_pos_order_id', '=', False), ('tax_tag_ids','=', ref('l10n_in.tax_tag_cgst'))]"/>
    </record>

    <record id="l10n_in_reports.account_report_gstr3b_4_a_4_tax_sgst" model="account.report.expression">
        <field name="formula" eval="[('move_id.move_type', 'in', ('entry', 'in_invoice')), ('move_id.l10n_in_gst_treatment','!=', 'overseas'), ('move_id.pos_session_ids', '=', False), ('move_id.reversed_pos_order_id', '=', False), ('tax_tag_ids','=', ref('l10n_in.tax_tag_sgst'))]"/>
    </record>

    <record id="l10n_in_reports.account_report_gstr3b_4_a_4_tax_igst" model="account.report.expression">
        <field name="formula" eval="[('move_id.move_type', 'in', ('entry', 'in_invoice')), ('move_id.l10n_in_gst_treatment','!=', 'overseas'), ('move_id.pos_session_ids', '=', False), ('move_id.reversed_pos_order_id', '=', False), ('tax_tag_ids','=', ref('l10n_in.tax_tag_igst'))]"/>
    </record>

    <record id="l10n_in_reports.account_report_gstr3b_4_a_4_tax_cess" model="account.report.expression">
        <field name="formula" eval="[('move_id.move_type', 'in', ('entry', 'in_invoice')), ('move_id.l10n_in_gst_treatment','!=', 'overseas'), ('move_id.pos_session_ids', '=', False), ('move_id.reversed_pos_order_id', '=', False), ('tax_tag_ids','=', ref('l10n_in.tax_tag_cess'))]"/>
    </record>

    <!-- 4(B)(1) As per section 17(5) of CGST/SGST Act -->
    <record id="l10n_in_reports.account_report_gstr3b_4_b_1_tax_cgst" model="account.report.expression">
        <field name="formula" eval="[('move_id.move_type', 'in', ('entry', 'in_invoice')), ('move_id.pos_session_ids', '=', False), ('move_id.reversed_pos_order_id', '=', False), ('tax_tag_ids','=', ref('l10n_in.tax_tag_non_itc_cgst'))]"/>
    </record>

    <record id="l10n_in_reports.account_report_gstr3b_4_b_1_tax_sgst" model="account.report.expression">
        <field name="formula" eval="[('move_id.move_type', 'in', ('entry', 'in_invoice')), ('move_id.pos_session_ids', '=', False), ('move_id.reversed_pos_order_id', '=', False), ('tax_tag_ids','=', ref('l10n_in.tax_tag_non_itc_sgst'))]"/>
    </record>

    <record id="l10n_in_reports.account_report_gstr3b_4_b_1_tax_igst" model="account.report.expression">
        <field name="formula" eval="[('move_id.move_type', 'in', ('entry', 'in_invoice')), ('move_id.pos_session_ids', '=', False), ('move_id.reversed_pos_order_id', '=', False), ('tax_tag_ids','=', ref('l10n_in.tax_tag_non_itc_igst'))]"/>
    </record>

    <record id="l10n_in_reports.account_report_gstr3b_4_b_2_tax_cess" model="account.report.expression">
        <field name="formula" eval="[('move_id.move_type', 'in', ('entry', 'in_invoice')), ('move_id.pos_session_ids', '=', False), ('move_id.reversed_pos_order_id', '=', False), ('tax_tag_ids','=', ref('l10n_in.tax_tag_non_itc_cess'))]"/>
    </record>

    <!-- 4(B)(2) Others -->
    <record id="l10n_in_reports.account_report_gstr3b_4_b_2_tax_cgst" model="account.report.expression">
        <field name="formula" eval="[('move_id.move_type', 'in', ('entry', 'in_invoice')), ('move_id.pos_session_ids', '=', False), ('move_id.reversed_pos_order_id', '=', False), ('tax_tag_ids','=', ref('l10n_in.tax_tag_other_non_itc_cgst'))]"/>
    </record>

    <record id="l10n_in_reports.account_report_gstr3b_4_b_2_tax_sgst" model="account.report.expression">
        <field name="formula" eval="[('move_id.move_type', 'in', ('entry', 'in_invoice')), ('move_id.pos_session_ids', '=', False), ('move_id.reversed_pos_order_id', '=', False), ('tax_tag_ids','=', ref('l10n_in.tax_tag_other_non_itc_sgst'))]"/>
    </record>

    <record id="l10n_in_reports.account_report_gstr3b_4_b_2_tax_igst" model="account.report.expression">
        <field name="formula" eval="[('move_id.move_type', 'in', ('entry', 'in_invoice')), ('move_id.pos_session_ids', '=', False), ('move_id.reversed_pos_order_id', '=', False), ('tax_tag_ids','=', ref('l10n_in.tax_tag_other_non_itc_igst'))]"/>
    </record>

    <record id="l10n_in_reports.account_report_gstr3b_4_b_2_tax_cess" model="account.report.expression">
        <field name="formula" eval="[('move_id.move_type', 'in', ('entry', 'in_invoice')), ('move_id.pos_session_ids', '=', False), ('move_id.reversed_pos_order_id', '=', False), ('tax_tag_ids','=', ref('l10n_in.tax_tag_other_non_itc_cess'))]"/>
    </record>
</odoo>
