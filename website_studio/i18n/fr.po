# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_studio
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:29+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON><PERSON>, 2025\n"
"Language-Team: French (https://app.transifex.com/odoo/teams/41243/fr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fr\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: website_studio
#: model_terms:ir.ui.view,arch_db:website_studio.website_controller_page_form_dialog
msgid "<span class=\"bg-300 align-self-baseline\">/model/</span>"
msgstr "<span class=\"bg-300 align-self-baseline\">/modèle/</span>"

#. module: website_studio
#: model_terms:ir.ui.view,arch_db:website_studio.default_form_field_name
msgid ""
"<span class=\"s_website_form_label_content\">Name</span>\n"
"                                            <span class=\"s_website_form_mark\"> *</span>"
msgstr ""
"<span class=\"s_website_form_label_content\">Nom</span>\n"
"                                             <span class=\"s_website_form_mark\"> *</span>"

#. module: website_studio
#: model_terms:ir.ui.view,arch_db:website_studio.website_controller_page_form_dialog
msgid "<span invisible=\"name_slugified\" class=\"bg-300\">...</span>"
msgstr "<span invisible=\"name_slugified\" class=\"bg-300\">...</span>"

#. module: website_studio
#: model_terms:ir.ui.view,arch_db:website_studio.website_controller_page_form_dialog
msgid "Advanced"
msgstr "Avancé"

#. module: website_studio
#: model_terms:ir.ui.view,arch_db:website_studio.website_controller_page_form_dialog
msgid "Basic parameters"
msgstr "Paramètres de base"

#. module: website_studio
#. odoo-javascript
#: code:addons/website_studio/static/src/website_form_editor.js:0
msgid "Create %s"
msgstr "Créer %s"

#. module: website_studio
#: model:ir.model.fields,field_description:website_studio.field_website_controller_page__auto_single_page
msgid "Create Single Page"
msgstr "Créer une seule page"

#. module: website_studio
#: model:ir.model.fields,field_description:website_studio.field_website_controller_page__use_menu
msgid "Create Website Menu"
msgstr "Créer un menu de site web"

#. module: website_studio
#: model_terms:ir.ui.view,arch_db:website_studio.default_record_page
msgid "Drag building blocks to customize the footer of single record pages."
msgstr ""
"Faites glisser les blocs de construction pour personnaliser le pied de page "
"des enregistrements individuels."

#. module: website_studio
#: model_terms:ir.ui.view,arch_db:website_studio.default_record_page
msgid "Drag building blocks to customize the header of single record pages."
msgstr ""
"Faites glisser les blocs de construction pour personnaliser l'en-tête des "
"enregistrements individuels."

#. module: website_studio
#. odoo-python
#: code:addons/website_studio/models/website_controller_page.py:0
msgid "Drag building blocks to edit the website description of this record."
msgstr ""
"Glissez des blocs de construction pour modifier la description de cet "
"enregistrement sur le site web."

#. module: website_studio
#: model_terms:ir.ui.view,arch_db:website_studio.website_controller_page_form_dialog
msgid "Exposed model"
msgstr "Modèle exposé"

#. module: website_studio
#: model_terms:ir.ui.view,arch_db:website_studio.s_website_form_options
msgid "Form Access"
msgstr "Accès au formulaire"

#. module: website_studio
#: model_terms:ir.ui.view,arch_db:website_studio.listing_layout_switcher
msgid "Grid"
msgstr "Grid"

#. module: website_studio
#: model:ir.model.fields,help:website_studio.field_website_controller_page__auto_single_page
msgid "If checked, a single page will be created along with your listing page"
msgstr ""
"Si cette case est cochée, une seule page sera créée avec votre page de "
"référencement"

#. module: website_studio
#: model_terms:ir.ui.view,arch_db:website_studio.listing_layout_switcher
msgid "List"
msgstr "Liste"

#. module: website_studio
#: model:ir.model,name:website_studio.model_website_controller_page
msgid "Model Page"
msgstr "Page modèle"

#. module: website_studio
#. odoo-javascript
#: code:addons/website_studio/static/src/editor_tabs.js:0
msgid "Model Pages"
msgstr "Pages modèle"

#. module: website_studio
#. odoo-javascript
#: code:addons/website_studio/static/src/website_form_editor.js:0
msgid "More models"
msgstr "Plus de modèles"

#. module: website_studio
#: model_terms:ir.ui.view,arch_db:website_studio.default_listing
msgid "Search"
msgstr "Rechercher"

#. module: website_studio
#: model_terms:ir.ui.view,arch_db:website_studio.default_listing
msgid "Search..."
msgstr "Rechercher..."

#. module: website_studio
#. odoo-javascript
#: code:addons/website_studio/static/src/website_form_editor.js:0
msgid "Select model"
msgstr "Sélectionner un modèle"

#. module: website_studio
#: model_terms:ir.ui.view,arch_db:website_studio.default_form_field_name
msgid "Submit"
msgstr "Soumettre"

#. module: website_studio
#: model_terms:ir.ui.view,arch_db:website_studio.website_controller_page_form_dialog
msgid "URL"
msgstr "URL"

#. module: website_studio
#: model:ir.model.fields,field_description:website_studio.field_website_controller_page__name
msgid "View Name"
msgstr "Nom de vue"

#. module: website_studio
#. odoo-python
#: code:addons/website_studio/models/website_controller_page.py:0
msgid "Website Description"
msgstr "Description du site web"

#. module: website_studio
#: model_terms:ir.ui.view,arch_db:website_studio.default_listing
msgid "found)"
msgstr "trouvé(s))"
