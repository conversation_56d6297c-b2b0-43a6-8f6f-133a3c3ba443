<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <!-- Form View -->
    <record id="import_vttc_entry_form_view" model="ir.ui.view">
        <field name="name">import.vttc.entry.form.view</field>
        <field name="model">import.vttc.entry</field>
        <field name="arch" type="xml">
            <form string="Import VTTC Entry">
                <header>
                    <button name="action_import_vttc_entry" class="btn-primary" type="object" string="Import File"/>
                </header>
                <sheet>
                    <div name="button_box" class="oe_button_box">
                        <button name="action_open_account_move" class="oe_stat_button" icon="fa-bars" type="object"
                                invisible="not move_count">
                            <field name="move_count" string="Entries" widget="statinfo"/>
                        </button>
                    </div>
                    <h1>
                        <field name="name" readonly="1"/>
                    </h1>
                    <group>
                        <group>
                            <field name="date"/>
                            <field name="move_count" invisible="1"/>
                            <field name="import_file" widget="many2many_binary"/>
                        </group>
                        <group>
                            <field name="user_id" readonly="1" force_save="1"/>
                            <field name="company_id" options="{'no_open':True,'no_create':True}" required="1"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Imported VTTC Entries">
                            <field name="imported_vttc_entry_line" readonly="1">
                                <list create="0" delete="0">
                                    <field name="doc_ref"/>
                                    <field name="transaction_date"/>
                                    <field name="transaction_type"/>
                                    <field name="branch_code"/>
                                    <field name="company_id"/>
                                    <field name="date" readonly="1" column_invisible="1"/>
                                    <field name="erp_code"/>
                                    <field name="debit" sum="total"/>
                                    <field name="credit" sum="total"/>
                                    <field name="narration" column_invisible="1"/>
                                </list>
                            </field>
                        </page>
                        <page string="Not Import VTTC Entry">
                            <div style="margin-bottom:10px;margin-top:10px;float:right;"
                                 invisible="not not_imported_vttc_entry_line">
                                <button name="action_re_import_data" string="Re-Import Data" type="object"
                                        class="btn-outline-primary"/>
                            </div>
                            <field name="not_imported_vttc_entry_line" options="{'no_edit':False}">
                                <list create="0">
                                    <field name="doc_ref"/>
                                    <field name="transaction_date" readonly="1"/>
                                    <field name="transaction_type"/>
                                    <field name="branch_code"/>
                                    <field name="company_id"/>
                                    <field name="date" readonly="1" column_invisible="1"/>
                                    <field name="erp_code"/>
                                    <field name="debit" sum="total"/>
                                    <field name="credit" sum="total"/>
                                    <field name="narration" column_invisible="1"/>
                                    <field name="reason"/>
                                </list>
                            </field>
                        </page>
                    </notebook>
                </sheet>
                <chatter/>
            </form>
        </field>
    </record>

    <!-- List View -->
    <record id="import_vttc_entry_list_view" model="ir.ui.view">
        <field name="name">import.vttc.entry.list.view</field>
        <field name="model">import.vttc.entry</field>
        <field name="arch" type="xml">
            <list string="Import VTTC Entry">
                <field name="date"/>
                <field name="user_id"/>
                <field name="company_id"/>
            </list>
        </field>
    </record>

    <!-- Search View -->
    <record id="import_vttc_entry_search_view" model="ir.ui.view">
        <field name="name">import.vttc.entry.search.view</field>
        <field name="model">import.vttc.entry</field>
        <field name="arch" type="xml">
            <search string="Search Import VTT Entry">
                <field name="date"/>
                <field name="user_id"/>
            </search>
        </field>
    </record>

    <!-- Action -->
    <record id="action_import_vttc_entry" model="ir.actions.act_window">
        <field name="name">Import VTTC Entries</field>
        <field name="res_model">import.vttc.entry</field>
        <field name="view_mode">list,form</field>
    </record>
</odoo>
