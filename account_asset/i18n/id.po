# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_asset
# 
# Translators:
# Wil Odo<PERSON>, 2024
# <PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:25+0000\n"
"PO-Revision-Date: 2024-09-25 09:43+0000\n"
"Last-Translator: Abe Manyo, 2025\n"
"Language-Team: Indonesian (https://app.transifex.com/odoo/teams/41243/id/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: id\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__total_depreciation_entries_count
msgid "# Depreciation Entries"
msgstr "# Entri Penyusutan"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__gross_increase_count
msgid "# Gross Increases"
msgstr "# Peningkatan Kotor "

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__depreciation_entries_count
msgid "# Posted Depreciation Entries"
msgstr "# Entri Penyusutan Terposting"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
msgid "%(asset)s: Disposal"
msgstr "%(asset)s: Disposal"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
msgid "%(asset)s: Sale"
msgstr "%(asset)s: Sale"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_assets_report.py:0
msgid "%(months)s m"
msgstr "%(months)s bula"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_move.py:0
msgid "%(move_line)s (%(current)s of %(total)s)"
msgstr "%(move_line)s (%(current)s dari %(total)s)"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_assets_report.py:0
msgid "%(years)s y"
msgstr "%(years)s tahun"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
msgid "%s (copy)"
msgstr "%s (salin)"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/wizard/asset_modify.py:0
msgid ""
"%s Future entries will be recomputed to depreciate the asset following the "
"changes."
msgstr ""
"%s Entri masa depan akan dihitung ulang untuk melakukan depresiasi aset "
"mengikuti perubahan ini."

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_move.py:0
msgid "%s: Depreciation"
msgstr "%s: Depresiasi"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_assets_report.py:0
msgid "(No %s)"
msgstr "(No %s)"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "(incl."
msgstr "(termasuk."

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/wizard/asset_modify.py:0
msgid ""
"A depreciation entry will be posted on and including the date %(date)s. "
"<br/> %(extra_text)s Future entries will be recomputed to depreciate the "
"asset following the changes."
msgstr ""
"Entri depresiasi akan dipost pada dan termasuk tanggal %(date)s. <br/> "
"%(extra_text)s Entri masa depan akan dihitung ulang untuk melakukan "
"depresiasi aset mengikuti perubahan ini."

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/wizard/asset_modify.py:0
msgid ""
"A depreciation entry will be posted on and including the date %(date)s.<br/>"
" A disposal entry will be posted on the %(account_type)s account "
"<b>%(account)s</b>."
msgstr ""
"Entri depresiasi akan dipost pada dan termasuk tanggal %(date)s.<br/> Entri "
"pembuangan akan dipost pada akun %(account_type)s <b>%(account)s</b>."

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/wizard/asset_modify.py:0
msgid ""
"A depreciation entry will be posted on and including the date %(date)s.<br/>"
" A second entry will neutralize the original income and post the  outcome of"
" this sale on account <b>%(account)s</b>."
msgstr ""
"Entri depresiasi akan dipost pada dan termasuk tanggal %(date)s.<br/> Entri "
"kedua akan menetralisir income original dan post outcome dari sale ini pada "
"akun <b>%(account)s</b>."

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/wizard/asset_modify.py:0
msgid "A depreciation entry will be posted on and including the date %s."
msgstr "Entri depresiasi akan dipost pada dan termasuk tanggal %s."

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
msgid "A document linked to %(move_line_name)s has been deleted: %(link)s"
msgstr "Dokumen yang di-link ke %(move_line_name)s telah dihapus: %(link)s"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
msgid "A document linked to this move has been deleted: %s"
msgstr "Dokumen yang di-link ke pergerakan ini telah dihapus: %s"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/wizard/asset_modify.py:0
msgid "A gross increase has been created: %(link)s"
msgstr "Peningkatan kotor telah dibuat: %(link)s"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
msgid ""
"A non deductible tax value of %(tax_value)s was added to %(name)s's initial "
"value of %(purchase_value)s"
msgstr ""
"Non deductible tax sejumlah %(tax_value)s telah ditambahkan ke nilai awal "
"%(name)s yang sejumlah %(purchase_value)s"

#. module: account_asset
#: model:ir.model,name:account_asset.model_account_account
msgid "Account"
msgstr "Akun"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__account_type
msgid ""
"Account Type is used for information purpose, to generate country-specific "
"legal reports, and set the rules to close a fiscal year and generate opening"
" entries."
msgstr ""
"Tipe Akun berguna sebagai penyedia informasi, untuk membuat laporan "
"akuntansi yang spesifik tiap negara, dan menyusun aturan untuk tutup buku "
"tahunan dan membuat saldo awal."

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__account_depreciation_id
msgid "Account used in the depreciation entries, to decrease the asset value."
msgstr ""
"Akun yang digunakan di entri penyusutan, untuk mengurangi nilai aktiva."

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__account_depreciation_expense_id
msgid ""
"Account used in the periodical entries, to record a part of the asset as "
"expense."
msgstr ""
"Akun yang digunakan di entri periode, untuk mencatat aktiva sebagai beban."

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__account_asset_id
msgid ""
"Account used to record the purchase of the asset at its original price."
msgstr ""
"Akun yang digunakan untuk mencatat pembelian aktiva pada harga originalnya."

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_asset_modify__gain_account_id
msgid "Account used to write the journal item in case of gain"
msgstr "Akun yang digunakan untuk menulis item jurnal jika terjadi keuntungan"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_res_company__gain_account_id
msgid ""
"Account used to write the journal item in case of gain while selling an "
"asset"
msgstr ""
"Akun yang digunakan untuk menulis item jurnal jika terjadi keuntungan ketika"
" menjual sebuah aset"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_asset_modify__loss_account_id
msgid "Account used to write the journal item in case of loss"
msgstr "Akun yang digunakan untuk menulis item jurnal jika terjadi kerugian"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_res_company__loss_account_id
msgid ""
"Account used to write the journal item in case of loss while selling an "
"asset"
msgstr ""
"Akun yang digunakan untuk menulis item jurnal jika terjadi kerugian ketika "
"menjual sebuah aset"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Accounting"
msgstr "Akuntansi"

#. module: account_asset
#: model:ir.model,name:account_asset.model_account_report
msgid "Accounting Report"
msgstr "Laporan Akuntansi"

#. module: account_asset
#: model:account.report.column,name:account_asset.assets_report_acquisition_date
#: model:ir.model.fields,field_description:account_asset.field_account_asset__acquisition_date
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_kanban
msgid "Acquisition Date"
msgstr "Akuisisi Tanggal"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__modify_action
msgid "Action"
msgstr "Tindakan"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__message_needaction
msgid "Action Needed"
msgstr "Tindakan Diperluka"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__active
msgid "Active"
msgstr "Aktif"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__activity_ids
msgid "Activities"
msgstr "Aktivitas"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Dekorasi Pengecualian Aktivitas"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__activity_state
msgid "Activity State"
msgstr "Status Aktivitas"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__activity_type_icon
msgid "Activity Type Icon"
msgstr "Ikon Jenis Aktifitas"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.asset_modify_form
msgid "Add an internal note"
msgstr "Tambahkan catatan internal"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
#: code:addons/account_asset/models/account_move.py:0
msgid "All the lines should be from the same account"
msgstr "Semua baris harus dari akun yang sama"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_move.py:0
msgid "All the lines should be from the same company"
msgstr "Semua baris harus dari perusahaan yang sama."

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
#: code:addons/account_asset/models/account_move.py:0
msgid "All the lines should be posted"
msgstr "Semua baris harus dikirimkan"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__already_depreciated_amount_import
msgid "Already Depreciated Amount Import"
msgstr "Import Jumlah yang Sudah Didepresiasi"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__parent_id
msgid "An asset has a parent when it is the result of gaining value"
msgstr ""
"Sebuah aset mempunyai induk ketika berupa hasil dari nilai yang diperoleh"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
msgid "An asset has been created for this move:"
msgstr "Sebuah aset telah dibuat untuk pergerakan ini:"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_account__asset_model_ids
msgid ""
"An asset wil be created for each asset model when this account is used on a "
"vendor bill or a refund"
msgstr ""
"Aset akan dibuat untuk setiap model aset saat akun ini digunakan pada "
"tagihan vendor atau refund"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/wizard/asset_modify.py:0
msgid "An asset will be created for the value increase of the asset. <br/>"
msgstr "Aset akan dibuat untuk peninangkatan value aset. <br/>"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__analytic_distribution
msgid "Analytic Distribution"
msgstr "Distribusi Analitik"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__analytic_precision
msgid "Analytic Precision"
msgstr "Ketelitian Analitik"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_search
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Archived"
msgstr "Diarsipkan"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
#: code:addons/account_asset/models/account_move.py:0
#: model:ir.model.fields,field_description:account_asset.field_account_bank_statement_line__asset_id
#: model:ir.model.fields,field_description:account_asset.field_account_move__asset_id
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__asset_id
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Asset"
msgstr "Aktiva"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Asset Account"
msgstr "Akun Aktiva"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
msgid "Asset Cancelled"
msgstr "Aset Dibatalkan"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__account_asset_counterpart_id
msgid "Asset Counterpart Account"
msgstr "Akun Counterpart Aset"

#. module: account_asset
#: model:ir.model,name:account_asset.model_account_asset_group
#: model:ir.model.fields,field_description:account_asset.field_account_asset__asset_group_id
#: model_terms:ir.ui.view,arch_db:account_asset.asset_group_form_view
#: model_terms:ir.ui.view,arch_db:account_asset.asset_group_list_view
msgid "Asset Group"
msgstr "Kelompok Aset"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_bank_statement_line__asset_id_display_name
#: model:ir.model.fields,field_description:account_asset.field_account_move__asset_id_display_name
msgid "Asset Id Display Name"
msgstr "Nama Tampilan ID Aset"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__asset_lifetime_days
msgid "Asset Lifetime Days"
msgstr "Umur Hidup Aset Hari"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_account__asset_model_ids
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_search
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_search
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_form_asset_inherit
msgid "Asset Model"
msgstr "Model Aset"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Asset Model name"
msgstr "Nama Model Aset"

#. module: account_asset
#: model:ir.actions.act_window,name:account_asset.action_account_asset_model_form
#: model:ir.ui.menu,name:account_asset.menu_action_account_asset_model_form
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_tree
msgid "Asset Models"
msgstr "Model Aset"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_bank_statement_line__asset_move_type
#: model:ir.model.fields,field_description:account_asset.field_account_move__asset_move_type
msgid "Asset Move Type"
msgstr "Tipe Pergerakan Aset"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__name
msgid "Asset Name"
msgstr "Nama Aktiva"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__asset_paused_days
msgid "Asset Paused Days"
msgstr "Hari Aset yang Dipause"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_bank_statement_line__asset_value_change
#: model:ir.model.fields,field_description:account_asset.field_account_move__asset_value_change
msgid "Asset Value Change"
msgstr "Perubahan Nilai Aset"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Asset Values"
msgstr "Nilai Aset"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
msgid "Asset created"
msgstr "Aktiva telah dibuat"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_move.py:0
msgid "Asset created from invoice: %s"
msgstr "Asset dibuat dari faktur: %s"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
msgid "Asset disposed. %s"
msgstr "Asset Dibuang. %s"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
msgid "Asset paused. %s"
msgstr "Aset Dipause. %s"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
msgid "Asset sold. %s"
msgstr "Aset dijual. %s"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/wizard/asset_modify.py:0
msgid "Asset unpaused. %s"
msgstr "Aset Diunpause. %s"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.asset_group_form_view
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
#: model_terms:ir.ui.view,arch_db:account_asset.view_move_form_asset_inherit
msgid "Asset(s)"
msgstr "Aset(s)"

#. module: account_asset
#: model:ir.model,name:account_asset.model_account_asset
msgid "Asset/Revenue Recognition"
msgstr "Aset/Pengakuan Pendapatan"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_assets_report.py:0
#: model:ir.actions.act_window,name:account_asset.action_account_asset_form
#: model:ir.model.fields,field_description:account_asset.field_account_bank_statement_line__asset_ids
#: model:ir.model.fields,field_description:account_asset.field_account_move__asset_ids
#: model:ir.ui.menu,name:account_asset.menu_action_account_asset_form
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_tree
msgid "Assets"
msgstr "Aktiva"

#. module: account_asset
#: model:ir.model,name:account_asset.model_account_asset_report_handler
msgid "Assets Report Custom Handler"
msgstr "Assets Report Custom Handler"

#. module: account_asset
#: model:ir.ui.menu,name:account_asset.menu_finance_config_assets
msgid "Assets and Revenues"
msgstr "Aktiva dan Pendapatan"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Assets in closed state"
msgstr "Aktiva dalam status tertutup"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Assets in draft and open states"
msgstr "Aktiva dalam status rancangan dan terbuka"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
msgid ""
"Atleast one asset (%s) couldn't be set as running because it lacks any "
"required information"
msgstr ""
"Setidaknya satu aset (%s) tidak dapat dijalankan karena kurang informasi "
"yang dibutuhkan"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__message_attachment_count
msgid "Attachment Count"
msgstr "Hitungan Lampiran"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_form_asset_inherit
msgid "Automate Asset"
msgstr "Otomatisasi Aset"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_form_asset_inherit
msgid "Automation"
msgstr "Otomatisasi"

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset__prorata_computation_type__daily_computation
msgid "Based on days per period"
msgstr "Berdasarkan hari-hari per periode"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Bills"
msgstr "Tagihan"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_assets_report.py:0
#: model:ir.model.fields,field_description:account_asset.field_account_asset__book_value
msgid "Book Value"
msgstr "Nilai Buku"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_account__can_create_asset
msgid "Can Create Asset"
msgstr "Aset yang Dapat Dibuat"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.asset_modify_form
msgid "Cancel"
msgstr "Batal"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Cancel Asset"
msgstr "Batalkan Aset"

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset__state__cancelled
msgid "Cancelled"
msgstr "Dibatalkan"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_assets_report.py:0
msgid "Characteristics"
msgstr "Karakteristik"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__children_ids
msgid "Children"
msgstr "Turunan"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__method
msgid ""
"Choose the method to use to compute the amount of depreciation lines.\n"
"  * Straight Line: Calculated on basis of: Gross Value / Duration\n"
"  * Declining: Calculated on basis of: Residual Value * Declining Factor\n"
"  * Declining then Straight Line: Like Declining but with a minimum depreciation value equal to the straight line value."
msgstr ""
"Pilih metode yang digunakan untuk menghitung jumlah baris depresiasi.\n"
"  * Straight Line: Dihitung berdasarkan basis: Gross Value / Duration\n"
"  * Declining: Dihitung berdasarkan: Residual Value * Declining Factor\n"
"  * Declining then Straight Line: Seperti Declining tapi dengan nilai depresiasi sama dengan nilai straight line value."

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset__state__close
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Closed"
msgstr "Ditutup"

#. module: account_asset
#: model:ir.model,name:account_asset.model_res_company
msgid "Companies"
msgstr "Perusahaan"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__company_id
#: model:ir.model.fields,field_description:account_asset.field_account_asset_group__company_id
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__company_id
msgid "Company"
msgstr "Perusahaan"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__prorata_computation_type
msgid "Computation"
msgstr "Penghitungan"

#. module: account_asset
#: model:ir.actions.server,name:account_asset.action_account_asset_compute_depreciations
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Compute Depreciation"
msgstr "Hitung Penyusutan"

#. module: account_asset
#: model:ir.actions.server,name:account_asset.action_account_asset_run
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Confirm"
msgstr "Konfirmasi"

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset__prorata_computation_type__constant_periods
msgid "Constant Periods"
msgstr "Periode Konstan"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_bank_statement_line__count_asset
#: model:ir.model.fields,field_description:account_asset.field_account_move__count_asset
msgid "Count Asset"
msgstr "Hitung Aset"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__count_linked_asset
msgid "Count Linked Asset"
msgstr "Count Linked Asset"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_group__count_linked_assets
msgid "Count Linked Assets"
msgstr "Count Linked Assets"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__country_code
msgid "Country Code"
msgstr "Kode Negara"

#. module: account_asset
#: model:ir.actions.server,name:account_asset.action_account_aml_to_asset
#: model:ir.model.fields,field_description:account_asset.field_account_account__create_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_move_line_tree
msgid "Create Asset"
msgstr "Buat Aset"

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_account__create_asset__validate
msgid "Create and validate"
msgstr "Buat dan Validasi"

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_account__create_asset__draft
msgid "Create in draft"
msgstr "Buat dalam Konsep"

#. module: account_asset
#: model_terms:ir.actions.act_window,help:account_asset.action_account_asset_form
msgid "Create new asset"
msgstr "Buat aset baru"

#. module: account_asset
#: model_terms:ir.actions.act_window,help:account_asset.action_account_asset_model_form
msgid "Create new asset model"
msgstr "Buat model aset baru"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__create_uid
#: model:ir.model.fields,field_description:account_asset.field_account_asset_group__create_uid
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__create_uid
msgid "Created by"
msgstr "Dibuat oleh"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__create_date
#: model:ir.model.fields,field_description:account_asset.field_account_asset_group__create_date
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__create_date
msgid "Created on"
msgstr "Dibuat pada"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_bank_statement_line__asset_depreciated_value
#: model:ir.model.fields,field_description:account_asset.field_account_move__asset_depreciated_value
msgid "Cumulative Depreciation"
msgstr "Akumulai Penyusutan"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__currency_id
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__currency_id
msgid "Currency"
msgstr "Mata Uang"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Current"
msgstr "Terbaru"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Current Values"
msgstr "Nilai Saat Ini"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__invoice_ids
msgid "Customer Invoice"
msgstr "Faktur Pelanggan"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__date
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Date"
msgstr "Tanggal"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_bank_statement_line__asset_depreciation_beginning_date
#: model:ir.model.fields,field_description:account_asset.field_account_move__asset_depreciation_beginning_date
msgid "Date of the beginning of the depreciation"
msgstr "Tanggal permulaan depresiasi"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_assets_report.py:0
msgid "Dec. then Straight"
msgstr "Dec. then Straight"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_assets_report.py:0
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset__method__degressive
msgid "Declining"
msgstr "Declining"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__method_progress_factor
msgid "Declining Factor"
msgstr "Declining Factor"

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset__method__degressive_then_linear
msgid "Declining then Straight Line"
msgstr "Declining then Straight Line"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__value_residual
msgid "Depreciable Amount"
msgstr "Jumlah Penyusutan"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__value_residual
#: model:ir.model.fields,field_description:account_asset.field_account_bank_statement_line__asset_remaining_value
#: model:ir.model.fields,field_description:account_asset.field_account_move__asset_remaining_value
msgid "Depreciable Value"
msgstr "Nilai Penyusutan"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Depreciated Amount"
msgstr "Jumlah Yang Terdepresiasi"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_assets_report.py:0
#: model:ir.model.fields,field_description:account_asset.field_account_bank_statement_line__depreciation_value
#: model:ir.model.fields,field_description:account_asset.field_account_move__depreciation_value
#: model:ir.model.fields.selection,name:account_asset.selection__account_move__asset_move_type__depreciation
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Depreciation"
msgstr "Penyusutan"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__account_depreciation_id
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__account_depreciation_id
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_search
msgid "Depreciation Account"
msgstr "Akun Penyusutan"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Depreciation Board"
msgstr "Papan Penyusutan"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Depreciation Date"
msgstr "Tanggal Penyusutan"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__depreciation_move_ids
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Depreciation Lines"
msgstr "Baris Penyusutan"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Depreciation Method"
msgstr "Metode Penyusutan"

#. module: account_asset
#: model:account.report,name:account_asset.assets_report
#: model:ir.actions.client,name:account_asset.action_account_report_assets
#: model:ir.ui.menu,name:account_asset.menu_action_account_report_assets
msgid "Depreciation Schedule"
msgstr "Jadwal Depresiasi"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/wizard/asset_modify.py:0
msgid "Depreciation board modified %s"
msgstr "Board depresiasi dimofidikasi %s"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_move.py:0
msgid "Depreciation entry %(name)s posted (%(value)s)"
msgstr "Entri depresiasi %(name)s dipost (%(value)s)"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_move.py:0
msgid "Depreciation entry %(name)s reversed (%(value)s)"
msgstr "Entri depresiasi %(name)s dikembalikkan (%(value)s)"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__display_account_asset_id
msgid "Display Account Asset"
msgstr "Aset Tampilan Akun"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__display_name
#: model:ir.model.fields,field_description:account_asset.field_account_asset_group__display_name
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__display_name
msgid "Display Name"
msgstr "Nama Tampilan"

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_move__asset_move_type__disposal
msgid "Disposal"
msgstr "Pembuangan"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__disposal_date
msgid "Disposal Date"
msgstr "Tanggal Pembuangan"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
msgid "Disposal Move"
msgstr "Pergerakan Pembuangan"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
msgid "Disposal Moves"
msgstr "Pergerakan Pembuangan"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/wizard/asset_modify.py:0
#: model_terms:ir.ui.view,arch_db:account_asset.asset_modify_form
msgid "Dispose"
msgstr "Dibuang"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__distribution_analytic_account_ids
msgid "Distribution Analytic Account"
msgstr "Distribution Analytic Account"

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset__state__draft
msgid "Draft"
msgstr "Draft"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_bank_statement_line__draft_asset_exists
#: model:ir.model.fields,field_description:account_asset.field_account_move__draft_asset_exists
msgid "Draft Asset Exists"
msgstr "Draft Aset Tersedia"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__method_number
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__method_number
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_kanban
msgid "Duration"
msgstr "Durasi"

#. module: account_asset
#: model:account.report.column,name:account_asset.assets_report_duration_rate
msgid "Duration / Rate"
msgstr "Durasi / Nilai"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__account_depreciation_expense_id
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__account_depreciation_expense_id
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_search
msgid "Expense Account"
msgstr "Akun Beban"

#. module: account_asset
#: model:account.report.column,name:account_asset.assets_report_first_depreciation
msgid "First Depreciation"
msgstr "Penyusutan Awal"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__account_asset_id
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_search
msgid "Fixed Asset Account"
msgstr "Akun Aktiva Tetap"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__message_follower_ids
msgid "Followers"
msgstr "Follower"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__message_partner_ids
msgid "Followers (Partners)"
msgstr "Follower (Mitra)"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Ikon font awesome, misalnya fa-tasks"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_account__form_view_ref
msgid "Form View Ref"
msgstr "Referensi Tampilan Bagan"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Future Activities"
msgstr "Kegiatan - Kegiatan Mendatang"

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__asset_modify__gain_or_loss__gain
msgid "Gain"
msgstr "Keuntungan"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__gain_account_id
#: model:ir.model.fields,field_description:account_asset.field_res_company__gain_account_id
msgid "Gain Account"
msgstr "Akun Keuntungan"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__gain_or_loss
msgid "Gain Or Loss"
msgstr "Keuntungan Atau Kerugian"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__gain_value
msgid "Gain Value"
msgstr "Nilai Keuntungan"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Gross Increase"
msgstr "Peningkatan Kotor "

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__account_asset_id
msgid "Gross Increase Account"
msgstr "Akun Peningkatan Kotor"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__gross_increase_value
msgid "Gross Increase Value"
msgstr "Nilai Peningkatan Kotor"

#. module: account_asset
#. odoo-javascript
#: code:addons/account_asset/static/src/components/depreciation_schedule/groupby.xml:0
msgid "Group By Account"
msgstr "Kelompokkan Berdasarkan Akun"

#. module: account_asset
#. odoo-javascript
#: code:addons/account_asset/static/src/components/depreciation_schedule/groupby.xml:0
msgid "Group By Asset Group"
msgstr "Kelompokkan Berdasarkan Kelompok Aset"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_search
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Group By..."
msgstr "Kelompokkan Menurut..."

#. module: account_asset
#. odoo-javascript
#: code:addons/account_asset/static/src/components/depreciation_schedule/groupby.xml:0
msgid "Group by Account"
msgstr "Kelompokkan berdasarkan Akun"

#. module: account_asset
#. odoo-javascript
#: code:addons/account_asset/static/src/components/depreciation_schedule/groupby.xml:0
msgid "Group by Asset Group"
msgstr "Kelompokkan berdasarkan Kelompok Aset"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__has_message
msgid "Has Message"
msgstr "Memiliki Pesan"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__id
#: model:ir.model.fields,field_description:account_asset.field_account_asset_group__id
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__id
msgid "ID"
msgstr "ID"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__activity_exception_icon
msgid "Icon"
msgstr "Ikon"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Ikon untuk menunjukkan sebuah aktivitas pengecualian."

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Jika dicentang, pesan baru memerlukan penanganan dan perhatian Anda."

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__message_has_error
#: model:ir.model.fields,help:account_asset.field_account_asset__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Jika dicentang, beberapa pesan mempunyai kesalahan dalam pengiriman."

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__already_depreciated_amount_import
msgid ""
"In case of an import from another software, you might need to use this field"
" to have the right depreciation table report. This is the value that was "
"already depreciated with entries not computed from this model"
msgstr ""
"Dalam kasus import dari software lain, Anda mungkin harus menggunakan field "
"ini untuk memiliki laporan tabel depresiasi yang tepat. Field ini adalah "
"nilai yang sudah didepresiasi dengan entri yang tidak dihitung dari model "
"ini"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__informational_text
msgid "Informational Text"
msgstr "Teks Informasi"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__invoice_line_ids
msgid "Invoice Line"
msgstr "Baris Faktur"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__message_is_follower
msgid "Is Follower"
msgstr "Adalah Follower"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__salvage_value
#: model:ir.model.fields,help:account_asset.field_account_asset__salvage_value_pct
msgid "It is the amount you plan to have that you cannot depreciate."
msgstr "Adalah besaran yang direncanakan tersisa setelah penyusutan selesai."

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__journal_id
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_search
msgid "Journal"
msgstr "Jurnal"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
msgid "Journal Entries"
msgstr "Entri Jurnal"

#. module: account_asset
#: model:ir.model,name:account_asset.model_account_move
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Journal Entry"
msgstr "Entri Jurnal"

#. module: account_asset
#: model:ir.model,name:account_asset.model_account_move_line
msgid "Journal Item"
msgstr "Item Jurnal"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
#: model:ir.model.fields,field_description:account_asset.field_account_asset__original_move_line_ids
msgid "Journal Items"
msgstr "Item Jurnal"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_move.py:0
msgid ""
"Journal Items of %(account)s should have a label in order to generate an "
"asset"
msgstr ""
"Item Jurnal %(account)s harusnya memiliki label agar dapat membuat asset"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__write_uid
#: model:ir.model.fields,field_description:account_asset.field_account_asset_group__write_uid
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__write_uid
msgid "Last Updated by"
msgstr "Terakhir Diperbarui oleh"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__write_date
#: model:ir.model.fields,field_description:account_asset.field_account_asset_group__write_date
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__write_date
msgid "Last Updated on"
msgstr "Terakhir Diperbarui pada"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Late Activities"
msgstr "Aktifitas terakhir"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_assets_report.py:0
msgid "Linear"
msgstr "Linier"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__linked_assets_ids
msgid "Linked Assets"
msgstr "Linked Assets"

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__asset_modify__gain_or_loss__loss
msgid "Loss"
msgstr "Kerugian"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__loss_account_id
#: model:ir.model.fields,field_description:account_asset.field_res_company__loss_account_id
msgid "Loss Account"
msgstr "Akun Rugi"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_form_asset_inherit
msgid "Manage Items"
msgstr "Kelola Item"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__message_has_error
msgid "Message Delivery error"
msgstr "Kesalahan Pengiriman Pesan"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__message_ids
msgid "Messages"
msgstr "Pesan"

#. module: account_asset
#: model:account.report.column,name:account_asset.assets_report_first_method
#: model:ir.model.fields,field_description:account_asset.field_account_asset__method
msgid "Method"
msgstr "Metode"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__model_id
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset__state__model
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_kanban
msgid "Model"
msgstr "Model"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__asset_properties_definition
msgid "Model Properties"
msgstr "Properti-Properti Model"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.asset_modify_form
msgid "Modify"
msgstr "Ubah"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
#: model:ir.model,name:account_asset.model_asset_modify
#: model_terms:ir.ui.view,arch_db:account_asset.asset_modify_form
msgid "Modify Asset"
msgstr "Ubah Aktiva"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Modify Depreciation"
msgstr "Penyusutan Bulanan"

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset__method_period__1
#: model:ir.model.fields.selection,name:account_asset.selection__asset_modify__method_period__1
msgid "Months"
msgstr "Bulan"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_account__multiple_assets_per_line
msgid "Multiple Assets per Line"
msgstr "Lebih dari Satu Aset per Baris"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_account__multiple_assets_per_line
msgid ""
"Multiple asset items will be generated depending on the bill line quantity "
"instead of 1 global asset."
msgstr ""
"Lebih dari satu item aset yang akan dibuat berdasarkan kuantitas baris "
"tagihan alih-alih 1 aset global."

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Deadline Kegiatan Saya"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_group__name
msgid "Name"
msgstr "Nama"

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_move__asset_move_type__negative_revaluation
msgid "Negative revaluation"
msgstr "Evaluasi ulang negatif"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__net_gain_on_sale
msgid "Net gain on sale"
msgstr "Perolehan bersih pada penjualan"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__net_gain_on_sale
msgid "Net value of gain or loss on sale of an asset"
msgstr "Nilai bersih dari perolehan atau kerugian pada penjualan aset"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_asset_modify__value_residual
msgid "New residual amount for the asset"
msgstr "Jumlah penambahan yang baru untuk aset"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_asset_modify__salvage_value
msgid "New salvage amount for the asset"
msgstr "Jumlah pengurangan yang baru untuk aset"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Kalender Acara Aktivitas Berikutnya"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Batas Waktu Aktivitas Berikutnya"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__activity_summary
msgid "Next Activity Summary"
msgstr "Ringkasan Aktivitas Berikutnya"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__activity_type_id
msgid "Next Activity Type"
msgstr "Tipe Aktivitas Berikutnya"

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_account__create_asset__no
#: model:ir.model.fields.selection,name:account_asset.selection__asset_modify__gain_or_loss__no
msgid "No"
msgstr "Tidak"

#. module: account_asset
#. odoo-javascript
#: code:addons/account_asset/static/src/components/depreciation_schedule/groupby.xml:0
msgid "No Grouping"
msgstr "Tidak ada Pengelompokkan"

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset__prorata_computation_type__none
msgid "No Prorata"
msgstr "Tanpa Prorata"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__non_deductible_tax_value
#: model:ir.model.fields,field_description:account_asset.field_account_move_line__non_deductible_tax_value
msgid "Non Deductible Tax Value"
msgstr "Non Deductible Tax Value"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__salvage_value
msgid "Not Depreciable Amount"
msgstr "Jumlah Bukan Penyusutan"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__salvage_value
msgid "Not Depreciable Value"
msgstr "Nilai Bukan Penyusutan"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__salvage_value_pct
msgid "Not Depreciable Value Percent"
msgstr "Persentase Nilai Tidak Bisa Disusutkan"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__name
msgid "Note"
msgstr "Catatan"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__message_needaction_counter
msgid "Number of Actions"
msgstr "Jumlah Action"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_tree
msgid "Number of Depreciations"
msgstr "Jumlah penyusutan"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__method_period
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__method_period
msgid "Number of Months in a Period"
msgstr "Jumlah Bulan dalam Satu Periode"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__gross_increase_count
msgid "Number of assets made to increase the value of the asset"
msgstr "Jumlah aset yang dibuat untuk meningkatkan nilai aset tersebut"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_bank_statement_line__asset_number_days
#: model:ir.model.fields,field_description:account_asset.field_account_move__asset_number_days
msgid "Number of days"
msgstr "Jumlah hari"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__total_depreciation_entries_count
msgid "Number of depreciation entries (posted or not)"
msgstr "Jumlah entri penyusutan (yang diposting ataupun tidak)"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__message_has_error_counter
msgid "Number of errors"
msgstr "Jumlah kesalahan"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Jumlah pesan yang membutuhkan tindakan"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Jumlah pesan dengan kesalahan pengiriman"

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset__state__paused
msgid "On Hold"
msgstr "Tertahan"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_assets_report.py:0
msgid "Open Asset"
msgstr "Aset Terbuka"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__original_value
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_kanban
msgid "Original Value"
msgstr "Original Value"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__parent_id
msgid "Parent"
msgstr "Induk"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Parent Asset"
msgstr "Aset Induk"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/wizard/asset_modify.py:0
#: model_terms:ir.ui.view,arch_db:account_asset.asset_modify_form
msgid "Pause"
msgstr "Jeda"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__paused_prorata_date
msgid "Paused Prorata Date"
msgstr "Tanggal Prorata Dipause"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_tree
msgid "Period length"
msgstr "Panjang periode"

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_move__asset_move_type__positive_revaluation
msgid "Positive revaluation"
msgstr "Evaluasi ulang positif"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Posted Entries"
msgstr "Entri yang Dipost"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__asset_properties
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Properties"
msgstr "Properti"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__prorata_date
msgid "Prorata Date"
msgstr "Tanggal Prorata"

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_move__asset_move_type__purchase
msgid "Purchase"
msgstr "Pembelian"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__rating_ids
msgid "Ratings"
msgstr "Rating"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/wizard/asset_modify.py:0
msgid "Re-evaluate"
msgstr "Evaluasi ulang"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_group__linked_asset_ids
#: model:ir.model.fields,field_description:account_asset.field_account_move_line__asset_ids
#: model_terms:ir.ui.view,arch_db:account_asset.view_move_line_form_asset_inherit
msgid "Related Assets"
msgstr "Aset Terkait"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__related_purchase_value
msgid "Related Purchase Value"
msgstr "Nilai Pembelian Terkait"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
msgid "Reset to running"
msgstr "Reset menjadi running"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__activity_user_id
msgid "Responsible User"
msgstr "Tanggung-jawab"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/wizard/asset_modify.py:0
#: model_terms:ir.ui.view,arch_db:account_asset.asset_modify_form
msgid "Resume"
msgstr "Lanjutkan"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Resume Depreciation"
msgstr "Lanjutkan Depresiasi"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/wizard/asset_modify.py:0
msgid ""
"Reverse the depreciation entries posted in the future in order to modify the"
" depreciation"
msgstr ""
"Membalikkan entri depresiasi yang dipost di masa depan untuk memodifikasi "
"depresiasi"

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset__state__open
msgid "Running"
msgstr "Sedang berjalan"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Kesalahan Pengiriman SMS`"

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_move__asset_move_type__sale
msgid "Sale"
msgstr "Penjualan"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Save as Model"
msgstr "Simpan sebagai Model"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
msgid "Save model"
msgstr "Simpan model"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__select_invoice_line_id
msgid "Select Invoice Line"
msgstr "Pilih Baris Faktur"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/wizard/asset_modify.py:0
#: model_terms:ir.ui.view,arch_db:account_asset.asset_modify_form
msgid "Sell"
msgstr "Jual"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Set to Draft"
msgstr "Set ke Rancangan"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Set to Running"
msgstr "Tetapkan menjadi Running"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Show all records which has next action date is before today"
msgstr "Tampilkan semua dokumen dengan aksi berikut sebelum hari ini"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_move.py:0
msgid "Some fields are missing %s"
msgstr "Beberapa field tidak ada %s"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
msgid "Some required values are missing"
msgstr "Beberapa nilai yang diperlukan tidak ditemukan"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__prorata_date
msgid ""
"Starting date of the period used in the prorata calculation of the first "
"depreciation"
msgstr ""
"Tanggal mulai periode yang digunakan di kalkulasi prorata depresiasi pertama"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__state
msgid "Status"
msgstr "Status"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Status berdasarkan aktivitas\n"
"Terlambat: Batas waktu telah terlewati\n"
"Hari ini: Tanggal aktivitas adalah hari ini\n"
"Direncanakan: Aktivitas yang akan datang."

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset__method__linear
msgid "Straight Line"
msgstr "Garis Lurus"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__book_value
msgid ""
"Sum of the depreciable value, the salvage value and the book value of all "
"value increase items"
msgstr ""
"Jumlah dari nilai yang dapat depresiasi, salvage value dan book value dari "
"semua value increase items"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__country_code
msgid ""
"The ISO country code in two chars. \n"
"You can use this field for quick search."
msgstr ""
"Kode ISO negara dalam dua karakter.\n"
"Anda dapat menggunakan kolom ini untuk pencarian cepat."

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
msgid ""
"The account %(exp_acc)s has been credited by %(exp_delta)s, while the "
"account %(dep_acc)s has been debited by %(dep_delta)s. This corresponds to "
"%(move_count)s cancelled %(word)s:"
msgstr ""
"Akun %(exp_acc)s telah diberikan kredit oleh %(exp_delta)s, sementara akun "
"%(dep_acc)s telah didebet oleh %(dep_delta)s. Ini sesuai dengan "
"%(move_count)s yang dibatalkan %(word)s:"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__method_period
#: model:ir.model.fields,help:account_asset.field_asset_modify__method_period
msgid "The amount of time between two depreciations"
msgstr "Jumlah waktu di antara dua depresiasi"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
msgid ""
"The amount you have entered (%(entered_amount)s) does not match the Related "
"Purchase's value (%(purchase_value)s). Please make sure this is what you "
"want."
msgstr ""
"Jumlah yang Anda masukkan (%(entered_amount)s) tidak sesuai dengan nilai "
"Pembelian Terkait (%(purchase_value)s). Mohon pastikkan ini adalah apa yang "
"Anda inginkan."

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_asset_modify__asset_id
msgid "The asset to be modified by this wizard"
msgstr "Aset dapat dimodifikasi oleh wizard ini"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__children_ids
msgid "The children are the gains in value of this asset"
msgstr "The children are the gains in value of this asset"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_asset_modify__invoice_ids
msgid ""
"The disposal invoice is needed in order to generate the closing journal "
"entry."
msgstr "Faktur pembuangan dibutuhkan untuk membuat entri penutupan jurnal."

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__method_number
msgid "The number of depreciations needed to depreciate your asset"
msgstr "Jumlah penyusutan yang dibutuhkan untuk menyusutkan aktiva anda"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
msgid "The remaining value on the last depreciation line must be 0"
msgstr "Nilai tersisa pada baris depresiasi terakhir harus 0"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_asset_modify__invoice_line_ids
msgid "There are multiple lines that could be the related to this asset"
msgstr "Terdapat lebih dari satu baris yang dapat terkait aset ini"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/wizard/asset_modify.py:0
msgid ""
"There are unposted depreciations prior to the selected operation date, "
"please deal with them first."
msgstr ""
"Terdapat depresiasi yang menunggu dipost sebelum tanggal operasi yang "
"dipilih, mohon selesaikan depresiasi tersebut terlebih dahulu."

#. module: account_asset
#. odoo-javascript
#: code:addons/account_asset/static/src/components/move_reversed/move_reversed.xml:0
msgid "This move has been reversed"
msgstr "Pergerakan ini telah dibatalkan"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Today Activities"
msgstr "Aktivitas Hari ini"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_assets_report.py:0
msgid "Total"
msgstr "Total"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__total_depreciable_value
msgid "Total Depreciable Value"
msgstr "Total Depreciable Value"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_move.py:0
msgid "Turn as an asset"
msgstr "Turn as an asset"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__account_type
msgid "Type of the account"
msgstr "Tipe akun"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Jenis dari aktivitas pengecualian pada rekaman data."

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Value at Import"
msgstr "Value pada Impor"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/wizard/asset_modify.py:0
msgid "Value decrease for: %(asset)s"
msgstr "Nilai diturunkan untuk: %(asset)s"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/wizard/asset_modify.py:0
msgid "Value increase for: %(asset)s"
msgstr "Nilai dinaikkan untuk: %(asset)s"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__warning_count_assets
msgid "Warning Count Assets"
msgstr "Peringatan Jumlah Aset"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
msgid "Warning for the Original Value of %s"
msgstr "Peringatan untuk Original Value untuk %s"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__website_message_ids
msgid "Website Messages"
msgstr "Pesan situs"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__website_message_ids
msgid "Website communication history"
msgstr "Sejarah komunikasi situs"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__state
msgid ""
"When an asset is created, the status is 'Draft'.\n"
"If the asset is confirmed, the status goes in 'Running' and the depreciation lines can be posted in the accounting.\n"
"The 'On Hold' status can be set manually when you want to pause the depreciation of an asset for some time.\n"
"You can manually close an asset when the depreciation is over.\n"
"By cancelling an asset, all depreciation entries will be reversed"
msgstr ""
"Ketika aset dibuat, statusnya adalah 'Draft'.\n"
"Bila aset dikonfirmasi, statusnya berubah menjadi 'Running' dan garis depresiasinya bisa diposting di dalam akuntansi.\n"
"Status 'Ditahan' bisa diatur secara manual saat Anda ingin untuk sementara memberhentikan depresiasi aset.\n"
"Anda dapat secara manual menutup aset saat depresiasi sudah berhenti.\n"
"Dengan membatalkan aset, semua entri depresiasi akan di-reverse"

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset__method_period__12
#: model:ir.model.fields.selection,name:account_asset.selection__asset_modify__method_period__12
msgid "Years"
msgstr "Tahun"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_move.py:0
msgid ""
"You can't post an entry related to a draft asset. Please post the asset "
"before."
msgstr ""
"Anda tidak dapat post entri terkait draft aset. Mohon post aset tersebut "
"sebelumnya."

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/wizard/asset_modify.py:0
msgid "You can't re-evaluate the asset before the lock date."
msgstr "Anda tidak dapat mengevaluasi ulang aset sebelum tanggal kunci."

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
msgid ""
"You cannot add or remove bills when the asset is already running or closed."
msgstr ""
"Anda tidak dapat menambahkan atau menghapus tagihan saat asset sudah  "
"berjalan atau ditutup."

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
msgid "You cannot archive a record that is not closed"
msgstr " Anda tidak dapat mengarsipkan pencatatan yang tidak ditutup"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
#: code:addons/account_asset/wizard/asset_modify.py:0
msgid ""
"You cannot automate the journal entry for an asset that has a running gross "
"increase. Please use 'Dispose' on the increase(s)."
msgstr ""
"Anda tidak dapat mengotomatiskan entri jurnal untuk aset yang memiliki "
"peningkatan gross yang sedang berlangsung. Mohon gunakan 'Buang' pada "
"peningkatan."

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
msgid ""
"You cannot create an asset from lines containing credit and debit on the "
"account or with a null amount"
msgstr ""
"Anda tidak dapat membuat aset dari baris yang memiliki kredit dan debit pada"
" akun atau dengan jumlah null"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
msgid "You cannot delete a document that is in %s state."
msgstr "Anda tidak dapat menghapus dokumen yang sedang dalam status %s."

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
msgid ""
"You cannot delete an asset linked to posted entries.\n"
"You should either confirm the asset, then, sell or dispose of it, or cancel the linked journal entries."
msgstr ""
"Anda tidak dapat menghapus aset yang terkait entri yang di-post.\n"
"Anda bisa mengonfirmasi aset, lalu, menjual atau membuangnya, atau membatalkan entri jurnal yang di-link."

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
msgid "You cannot dispose of an asset before the lock date."
msgstr "Anda tidak dapat membuang aset sebelum tanggal kunci."

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_move.py:0
msgid "You cannot reset to draft an entry related to a posted asset"
msgstr ""
"Anda tidak dapat reset ke draft sebuah entri yang terkait aset yang sudah "
"di-post"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/wizard/asset_modify.py:0
msgid "You cannot resume at a date equal to or before the pause date"
msgstr ""
"Anda tidak dapat melanjutkan pada tanggal yang sama dengan atau sebelum "
"tanggal pause"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/wizard/asset_modify.py:0
msgid "You cannot select the same account as the Depreciation Account"
msgstr "Anda tidak dapat memilih akun yang sama dengan Akun Depresiasi"

#. module: account_asset
#: model:account.report.column,name:account_asset.assets_report_balance
msgid "book_value"
msgstr "book_value"

#. module: account_asset
#: model:account.report.column,name:account_asset.assets_report_date_from
#: model:account.report.column,name:account_asset.assets_report_depre_date_from
msgid "date from"
msgstr "tanggal dari"

#. module: account_asset
#: model:account.report.column,name:account_asset.assets_report_assets_date_to
#: model:account.report.column,name:account_asset.assets_report_depre_date_to
msgid "date to"
msgstr "tanggal ke"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "depreciable)"
msgstr "dapat depresiasi)"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "e.g. Laptop iBook"
msgstr "Misalnya: Laptop iBook"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
msgid "entries"
msgstr "entri-entri"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
msgid "entry"
msgstr "entri"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/wizard/asset_modify.py:0
msgid "gain"
msgstr "untung"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/wizard/asset_modify.py:0
msgid "gain/loss"
msgstr "untung/rugi"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/wizard/asset_modify.py:0
msgid "loss"
msgstr "rugi"
